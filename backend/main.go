package main

import (
	"backend/internal/app"
	"log"
)

// 添加注释以描述 server 信息
// @title           Swagger  API
// @version         1.0
// @description     This is a sample server celler server.
// @termsOfService  http://swagger.io/terms/

// @contact.name   API Support
// @contact.url    http://www.swagger.io/support
// @contact.email  <EMAIL>

// @license.name  Apache 2.0
// @license.url   http://www.apache.org/licenses/LICENSE-2.0.html

// @host      localhost:8080
// @BasePath  /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	srv, err := app.NewServer()
	if err != nil {
		log.Fatalf("初始化服务器失败: %v", err)
	}

	// 启动服务器
	if err := srv.Start(); err != nil {
		log.Fatalf("启动服务器失败: %v", err)
	}

	srv.WaitForSignal()

	// 停止服务器
	if err := srv.Stop(); err != nil {
		log.Fatalf("停止服务器失败: %v", err)
	}
}
