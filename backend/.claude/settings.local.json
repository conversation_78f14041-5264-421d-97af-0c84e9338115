{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(rg:*)", "Bash(grep:*)", "Bash(find:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(go build:*)", "Ba<PERSON>(go vet:*)", "Bash(gofmt:*)", "<PERSON><PERSON>(go run:*)", "Bash(rm:*)", "Bash(go mod:*)", "Bash(ls:*)", "WebFetch(domain:open.feishu.cn)", "WebFetch(domain:github.com)", "WebFetch(domain:pkg.go.dev)"], "deny": []}}