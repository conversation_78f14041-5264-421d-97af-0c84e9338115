package main

import (
	"backend/configs"
	"backend/internal/infrastructure/database"
	zapLog "backend/internal/infrastructure/logger"
	"backend/internal/modules/customerapi"
	"backend/internal/modules/customerapi/model"
	customerService "backend/internal/modules/customerapi/service"
	scheduleRepo "backend/internal/modules/schedule/repository"
	scheduleService "backend/internal/modules/schedule/service"
	ticketRepo "backend/internal/modules/ticket/repository"
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"go.temporal.io/sdk/client"
	"go.uber.org/zap"
)

// Config 配置项
type Config struct {
	Port           int           // 服务端口
	TokenDuration  time.Duration // 令牌有效期
	AllowedOrigins string        // 允许的跨域来源
	CustomersFile  string        // 客户配置文件路径
	CustomersEnv   string        // 客户配置环境变量名
}

// 从环境变量或命令行参数获取配置
func loadConfig() *Config {
	config := &Config{}

	// 定义命令行参数
	flag.IntVar(&config.Port, "port", 8090, "服务端口")
	flag.DurationVar(&config.TokenDuration, "token-duration", 24*time.Hour, "令牌有效期，格式：1h, 24h, 168h等")
	flag.StringVar(&config.AllowedOrigins, "allowed-origins", "*", "允许的跨域来源，多个来源用逗号分隔")
	flag.StringVar(&config.CustomersFile, "customers-file", "./configs/customers.json", "客户配置文件路径")
	flag.StringVar(&config.CustomersEnv, "customers-env", "API_CUSTOMERS", "客户配置环境变量名")

	// 解析命令行参数
	flag.Parse()

	// 优先使用环境变量
	if port := os.Getenv("API_PORT"); port != "" {
		if parsedPort, err := fmt.Sscanf(port, "%d", &config.Port); err != nil || parsedPort != 1 {
			log.Printf("警告: 无效的端口号 '%s'，使用默认值 %d", port, config.Port)
		}
	}
	if duration := os.Getenv("API_TOKEN_DURATION"); duration != "" {
		if parsedDuration, err := time.ParseDuration(duration); err == nil {
			config.TokenDuration = parsedDuration
		}
	}
	if origins := os.Getenv("API_ALLOWED_ORIGINS"); origins != "" {
		config.AllowedOrigins = origins
	}
	if customersFile := os.Getenv("API_CUSTOMERS_FILE"); customersFile != "" {
		config.CustomersFile = customersFile
	}
	if customersEnv := os.Getenv("API_CUSTOMERS_ENV_NAME"); customersEnv != "" {
		config.CustomersEnv = customersEnv
	}

	return config
}

// 加载客户配置
func loadCustomers(config *Config, logger *zap.Logger) model.CustomerMap {
	customers := make(model.CustomerMap)

	// 首先尝试从环境变量加载
	customersJSON := os.Getenv(config.CustomersEnv)
	if customersJSON != "" {
		var customerList []*model.Customer
		if err := json.Unmarshal([]byte(customersJSON), &customerList); err == nil {
			// 成功从环境变量加载
			for _, customer := range customerList {
				if customer.ID != "" && customer.Secret != "" {
					customers[customer.ID] = customer
				}
			}
			logger.Info("从环境变量成功加载客户配置",
				zap.String("env", config.CustomersEnv),
				zap.Int("count", len(customers)))

			if len(customers) > 0 {
				// 如果从环境变量加载成功，直接返回
				return customers
			}

			logger.Warn("环境变量中的客户配置无效或为空，尝试从文件加载")
		} else {
			logger.Warn("解析环境变量中的客户配置失败，尝试从文件加载",
				zap.Error(err),
				zap.String("env", config.CustomersEnv))
		}
	}

	// 如果环境变量未设置或解析失败，尝试从文件加载
	if _, err := os.Stat(config.CustomersFile); os.IsNotExist(err) {
		logger.Error("客户配置文件不存在且环境变量未设置有效配置，无法启动服务",
			zap.String("file", config.CustomersFile),
			zap.String("env", config.CustomersEnv))
		log.Fatalf("错误: 未找到有效的客户配置，请提供配置文件或环境变量")
		return nil
	}

	// 读取文件
	data, err := os.ReadFile(config.CustomersFile)
	if err != nil {
		logger.Error("读取客户配置文件失败", zap.String("path", config.CustomersFile), zap.Error(err))
		log.Fatalf("错误: 读取客户配置文件失败: %v", err)
		return nil
	}

	// 解析JSON
	var customerList []*model.Customer
	if err := json.Unmarshal(data, &customerList); err != nil {
		logger.Error("解析客户配置文件失败", zap.Error(err))
		log.Fatalf("错误: 解析客户配置文件失败: %v", err)
		return nil
	}

	// 转换为映射，只接受有效配置
	for _, customer := range customerList {
		if customer.ID != "" && customer.Secret != "" {
			customers[customer.ID] = customer
		}
	}

	// 验证是否有有效客户
	if len(customers) == 0 {
		logger.Error("配置文件中没有有效的客户配置", zap.String("path", config.CustomersFile))
		log.Fatalf("错误: 配置文件中没有有效的客户配置，每个客户必须提供ID和密钥")
		return nil
	}

	logger.Info("从文件成功加载客户配置", zap.String("path", config.CustomersFile), zap.Int("count", len(customers)))
	return customers
}

// 初始化Temporal客户端
func initTemporalClient(logger *zap.Logger) client.Client {
	// 默认连接本地Temporal服务器
	temporalAddress := os.Getenv("TEMPORAL_ADDRESS")
	if temporalAddress == "" {
		temporalAddress = "localhost:7233"
	}

	// 创建Temporal客户端
	temporalClient, err := client.Dial(client.Options{
		HostPort: temporalAddress,
	})
	if err != nil {
		logger.Error("无法连接到Temporal服务", zap.Error(err))
		log.Fatalf("无法连接到Temporal服务: %v", err)
	}

	return temporalClient
}

// 设置跨域中间件
func setupCorsMiddleware(allowedOrigins string) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", allowedOrigins)
		c.Writer.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-Customer-ID, X-Timestamp, X-Signature")
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

func main() {
	// 加载应用配置
	appConfig, err := configs.LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化日志系统
	logger, err := zapLog.NewLoggerFromConfig(&appConfig.Logger)
	if err != nil {
		log.Fatalf("初始化日志系统失败: %v", err)
	}
	defer func() {
		if err := logger.Sync(); err != nil {
			if !strings.Contains(err.Error(), "sync /dev/stderr: invalid argument") {
				log.Printf("关闭日志时发生错误: %v", err)
			}
		}
	}()

	// 加载API服务配置
	config := loadConfig()

	// 加载客户配置
	customers := loadCustomers(config, logger)

	// 设置运行模式
	gin.SetMode(gin.ReleaseMode)

	// 初始化数据库连接
	db := database.InitDB(appConfig)

	// 初始化数据库仓库和服务
	faultTicketRepo := ticketRepo.NewFaultTicketRepository(db)
	scheduleRepo := scheduleRepo.NewSoftScheRepository(db)
	scheduleSvc := scheduleService.NewSoftScheService(scheduleRepo)

	// 初始化Temporal客户端
	temporalClient := initTemporalClient(logger)
	defer temporalClient.Close()

	// 获取飞书配置
	feishuWebhookURL := ""
	feishuSecret := ""
	detailUrlTemplate := ""

	if appConfig.Feishu != nil {
		feishuWebhookURL = appConfig.Feishu.WebhookURL
		feishuSecret = appConfig.Feishu.Secret
		detailUrlTemplate = appConfig.Feishu.TicketDetailUrlTemplate
	}

	// 创建故障工单服务适配器
	faultTicketService := customerService.NewWorkflowEnabledFaultTicketService(
		db, faultTicketRepo, temporalClient, logger, scheduleSvc,
	)

	// 创建客户工单服务
	customerTicketService := customerService.NewCustomerTicketService(
		faultTicketService, feishuWebhookURL, feishuSecret, detailUrlTemplate,
	)

	// 创建Gin实例并设置中间件
	router := gin.New()
	router.Use(gin.Recovery())
	router.Use(gin.Logger())
	router.Use(setupCorsMiddleware(config.AllowedOrigins))

	// 创建客户API模块配置
	apiConfig := customerapi.ModuleConfig{
		Customers:         customers,
		FeishuWebhookURL:  feishuWebhookURL,
		FeishuSecret:      feishuSecret,
		DetailURLTemplate: detailUrlTemplate,
	}

	// 初始化客户API（带日志记录功能）
	customerapi.InitCustomerAPIWithLogging(
		router,
		customerTicketService,
		db,
		logger,
		apiConfig,
	)

	// 添加健康检查接口
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	// 创建HTTP服务器
	server := &http.Server{
		Addr:              fmt.Sprintf(":%d", config.Port),
		Handler:           router,
		ReadHeaderTimeout: 10 * time.Second, // 防止Slowloris攻击
	}

	// 优雅关闭处理
	go handleGracefulShutdown(server)

	// 启动服务器
	log.Printf("客户API服务启动在端口 %d", config.Port)
	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		log.Fatalf("启动服务器错误: %v", err)
	}
}

// 处理优雅关闭
func handleGracefulShutdown(server *http.Server) {
	// 监听中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("正在关闭服务器...")

	// 创建上下文用于通知服务器关闭
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("服务器关闭错误: %v", err)
	}
}
