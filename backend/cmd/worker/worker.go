package main

import (
	"backend/internal/infrastructure/temporal"
	"fmt"
	"log"
)

func main() {
	fmt.Println("正在启动Worker服务...")

	// 创建并初始化Worker
	w, err := temporal.NewWorker()
	if err != nil {
		log.Fatalf("初始化Worker失败: %v", err)
	}
	defer func() {
		if err := w.Close(); err != nil {
			log.Printf("关闭Worker失败: %v", err)
		}
	}()

	// 启动Worker
	if err := w.Start(); err != nil {
		log.Fatalf("启动Worker失败: %v", err)
	}
}
