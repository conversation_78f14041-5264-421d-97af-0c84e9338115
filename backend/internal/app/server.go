package app

import (
	"backend/configs"
	"backend/internal/config"
	"backend/internal/infrastructure/app"
	"backend/internal/infrastructure/auth"
	"backend/internal/infrastructure/casbin"
	infraTask "backend/internal/infrastructure/task"
	"backend/internal/modules/system/repository"
	"backend/internal/routes"
	"context"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

// Server 包含所有服务器相关的组件
type Server struct {
	app           *app.App
	router        *gin.Engine
	httpServer    *http.Server
	casbinService *casbin.CasbinService
	taskScheduler *infraTask.TaskScheduler
	logger        *zap.Logger
	config        *configs.Config
}

// NewServer 创建一个新的Server实例
func NewServer() (*Server, error) {
	// 加载配置
	appConfig, err := configs.LoadConfig()
	if err != nil {
		return nil, err
	}

	// 加载权限配置文件
	if err := config.LoadAuthConfig("./configs/auth_config.yaml"); err != nil {
		return nil, err
	}

	// 创建应用程序实例
	application, err := app.NewApp(appConfig)
	if err != nil {
		return nil, err
	}

	// 获取JWT密钥
	jwtSecretKey := auth.GetJWTSecretKey()

	// 初始化Casbin服务
	menuRepo := repository.NewMenuRepository(application.DB)
	casbinService, err := casbin.NewCasbinService(application.DB, menuRepo, application.Logger)
	if err != nil {
		return nil, err
	}

	// 初始化Casbin策略
	if err := casbinService.InitPoliciesFromDB(context.Background()); err != nil {
		application.Logger.Error("初始化Casbin策略失败", zap.Error(err))
	} else {
		application.Logger.Info("Casbin策略初始化成功")
	}

	// 初始化路由
	r := routes.InitRouter(application.DB, jwtSecretKey, application.Logger, appConfig.Mode, appConfig, application.RedisCache, casbinService)

	// 初始化任务调度器
	scheduler := infraTask.NewTaskScheduler()

	// 获取端口
	port := viper.GetString("server.port")
	if port == "" {
		port = "8080"
	}

	// 创建HTTP服务器
	httpServer := &http.Server{
		Addr:              ":" + port,
		Handler:           r,
		ReadHeaderTimeout: 10 * time.Second, // 防止Slowloris攻击
	}

	return &Server{
		app:           application,
		router:        r,
		httpServer:    httpServer,
		casbinService: casbinService,
		taskScheduler: scheduler,
		logger:        application.Logger,
		config:        appConfig,
	}, nil
}

// Start 启动服务器
func (s *Server) Start() error {
	// 设置任务
	infraTask.SetupTaskSchedulers(s.app.DB, s.taskScheduler)

	// 启动任务调度器
	go s.taskScheduler.Start()

	// 启动HTTP服务器
	go func() {
		s.logger.Info("服务启动", zap.String("端口", s.httpServer.Addr))
		if err := s.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			s.logger.Fatal("监听失败", zap.Error(err))
		}
	}()

	return nil
}

// WaitForSignal 等待系统信号
func (s *Server) WaitForSignal() {
	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	s.logger.Info("接收到系统信号，准备优雅退出")
}

// Stop 优雅停止服务器
func (s *Server) Stop() error {
	// 停止任务调度器
	s.taskScheduler.Stop()

	// 创建超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 关闭HTTP服务器
	if err := s.httpServer.Shutdown(ctx); err != nil {
		s.logger.Error("服务器关闭出错", zap.Error(err))
		return err
	}

	// 关闭应用程序资源
	if err := s.app.Close(); err != nil {
		s.logger.Error("关闭应用程序资源失败", zap.Error(err))
		return err
	}

	s.logger.Info("服务已完全退出")
	return nil
}
