package di

import (
	inventoryRepository "backend/internal/modules/cmdb/repository/inventory"
	inventoryService "backend/internal/modules/cmdb/service/inventory"
	importhandler "backend/internal/modules/import/handle"
	"backend/internal/modules/user"
	"log"

	serverController "backend/internal/modules/server/controller"
	serverRepository "backend/internal/modules/server/repository"
	serverService "backend/internal/modules/server/service"

	// 用户模块(移至模块化方式注册，不再需要在此导入)
	userService "backend/internal/modules/user/service"

	// 位置管理模块
	locationController "backend/internal/modules/cmdb/controller/location"
	locationRepository "backend/internal/modules/cmdb/repository/location"
	locationService "backend/internal/modules/cmdb/service/location"

	//审计模块

	//设备资产管理模块
	assetController "backend/internal/modules/cmdb/controller/asset"
	assetRepository "backend/internal/modules/cmdb/repository/asset"
	assetService "backend/internal/modules/cmdb/service/asset"

	// 导入模块
	importController "backend/internal/modules/import/controller"
	importService "backend/internal/modules/import/service"

	// 套餐模板相关
	templateController "backend/internal/modules/cmdb/controller/template"
	templateRepo "backend/internal/modules/cmdb/repository/template"
	templateService "backend/internal/modules/cmdb/service/template"

	// 文件上传模块已移至模块化方式注册，不再需要在此导入
	// fileController "backend/internal/modules/file/controller"
	fileService "backend/internal/modules/file/service"

	productRepo "backend/internal/modules/cmdb/repository/product"

	// 入库模块
	inboundController "backend/internal/modules/cmdb/controller/inbound"
	inboundRepository "backend/internal/modules/cmdb/repository/inbound"
	inboundService "backend/internal/modules/cmdb/service/inbound"

	// 采购模块
	purchaseControler "backend/internal/modules/purchase_old/controller"
	purchaseReposity "backend/internal/modules/purchase_old/repository"
	purchaseService "backend/internal/modules/purchase_old/service"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// APIController 包含所有控制器
type APIController struct {
	// 现有模块
	GpuServerCtrl *serverController.GpuServerController
	ServerCtrl    *serverController.ServerController

	// 位置管理模块
	RegionCtrl     *locationController.RegionController
	AZCtrl         *locationController.AZController
	DataCenterCtrl *locationController.DataCenterController
	RoomCtrl       *locationController.RoomController
	CabinetCtrl    *locationController.CabinetController

	// 资产管理模块
	DeviceCtrl         *assetController.DeviceController
	ResourceCtrl       *assetController.ResourceController
	StatusChangeCtrl   *assetController.StatusChangeController
	DeviceResourceCtrl *assetController.DeviceResourceController

	// 导入模块
	ImportCtrl *importController.ImportController

	// 套餐模板相关
	MachineTemplateCtrl   *templateController.MachineTemplateController
	TemplateComponentCtrl *templateController.TemplateComponentController

	// 文件上传控制器已移至模块化方式注册，不再需要在此定义
	// FileCtrl *fileController.FileController

	// 入库管理
	InboundCtrl *inboundController.InboundController

	// 采购模块
	purchaseCtrl *purchaseControler.PurchaseController

	// 允许其他模块获取用户服务
	jwtSecret string
	db        *gorm.DB
}

// NewAPIController 初始化所有控制器
func NewAPIController(db *gorm.DB, jwtSecret string) *APIController {
	// 初始化现有模块
	gpuRepo := serverRepository.NewGpuServerRepository(db)
	serverRepo := serverRepository.NewServerRepository(db)

	gpuSvc := serverService.NewGpuServerService(gpuRepo)
	serverSvc := serverService.NewServerService(serverRepo)

	// 初始化位置管理模块
	regionRepo := locationRepository.NewRegionRepository(db)
	azRepo := locationRepository.NewAZRepository(db)
	dataCenterRepo := locationRepository.NewDataCenterRepository(db)
	roomRepo := locationRepository.NewRoomRepository(db)
	cabinetRepo := locationRepository.NewCabinetRepository(db)

	regionSvc := locationService.NewRegionService(regionRepo)
	azSvc := locationService.NewAZService(azRepo)
	dataCenterSvc := locationService.NewDataCenterService(dataCenterRepo)
	roomSvc := locationService.NewRoomService(roomRepo)
	cabinetSvc := locationService.NewCabinetService(cabinetRepo)

	//初始化资产管理模块
	deviceRepo := assetRepository.NewDeviceRepository(db)
	resourceRepo := assetRepository.NewResourceRepository(db)

	deviceSvc := assetService.NewDeviceService(deviceRepo)
	resourceSvc := assetService.NewResourceService(resourceRepo)

	// 初始化资产状态变更模块
	statusChangeRepo := assetRepository.NewStatusChangeRepository(db)
	statusChangeSvc := assetService.NewStatusChangeService(statusChangeRepo, deviceRepo, resourceRepo)
	statusChangeCtrl := assetController.NewStatusChangeController(statusChangeSvc)

	// 使用合并后的设备服务实现设备资源控制器
	deviceResourceCtrl := assetController.NewDeviceResourceController(deviceSvc)

	// 注册导入服务和控制器
	importService := importService.NewImportService(db)
	importCtrl := importController.NewImportController(importService)

	// 初始化套餐模板相关仓库
	machineTemplateRepo := templateRepo.NewMachineTemplateRepository(db)
	templateComponentRepo := templateRepo.NewTemplateComponentRepository(db)
	productRepository := productRepo.NewProductRepository(db)

	// 初始化套餐模板相关服务
	machineTemplateService := templateService.NewMachineTemplateService(machineTemplateRepo, templateComponentRepo)
	templateComponentService := templateService.NewTemplateComponentService(
		templateComponentRepo, machineTemplateRepo, productRepository,
	)

	// 初始化套餐模板相关控制器
	machineTemplateController := templateController.NewMachineTemplateController(machineTemplateService)
	templateComponentController := templateController.NewTemplateComponentController(templateComponentService)

	// 文件上传服务和控制器已移至模块化方式注册，不再需要在此初始化
	// fileSvc := fileService.NewFileService(db)
	// fileCtrl := fileController.NewFileController(fileSvc)

	// 初始化 采购相关模块
	purchaseRepo := purchaseReposity.NewPurchaseRepository(db)
	purchaseSvc := purchaseService.NewPurchaseService(purchaseRepo)
	purchaseCtrl := purchaseControler.NewPurchaseController(purchaseSvc)

	// 初始化入库相关模块
	inboundRepo := inboundRepository.NewInboundRepository(db)
	warehouseRepo := assetRepository.NewWarehouseRepository(db)
	inventoryRepo := inventoryRepository.NewInventoryRepository(db)
	inventorySvc := inventoryService.NewInventoryService(inventoryRepo, db)

	// 服务器相关模块
	serviceRepo := serverRepository.NewServerRepository(db)
	serviceSvc := serverService.NewServerService(serviceRepo)

	inboundSvc := inboundService.InitInboundService(inboundRepo, purchaseSvc, inventorySvc, inventoryRepo, serviceSvc)
	spareHandler := importhandler.NewSpareHandler(db)
	productHdl := importhandler.NewProductHandler(db)

	inboundImportSvc := inboundService.NewInboundImportService(inboundRepo, spareHandler, productHdl, productRepository, warehouseRepo)
	fileSvc := fileService.NewFileService(db)
	inboundCtrl := inboundController.NewInboundController(inboundSvc, inboundImportSvc, fileSvc)

	return &APIController{
		// 现有模块
		GpuServerCtrl: serverController.NewGpuServerController(gpuSvc),
		ServerCtrl:    serverController.NewServerController(serverSvc),

		// cmdb位置管理模块
		RegionCtrl:     locationController.NewRegionController(regionSvc),
		AZCtrl:         locationController.NewAZController(azSvc),
		DataCenterCtrl: locationController.NewDataCenterController(dataCenterSvc),
		RoomCtrl:       locationController.NewRoomController(roomSvc),
		CabinetCtrl:    locationController.NewCabinetController(cabinetSvc),

		// cmdb资产管理模块
		DeviceCtrl:         assetController.NewDeviceController(deviceSvc),
		ResourceCtrl:       assetController.NewResourceController(resourceSvc),
		StatusChangeCtrl:   statusChangeCtrl,
		DeviceResourceCtrl: deviceResourceCtrl,
		ImportCtrl:         importCtrl,

		// 套餐模板相关控制器
		MachineTemplateCtrl:   machineTemplateController,
		TemplateComponentCtrl: templateComponentController,
		// 入库管理
		InboundCtrl: inboundCtrl,

		// 文件上传控制器已移至模块化方式注册，不再需要在此赋值
		// FileCtrl: fileCtrl,

		// 采购模块
		purchaseCtrl: purchaseCtrl,

		// 保存基本信息用于GetUserService
		jwtSecret: jwtSecret,
		db:        db,
	}
}

// GetUserService 获取用户服务实例，供其他模块使用
// 注意: 此方法仍保留用于兼容性，但实际调用了用户模块的服务
func (c *APIController) GetUserService() userService.IUserService {
	// 创建基本的日志记录器(如果没有提供)
	logger, err := zap.NewProduction()
	if err != nil {
		log.Printf("创建日志记录器失败: %v", err)
		return nil
	}

	// 从用户模块获取实例
	module := user.NewModule(c.db, logger, nil, c.jwtSecret)

	// 初始化模块
	if err := module.Initialize(); err != nil {
		logger.Error("初始化用户模块失败", zap.Error(err))
		return nil
	}

	return module.GetUserService()
}
