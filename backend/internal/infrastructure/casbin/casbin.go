package casbin

import (
	"backend/internal/modules/system/model"
	"backend/internal/modules/system/repository"
	"context"
	"fmt"

	"github.com/casbin/casbin/v2"
	gormadapter "github.com/casbin/gorm-adapter/v3"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// CasbinService 权限服务
type CasbinService struct {
	enforcer *casbin.Enforcer
	db       *gorm.DB
	menuRepo repository.IMenuRepository
	logger   *zap.Logger
}

// NewCasbinService 创建Casbin服务
func NewCasbinService(db *gorm.DB, menuRepo repository.IMenuRepository, logger *zap.Logger) (*CasbinService, error) {
	adapter, err := gormadapter.NewAdapterByDB(db)
	if err != nil {
		return nil, fmt.Errorf("创建Casbin适配器错误: %w", err)
	}

	enforcer, err := casbin.NewEnforcer("./configs/rbac_model.conf", adapter)
	if err != nil {
		return nil, fmt.Errorf("创建Casbin执行器错误: %w", err)
	}

	// 启用自动保存
	enforcer.EnableAutoSave(true)

	return &CasbinService{
		enforcer: enforcer,
		db:       db,
		menuRepo: menuRepo,
		logger:   logger,
	}, nil
}

// LoadPolicy 加载策略
func (s *CasbinService) LoadPolicy() error {
	return s.enforcer.LoadPolicy()
}

// InitPoliciesFromDB 从数据库初始化策略
func (s *CasbinService) InitPoliciesFromDB(ctx context.Context) error {
	// 清除所有现有策略
	s.enforcer.ClearPolicy()

	// 获取所有菜单项
	menus, err := s.menuRepo.GetAllMenusWithoutStatusFilter(ctx)
	if err != nil {
		return err
	}

	// 获取所有角色权限关联
	var rolePermissions []model.RolePermission
	if err := s.db.WithContext(ctx).Find(&rolePermissions).Error; err != nil {
		return err
	}

	// 构建菜单ID到authCode的映射
	menuAuthMap := make(map[uint]string)
	for _, menu := range menus {
		if menu.AuthCode != "" {
			menuAuthMap[menu.ID] = menu.AuthCode
		}
	}

	// 添加策略
	for _, rp := range rolePermissions {
		authCode, ok := menuAuthMap[rp.PermissionID]
		if !ok || authCode == "" {
			continue
		}

		// 为所有HTTP方法添加策略
		for _, method := range []string{"GET", "POST", "PUT", "DELETE"} {
			_, err = s.enforcer.AddPolicy(rp.RoleID, authCode, method)
			if err != nil {
				s.logger.Error("添加策略失败",
					zap.Error(err),
					zap.String("roleID", rp.RoleID),
					zap.String("authCode", authCode),
					zap.String("method", method))
			}
		}
	}

	return nil
}

// SyncRolePermissions 同步角色权限
func (s *CasbinService) SyncRolePermissions(ctx context.Context, roleID string, permissionIDs []uint) error {
	// 先删除该角色的所有权限
	_, err := s.enforcer.DeletePermissionsForUser(roleID)
	if err != nil {
		return err
	}

	// 获取所有菜单
	menus, err := s.menuRepo.GetAllMenusWithoutStatusFilter(ctx)
	if err != nil {
		return err
	}

	// 构建ID到authCode的映射
	menuAuthMap := make(map[uint]string)
	for _, menu := range menus {
		if menu.AuthCode != "" {
			menuAuthMap[menu.ID] = menu.AuthCode
		}
	}

	// 添加新权限
	for _, permID := range permissionIDs {
		authCode, ok := menuAuthMap[permID]
		if !ok || authCode == "" {
			continue
		}

		// 为所有HTTP方法添加策略
		for _, method := range []string{"GET", "POST", "PUT", "DELETE"} {
			_, err = s.enforcer.AddPolicy(roleID, authCode, method)
			if err != nil {
				s.logger.Error("同步角色权限失败",
					zap.Error(err),
					zap.String("roleID", roleID),
					zap.String("authCode", authCode),
					zap.String("method", method))
				return err
			}
		}
	}

	return nil
}

// CheckPermission 检查权限
func (s *CasbinService) CheckPermission(roles []string, obj string, act string) bool {
	// 超级管理员直接放行
	for _, role := range roles {
		if role == "super" {
			return true
		}

		// 检查角色权限
		result, err := s.enforcer.Enforce(role, obj, act)
		if err != nil {
			s.logger.Error("检查权限出错",
				zap.Error(err),
				zap.String("role", role),
				zap.String("obj", obj),
				zap.String("act", act))
			continue // 出错时继续检查下一个角色
		}

		if result {
			return true
		}
	}

	return false
}

// GetEnforcer 获取enforcer
func (s *CasbinService) GetEnforcer() *casbin.Enforcer {
	return s.enforcer
}
