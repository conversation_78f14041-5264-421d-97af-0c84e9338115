package temporal

import (
	cmdbWorkflow "backend/internal/modules/cmdb/workflow"
	cmdbActivities "backend/internal/modules/cmdb/workflow/activities"
	hardwoareMaintenanceWorkflow "backend/internal/modules/hardware_maintenance/workflow"
	"backend/internal/modules/purchase/constants"
	common1 "backend/internal/modules/software_maintenance/common"
	softwareMaintenanceWorkflow "backend/internal/modules/software_maintenance/workflow"
	"backend/internal/modules/ticket/common"
	ticketWorkflow "backend/internal/modules/ticket/workflow"
	"backend/internal/modules/ticket/workflow/activities"

	purchaseWorkflow "backend/internal/modules/purchase/workflow"
	purchaseActivity "backend/internal/modules/purchase/workflow/activity"
	contractActivity "backend/internal/modules/purchase/workflow/activity/contract"
	paymentActivity "backend/internal/modules/purchase/workflow/activity/payment"
	inquiryActivity "backend/internal/modules/purchase/workflow/activity/inquiry"

	temporalWorker "go.temporal.io/sdk/worker"
)

// initWorkers 初始化所有的Temporal workers
func (w *Worker) initWorkers() error {
	if w.repositories == nil {
		return ErrRepoNotInitialized
	}

	// 创建各种workers
	workers := []temporalWorker.Worker{
		w.createFaultTicketWorker(),    // 报障单Worker
		w.createRepairTicketWorker(),   // 维修单Worker
		w.createOutboundTicketWorker(), // 出库单Worker
		w.createEntryTicketWorker(),    // 入室单Worker

		w.createPartInboundTicketWorker(),    // 配件入库单Worker
		w.createDeviceInboundTicketWorker(),  // 设备入库单Worker
		w.createPartOutboundTicketWorker(),   // 配件出库单Worker
		w.createDeviceOutboundTicketWorker(), // 设备出库单Worker

		w.createNewInboundTicketWorker(),        // 新购入库单Worker
		w.createRepairInboundTicketWorker(),     // 返修入库单Worker
		w.createDismantledInboundTicketWorker(), // 拆机入库单Worker
		w.createAcceptanceOrderWorker(),         // 验收工单Worker
		w.createRackingTicketWorker(),           // 上架工单Worker
		w.createLaunchTicketWorker(),            // 上线工单Worker
		w.createOfflineTicketWorker(),           // 下线工单Worker
		w.createPurchaseRequestWorker(),         // 采购申请Worker
		w.createPurchaseInquiryWorker(),         // 采购询比价Worker
		w.createPurchaseContractWorker(),        // 采购合同Worker
		w.createPaymentRequestWorker(),          // 付款申请Worker
		w.createArrivalWorker(),                 // 到货管理Worker
		w.createShipmentWorker(),                // 发货管理Worker
	}

	w.workers = workers
	return nil
}

// createFaultTicketWorker 创建故障单Worker
func (w *Worker) createFaultTicketWorker() temporalWorker.Worker {
	// 创建Worker
	faultTicketWorker := temporalWorker.New(w.temporalClient, ticketWorkflow.FaultTicketTaskQueue, temporalWorker.Options{})

	// 注册工作流
	faultTicketWorker.RegisterWorkflow(ticketWorkflow.FaultTicketWorkflow)

	// 注册活动
	faultTicketWorker.RegisterActivity(activities.AcknowledgeFaultActivity)
	faultTicketWorker.RegisterActivity(activities.DiagnoseFaultActivity)
	faultTicketWorker.RegisterActivity(activities.CreateCustomerApprovalActivity)
	faultTicketWorker.RegisterActivity(activities.WaitForCustomerApprovalActivity)
	faultTicketWorker.RegisterActivity(activities.CancelFaultTicketActivity)
	faultTicketWorker.RegisterActivity(activities.CreateRepairTicketActivity)
	faultTicketWorker.RegisterActivity(activities.VerifyRepairActivity)
	faultTicketWorker.RegisterActivity(activities.SummarizeFaultTicketActivity)
	faultTicketWorker.RegisterActivity(activities.UpdateSLARecordActivity)
	faultTicketWorker.RegisterActivity(activities.GetRepairSelectionActivity)
	faultTicketWorker.RegisterActivity(activities.GetCustomerApprovalActivity)
	faultTicketWorker.RegisterActivity(activities.GetVerificationActivity)

	// 注册冷迁移后创建新报障单活动
	faultTicketWorker.RegisterActivity(activities.CreateFaultTicketAfterColdMigrationActivity)

	// 注册新增的报障单活动
	faultTicketWorker.RegisterActivity(activities.WaitForTicketAcceptanceActivity)
	faultTicketWorker.RegisterActivity(activities.WaitForRepairSelectionActivity)
	faultTicketWorker.RegisterActivity(activities.WaitForVerificationActivity)
	faultTicketWorker.RegisterActivity(activities.WaitForSummaryActivity)
	faultTicketWorker.RegisterActivity(activities.UpdateFaultTicketStatusActivity)
	faultTicketWorker.RegisterActivity(activities.ExecuteSystemRestartActivity)
	faultTicketWorker.RegisterActivity(activities.ExecuteSoftwareRepairActivity)
	faultTicketWorker.RegisterActivity(activities.ExecuteHardwareRepairActivity)

	// 注册手动触发相关活动
	faultTicketWorker.RegisterActivity(activities.WaitForManualActionActivity)
	faultTicketWorker.RegisterActivity(activities.TriggerManualAction)
	faultTicketWorker.RegisterActivity(activities.GetWaitingStagesActivity)

	// 注册通知相关活动
	faultTicketWorker.RegisterActivity(activities.SendNotificationActivity)
	faultTicketWorker.RegisterActivity(activities.SendRepairTicketWaitingAcceptNotificationActivity)
	faultTicketWorker.RegisterActivity(activities.SendRepairTicketWaitingVerificationNotificationActivity)

	// 注册授权维修单活动
	faultTicketWorker.RegisterActivity(activities.AuthorizeRepairTicketForFaultActivity)

	// 注册获取故障单活动
	faultTicketWorker.RegisterActivity(activities.GetFaultTicketByIDActivity)

	// 注册计算时间指标活动
	faultTicketWorker.RegisterActivity(activities.CalculateDurationsActivity)

	// 注册设备状态更新活动
	faultTicketWorker.RegisterActivity(activities.UpdateDeviceStatusActivity)
	faultTicketWorker.RegisterActivity(activities.UpdateDeviceHardwareStatusActivity)
	faultTicketWorker.RegisterActivity(activities.UpdateDeviceAssetStatusActivity)
	faultTicketWorker.RegisterActivity(activities.UpdateResourceBizStatusActivity)
	faultTicketWorker.RegisterActivity(activities.GetDeviceOriginalStatusActivity)
	faultTicketWorker.RegisterActivity(activities.GetDeviceOriginalStatusBySNActivity)
	faultTicketWorker.RegisterActivity(activities.UpdateFieldsActivity)

	// 注册冷迁移相关活动
	faultTicketWorker.RegisterActivity(activities.ProcessColdMigrationActivity)
	faultTicketWorker.RegisterActivity(activities.HandleColdMigrationActivity)
	faultTicketWorker.RegisterActivity(activities.RevertColdMigrationActivity)

	return faultTicketWorker
}

// createRepairTicketWorker 创建维修单Worker
func (w *Worker) createRepairTicketWorker() temporalWorker.Worker {
	// 创建Worker
	repairTicketWorker := temporalWorker.New(w.temporalClient, ticketWorkflow.RepairTicketTaskQueue, temporalWorker.Options{})

	// 注册工作流
	repairTicketWorker.RegisterWorkflow(ticketWorkflow.RepairWorkflow)

	// 注册活动
	repairTicketWorker.RegisterActivity(activities.GetRepairTicketActivity)
	repairTicketWorker.RegisterActivity(activities.EngineerAcknowledgeActivity)
	repairTicketWorker.RegisterActivity(activities.UpdateRepairTicketStatusActivity)
	repairTicketWorker.RegisterActivity(activities.ArriveOnSiteActivity)
	repairTicketWorker.RegisterActivity(activities.RecordHardwareReplaceStartActivity)
	repairTicketWorker.RegisterActivity(activities.CompleteHardwareReplaceActivity)
	repairTicketWorker.RegisterActivity(activities.CompleteRepairActivity)
	repairTicketWorker.RegisterActivity(activities.EngineerTakeRepairTicketActivity)
	repairTicketWorker.RegisterActivity(activities.ListAvailableRepairTicketsActivity)
	repairTicketWorker.RegisterActivity(activities.UpdateRepairTicketFields)

	// 注册通知相关活动
	repairTicketWorker.RegisterActivity(activities.SendRepairTicketWaitingAcceptNotificationActivity)
	repairTicketWorker.RegisterActivity(activities.SendRepairTicketWaitingVerificationNotificationActivity)
	repairTicketWorker.RegisterActivity(activities.SendVerificationFailedNotification)

	// 注册获取故障单活动
	repairTicketWorker.RegisterActivity(activities.GetFaultTicketByIDActivity)

	// 注册状态共享活动
	repairTicketWorker.RegisterActivity(activities.UpdateFaultTicketStatusActivity)

	// 注册设备状态活动
	repairTicketWorker.RegisterActivity(activities.UpdateDeviceStatusActivity)
	repairTicketWorker.RegisterActivity(activities.UpdateDeviceHardwareStatusActivity)
	repairTicketWorker.RegisterActivity(activities.UpdateDeviceAssetStatusActivity)
	repairTicketWorker.RegisterActivity(activities.UpdateResourceBizStatusActivity)
	repairTicketWorker.RegisterActivity(activities.GetDeviceOriginalStatusActivity)
	repairTicketWorker.RegisterActivity(activities.GetDeviceOriginalStatusBySNActivity)

	// 注册冷迁移相关活动
	repairTicketWorker.RegisterActivity(activities.ProcessColdMigrationActivity)
	repairTicketWorker.RegisterActivity(activities.HandleColdMigrationActivity)
	repairTicketWorker.RegisterActivity(activities.RevertColdMigrationActivity)

	return repairTicketWorker
}

// createOutboundTicketWorker 创建出库单Worker
func (w *Worker) createOutboundTicketWorker() temporalWorker.Worker {
	// 创建Worker
	outboundTicketWorker := temporalWorker.New(w.temporalClient, cmdbWorkflow.OutboundTicketTaskQueue, temporalWorker.Options{})

	// 注册工作流
	outboundTicketWorker.RegisterWorkflow(cmdbWorkflow.OutboundTicketWorkflow)

	// 注册活动
	outboundTicketWorker.RegisterActivity(cmdbActivities.CreateCustomerApprovalActivity)
	outboundTicketWorker.RegisterActivity(cmdbActivities.GetOutboundTicketByIDActivity)
	outboundTicketWorker.RegisterActivity(cmdbActivities.ExecuteAllocateOutboundActivity)
	outboundTicketWorker.RegisterActivity(cmdbActivities.UpdateOutboundTicketStatusActivity)
	outboundTicketWorker.RegisterActivity(cmdbActivities.ExecuteSpareOutboundActivity)
	outboundTicketWorker.RegisterActivity(cmdbActivities.GetApprovalActivity)

	return outboundTicketWorker
}

// createEntryTicketWorker 创建入室单Worker
func (w *Worker) createEntryTicketWorker() temporalWorker.Worker {
	// 创建Worker
	entryTicketWorker := temporalWorker.New(w.temporalClient, ticketWorkflow.EntryTicketTaskQueue, temporalWorker.Options{})

	// 注册工作流
	entryTicketWorker.RegisterWorkflow(ticketWorkflow.EntryTicketWorkflow)

	// 注册活动
	entryTicketWorker.RegisterActivity(activities.GetApprovalActivity)
	entryTicketWorker.RegisterActivity(activities.GetEntryTicketByIDActivity)
	entryTicketWorker.RegisterActivity(activities.UpdateEntryTicketStatusActivity)
	entryTicketWorker.RegisterActivity(activities.SaveEntryPersonActivity)

	return entryTicketWorker
}

// createPartOutboundTicketWorker 创建配件出库单Worker
func (w *Worker) createPartOutboundTicketWorker() temporalWorker.Worker {
	// 创建Worker
	partOutboundTicketWorker := temporalWorker.New(w.temporalClient, common.PartOutboundTaskQueue, temporalWorker.Options{})

	// @配件出库工作流
	partOutboundTicketWorker.RegisterWorkflow(cmdbWorkflow.PartOutboundWorkflow)
	partOutboundTicketWorker.RegisterActivity(w.repositories.PartOutboundActivities)

	return partOutboundTicketWorker
}

func (w *Worker) createDeviceOutboundTicketWorker() temporalWorker.Worker {
	// 创建Worker
	deviceOutboundTicketWorker := temporalWorker.New(w.temporalClient, common.DeviceOutboundTaskQueue, temporalWorker.Options{})

	// @设备出库工作流
	deviceOutboundTicketWorker.RegisterWorkflow(cmdbWorkflow.DeviceOutboundWorkflow)
	deviceOutboundTicketWorker.RegisterActivity(w.repositories.DeviceOutboundActivities)

	return deviceOutboundTicketWorker
}

// createPartInboundTicketWorker 创建配件入库单Worker
func (w *Worker) createPartInboundTicketWorker() temporalWorker.Worker {
	// 创建Worker
	inboundTicketWorker := temporalWorker.New(w.temporalClient, common.PartInboundTaskQueue, temporalWorker.Options{})

	// @ 配件入库工作流
	inboundTicketWorker.RegisterWorkflow(ticketWorkflow.PartInboundWorkflow)
	inboundTicketWorker.RegisterActivity(w.repositories.PartInboundActivities)
	return inboundTicketWorker
}

// createDeviceInboundTicketWorker 创建设备入库单Worker
func (w *Worker) createDeviceInboundTicketWorker() temporalWorker.Worker {
	// 创建Worker
	deviceInboundWorker := temporalWorker.New(w.temporalClient, common.DeviceInboundTaskQueue, temporalWorker.Options{})
	// 注册工作流
	deviceInboundWorker.RegisterWorkflow(ticketWorkflow.DeviceInboundWorkflow)
	// 注册活动
	deviceInboundWorker.RegisterActivity(w.repositories.DeviceInboundActivities)
	return deviceInboundWorker
}

// createNewInboundTicketWorker 创建新购入库单Worker
func (w *Worker) createNewInboundTicketWorker() temporalWorker.Worker {
	// 创建Worker
	newInboundTicketWorker := temporalWorker.New(w.temporalClient, common.NewInboundTaskQueue, temporalWorker.Options{})

	// 注册工作流
	//newInboundTicketWorker.RegisterWorkflow(ticketWorkflow.NewInboundWorkflow)
	newInboundTicketWorker.RegisterWorkflow(ticketWorkflow.NewPartInboundWorkflow)

	// 注册活动
	newInboundTicketWorker.RegisterActivity(w.repositories.NewInboundActivities)

	return newInboundTicketWorker
}

// createRepairInboundTicketWorker 创建返修入库单Worker
func (w *Worker) createRepairInboundTicketWorker() temporalWorker.Worker {
	// 创建Worker
	repairInboundTicketWorker := temporalWorker.New(w.temporalClient, common.RepairInboundTaskQueue, temporalWorker.Options{})

	// 注册工作流
	repairInboundTicketWorker.RegisterWorkflow(ticketWorkflow.RepairPartInboundWorkflow)

	// 注册活动
	repairInboundTicketWorker.RegisterActivity(w.repositories.RepairInboundActivities)

	return repairInboundTicketWorker
}

// createDismantledInboundTicketWorker 创建拆机入库单Worker
func (w *Worker) createDismantledInboundTicketWorker() temporalWorker.Worker {
	// 创建Worker
	dismantledInboundTicketWorker := temporalWorker.New(w.temporalClient, common.DismantledInboundTaskQueue, temporalWorker.Options{})

	// 注册工作流
	dismantledInboundTicketWorker.RegisterWorkflow(ticketWorkflow.DismantledPartInboundWorkflow)

	// 注册活动
	dismantledInboundTicketWorker.RegisterActivity(w.repositories.DismantledInboundActivities)

	return dismantledInboundTicketWorker
}

func (w *Worker) createAcceptanceOrderWorker() temporalWorker.Worker {
	// 创建Worker
	acceptanceOrderWorker := temporalWorker.New(w.temporalClient, hardwoareMaintenanceWorkflow.AcceptanceOrderTaskQueue, temporalWorker.Options{})

	// 注册工作流
	//acceptanceOrderWorker.RegisterWorkflow(hardwoareMaintenanceWorkflow.AcceptanceOrderWorkflow)
	acceptanceOrderWorker.RegisterWorkflow(hardwoareMaintenanceWorkflow.AcceptanceOrderWorkflowV2)

	// 注册活动
	acceptanceOrderWorker.RegisterActivity(w.repositories.AcceptanceOrderActivities)

	return acceptanceOrderWorker
}

func (w *Worker) createRackingTicketWorker() temporalWorker.Worker {
	rackingTicketWorker := temporalWorker.New(w.temporalClient, hardwoareMaintenanceWorkflow.RackingTicketTaskQueue, temporalWorker.Options{})

	rackingTicketWorker.RegisterWorkflow(hardwoareMaintenanceWorkflow.RackingTicketWorkflow)

	rackingTicketWorker.RegisterActivity(w.repositories.RackingTicketActivities)
	return rackingTicketWorker
}

// createPurchaseRequestWorker 创建采购申请Worker
func (w *Worker) createPurchaseRequestWorker() temporalWorker.Worker {
	// 创建Worker
	purchaseRequestWorker := temporalWorker.New(w.temporalClient, constants.TaskQueuePurchaseRequest, temporalWorker.Options{})

	// 注册工作流
	purchaseRequestWorker.RegisterWorkflow(purchaseWorkflow.PurchaseRequestApprovalWorkflow)

	// 注册活动
	purchaseRequestWorker.RegisterActivity(purchaseActivity.NotifyCTOActivity)
	purchaseRequestWorker.RegisterActivity(purchaseActivity.UpdateRequestStatusActivity)
	purchaseRequestWorker.RegisterActivity(purchaseActivity.SendNotificationActivity)
	purchaseRequestWorker.RegisterActivity(purchaseActivity.SendFeishuPurchaseNotificationActivity)
	purchaseRequestWorker.RegisterActivity(inquiryActivity.GetUserRealNameActivity)

	return purchaseRequestWorker
}

// createPurchaseInquiryWorker 创建采购询比价Worker
func (w *Worker) createPurchaseInquiryWorker() temporalWorker.Worker {
	// 创建Worker
	purchaseInquiryWorker := temporalWorker.New(w.temporalClient, constants.TaskQueuePurchaseInquiry, temporalWorker.Options{})

	// 注册工作流
	purchaseInquiryWorker.RegisterWorkflow(purchaseWorkflow.PurchaseInquiryApprovalWorkflow)

	// 注册活动
	purchaseInquiryWorker.RegisterActivity(inquiryActivity.UpdateInquiryStatusActivity)
	purchaseInquiryWorker.RegisterActivity(inquiryActivity.SendFeishuInquiryNotificationActivity)
	purchaseInquiryWorker.RegisterActivity(inquiryActivity.GetUserRealNameActivity)

	return purchaseInquiryWorker
}

// createPurchaseContractWorker 创建采购合同Worker
func (w *Worker) createPurchaseContractWorker() temporalWorker.Worker {
	// 创建Worker
	purchaseContractWorker := temporalWorker.New(w.temporalClient, constants.TaskQueuePurchaseContract, temporalWorker.Options{})

	// 注册工作流
	purchaseContractWorker.RegisterWorkflow(purchaseWorkflow.PurchaseContractApprovalWorkflow)

	// 注册活动
	purchaseContractWorker.RegisterActivity(contractActivity.UpdateContractStatusActivity)
	purchaseContractWorker.RegisterActivity(contractActivity.SendFeishuContractNotificationActivity)
	purchaseContractWorker.RegisterActivity(contractActivity.GetUserRealNameActivity)

	return purchaseContractWorker
}

// createLaunchTicketWorker 创建上线工单worker
func (w *Worker) createLaunchTicketWorker() temporalWorker.Worker {
	// 创建worker
	launchTicketWorker := temporalWorker.New(w.temporalClient, common1.LaunchTaskQueue, temporalWorker.Options{})

	// 注册工作流
	launchTicketWorker.RegisterWorkflow(softwareMaintenanceWorkflow.SoftwareLaunchWorkflow)

	// 注册活动
	launchTicketWorker.RegisterActivity(w.repositories.LaunchTicketActivities)

	return launchTicketWorker
}

// createOfflineTicketWorker 创建下线工单worker
func (w *Worker) createOfflineTicketWorker() temporalWorker.Worker {
	// 创建worker
	offlineTicketWorker := temporalWorker.New(w.temporalClient, common1.OfflineTaskQueue, temporalWorker.Options{})

	// 注册工作流
	offlineTicketWorker.RegisterWorkflow(softwareMaintenanceWorkflow.SoftwareOfflineWorkflow)

	// 注册活动
	offlineTicketWorker.RegisterActivity(w.repositories.OfflineTicketActivities)

	return offlineTicketWorker
}

// createPaymentRequestWorker 创建付款申请Worker
func (w *Worker) createPaymentRequestWorker() temporalWorker.Worker {
	// 创建Worker
	paymentRequestWorker := temporalWorker.New(w.temporalClient, constants.TaskQueuePaymentRequest, temporalWorker.Options{})

	// 注册工作流
	paymentRequestWorker.RegisterWorkflow(purchaseWorkflow.PaymentRequestWorkflow)

	// 注册活动
	paymentRequestWorker.RegisterActivity(paymentActivity.UpdatePaymentStatusActivity)
	paymentRequestWorker.RegisterActivity(paymentActivity.SendFeishuPaymentNotificationActivity)
	paymentRequestWorker.RegisterActivity(contractActivity.GetUserRealNameActivity)

	return paymentRequestWorker
}

// createArrivalWorker 创建到货管理Worker
func (w *Worker) createArrivalWorker() temporalWorker.Worker {
	// 创建Worker
	arrivalWorker := temporalWorker.New(w.temporalClient, constants.TaskQueueArrival, temporalWorker.Options{})

	// 注册工作流
	arrivalWorker.RegisterWorkflow(purchaseWorkflow.ArrivalWorkflow)

	// 注册活动
	arrivalWorker.RegisterActivity(w.repositories.ArrivalActivities.UpdateArrivalStatusActivity)
	arrivalWorker.RegisterActivity(w.repositories.ArrivalActivities.SendArrivalNotificationActivity)
	arrivalWorker.RegisterActivity(w.repositories.ArrivalActivities.GetArrivalRequestTypeActivity)
	arrivalWorker.RegisterActivity(w.repositories.ArrivalActivities.GetUserRealNameActivity)
	arrivalWorker.RegisterActivity(w.repositories.ArrivalActivities.SendFeishuArrivalNotificationActivity)

	return arrivalWorker
}

// createShipmentWorker 创建发货管理Worker
func (w *Worker) createShipmentWorker() temporalWorker.Worker {
	// 创建Worker
	shipmentWorker := temporalWorker.New(w.temporalClient, constants.TaskQueueShipment, temporalWorker.Options{})

	// 注册工作流
	shipmentWorker.RegisterWorkflow(purchaseWorkflow.ShipmentWorkflow)

	// 注册活动
	shipmentWorker.RegisterActivity(w.repositories.ShipmentActivities.UpdateShipmentStatusActivity)
	shipmentWorker.RegisterActivity(w.repositories.ShipmentActivities.SendShipmentNotificationActivity)

	return shipmentWorker
}
