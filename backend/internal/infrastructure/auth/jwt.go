package auth

import (
	"backend/configs"
	"backend/internal/modules/user/model"
	"os"
	"strconv"
	"time"

	"github.com/dgrijalva/jwt-go"
)

type Claims struct {
	UserID uint `json:"user_id"`
	jwt.StandardClaims
}

// GetJWTSecretKey 统一获取JWT密钥的函数
func GetJWTSecretKey() string {
	// 优先从环境变量获取
	secretKey := os.Getenv("APP_JWT_SECRETKEY")
	if secretKey != "" {
		return secretKey
	}

	// 从配置文件获取
	config, err := configs.LoadConfig()
	if err == nil && config.JWT.SecretKey != "" {
		return config.JWT.SecretKey
	}

	// 默认值
	return "default-insecure-jwt-secret"
}

// GetJWTExpireDuration 统一获取JWT过期时间的函数
func GetJWTExpireDuration() time.Duration {
	// 优先从环境变量获取，单位为分钟
	expireStr := os.Getenv("APP_JWT_EXPIRES")
	if expireStr != "" {
		expireMinutes, err := strconv.Atoi(expireStr)
		if err == nil && expireMinutes > 0 {
			return time.Duration(expireMinutes) * time.Minute
		}
	}

	// 从配置文件获取
	config, err := configs.LoadConfig()
	if err == nil && config.JWT.Expires > 0 {
		return time.Duration(config.JWT.Expires) * time.Minute
	}

	// 默认值：24小时
	return 24 * time.Hour
}

func GenerateToken(user model.User, secret string, expire time.Duration) (string, int64, error) {
	expirationTime := time.Now().Add(expire)
	claims := &Claims{
		UserID: user.ID,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: expirationTime.Unix(),
			IssuedAt:  time.Now().Unix(),
			Issuer:    "hanyun",
			Subject:   "user token",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(secret))
	if err != nil {
		return "", 0, err
	}

	return tokenString, expirationTime.Unix(), nil
}

func ParseToken(tokenString string, secret string) (*Claims, error) {
	claims := &Claims{}
	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(secret), nil
	})

	if err != nil || !token.Valid {
		return nil, err
	}

	return claims, nil
}
