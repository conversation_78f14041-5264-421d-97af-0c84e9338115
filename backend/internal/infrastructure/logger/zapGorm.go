package zapLog

import (
	"context"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm/logger"
)

// ZapLogger 实现了 gorm/logger.Interface 接口，使用 zap 记录日志
type ZapLogger struct {
	logger *zap.Logger
	config logger.Config
}

// NewZapLogger 返回一个新的 ZapLogger 实例
func NewZapLogger(l *zap.Logger, cfg logger.Config) *ZapLogger {
	return &ZapLogger{
		logger: l,
		config: cfg,
	}
}

// LogMode 设置日志级别
func (z *ZapLogger) LogMode(level logger.LogLevel) logger.Interface {
	newCfg := z.config
	newCfg.LogLevel = level
	return &ZapLogger{
		logger: z.logger,
		config: newCfg,
	}
}

// Info 使用 zap 记录 Info 日志
func (z *ZapLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	if z.config.LogLevel >= logger.Info {
		z.logger.Sugar().Infof(msg, data...)
	}
}

// Warn 使用 zap 记录 Warn 日志
func (z *ZapLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	if z.config.LogLevel >= logger.Warn {
		z.logger.Sugar().Warnf(msg, data...)
	}
}

// Error 使用 zap 记录 Error 日志
func (z *ZapLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	if z.config.LogLevel >= logger.Error {
		z.logger.Sugar().Errorf(msg, data...)
	}
}

// Trace 记录 SQL 执行过程日志
func (z *ZapLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	if z.config.LogLevel == logger.Silent {
		return
	}

	elapsed := time.Since(begin)
	sql, rows := fc()

	// 从上下文中提取请求ID等信息
	var requestID string
	if ctx != nil {
		if v, ok := ctx.Value("request_id").(string); ok {
			requestID = v
		}
	}

	// 构建结构化日志字段
	fields := []zap.Field{
		zap.Float64("duration_ms", float64(elapsed.Milliseconds())),
		zap.Int64("rows", rows),
		zap.String("sql", sql),
	}

	if requestID != "" {
		fields = append(fields, zap.String("request_id", requestID))
	}

	if err != nil {
		fields = append(fields, zap.Error(err))
		z.logger.Error("SQL执行错误", fields...)
	} else if elapsed > time.Millisecond*500 { // 慢查询标记
		z.logger.Warn("SQL慢查询", fields...)
	} else {
		z.logger.Info("SQL执行", fields...)
	}
}
