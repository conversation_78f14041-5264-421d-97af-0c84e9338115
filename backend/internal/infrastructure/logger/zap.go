package zapLog

import (
	"context"
	"os"
	"time"

	"backend/configs"

	"github.com/natefinch/lumberjack" // 日志文件切割
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// LogConfig 定义日志配置选项
type LogConfig struct {
	Level          string   `json:"level"`           // 日志级别: debug, info, warn, error
	OutputPaths    []string `json:"output_paths"`    // 输出路径，如 ["stdout", "./logs/app.log"]
	ErrorPaths     []string `json:"error_paths"`     // 错误日志路径
	Encoding       string   `json:"encoding"`        // 编码格式: json 或 console
	MaxSize        int      `json:"max_size"`        // 单个文件最大尺寸(MB)
	MaxAge         int      `json:"max_age"`         // 保留天数
	MaxBackups     int      `json:"max_backups"`     // 最大备份数
	Compress       bool     `json:"compress"`        // 是否压缩
	Development    bool     `json:"development"`     // 是否为开发环境
	EnableSampling bool     `json:"enable_sampling"` // 是否启用采样
}

// DefaultLogConfig 返回默认日志配置
func DefaultLogConfig() LogConfig {
	return LogConfig{
		Level:          "info",
		OutputPaths:    []string{"stdout", "./logs/server.log"},
		ErrorPaths:     []string{"stderr", "./logs/error.log"},
		Encoding:       "json",
		MaxSize:        50,
		MaxAge:         28,
		MaxBackups:     3,
		Compress:       true,
		Development:    false,
		EnableSampling: false,
	}
}

// NewLogger 创建一个根据配置定制的日志记录器
func NewLogger(config LogConfig) (*zap.Logger, error) {
	// 解析日志级别
	var zapLevel zapcore.Level
	switch config.Level {
	case "debug":
		zapLevel = zapcore.DebugLevel
	case "info":
		zapLevel = zapcore.InfoLevel
	case "warn":
		zapLevel = zapcore.WarnLevel
	case "error":
		zapLevel = zapcore.ErrorLevel
	default:
		zapLevel = zapcore.InfoLevel
	}

	// 配置文件输出
	var writers []zapcore.WriteSyncer
	if len(config.OutputPaths) > 0 {
		for _, path := range config.OutputPaths {
			switch path {
			case "stdout":
				writers = append(writers, zapcore.AddSync(os.Stdout))
			case "stderr":
				writers = append(writers, zapcore.AddSync(os.Stderr))
			default:
				// 配置日志切割
				lumberjackLogger := &lumberjack.Logger{
					Filename:   path,
					MaxSize:    config.MaxSize,
					MaxBackups: config.MaxBackups,
					MaxAge:     config.MaxAge,
					Compress:   config.Compress,
				}
				writers = append(writers, zapcore.AddSync(lumberjackLogger))
			}
		}
	} else {
		// 默认输出到控制台
		writers = append(writers, zapcore.AddSync(os.Stdout))
	}

	multiWriter := zapcore.NewMultiWriteSyncer(writers...)

	// 配置基本的编码器设置
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "timestamp",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		FunctionKey:    zapcore.OmitKey,
		MessageKey:     "message",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.MillisDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// 控制台编码器配置 - 使用彩色级别
	consoleEncoderConfig := encoderConfig
	consoleEncoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder

	// JSON编码器配置 - 不使用彩色
	jsonEncoderConfig := encoderConfig
	jsonEncoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	// 创建编码器
	var encoder zapcore.Encoder
	if config.Encoding == "console" {
		encoder = zapcore.NewConsoleEncoder(consoleEncoderConfig)
	} else {
		encoder = zapcore.NewJSONEncoder(jsonEncoderConfig)
	}

	// 构建核心
	core := zapcore.NewCore(encoder, multiWriter, zapLevel)

	// 是否启用采样
	if config.EnableSampling {
		core = zapcore.NewSamplerWithOptions(
			core,
			time.Second, // 采样间隔
			100,         // 初始计数
			100,         // 每采样间隔后保留的日志条数
		)
	}

	// 构建选项
	options := []zap.Option{zap.AddCaller(), zap.AddCallerSkip(1)}
	if config.Development {
		options = append(options, zap.Development())
	}

	// 返回日志器
	return zap.New(core, options...), nil
}

// NewProductionLogger 使用默认配置创建生产环境日志记录器
func NewProductionLogger() (*zap.Logger, error) {
	config := DefaultLogConfig()
	return NewLogger(config)
}

// NewGormFileLogger 创建一个仅保存 GORM 日志的 zap Logger
func NewGormFileLogger() (*zap.Logger, error) {
	config := DefaultLogConfig()
	config.OutputPaths = []string{"stdout", "./logs/gorm.log"}
	return NewLogger(config)
}

// 自定义context key类型，避免与其他包的字符串key冲突
type contextKey string

const (
	// RequestIDKey 是请求ID的上下文键
	requestIDKey contextKey = "request_id"
)

// WithRequestContext 将请求ID添加到日志上下文中
func WithRequestContext(ctx context.Context, logger *zap.Logger, requestID string) (*zap.Logger, context.Context) {
	// 为日志添加请求ID
	ctxLogger := logger.With(zap.String("request_id", requestID))

	// 将请求ID保存到上下文中，供后续使用
	newCtx := context.WithValue(ctx, requestIDKey, requestID)

	return ctxLogger, newCtx
}

// NewLoggerFromConfig 接收配置文件中的日志配置
func NewLoggerFromConfig(config *configs.LogConfig) (*zap.Logger, error) {
	// 解析日志级别
	var zapLevel zapcore.Level
	switch config.Level {
	case "debug":
		zapLevel = zapcore.DebugLevel
	case "info":
		zapLevel = zapcore.InfoLevel
	case "warn":
		zapLevel = zapcore.WarnLevel
	case "error":
		zapLevel = zapcore.ErrorLevel
	default:
		zapLevel = zapcore.InfoLevel
	}

	// 配置基本的编码器设置
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "timestamp",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		FunctionKey:    zapcore.OmitKey,
		MessageKey:     "message",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.MillisDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// 控制台编码器配置 - 使用彩色级别
	consoleEncoderConfig := encoderConfig
	consoleEncoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder

	// JSON编码器配置 - 不使用彩色
	jsonEncoderConfig := encoderConfig
	jsonEncoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	// 创建不同类型的编码器
	jsonEncoder := zapcore.NewJSONEncoder(jsonEncoderConfig)
	consoleEncoder := zapcore.NewConsoleEncoder(consoleEncoderConfig)

	// 初始化cores切片，用于存储不同的输出目标
	var cores []zapcore.Core

	// 处理控制台输出
	if config.Console {
		stdoutSyncer := zapcore.AddSync(os.Stdout)
		cores = append(cores, zapcore.NewCore(consoleEncoder, stdoutSyncer, zapLevel))
	}

	// 处理文件输出
	for _, path := range config.OutputPaths {
		if path != "stdout" && path != "stderr" {
			// 配置日志切割
			lumberjackLogger := &lumberjack.Logger{
				Filename:   path,
				MaxSize:    config.MaxSize,
				MaxBackups: config.MaxBackups,
				MaxAge:     config.MaxAge,
				Compress:   config.Compress,
			}
			// 文件使用JSON格式
			cores = append(cores, zapcore.NewCore(jsonEncoder, zapcore.AddSync(lumberjackLogger), zapLevel))
		}
	}

	// 错误日志输出
	for _, path := range config.ErrorPaths {
		if path == "stderr" && config.Console {
			// 控制台错误输出使用console格式
			cores = append(cores, zapcore.NewCore(consoleEncoder, zapcore.AddSync(os.Stderr), zapcore.ErrorLevel))
		} else if path != "stdout" && path != "stderr" {
			// 文件错误输出使用JSON格式
			lumberjackLogger := &lumberjack.Logger{
				Filename:   path,
				MaxSize:    config.MaxSize,
				MaxBackups: config.MaxBackups,
				MaxAge:     config.MaxAge,
				Compress:   config.Compress,
			}
			cores = append(cores, zapcore.NewCore(jsonEncoder, zapcore.AddSync(lumberjackLogger), zapcore.ErrorLevel))
		}
	}

	// 合并所有core
	core := zapcore.NewTee(cores...)

	// 是否启用采样
	if config.EnableSampling {
		core = zapcore.NewSamplerWithOptions(
			core,
			time.Second,
			100,
			100,
		)
	}

	// 构建选项
	options := []zap.Option{zap.AddCaller(), zap.AddCallerSkip(1)}
	if config.Development {
		options = append(options, zap.Development())
	}

	// 返回日志器
	return zap.New(core, options...), nil
}

// NewGormLoggerFromConfig 为GORM创建日志记录器
func NewGormLoggerFromConfig(config *configs.LogConfig) (*zap.Logger, error) {
	// 解析日志级别
	var zapLevel zapcore.Level
	switch config.Level {
	case "debug":
		zapLevel = zapcore.DebugLevel
	case "info":
		zapLevel = zapcore.InfoLevel
	case "warn":
		zapLevel = zapcore.WarnLevel
	case "error":
		zapLevel = zapcore.ErrorLevel
	default:
		zapLevel = zapcore.InfoLevel
	}

	// 配置基本的编码器设置
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "timestamp",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		FunctionKey:    zapcore.OmitKey,
		MessageKey:     "message",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.MillisDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// 控制台编码器配置 - 使用彩色级别
	consoleEncoderConfig := encoderConfig
	consoleEncoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder

	// JSON编码器配置 - 不使用彩色
	jsonEncoderConfig := encoderConfig
	jsonEncoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	// 创建不同类型的编码器
	jsonEncoder := zapcore.NewJSONEncoder(jsonEncoderConfig)
	consoleEncoder := zapcore.NewConsoleEncoder(consoleEncoderConfig)

	// 初始化cores切片，用于存储不同的输出目标
	var cores []zapcore.Core

	// 处理控制台输出
	if config.Console && config.GormConsole {
		stdoutSyncer := zapcore.AddSync(os.Stdout)
		cores = append(cores, zapcore.NewCore(consoleEncoder, stdoutSyncer, zapLevel))
	}

	// 配置GORM专用日志文件
	gormLogFile := &lumberjack.Logger{
		Filename:   "./logs/gorm.log",
		MaxSize:    config.MaxSize,
		MaxBackups: config.MaxBackups,
		MaxAge:     config.MaxAge,
		Compress:   config.Compress,
	}
	// 文件使用JSON格式
	cores = append(cores, zapcore.NewCore(jsonEncoder, zapcore.AddSync(gormLogFile), zapLevel))

	// 合并所有core
	core := zapcore.NewTee(cores...)

	// 是否启用采样
	if config.EnableSampling {
		core = zapcore.NewSamplerWithOptions(
			core,
			time.Second,
			100,
			100,
		)
	}

	// 构建选项
	options := []zap.Option{zap.AddCaller(), zap.AddCallerSkip(1)}
	if config.Development {
		options = append(options, zap.Development())
	}

	// 返回日志器
	return zap.New(core, options...), nil
}
