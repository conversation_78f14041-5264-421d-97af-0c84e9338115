package zapLog

import (
	"path/filepath"
	"runtime"

	"go.uber.org/zap"
)

// LogHelper 提供简便的日志记录方法
type LogHelper struct {
	logger *zap.Logger
	isDev  bool // 是否为开发环境
}

// NewLogHelper 创建新的日志助手
func NewLogHelper(logger *zap.Logger) *LogHelper {
	return &LogHelper{
		logger: logger,
		isDev:  false,
	}
}

// NewDevLogHelper 创建开发环境下的日志助手
func NewDevLogHelper(logger *zap.Logger) *LogHelper {
	return &LogHelper{
		logger: logger,
		isDev:  true,
	}
}

// SetDevMode 设置是否为开发模式
func (h *LogHelper) SetDevMode(isDev bool) {
	h.isDev = isDev
}

// DebugLog 记录调试日志（仅在开发环境下）
func (h *LogHelper) DebugLog(message string, fields ...zap.Field) {
	if h.isDev {
		// 获取调用者信息
		_, file, line, _ := runtime.Caller(1)
		file = filepath.Base(file)

		allFields := append(fields,
			zap.String("file", file),
			zap.Int("line", line),
		)
		h.logger.Debug(message, allFields...)
	}
}

// APILog 记录API操作日志
func (h *LogHelper) APILog(message string, fields ...zap.Field) {
	h.logger.Info(message, fields...)
}

// APILogWithCustomer 记录客户API操作日志
func (h *LogHelper) APILogWithCustomer(customerID, action, path, method, sourceIP, resourceIP, requestBody string, responseCode int, responseMsg string, success bool) {
	h.logger.Info("客户API操作",
		zap.String("action", action),
		zap.String("source_ip", sourceIP),
		zap.String("customer_id", customerID),
		zap.String("path", path),
		zap.String("method", method),
		zap.String("resource_ip", resourceIP),
		zap.String("request_body", requestBody),
		zap.Int("response_code", responseCode),
		zap.String("response_msg", responseMsg),
		zap.Bool("success", success),
	)
}

// ErrorLog 记录错误日志
func (h *LogHelper) ErrorLog(message string, err error, fields ...zap.Field) {
	allFields := append(fields, zap.Error(err))

	// 在开发环境添加更多调用信息
	if h.isDev {
		_, file, line, _ := runtime.Caller(1)
		file = filepath.Base(file)

		allFields = append(allFields,
			zap.String("file", file),
			zap.Int("line", line),
		)
	}

	h.logger.Error(message, allFields...)
}

// SystemLog 记录系统操作日志
func (h *LogHelper) SystemLog(action, component, detail string, fields ...zap.Field) {
	allFields := append(fields,
		zap.String("action", action),
		zap.String("component", component),
		zap.String("detail", detail),
	)
	h.logger.Info("系统操作", allFields...)
}

// DevTrace 记录开发环境中的详细跟踪信息
func (h *LogHelper) DevTrace(event string, fields ...zap.Field) {
	if !h.isDev {
		return
	}

	// 获取调用者信息
	_, file, line, _ := runtime.Caller(1)
	file = filepath.Base(file)

	allFields := append(fields,
		zap.String("file", file),
		zap.Int("line", line),
	)
	h.logger.Debug("调试信息: "+event, allFields...)
}
