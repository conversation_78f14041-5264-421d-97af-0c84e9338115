package database

import (
	"backend/configs"
	zapLog "backend/internal/infrastructure/logger"
	"backend/internal/modules/cmdb/model/component"
	"backend/internal/modules/cmdb/model/inbound"
	locationModel "backend/internal/modules/cmdb/model/location"
	purchase "backend/internal/modules/purchase_old/model"
	schedule "backend/internal/modules/schedule/model"
	model2 "backend/internal/modules/server/model"
	software "backend/internal/modules/software_maintenance/model"
	"backend/internal/modules/user/model"
	"fmt"
	"os"
	"time"

	"github.com/spf13/viper"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	// 导入资产模型包
	assetModel "backend/internal/modules/cmdb/model/asset"
	workflowModel "backend/internal/modules/cmdb/model/workflow"

	productModel "backend/internal/modules/cmdb/model/product"
	templateModel "backend/internal/modules/cmdb/model/template"

	inventoryModel "backend/internal/modules/cmdb/model/inventory"
)

var DB *gorm.DB

func InitDB(config *configs.Config) *gorm.DB {
	// 优先从环境变量读取数据库参数，如果不存在则从配置文件读取
	host := getEnvOrConfig("APP_DB_HOST", "db.host", "127.0.0.1")
	port := getEnvOrConfig("APP_DB_PORT", "db.port", "3306")
	database := getEnvOrConfig("APP_DB_DATABASE", "db.database", "")
	username := getEnvOrConfig("APP_DB_USERNAME", "db.username", "")
	password := getEnvOrConfig("APP_DB_PASSWORD", "db.password", "")
	charset := getEnvOrConfig("APP_DB_CHARSET", "db.charset", "utf8mb4")

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=True&loc=Local",
		username, password, host, port, database, charset,
	)

	// 使用配置创建GORM日志记录器
	gormZapLogger, err := zapLog.NewGormLoggerFromConfig(&config.Logger)
	if err != nil {
		panic("failed to create gorm zap logger, err: " + err.Error())
	}

	// 设置GORM的日志级别，可以根据配置决定
	var logLevel logger.LogLevel
	switch config.Logger.Level {
	case "debug":
		logLevel = logger.Info // GORM没有debug级别，所以debug映射到Info
	case "info":
		logLevel = logger.Info
	case "warn":
		logLevel = logger.Warn
	case "error":
		logLevel = logger.Error
	default:
		logLevel = logger.Info
	}

	// 创建一个 gorm 的自定义日志记录器，设置慢查询阈值、日志级别等
	gormLogger := zapLog.NewZapLogger(gormZapLogger, logger.Config{
		SlowThreshold:             time.Second, // 这个也可以从配置中读取
		LogLevel:                  logLevel,    // 使用配置文件中的日志级别
		IgnoreRecordNotFoundError: true,
		Colorful:                  config.Logger.Encoding == "console", // 如果是控制台格式则启用颜色
	})

	// 使用自定义 Logger 初始化 gorm
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		panic("failed to connect database, err: " + err.Error())
	}

	// 删除表
	// err = db.Migrator().DropTable(
	////&inbound.PartInbound{},
	////&inbound.PartInboundTicket{},
	//)
	//if err != nil {
	//	panic("failed to dropTable, err: " + err.Error())
	//}
	// 自动迁移模型
	err = db.AutoMigrate(
		&model.User{},
		&model2.GpuServer{},
		&locationModel.Region{},
		&locationModel.AZ{},
		&locationModel.DataCenter{},
		&locationModel.Room{},
		&locationModel.Cabinet{},
		&assetModel.Device{},
		&assetModel.Resource{},
		&productModel.Product{},
		&component.ServerComponent{},
		&assetModel.StatusChangeLog{},
		&workflowModel.AssetWorkflow{},
		&templateModel.MachineTemplate{},
		&templateModel.TemplateComponent{},
		&assetModel.Warehouse{},
		&assetModel.AssetSpare{},
		&inventoryModel.InventoryDetail{},
		&inventoryModel.StockHistory{},
		&component.ComponentChangeLog{},
		&assetModel.NetworkDevice{},

		// 采购模块
		&purchase.PurchaseOrder{},
		&purchase.PurchaseDetails{},

		// 入库模块
		&inbound.InboundList{},

		// 排班表
		&schedule.HardSchedule{},
		&schedule.SoftSchedule{},

		&inbound.DismantledInboundDetails{},

		// 软件上线模块
		&software.SoftwareLaunchTicket{},
		&software.LaunchDevice{},
		&software.LaunchHistory{},
		&software.LaunchItem{},

		// 软件下线模块
		&software.SoftwareOfflineTicket{},
		&software.OfflineDevice{},
		&software.OfflineHistory{},
		&software.OfflineCheckItem{},
	)
	if err != nil {
		panic("failed to migrate db, err: " + err.Error())
	}
	DB = db
	return db
}

// 辅助函数：优先从环境变量获取值，如果不存在则从配置文件获取，最后使用默认值
func getEnvOrConfig(envKey, configKey, defaultValue string) string {
	// 优先检查环境变量
	if value := os.Getenv(envKey); value != "" {
		return value
	}

	// 其次检查配置文件
	if value := viper.GetString(configKey); value != "" {
		return value
	}

	// 最后使用默认值
	return defaultValue
}

func GetDB() *gorm.DB {
	return DB
}

// SetDB 更新全局数据库连接实例
// 这个函数用于在中间件中更新数据库上下文
func SetDB(db *gorm.DB) {
	DB = db
}
