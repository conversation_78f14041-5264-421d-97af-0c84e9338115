package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"time"

	"backend/configs"

	"github.com/redis/go-redis/v9"
)

// RedisClient Redis客户端
type RedisClient struct {
	client *redis.Client
	ttl    time.Duration
}

// NewRedisClient 创建Redis客户端
func NewRedisClient(cfg configs.RedisConfig) *RedisClient {
	// 从环境变量获取配置，如果存在则覆盖配置文件中的设置
	host := getEnvOrDefault("REDIS_HOST", cfg.Host)
	password := getEnvOrDefault("REDIS_PASSWORD", cfg.Password)

	// 获取端口
	port := cfg.Port
	if envPort := os.Getenv("REDIS_PORT"); envPort != "" {
		if p, err := strconv.Atoi(envPort); err == nil && p > 0 {
			port = p
		}
	}

	// 获取DB
	db := cfg.DB
	if envDB := os.Getenv("REDIS_DB"); envDB != "" {
		if d, err := strconv.Atoi(envDB); err == nil && d >= 0 {
			db = d
		}
	}

	// 获取TTL
	ttl := time.Duration(cfg.TTL) * time.Second
	if envTTL := os.Getenv("REDIS_TTL"); envTTL != "" {
		if t, err := strconv.Atoi(envTTL); err == nil && t > 0 {
			ttl = time.Duration(t) * time.Second
		}
	}

	// 如果TTL仍然小于等于0，则设置默认值
	if ttl <= 0 {
		ttl = time.Hour // 默认过期时间为1小时
	}

	client := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", host, port),
		Password: password,
		DB:       db,
	})

	return &RedisClient{
		client: client,
		ttl:    ttl,
	}
}

// 从环境变量获取值，如果环境变量不存在或为空，则返回默认值
func getEnvOrDefault(key string, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// Set 设置缓存
func (r *RedisClient) Set(ctx context.Context, key string, value interface{}, ttl ...time.Duration) error {
	expiration := r.ttl
	if len(ttl) > 0 && ttl[0] > 0 {
		expiration = ttl[0]
	}

	// 将复杂数据类型转换为JSON字符串
	jsonData, err := json.Marshal(value)
	if err != nil {
		return err
	}

	return r.client.Set(ctx, key, jsonData, expiration).Err()
}

// Get 获取缓存
func (r *RedisClient) Get(ctx context.Context, key string, dest interface{}) error {
	data, err := r.client.Get(ctx, key).Bytes()
	if err != nil {
		if err == redis.Nil {
			return fmt.Errorf("key %s not found", key)
		}
		return err
	}

	return json.Unmarshal(data, dest)
}

// Del 删除缓存
func (r *RedisClient) Del(ctx context.Context, keys ...string) error {
	return r.client.Del(ctx, keys...).Err()
}

// Exists 检查键是否存在
func (r *RedisClient) Exists(ctx context.Context, key string) (bool, error) {
	result, err := r.client.Exists(ctx, key).Result()
	if err != nil {
		return false, err
	}
	return result > 0, nil
}

// Close 关闭Redis连接
func (r *RedisClient) Close() error {
	return r.client.Close()
}

// DelByPattern 根据模式删除缓存
func (r *RedisClient) DelByPattern(ctx context.Context, pattern string) error {
	keys, err := r.client.Keys(ctx, pattern).Result()
	if err != nil {
		return err
	}

	if len(keys) > 0 {
		return r.client.Del(ctx, keys...).Err()
	}

	return nil
}

// SetWithExpiration 设置缓存并指定过期时间
func (r *RedisClient) SetWithExpiration(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	// 将复杂数据类型转换为JSON字符串
	jsonData, err := json.Marshal(value)
	if err != nil {
		return err
	}

	return r.client.Set(ctx, key, jsonData, expiration).Err()
}
