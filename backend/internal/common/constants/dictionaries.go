package constants

// 资产类型
const (
	AssetTypeSwitch       = "switch"        // 交换机
	AssetTypeRouter       = "router"        // 路由器
	AssetTypeLoadbalancer = "load_balancer" // 负载均衡器
	AssetTypeFirewall     = "firewall"      // 防火墙
	AssetTypeStorage      = "storage"       // 存储设备
	AssetTypeServer       = "server"        // 服务器
	AssetTypeGPUServer    = "gpu_server"    // GPU服务器
	AssetTypeOther        = "other"         // 其他设备
)

// 资产状态
const (
	AssetStatusPendingIn    = "pending_in"    // 待入库
	AssetStatusInStock      = "in_stock"      // 已入库
	AssetStatusPendingOut   = "pending_out"   // 待出库
	AssetStatusOutStock     = "out_stock"     // 已出库
	AssetStatusOnRack       = "on_rack"       // 已上架
	AssetStatusVerify       = "verify"        // 已验收
	AssetStatusIdle         = "idle"          // 闲置中
	AssetStatusInUse        = "in_use"        // 使用中
	AssetStatusRepairing    = "repairing"     // 维修中
	AssetStatusPendingScrap = "pending_scrap" // 待报废
	AssetStatusScrapped     = "scrapped"      // 已报废
	AssetStatusSoldOut      = "sold_out"      // 已售卖
)

// 硬件状态
const (
	HardwareStatusNormal  = "normal"  // 正常
	HardwareStatusFaulty  = "faulty"  // 故障
	HardwareStatusWarning = "warning" // 警告
)

const (
	BizStatusActive      = "active"
	BizStatusMaintaining = "maintaining"
	BizStatusOutage      = "outage"
)

// 来源类型
const (
	SourceTypeNewPurchase  = "new_purchase"  // 新购
	SourceTypeDismantled   = "dismantled"    // 拆机
	SourceTypeReturnRepair = "return_repair" // 返厂维修
	SourceTypeReturn       = "return"        // 退回
	SourceTypeOther        = "other"         // 其他
	SourceTypeAllocate     = "allocate"      // 调拨

	// 生命周期管理新增来源
	SourceTypeInbound  = "inbound"  // 入库
	SourceTypeOutbound = "outbound" // 出库
	SourceTypeVerify   = "verify"   // 验收
	SourceTypeOnRack   = "on_rack"  // 上架
	SourceTypeOnline   = "online"   // 上线
	SourceTypeOffline  = "offline"  // 下线
	SourceTypeOffRack  = "off_rack" // 下架
	SourceTypeOps      = "ops"      // 运维
)

const (
	ChangeTypeAdjust   = "adjust"
	ChangeTypeInbound  = "inbound"
	ChangeTypeOutbound = "outbound"
	ChangeTypeSync     = "sync"     // 系统同步
	ChangeTypeAllocate = "allocate" // 分配
	ChangeTypeRelease  = "release"  // 释放
)

// 变更原因（库存历史）
const (
	ChangeReasonNewPurchase  = "new_purchase"  // 新购
	ChangeReasonDismantled   = "dismantled"    // 拆机
	ChangeReasonRepair       = "repair"        // 维修
	ChangeReasonReturnRepair = "return_repair" // 返厂维修
	ChangeReasonSell         = "sell"          // 售卖
	ChangeReasonReturned     = "return"        // 退回
	ChangeReasonOther        = "other"         // 其他
	ChangeReasonRenew        = "renew"         // 换新
	ChangeReasonReplace      = "replacement"   // 改配
	ChangeReasonAllocate     = "allocate"      // 调拨
)
