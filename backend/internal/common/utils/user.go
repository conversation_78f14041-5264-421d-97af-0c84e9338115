package utils

import (
	"context"
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// GetUserID 从上下文中获取用户ID（支持Gin上下文和标准上下文）
func GetUserID(ctx interface{}) (uint, error) {
	// 尝试从Gin上下文获取
	if ginCtx, ok := ctx.(*gin.Context); ok {
		if userID, exists := ginCtx.Get("userID"); exists {
			if id, ok := userID.(uint); ok {
				return id, nil
			}
		}
		return 0, fmt.Errorf("Gin上下文中不存在用户ID")
	}

	// 尝试从标准上下文获取
	if stdCtx, ok := ctx.(context.Context); ok {
		userID, exists := stdCtx.Value("userID").(uint)
		if !exists {
			return 0, fmt.Errorf("标准上下文中不存在用户ID")
		}
		return userID, nil
	}

	return 0, fmt.Errorf("不支持的上下文类型")
}

// GetUserIDWithDefault 从上下文中获取用户ID，如果不存在则返回默认值
func GetUserIDWithDefault(ctx interface{}, defaultID uint) uint {
	userID, err := GetUserID(ctx)
	if err != nil {
		return defaultID
	}
	return userID
}

// GetUserName 从上下文中获取用户姓名（支持Gin上下文和标准上下文）
func GetUserName(ctx interface{}) (string, error) {
	var userInfo map[string]interface{}
	var exists bool

	// 尝试从Gin上下文获取
	if ginCtx, ok := ctx.(*gin.Context); ok {
		if info, ginExists := ginCtx.Get("userInfo"); ginExists {
			userInfo, exists = info.(map[string]interface{})
		}
	} else if stdCtx, ok := ctx.(context.Context); ok {
		// 尝试从标准上下文获取
		userInfo, exists = stdCtx.Value("userInfo").(map[string]interface{})
	}

	if !exists {
		return "", fmt.Errorf("上下文中不存在用户信息")
	}

	realName, ok := userInfo["realName"].(string)
	if !ok {
		return "", fmt.Errorf("用户信息中不存在真实姓名")
	}

	return realName, nil
}

// GetUserNameWithDefault 从上下文中获取用户姓名，如果不存在则返回默认值
func GetUserNameWithDefault(ctx interface{}, defaultName string) string {
	userName, err := GetUserName(ctx)
	if err != nil {
		return defaultName
	}
	return userName
}

// GetUserDepartment 从上下文中获取用户部门（支持Gin上下文和标准上下文）
func GetUserDepartment(ctx interface{}) (string, error) {
	var userInfo map[string]interface{}
	var exists bool

	// 尝试从Gin上下文获取
	if ginCtx, ok := ctx.(*gin.Context); ok {
		if info, ginExists := ginCtx.Get("userInfo"); ginExists {
			userInfo, exists = info.(map[string]interface{})
		}
	} else if stdCtx, ok := ctx.(context.Context); ok {
		// 尝试从标准上下文获取
		userInfo, exists = stdCtx.Value("userInfo").(map[string]interface{})
	}

	if !exists {
		return "", fmt.Errorf("上下文中不存在用户信息")
	}

	department, ok := userInfo["department"].(string)
	if !ok {
		return "", fmt.Errorf("用户信息中不存在部门信息")
	}

	return department, nil
}

// GetUserDepartmentWithDefault 从上下文中获取用户部门，如果不存在则返回默认值
func GetUserDepartmentWithDefault(ctx interface{}, defaultDepartment string) string {
	department, err := GetUserDepartment(ctx)
	if err != nil {
		return defaultDepartment
	}
	return department
}

// GetUserRoles 从上下文中获取用户角色列表（支持Gin上下文和标准上下文）
func GetUserRoles(ctx interface{}) ([]string, error) {
	// 尝试从Gin上下文获取userRoles（JWT中间件设置的）
	if ginCtx, ok := ctx.(*gin.Context); ok {
		if roles, exists := ginCtx.Get("userRoles"); exists {
			if rolesSlice, ok := roles.([]string); ok {
				return rolesSlice, nil
			}
		}
		// 如果userRoles不存在，尝试从userInfo获取
		if info, exists := ginCtx.Get("userInfo"); exists {
			if userInfo, ok := info.(map[string]interface{}); ok {
				return extractRolesFromUserInfo(userInfo)
			}
		}
		return nil, fmt.Errorf("Gin上下文中不存在角色信息")
	}

	// 尝试从标准上下文获取
	if stdCtx, ok := ctx.(context.Context); ok {
		userInfo, exists := stdCtx.Value("userInfo").(map[string]interface{})
		if !exists {
			return nil, fmt.Errorf("标准上下文中不存在用户信息")
		}
		return extractRolesFromUserInfo(userInfo)
	}

	return nil, fmt.Errorf("不支持的上下文类型")
}

// extractRolesFromUserInfo 从用户信息中提取角色列表
func extractRolesFromUserInfo(userInfo map[string]interface{}) ([]string, error) {
	rolesInterface, ok := userInfo["roles"]
	if !ok {
		return nil, fmt.Errorf("用户信息中不存在角色信息")
	}

	rolesSlice, ok := rolesInterface.([]interface{})
	if !ok {
		return nil, fmt.Errorf("角色信息格式不正确")
	}

	roles := make([]string, 0, len(rolesSlice))
	for _, roleInterface := range rolesSlice {
		if role, ok := roleInterface.(string); ok {
			roles = append(roles, role)
		}
	}

	return roles, nil
}

// HasRole 检查用户是否拥有指定角色
func HasRole(ctx interface{}, role string) bool {
	roles, err := GetUserRoles(ctx)
	if err != nil {
		return false
	}

	for _, r := range roles {
		if r == role {
			return true
		}
	}

	return false
}

// GetUserRealName 获取用户真实姓名 - 通用工具函数
func GetUserRealName(db *gorm.DB, userID uint) string {
	if userID == 0 {
		return "系统"
	}

	// 查询用户表获取真实姓名
	type User struct {
		ID       uint   `gorm:"column:id"`
		RealName string `gorm:"column:real_name"`
		Username string `gorm:"column:username"`
	}

	var user User
	if err := db.Table("users").Select("id, real_name, username").Where("id = ?", userID).First(&user).Error; err != nil {
		// 如果查询失败，返回默认值
		return fmt.Sprintf("用户_%d", userID)
	}

	// 优先返回真实姓名，如果为空则返回用户名
	if user.RealName != "" {
		return user.RealName
	}
	if user.Username != "" {
		return user.Username
	}
	return fmt.Sprintf("用户_%d", userID)
}
