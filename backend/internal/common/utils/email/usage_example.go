package email

// 使用示例：如何与现有系统集成
// 将此文件作为参考，展示如何在业务代码中使用邮件工具

/*

// 1. 在服务初始化时设置邮件工具
func InitializeServices(config *configs.Config, logger *zap.Logger) {
    // 初始化邮件工具
    InitEmailUtils(config, logger)

    // 其他服务初始化...
}

// 2. 在业务服务中使用
package service

import (
    "context"
    "backend/internal/common/utils/email"
    "go.uber.org/zap"
)

type UserService struct {
    db     *gorm.DB
    logger *zap.Logger
}

func (s *UserService) RegisterUser(ctx context.Context, user *User) error {
    // 保存用户到数据库
    if err := s.db.Create(user).Error; err != nil {
        return err
    }

    // 发送欢迎邮件（非关键操作，失败不影响注册流程）
    if err := email.SendWelcome(ctx, []string{user.Email}, user.Name); err != nil {
        s.logger.Error("发送欢迎邮件失败", zap.Error(err))
    }

    return nil
}

// 3. 在工单系统中使用
func (s *TicketService) CreateTicket(ctx context.Context, ticket *Ticket) error {
    // 创建工单
    if err := s.db.Create(ticket).Error; err != nil {
        return err
    }

    // 发送通知邮件
    details := map[string]string{
        "工单编号": ticket.Number,
        "工单类型": ticket.Type,
        "创建人":  ticket.Creator,
    }

    if err := email.SendNotification(ctx,
        []string{ticket.AssigneeEmail},
        "新工单创建通知",
        "您有一个新的工单需要处理",
        details); err != nil {
        s.logger.Error("发送通知邮件失败", zap.Error(err))
    }

    return nil
}

// 4. 在系统监控中使用
func (s *MonitorService) HandleAlert(ctx context.Context, alert *Alert) error {
    details := map[string]string{
        "告警级别": alert.Level,
        "影响服务": alert.Service,
        "错误信息": alert.Message,
    }

    if err := email.SendAlert(ctx,
        []string{"<EMAIL>", "<EMAIL>"},
        "系统告警：" + alert.Title,
        alert.Description,
        details); err != nil {
        s.logger.Error("发送告警邮件失败", zap.Error(err))
    }

    return nil
}

// 5. 使用自定义模板
func (s *CustomService) SendCustomEmail(ctx context.Context, user *User) error {
    emailUtils := email.GetEmailUtils()
    if emailUtils == nil {
        return nil
    }

    customTemplate := `
    <!DOCTYPE html>
    <html>
    <head><title>{{.Subject}}</title></head>
    <body>
        <h1>Hello {{.Name}}!</h1>
        <p>您的账户余额：{{.Balance}}元</p>
    </body>
    </html>`

    data := email.TemplateData{
        "Subject": "账户余额提醒",
        "Name":    user.Name,
        "Balance": user.Balance,
    }

    req := &email.EmailRequest{
        To:      []string{user.Email},
        Subject: "账户余额提醒",
    }

    return emailUtils.SendEmailWithTemplate(ctx, req, customTemplate, data)
}

*/
