package notifier

import (
	"backend/configs"
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"
)

type HardScheNotifier struct {
	HardWebhookURL          string
	HardSecret              string
	HardDetailUrlTemplate   string
	HardMorningScheTemplate string
	HardEveningScheTemplate string
	HardScheEnabled         bool
}

// 初始化硬件排班飞书通知
func NewCustomHardScheNotifier(feishuConfig *configs.FeishuConfig) *HardScheNotifier {
	if feishuConfig == nil {
		fmt.Println("【ERROR】初始化软件人员排班飞书通知失败，缺少飞书配置")
		return &HardScheNotifier{}
	}
	return &HardScheNotifier{
		HardWebhookURL:          feishuConfig.HardScheWebhookURL,
		HardSecret:              feishuConfig.HardScheSecret,
		HardDetailUrlTemplate:   feishuConfig.HardScheDetailUrlTemplate,
		HardMorningScheTemplate: feishuConfig.HardScheMorningTemplate,
		HardEveningScheTemplate: feishuConfig.HardScheEveningTemplate,
		HardScheEnabled:         feishuConfig.HardScheEnabled,
	}
}

// 生成签名
func (h *HardScheNotifier) generateSign(secret string) (string, string) {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	stringToSign := fmt.Sprintf("%s\n%s", timestamp, secret)

	hmac := hmac.New(sha256.New, []byte(stringToSign))

	signBytes := hmac.Sum(nil)
	sign := base64.StdEncoding.EncodeToString(signBytes)
	return timestamp, sign
}

// sendHardScheMorningNotifier 发送硬件排班早上信息统一接口
func (h *HardScheNotifier) sendHardScheMorningNotifier(templateID, templateVersionName string, templateVariable map[string]interface{}, bot HardScheNotifier) {
	// 构建消息
	msg := map[string]interface{}{
		"msg_type": "interactive",
		"card": map[string]interface{}{
			"type": "template",
			"data": map[string]interface{}{
				"template_id":           templateID,
				"template_version_name": templateVersionName,
				"template_variable":     templateVariable,
			},
		},
	}
	// 添加签名
	timestamp, sign := h.generateSign(bot.HardSecret)
	msg["timestamp"] = timestamp
	msg["sign"] = sign

	// 序列化为JSON
	jsonData, err := json.Marshal(msg)
	if err != nil {
		fmt.Printf("飞书通知JSON序列化失败: %v\n", err)
	}
	// 发送HTTP请求
	resp, err := http.Post(bot.HardWebhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("发送飞书通知失败 -  error: %v\n", err)
	}

	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			fmt.Printf("关闭飞书通知请求体失败 -  error: %v\n", err)
		}
	}(resp.Body)
}

// SendHardScheMorningNotification 发送硬件排班早上通知
func (h *HardScheNotifier) SendHardScheMorningNotification(date string, users []string) error {
	// TemplateVariable 定义模板变量结构
	type TemplateVariable map[string]interface{}
	bot := HardScheNotifier{
		HardWebhookURL:          h.HardWebhookURL,
		HardSecret:              h.HardSecret,
		HardDetailUrlTemplate:   h.HardDetailUrlTemplate,
		HardScheEnabled:         h.HardScheEnabled,
		HardMorningScheTemplate: h.HardMorningScheTemplate,
	}
	usersStr := strings.Join(users, ",")
	tem := TemplateVariable{
		"date":         date,
		"primary_name": usersStr,
	}
	h.sendHardScheMorningNotifier(bot.HardMorningScheTemplate, "1.0.7", tem, bot)
	return nil
}

// sendHardScheEveningNotifier 发送硬件排班晚上信息统一接口
func (h *HardScheNotifier) sendHardScheEveningNotifier(templateID, templateVersionName string, templateVariable map[string]interface{}, bot HardScheNotifier) {
	// 构建消息
	msg := map[string]interface{}{
		"msg_type": "interactive",
		"card": map[string]interface{}{
			"type": "template",
			"data": map[string]interface{}{
				"template_id":           templateID,
				"template_version_name": templateVersionName,
				"template_variable":     templateVariable,
			},
		},
	}
	// 添加签名
	timestamp, sign := h.generateSign(bot.HardSecret)
	msg["timestamp"] = timestamp
	msg["sign"] = sign

	// 序列化为JSON
	jsonData, err := json.Marshal(msg)
	if err != nil {
		fmt.Printf("飞书通知JSON序列化失败: %v\n", err)
	}
	// 发送HTTP请求
	resp, err := http.Post(bot.HardWebhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("发送飞书通知失败 -  error: %v\n", err)
	}

	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			fmt.Printf("关闭飞书通知请求体失败 -  error: %v\n", err)
		}
	}(resp.Body)
}

// SendHardScheEveningNotification 发送硬件排班晚上通知
func (h *HardScheNotifier) SendHardScheEveningNotification(date string, users []string) error {
	// TemplateVariable 定义模板变量结构
	type TemplateVariable map[string]interface{}
	bot := HardScheNotifier{
		HardWebhookURL:          h.HardWebhookURL,
		HardSecret:              h.HardSecret,
		HardDetailUrlTemplate:   h.HardDetailUrlTemplate,
		HardScheEnabled:         h.HardScheEnabled,
		HardEveningScheTemplate: h.HardEveningScheTemplate,
	}
	usersStr := strings.Join(users, ",")
	tem := TemplateVariable{
		"date":         date,
		"primary_name": usersStr,
	}
	h.sendHardScheEveningNotifier(bot.HardEveningScheTemplate, "1.0.8", tem, bot)
	return nil
}
