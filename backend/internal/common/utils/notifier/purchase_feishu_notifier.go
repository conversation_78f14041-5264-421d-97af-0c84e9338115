package notifier

import (
	"backend/configs"
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// PurchaseFeishuNotifier 采购流程专用飞书通知器
type PurchaseFeishuNotifier struct {
	*FeishuNotifier
	PurchaseWebhookURL string
	PurchaseSecret     string
	UrlTemplates       configs.PurchaseUrlTemplates // 使用结构化URL模板
	PurchaseEnabled    bool
	DB                 *gorm.DB // 添加数据库连接
}

// NewPurchaseFeishuNotifier 创建采购流程专用飞书通知器
func NewPurchaseFeishuNotifier(notifier *FeishuNotifier, purchaseWebhookURL, purchaseSecret string, urlTemplates configs.PurchaseUrlTemplates, purchaseEnabled bool) *PurchaseFeishuNotifier {
	return &PurchaseFeishuNotifier{
		FeishuNotifier:     notifier,
		PurchaseWebhookURL: purchaseWebhookURL,
		PurchaseSecret:     purchaseSecret,
		UrlTemplates:       urlTemplates,
		PurchaseEnabled:    purchaseEnabled,
	}
}

// GetUrlTemplate 根据模块类型获取对应的URL模板
func (p *PurchaseFeishuNotifier) GetUrlTemplate(moduleType string) string {
	switch moduleType {
	case "request":
		if p.UrlTemplates.Request != "" {
			return p.UrlTemplates.Request
		}
	case "inquiry":
		if p.UrlTemplates.Inquiry != "" {
			return p.UrlTemplates.Inquiry
		}
	case "contract":
		if p.UrlTemplates.Contract != "" {
			return p.UrlTemplates.Contract
		}
	case "payment":
		if p.UrlTemplates.Payment != "" {
			return p.UrlTemplates.Payment
		}
	case "arrival":
		if p.UrlTemplates.Arrival != "" {
			return p.UrlTemplates.Arrival
		}
	case "shipment":
		if p.UrlTemplates.Shipment != "" {
			return p.UrlTemplates.Shipment
		}
	case "invoice":
		if p.UrlTemplates.Invoice != "" {
			return p.UrlTemplates.Invoice
		}
	}

	// 回退到默认URL
	if p.UrlTemplates.Default != "" {
		return p.UrlTemplates.Default
	}

	// 最后回退到基础URL
	return p.DetailUrlTemplate
}

// SetDB 设置数据库连接
func (p *PurchaseFeishuNotifier) SetDB(db *gorm.DB) {
	p.DB = db
}

// ApprovalFlowType 表示审批流程类型
type ApprovalFlowType string

const (
	FlowTypePurchaseRequest ApprovalFlowType = "purchase_request" // 采购申请流程
	FlowTypePurchaseInquiry ApprovalFlowType = "purchase_inquiry" // 采购询价流程
	FlowTypeContract        ApprovalFlowType = "contract"         // 合同审批流程
	FlowTypePayment         ApprovalFlowType = "payment"          // 付款审批流程
	FlowTypeArrival         ApprovalFlowType = "arrival"          // 到货管理流程
	FlowTypeShipment        ApprovalFlowType = "shipment"         // 发货管理流程
	FlowTypeInvoice         ApprovalFlowType = "invoice"          // 发票管理流程
)

// NotifyEventType 表示通知事件类型
type NotifyEventType string

const (
	EventTypeSubmitted   NotifyEventType = "submitted"    // 提交审批
	EventTypeStageChange NotifyEventType = "stage_change" // 阶段变更
	EventTypeApproved    NotifyEventType = "approved"     // 审批通过
	EventTypeRejected    NotifyEventType = "rejected"     // 审批拒绝
	EventTypeRollback    NotifyEventType = "rollback"     // 流程回退
	EventTypeCompleted   NotifyEventType = "completed"    // 流程完成
	EventTypeCreated     NotifyEventType = "created"      // 创建通知
)

// GeneralApprovalNotifyParams 通用审批通知参数
type GeneralApprovalNotifyParams struct {
	FlowType       ApprovalFlowType         // 流程类型
	EventType      NotifyEventType          // 事件类型
	BusinessID     uint                     // 业务ID
	BusinessNo     string                   // 业务编号
	RequesterName  string                   // 申请人姓名
	ApproverName   string                   // 审批人姓名(可选)
	BusinessType   string                   // 业务类型(如"设备采购"、"服务采购"等)
	ProjectName    string                   // 项目名称
	Comments       string                   // 审批意见/拒绝理由
	FromStage      string                   // 回退前阶段(回退时使用)
	ToStage        string                   // 目标阶段/回退到阶段
	CustomFields   map[string]string        // 自定义字段，用于扩展
	CustomElements []map[string]interface{} // 自定义元素，用于扩展卡片内容
}

// SendGeneralApprovalNotification 发送通用审批通知
func (p *PurchaseFeishuNotifier) SendGeneralApprovalNotification(params GeneralApprovalNotifyParams) error {
	// 构建基础消息
	msg := p.buildGeneralApprovalCard(params)

	// 记录日志
	p.Logger.Info("发送通用审批通知",
		zap.String("flowType", string(params.FlowType)),
		zap.String("eventType", string(params.EventType)),
		zap.Uint("businessID", params.BusinessID),
		zap.String("businessNo", params.BusinessNo))

	return p.sendFeishuMessage(msg)
}

// buildGeneralApprovalCard 构建通用审批卡片
func (p *PurchaseFeishuNotifier) buildGeneralApprovalCard(params GeneralApprovalNotifyParams) map[string]interface{} {
	// 根据流程类型和事件类型确定标题和内容
	header := p.getCardHeader(params)
	content := p.getCardContent(params)

	// 构建基础卡片
	elements := []map[string]interface{}{
		{
			"tag": "div",
			"text": map[string]interface{}{
				"content": content,
				"tag":     "lark_md",
			},
		},
	}

	// 添加基本信息字段
	elements = append(elements, p.buildBaseInfoFields(params)...)

	// 添加审批意见(如果有)
	if params.Comments != "" {
		elements = append(elements, p.buildCommentsElement(params.EventType, params.Comments))
	}

	// 添加阶段信息(如果是回退事件)
	if params.EventType == EventTypeRollback && params.FromStage != "" && params.ToStage != "" {
		elements = append(elements, p.buildStageChangeElement(params.FromStage, params.ToStage))
	}

	// 添加自定义元素(如果有)
	if len(params.CustomElements) > 0 {
		elements = append(elements, params.CustomElements...)
	}

	// 添加查看详情按钮
	elements = append(elements, p.buildViewDetailButton(params.FlowType, params.BusinessID))

	// 构建完整消息
	msg := map[string]interface{}{
		"msg_type": "interactive",
		"card": map[string]interface{}{
			"config": map[string]interface{}{
				"wide_screen_mode": true,
				"enable_forward":   true,
			},
			"header":   header,
			"elements": elements,
		},
	}

	return msg
}

// getCardHeader 根据流程类型和事件类型获取卡片标题
func (p *PurchaseFeishuNotifier) getCardHeader(params GeneralApprovalNotifyParams) map[string]interface{} {
	flowType := params.FlowType
	eventType := params.EventType
	var template, title string

	switch eventType {
	case EventTypeSubmitted:
		template = "blue"
		if params.ToStage != "" {
			title = "📋 " + p.getFlowTypePrefix(flowType) + "【待" + params.ToStage + "审批】"
		} else {
			title = "📋 " + p.getFlowTypePrefix(flowType) + "【待审批】"
		}
	case EventTypeStageChange:
		template = "blue"
		if params.ToStage != "" {
			title = "📋 " + p.getFlowTypePrefix(flowType) + "【待" + params.ToStage + "审批】"
		} else {
			title = "📋 " + p.getFlowTypePrefix(flowType) + "【待审批】"
		}
	case EventTypeApproved:
		template = "green"
		title = "✅ " + p.getFlowTypePrefix(flowType) + "【已通过】"
	case EventTypeRejected:
		template = "red"
		title = "❌ " + p.getFlowTypePrefix(flowType) + "【已拒绝】"
	case EventTypeRollback:
		template = "yellow"
		title = "⏮️ " + p.getFlowTypePrefix(flowType) + "【已回退】"
	default:
		template = "blue"
		title = p.getFlowTypePrefix(flowType) + "流程通知"
	}

	return map[string]interface{}{
		"template": template,
		"title": map[string]interface{}{
			"content": title,
			"tag":     "plain_text",
		},
	}
}

// getCardContent 根据流程类型和事件类型获取卡片内容
func (p *PurchaseFeishuNotifier) getCardContent(params GeneralApprovalNotifyParams) string {
	prefix := p.getFlowTypePrefix(params.FlowType)
	bizNo := ""
	if params.BusinessNo != "" {
		bizNo = " (" + params.BusinessNo + ")"
	}

	switch params.EventType {
	case EventTypeSubmitted:
		if params.ToStage != "" {
			return fmt.Sprintf("**%s%s 等待 %s 审批**",
				prefix, bizNo, params.ToStage)
		}
		return fmt.Sprintf("**📝 %s%s 已创建，等待审批**", prefix, bizNo)
	case EventTypeStageChange:
		if params.ToStage != "" {
			return fmt.Sprintf("**%s%s 已进入 %s 阶段**",
				prefix, bizNo, params.ToStage)
		}
		return fmt.Sprintf("**🔄 %s%s 状态已更新，需要审批**", prefix, bizNo)
	case EventTypeApproved:
		return fmt.Sprintf("**✅ %s%s 已审批通过**\n\n所有审批流程已完成", prefix, bizNo)
	case EventTypeRejected:
		return fmt.Sprintf("**❌ %s%s 审批未通过**\n\n流程已终止", prefix, bizNo)
	case EventTypeRollback:
		if params.FromStage != "" && params.ToStage != "" {
			return fmt.Sprintf("**⏮️ %s%s 已回退**\n\n从 **%s** 阶段回退至 **%s** 阶段",
				prefix, bizNo, params.FromStage, params.ToStage)
		}
		return fmt.Sprintf("**⏮️ %s%s 已被回退，需重新审批**", prefix, bizNo)
	default:
		return fmt.Sprintf("**%s%s 流程通知**", prefix, bizNo)
	}
}

// getFlowTypePrefix 根据流程类型获取前缀
func (p *PurchaseFeishuNotifier) getFlowTypePrefix(flowType ApprovalFlowType) string {
	switch flowType {
	case FlowTypePurchaseRequest:
		return "采购申请"
	case FlowTypePurchaseInquiry:
		return "采购询价"
	case FlowTypeContract:
		return "合同审批"
	case FlowTypePayment:
		return "付款申请"
	case FlowTypeArrival:
		return "到货管理"
	case FlowTypeShipment:
		return "发货管理"
	default:
		return "审批流程"
	}
}

// buildBaseInfoFields 构建基本信息字段
func (p *PurchaseFeishuNotifier) buildBaseInfoFields(params GeneralApprovalNotifyParams) []map[string]interface{} {
	elements := []map[string]interface{}{}

	// 创建分隔线
	elements = append(elements, map[string]interface{}{
		"tag": "hr",
	})

	// 创建两列布局的字段
	fields := []map[string]interface{}{}

	// 添加业务编号 (左列)
	if params.BusinessNo != "" {
		fields = append(fields, map[string]interface{}{
			"is_short": true,
			"text": map[string]interface{}{
				"content": fmt.Sprintf("**📝 单号**\n%s", params.BusinessNo),
				"tag":     "lark_md",
			},
		})
	}

	// 添加申请人 (右列)
	if params.RequesterName != "" {
		fields = append(fields, map[string]interface{}{
			"is_short": true,
			"text": map[string]interface{}{
				"content": fmt.Sprintf("**👤 申请人**\n%s", params.RequesterName),
				"tag":     "lark_md",
			},
		})
	}

	// 添加项目名称 (左列)
	if params.ProjectName != "" && params.ProjectName != "无" {
		fields = append(fields, map[string]interface{}{
			"is_short": true,
			"text": map[string]interface{}{
				"content": fmt.Sprintf("**📂 项目**\n%s", params.ProjectName),
				"tag":     "lark_md",
			},
		})
	}

	// 添加业务类型 (右列)
	if params.BusinessType != "" {
		fields = append(fields, map[string]interface{}{
			"is_short": true,
			"text": map[string]interface{}{
				"content": fmt.Sprintf("**🏷️ 类型**\n%s", params.BusinessType),
				"tag":     "lark_md",
			},
		})
	}

	// 只有当有字段时才添加
	if len(fields) > 0 {
		elements = append(elements, map[string]interface{}{
			"tag":    "div",
			"fields": fields,
		})

		// 添加分隔线
		elements = append(elements, map[string]interface{}{
			"tag": "hr",
		})
	}

	return elements
}

// buildCommentsElement 构建审批意见元素
func (p *PurchaseFeishuNotifier) buildCommentsElement(eventType NotifyEventType, comments string) map[string]interface{} {
	var label string
	var icon string

	switch eventType {
	case EventTypeApproved:
		label = "审批意见"
		icon = "✅"
	case EventTypeRejected:
		label = "拒绝理由"
		icon = "❌"
	case EventTypeRollback:
		label = "回退原因"
		icon = "⏮️"
	default:
		label = "备注"
		icon = "📝"
	}

	// 格式化评论内容，限制长度
	formattedComment := comments
	if len(formattedComment) > 500 {
		formattedComment = formattedComment[:497] + "..."
	}

	// 构建评论区块
	return map[string]interface{}{
		"tag": "div",
		"text": map[string]interface{}{
			"content": fmt.Sprintf("**%s %s**\n%s", icon, label, formattedComment),
			"tag":     "lark_md",
		},
	}
}

// buildStageChangeElement 构建阶段变更元素
func (p *PurchaseFeishuNotifier) buildStageChangeElement(fromStage, toStage string) map[string]interface{} {
	// 构建两列布局
	return map[string]interface{}{
		"tag": "div",
		"fields": []map[string]interface{}{
			{
				"is_short": true,
				"text": map[string]interface{}{
					"content": fmt.Sprintf("**⬆️ 回退前**\n%s", fromStage),
					"tag":     "lark_md",
				},
			},
			{
				"is_short": true,
				"text": map[string]interface{}{
					"content": fmt.Sprintf("**⬇️ 回退后**\n%s", toStage),
					"tag":     "lark_md",
				},
			},
		},
	}
}

// buildViewDetailButton 构建查看详情按钮
func (p *PurchaseFeishuNotifier) buildViewDetailButton(flowType ApprovalFlowType, businessID uint) map[string]interface{} {
	var url string

	switch flowType {
	case FlowTypePurchaseRequest:
		url = fmt.Sprintf("%s%s", p.GetUrlTemplate("request"), strconv.FormatUint(uint64(businessID), 10))
	case FlowTypePurchaseInquiry:
		url = fmt.Sprintf("%s%s", p.GetUrlTemplate("inquiry"), strconv.FormatUint(uint64(businessID), 10))
	case FlowTypeContract:
		url = fmt.Sprintf("%s%s", p.GetUrlTemplate("contract"), strconv.FormatUint(uint64(businessID), 10))
	case FlowTypePayment:
		url = fmt.Sprintf("%s%s", p.GetUrlTemplate("payment"), strconv.FormatUint(uint64(businessID), 10))
	default:
		url = fmt.Sprintf("%s%s", p.GetUrlTemplate("default"), strconv.FormatUint(uint64(businessID), 10))
	}

	// 构建操作按钮组 - 只保留查看详情按钮
	actions := []map[string]interface{}{
		{
			"tag": "button",
			"text": map[string]interface{}{
				"content": "查看详情",
				"tag":     "plain_text",
			},
			"type":  "primary",
			"url":   url,
			"value": map[string]interface{}{},
		},
	}

	return map[string]interface{}{
		"tag":     "action",
		"actions": actions,
	}
}

// SendPurchaseRequestSubmittedNotification 发送采购申请提交通知 (兼容旧接口)
func (p *PurchaseFeishuNotifier) SendPurchaseRequestSubmittedNotification(requestID uint, requestNo string, requesterName string, purchaseType string, projectName string) error {
	params := GeneralApprovalNotifyParams{
		FlowType:      FlowTypePurchaseRequest,
		EventType:     EventTypeSubmitted,
		BusinessID:    requestID,
		BusinessNo:    requestNo,
		RequesterName: requesterName,
		BusinessType:  purchaseType,
		ProjectName:   projectName,
	}

	return p.SendGeneralApprovalNotification(params)
}

// SendPurchaseStageChangedNotification 发送采购申请阶段变更通知 (兼容旧接口)
func (p *PurchaseFeishuNotifier) SendPurchaseStageChangedNotification(requestID uint, requestNo string, stageName string, requesterName string, projectName string) error {
	params := GeneralApprovalNotifyParams{
		FlowType:      FlowTypePurchaseRequest,
		EventType:     EventTypeStageChange,
		BusinessID:    requestID,
		BusinessNo:    requestNo,
		RequesterName: requesterName,
		ToStage:       stageName,
		ProjectName:   projectName,
	}

	return p.SendGeneralApprovalNotification(params)
}

// SendPurchaseApprovedNotification 发送采购申请审批通过通知 (兼容旧接口)
func (p *PurchaseFeishuNotifier) SendPurchaseApprovedNotification(requestID uint, requestNo string, requesterName string, comments string, projectName string) error {
	// 记录日志，追踪实际参数
	p.Logger.Info("发送采购申请审批通过通知",
		zap.Uint("requestID", requestID),
		zap.String("requestNo", requestNo),
		zap.String("requesterName", requesterName),
		zap.String("comments", comments),
		zap.String("projectName", projectName))

	// 获取所有审批历史的意见
	allComments := p.getAllApprovalComments(requestID)
	if allComments == "" {
		allComments = comments
		p.Logger.Info("未找到历史审批意见，使用当前意见", zap.String("comments", comments))
	} else if comments != "" && !strings.Contains(allComments, comments) {
		allComments = allComments + comments
		p.Logger.Info("合并历史审批意见和当前意见",
			zap.String("allComments", allComments),
			zap.String("currentComments", comments))
	}

	params := GeneralApprovalNotifyParams{
		FlowType:      FlowTypePurchaseRequest,
		EventType:     EventTypeApproved,
		BusinessID:    requestID,
		BusinessNo:    requestNo,
		RequesterName: requesterName,
		ProjectName:   projectName,
		Comments:      allComments,
	}

	return p.SendGeneralApprovalNotification(params)
}

// SendPurchaseRejectedNotification 发送采购申请拒绝通知 (兼容旧接口)
func (p *PurchaseFeishuNotifier) SendPurchaseRejectedNotification(requestID uint, requestNo string, requesterName string, comments string, projectName string) error {
	params := GeneralApprovalNotifyParams{
		FlowType:      FlowTypePurchaseRequest,
		EventType:     EventTypeRejected,
		BusinessID:    requestID,
		BusinessNo:    requestNo,
		RequesterName: requesterName,
		Comments:      comments,
		ProjectName:   projectName,
	}

	return p.SendGeneralApprovalNotification(params)
}

// SendPurchaseRollbackNotification 发送采购申请回退通知 (兼容旧接口)
func (p *PurchaseFeishuNotifier) SendPurchaseRollbackNotification(requestID uint, requestNo string, requesterName string, fromStage string, toStage string, comments string, projectName string) error {
	params := GeneralApprovalNotifyParams{
		FlowType:      FlowTypePurchaseRequest,
		EventType:     EventTypeRollback,
		BusinessID:    requestID,
		BusinessNo:    requestNo,
		RequesterName: requesterName,
		FromStage:     fromStage,
		ToStage:       toStage,
		Comments:      comments,
		ProjectName:   projectName,
	}

	return p.SendGeneralApprovalNotification(params)
}

// SendPurchaseInquirySubmittedNotification 发送采购询价提交通知 (兼容旧接口)
func (p *PurchaseFeishuNotifier) SendPurchaseInquirySubmittedNotification(inquiryID uint, inquiryNo string, requesterName string, purchaseType string, projectName string) error {
	params := GeneralApprovalNotifyParams{
		FlowType:      FlowTypePurchaseInquiry,
		EventType:     EventTypeSubmitted,
		BusinessID:    inquiryID,
		BusinessNo:    inquiryNo,
		RequesterName: requesterName,
		BusinessType:  purchaseType,
		ProjectName:   projectName,
	}

	return p.SendGeneralApprovalNotification(params)
}

// SendPurchaseInquiryStageChangedNotification 发送采购询价阶段变更通知 (兼容旧接口)
func (p *PurchaseFeishuNotifier) SendPurchaseInquiryStageChangedNotification(inquiryID uint, inquiryNo string, stageName string, requesterName string, projectName string) error {
	params := GeneralApprovalNotifyParams{
		FlowType:      FlowTypePurchaseInquiry,
		EventType:     EventTypeStageChange,
		BusinessID:    inquiryID,
		BusinessNo:    inquiryNo,
		RequesterName: requesterName,
		ToStage:       stageName,
		ProjectName:   projectName,
	}

	return p.SendGeneralApprovalNotification(params)
}

// SendPurchaseInquiryApprovedNotification 发送采购询价审批通过通知 (兼容旧接口)
func (p *PurchaseFeishuNotifier) SendPurchaseInquiryApprovedNotification(inquiryID uint, inquiryNo string, requesterName string, comments string, projectName string) error {
	// 记录日志，追踪实际参数
	p.Logger.Info("发送采购询价审批通过通知",
		zap.Uint("inquiryID", inquiryID),
		zap.String("inquiryNo", inquiryNo),
		zap.String("requesterName", requesterName),
		zap.String("comments", comments),
		zap.String("projectName", projectName))

	// 获取所有审批历史的意见
	allComments := p.getAllInquiryApprovalComments(inquiryID)
	if allComments == "" {
		allComments = comments
		p.Logger.Info("未找到历史审批意见，使用当前意见", zap.String("comments", comments))
	} else if comments != "" && !strings.Contains(allComments, comments) {
		allComments = allComments + comments
		p.Logger.Info("合并历史审批意见和当前意见",
			zap.String("allComments", allComments),
			zap.String("currentComments", comments))
	}

	params := GeneralApprovalNotifyParams{
		FlowType:      FlowTypePurchaseInquiry,
		EventType:     EventTypeApproved,
		BusinessID:    inquiryID,
		BusinessNo:    inquiryNo,
		RequesterName: requesterName,
		Comments:      allComments,
		ProjectName:   projectName,
	}

	return p.SendGeneralApprovalNotification(params)
}

// SendPurchaseInquiryRejectedNotification 发送采购询价拒绝通知 (兼容旧接口)
func (p *PurchaseFeishuNotifier) SendPurchaseInquiryRejectedNotification(inquiryID uint, inquiryNo string, requesterName string, comments string, projectName string) error {
	params := GeneralApprovalNotifyParams{
		FlowType:      FlowTypePurchaseInquiry,
		EventType:     EventTypeRejected,
		BusinessID:    inquiryID,
		BusinessNo:    inquiryNo,
		RequesterName: requesterName,
		Comments:      comments,
		ProjectName:   projectName,
	}

	return p.SendGeneralApprovalNotification(params)
}

// SendPurchaseInquiryRollbackNotification 发送采购询价回退通知 (兼容旧接口)
func (p *PurchaseFeishuNotifier) SendPurchaseInquiryRollbackNotification(inquiryID uint, inquiryNo string, requesterName string, fromStage string, toStage string, comments string, projectName string) error {
	params := GeneralApprovalNotifyParams{
		FlowType:      FlowTypePurchaseInquiry,
		EventType:     EventTypeRollback,
		BusinessID:    inquiryID,
		BusinessNo:    inquiryNo,
		RequesterName: requesterName,
		FromStage:     fromStage,
		ToStage:       toStage,
		Comments:      comments,
		ProjectName:   projectName,
	}

	return p.SendGeneralApprovalNotification(params)
}

// getAllApprovalComments 获取所有审批意见
func (p *PurchaseFeishuNotifier) getAllApprovalComments(requestID uint) string {
	if p.DB == nil {
		p.Logger.Error("未设置数据库连接，无法获取审批意见")
		return ""
	}

	type ApprovalHistory struct {
		Action       string    `gorm:"column:action"`
		OperatorName string    `gorm:"column:operator_name"`
		Comments     string    `gorm:"column:comments"`
		NewStatus    string    `gorm:"column:new_status"`
		CreatedAt    time.Time `gorm:"column:created_at"`
	}

	// 记录SQL查询
	sqlQuery := fmt.Sprintf("SELECT action, operator_name, comments, new_status, created_at FROM purchase_approval_histories WHERE business_id = %d AND business_type = 'purchase_request' AND action IN ('approve', 'reject') ORDER BY created_at ASC", requestID)
	p.Logger.Info("执行SQL查询", zap.String("query", sqlQuery))

	var histories []ApprovalHistory
	if err := p.DB.Table("purchase_approval_histories").
		Select("action, operator_name, comments, new_status, created_at").
		Where("business_id = ? AND business_type = 'purchase_request' AND action IN ('approve', 'reject')", requestID).
		Order("created_at ASC").
		Find(&histories).Error; err != nil {
		p.Logger.Error("查询审批历史失败", zap.Error(err))
		return ""
	}

	p.Logger.Info("查询到审批历史记录", zap.Int("count", len(histories)))

	if len(histories) == 0 {
		return ""
	}

	var result strings.Builder
	for i, history := range histories {
		if history.Comments != "" {
			actionText := "审批"
			if history.Action == "reject" {
				actionText = "拒绝"
			}

			commentLine := fmt.Sprintf("**%s (%s)：** %s\n\n",
				history.OperatorName,
				actionText,
				history.Comments)
			result.WriteString(commentLine)

			p.Logger.Info("处理审批记录",
				zap.Int("index", i),
				zap.String("action", history.Action),
				zap.String("operatorName", history.OperatorName),
				zap.String("comments", history.Comments),
				zap.String("formatted", commentLine))
		} else {
			p.Logger.Info("跳过空审批意见",
				zap.Int("index", i),
				zap.String("action", history.Action),
				zap.String("operatorName", history.OperatorName))
		}
	}

	return result.String()
}

// getAllInquiryApprovalComments 获取所有询价审批意见
func (p *PurchaseFeishuNotifier) getAllInquiryApprovalComments(inquiryID uint) string {
	if p.DB == nil {
		p.Logger.Error("未设置数据库连接，无法获取审批意见")
		return ""
	}

	type ApprovalHistory struct {
		Action       string    `gorm:"column:action"`
		OperatorName string    `gorm:"column:operator_name"`
		Comments     string    `gorm:"column:comments"`
		NewStatus    string    `gorm:"column:new_status"`
		CreatedAt    time.Time `gorm:"column:created_at"`
	}

	// 记录SQL查询
	sqlQuery := fmt.Sprintf("SELECT action, operator_name, comments, new_status, created_at FROM purchase_approval_histories WHERE business_id = %d AND business_type = 'purchase_inquiry' AND action IN ('approve', 'reject') ORDER BY created_at ASC", inquiryID)
	p.Logger.Info("执行SQL查询", zap.String("query", sqlQuery))

	var histories []ApprovalHistory
	if err := p.DB.Table("purchase_approval_histories").
		Select("action, operator_name, comments, new_status, created_at").
		Where("business_id = ? AND business_type = 'purchase_inquiry' AND action IN ('approve', 'reject')", inquiryID).
		Order("created_at ASC").
		Find(&histories).Error; err != nil {
		p.Logger.Error("查询审批历史失败", zap.Error(err))
		return ""
	}

	p.Logger.Info("查询到审批历史记录", zap.Int("count", len(histories)))

	if len(histories) == 0 {
		return ""
	}

	var result strings.Builder
	for i, history := range histories {
		if history.Comments != "" {
			actionText := "审批"
			if history.Action == "reject" {
				actionText = "拒绝"
			}

			commentLine := fmt.Sprintf("**%s (%s)：** %s\n\n",
				history.OperatorName,
				actionText,
				history.Comments)
			result.WriteString(commentLine)

			p.Logger.Info("处理审批记录",
				zap.Int("index", i),
				zap.String("action", history.Action),
				zap.String("operatorName", history.OperatorName),
				zap.String("comments", history.Comments),
				zap.String("formatted", commentLine))
		} else {
			p.Logger.Info("跳过空审批意见",
				zap.Int("index", i),
				zap.String("action", history.Action),
				zap.String("operatorName", history.OperatorName))
		}
	}

	return result.String()
}

// generatePurchaseSign 生成采购通知专用签名
func (p *PurchaseFeishuNotifier) generatePurchaseSign() (string, string) {
	// 如果没有配置采购专用secret，则使用通用签名方法
	if p.PurchaseSecret == "" {
		return p.generateSign()
	}

	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	stringToSign := fmt.Sprintf("%s\n%s", timestamp, p.PurchaseSecret)

	h := hmac.New(sha256.New, []byte(stringToSign))
	signBytes := h.Sum(nil)
	sign := base64.StdEncoding.EncodeToString(signBytes)

	return timestamp, sign
}

// sendFeishuMessage 发送飞书消息通用方法
func (p *PurchaseFeishuNotifier) sendFeishuMessage(msg map[string]interface{}) error {
	// 检查是否启用了采购通知
	if !p.PurchaseEnabled && p.PurchaseWebhookURL != p.WebhookURL {
		p.Logger.Info("采购通知未启用，跳过发送")
		return nil
	}

	// 添加签名
	timestamp, sign := p.generatePurchaseSign()
	msg["timestamp"] = timestamp
	msg["sign"] = sign

	// 序列化为JSON
	jsonData, err := json.Marshal(msg)
	if err != nil {
		p.Logger.Error("飞书通知JSON序列化失败", zap.Error(err))
		return fmt.Errorf("序列化消息失败: %w", err)
	}

	// 确定使用哪个webhook
	webhookURL := p.WebhookURL
	if p.PurchaseWebhookURL != "" {
		webhookURL = p.PurchaseWebhookURL
	}

	// 验证webhook URL
	if err := validateWebhookURL(webhookURL); err != nil {
		p.Logger.Error("无效的webhook URL", zap.String("url", webhookURL), zap.Error(err))
		return fmt.Errorf("无效的webhook URL: %w", err)
	}

	// 创建请求
	req, err := http.NewRequest(http.MethodPost, webhookURL, bytes.NewBuffer(jsonData)) // #nosec G107
	if err != nil {
		p.Logger.Error("创建HTTP请求失败", zap.Error(err))
		return fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		p.Logger.Error("发送飞书通知失败", zap.Error(err))
		return fmt.Errorf("发送通知失败: %w", err)
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			p.Logger.Error("关闭响应体失败", zap.Error(err))
		}
	}(resp.Body)

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		p.Logger.Error("飞书通知API返回非200状态码",
			zap.Int("statusCode", resp.StatusCode))
		return fmt.Errorf("飞书API返回状态码: %d", resp.StatusCode)
	}

	p.Logger.Info("飞书通知发送成功")
	return nil
}

// validateWebhookURL 验证webhook URL的安全性
func validateWebhookURL(urlStr string) error {
	// 解析URL
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return fmt.Errorf("URL解析失败: %w", err)
	}

	// 检查URL方案，必须是https
	if parsedURL.Scheme != "https" {
		return fmt.Errorf("webhook URL必须使用HTTPS协议")
	}

	// 检查主机名，不允许使用IP地址或内部域名
	host := parsedURL.Hostname()

	// 检查是否是IP地址
	ip := net.ParseIP(host)
	if ip != nil {
		// 检查是否是内部IP
		if isPrivateIP(ip) {
			return fmt.Errorf("webhook URL不能使用内部IP地址")
		}
	}

	// 飞书webhook域名白名单
	allowedDomains := []string{
		"open.feishu.cn",
		"open.larksuite.com",
		"open.feishu.com",
	}

	// 检查是否是允许的域名
	isAllowed := false
	for _, domain := range allowedDomains {
		if strings.HasSuffix(host, domain) {
			isAllowed = true
			break
		}
	}

	if !isAllowed {
		return fmt.Errorf("webhook URL必须使用飞书官方域名")
	}

	return nil
}

// isPrivateIP 检查IP是否是内部IP
func isPrivateIP(ip net.IP) bool {
	// 检查IPv4私有地址范围
	if ip4 := ip.To4(); ip4 != nil {
		// 10.0.0.0/8
		if ip4[0] == 10 {
			return true
		}
		// **********/12
		if ip4[0] == 172 && ip4[1] >= 16 && ip4[1] <= 31 {
			return true
		}
		// ***********/16
		if ip4[0] == 192 && ip4[1] == 168 {
			return true
		}
		// *********/8
		if ip4[0] == 127 {
			return true
		}
	}

	// 检查IPv6本地地址
	if ip.IsLoopback() || ip.IsLinkLocalUnicast() || ip.IsLinkLocalMulticast() {
		return true
	}

	return false
}

// SendInvoiceCreatedNotification 发送发票创建通知
func (p *PurchaseFeishuNotifier) SendInvoiceCreatedNotification(invoiceID uint, invoiceNo string, contractNo string, creatorName string, projectName string, supplierName string, invoiceAmount float64) error {
	// 检查是否启用采购通知
	if !p.PurchaseEnabled {
		return nil
	}

	// 构建发票详情URL（使用模块化URL模板）
	detailURL := p.GetUrlTemplate("invoice")

	// 处理项目名称为空的情况
	projectDisplay := projectName
	if projectDisplay == "" {
		projectDisplay = "暂无项目信息"
	}

	// 构建卡片标题
	header := map[string]interface{}{
		"template": "green",
		"title": map[string]interface{}{
			"content": "💰 发票创建通知",
			"tag":     "plain_text",
		},
	}

	// 构建主要内容
	content := fmt.Sprintf("**📝 新发票已创建 (%s)**", invoiceNo)

	// 构建基本信息字段
	fields := []map[string]interface{}{
		{
			"is_short": true,
			"text": map[string]interface{}{
				"content": fmt.Sprintf("**📝 发票号**\n%s", invoiceNo),
				"tag":     "lark_md",
			},
		},
		{
			"is_short": true,
			"text": map[string]interface{}{
				"content": fmt.Sprintf("**👤 创建人**\n%s", creatorName),
				"tag":     "lark_md",
			},
		},
		{
			"is_short": true,
			"text": map[string]interface{}{
				"content": fmt.Sprintf("**📋 合同编号**\n%s", contractNo),
				"tag":     "lark_md",
			},
		},
		{
			"is_short": true,
			"text": map[string]interface{}{
				"content": fmt.Sprintf("**💵 开票金额**\n¥%.2f", invoiceAmount),
				"tag":     "lark_md",
			},
		},
	}

	// 如果有项目信息，添加项目字段
	if projectDisplay != "暂无项目信息" {
		fields = append(fields, map[string]interface{}{
			"is_short": true,
			"text": map[string]interface{}{
				"content": fmt.Sprintf("**📂 关联项目**\n%s", projectDisplay),
				"tag":     "lark_md",
			},
		})
	}

	// 构建元素数组
	elements := []map[string]interface{}{
		{
			"tag": "div",
			"text": map[string]interface{}{
				"content": content,
				"tag":     "lark_md",
			},
		},
		{
			"tag": "hr",
		},
		{
			"tag":    "div",
			"fields": fields,
		},
	}

	// 添加分隔线和查看详情按钮
	elements = append(elements,
		map[string]interface{}{
			"tag": "hr",
		},
		map[string]interface{}{
			"tag": "action",
			"actions": []map[string]interface{}{
				{
					"tag": "button",
					"text": map[string]interface{}{
						"content": "📋 查看发票详情",
						"tag":     "plain_text",
					},
					"url":  detailURL,
					"type": "primary",
				},
			},
		},
	)

	// 构建完整消息
	message := map[string]interface{}{
		"msg_type": "interactive",
		"card": map[string]interface{}{
			"config": map[string]interface{}{
				"wide_screen_mode": true,
				"enable_forward":   true,
			},
			"header":   header,
			"elements": elements,
		},
	}

	// 使用采购专用的webhook发送
	return p.sendPurchaseMessage(message)
}

// sendPurchaseMessage 发送采购专用消息
func (p *PurchaseFeishuNotifier) sendPurchaseMessage(message map[string]interface{}) error {
	// 使用配置的采购专用webhook URL和secret
	webhookURL := p.PurchaseWebhookURL
	secret := p.PurchaseSecret

	// 验证URL安全性
	if err := p.validateWebhookURL(webhookURL); err != nil {
		return fmt.Errorf("webhook URL验证失败: %w", err)
	}

	// 添加签名
	if secret != "" {
		timestamp, sign := p.generateCustomSign(secret)
		message["timestamp"] = timestamp
		message["sign"] = sign
	}

	// 序列化消息
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("序列化消息失败: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", webhookURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	// 使用安全的HTTP客户端发送请求
	client := p.createSecureHTTPClient()
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送通知失败: %w", err)
	}
	defer func() {
		if closeErr := resp.Body.Close(); closeErr != nil {
			// 记录关闭错误，但不影响主要逻辑
			fmt.Printf("关闭响应体失败: %v\n", closeErr)
		}
	}()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("飞书API返回状态码: %d", resp.StatusCode)
	}

	return nil
}

// generateCustomSign 生成自定义签名
func (p *PurchaseFeishuNotifier) generateCustomSign(secret string) (string, string) {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	stringToSign := fmt.Sprintf("%s\n%s", timestamp, secret)

	h := hmac.New(sha256.New, []byte(stringToSign))
	signBytes := h.Sum(nil)
	sign := base64.StdEncoding.EncodeToString(signBytes)

	return timestamp, sign
}

// validateWebhookURL 验证webhook URL的安全性
func (p *PurchaseFeishuNotifier) validateWebhookURL(webhookURL string) error {
	if webhookURL == "" {
		return fmt.Errorf("webhook URL不能为空")
	}

	// 解析URL
	parsedURL, err := url.Parse(webhookURL)
	if err != nil {
		return fmt.Errorf("无效的URL格式: %w", err)
	}

	// 检查协议
	if parsedURL.Scheme != "https" {
		return fmt.Errorf("webhook URL必须使用HTTPS协议")
	}

	// 检查域名是否为飞书官方域名
	allowedHosts := []string{
		"open.feishu.cn",
		"open.larksuite.com",
	}

	hostAllowed := false
	for _, allowedHost := range allowedHosts {
		if parsedURL.Host == allowedHost {
			hostAllowed = true
			break
		}
	}

	if !hostAllowed {
		return fmt.Errorf("不允许的webhook域名: %s", parsedURL.Host)
	}

	return nil
}

// createSecureHTTPClient 创建安全的HTTP客户端
func (p *PurchaseFeishuNotifier) createSecureHTTPClient() *http.Client {
	return &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			// 禁用HTTP重定向以防止重定向攻击
			DisableCompression: false,
			// 设置连接超时
			DialContext: (&net.Dialer{
				Timeout:   10 * time.Second,
				KeepAlive: 30 * time.Second,
			}).DialContext,
			// 设置TLS超时
			TLSHandshakeTimeout: 10 * time.Second,
			// 限制空闲连接
			MaxIdleConns:        10,
			MaxIdleConnsPerHost: 2,
			IdleConnTimeout:     60 * time.Second,
		},
		// 禁用自动重定向
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		},
	}
}
