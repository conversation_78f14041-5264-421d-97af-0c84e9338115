package notifier

import (
	"backend/configs"
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"
)

// 软件排班机器人配置
type SoftScheNotifier struct {
	SoftWebhookURL        string
	SoftSecret            string
	SoftDetailUrlTemplate string
	SoftScheTemplate      string
	SoftScheEnabled       bool
}

// 硬件排班机器人配置

// 初始化软件排班飞书通知
func NewCustomSoftScheNotifier(feishuConfig *configs.FeishuConfig) *SoftScheNotifier {
	if feishuConfig == nil {
		fmt.Println("【ERROR】初始化软件人员排班飞书通知失败，缺少飞书配置")
		return &SoftScheNotifier{}
	}
	return &SoftScheNotifier{
		SoftWebhookURL:        feishuConfig.SoftScheWebhookURL,
		SoftSecret:            feishuConfig.SoftScheSecret,
		SoftDetailUrlTemplate: feishuConfig.SoftScheDetailUrlTemplate,
		SoftScheTemplate:      feishuConfig.SoftScheTemplate,
		SoftScheEnabled:       feishuConfig.SoftScheEnabled,
	}
}

// 生成签名
func (s *SoftScheNotifier) generateSign(secret string) (string, string) {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	stringToSign := fmt.Sprintf("%s\n%s", timestamp, secret)

	h := hmac.New(sha256.New, []byte(stringToSign))

	signBytes := h.Sum(nil)
	sign := base64.StdEncoding.EncodeToString(signBytes)
	return timestamp, sign
}

// sendSoftScheNotifier 发送软件排班信息统一接口
func (s *SoftScheNotifier) sendSoftScheNotifier(templateID, templateVersionName string, templateVariable map[string]interface{}, bot SoftScheNotifier) {
	// 构建消息
	msg := map[string]interface{}{
		"msg_type": "interactive",
		"card": map[string]interface{}{
			"type": "template",
			"data": map[string]interface{}{
				"template_id":           templateID,
				"template_version_name": templateVersionName,
				"template_variable":     templateVariable,
			},
		},
	}
	// 添加签名
	timestamp, sign := s.generateSign(bot.SoftSecret)
	msg["timestamp"] = timestamp
	msg["sign"] = sign

	// 序列化为JSON
	jsonData, err := json.Marshal(msg)
	if err != nil {
		fmt.Printf("飞书通知JSON序列化失败: %v\n", err)
	}
	// 发送HTTP请求
	resp, err := http.Post(bot.SoftWebhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("发送飞书通知失败 -  error: %v\n", err)
	}

	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			fmt.Printf("关闭飞书通知请求体失败 -  error: %v\n", err)
		}
	}(resp.Body)
}

// SendSoftScheNotification 发送软件排班通知
func (s *SoftScheNotifier) SendSoftScheNotification(date, primaryName, secondName string) error {
	// TemplateVariable 定义模板变量结构
	type TemplateVariable map[string]interface{}
	bot := SoftScheNotifier{
		SoftWebhookURL:        s.SoftWebhookURL,
		SoftSecret:            s.SoftSecret,
		SoftDetailUrlTemplate: s.SoftDetailUrlTemplate,
		SoftScheEnabled:       s.SoftScheEnabled,
		SoftScheTemplate:      s.SoftScheTemplate,
	}
	tem := TemplateVariable{
		"date":         date,
		"primary_name": primaryName,
		"second_name":  secondName,
	}
	s.sendSoftScheNotifier(bot.SoftScheTemplate, "1.0.6", tem, bot)
	return nil
}
