// routes/api_v1.go 中添加位置管理路由

package routes

import (
	"backend/internal/di"

	"github.com/gin-gonic/gin"
)

// RegisterAPIRouter 注册 API 路由
func RegisterAPIRouter(r *gin.Engine, apiCtrl *di.APIController) {
	// 创建 API v1 路由组
	v1 := r.Group("/api/v1")

	// 受保护路由（需要认证）
	protected := v1.Group("/")
	// 全局已经添加了认证中间件，这里不需要重复添加
	// protected.Use(middleware.AuthMiddleware()) // 移除重复的JWT认证中间件
	{
		// GPU 服务器相关路由
		gpuServer := protected.Group("/cmdb/gpuServer")
		{
			gpuServer.POST("", apiCtrl.GpuServerCtrl.CreateGpuServer)       // 创建 GPU 服务器记录
			gpuServer.GET("", apiCtrl.GpuServerCtrl.ListGpuServer)          // 查询 GPU 服务器记录（支持字段过滤）
			gpuServer.GET("/:id", apiCtrl.GpuServerCtrl.GetGpuServer)       // 获取单条 GPU 服务器记录
			gpuServer.PUT("/:id", apiCtrl.GpuServerCtrl.UpdateGpuServerPut) // 更新 GPU 服务器记录
			gpuServer.DELETE("/:id", apiCtrl.GpuServerCtrl.DeleteGpuServer) // 删除 GPU 服务器记录
		}

		// 服务器相关路由
		serverInfo := protected.Group("/cmdb/server")
		{
			serverInfo.GET("/:sn", apiCtrl.ServerCtrl.GetServerInfoHandler) // 获取服务器详细信息
		}

		//导入数据相关路由
		importData := protected.Group("/import")
		{
			importData.POST("", apiCtrl.ImportCtrl.ImportData)                       // 导入数据
			importData.GET("/template/:model", apiCtrl.ImportCtrl.GetImportTemplate) // 获取导入模板

			importOutboundData := importData.Group("/outbound")
			{
				//importOutboundData.GET("/template/:model", apiCtrl.ImportCtrl.GetOutboundImportTemplate)
				importOutboundData.POST("/spare", apiCtrl.ImportCtrl.ImportOutboundSpareData)
			}
		}

		// CMDB 管理相关路由
		location := protected.Group("/cmdb")

		{
			// 区域管理路由
			regions := location.Group("/regions")
			{
				regions.POST("", apiCtrl.RegionCtrl.Create)
				regions.PUT("/:id", apiCtrl.RegionCtrl.Update)
				regions.DELETE("/:id", apiCtrl.RegionCtrl.Delete)
				regions.GET("/:id", apiCtrl.RegionCtrl.GetByID)
				regions.GET("", apiCtrl.RegionCtrl.List)
				regions.GET("/warehouse/:name", apiCtrl.RegionCtrl.GetWarehousesByProject)
				// 获取区域下的可用区（RESTful风格）
				regions.GET("/:id/azs", apiCtrl.AZCtrl.GetByRegionID)
			}

			// 可用区管理路由
			azs := location.Group("/azs")
			{
				azs.POST("", apiCtrl.AZCtrl.Create)
				azs.PUT("/:id", apiCtrl.AZCtrl.Update)
				azs.DELETE("/:id", apiCtrl.AZCtrl.Delete)
				azs.GET("/:id", apiCtrl.AZCtrl.GetByID)
				azs.GET("", apiCtrl.AZCtrl.List)

				// 获取可用区下的机房（RESTful风格）
				azs.GET("/:id/datacenters", apiCtrl.DataCenterCtrl.GetByAZID)
			}

			// 机房管理路由
			datacenters := location.Group("/datacenters")
			{
				datacenters.POST("", apiCtrl.DataCenterCtrl.Create)
				datacenters.PUT("/:id", apiCtrl.DataCenterCtrl.Update)
				datacenters.DELETE("/:id", apiCtrl.DataCenterCtrl.Delete)
				datacenters.GET("/:id", apiCtrl.DataCenterCtrl.GetByID)
				datacenters.GET("", apiCtrl.DataCenterCtrl.List)

				// 获取机房下的房间（RESTful风格）
				datacenters.GET("/:id/rooms", apiCtrl.RoomCtrl.GetByDataCenterID)
			}

			// 房间管理路由
			rooms := location.Group("/rooms")
			{
				rooms.POST("", apiCtrl.RoomCtrl.Create)
				rooms.PUT("/:id", apiCtrl.RoomCtrl.Update)
				rooms.DELETE("/:id", apiCtrl.RoomCtrl.Delete)
				rooms.GET("/:id", apiCtrl.RoomCtrl.GetByID)
				rooms.GET("", apiCtrl.RoomCtrl.List)

				// 获取房间下的机柜（RESTful风格）
				rooms.GET("/:id/cabinets", apiCtrl.CabinetCtrl.GetByRoomID)
			}

			// 机柜管理路由
			cabinets := location.Group("/cabinets")
			{
				cabinets.POST("", apiCtrl.CabinetCtrl.Create)
				cabinets.PUT("/:id", apiCtrl.CabinetCtrl.Update)
				cabinets.DELETE("/:id", apiCtrl.CabinetCtrl.Delete)
				cabinets.GET("/:id", apiCtrl.CabinetCtrl.GetByID)
				cabinets.GET("", apiCtrl.CabinetCtrl.List)
			}

			// 资产设备管理路由
			devices := location.Group("/devices")
			{
				devices.POST("", apiCtrl.DeviceCtrl.Create)
				devices.PUT("/:id", apiCtrl.DeviceCtrl.Update)
				devices.DELETE("/:id", apiCtrl.DeviceCtrl.Delete)
				devices.GET("/:id", apiCtrl.DeviceCtrl.GetByID)
				devices.GET("/by-sn", apiCtrl.DeviceCtrl.GetBySN)
				devices.GET("/idle/by-sn", apiCtrl.DeviceCtrl.GetIdleBySN)
				devices.GET("/isExist", apiCtrl.DeviceCtrl.IsExist)
				devices.GET("", apiCtrl.DeviceCtrl.List)
				devices.GET("/amount", apiCtrl.DeviceCtrl.GetDeviceAmount) // 获取设备数量
			}

			// 资源管理路由
			resources := location.Group("/resources")
			{
				resources.POST("", apiCtrl.ResourceCtrl.Create)
				resources.PUT("/:id", apiCtrl.ResourceCtrl.Update)
				resources.DELETE("/:id", apiCtrl.ResourceCtrl.Delete)
				resources.GET("/:id", apiCtrl.ResourceCtrl.GetByID)
				resources.GET("/by-asset", apiCtrl.ResourceCtrl.GetByAssetID)
				resources.GET("/by-sn", apiCtrl.ResourceCtrl.GetBySN)
				resources.GET("", apiCtrl.ResourceCtrl.List)
				resources.GET("/projects", apiCtrl.ResourceCtrl.GetAllProjects)
				resources.GET("/available-backups", apiCtrl.ResourceCtrl.GetAvailableBackups)
				resources.GET("/clusters", apiCtrl.ResourceCtrl.GetAllClusters)
			}

			// 资产状态变更相关路由
			assetStatus := location.Group("/asset-status")
			{
				assetStatus.GET("/:id/history", apiCtrl.StatusChangeCtrl.GetAssetStatusHistory)
				//assetStatus.POST("/change-asset-status", apiCtrl.StatusChangeCtrl.ChangeAssetStatus)
				//assetStatus.POST("/change-biz-status", apiCtrl.StatusChangeCtrl.ChangeBizStatus)
				//assetStatus.POST("/process-storage", apiCtrl.StatusChangeCtrl.ProcessAssetStorage)
				//assetStatus.POST("/process-outbound", apiCtrl.StatusChangeCtrl.ProcessAssetOutbound)
				//assetStatus.POST("/process-racking", apiCtrl.StatusChangeCtrl.ProcessAssetRacking)
				//assetStatus.POST("/process-delivery", apiCtrl.StatusChangeCtrl.ProcessAssetDelivery)
				//assetStatus.POST("/process-maintenance", apiCtrl.StatusChangeCtrl.ProcessAssetMaintenance)
				//assetStatus.POST("/process-scrap", apiCtrl.StatusChangeCtrl.ProcessAssetScrap)
			}
			deviceReource := location.Group("/device-resources")
			{
				deviceReource.GET("", apiCtrl.DeviceResourceCtrl.List)
				deviceReource.GET("/:id", apiCtrl.DeviceResourceCtrl.GetByID)
				deviceReource.POST("", apiCtrl.DeviceResourceCtrl.Create)
				deviceReource.PUT("/:id", apiCtrl.DeviceResourceCtrl.Update)
				deviceReource.DELETE("/:id", apiCtrl.DeviceResourceCtrl.Delete)
			}

			// 套餐模板相关路由
			templateGroup := location.Group("/machine-templates")
			{
				// 套餐模板CRUD
				templateGroup.GET("", apiCtrl.MachineTemplateCtrl.List)
				templateGroup.GET("/:id", apiCtrl.MachineTemplateCtrl.Get)
				templateGroup.POST("", apiCtrl.MachineTemplateCtrl.Create)
				templateGroup.PUT("/:id", apiCtrl.MachineTemplateCtrl.Update)
				templateGroup.DELETE("/:id", apiCtrl.MachineTemplateCtrl.Delete)

				// 套餐模板组件管理
				templateGroup.GET("/:id/components", apiCtrl.TemplateComponentCtrl.ListByTemplateID)
				templateGroup.POST("/:id/components", apiCtrl.TemplateComponentCtrl.Create)
			}

			// 独立的模板组件路由
			componentGroup := location.Group("/template-components")
			{
				componentGroup.PUT("/:id", apiCtrl.TemplateComponentCtrl.Update)
				componentGroup.DELETE("/:id", apiCtrl.TemplateComponentCtrl.Delete)
			}

			// 添加设备模板路由
			location.GET("/devices/:id/machine-template", apiCtrl.DeviceResourceCtrl.GetDeviceTemplate)
			//排班表相关路由
			//schedule := protected.Group("/schedule")
			//{
			//	softSchedule := schedule.Group("/soft-sche")
			//	{
			//		softSchedule.POST("", apiCtrl.SoftScheduleCtrl.CreateSchedule)
			//		softSchedule.GET("/:date", apiCtrl.SoftScheduleCtrl.GetScheduleByMonth)     //根据年月得到值班人列表
			//		softSchedule.GET("/date/:date", apiCtrl.SoftScheduleCtrl.GetScheduleByDate) //根据日期得到值班人
			//		softSchedule.PUT("/:date", apiCtrl.SoftScheduleCtrl.UpdateSchedule)         //更新值班人
			//
			//	}
			//	hardSchedule := schedule.Group("/hard-sche")
			//	{
			//		hardSchedule.POST("", apiCtrl.HardScheduleCtrl.Create)
			//		hardSchedule.GET("/:date", apiCtrl.HardScheduleCtrl.GetByMonth)
			//		hardSchedule.GET("", apiCtrl.HardScheduleCtrl.GetByDate)
			//		hardSchedule.PUT("", apiCtrl.HardScheduleCtrl.Update)
			//	}
			//}
		}
	}
}
