package routes

import (
	"backend/configs"
	_ "backend/docs"
	"backend/internal/common/utils"
	"backend/internal/di"
	"backend/internal/infrastructure/app"
	"backend/internal/infrastructure/cache"
	"backend/internal/infrastructure/casbin"
	zapLog "backend/internal/infrastructure/logger"
	"backend/internal/middleware"
	"backend/internal/modules/audit"
	auditService "backend/internal/modules/audit/service"
	"backend/internal/modules/cmdb"
	productRepo "backend/internal/modules/cmdb/repository/product"
	productService "backend/internal/modules/cmdb/service/product"
	"backend/internal/modules/customerapi"
	"backend/internal/modules/customerapi/model"
	customerService "backend/internal/modules/customerapi/service"
	"backend/internal/modules/dashboard"
	"backend/internal/modules/export"
	"backend/internal/modules/file"
	"backend/internal/modules/hardware_maintenance"
	"backend/internal/modules/purchase"
	"backend/internal/modules/purchase_old"
	"backend/internal/modules/schedule"
	"backend/internal/modules/software_maintenance"
	"backend/internal/modules/system"
	"backend/internal/modules/ticket"
	ticketService "backend/internal/modules/ticket/service"
	"backend/internal/modules/user"
	userService "backend/internal/modules/user/service"
	"context"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	"github.com/gin-gonic/gin"
	"go.temporal.io/sdk/client"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// InitRouter 初始化路由
func InitRouter(db *gorm.DB, jwtSecret string, logger *zap.Logger, appMode string, config *configs.Config, redisCache *cache.RedisClient, casbinService *casbin.CasbinService) *gin.Engine {
	// 设置Gin运行模式
	setupGinMode(logger, appMode)

	// 创建gin引擎并配置中间件
	r := gin.New()
	setupMiddleware(r, db, logger, casbinService)

	// 注册API路由
	apiCtrl := di.NewAPIController(db, jwtSecret)
	RegisterAPIRouter(r, apiCtrl)

	// 注册模块路由
	apiV1 := r.Group("/api/v1")
	registerModuleRoutes(apiV1, db, logger, config, jwtSecret, redisCache, casbinService)

	// 注册健康检查路由
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	// 注册Swagger文档路由
	registerSwaggerIfEnabled(r, logger)

	return r
}

// 设置Gin运行模式
func setupGinMode(logger *zap.Logger, appMode string) {
	ginMode := os.Getenv("GIN_MODE")
	if ginMode != "" {
		gin.SetMode(ginMode)
		logger.Info("Gin运行模式: " + ginMode + " (由环境变量GIN_MODE设置)")
		return
	}

	envMode := os.Getenv("APP_ENV")
	switch {
	case envMode == "prod":
		gin.SetMode(gin.ReleaseMode)
		logger.Info("Gin运行模式: Release Mode (由环境变量APP_ENV设置)")
	case envMode != "":
		gin.SetMode(gin.DebugMode)
		logger.Info("Gin运行模式: Debug Mode (由环境变量APP_ENV设置)")
	case appMode == "production":
		gin.SetMode(gin.ReleaseMode)
		logger.Info("Gin运行模式: Release Mode (由配置文件设置)")
	default:
		gin.SetMode(gin.DebugMode)
		logger.Info("Gin运行模式: Debug Mode (由配置文件设置)")
	}
}

// 设置中间件
func setupMiddleware(r *gin.Engine, db *gorm.DB, logger *zap.Logger, casbinService *casbin.CasbinService) {
	// 添加全局中间件
	r.Use(gin.Recovery())
	r.Use(middleware.CORSMiddleware())
	r.Use(middleware.RequestLoggerMiddleware(logger))

	// 添加操作日志中间件
	operationLogService := auditService.NewOperationLogService(db, logger)
	operationLoggerMiddleware := middleware.NewOperationLoggerMiddleware(operationLogService, logger)
	r.Use(operationLoggerMiddleware.Handle())

	// 登录和公共API路由不需要认证和权限验证
	publicPaths := []string{
		"/api/v1/auth/login",       // 登录接口
		"/api/v1/auth/feishu/*",    // 飞书登录相关接口
		"/health",                  // 健康检查
		"/swagger/*",               // Swagger文档
		"/api/v1/file/view/*",      // 文件查看
		"/api/v1/file/thumbnail/*", // 文件缩略图
	}

	// 需要认证但无需权限验证的路径
	authOnlyPaths := []string{

		"/api/v1/system/menu/all",              // 获取所有菜单（系统路径）
		"/api/v1/auth/userInfo",                // 获取用户信息
		"/api/v1/auth/codes",                   // 获取权限码
		"/api/v1/menu/dashboard",               // 仪表盘菜单
		"/api/v1/system/menu/dashboard",        // 仪表盘菜单（系统路径）
		"/api/v1/auth/check-permission-status", // 检查权限更新状态

	}

	// 添加全局认证中间件，但排除公共路径
	r.Use(func(c *gin.Context) {
		path := c.Request.URL.Path

		// 检查是否是公共路径
		for _, publicPath := range publicPaths {
			if utils.PathMatch(path, publicPath) {
				c.Next()
				return
			}
		}

		// 对需要认证的路径应用认证中间件
		if strings.HasPrefix(path, "/api/v1") {
			middleware.AuthMiddleware()(c)
			return
		}

		c.Next()
	})

	// 为需要权限验证的路由添加Casbin中间件
	if casbinService != nil {
		// 使用Casbin中间件，但排除公共路由和仅需认证的路由
		r.Use(func(c *gin.Context) {
			// 如果请求已被终止（例如认证失败），则跳过权限验证
			if c.IsAborted() {
				return
			}

			path := c.Request.URL.Path

			// 检查是否是公共路径
			for _, publicPath := range publicPaths {
				if utils.PathMatch(path, publicPath) {
					return
				}
			}

			// 检查是否是仅需认证的路径
			for _, authPath := range authOnlyPaths {
				if utils.PathMatch(path, authPath) {
					return
				}
			}

			// 如果不是公共路径或仅需认证的路径，且请求路径以/api/v1开头
			if strings.HasPrefix(path, "/api/v1") {
				middleware.AuthorizeWithCasbin(casbinService)(c)
			}
		})
		logger.Info("已启用全局Casbin权限验证中间件")
	} else {
		logger.Warn("Casbin服务未初始化，权限验证已禁用")
	}
}

// 注册模块路由
func registerModuleRoutes(apiV1 *gin.RouterGroup, db *gorm.DB, logger *zap.Logger, config *configs.Config, jwtSecret string, redisCache *cache.RedisClient, casbinService *casbin.CasbinService) {
	// 先注册用户模块，因为其他模块需要用户模块的服务
	// 注册用户模块
	userService := registerUserModule(apiV1, db, logger, redisCache)

	// 注册CMDB模块
	registerCMDBModules(apiV1, db)

	// 注册仪表盘模块
	registerDashboardModule(apiV1, db)

	// 注册审计模块
	registerAuditModule(apiV1, db, logger)

	// 注册文件模块
	registerFileModule(apiV1, db, logger)

	// 注册工单模块
	temporalClient := setupTemporalClient(logger)
	registerTicketModule(apiV1, db, logger, temporalClient, config, jwtSecret, userService)

	// 注册导出模块
	registerExportModule(apiV1, db, logger)

	// 注册采购模块 (需要用户服务)
	registerPurchaseModules(apiV1, db, logger, temporalClient, userService)

	// 注册硬件运维模块
	registerHardwareMaintenance(apiV1, db, logger, temporalClient)

	// 注册软件运维模块
	registerSoftwareMaintenance(apiV1, db, logger, temporalClient)

	// 注册排班表模块
	registerScheduleModule(apiV1, db, config)

	// 注册系统模块
	registerSystemModule(apiV1, db, logger, redisCache, userService, casbinService)
}

// 注册CMDB模块
func registerCMDBModules(apiV1 *gin.RouterGroup, db *gorm.DB) {
	cmdb.RegisterProductModule(apiV1, db)
	cmdb.RegisterComponentModule(apiV1, db)
	cmdb.RegisterInventoryModule(apiV1, db)
	cmdb.RegisterSpareModule(apiV1, db)
	cmdb.RegisterNetworkDeviceModule(apiV1, db)
	cmdb.RegisterWarehouseModule(apiV1, db)
	cmdb.RegisterInboundModule(apiV1, db)
}

// 注册采购管理模块
func registerPurchaseModules(apiV1 *gin.RouterGroup, db *gorm.DB, logger *zap.Logger, temporalClient client.Client, userService userService.IUserService) {
	// 注册旧的采购模块 - 保证旧模块始终注册
	purchase_old.RegisterPurchaseModule(apiV1, db)

	// 单独注册新的采购模块
	registerNewPurchaseModule(apiV1, db, logger, temporalClient, userService)
}

// 单独注册新的采购模块
func registerNewPurchaseModule(apiV1 *gin.RouterGroup, db *gorm.DB, logger *zap.Logger, temporalClient client.Client, userService userService.IUserService) {
	// 首先初始化产品服务，因为采购模块需要它
	productRepo := productRepo.NewProductRepository(db)
	productService := productService.NewProductService(productRepo)

	// 注册新的采购模块，传递产品服务
	purchaseModule := purchase.NewModule(db, logger, temporalClient, userService, productService)
	if err := purchaseModule.Initialize(); err != nil {
		logger.Error("初始化采购模块失败", zap.Error(err))
		return
	}

	if err := purchaseModule.AutoMigrate(); err != nil {
		logger.Error("采购模块数据库迁移失败", zap.Error(err))
		return
	}

	purchaseModule.RegisterRoutes(apiV1)
	logger.Info("新采购模块注册成功")
}

// 注册仪表盘模块
func registerDashboardModule(apiV1 *gin.RouterGroup, db *gorm.DB) {
	dashboard.RegisterDashboardModule(apiV1, db)
}

// 注册系统模块
func registerSystemModule(apiV1 *gin.RouterGroup, db *gorm.DB, logger *zap.Logger, redisCache *cache.RedisClient, userService userService.IUserService, casbinService *casbin.CasbinService) {
	// 创建应用程序实例
	application := &app.App{
		DB:         db,
		Logger:     logger,
		RedisCache: redisCache,
	}

	// 注册系统模块
	system.RegisterSystemModule(apiV1, application, userService, casbinService)
	logger.Info("系统模块初始化并迁移成功")
}

// 注册审计模块
func registerAuditModule(apiV1 *gin.RouterGroup, db *gorm.DB, logger *zap.Logger) {
	// 创建审计模块实例
	auditModule := audit.NewModule(db)

	// 初始化模块
	if err := auditModule.Initialize(); err != nil {
		logger.Error("初始化审计模块失败", zap.Error(err))
		return
	}

	// 执行数据库迁移
	if err := auditModule.AutoMigrate(); err != nil {
		logger.Error("审计模块数据库迁移失败", zap.Error(err))
		return
	}

	// 注册路由
	auditModule.RegisterRoutes(apiV1)
}

// 注册文件模块
func registerFileModule(apiV1 *gin.RouterGroup, db *gorm.DB, logger *zap.Logger) {
	fileModule := file.NewModule(db)
	if err := fileModule.Initialize(); err != nil {
		logger.Error("初始化文件模块失败", zap.Error(err))
		return
	}

	if err := fileModule.AutoMigrate(); err != nil {
		logger.Error("文件模块数据库迁移失败", zap.Error(err))
		return
	}

	fileModule.RegisterRoutes(apiV1)
	logger.Info("文件模块初始化并迁移成功")
}

// 设置Temporal客户端
func setupTemporalClient(logger *zap.Logger) client.Client {
	temporalAddr := os.Getenv("TEMPORAL_ADDRESS")
	if temporalAddr == "" {
		temporalAddr = "127.0.0.1:7233"
	}

	logger.Info("尝试连接Temporal服务", zap.String("address", temporalAddr))

	clientOptions := client.Options{
		HostPort: temporalAddr,
		Logger:   zapLog.NewTemporalLogger(logger),
	}

	c, err := client.Dial(clientOptions)
	if err != nil {
		logger.Warn("Temporal服务连接失败，工作流功能将被禁用", zap.Error(err))
		return nil
	}

	logger.Info("成功连接Temporal服务")
	setupTemporalHealthCheck(c, logger)
	setupTemporalCleanup(c, logger)

	return c
}

// 设置Temporal健康检查
func setupTemporalHealthCheck(c client.Client, logger *zap.Logger) {
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for range ticker.C {
			ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
			_, err := c.DescribeWorkflowExecution(ctx, "health-check", "")
			cancel()

			if err != nil && !isExpectedTemporalError(err) {
				logger.Warn("Temporal服务连接异常", zap.Error(err))
			}
		}
	}()
}

// 设置Temporal清理
func setupTemporalCleanup(c client.Client, logger *zap.Logger) {
	go func() {
		sigCh := make(chan os.Signal, 1)
		signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
		<-sigCh

		logger.Info("正在关闭Temporal客户端连接...")
		c.Close()
		logger.Info("Temporal客户端已关闭")
	}()
}

// 注册工单模块
func registerTicketModule(apiV1 *gin.RouterGroup, db *gorm.DB, logger *zap.Logger,
	temporalClient client.Client, config *configs.Config, jwtSecret string, userSvc userService.IUserService) {
	// 从依赖注入系统中获取用户服务
	userService := di.NewAPIController(db, jwtSecret).GetUserService()

	// 初始化工单模块
	ticketModule := ticket.NewModule(db, logger, temporalClient, userService, config, userSvc)
	if err := ticketModule.Initialize(); err != nil {
		logger.Error("初始化工单模块失败", zap.Error(err))
		return
	}

	if err := ticketModule.AutoMigrate(); err != nil {
		logger.Error("工单模块数据库迁移失败", zap.Error(err))
		return
	}

	ticketModule.RegisterRoutes(apiV1)

	// 获取故障工单服务实例
	faultTicketService := ticketModule.GetFaultTicketService()

	// 初始化客户API
	registerCustomerAPI(db, logger, jwtSecret, faultTicketService, config)
}

// 注册导出模块
func registerExportModule(apiV1 *gin.RouterGroup, db *gorm.DB, logger *zap.Logger) {
	exportModule := export.NewModule(db)
	if err := exportModule.Initialize(); err != nil {
		logger.Error("初始化导出模块失败", zap.Error(err))
		return
	}

	exportModule.RegisterRoutes(apiV1)
}

// 注册硬件运维模块
func registerHardwareMaintenance(apiV1 *gin.RouterGroup, db *gorm.DB, logger *zap.Logger, temporalClient client.Client) {
	hardwareMaintenanceModule := hardware_maintenance.NewModule(db, logger, temporalClient)
	if err := hardwareMaintenanceModule.Initialize(); err != nil {
		logger.Error("初始化导出模块失败", zap.Error(err))
		return
	}

	if err := hardwareMaintenanceModule.AutoMigrate(); err != nil {
		logger.Error("审计模块数据库迁移失败", zap.Error(err))
		return
	}
	hardwareMaintenanceModule.RegisterRoutes(apiV1)
	logger.Info("硬件运维模块初始化并迁移成功")
}

// 注册软件运维模块
func registerSoftwareMaintenance(apiV1 *gin.RouterGroup, db *gorm.DB, logger *zap.Logger, temporalClient client.Client) {
	software_maintenance.RegisterSoftwareMaintenance(apiV1, db, logger, temporalClient)
}

// 注册排班表模块
func registerScheduleModule(apiV1 *gin.RouterGroup, db *gorm.DB, config *configs.Config) {
	schedule.RegisterScheduleModule(apiV1, db, config)
}

// 注册客户API
func registerCustomerAPI(db *gorm.DB, logger *zap.Logger, jwtSecret string,
	faultTicketService ticketService.FaultTicketService, config *configs.Config) {
	// 读取飞书配置
	feishuWebhookURL := ""
	feishuSecret := ""
	detailUrlTemplate := ""

	if config != nil && config.Feishu != nil {
		feishuWebhookURL = config.Feishu.WebhookURL
		feishuSecret = config.Feishu.Secret
		detailUrlTemplate = config.Feishu.TicketDetailUrlTemplate
	}

	// 创建默认客户
	customers := make(model.CustomerMap)
	customers["cloud17"] = &model.Customer{
		ID:     "cloud17",
		Secret: jwtSecret,
		Name:   "默认客户",
	}

	// 创建模块配置
	apiConfig := customerapi.ModuleConfig{
		Customers:         customers,
		FeishuWebhookURL:  feishuWebhookURL,
		FeishuSecret:      feishuSecret,
		DetailURLTemplate: detailUrlTemplate,
	}

	// 创建客户工单服务
	customerTicketService := customerService.NewCustomerTicketService(
		faultTicketService,
		apiConfig.FeishuWebhookURL,
		apiConfig.FeishuSecret,
		apiConfig.DetailURLTemplate,
	)

	// 初始化客户API模块
	r := gin.New() // 为演示目的创建新引擎，实际应用应使用现有的
	customerapi.InitCustomerAPIWithLogging(r, customerTicketService, db, logger, apiConfig)
	logger.Info("客户API模块初始化完成")
}

// 注册Swagger路由
func registerSwaggerIfEnabled(r *gin.Engine, logger *zap.Logger) {
	enableSwagger := os.Getenv("ENABLE_SWAGGER")
	if gin.Mode() != gin.ReleaseMode || enableSwagger == "true" {
		r.GET("/swagger/*any", ginSwagger.WrapHandler(
			swaggerFiles.Handler,
			ginSwagger.DocExpansion("none"), // 默认全部折叠
		))
		logger.Info("已启用Swagger文档，访问路径: /swagger/index.html")
	}
}

// isExpectedTemporalError 判断是否为预期的Temporal错误
func isExpectedTemporalError(err error) bool {
	errMsg := err.Error()
	return errMsg == "workflow not found for ID: health-check" ||
		errMsg == "workflow execution not found" ||
		errMsg == "entity not found"
}

// 注册用户模块
func registerUserModule(apiV1 *gin.RouterGroup, db *gorm.DB, logger *zap.Logger, redisCache *cache.RedisClient) userService.IUserService {
	// 创建应用程序实例
	application := &app.App{
		DB:         db,
		Logger:     logger,
		RedisCache: redisCache,
	}

	// 注册用户模块
	userSvc := user.RegisterUserModule(apiV1, application)
	logger.Info("用户模块初始化并迁移成功")

	return userSvc
}
