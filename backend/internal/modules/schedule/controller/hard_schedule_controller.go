package controller

import (
	"backend/internal/modules/schedule/model"
	ser "backend/internal/modules/schedule/service"
	"backend/response"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/datatypes"
)

// HardScheduleController 硬件排班表控制器
type HardScheduleController struct {
	service ser.HardScheduleService
}

// 创建排班表控制器
func NewHardScheController(service ser.HardScheduleService) *HardScheduleController {
	return &HardScheduleController{service: service}
}

func (c *HardScheduleController) RegisterHardScheRoutes(r *gin.RouterGroup) {
	hardSchedule := r.Group("/hard-sche")
	{
		hardSchedule.POST("", c.Create)
		hardSchedule.GET("/:date", c.GetByMonth)
		hardSchedule.GET("", c.GetByDate)
		hardSchedule.PUT("", c.Update)
		hardSchedule.POST("/trigger", c.TriggerNotification)
	}
}

// Create 创建排班表
// @Summary 创建指定日期的排班信息
// @Description 根据路径中的日期（2006-02-01T19:00:00+08:00）白班夜班值班人创建排班记录
// @Tags 排班管理
// @Accept json
// @Produce json
// @Param date path string true "要创建的日期（示例：2023-01-01）"
// @Param schedule body model.HardSchedule true "排班信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /hard-sche [post]
func (c *HardScheduleController) Create(ctx *gin.Context) {
	//var schedule model.HardSchedule
	//
	//if err := ctx.ShouldBindJSON(&schedule); err != nil {
	//	response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
	//	return
	//}
	//if err := c.service.Create(ctx, &schedule); err != nil {
	//	response.Fail(ctx, http.StatusInternalServerError, "创建区域失败: "+err.Error())
	//	return
	//}
	//response.Success(ctx, gin.H{"date": schedule.Date}, "创建区域成功")
	var req struct {
		Date     string   `json:"date"`
		UserName []string `json:"username"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}
	usersJson, err := json.Marshal(req.UserName)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "序列化用户名失败: "+err.Error())
		return
	}
	date, err := time.Parse("2006-01-02T15:04:05Z07:00", req.Date)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "日期格式错误: "+err.Error())
		return
	}
	schedule := model.HardSchedule{
		Date:     date,
		UserName: datatypes.JSON(usersJson),
	}
	if err := c.service.Create(ctx, &schedule); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建失败: "+err.Error())
		return
	}
	response.Success(ctx, gin.H{"date": schedule.Date}, "创建成功")
}

// 获取当月所有的排班信息,传的是时间，问题在月的1号获取不到前一天的，
// GetByMonth 根据月份获取排班表
// @Summary 获取指定年月的排班记录
// @Description 根据年份和月份查询当月所有日期的排班信息
// @Tags 排班管理
// @Accept json
// @Produce json
// @Param date path string true "要查询的日期（示例：2006-02-01T19:00:00+08:00）"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /hard-sche/{date} [get]
func (c *HardScheduleController) GetByMonth(ctx *gin.Context) {
	//dateStr := ctx.Param("date")
	//parts := strings.Split(dateStr, "-")
	//year, err := strconv.Atoi(parts[0])
	//if err != nil {
	//	response.Fail(ctx, http.StatusBadRequest, "获取年份失败: "+err.Error())
	//	return
	//}
	//month, err := strconv.Atoi(parts[1])
	//if err != nil {
	//	response.Fail(ctx, http.StatusBadRequest, "获取月份失败: "+err.Error())
	//	return
	//}
	//// 计算该月的起始日期（当月1号）和结束日期（当月最后一天）
	//startDate, endDate, err := getHardMonthDate(year, month)
	//if err != nil {
	//	response.Fail(ctx, http.StatusBadRequest, "日期计算失败: "+err.Error())
	//	return
	//}
	//
	//// 调用服务层查询当月排班记录
	//schedules, err := c.service.GetByMonth(ctx, startDate, endDate)
	//if err != nil {
	//	response.Fail(ctx, http.StatusInternalServerError, "查询失败: "+err.Error())
	//	return
	//}
	//
	//responseData := map[string]interface{}{
	//	"list": schedules, // 将 schedules 放入 list 字段
	//}
	//// 返回成功响应
	//response.Success(ctx, responseData, "获取排班信息成功")
	dateStr := ctx.Param("date")
	parts := strings.Split(dateStr, "-")
	year, err := strconv.Atoi(parts[0])
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取年份失败: "+err.Error())
		return
	}
	month, err := strconv.Atoi(parts[1])
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取月份失败: "+err.Error())
		return
	}
	startDate, endDate, err := getHardMonthDate(year, month)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "日期计算失败: "+err.Error())
		return
	}
	schedules, err := c.service.GetByMonth(ctx, startDate, endDate)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "查询失败: "+err.Error())
		return
	}
	// 反序列化每一条
	var result []map[string]interface{}
	for _, s := range schedules {
		var users []string
		if err := json.Unmarshal(s.UserName, &users); err != nil {
			response.Fail(ctx, http.StatusInternalServerError, "解析用户数据失败: "+err.Error())
			return
		}
		result = append(result, map[string]interface{}{
			"date":     s.Date,
			"username": users,
		})
	}
	responseData := map[string]interface{}{
		"list": result,
	}
	response.Success(ctx, responseData, "获取排班信息成功")
}

// GetScheduleByDate 根据日期获取值班信息
// @Summary 获取指定日期的值班人信息
// @Description 根据日期（如：2006-02-01T19:00:00+08:00）查询排版信息
// @Tags 排班管理
// @Accept json
// @Produce json
// @Param date body string true "查询日期"
// @Success 200 {object} response.ResponseStruct{data=model.HardSchedule} "成功返回当天值班人信息（主/副值班人姓名）"
// @Failure 400 {object} response.ResponseStruct
// @Router /hard-sche [get]
func (c *HardScheduleController) GetByDate(ctx *gin.Context) {
	//var req struct {
	//	Date string `json:"date" binding:"required"`
	//}
	//if err := ctx.ShouldBindJSON(&req); err != nil {
	//	response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
	//	return
	//}
	//date, err := time.Parse("2006-01-02T15:04:05Z07:00", req.Date)
	//if err != nil {
	//	response.Fail(ctx, http.StatusBadRequest, "转化失败:"+err.Error())
	//}
	//schedule, err := c.service.GetByDate(ctx, date)
	//if err != nil {
	//	response.Fail(ctx, http.StatusInternalServerError, "数据查询失败: "+err.Error())
	//	return
	//}
	//response.Success(ctx, schedule, "获取值班人成功")
	var req struct {
		Date string `json:"date" binding:"required"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}
	date, err := time.Parse("2006-01-02T15:04:05Z07:00", req.Date)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "转化失败:"+err.Error())
		return
	}
	schedule, err := c.service.GetByDate(ctx, date)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "数据查询失败: "+err.Error())
		return
	}
	// 反序列化UserName
	var users []string
	if err := json.Unmarshal(schedule.UserName, &users); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "解析用户数据失败: "+err.Error())
		return
	}
	resp := map[string]interface{}{
		"date":     schedule.Date,
		"username": users,
	}
	response.Success(ctx, resp, "获取值班人成功")
}

func (c *HardScheduleController) Update(ctx *gin.Context) {
	//var schedule model.HardSchedule
	//if err := ctx.ShouldBindJSON(&schedule); err != nil {
	//	response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
	//	return
	//}
	//if err := c.service.Update(ctx, &schedule); err != nil {
	//	response.Fail(ctx, http.StatusInternalServerError, "更新排班表失败: "+err.Error())
	//	return
	//}
	//
	//response.Success(ctx, nil, "更新排班表成功")
	var req struct {
		Date     string   `json:"date"`
		UserName []string `json:"username"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}
	usersJson, err := json.Marshal(req.UserName)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "序列化用户名失败: "+err.Error())
		return
	}
	date, err := time.Parse("2006-01-02T15:04:05Z07:00", req.Date)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "日期格式错误: "+err.Error())
		return
	}
	schedule := model.HardSchedule{
		Date:     date,
		UserName: datatypes.JSON(usersJson),
	}
	if err := c.service.Update(ctx, &schedule); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新排班表失败: "+err.Error())
		return
	}
	response.Success(ctx, nil, "更新排班表成功")
}

// 获取月度排班开始时间和结束时间
func getHardMonthDate(year, month int) (start time.Time, end time.Time, err error) {
	// 起始日期：当月1号 00:00:00
	start = time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.UTC)
	// 结束日期：下个月1号的前一天 23:59:59
	end = start.AddDate(0, 1, 0).Add(time.Hour*23 + time.Minute*59 + time.Second*59)
	return start, end, nil
}

func (c *HardScheduleController) TriggerNotification(ctx *gin.Context) {
	hour := ctx.Query("hour")
	if hour == "" {
		fmt.Print("hour为空")
		return
	}
	hourInt, err := strconv.Atoi(hour)
	if err != nil {
		return
	}
	if hourInt < 0 || hourInt > 23 {
		return
	}
	err = c.service.TriggerNotification(ctx, hourInt)
	if err != nil {
		return
	}
}
