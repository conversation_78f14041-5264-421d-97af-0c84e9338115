package controller

import (
	"backend/internal/modules/schedule/model"
	ser "backend/internal/modules/schedule/service"
	"backend/response"
	"strings"

	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// SoftScheduleController 软件排班表控制器
type SoftScheduleController struct {
	service ser.SoftScheduleService
}

// 创建排班表控制器
func NewSoftScheController(service ser.SoftScheduleService) *SoftScheduleController {
	return &SoftScheduleController{service: service}
}

func (c *SoftScheduleController) RegisterSoftScheRoutes(r *gin.RouterGroup) {
	softSchedule := r.Group("/soft-sche")
	{
		softSchedule.POST("", c.CreateSchedule)
		softSchedule.GET("/:date", c.GetScheduleByMonth)     //根据年月得到值班人列表
		softSchedule.GET("/date/:date", c.GetScheduleByDate) //根据日期得到值班人
		softSchedule.PUT("/:date", c.UpdateSchedule)         //更新值班人

	}
}

// Update 更新排班表
// @Summary 更新指定日期的排班信息
// @Description 根据路径中的日期（YYYY-MM-DD）更新当天的排班记录（主值班人/副值班人信息）
// @Tags 排班管理
// @Accept json
// @Produce json
// @Param date path string true "要更新的日期（示例：2023-01-01）"
// @Param schedule body model.SoftSchedule true "排班信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /soft-sche/{date} [put]
func (c *SoftScheduleController) UpdateSchedule(ctx *gin.Context) {
	date := ctx.Param("date")
	var schedule model.SoftSchedule
	if err := ctx.ShouldBindJSON(&schedule); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}
	schedule.Date = date
	if err := c.service.Update(ctx, &schedule); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新排班表失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新排班表成功")
}

// CreateSchedule 创建排班表
// @Summary 创建指定日期的排班信息
// @Description 根据路径中的日期（YYYY-MM-DD）值班人（主值班人/副值班人）创建当天的排班记录
// @Tags 排班管理
// @Accept json
// @Produce json
// @Param date path string true "要创建的日期（示例：2023-01-01）"
// @Param schedule body model.SoftSchedule true "排班信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /soft-sche [post]
func (c *SoftScheduleController) CreateSchedule(ctx *gin.Context) {
	type Request struct {
		Date            string `json:"date"`
		PrimaryUsername string `json:"primary_username"`
		SecondUsername  string `json:"second_username"`
	}
	var req Request
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}
	schedule := model.SoftSchedule{
		Date:            req.Date,
		PrimaryUserName: req.PrimaryUsername,
		SecondUserName:  req.SecondUsername,
	}
	if err := c.service.Create(ctx, &schedule); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建区域失败: "+err.Error())
		return
	}
	response.Success(ctx, gin.H{"date": schedule.Date}, "创建区域成功")
}

// GetByMonth 根据月份获取排班表
// @Summary 获取指定年月的排班记录
// @Description 根据年份和月份查询当月所有日期的排班信息(主/副值班人)
// @Tags 排班管理
// @Accept json
// @Produce json
// @Param date path string true "要查询的日期（示例：2023-01-01）"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /soft-sche/{date} [get]
func (c *SoftScheduleController) GetScheduleByMonth(ctx *gin.Context) {
	dateStr := ctx.Param("date")
	parts := strings.Split(dateStr, "-")
	year, err := strconv.Atoi(parts[0])
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取年份失败: "+err.Error())
		return
	}
	month, err := strconv.Atoi(parts[1])
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取月份失败: "+err.Error())
		return
	}
	// 计算该月的起始日期（当月1号）和结束日期（当月最后一天）
	startDate, endDate, err := getSoftMonthDate(year, month)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "日期计算失败: "+err.Error())
		return
	}
	// 调用服务层查询当月排班记录
	schedules, err := c.service.GetByMonth(ctx, startDate, endDate)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "查询失败: "+err.Error())
		return
	}
	responseData := map[string]interface{}{
		"list": schedules, // 将 schedules 放入 list 字段
	}
	// 返回成功响应
	response.Success(ctx, responseData, "获取排班信息成功")
}

// GetScheduleByDate 根据日期获取主副值班人
// @Summary 获取指定日期的值班人信息
// @Description 根据日期（格式：YYYY-MM-DD）查询当天的主值班人和副值班人姓名
// @Tags 排班管理
// @Accept json
// @Produce json
// @Param date path string true "要查询的日期（示例：2023-01-01）"
// @Success 200 {object} response.ResponseStruct{data=model.SoftSchedule} "成功返回当天值班人信息（主/副值班人姓名）"
// @Failure 400 {object} response.ResponseStruct
// @Router /soft-sche/date/{date} [get]
func (c *SoftScheduleController) GetScheduleByDate(ctx *gin.Context) {
	date := ctx.Param("date")
	schedule, err := c.service.GetByDate(ctx, date)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取值班人失败: "+err.Error())
		return
	}
	response.Success(ctx, schedule, "获取值班人成功")
}

func getSoftMonthDate(year, month int) (start time.Time, end time.Time, err error) {
	// 起始日期：当月1号 00:00:00
	start = time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.UTC)
	// 结束日期：下个月1号的前一天 23:59:59
	nextMonth := start.AddDate(0, 1, 0) // 下个月1号
	end = nextMonth.AddDate(0, 0, -1).Add(time.Hour*23 + time.Minute*59 + time.Second*59)
	return start, end, nil
}
