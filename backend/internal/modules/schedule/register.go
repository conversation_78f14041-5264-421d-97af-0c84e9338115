package schedule

import (
	"backend/internal/common/utils/notifier"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"backend/configs"
	"backend/internal/middleware"
	scheduleController "backend/internal/modules/schedule/controller"
	scheduleRepository "backend/internal/modules/schedule/repository"
	scheduleService "backend/internal/modules/schedule/service"
	//排班表飞书通知
)

// ReisterScheduleModule 注册排班表模块
func RegisterScheduleModule(router *gin.RouterGroup, db *gorm.DB, config *configs.Config) {
	//初始化软件排班表模块
	bots := notifier.NewCustomSoftScheNotifier(config.Feishu)
	softwareScheRepo := scheduleRepository.NewSoftScheRepository(db)
	softwareScheSvc := scheduleService.NewSoftScheService(softwareScheRepo, bots)
	softwareScheControl := scheduleController.NewSoftScheController(softwareScheSvc)
	//初始化软件排班定时任务调度器
	softScheduler := NewSoftScheduler(softwareScheSvc)

	//初始化硬件排班表模块
	bots1 := notifier.NewCustomHardScheNotifier(config.Feishu)
	hardwareScheRepo := scheduleRepository.NewHardScheduleRepository(db)
	hardwareScheSvc := scheduleService.NewHardScheService(hardwareScheRepo, bots1)
	hardwareScheControl := scheduleController.NewHardScheController(hardwareScheSvc)
	//初始化硬件排班定时任务调度器
	hardScheduler := NewHardScheduler(hardwareScheSvc)

	if config.Mode == "production" {
		// 创建一个共享的信号通道
		sigCh := make(chan os.Signal, 1)
		signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

		// 启动软件排班调度器
		if config.Feishu.SoftScheEnabled {
			go func() {
				softScheduler.Start()
				log.Println("软件排班定时任务调度器已启动")
			}()
		} else {
			log.Println("软件排班表通知已禁用，跳过启动定时任务调度器")
		}

		// 启动硬件排班调度器
		if config.Feishu.HardScheEnabled {
			go func() {
				hardScheduler.Start()
				log.Println("硬件排班定时任务调度器已启动")
			}()
		} else {
			log.Println("硬件排班表通知已禁用，跳过启动定时任务调度器")
		}

		// 在单独的协程中处理信号
		go func() {
			<-sigCh
			// 收到信号后停止所有调度器
			if config.Feishu.SoftScheEnabled {
				softScheduler.Stop()
				log.Println("软件排班定时任务调度器已停止")
			}
			if config.Feishu.HardScheEnabled {
				hardScheduler.Stop()
				log.Println("硬件排班定时任务调度器已停止")
			}
		}()
	}

	apiGroup := router.Group("/schedule")
	apiGroup.Use(middleware.AuthMiddleware()) //添加认真中间件
	softwareScheControl.RegisterSoftScheRoutes(apiGroup)
	hardwareScheControl.RegisterHardScheRoutes(apiGroup)

}
