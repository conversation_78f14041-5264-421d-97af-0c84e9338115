package service

import (
	"backend/internal/common/utils/notifier"
	"context"
	"fmt"
	"time"

	"backend/internal/modules/schedule/model"
	repo "backend/internal/modules/schedule/repository"
)

// SoftScheduleService 排班表服务接口
type SoftScheduleService interface {
	Update(ctx context.Context, schedule *model.SoftSchedule) error
	GetByMonth(ctx context.Context, startDate, endDate time.Time) ([]model.SoftSchedule, error)
	GetByDate(ctx context.Context, targetDate string) (*model.SoftSchedule, error)
	Create(ctx context.Context, schedule *model.SoftSchedule) error
	SendSoftScheMsg(ctx context.Context, today string) error
}

// softScheduleService 排班表服务实现
type softScheduleService struct {
	repo             repo.SoftScheduleRepository
	softScheNotifier *notifier.SoftScheNotifier
}

// NewSoftScheService 创建排班表服务
func NewSoftScheService(repo repo.SoftScheduleRepository, softScheNotifier ...*notifier.SoftScheNotifier) SoftScheduleService {
	var notifier *notifier.SoftScheNotifier
	if len(softScheNotifier) > 0 {
		notifier = softScheNotifier[0]
	}
	return &softScheduleService{repo: repo, softScheNotifier: notifier}
}

// Update 更新排班表服务
func (s *softScheduleService) Update(ctx context.Context, schedule *model.SoftSchedule) error {
	return s.repo.UpdateSchedule(ctx, schedule)
}

// GetByMonth 通过Month获取排班表服务
func (s *softScheduleService) GetByMonth(ctx context.Context, startDate, endDate time.Time) ([]model.SoftSchedule, error) {
	return s.repo.GetScheduleByMonth(ctx, startDate, endDate)
}

// GetByDate 通过Date获取排班表服务
func (s *softScheduleService) GetByDate(ctx context.Context, targetDate string) (*model.SoftSchedule, error) {
	return s.repo.GetScheduleByDate(ctx, targetDate)
}

// 创建排班表
func (s *softScheduleService) Create(ctx context.Context, schedule *model.SoftSchedule) error {
	return s.repo.CreateSchedule(ctx, schedule)
}

// 发送入库信息通知
func (s *softScheduleService) SendSoftScheMsg(ctx context.Context, today string) error {
	sche, err := s.repo.GetScheduleByDate(ctx, today)
	if err != nil {
		return fmt.Errorf("获取飞书通知前置信息失败: %v", err)
	}
	// 检查notifier是否已初始化
	if s.softScheNotifier == nil {
		return fmt.Errorf("软件排班通知器未初始化")
	}
	date := sche.Date
	primary_name := sche.PrimaryUserName
	second_name := sche.SecondUserName
	err = s.softScheNotifier.SendSoftScheNotification(date, primary_name, second_name)
	if err != nil {
		return fmt.Errorf("发送飞书通知失败: %v", err)
	}
	return nil
}
