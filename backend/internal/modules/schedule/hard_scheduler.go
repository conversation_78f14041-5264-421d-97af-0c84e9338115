package schedule

import (
	"backend/internal/modules/schedule/service"
	"context"
	"fmt"
	"time"

	"github.com/robfig/cron"
)

type HardScheduler struct {
	cron            *cron.Cron
	scheduleService service.HardScheduleService
	isRunning       bool
}

func NewHardScheduler(scheduleService service.HardScheduleService) *HardScheduler {
	// 创建一个定时任务管理器（使用东八区时间）
	c := cron.NewWithLocation(time.FixedZone("CST", 8*60*60))

	return &HardScheduler{
		cron:            c,
		scheduleService: scheduleService,
		isRunning:       false,
	}
}

func (s *HardScheduler) Start() {
	if s.isRunning {
		fmt.Printf("硬件排班定时任务调度器已经在运行中，忽略此次启动请求\n")
		return
	}
	// 在添加任务前清空现有任务
	s.cron.Stop()
	s.cron = cron.NewWithLocation(time.FixedZone("CST", 8*60*60))

	// 每天上午9点执行
	err := s.cron.AddFunc("0 0 9 * * *", func() {
		s.sendHardScheduleNotification(9)
	})
	if err != nil {
		fmt.Printf("添加上午9点硬件排班通知定时任务失败: %v\n", err)
		return
	}

	// 每天晚上21点执行
	err = s.cron.AddFunc("0 0 21 * * *", func() {
		s.sendHardScheduleNotification(21)
	})
	if err != nil {
		fmt.Printf("添加晚上21点硬件排班通知定时任务失败: %v\n", err)
		return
	}

	fmt.Printf("启动硬件排班定时任务调度器\n")
	s.cron.Start()
	s.isRunning = true
}

func (s *HardScheduler) Stop() {
	if !s.isRunning {
		fmt.Printf("定时任务调度器已经停止，忽略此次停止请求\n")
		return
	}
	s.cron.Stop()
	s.isRunning = false
	fmt.Printf("停止定时任务调度器\n")
}

func (s *HardScheduler) sendHardScheduleNotification(hour int) {
	// 获取今天的日期，并设置为特定小时（9点或21点）
	now := time.Now()
	targetTime := time.Date(now.Year(), now.Month(), now.Day(), hour, 0, 0, 0, now.Location())
	fmt.Print(targetTime)

	// 用于防重复发送的key
	dedupeKey := fmt.Sprintf("%s-%d", now.Format("2006-01-02"), hour)

	// 检查是否在短时间内已经发送过通知
	SendMutex.Lock()
	lastSendTime, exists := SendRecords[dedupeKey]
	if exists && now.Sub(lastSendTime) < DedupeWindow {
		fmt.Printf("跳过重复发送硬件排班通知，上次发送时间: %v, 当前时间: %v\n",
			lastSendTime.Format("15:04:05"), now.Format("15:04:05"))
		SendMutex.Unlock()
		return
	}
	// 记录本次发送时间
	SendRecords[dedupeKey] = now
	SendMutex.Unlock()
	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	// 调用服务发送通知，传入目标时间点
	err := s.scheduleService.SendHardScheMsg(ctx, targetTime)
	if err != nil {
		fmt.Printf("发送%d点硬件排班通知失败: %v\n", hour, err)
	} else {
		fmt.Printf("成功发送%d点硬件排班通知\n", hour)
	}
}
