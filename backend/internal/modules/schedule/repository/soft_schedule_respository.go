package repository

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"

	"backend/internal/modules/schedule/model"
)

// SoftScheduleRepository 排班表仓库接口
type SoftScheduleRepository interface {
	UpdateSchedule(ctx context.Context, schedule *model.SoftSchedule) error
	GetScheduleByMonth(ctx context.Context, startDate, endDate time.Time) ([]model.SoftSchedule, error)
	GetScheduleByDate(ctx context.Context, targetDate string) (*model.SoftSchedule, error)
	CreateSchedule(ctx context.Context, schedule *model.SoftSchedule) error
}

// scheduleRepository 排班表仓库实现
type softScheduleRepository struct {
	db *gorm.DB
}

// NewSoftScheRepository 创建排班表仓库
func NewSoftScheRepository(db *gorm.DB) SoftScheduleRepository {
	return &softScheduleRepository{db: db}
}

// UpdateSchedule 更新排班表
func (r *softScheduleRepository) UpdateSchedule(ctx context.Context, schedule *model.SoftSchedule) error {
	return r.db.WithContext(ctx).Model(&model.SoftSchedule{}).
		Where("date = ?", schedule.Date).
		Select("primary_username", "second_username").
		Updates(map[string]interface{}{
			"primary_username": schedule.PrimaryUserName,
			"second_username":  schedule.SecondUserName,
		}).Error
}

// GetScheduleByMonth 通过Month获取主副值班列表
func (r *softScheduleRepository) GetScheduleByMonth(ctx context.Context, startDate, endDate time.Time) ([]model.SoftSchedule, error) {
	// 将时间转换为字符串格式，用于查询
	startDateStr := startDate.Format("2006-01-02")
	endDateStr := endDate.Format("2006-01-02")

	var schedules []model.SoftSchedule
	err := r.db.WithContext(ctx).
		Select("id,date,primary_username,second_username").
		Where("date >= ? AND date <= ?", startDateStr, endDateStr).
		Order("date ASC"). // 按日期升序排列
		Find(&schedules).Error
	return schedules, err
}

// GetScheduleByDate  通过Date获取主副值班人
func (r *softScheduleRepository) GetScheduleByDate(ctx context.Context, targetDate string) (*model.SoftSchedule, error) {
	var schedule model.SoftSchedule
	if err := r.db.WithContext(ctx).
		Model(&model.SoftSchedule{}). //Select("primary_username", "second_username").
		Where("date = ?", targetDate).First(&schedule).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("值班人不存在")
		}
		return nil, err
	}
	return &schedule, nil
}

// CreateSchedule 创建排班表
func (r *softScheduleRepository) CreateSchedule(ctx context.Context, schedule *model.SoftSchedule) error {
	return r.db.WithContext(ctx).Create(schedule).Error
}
