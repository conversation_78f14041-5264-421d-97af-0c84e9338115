package controller

import (
	"backend/internal/modules/purchase_old/model"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"backend/internal/modules/purchase_old/service"
	"backend/response"
)

// RegisterRoutes 注册路由
func (c *PurchaseController) RegisterRoutes(router *gin.RouterGroup) {
	// api/v1/purchase_old/orders
	ordersGroup := router.Group("/orders")
	{
		// 创建采购订单，通过用户手动输入创建
		ordersGroup.POST("", c.CreatePurchaseOrderByInput)
		ordersGroup.POST("/import", c.CreatePurchaseOrderByFile)
		// 获取采购订单列表
		ordersGroup.GET("", c.ListPurchaseOrders)
		// 根据ID获取采购订单
		ordersGroup.GET(":id", c.GetPurchaseOrderByID)
		// 根据PO编号获取采购订单
		ordersGroup.GET("po/:poNumber", c.GetPurchaseOrderByPONumber)
	}

}

// PurchaseController 采购订单控制器
type PurchaseController struct {
	service service.PurchaseService
}

// NewPurchaseController 创建采购订单控制器实例
func NewPurchaseController(service service.PurchaseService) *PurchaseController {
	return &PurchaseController{service: service}
}

// CreatePurchaseOrderByInput 创建采购订单
// @Summary 创建采购订单
// @Description 创建新的采购订单及详情
// @Tags 采购管理
// @Accept json
// @Produce json
// @Param order body service.CreatePurchaseOrderDTO true "采购订单信息"
// @Success 201 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase_old/orders [post]
func (c *PurchaseController) CreatePurchaseOrderByInput(ctx *gin.Context) {
	var dto service.CreatePurchaseOrderDTO
	if err := ctx.ShouldBindJSON(&dto); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求数据")
		return
	}

	//order, err := c.service.CreatePurchaseOrderByInput(ctx, dto)
	_, err := c.service.CreatePurchaseOrderByInput(ctx, dto)
	if err != nil {
		if err == service.ErrInvalidPurchaseData {
			response.Fail(ctx, http.StatusBadRequest, "无效的采购订单数据")
			return
		}
		if err == service.ErrDuplicatePONumber {
			response.Fail(ctx, http.StatusBadRequest, "采购订单编号已存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "创建采购订单失败"+err.Error())
		return
	}

	//response.Success(ctx, order, "创建采购订单成功")
	response.Success(ctx, nil, "创建采购订单成功")
}

func (c *PurchaseController) CreatePurchaseOrderByFile(ctx *gin.Context) {
	var ImportForm model.ImportForm
	err := ctx.ShouldBind(&ImportForm)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "导入采购入库结构信息失败："+err.Error())
		return
	}
	_, err = c.service.CreatePurchaseOrderByFile(ctx, ImportForm)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建采购入库单失败"+err.Error())
		return
	}
	response.Success(ctx, nil, "创建成功")
}

// GetPurchaseOrderByID 根据ID获取采购订单
// @Summary 获取采购订单详情
// @Description 通过订单ID查询采购订单详情
// @Tags 采购管理
// @Accept json
// @Produce json
// @Param id path int true "采购订单ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购订单不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase_old/orders/{id} [get]
func (c *PurchaseController) GetPurchaseOrderByID(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	order, err := c.service.GetPurchaseOrderByID(ctx, uint(id))
	if err != nil {
		if err == service.ErrPurchaseOrderNotFound {
			response.Fail(ctx, http.StatusNotFound, "采购订单不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "获取采购订单失败")
		return
	}

	response.Success(ctx, order, "获取采购订单成功")
}

// GetPurchaseOrderByPONumber 根据PO编号获取采购订单
// @Summary 通过PO编号查询采购订单
// @Description 通过采购订单编号（PO Number）查询订单详情
// @Tags 采购管理
// @Accept json
// @Produce json
// @Param poNumber path string true "采购订单编号"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购订单不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase_old/orders/po/{poNumber} [get]
func (c *PurchaseController) GetPurchaseOrderByPONumber(ctx *gin.Context) {
	poNumber := ctx.Param("poNumber")
	if poNumber == "" {
		response.Fail(ctx, http.StatusBadRequest, "采购订单编号不可为空")
		return
	}

	order, err := c.service.GetPurchaseOrderByPurchaseOrderNo(ctx, poNumber)
	if err != nil {
		if err == service.ErrPurchaseOrderNotFound {
			response.Fail(ctx, http.StatusNotFound, "采购订单不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "获取采购订单失败")
		return
	}

	response.Success(ctx, order, "获取采购订单成功")
}

// ListPurchaseOrders 获取采购订单列表
// @Summary 查询采购订单列表
// @Description 支持分页和筛选条件（订单编号、供应商名称、订单日期范围）
// @Tags 采购管理
// @Accept json
// @Produce json
// @Param po_number query string false "采购订单编号（模糊查询）"
// @Param supplier_name query string false "供应商名称（模糊查询）"
// @Param order_date_start query string false "订单日期起始（格式：YYYY-MM-DD）"
// @Param order_date_end query string false "订单日期结束（格式：YYYY-MM-DD）"
// @Param page query int true "页码（从1开始）"
// @Param pageSize query int true "每页数量（最大100）"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase_old/orders [get]
func (c *PurchaseController) ListPurchaseOrders(ctx *gin.Context) {
	var query service.PurchaseOrderListQuery
	if err := ctx.ShouldBindQuery(&query); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的查询参数")
		return
	}

	result, err := c.service.ListPurchaseOrders(ctx, query)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取采购订单列表失败")
		return
	}

	response.Success(ctx, result, "获取采购订单列表成功")
}
