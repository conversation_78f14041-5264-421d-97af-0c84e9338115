package service

import (
	"context"
	"errors"
	"time"

	"backend/internal/modules/purchase_old/model"
)

var (
	// ErrPurchaseOrderNotFound 采购订单不存在
	ErrPurchaseOrderNotFound = errors.New("采购订单不存在")
	// ErrInvalidPurchaseData 无效的采购数据
	ErrInvalidPurchaseData = errors.New("无效的采购数据")
	// ErrDuplicatePONumber 采购订单编号已存在
	ErrDuplicatePONumber = errors.New("采购订单编号已存在")
)

// CreatePurchaseDetailDTO 创建采购详情DTO
type CreatePurchaseDetailDTO struct {
	PN              string  `json:"pn" binding:"required"`
	Amount          uint    `json:"amount" binding:"required,min=1"`
	Price           float64 `json:"price" binding:"required,min=0"`
	MaterialType    string  `json:"material_type" binding:"required"`
	Brand           string  `json:"brand" binding:"required"`
	ProductModel    string  `json:"product_model" binding:"required"`
	ProductCategory string  `json:"product_category" binding:"required"`
}

// CreatePurchaseOrderDTO 创建采购订单DTO
type CreatePurchaseOrderDTO struct {
	PurchaseTitle   string                  `json:"purchase_title" form:"purchase_title" binding:"required"`
	PurchaseOrderNo string                  `json:"purchase_order_no" form:"purchase_order_no" binding:"required"`
	SupplierName    string                  `json:"supplier_name" form:"supplier_name" binding:"required"`
	PurchaseType    string                  `json:"purchase_type" form:"purchase_type"`
	OrderDate       *time.Time              `json:"order_date" form:"order_date"`
	Details         []model.PurchaseDetails `json:"details" form:"details" binding:"required,min=1"`
}

// PurchaseOrderListQuery 采购订单查询参数
type PurchaseOrderListQuery struct {
	PurchaseOrderNo string `json:"purchase_order_no"`
	SupplierName    string `form:"supplier_name"`
	OrderDateStart  string `form:"order_date_start"`
	OrderDateEnd    string `form:"order_date_end"`
	Page            int    `form:"page" binding:"required,min=1"`
	PageSize        int    `form:"pageSize" binding:"required,min=1,max=1000"`
}

// PurchaseOrderListResult 采购订单列表结果
type PurchaseOrderListResult struct {
	Total int64                  `json:"total"`
	List  []*model.PurchaseOrder `json:"list"`
}

// PurchaseService 采购服务接口
type PurchaseService interface {
	// CreatePurchaseOrderByInput 通过用户手动创建采购订单
	CreatePurchaseOrderByInput(ctx context.Context, dto CreatePurchaseOrderDTO) (*model.PurchaseOrder, error)
	CreatePurchaseOrderByFile(ctx context.Context, form model.ImportForm) (*model.PurchaseOrder, error)

	// GetPurchaseOrderByID 根据ID获取采购订单
	GetPurchaseOrderByID(ctx context.Context, id uint) (*model.PurchaseOrder, error)

	// GetPurchaseOrderByPurchaseOrderNo 根据PO编号获取采购订单
	GetPurchaseOrderByPurchaseOrderNo(ctx context.Context, poNumber string) (*model.PurchaseOrder, error)

	// ListPurchaseOrders 获取采购订单列表
	ListPurchaseOrders(ctx context.Context, query PurchaseOrderListQuery) (*PurchaseOrderListResult, error)

	// StartNewInboundWorkflow 启动新购入库工作流
	StartNewInboundWorkflow(ctx context.Context) error

	// 读取采购配件入库Excel文件
	ReadPurchasePartInboundExcel(filePath string) ([]model.PurchaseDetails, error)
}
