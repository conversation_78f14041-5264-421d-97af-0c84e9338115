package model

import (
	"backend/internal/modules/cmdb/model/product"
	"backend/internal/modules/cmdb/model/template"
	"time"

	"gorm.io/gorm"
)

// 合同表
type PurchaseOrder struct {
	gorm.Model
	PurchaseTitle   string `gorm:"type:varchar(255);comment:采购标题" json:"purchase_title"`
	PurchaseOrderNo string `gorm:"column:purchase_order_no;index;comment:采购合同号 " json:"purchase_order_no"`
	PurchaseType    string `gorm:"type:varchar(20);comment:采购设备类型" json:"purchase_type" example:"part | device" `
	//NewInbound      []inbound.NewInbound    `gorm:"comment:关联配件入库单" json:"new_inbound"` //一个采购合同可以对应多个配件入库单
	//DeviceInbound   []inbound.DeviceInbound `gorm:"comment:关联设备入库单" json:"device_inbound"`
	//PurchaseDetails []PurchaseDetails       `gorm:"comment:关联合同详情" json:"purchase_details"`    // 入库合同详情
	SupplierID   uint   `gorm:"column:supplier_id" json:"supplier_id"`     // 供应商ID
	SupplierName string `gorm:"column:supplier_name" json:"supplier_name"` // 供应商名称
	//Supplier        Supplier             `gorm:"foreignKey:SupplierID" json:"supplier"`        // 关联供应商
	OrderDate   *time.Time `gorm:"column:order_date" json:"order_date"`     // 下单时间
	TotalAmount float64    `gorm:"column:total_amount" json:"total_amount"` // 总金额
}

func (PurchaseOrder) TableName() string { return "purchase_order" }

// 合同详情
type PurchaseDetails struct {
	gorm.Model
	PurchaseOrderID uint                      `gorm:"comment:关联合同表" json:"purchase_order_id" form:"purchase_order_id"`
	ProductID       uint                      `gorm:"column:product_id;comment:相关规格信息;default:null;" json:"product_id,omitempty" form:"product_id,omitempty"`
	Product         *product.Product          `gorm:"foreignkey:ProductID" json:"product" form:"product"`
	TemplateID      uint                      `gorm:"column:template_id;default:null" json:"template_id,omitempty" form:"template_id,omitempty,omitempty"`
	Template        *template.MachineTemplate `gorm:"foreignkey:TemplateID" json:"template" form:"template"`
	Amount          uint                      `gorm:"comment:数量" json:"amount" form:"amound" binding:"required"`
	Price           float64                   `gorm:"comment:单件价格" json:"price" form:"price" `
}

func (PurchaseDetails) TableName() string { return "purchase_details" }
