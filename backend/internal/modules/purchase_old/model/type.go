package model

import (
	"mime/multipart"
	"time"
)

type ImportForm struct {
	PurchaseTitle   string                `json:"purchase_title" form:"purchase_title" binding:"required"`
	PurchaseOrderNo string                `json:"purchase_order_no" form:"purchase_order_no" binding:"required"`
	SupplierName    string                `json:"supplier_name" form:"supplier_name" binding:"required"`
	OrderDate       *time.Time            `json:"order_date" form:"order_date"`
	File            *multipart.FileHeader `form:"file" binding:"required"`
}
