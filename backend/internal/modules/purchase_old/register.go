package purchase_old

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"backend/internal/middleware"
	"backend/internal/modules/purchase_old/controller"
	"backend/internal/modules/purchase_old/repository"
	"backend/internal/modules/purchase_old/service"
)

// RegisterPurchaseModule 注册采购模块
func RegisterPurchaseModule(router *gin.RouterGroup, db *gorm.DB) {
	// 初始化仓库
	purchaseRepo := repository.NewPurchaseRepository(db)

	// 初始化服务层
	purchaseSvc := service.NewPurchaseService(purchaseRepo)

	// 初始化控制层
	purchaseCtrl := controller.NewPurchaseController(purchaseSvc)

	// 路由 api/v1/purchase_old
	apiGroup := router.Group("/purchase_old")
	apiGroup.Use(middleware.AuthMiddleware()) // 添加认证中间件
	// 注册路由
	purchaseCtrl.RegisterRoutes(apiGroup)
}
