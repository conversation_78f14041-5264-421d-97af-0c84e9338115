package system

import (
	"backend/internal/infrastructure/app"
	"backend/internal/infrastructure/cache"
	"backend/internal/infrastructure/casbin"
	"backend/internal/modules/system/controller"
	"backend/internal/modules/system/model"
	"backend/internal/modules/system/repository"
	"backend/internal/modules/system/service"
	userService "backend/internal/modules/user/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// RegisterSystemModule 注册系统模块
func RegisterSystemModule(router *gin.RouterGroup, app *app.App, userSvc userService.IUserService, casbinService *casbin.CasbinService) {
	// 创建系统模块实例
	systemModule := NewModule(app.DB, app.Logger, app.RedisCache, userSvc, casbinService)

	// 初始化模块
	if err := systemModule.Initialize(); err != nil {
		app.Logger.Error("初始化系统模块失败", zap.Error(err))
		return
	}

	// 执行数据库迁移
	if err := systemModule.AutoMigrate(); err != nil {
		app.Logger.Error("系统模块数据库迁移失败", zap.Error(err))
		return
	}

	// 注册路由
	systemModule.RegisterRoutes(router)
}

// Module 系统模块
type Module struct {
	db            *gorm.DB
	logger        *zap.Logger
	redisCache    *cache.RedisClient
	userService   userService.IUserService
	casbinService *casbin.CasbinService

	// 控制器
	menuController *controller.MenuController
	roleController *controller.RoleController
}

// NewModule 创建系统模块
func NewModule(db *gorm.DB, logger *zap.Logger, redisCache *cache.RedisClient, userSvc userService.IUserService, casbinService *casbin.CasbinService) *Module {
	return &Module{
		db:            db,
		logger:        logger,
		redisCache:    redisCache,
		userService:   userSvc,
		casbinService: casbinService,
	}
}

// SystemModuleInjector 系统模块依赖注入器
type SystemModuleInjector struct {
	DB            *gorm.DB
	Logger        *zap.Logger
	RedisCache    *cache.RedisClient
	UserService   userService.IUserService
	CasbinService *casbin.CasbinService
}

// NewSystemModuleInjector 创建系统模块依赖注入器
func NewSystemModuleInjector(db *gorm.DB, logger *zap.Logger, redisCache *cache.RedisClient, userSvc userService.IUserService, casbinService *casbin.CasbinService) *SystemModuleInjector {
	return &SystemModuleInjector{
		DB:            db,
		Logger:        logger,
		RedisCache:    redisCache,
		UserService:   userSvc,
		CasbinService: casbinService,
	}
}

// InjectMenuController 注入菜单控制器
func (i *SystemModuleInjector) InjectMenuController() *controller.MenuController {
	// 创建仓储层
	menuRepo := repository.NewMenuRepository(i.DB)

	// 创建角色仓储
	roleRepo := repository.NewRoleRepository(i.DB)

	// 创建服务层
	menuService := service.NewMenuService(menuRepo, i.RedisCache)
	roleService := service.NewRoleService(roleRepo, menuRepo, i.RedisCache)

	// 设置用户服务
	if i.UserService != nil {
		roleService.SetUserService(i.UserService)
	}

	// 创建控制器
	return controller.NewMenuController(menuService, roleService)
}

// InjectRoleController 注入角色控制器
func (i *SystemModuleInjector) InjectRoleController() *controller.RoleController {
	// 创建仓储层
	roleRepo := repository.NewRoleRepository(i.DB)
	menuRepo := repository.NewMenuRepository(i.DB)

	// 创建服务层
	roleService := service.NewRoleService(roleRepo, menuRepo, i.RedisCache)

	// 设置用户服务
	if i.UserService != nil {
		roleService.SetUserService(i.UserService)
	}

	// 创建控制器
	return controller.NewRoleController(roleService, i.CasbinService, i.Logger)
}

// Initialize 初始化模块
func (m *Module) Initialize() error {
	// 使用依赖注入器初始化控制器
	injector := NewSystemModuleInjector(m.db, m.logger, m.redisCache, m.userService, m.casbinService)

	// 注入控制器
	m.menuController = injector.InjectMenuController()
	m.roleController = injector.InjectRoleController()

	return nil
}

// AutoMigrate 自动迁移数据库
func (m *Module) AutoMigrate() error {
	// 执行菜单表的迁移
	if err := m.db.AutoMigrate(&model.Menu{}); err != nil {
		return err
	}

	// 执行角色表的迁移
	if err := m.db.AutoMigrate(&model.Role{}, &model.RolePermission{}); err != nil {
		return err
	}

	return nil
}

// RegisterRoutes 注册路由
func (m *Module) RegisterRoutes(router *gin.RouterGroup) {
	// 系统管理路由组
	systemRouter := router.Group("/system")

	// 使用JWT认证中间件
	// 已在全局添加认证中间件，此处可以移除
	// systemRouter.Use(middleware.AuthMiddleware())

	// 部分需要权限控制的路由使用Casbin中间件
	// 菜单路由
	menuRouter := systemRouter.Group("/menu")
	{
		// 获取菜单列表等操作，通常需要权限验证
		menuRouter.GET("/list", m.menuController.ListMenus)

		// 一般查询操作，不需要严格权限控制
		menuRouter.GET("/all", m.menuController.GetAllMenus)
		menuRouter.GET("/dashboard", m.menuController.GetDashboardMenus)
		menuRouter.GET("/:id", m.menuController.GetMenuByID)

		// 修改操作需要权限验证，已在全局添加权限验证中间件，此处可以移除
		menuRouter.POST("", m.menuController.CreateMenu)
		menuRouter.PUT("", m.menuController.UpdateMenu)
		menuRouter.DELETE("/:id", m.menuController.DeleteMenu)
	}

	// 角色路由
	roleRouter := systemRouter.Group("/role")
	{
		// 角色查询通常需要权限验证，已在全局添加权限验证中间件，此处可以移除
		roleRouter.GET("/list", m.roleController.ListRoles)
		roleRouter.GET("/:id", m.roleController.GetRoleByID)

		// 角色修改一定需要权限验证，已在全局添加权限验证中间件，此处可以移除
		roleRouter.POST("", m.roleController.CreateRole)
		roleRouter.PUT("", m.roleController.UpdateRole)
		roleRouter.DELETE("/:id", m.roleController.DeleteRole)

		// 获取用户菜单不需要严格权限控制
		roleRouter.GET("/menus", m.roleController.GetUserMenus)
	}
}
