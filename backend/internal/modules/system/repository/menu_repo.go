package repository

import (
	"backend/internal/modules/system/model"
	"context"

	"gorm.io/gorm"
)

// IMenuRepository 菜单存储接口
type IMenuRepository interface {
	GetAllMenus(ctx context.Context) ([]model.Menu, error)
	GetMenuByID(ctx context.Context, id uint) (*model.Menu, error)
	CreateMenu(ctx context.Context, menu *model.Menu) error
	UpdateMenu(ctx context.Context, menu *model.Menu) error
	DeleteMenu(ctx context.Context, id uint) error
	GetChildMenus(ctx context.Context, parentID uint) ([]model.Menu, error)
	GetAllMenusWithoutStatusFilter(ctx context.Context) ([]model.Menu, error)
	GetDB() *gorm.DB
}

// MenuRepository 菜单存储实现
type MenuRepository struct {
	db *gorm.DB
}

// NewMenuRepository 创建菜单存储实例
func NewMenuRepository(db *gorm.DB) IMenuRepository {
	return &MenuRepository{
		db: db,
	}
}

// GetAllMenus 获取所有菜单
func (r *MenuRepository) GetAllMenus(ctx context.Context) ([]model.Menu, error) {
	var menus []model.Menu
	if err := r.db.WithContext(ctx).Where("status = ?", true).Order("`order` asc").Find(&menus).Error; err != nil {
		return nil, err
	}
	return menus, nil
}

// GetMenuByID 根据ID获取菜单
func (r *MenuRepository) GetMenuByID(ctx context.Context, id uint) (*model.Menu, error) {
	var menu model.Menu
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&menu).Error; err != nil {
		return nil, err
	}
	return &menu, nil
}

// CreateMenu 创建菜单
func (r *MenuRepository) CreateMenu(ctx context.Context, menu *model.Menu) error {
	return r.db.WithContext(ctx).Create(menu).Error
}

// UpdateMenu 更新菜单
func (r *MenuRepository) UpdateMenu(ctx context.Context, menu *model.Menu) error {
	// 先获取原始记录
	existingMenu := &model.Menu{}
	if err := r.db.WithContext(ctx).Where("id = ?", menu.ID).First(existingMenu).Error; err != nil {
		return err
	}

	// 手动更新字段，只更新非空字段
	updates := make(map[string]interface{})

	// 基本字段更新
	if menu.Name != "" {
		updates["name"] = menu.Name
	}
	if menu.Path != "" {
		updates["path"] = menu.Path
	}
	if menu.Component != "" || menu.Component == "" { // 允许清空组件
		updates["component"] = menu.Component
	}
	if menu.Redirect != "" || menu.Redirect == "" { // 允许清空重定向
		updates["redirect"] = menu.Redirect
	}
	if menu.ParentID > 0 { // ParentID=0是顶级菜单，也是有效值
		updates["parent_id"] = menu.ParentID
	}
	if menu.Type != "" {
		updates["type"] = menu.Type
	}

	// status需要特殊处理，因为它是布尔类型
	updates["status"] = menu.Status

	// 处理Order字段，如果明确设置了值就更新
	if menu.Order != existingMenu.Order {
		updates["order"] = menu.Order
	}

	// 处理AuthCode，允许设置为空
	updates["auth_code"] = menu.AuthCode

	// Meta字段需要特殊处理
	// 标题是必须的
	if menu.Meta.Title != "" {
		updates["meta_title"] = menu.Meta.Title
	}
	// 其他Meta字段
	updates["meta_icon"] = menu.Meta.Icon
	updates["meta_active_icon"] = menu.Meta.ActiveIcon
	updates["meta_active_path"] = menu.Meta.ActivePath
	updates["meta_affix_tab"] = menu.Meta.AffixTab
	updates["meta_affix_tab_order"] = menu.Meta.AffixTabOrder
	updates["meta_badge"] = menu.Meta.Badge
	updates["meta_badge_type"] = menu.Meta.BadgeType
	updates["meta_badge_variants"] = menu.Meta.BadgeVariants
	updates["meta_hide_children_in_menu"] = menu.Meta.HideChildrenInMenu
	updates["meta_hide_in_breadcrumb"] = menu.Meta.HideInBreadcrumb
	updates["meta_hide_in_menu"] = menu.Meta.HideInMenu
	updates["meta_hide_in_tab"] = menu.Meta.HideInTab
	updates["meta_iframe_src"] = menu.Meta.IframeSrc
	updates["meta_ignore_access"] = menu.Meta.IgnoreAccess
	updates["meta_keep_alive"] = menu.Meta.KeepAlive
	updates["meta_link"] = menu.Meta.Link
	updates["meta_menu_visible_with_forbidden"] = menu.Meta.MenuVisibleWithForbidden
	updates["meta_no_basic_layout"] = menu.Meta.NoBasicLayout
	updates["meta_open_in_new_window"] = menu.Meta.OpenInNewWindow
	updates["meta_order"] = menu.Meta.Order

	// 执行更新
	return r.db.WithContext(ctx).Model(&model.Menu{}).Where("id = ?", menu.ID).Updates(updates).Error
}

// DeleteMenu 删除菜单
func (r *MenuRepository) DeleteMenu(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.Menu{}, id).Error
}

// GetChildMenus 获取子菜单
func (r *MenuRepository) GetChildMenus(ctx context.Context, parentID uint) ([]model.Menu, error) {
	var menus []model.Menu
	if err := r.db.WithContext(ctx).Where("parent_id = ? AND status = ?", parentID, true).Order("`order` asc").Find(&menus).Error; err != nil {
		return nil, err
	}
	return menus, nil
}

// GetAllMenusWithoutStatusFilter 获取所有菜单(不过滤状态)
func (r *MenuRepository) GetAllMenusWithoutStatusFilter(ctx context.Context) ([]model.Menu, error) {
	var menus []model.Menu
	if err := r.db.WithContext(ctx).Order("`order` asc").Find(&menus).Error; err != nil {
		return nil, err
	}
	return menus, nil
}

// GetDB 获取数据库连接
func (r *MenuRepository) GetDB() *gorm.DB {
	return r.db
}
