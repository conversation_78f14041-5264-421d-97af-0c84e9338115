package controller

import (
	"backend/internal/infrastructure/casbin"
	"backend/internal/modules/system/model"
	"backend/internal/modules/system/service"
	"backend/response"
	"context"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// RoleController 角色控制器
type RoleController struct {
	roleService   service.IRoleService
	casbinService *casbin.CasbinService
	logger        *zap.Logger
}

// NewRoleController 创建角色控制器
func NewRoleController(s service.IRoleService, cs *casbin.CasbinService, logger *zap.Logger) *RoleController {
	return &RoleController{
		roleService:   s,
		casbinService: cs,
		logger:        logger,
	}
}

// ListRoles godoc
// @Summary 获取角色列表
// @Description 获取角色列表
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param page query int false "页码"
// @Param size query int false "每页数量"
// @Param name query string false "角色名称"
// @Success 200 {object} model.RoleListResponse "获取角色列表成功"
// @Failure 500 {string} string "获取角色列表失败"
// @Router /system/role/list [get]
func (ctrl *RoleController) ListRoles(c *gin.Context) {
	// 获取查询参数
	pageStr := c.DefaultQuery("page", "1")
	sizeStr := c.DefaultQuery("size", "10")
	name := c.Query("name")

	// 转换参数
	page, err := strconv.Atoi(pageStr)
	if err != nil {
		response.Response(c, http.StatusBadRequest, nil, "页码参数错误")
		return
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil {
		response.Response(c, http.StatusBadRequest, nil, "每页数量参数错误")
		return
	}

	// 获取角色列表
	roles, err := ctrl.roleService.ListRoles(c.Request.Context(), page, size, name)
	if err != nil {
		response.Response(c, http.StatusInternalServerError, nil, "获取角色列表失败: "+err.Error())
		return
	}

	response.Success(c, roles, "获取角色列表成功")
}

// GetRoleByID godoc
// @Summary 根据ID获取角色
// @Description 根据ID获取角色
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path string true "角色ID"
// @Success 200 {object} model.RoleResponse "获取角色成功"
// @Failure 400 {string} string "请求参数错误"
// @Failure 500 {string} string "获取角色失败"
// @Router /system/role/{id} [get]
func (ctrl *RoleController) GetRoleByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		response.Response(c, http.StatusBadRequest, nil, "角色ID不能为空")
		return
	}

	role, err := ctrl.roleService.GetRoleByID(c.Request.Context(), id)
	if err != nil {
		response.Response(c, http.StatusInternalServerError, nil, "获取角色失败: "+err.Error())
		return
	}

	response.Success(c, role, "获取角色成功")
}

// CreateRole godoc
// @Summary 创建角色
// @Description 创建角色
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param role body model.RoleCreateRequest true "角色信息"
// @Success 200 {string} string "创建角色成功"
// @Failure 400 {string} string "请求参数错误"
// @Failure 500 {string} string "创建角色失败"
// @Router /system/role [post]
func (ctrl *RoleController) CreateRole(c *gin.Context) {
	var req model.RoleCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Response(c, http.StatusBadRequest, nil, "请求参数错误: "+err.Error())
		return
	}

	roleID, err := ctrl.roleService.CreateRole(c.Request.Context(), &req)
	if err != nil {
		response.Response(c, http.StatusInternalServerError, nil, "创建角色失败: "+err.Error())
		return
	}

	// 如果创建角色时指定了权限，同步到Casbin
	if len(req.Permissions) > 0 {
		if err := ctrl.syncRolePermissionsToCasbin(c.Request.Context(), roleID, req.Permissions); err != nil {
			ctrl.logger.Error("同步角色权限到Casbin失败",
				zap.Error(err),
				zap.String("roleID", roleID))
		}
	}

	response.Success(c, nil, "创建角色成功")
}

// UpdateRole godoc
// @Summary 更新角色
// @Description 更新角色
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param role body model.RoleUpdateRequest true "角色信息"
// @Success 200 {string} string "更新角色成功"
// @Failure 400 {string} string "请求参数错误"
// @Failure 500 {string} string "更新角色失败"
// @Router /system/role [put]
func (ctrl *RoleController) UpdateRole(c *gin.Context) {
	var req model.RoleUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Response(c, http.StatusBadRequest, nil, "请求参数错误: "+err.Error())
		return
	}

	if err := ctrl.roleService.UpdateRole(c.Request.Context(), &req); err != nil {
		response.Response(c, http.StatusInternalServerError, nil, "更新角色失败: "+err.Error())
		return
	}

	// 如果更新角色时指定了权限，同步到Casbin
	if len(req.Permissions) > 0 {
		if err := ctrl.syncRolePermissionsToCasbin(c.Request.Context(), req.ID, req.Permissions); err != nil {
			ctrl.logger.Error("同步角色权限到Casbin失败",
				zap.Error(err),
				zap.String("roleID", req.ID))
		}
	}

	response.Success(c, nil, "更新角色成功")
}

// DeleteRole godoc
// @Summary 删除角色
// @Description 删除角色
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path string true "角色ID"
// @Success 200 {string} string "删除角色成功"
// @Failure 400 {string} string "请求参数错误"
// @Failure 500 {string} string "删除角色失败"
// @Router /system/role/{id} [delete]
func (ctrl *RoleController) DeleteRole(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		response.Response(c, http.StatusBadRequest, nil, "角色ID不能为空")
		return
	}

	if err := ctrl.roleService.DeleteRole(c.Request.Context(), id); err != nil {
		response.Response(c, http.StatusInternalServerError, nil, "删除角色失败: "+err.Error())
		return
	}

	// 从Casbin中删除该角色的所有权限
	if _, err := ctrl.casbinService.GetEnforcer().DeleteRole(id); err != nil {
		ctrl.logger.Error("从Casbin中删除角色失败",
			zap.Error(err),
			zap.String("roleID", id))
	}

	response.Success(c, nil, "删除角色成功")
}

// GetUserMenus godoc
// @Summary 获取用户菜单
// @Description 根据用户角色ID获取有权限的菜单
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param roleId query string true "角色ID"
// @Success 200 {object} []model.MenuResponse "获取用户菜单成功"
// @Failure 400 {string} string "请求参数错误"
// @Failure 500 {string} string "获取用户菜单失败"
// @Router /system/role/menus [get]
func (ctrl *RoleController) GetUserMenus(c *gin.Context) {
	roleID := c.Query("roleId")
	if roleID == "" {
		response.Response(c, http.StatusBadRequest, nil, "角色ID不能为空")
		return
	}

	menus, err := ctrl.roleService.GetUserMenusByRoleID(c.Request.Context(), roleID)
	if err != nil {
		response.Response(c, http.StatusInternalServerError, nil, "获取用户菜单失败: "+err.Error())
		return
	}

	response.Success(c, menus, "获取用户菜单成功")
}

// 辅助方法：同步角色权限到Casbin
func (ctrl *RoleController) syncRolePermissionsToCasbin(ctx context.Context, roleID string, permissionIDs []uint) error {
	return ctrl.casbinService.SyncRolePermissions(ctx, roleID, permissionIDs)
}
