package service

import (
	"backend/internal/infrastructure/cache"
	"backend/internal/modules/system/model"
	"backend/internal/modules/system/repository"
	userService "backend/internal/modules/user/service"
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"
)

// IRoleService 角色服务接口
type IRoleService interface {
	ListRoles(ctx context.Context, page, size int, name string) (*model.RoleListResponse, error)
	GetRoleByID(ctx context.Context, id string) (*model.RoleResponse, error)
	CreateRole(ctx context.Context, req *model.RoleCreateRequest) (string, error)
	UpdateRole(ctx context.Context, req *model.RoleUpdateRequest) error
	DeleteRole(ctx context.Context, id string) error
	GetUserMenusByRoleID(ctx context.Context, roleID string) ([]model.MenuResponse, error)
	ClearRoleCache(ctx context.Context) error
	SetUserService(userSvc userService.IUserService)
}

// RoleService 角色服务实现
type RoleService struct {
	roleRepo    repository.IRoleRepository
	menuRepo    repository.IMenuRepository
	redisCache  *cache.RedisClient
	userService userService.IUserService
}

// NewRoleService 创建角色服务实例
func NewRoleService(roleRepo repository.IRoleRepository, menuRepo repository.IMenuRepository, redisCache *cache.RedisClient) IRoleService {
	return &RoleService{
		roleRepo:   roleRepo,
		menuRepo:   menuRepo,
		redisCache: redisCache,
	}
}

// SetUserService 设置用户服务
func (s *RoleService) SetUserService(userSvc userService.IUserService) {
	s.userService = userSvc
}

// ListRoles 获取角色列表
func (s *RoleService) ListRoles(ctx context.Context, page, size int, name string) (*model.RoleListResponse, error) {
	// 缓存键
	cacheKey := fmt.Sprintf("role:list:%d:%d:%s", page, size, name)

	// 尝试从缓存获取
	if s.redisCache != nil {
		var response model.RoleListResponse
		err := s.redisCache.Get(ctx, cacheKey, &response)
		if err == nil {
			return &response, nil
		}
	}

	// 从数据库获取
	roles, total, err := s.roleRepo.ListRoles(ctx, page, size, name)
	if err != nil {
		return nil, err
	}

	var items []model.RoleResponse
	for _, role := range roles {
		// 获取角色权限
		permissions, err := s.roleRepo.GetRolePermissionIDs(ctx, strconv.FormatUint(uint64(role.ID), 10))
		if err != nil {
			return nil, err
		}

		items = append(items, model.RoleResponse{
			ID:          strconv.FormatUint(uint64(role.ID), 10),
			Name:        role.Name,
			Status:      role.Status,
			CreateTime:  role.CreateTime.Format("2006/01/02 15:04:05"),
			Permissions: permissions,
			Remark:      role.Remark,
		})
	}

	result := &model.RoleListResponse{
		Items: items,
		Total: total,
	}

	// 存入缓存
	if s.redisCache != nil {
		if err := s.redisCache.Set(ctx, cacheKey, result, time.Hour); err != nil {
			// 记录缓存错误但不影响正常流程
			fmt.Printf("Failed to cache role list: %v\n", err)
		}
	}

	return result, nil
}

// GetRoleByID 根据ID获取角色
func (s *RoleService) GetRoleByID(ctx context.Context, id string) (*model.RoleResponse, error) {
	// 缓存键
	cacheKey := fmt.Sprintf("role:id:%s", id)

	// 尝试从缓存获取
	if s.redisCache != nil {
		var response model.RoleResponse
		err := s.redisCache.Get(ctx, cacheKey, &response)
		if err == nil {
			return &response, nil
		}
	}

	// 从数据库获取
	role, err := s.roleRepo.GetRoleByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取角色权限
	permissions, err := s.roleRepo.GetRolePermissionIDs(ctx, strconv.FormatUint(uint64(role.ID), 10))
	if err != nil {
		return nil, err
	}

	result := &model.RoleResponse{
		ID:          strconv.FormatUint(uint64(role.ID), 10),
		Name:        role.Name,
		Status:      role.Status,
		CreateTime:  role.CreateTime.Format("2006/01/02 15:04:05"),
		Permissions: permissions,
		Remark:      role.Remark,
	}

	// 存入缓存
	if s.redisCache != nil {
		if err := s.redisCache.Set(ctx, cacheKey, result, time.Hour*24); err != nil {
			// 记录缓存错误但不影响正常流程
			fmt.Printf("Failed to cache role by ID: %v\n", err)
		}
	}

	return result, nil
}

// CreateRole 创建角色
func (s *RoleService) CreateRole(ctx context.Context, req *model.RoleCreateRequest) (string, error) {
	role := &model.Role{
		Name:   req.Name,
		Status: req.Status,
		Remark: req.Remark,
	}

	// 创建角色
	if err := s.roleRepo.CreateRole(ctx, role); err != nil {
		return "", err
	}

	// 生成角色ID字符串
	roleID := strconv.FormatUint(uint64(role.ID), 10)

	// 更新角色权限
	if len(req.Permissions) > 0 {
		if err := s.roleRepo.UpdateRolePermissions(ctx, roleID, req.Permissions); err != nil {
			return roleID, err
		}
	}

	// 清除缓存
	if err := s.ClearRoleCache(ctx); err != nil {
		fmt.Printf("Failed to clear role cache: %v\n", err)
	}

	return roleID, nil
}

// UpdateRole 更新角色
func (s *RoleService) UpdateRole(ctx context.Context, req *model.RoleUpdateRequest) error {
	// 获取原角色
	role, err := s.roleRepo.GetRoleByID(ctx, req.ID)
	if err != nil {
		return err
	}

	// 获取原角色权限，用于比较是否有变化
	oldPermissions, err := s.roleRepo.GetRolePermissionIDs(ctx, strconv.FormatUint(uint64(role.ID), 10))
	if err != nil {
		return err
	}

	// 更新角色信息
	role.Name = req.Name
	role.Status = req.Status
	role.Remark = req.Remark

	if err := s.roleRepo.UpdateRole(ctx, role); err != nil {
		return err
	}

	// 更新角色权限
	if err := s.roleRepo.UpdateRolePermissions(ctx, strconv.FormatUint(uint64(role.ID), 10), req.Permissions); err != nil {
		return err
	}

	// 检查权限是否有变更
	permissionsChanged := !equalUintSlices(oldPermissions, req.Permissions)

	// 如果权限有变更，设置Redis标志位
	if permissionsChanged && s.redisCache != nil {
		if err := s.redisCache.Set(ctx, fmt.Sprintf("role:permission:updated:%s", req.ID), true, time.Hour*24); err != nil {
			fmt.Printf("Failed to set permissions changed flag: %v\n", err)
		}

		// 设置更新时间戳
		now := time.Now().Unix()
		if err := s.redisCache.Set(ctx, fmt.Sprintf("role:permission:update_time:%s", req.ID), now, time.Hour*24); err != nil {
			fmt.Printf("Failed to set permission update timestamp: %v\n", err)
		}
	}

	// 清除缓存
	if err := s.ClearRoleCache(ctx); err != nil {
		fmt.Printf("Failed to clear role cache: %v\n", err)
	}

	return nil
}

// equalUintSlices 比较两个uint切片是否相等
func equalUintSlices(a, b []uint) bool {
	if len(a) != len(b) {
		return false
	}

	// 创建一个map来存储a中的元素
	aMap := make(map[uint]int)
	for _, val := range a {
		aMap[val]++
	}

	// 检查b中的元素是否与a中的相同
	for _, val := range b {
		if count, found := aMap[val]; !found || count == 0 {
			return false
		}
		aMap[val]--
	}

	return true
}

// DeleteRole 删除角色
func (s *RoleService) DeleteRole(ctx context.Context, id string) error {
	err := s.roleRepo.DeleteRole(ctx, id)
	if err != nil {
		return err
	}

	// 清除缓存
	if err := s.ClearRoleCache(ctx); err != nil {
		fmt.Printf("Failed to clear role cache: %v\n", err)
	}

	return nil
}

// ClearRoleCache 清除角色相关缓存
func (s *RoleService) ClearRoleCache(ctx context.Context) error {
	if s.redisCache == nil {
		return nil
	}

	// 使用模式匹配清除所有角色相关缓存
	patterns := []string{
		"role:list:*",  // 角色列表缓存
		"role:id:*",    // 角色详情缓存
		"menu:roles:*", // 角色菜单缓存
		"menu:role:*",  // 单个角色菜单缓存
	}

	for _, pattern := range patterns {
		// 对于确定的键直接删除
		if !strings.Contains(pattern, "*") {
			if err := s.redisCache.Del(ctx, pattern); err != nil {
				fmt.Printf("Failed to delete cache key %s: %v\n", pattern, err)
			}
		} else {
			// 对于模式匹配使用DelByPattern
			if err := s.redisCache.DelByPattern(ctx, pattern); err != nil {
				fmt.Printf("Failed to delete cache pattern %s: %v\n", pattern, err)
			}
		}
	}

	// 注意：不清除 role:permission:updated:* 和 role:permission:update_time:* 标志位
	// 这些标志位用于检测权限更新

	// 清除所有用户的权限码缓存
	if s.userService != nil {
		if err := s.userService.ClearAllAuthCodesCache(ctx); err != nil {
			fmt.Printf("Failed to clear all auth codes cache: %v\n", err)
		}
	}

	return nil
}

// GetUserMenusByRoleID 根据角色ID获取用户菜单
func (s *RoleService) GetUserMenusByRoleID(ctx context.Context, roleID string) ([]model.MenuResponse, error) {
	// 缓存键
	cacheKey := fmt.Sprintf("menu:role:%s", roleID)

	// 尝试从缓存获取
	if s.redisCache != nil {
		var cachedMenus []model.MenuResponse
		err := s.redisCache.Get(ctx, cacheKey, &cachedMenus)
		if err == nil && len(cachedMenus) > 0 {
			return cachedMenus, nil
		}
	}

	// 从数据库获取角色关联的所有菜单ID
	permissions, err := s.roleRepo.GetRolePermissionIDs(ctx, roleID)
	if err != nil {
		return nil, err
	}

	// 获取所有菜单
	allMenus, err := s.menuRepo.GetAllMenus(ctx)
	if err != nil {
		return nil, err
	}

	// 筛选出角色有权限的菜单
	var allMenuMap = make(map[uint]model.Menu)
	for _, menu := range allMenus {
		allMenuMap[menu.ID] = menu
	}

	// 构建角色有权限的菜单树
	var result []model.MenuResponse
	for _, menuID := range permissions {
		if menu, exists := allMenuMap[menuID]; exists {
			// 顶级菜单直接添加到结果中
			if menu.ParentID == 0 {
				menuResp := convertMenuToResponse(menu)
				// 递归查找子菜单
				children := findChildMenus(allMenuMap, menuID, permissions)
				if len(children) > 0 {
					menuResp.Children = children
				}
				result = append(result, menuResp)
			}
		}
	}

	// 存入缓存
	if s.redisCache != nil && len(result) > 0 {
		if err := s.redisCache.Set(ctx, cacheKey, result, time.Hour*24); err != nil {
			// 记录缓存错误但不影响正常流程
			fmt.Printf("Failed to cache user menus by role ID: %v\n", err)
		}
	}

	return result, nil
}

// 辅助函数：将菜单转换为响应结构
func convertMenuToResponse(menu model.Menu) model.MenuResponse {
	return model.MenuResponse{
		Name:      menu.Name,
		Path:      menu.Path,
		Component: menu.Component,
		Redirect:  menu.Redirect,
		Meta:      menu.Meta,
	}
}

// 辅助函数：递归查找子菜单
func findChildMenus(menuMap map[uint]model.Menu, parentID uint, permissions []uint) []model.MenuResponse {
	var children []model.MenuResponse

	// 遍历所有菜单，找出父ID匹配且有权限的子菜单
	for _, menu := range menuMap {
		if menu.ParentID == parentID {
			// 检查是否有权限
			hasPermission := false
			for _, permID := range permissions {
				if permID == menu.ID {
					hasPermission = true
					break
				}
			}

			if hasPermission {
				child := convertMenuToResponse(menu)
				// 递归查找更深层次的子菜单
				subChildren := findChildMenus(menuMap, menu.ID, permissions)
				if len(subChildren) > 0 {
					child.Children = subChildren
				}
				children = append(children, child)
			}
		}
	}

	return children
}
