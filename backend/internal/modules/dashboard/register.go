package dashboard

import (
	"backend/internal/middleware"
	"backend/internal/modules/dashboard/controller"
	"backend/internal/modules/dashboard/model"
	"backend/internal/modules/dashboard/repository"
	"backend/internal/modules/dashboard/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// autoMigrate 自动迁移数据库表结构
func autoMigrate(db *gorm.DB, logger *zap.Logger) error {
	if logger != nil {
		logger.Info("开始看板模块数据库迁移")
	}

	err := db.AutoMigrate(
		&model.GPUServerMonthlyStats{},
	)
	if err != nil {
		if logger != nil {
			logger.Error("看板模块数据库迁移失败", zap.Error(err))
		}
		return err
	}

	if logger != nil {
		logger.Info("看板模块数据库迁移完成")
	}
	return nil
}

// RegisterDashboardModule 注册看板模块
func RegisterDashboardModule(router *gin.RouterGroup, db *gorm.DB) {
	// 执行数据库迁移
	if err := autoMigrate(db, nil); err != nil {
		panic("看板模块数据库迁移失败: " + err.Error())
	}

	// 初始化仓库
	dashboardRepo := repository.NewDashboardRepository(db)
	resourceStatsRepo := repository.NewResourceStatsRepository(db)
	slaRepo := repository.NewSLARepository(db)

	// 初始化服务
	dashboardSvc := service.NewDashboardService(dashboardRepo)
	resourceStatsSvc := service.NewResourceStatsService(resourceStatsRepo)
	slaSvc := service.NewSLAService(slaRepo)

	// 初始化控制器
	dashboardCtrl := controller.NewDashboardController(dashboardSvc)
	resourceStatsCtrl := controller.NewResourceStatsController(resourceStatsSvc)
	slaCtrl := controller.NewSLAController(slaSvc)

	// 注册路由
	apiGroup := router.Group("/dashboard")
	apiGroup.Use(middleware.AuthMiddleware()) // 添加认证中间件
	dashboardCtrl.RegisterRoutes(apiGroup)
	resourceStatsCtrl.RegisterRoutes(apiGroup)
	slaCtrl.RegisterRoutes(apiGroup)
}

// Module 看板模块
type Module struct {
	db     *gorm.DB
	logger *zap.Logger

	// 服务
	dashboardService     service.DashboardService
	resourceStatsService service.ResourceStatsService
	slaService           service.SLAService

	// 控制器
	dashboardController     *controller.DashboardController
	resourceStatsController *controller.ResourceStatsController
	slaController           *controller.SLAController
}

// AutoMigrate 执行数据库迁移
func (m *Module) AutoMigrate() error {
	return autoMigrate(m.db, m.logger)
}

// Initialize 初始化模块
func (m *Module) Initialize() error {
	// 初始化仓库
	dashboardRepo := repository.NewDashboardRepository(m.db)
	resourceStatsRepo := repository.NewResourceStatsRepository(m.db)
	slaRepo := repository.NewSLARepository(m.db)

	// 初始化服务和控制器
	m.dashboardService = service.NewDashboardService(dashboardRepo)
	m.resourceStatsService = service.NewResourceStatsService(resourceStatsRepo)
	m.slaService = service.NewSLAService(slaRepo)

	m.dashboardController = controller.NewDashboardController(m.dashboardService)
	m.resourceStatsController = controller.NewResourceStatsController(m.resourceStatsService)
	m.slaController = controller.NewSLAController(m.slaService)

	return nil
}

// RegisterRoutes 注册路由
func (m *Module) RegisterRoutes(router *gin.RouterGroup) {
	// 使用全局认证中间件保护路由
	dashboardGroup := router.Group("/dashboard")
	dashboardGroup.Use(middleware.AuthMiddleware()) // 添加认证中间件

	m.dashboardController.RegisterRoutes(dashboardGroup)
	m.resourceStatsController.RegisterRoutes(dashboardGroup)
	m.slaController.RegisterRoutes(dashboardGroup)
}
