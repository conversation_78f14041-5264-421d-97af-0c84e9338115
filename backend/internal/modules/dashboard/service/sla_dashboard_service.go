package service

import (
	"backend/internal/modules/dashboard/repository"
	"context"
	"fmt"
	"strings"
	"time"
)

// DashboardService 看板服务接口
type DashboardService interface {
	// 获取报障单时间分布统计
	GetTicketTimeStats(ctx context.Context, timeRange string, startDate, endDate time.Time, groupBy string, project string, countInSLA *bool) (map[string]interface{}, error)
	// 获取故障类型分布
	GetFaultTypeStats(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error)
	// 获取故障来源分布
	GetSourceStats(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error)
	// 获取具体故障类型分布
	GetFaultDetailTypeStats(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error)
	// 获取报障单状态分布
	GetStatusStats(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error)
	// 获取处理时长统计
	GetDurationStats(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error)
	// 获取SLA达标情况
	GetSLAStats(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error)
	// 计算计入SLA且已完成报障单的业务影响总时长
	GetBusinessImpactTotalTime(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error)
	// 获取GPU卡故障比
	GetGPUFaultRatio(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error)
	// 获取故障单详情列表
	GetFaultTicketDetails(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error)
	// 获取故障总台数
	GetTotalFaultDevices(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error)
	// 获取工程师响应和修复时长统计
	GetEngineerResponseAndFixTimeStats(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error)
	// 获取故障设备厂商分布统计
	GetFaultDeviceBrandDistribution(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error)
	// 获取每天故障处理总时长趋势统计
	GetDailyBusinessImpactTrend(ctx context.Context, timeRange string, startDate, endDate time.Time, groupBy string, project string, countInSLA *bool) (map[string]interface{}, error)
	// 获取每天硬件故障次数趋势统计
	GetHardwareFaultTrend(ctx context.Context, timeRange string, startDate, endDate time.Time, groupBy string, project string, countInSLA *bool) (map[string]interface{}, error)
	// 获取资源变更统计
	GetResourceChangesStats(ctx context.Context, timeRange string, startDate, endDate time.Time, project string) (map[string]interface{}, error)
}

// dashboardService 看板服务实现
type dashboardService struct {
	repo repository.DashboardRepository
}

// NewDashboardService 创建看板服务
func NewDashboardService(repo repository.DashboardRepository) DashboardService {
	return &dashboardService{repo: repo}
}

// GetTicketTimeStats 获取报障单时间分布统计
func (s *dashboardService) GetTicketTimeStats(ctx context.Context, timeRange string, startDate, endDate time.Time, groupBy string, project string, countInSLA *bool) (map[string]interface{}, error) {
	// 处理时间范围
	start, end := s.processTimeRange(timeRange, startDate, endDate)

	// 处理countInSLA参数，默认为true
	countInSLAValue := true
	if countInSLA != nil {
		countInSLAValue = *countInSLA
	}

	// 获取报障单时间统计
	timeStats, err := s.repo.GetTicketTimeStats(ctx, start, end, groupBy, project, countInSLAValue)
	if err != nil {
		return nil, err
	}

	// 格式化返回数据，适配ECharts图表格式
	result := map[string]interface{}{
		"xAxis": timeStats["dates"],
		"series": []map[string]interface{}{
			{
				"name": "报障单数量",
				"type": "line",
				"data": timeStats["counts"],
			},
		},
		"total": timeStats["total"],
		"timeRange": map[string]string{
			"start": start.Format("2006-01-02"),
			"end":   end.Format("2006-01-02"),
		},
	}

	// 如果指定了项目，添加项目信息到结果中
	if project != "" {
		result["project"] = project
	}

	return result, nil
}

// GetFaultTypeStats 获取故障类型分布
func (s *dashboardService) GetFaultTypeStats(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error) {
	// 处理时间范围
	start, end := s.processTimeRange(timeRange, startDate, endDate)

	// 处理countInSLA参数，默认为true
	countInSLAValue := true
	if countInSLA != nil {
		countInSLAValue = *countInSLA
	}

	// 获取故障类型统计
	typeStats, err := s.repo.GetFaultTypeStats(ctx, start, end, project, countInSLAValue)
	if err != nil {
		return nil, err
	}

	// 格式化返回数据，适配ECharts饼图格式
	result := map[string]interface{}{
		"legend": typeStats["types"],
		"series": []map[string]interface{}{
			{
				"name": "故障类型分布",
				"type": "pie",
				"data": typeStats["data"],
			},
		},
		"total": typeStats["total"],
		"timeRange": map[string]string{
			"start": start.Format("2006-01-02"),
			"end":   end.Format("2006-01-02"),
		},
	}

	// 如果指定了项目，添加项目信息到结果中
	if project != "" {
		result["project"] = project
	}

	return result, nil
}

// GetSourceStats 获取故障来源分布
func (s *dashboardService) GetSourceStats(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error) {
	// 处理时间范围
	start, end := s.processTimeRange(timeRange, startDate, endDate)

	// 处理countInSLA参数，默认为true
	countInSLAValue := true
	if countInSLA != nil {
		countInSLAValue = *countInSLA
	}

	// 获取故障来源统计
	sourceStats, err := s.repo.GetSourceStats(ctx, start, end, project, countInSLAValue)
	if err != nil {
		return nil, err
	}

	// 格式化返回数据，适配ECharts饼图格式
	result := map[string]interface{}{
		"legend": sourceStats["sources"],
		"series": []map[string]interface{}{
			{
				"name": "故障来源分布",
				"type": "pie",
				"data": sourceStats["data"],
			},
		},
		"total": sourceStats["total"],
		"timeRange": map[string]string{
			"start": start.Format("2006-01-02"),
			"end":   end.Format("2006-01-02"),
		},
	}

	// 如果指定了项目，添加项目信息到结果中
	if project != "" {
		result["project"] = project
	}

	return result, nil
}

// GetFaultDetailTypeStats 获取具体故障类型分布
func (s *dashboardService) GetFaultDetailTypeStats(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error) {
	// 处理时间范围
	start, end := s.processTimeRange(timeRange, startDate, endDate)

	// 处理countInSLA参数，默认为true
	countInSLAValue := true
	if countInSLA != nil {
		countInSLAValue = *countInSLA
	}

	// 获取具体故障类型统计
	typeStats, err := s.repo.GetFaultDetailTypeStats(ctx, start, end, project, countInSLAValue)
	if err != nil {
		return nil, err
	}

	// 格式化返回数据，适配ECharts饼图格式
	result := map[string]interface{}{
		"legend": typeStats["types"],
		"series": []map[string]interface{}{
			{
				"name": "具体故障类型分布",
				"type": "pie",
				"data": typeStats["data"],
			},
		},
		"total": typeStats["total"],
		"timeRange": map[string]string{
			"start": start.Format("2006-01-02"),
			"end":   end.Format("2006-01-02"),
		},
	}

	// 如果指定了项目，添加项目信息到结果中
	if project != "" {
		result["project"] = project
	}

	return result, nil
}

// GetStatusStats 获取报障单状态分布
func (s *dashboardService) GetStatusStats(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error) {
	// 处理时间范围
	start, end := s.processTimeRange(timeRange, startDate, endDate)

	// 处理countInSLA参数，默认为true
	countInSLAValue := true
	if countInSLA != nil {
		countInSLAValue = *countInSLA
	}

	// 获取状态统计
	statusStats, err := s.repo.GetStatusStats(ctx, start, end, project, countInSLAValue)
	if err != nil {
		return nil, err
	}

	// 格式化返回数据，适配ECharts图表格式
	result := map[string]interface{}{
		"legend": statusStats["statuses"],
		"series": []map[string]interface{}{
			{
				"name": "工单状态分布",
				"type": "pie",
				"data": statusStats["data"],
			},
		},
		"total": statusStats["total"],
		"timeRange": map[string]string{
			"start": start.Format("2006-01-02"),
			"end":   end.Format("2006-01-02"),
		},
	}

	// 如果指定了项目，添加项目信息到结果中
	if project != "" {
		result["project"] = project
	}

	return result, nil
}

// GetDurationStats 获取处理时长统计
func (s *dashboardService) GetDurationStats(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error) {
	// 处理时间范围
	start, end := s.processTimeRange(timeRange, startDate, endDate)

	// 处理countInSLA参数，默认为true
	countInSLAValue := true
	if countInSLA != nil {
		countInSLAValue = *countInSLA
	}

	// 获取处理时长统计
	durationStats, err := s.repo.GetDurationStats(ctx, start, end, project, countInSLAValue)
	if err != nil {
		return nil, err
	}

	// 格式化返回数据，适配ECharts图表格式
	result := map[string]interface{}{
		"phases":   durationStats["phases"],
		"avgTimes": durationStats["avgTimes"],
		"maxTimes": durationStats["maxTimes"],
		"minTimes": durationStats["minTimes"],
		"timeRange": map[string]string{
			"start": start.Format("2006-01-02"),
			"end":   end.Format("2006-01-02"),
		},
	}

	// 如果指定了项目，添加项目信息到结果中
	if project != "" {
		result["project"] = project
	}

	return result, nil
}

// GetSLAStats 获取SLA达标情况
func (s *dashboardService) GetSLAStats(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error) {
	// 处理时间范围
	start, end := s.processTimeRange(timeRange, startDate, endDate)

	// 处理countInSLA参数，默认为true
	countInSLAValue := true
	if countInSLA != nil {
		countInSLAValue = *countInSLA
	}

	// 获取SLA统计
	slaStats, err := s.repo.GetSLAStats(ctx, start, end, project, countInSLAValue)
	if err != nil {
		return nil, err
	}

	// 格式化返回数据，适配ECharts图表格式
	result := map[string]interface{}{
		"compliance":     slaStats["compliance"],
		"violated":       slaStats["violated"],
		"exempted":       slaStats["exempted"],
		"total":          slaStats["total"],
		"complianceRate": slaStats["complianceRate"],
		"timeRange": map[string]string{
			"start": start.Format("2006-01-02"),
			"end":   end.Format("2006-01-02"),
		},
	}

	// 如果指定了项目，添加项目信息到结果中
	if project != "" {
		result["project"] = project
	}

	return result, nil
}

// GetBusinessImpactTotalTime 计算计入SLA且已完成报障单的业务影响总时长
func (s *dashboardService) GetBusinessImpactTotalTime(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error) {
	// 处理时间范围
	start, end := s.processTimeRange(timeRange, startDate, endDate)

	// 处理countInSLA参数，默认为true
	countInSLAValue := true
	if countInSLA != nil {
		countInSLAValue = *countInSLA
	}

	// 获取业务影响时长统计
	impactStats, err := s.repo.GetBusinessImpactTotalTime(ctx, start, end, project, countInSLAValue)
	if err != nil {
		return nil, err
	}

	// 格式化返回数据
	result := map[string]interface{}{
		"total_tickets":     impactStats["total_tickets"],
		"total_impact_time": impactStats["total_impact_time"],
		"avg_impact_time":   impactStats["avg_impact_time"],
		"max_impact_time":   impactStats["max_impact_time"],
		"min_impact_time":   impactStats["min_impact_time"],
		"type_statistics":   impactStats["type_statistics"],
		"timeRange": map[string]string{
			"start": start.Format("2006-01-02"),
			"end":   end.Format("2006-01-02"),
		},
	}

	// 将总时长转换为更友好的格式
	totalMinutesInterface, ok := impactStats["total_impact_time"]
	if !ok {
		return nil, fmt.Errorf("缺少总影响时间数据")
	}

	totalMinutes, ok := totalMinutesInterface.(int64)
	if !ok {
		return nil, fmt.Errorf("总影响时间数据类型错误")
	}

	result["impact_time_display"] = formatDuration(totalMinutes)

	// 如果指定了项目，添加项目信息到结果中
	if project != "" {
		result["project"] = project
	}

	return result, nil
}

// formatDuration 将分钟数格式化为"x天y小时z分钟"的形式
func formatDuration(minutes int64) string {
	if minutes == 0 {
		return "0分钟"
	}

	days := minutes / (24 * 60)
	hours := (minutes % (24 * 60)) / 60
	mins := minutes % 60

	var parts []string
	if days > 0 {
		parts = append(parts, fmt.Sprintf("%d天", days))
	}
	if hours > 0 {
		parts = append(parts, fmt.Sprintf("%d小时", hours))
	}
	if mins > 0 {
		parts = append(parts, fmt.Sprintf("%d分钟", mins))
	}

	return strings.Join(parts, "")
}

// processTimeRange 处理时间范围
func (s *dashboardService) processTimeRange(timeRange string, startDate, endDate time.Time) (time.Time, time.Time) {
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	var start, end time.Time

	switch timeRange {
	case "today":
		start = today
		end = today.AddDate(0, 0, 1).Add(-time.Second)
	case "week":
		// 计算本周的开始（周一）
		weekday := int(now.Weekday())
		if weekday == 0 {
			weekday = 7 // 将周日设为7
		}
		start = today.AddDate(0, 0, -(weekday - 1))
		end = start.AddDate(0, 0, 7).Add(-time.Second)
	case "month":
		// 本月的开始
		start = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
		// 下月的开始减一秒
		end = time.Date(now.Year(), now.Month()+1, 1, 0, 0, 0, 0, now.Location()).Add(-time.Second)
	case "year":
		// 本年的开始
		start = time.Date(now.Year(), 1, 1, 0, 0, 0, 0, now.Location())
		// 明年的开始减一秒
		end = time.Date(now.Year()+1, 1, 1, 0, 0, 0, 0, now.Location()).Add(-time.Second)
	case "custom":
		// 使用传入的自定义日期范围
		if !startDate.IsZero() {
			start = startDate
		} else {
			// 默认30天前
			start = today.AddDate(0, 0, -30)
		}

		if !endDate.IsZero() {
			// 设置为当天的23:59:59
			end = time.Date(endDate.Year(), endDate.Month(), endDate.Day(), 23, 59, 59, 0, endDate.Location())
		} else {
			// 默认到今天
			end = today.AddDate(0, 0, 1).Add(-time.Second)
		}
	default:
		// 默认显示最近30天
		start = today.AddDate(0, 0, -30)
		end = today.AddDate(0, 0, 1).Add(-time.Second)
	}

	return start, end
}

// GetGPUFaultRatio 获取GPU卡故障比
func (s *dashboardService) GetGPUFaultRatio(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error) {
	// 处理时间范围
	start, end := s.processTimeRange(timeRange, startDate, endDate)

	// 如果项目为空，返回错误
	if project == "" {
		return nil, fmt.Errorf("项目不能为空")
	}

	// 处理countInSLA参数，默认为true
	countInSLAValue := true
	if countInSLA != nil {
		countInSLAValue = *countInSLA
	}

	// 获取GPU卡故障比
	gpuStats, err := s.repo.GetGPUFaultRatio(ctx, start, end, project, countInSLAValue)
	if err != nil {
		return nil, err
	}

	return gpuStats, nil
}

// GetFaultTicketDetails 获取故障单详情列表
func (s *dashboardService) GetFaultTicketDetails(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error) {
	// 处理时间范围
	start, end := s.processTimeRange(timeRange, startDate, endDate)

	// 如果项目为空，返回错误
	if project == "" {
		return nil, fmt.Errorf("项目不能为空")
	}

	// 处理countInSLA参数，默认为true
	countInSLAValue := true
	if countInSLA != nil {
		countInSLAValue = *countInSLA
	}

	// 获取故障单详情
	details, err := s.repo.GetFaultTicketDetails(ctx, start, end, project, countInSLAValue)
	if err != nil {
		return nil, err
	}

	// 返回结果
	result := map[string]interface{}{
		"details": details["tickets"],
		"total":   details["total"],
		"timeRange": map[string]string{
			"start": start.Format("2006-01-02"),
			"end":   end.Format("2006-01-02"),
		},
		"project": project,
	}

	return result, nil
}

// GetTotalFaultDevices 获取故障总台数
func (s *dashboardService) GetTotalFaultDevices(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error) {
	// 处理时间范围
	start, end := s.processTimeRange(timeRange, startDate, endDate)

	// 处理countInSLA参数，默认为true
	countInSLAValue := true
	if countInSLA != nil {
		countInSLAValue = *countInSLA
	}

	// 获取故障总台数
	stats, err := s.repo.GetTotalFaultDevices(ctx, start, end, project, countInSLAValue)
	if err != nil {
		return nil, err
	}

	// 格式化返回数据
	result := map[string]interface{}{
		"total_devices": stats["total_devices"],
		"total_tickets": stats["total_tickets"],
		"type_stats":    stats["type_stats"],
		"timeRange": map[string]string{
			"start": start.Format("2006-01-02"),
			"end":   end.Format("2006-01-02"),
		},
	}

	// 如果指定了项目，添加项目信息到结果中
	if project != "" {
		result["project"] = project
	}

	return result, nil
}

// GetEngineerResponseAndFixTimeStats 获取工程师响应和修复时长统计
func (s *dashboardService) GetEngineerResponseAndFixTimeStats(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error) {
	// 处理时间范围
	start, end := s.processTimeRange(timeRange, startDate, endDate)

	// 处理countInSLA参数，默认为true
	countInSLAValue := true
	if countInSLA != nil {
		countInSLAValue = *countInSLA
	}

	// 获取工程师统计
	engineerStats, err := s.repo.GetEngineerResponseAndFixTimeStats(ctx, start, end, project, countInSLAValue)
	if err != nil {
		return nil, err
	}

	// 格式化返回数据
	result := map[string]interface{}{
		"engineers": engineerStats["engineers"],
		"stats":     engineerStats["stats"],
		"timeRange": map[string]string{
			"start": start.Format("2006-01-02"),
			"end":   end.Format("2006-01-02"),
		},
	}

	// 如果指定了项目，添加项目信息到结果中
	if project != "" {
		result["project"] = project
	}

	return result, nil
}

// GetFaultDeviceBrandDistribution 获取故障设备厂商分布统计
func (s *dashboardService) GetFaultDeviceBrandDistribution(ctx context.Context, timeRange string, startDate, endDate time.Time, project string, countInSLA *bool) (map[string]interface{}, error) {
	// 处理时间范围
	start, end := s.processTimeRange(timeRange, startDate, endDate)

	// 处理countInSLA参数，默认为true
	countInSLAValue := true
	if countInSLA != nil {
		countInSLAValue = *countInSLA
	}

	// 获取厂商分布统计
	stats, err := s.repo.GetFaultDeviceBrandDistribution(ctx, start, end, project, countInSLAValue)
	if err != nil {
		return nil, err
	}

	// 格式化返回数据，适配ECharts饼图格式
	result := map[string]interface{}{
		"legend": stats["brands"],
		"series": []map[string]interface{}{
			{
				"name": "厂商分布",
				"type": "pie",
				"data": stats["data"],
			},
		},
		"total_devices": stats["total_devices"],
		"timeRange": map[string]string{
			"start": start.Format("2006-01-02"),
			"end":   end.Format("2006-01-02"),
		},
	}

	// 如果指定了项目，添加项目信息到结果中
	if project != "" {
		result["project"] = project
	}

	return result, nil
}

// GetDailyBusinessImpactTrend 获取每天业务影响时长趋势统计
func (s *dashboardService) GetDailyBusinessImpactTrend(ctx context.Context, timeRange string, startDate, endDate time.Time, groupBy string, project string, countInSLA *bool) (map[string]interface{}, error) {
	// 处理时间范围
	start, end := s.processTimeRange(timeRange, startDate, endDate)

	// 处理countInSLA参数，默认为false
	countInSLAValue := false
	if countInSLA != nil {
		countInSLAValue = *countInSLA
	}

	// 获取每天业务影响时长趋势
	stats, err := s.repo.GetDailyBusinessImpactTrend(ctx, start, end, groupBy, project, countInSLAValue)
	if err != nil {
		return nil, err
	}

	// 获取总时长（分钟数）
	totalMinutesInterface, ok := stats["total_impact_time"]
	if !ok {
		return nil, fmt.Errorf("缺少总影响时间数据")
	}

	totalMinutes, ok := totalMinutesInterface.(int64)
	if !ok {
		return nil, fmt.Errorf("总影响时间数据类型错误")
	}

	// 格式化返回数据，适配ECharts图表格式
	result := map[string]interface{}{
		"xAxis": stats["dates"],
		"series": []map[string]interface{}{
			{
				"name": "故障处理时长",
				"type": "line",
				"data": stats["impact_times"],
			},
			{
				"name":       "报障单数量",
				"type":       "bar",
				"yAxisIndex": 1,
				"data":       stats["ticket_counts"],
			},
		},
		"total_impact_time":   totalMinutes,
		"impact_time_display": totalMinutes, // 直接使用分钟数而非格式化字符串
		"total_tickets":       stats["total_tickets"],
		"timeRange": map[string]string{
			"start": start.Format("2006-01-02"),
			"end":   end.Format("2006-01-02"),
		},
	}

	// 如果指定了项目，添加项目信息到结果中
	if project != "" {
		result["project"] = project
	}

	return result, nil
}

// GetHardwareFaultTrend 获取每天硬件故障次数趋势统计
func (s *dashboardService) GetHardwareFaultTrend(ctx context.Context, timeRange string, startDate, endDate time.Time, groupBy string, project string, countInSLA *bool) (map[string]interface{}, error) {
	// 处理时间范围
	start, end := s.processTimeRange(timeRange, startDate, endDate)

	// 处理countInSLA参数，默认为false
	countInSLAValue := false
	if countInSLA != nil {
		countInSLAValue = *countInSLA
	}

	// 获取每天硬件故障次数趋势
	stats, err := s.repo.GetHardwareFaultTrend(ctx, start, end, groupBy, project, countInSLAValue)
	if err != nil {
		return nil, err
	}

	// 格式化返回数据，适配ECharts图表格式
	result := map[string]interface{}{
		"xAxis": stats["dates"],
		"series": []map[string]interface{}{
			{
				"name": "硬件故障次数",
				"type": "line",
				"data": stats["ticket_counts"],
			},
		},
		"total_tickets": stats["total_tickets"],
		"timeRange": map[string]string{
			"start": start.Format("2006-01-02"),
			"end":   end.Format("2006-01-02"),
		},
	}

	// 如果指定了项目，添加项目信息到结果中
	if project != "" {
		result["project"] = project
	}

	return result, nil
}

// GetResourceChangesStats 获取资源变更统计
func (s *dashboardService) GetResourceChangesStats(ctx context.Context, timeRange string, startDate, endDate time.Time, project string) (map[string]interface{}, error) {
	// 处理时间范围
	start, end := s.processTimeRange(timeRange, startDate, endDate)

	// 获取资源变更统计
	stats, err := s.repo.GetResourceChangesStats(ctx, start, end, project)
	if err != nil {
		return nil, err
	}

	// 如果指定了项目，添加项目信息到结果中
	if project != "" {
		stats["project"] = project
	}

	return stats, nil
}
