package repository

import (
	"context"
	"fmt"
	"time"

	"backend/internal/modules/dashboard/model"
	"gorm.io/gorm"
)

// SLATicket 影响SLA的工单
type SLATicket struct {
	ID            string     // 工单ID
	ImpactMinutes int64      // 影响SLA的时长（分钟）
	ServerCount   int        // 影响的服务器数量
	Project       string     // 所属项目
	CloseTime     *time.Time // 工单关闭时间
}

// SLARepository SLA存储库接口
type SLARepository interface {
	// 获取影响SLA的工单
	GetSLAImpactTickets(ctx context.Context, startDate, endDate time.Time, project string) ([]SLATicket, error)
	// 获取项目的GPU服务器数量（排除已报废）
	GetProjectGPUServerCount(ctx context.Context, project string) (int, error)
	// 获取指定月份的GPU服务器数量（优先使用月度统计数据）
	GetProjectGPUServerCountByMonth(ctx context.Context, project string, year, month int) (int, error)
	// 检查指定年月是否有月度统计数据
	HasMonthlyStats(ctx context.Context, year, month int) (bool, error)
	// 获取月度统计的项目GPU服务器数量
	GetMonthlyStatsProjectGPUCount(ctx context.Context, project string, year, month int) (int, error)
	// 获取月度统计的集群信息
	GetMonthlyStatsClusterInfo(ctx context.Context, project string, year, month int) ([]model.MonthlyStatsClusterInfo, error)
}

// slaRepository SLA存储库实现
type slaRepository struct {
	db *gorm.DB
}

// NewSLARepository 创建SLA存储库
func NewSLARepository(db *gorm.DB) SLARepository {
	return &slaRepository{db: db}
}

// GetSLAImpactTickets 获取影响SLA的工单
func (r *slaRepository) GetSLAImpactTickets(ctx context.Context, startDate, endDate time.Time, project string) ([]SLATicket, error) {
	// 查询状态为completed且CountInSLA为true且关闭时间在指定范围内的工单
	var results []struct {
		TicketNo           string     `gorm:"column:ticket_no"`
		BusinessImpactTime int64      `gorm:"column:business_impact_time"`
		ResourceIdentifier string     `gorm:"column:resource_identifier"`
		Project            string     `gorm:"column:project"`
		CloseTime          *time.Time `gorm:"column:close_time"`
	}

	// 基础查询：获取所有符合条件的工单
	baseQuery := r.db.WithContext(ctx).Table("fault_tickets").
		Select("fault_tickets.ticket_no, fault_tickets.business_impact_time, fault_tickets.resource_identifier, resources.project, fault_tickets.close_time").
		Joins("LEFT JOIN resources ON fault_tickets.device_sn = resources.sn").
		Where("fault_tickets.status = ?", "completed").
		Where("fault_tickets.count_in_sla = ?", true).
		Where("fault_tickets.close_time BETWEEN ? AND ?", startDate, endDate).
		Where("fault_tickets.deleted_at IS NULL")

	// 如果指定了项目，筛选该项目的工单
	if project != "" {
		baseQuery = baseQuery.Where("resources.project = ?", project)
	}

	// 执行查询
	err := baseQuery.Find(&results).Error
	if err != nil {
		return nil, err
	}

	// 转换为SLATicket列表
	tickets := make([]SLATicket, 0, len(results))
	for _, result := range results {
		// 默认每个工单影响1台服务器，除非resource_identifier字段能提供更准确的信息
		serverCount := 1

		tickets = append(tickets, SLATicket{
			ID:            result.TicketNo,
			ImpactMinutes: result.BusinessImpactTime,
			ServerCount:   serverCount,
			Project:       result.Project,
			CloseTime:     result.CloseTime,
		})
	}

	return tickets, nil
}

// GetProjectGPUServerCount 获取项目的GPU服务器数量（排除已报废）
func (r *slaRepository) GetProjectGPUServerCount(ctx context.Context, project string) (int, error) {
	var count int64

	query := r.db.WithContext(ctx).Table("resources").
		Joins("LEFT JOIN asset_devices ON resources.asset_id = asset_devices.id").
		Where("resources.deleted_at IS NULL").
		Where("asset_devices.asset_type = ?", "gpu_server").
		Where("asset_devices.asset_status != ?", "scrapped")

	// 如果指定了项目，只统计该项目的GPU服务器
	if project != "" {
		query = query.Where("resources.project = ?", project)
	}

	err := query.Count(&count).Error
	if err != nil {
		return 0, err
	}

	return int(count), nil
}

// GetProjectGPUServerCountByMonth 获取指定月份的GPU服务器数量（优先使用月度统计数据）
func (r *slaRepository) GetProjectGPUServerCountByMonth(ctx context.Context, project string, year, month int) (int, error) {
	// 首先检查是否有月度统计数据
	hasStats, err := r.HasMonthlyStats(ctx, year, month)
	if err != nil {
		return 0, fmt.Errorf("检查月度统计数据失败: %w", err)
	}

	// 如果有月度统计数据，使用月度统计数据
	if hasStats {
		return r.GetMonthlyStatsProjectGPUCount(ctx, project, year, month)
	}

	// 如果没有月度统计数据，使用当前数量
	return r.GetProjectGPUServerCount(ctx, project)
}

// HasMonthlyStats 检查指定年月是否有月度统计数据
func (r *slaRepository) HasMonthlyStats(ctx context.Context, year, month int) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.GPUServerMonthlyStats{}).
		Where("year = ? AND month = ?", year, month).
		Count(&count).Error

	return count > 0, err
}

// GetMonthlyStatsProjectGPUCount 获取月度统计的项目GPU服务器数量
func (r *slaRepository) GetMonthlyStatsProjectGPUCount(ctx context.Context, project string, year, month int) (int, error) {
	var stats model.GPUServerMonthlyStats
	err := r.db.WithContext(ctx).
		Where("year = ? AND month = ? AND project = ?", year, month, project).
		First(&stats).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return 0, fmt.Errorf("未找到项目%s在%d年%d月的月度统计数据", project, year, month)
		}
		return 0, err
	}

	return stats.TotalGPUServers, nil
}

// GetMonthlyStatsClusterInfo 获取月度统计的集群信息
func (r *slaRepository) GetMonthlyStatsClusterInfo(ctx context.Context, project string, year, month int) ([]model.MonthlyStatsClusterInfo, error) {
	var stats []model.GPUServerMonthlyStats
	err := r.db.WithContext(ctx).
		Where("year = ? AND month = ? AND project = ?", year, month, project).
		Find(&stats).Error

	if err != nil {
		return nil, err
	}

	clusters := make([]model.MonthlyStatsClusterInfo, len(stats))
	for i, stat := range stats {
		clusters[i] = model.MonthlyStatsClusterInfo{
			ClusterName:     stat.ClusterName,
			ClusterGPUCount: stat.ClusterGPUCount,
		}
	}

	return clusters, nil
}

// DatabaseHandler 数据库处理接口（假设）
type DatabaseHandler interface {
	// 数据库操作方法
	// ...
}
