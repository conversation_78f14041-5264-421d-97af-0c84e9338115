package repository

import (
	"context"
	"fmt"
	"time"

	"backend/internal/modules/dashboard/model"
	"gorm.io/gorm"
)

// ResourceStatsRepository 资源统计仓库接口
type ResourceStatsRepository interface {
	// 获取服务器总数量及各状态数量
	GetServerStats(ctx context.Context) (map[string]interface{}, error)
	// 获取各集群类型的数量
	GetClusterStats(ctx context.Context) (map[string]interface{}, error)
	// 按项目获取服务器数量（支持时间范围参数）
	GetServerStatsByProject(ctx context.Context, project string, startDate, endDate *time.Time) (map[string]interface{}, error)
	// 按项目获取集群数量（支持时间范围参数）
	GetClusterStatsByProject(ctx context.Context, project string, startDate, endDate *time.Time) (map[string]interface{}, error)
	// 获取设备品牌分布
	GetBrandDistribution(ctx context.Context, project string) (map[string]interface{}, error)
	// 获取服务器机房分布
	GetRoomDistribution(ctx context.Context, project string) (map[string]interface{}, error)
	// 获取GPU卡型号分布
	GetGpuModelDistribution(ctx context.Context, project string) (map[string]interface{}, error)
}

// resourceStatsRepository 资源统计仓库实现
type resourceStatsRepository struct {
	db *gorm.DB
}

// NewResourceStatsRepository 创建资源统计仓库
func NewResourceStatsRepository(db *gorm.DB) ResourceStatsRepository {
	return &resourceStatsRepository{db: db}
}

// GetServerStats 获取服务器总数量及各状态数量
func (r *resourceStatsRepository) GetServerStats(ctx context.Context) (map[string]interface{}, error) {
	// 查询服务器总数量
	var totalCount int64
	if err := r.db.WithContext(ctx).Table("resources").
		Where("deleted_at IS NULL").
		Count(&totalCount).Error; err != nil {
		return nil, err
	}

	// 查询各业务状态的服务器数量
	type StatusCount struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}
	var bizStatusCounts []StatusCount
	if err := r.db.WithContext(ctx).Table("resources").
		Select("biz_status as status, COUNT(*) as count").
		Where("deleted_at IS NULL").
		Group("biz_status").
		Find(&bizStatusCounts).Error; err != nil {
		return nil, err
	}

	// 查询各资源状态的服务器数量
	var resStatusCounts []StatusCount
	if err := r.db.WithContext(ctx).Table("resources").
		Select("res_status as status, COUNT(*) as count").
		Where("deleted_at IS NULL").
		Group("res_status").
		Find(&resStatusCounts).Error; err != nil {
		return nil, err
	}

	// 查询备机数量
	var backupCount int64
	if err := r.db.WithContext(ctx).Table("resources").
		Where("deleted_at IS NULL AND is_backup = ?", true).
		Count(&backupCount).Error; err != nil {
		return nil, err
	}

	// 查询各资产类型的服务器数量
	type AssetTypeCount struct {
		AssetType string `json:"asset_type"`
		Count     int64  `json:"count"`
	}
	var assetTypeCounts []AssetTypeCount
	if err := r.db.WithContext(ctx).Table("resources").
		Select("asset_devices.asset_type as asset_type, COUNT(*) as count").
		Joins("LEFT JOIN asset_devices ON resources.asset_id = asset_devices.id").
		Where("resources.deleted_at IS NULL").
		Group("asset_devices.asset_type").
		Order("count DESC").
		Find(&assetTypeCounts).Error; err != nil {
		return nil, err
	}

	// 将业务状态数量转换为map
	bizStatusMap := make(map[string]int64)
	for _, sc := range bizStatusCounts {
		bizStatusMap[sc.Status] = sc.Count
	}

	// 将资源状态数量转换为map
	resStatusMap := make(map[string]int64)
	for _, sc := range resStatusCounts {
		resStatusMap[sc.Status] = sc.Count
	}

	// 将资产类型数量转换为map，便于前端快速访问
	assetTypeMap := make(map[string]int64)
	for _, atc := range assetTypeCounts {
		assetTypeMap[atc.AssetType] = atc.Count
	}

	// 准备资产类型图表数据
	assetTypeChartData := make([]map[string]interface{}, 0, len(assetTypeCounts))
	for _, atc := range assetTypeCounts {
		assetTypeChartData = append(assetTypeChartData, map[string]interface{}{
			"name":  atc.AssetType,
			"value": atc.Count,
		})
	}

	// 返回统计结果
	return map[string]interface{}{
		"total_count":           totalCount,
		"biz_status_stats":      bizStatusCounts,
		"res_status_stats":      resStatusCounts,
		"asset_type_stats":      assetTypeCounts,
		"asset_type_chart_data": assetTypeChartData,
		"backup_count":          backupCount,
		"active_count":          bizStatusMap["active"],
		"maintaining_count":     bizStatusMap["maintaining"],
		"outage_count":          bizStatusMap["outage"],
		"allocated_count":       resStatusMap["allocated"],
		"unallocated_count":     resStatusMap["unallocated"],
		"server_count":          assetTypeMap["server"],
		"gpu_server_count":      assetTypeMap["gpu_server"],
		"network_count":         assetTypeMap["network"],
		"storage_count":         assetTypeMap["storage"],
		"switch_count":          assetTypeMap["switch"],
		"router_count":          assetTypeMap["router"],
		"firewall_count":        assetTypeMap["firewall"],
	}, nil
}

// GetClusterStats 获取各集群类型的数量
func (r *resourceStatsRepository) GetClusterStats(ctx context.Context) (map[string]interface{}, error) {
	type ClusterCount struct {
		Cluster string `json:"cluster"`
		Count   int64  `json:"count"`
	}

	var clusterCounts []ClusterCount
	if err := r.db.WithContext(ctx).Table("resources").
		Select("cluster, COUNT(*) as count").
		Where("deleted_at IS NULL AND cluster != ''").
		Group("cluster").
		Order("count DESC").
		Find(&clusterCounts).Error; err != nil {
		return nil, err
	}

	// 计算集群总数（唯一的集群类型数）
	var uniqueClusterCount int64
	if err := r.db.WithContext(ctx).Table("resources").
		Where("deleted_at IS NULL AND cluster != ''").
		Distinct("cluster").
		Count(&uniqueClusterCount).Error; err != nil {
		return nil, err
	}

	// 准备图表数据
	chartData := make([]map[string]interface{}, 0, len(clusterCounts))
	for _, cc := range clusterCounts {
		chartData = append(chartData, map[string]interface{}{
			"name":  cc.Cluster,
			"value": cc.Count,
		})
	}

	return map[string]interface{}{
		"cluster_counts":       clusterCounts,
		"unique_cluster_count": uniqueClusterCount,
		"chart_data":           chartData,
	}, nil
}

// GetServerStatsByProject 按项目获取服务器数量（支持时间范围参数）
func (r *resourceStatsRepository) GetServerStatsByProject(ctx context.Context, project string, startDate, endDate *time.Time) (map[string]interface{}, error) {
	// 如果指定了时间范围，检查是否为单个月份的查询
	if startDate != nil && endDate != nil {
		year, month, isFullMonth := r.extractMonthFromDateRange(*startDate, *endDate)
		if isFullMonth {
			hasStats, err := r.hasMonthlyStats(ctx, year, month)
			if err != nil {
				return nil, fmt.Errorf("检查月度统计数据失败: %w", err)
			}

			// 如果有月度统计数据，使用月度统计数据
			if hasStats {
				return r.getServerStatsByProjectFromMonthlyStats(ctx, project, year, month)
			}
		}
	}
	type ProjectCount struct {
		Project string `json:"project"`
		Count   int64  `json:"count"`
	}

	var projectCounts []ProjectCount
	query := r.db.WithContext(ctx).Table("resources").
		Select("project, COUNT(*) as count").
		Joins("LEFT JOIN asset_devices ON resources.asset_id = asset_devices.id").
		Where("resources.deleted_at IS NULL AND project != '' AND asset_devices.asset_status != ?", "scrapped")

	// 如果指定了项目，只获取该项目的数据
	if project != "" {
		query = query.Where("project = ?", project)
	}

	// 执行查询
	if err := query.Group("project").
		Order("count DESC").
		Find(&projectCounts).Error; err != nil {
		return nil, err
	}

	// 计算有服务器的项目数（唯一的项目数）
	var uniqueProjectCount int64
	countQuery := r.db.WithContext(ctx).Table("resources").
		Joins("LEFT JOIN asset_devices ON resources.asset_id = asset_devices.id").
		Where("resources.deleted_at IS NULL AND project != '' AND asset_devices.asset_status != ?", "scrapped")

	// 如果指定了项目，只计算该项目
	if project != "" {
		countQuery = countQuery.Where("project = ?", project)
	}

	if err := countQuery.Distinct("project").
		Count(&uniqueProjectCount).Error; err != nil {
		return nil, err
	}

	// 查询每个项目下各业务状态的服务器数量
	type ProjectStatusCount struct {
		Project string `json:"project"`
		Status  string `json:"status"`
		Count   int64  `json:"count"`
	}

	var projectBizStatusCounts []ProjectStatusCount
	statusQuery := r.db.WithContext(ctx).Table("resources").
		Select("project, biz_status as status, COUNT(*) as count").
		Joins("LEFT JOIN asset_devices ON resources.asset_id = asset_devices.id").
		Where("resources.deleted_at IS NULL AND project != '' AND asset_devices.asset_status != ?", "scrapped")

	// 如果指定了项目，只获取该项目的状态数据
	if project != "" {
		statusQuery = statusQuery.Where("project = ?", project)
	}

	if err := statusQuery.Group("project, biz_status").
		Find(&projectBizStatusCounts).Error; err != nil {
		return nil, err
	}

	// 查询每个项目下各资产类型的服务器数量
	type ProjectAssetTypeCount struct {
		Project   string `json:"project"`
		AssetType string `json:"asset_type"`
		Count     int64  `json:"count"`
	}

	var projectAssetTypeCounts []ProjectAssetTypeCount
	assetTypeQuery := r.db.WithContext(ctx).Table("resources").
		Select("resources.project, asset_devices.asset_type as asset_type, COUNT(*) as count").
		Joins("LEFT JOIN asset_devices ON resources.asset_id = asset_devices.id").
		Where("resources.deleted_at IS NULL AND resources.project != '' AND asset_devices.asset_status != ?", "scrapped")

	// 如果指定了项目，只获取该项目的资产类型数据
	if project != "" {
		assetTypeQuery = assetTypeQuery.Where("resources.project = ?", project)
	}

	if err := assetTypeQuery.Group("resources.project, asset_devices.asset_type").
		Order("resources.project, count DESC").
		Find(&projectAssetTypeCounts).Error; err != nil {
		return nil, err
	}

	// 准备图表数据
	chartData := make([]map[string]interface{}, 0, len(projectCounts))
	for _, pc := range projectCounts {
		chartData = append(chartData, map[string]interface{}{
			"name":  pc.Project,
			"value": pc.Count,
		})
	}

	// 按项目整理状态数据
	projectStatusMap := make(map[string]map[string]int64)
	for _, psc := range projectBizStatusCounts {
		if _, exists := projectStatusMap[psc.Project]; !exists {
			projectStatusMap[psc.Project] = make(map[string]int64)
		}
		projectStatusMap[psc.Project][psc.Status] = psc.Count
	}

	// 按项目整理资产类型数据
	projectAssetTypeMap := make(map[string]map[string]int64)
	for _, patc := range projectAssetTypeCounts {
		if _, exists := projectAssetTypeMap[patc.Project]; !exists {
			projectAssetTypeMap[patc.Project] = make(map[string]int64)
		}
		projectAssetTypeMap[patc.Project][patc.AssetType] = patc.Count
	}

	// 按项目准备资产类型图表数据
	projectAssetTypeChartData := make(map[string][]map[string]interface{})
	for _, patc := range projectAssetTypeCounts {
		if _, exists := projectAssetTypeChartData[patc.Project]; !exists {
			projectAssetTypeChartData[patc.Project] = make([]map[string]interface{}, 0)
		}
		projectAssetTypeChartData[patc.Project] = append(projectAssetTypeChartData[patc.Project], map[string]interface{}{
			"name":  patc.AssetType,
			"value": patc.Count,
		})
	}

	return map[string]interface{}{
		"project_counts":                projectCounts,
		"unique_project_count":          uniqueProjectCount,
		"chart_data":                    chartData,
		"project_status_stats":          projectStatusMap,
		"project_asset_type_stats":      projectAssetTypeMap,
		"project_asset_type_chart_data": projectAssetTypeChartData,
		"raw_asset_type_data":           projectAssetTypeCounts,
	}, nil
}

// GetClusterStatsByProject 按项目获取集群数量（支持时间范围参数）
func (r *resourceStatsRepository) GetClusterStatsByProject(ctx context.Context, project string, startDate, endDate *time.Time) (map[string]interface{}, error) {
	// 如果指定了时间范围，检查是否为单个月份的查询
	if startDate != nil && endDate != nil {
		year, month, isFullMonth := r.extractMonthFromDateRange(*startDate, *endDate)
		if isFullMonth {
			hasStats, err := r.hasMonthlyStats(ctx, year, month)
			if err != nil {
				return nil, fmt.Errorf("检查月度统计数据失败: %w", err)
			}

			// 如果有月度统计数据，使用月度统计数据
			if hasStats {
				return r.getClusterStatsByProjectFromMonthlyStats(ctx, project, year, month)
			}
		}
	}
	type ProjectClusterCount struct {
		Project string `json:"project"`
		Cluster string `json:"cluster"`
		Count   int64  `json:"count"`
	}

	var projectClusterCounts []ProjectClusterCount
	query := r.db.WithContext(ctx).Table("resources").
		Select("project, cluster, COUNT(*) as count").
		Joins("LEFT JOIN asset_devices ON resources.asset_id = asset_devices.id").
		Where("resources.deleted_at IS NULL AND project != '' AND cluster != '' AND asset_devices.asset_status != ?", "scrapped")

	// 如果指定了项目，只获取该项目的数据
	if project != "" {
		query = query.Where("project = ?", project)
	}

	if err := query.Group("project, cluster").
		Order("project, count DESC").
		Find(&projectClusterCounts).Error; err != nil {
		return nil, err
	}

	// 按项目整理集群数据
	projectClusterMap := make(map[string][]map[string]interface{})
	for _, pcc := range projectClusterCounts {
		if _, exists := projectClusterMap[pcc.Project]; !exists {
			projectClusterMap[pcc.Project] = make([]map[string]interface{}, 0)
		}

		projectClusterMap[pcc.Project] = append(projectClusterMap[pcc.Project], map[string]interface{}{
			"cluster": pcc.Cluster,
			"count":   pcc.Count,
		})
	}

	// 统计每个项目下的集群数量
	projectClusterCountMap := make(map[string]int)
	for project, clusters := range projectClusterMap {
		projectClusterCountMap[project] = len(clusters)
	}

	return map[string]interface{}{
		"project_cluster_stats":  projectClusterMap,
		"project_cluster_counts": projectClusterCountMap,
		"raw_data":               projectClusterCounts,
	}, nil
}

// GetBrandDistribution 获取设备品牌分布
func (r *resourceStatsRepository) GetBrandDistribution(ctx context.Context, project string) (map[string]interface{}, error) {
	type BrandCount struct {
		Brand string `json:"brand"`
		Count int64  `json:"count"`
	}

	var brandCounts []BrandCount
	query := r.db.WithContext(ctx).Table("asset_devices")

	// 只获取GPU服务器类型的数据
	if project != "" {
		query = query.Joins("LEFT JOIN resources ON asset_devices.id = resources.asset_id").
			Where("asset_devices.deleted_at IS NULL AND asset_devices.brand != '' AND asset_devices.asset_type = ? AND resources.project = ?", "gpu_server", project)
	} else {
		query = query.Where("deleted_at IS NULL AND brand != '' AND asset_devices.asset_status != ? AND asset_type = ?", "scrapped", "gpu_server")
	}

	if err := query.Select("brand, COUNT(*) as count").
		Group("brand").
		Order("count DESC").
		Find(&brandCounts).Error; err != nil {
		return nil, err
	}

	// 计算总品牌数
	var totalBrands int64
	countQuery := r.db.WithContext(ctx).Table("asset_devices")

	// 只获取GPU服务器类型的数据
	if project != "" {
		countQuery = countQuery.Joins("LEFT JOIN resources ON asset_devices.id = resources.asset_id").
			Where("asset_devices.deleted_at IS NULL AND asset_devices.brand != '' AND asset_devices.asset_type = ? AND resources.project = ?", "gpu_server", project)
	} else {
		countQuery = countQuery.Where("deleted_at IS NULL AND brand != '' AND asset_devices.asset_status != ? AND asset_type = ?", "scrapped", "gpu_server")
	}

	if err := countQuery.Distinct("brand").
		Count(&totalBrands).Error; err != nil {
		return nil, err
	}

	// 按资产类型查询品牌分布 (由于只查询gpu_server类型，这里不再需要按资产类型分组)

	// 准备图表数据
	chartData := make([]map[string]interface{}, 0, len(brandCounts))
	for _, bc := range brandCounts {
		chartData = append(chartData, map[string]interface{}{
			"name":  bc.Brand,
			"value": bc.Count,
		})
	}

	// 返回统计结果
	return map[string]interface{}{
		"brand_counts": brandCounts,
		"total_brands": totalBrands,
		"chart_data":   chartData,
		"project":      project,
		"asset_type":   "gpu_server", // 标明只返回了GPU服务器数据
	}, nil
}

// GetRoomDistribution 获取服务器机房分布
func (r *resourceStatsRepository) GetRoomDistribution(ctx context.Context, project string) (map[string]interface{}, error) {
	type RoomCount struct {
		RoomID   uint   `json:"room_id"`
		RoomName string `json:"room_name"`
		Count    int64  `json:"count"`
	}

	var roomCounts []RoomCount
	query := r.db.WithContext(ctx).Table("resources").
		Select("resources.room_id, rooms.name as room_name, COUNT(*) as count").
		Joins("LEFT JOIN rooms ON resources.room_id = rooms.id").
		Joins("LEFT JOIN asset_devices ON resources.asset_id = asset_devices.id").
		// 只统计GPU服务器
		Where("resources.deleted_at IS NULL AND resources.room_id > 0 AND asset_devices.asset_status != ? AND asset_devices.asset_type = ?", "scrapped", "gpu_server")

	// 如果指定了项目，添加项目过滤条件
	if project != "" {
		query = query.Where("resources.project = ?", project)
	}

	if err := query.Group("resources.room_id, rooms.name").
		Order("count DESC").
		Find(&roomCounts).Error; err != nil {
		return nil, err
	}

	// 查询数据中心信息
	type RoomDataCenterInfo struct {
		RoomID         uint   `json:"room_id"`
		DataCenterID   uint   `json:"data_center_id"`
		DataCenterName string `json:"data_center_name"`
	}

	var roomDataCenterInfos []RoomDataCenterInfo
	if err := r.db.WithContext(ctx).Table("rooms").
		Select("rooms.id as room_id, data_centers.id as data_center_id, data_centers.name as data_center_name").
		Joins("LEFT JOIN data_centers ON rooms.data_center_id = data_centers.id").
		Where("rooms.deleted_at IS NULL").
		Find(&roomDataCenterInfos).Error; err != nil {
		return nil, err
	}

	// 创建机房与数据中心的映射
	roomDataCenterMap := make(map[uint]RoomDataCenterInfo)
	for _, info := range roomDataCenterInfos {
		roomDataCenterMap[info.RoomID] = info
	}

	// 准备图表数据
	chartData := make([]map[string]interface{}, 0, len(roomCounts))
	for _, rc := range roomCounts {
		chartData = append(chartData, map[string]interface{}{
			"name":  rc.RoomName,
			"value": rc.Count,
		})
	}

	// 增强机房统计信息，添加数据中心信息
	enhancedRoomCounts := make([]map[string]interface{}, 0, len(roomCounts))
	for _, rc := range roomCounts {
		dcInfo := roomDataCenterMap[rc.RoomID]
		enhancedRoomCounts = append(enhancedRoomCounts, map[string]interface{}{
			"room_id":          rc.RoomID,
			"room_name":        rc.RoomName,
			"count":            rc.Count,
			"data_center_id":   dcInfo.DataCenterID,
			"data_center_name": dcInfo.DataCenterName,
		})
	}

	// 按数据中心统计机房分布
	dataCenterDistribution := make(map[uint]map[string]interface{})
	for _, rc := range roomCounts {
		dcInfo := roomDataCenterMap[rc.RoomID]
		dcID := dcInfo.DataCenterID

		if _, exists := dataCenterDistribution[dcID]; !exists {
			dataCenterDistribution[dcID] = map[string]interface{}{
				"data_center_id":   dcID,
				"data_center_name": dcInfo.DataCenterName,
				"total_count":      int64(0),
				"rooms":            make([]map[string]interface{}, 0),
			}
		}

		// 增加当前数据中心的总数
		totalCount, ok := dataCenterDistribution[dcID]["total_count"].(int64)
		if !ok {
			// 如果类型断言失败，初始化为0
			totalCount = 0
		}
		dataCenterDistribution[dcID]["total_count"] = totalCount + rc.Count

		// 添加房间信息
		roomsInterface, ok := dataCenterDistribution[dcID]["rooms"]
		if !ok {
			return nil, fmt.Errorf("数据中心 %d 的房间信息缺失", dcID)
		}

		rooms, ok := roomsInterface.([]map[string]interface{})
		if !ok {
			return nil, fmt.Errorf("数据中心 %d 的房间信息格式错误", dcID)
		}

		rooms = append(rooms, map[string]interface{}{
			"room_id":   rc.RoomID,
			"room_name": rc.RoomName,
			"count":     rc.Count,
		})
		dataCenterDistribution[dcID]["rooms"] = rooms
	}

	// 转换为数组
	dcDistributionArray := make([]map[string]interface{}, 0, len(dataCenterDistribution))
	for _, dcDist := range dataCenterDistribution {
		dcDistributionArray = append(dcDistributionArray, dcDist)
	}

	return map[string]interface{}{
		"room_counts":       enhancedRoomCounts,
		"chart_data":        chartData,
		"data_center_stats": dcDistributionArray,
		"project":           project,
		"asset_type":        "gpu_server", // 标明只返回了GPU服务器数据
	}, nil
}

// GetGpuModelDistribution 获取GPU卡型号分布
func (r *resourceStatsRepository) GetGpuModelDistribution(ctx context.Context, project string) (map[string]interface{}, error) {
	type GpuModelCount struct {
		GpuModel string `json:"gpu_model"`
		Count    int64  `json:"count"`
	}

	// 从模板中查询有GPU型号的模板数据
	var gpuModelCounts []GpuModelCount
	if err := r.db.WithContext(ctx).Table("machine_templates").
		Select("gpu_model, COUNT(*) as count").
		Where("deleted_at IS NULL AND gpu_model != ''").
		Group("gpu_model").
		Order("count DESC").
		Find(&gpuModelCounts).Error; err != nil {
		return nil, err
	}

	// 统计使用不同GPU型号的资源数量
	type ResourceGpuModelCount struct {
		GpuModel string `json:"gpu_model"`
		Count    int64  `json:"count"`
	}

	var resourceGpuModelCounts []ResourceGpuModelCount
	gpuQuery := r.db.WithContext(ctx).Table("resources").
		Select("machine_templates.gpu_model, COUNT(*) as count").
		Joins("LEFT JOIN asset_devices ON resources.asset_id = asset_devices.id").
		Joins("LEFT JOIN machine_templates ON asset_devices.template_id = machine_templates.id").
		// 只统计GPU服务器
		Where("resources.deleted_at IS NULL AND machine_templates.gpu_model != '' AND asset_devices.asset_status != ? AND asset_devices.asset_type = ?", "scrapped", "gpu_server")

	// 如果指定了项目，添加项目过滤条件
	if project != "" {
		gpuQuery = gpuQuery.Where("resources.project = ?", project)
	}

	if err := gpuQuery.Group("machine_templates.gpu_model").
		Order("count DESC").
		Find(&resourceGpuModelCounts).Error; err != nil {
		return nil, err
	}

	// 按项目统计GPU型号分布
	type ProjectGpuModelCount struct {
		Project  string `json:"project"`
		GpuModel string `json:"gpu_model"`
		Count    int64  `json:"count"`
	}

	var projectGpuModelCounts []ProjectGpuModelCount
	projectQuery := r.db.WithContext(ctx).Table("resources").
		Select("resources.project, machine_templates.gpu_model, COUNT(*) as count").
		Joins("LEFT JOIN asset_devices ON resources.asset_id = asset_devices.id").
		Joins("LEFT JOIN machine_templates ON asset_devices.template_id = machine_templates.id").
		// 只统计GPU服务器
		Where("resources.deleted_at IS NULL AND resources.project != '' AND machine_templates.gpu_model != '' AND asset_devices.asset_status != ? AND asset_devices.asset_type = ?", "scrapped", "gpu_server")

	// 如果指定了项目，添加项目过滤条件
	if project != "" {
		projectQuery = projectQuery.Where("resources.project = ?", project)
	}

	if err := projectQuery.Group("resources.project, machine_templates.gpu_model").
		Order("resources.project, count DESC").
		Find(&projectGpuModelCounts).Error; err != nil {
		return nil, err
	}

	// 准备图表数据
	chartData := make([]map[string]interface{}, 0, len(resourceGpuModelCounts))
	for _, gmc := range resourceGpuModelCounts {
		chartData = append(chartData, map[string]interface{}{
			"name":  gmc.GpuModel,
			"value": gmc.Count,
		})
	}

	// 按项目整理GPU型号数据
	projectGpuModelMap := make(map[string][]map[string]interface{})
	for _, pgmc := range projectGpuModelCounts {
		if _, exists := projectGpuModelMap[pgmc.Project]; !exists {
			projectGpuModelMap[pgmc.Project] = make([]map[string]interface{}, 0)
		}
		projectGpuModelMap[pgmc.Project] = append(projectGpuModelMap[pgmc.Project], map[string]interface{}{
			"gpu_model": pgmc.GpuModel,
			"count":     pgmc.Count,
		})
	}

	// 统计各项目的GPU服务器总数
	type ProjectGpuServerCount struct {
		Project string `json:"project"`
		Count   int64  `json:"count"`
	}

	var projectGpuServerCounts []ProjectGpuServerCount
	serverQuery := r.db.WithContext(ctx).Table("resources").
		Select("resources.project, COUNT(*) as count").
		Joins("LEFT JOIN asset_devices ON resources.asset_id = asset_devices.id").
		Where("resources.deleted_at IS NULL AND resources.project != '' AND asset_devices.asset_status != ? AND asset_devices.asset_type = ?", "scrapped", "gpu_server")

	// 如果指定了项目，添加项目过滤条件
	if project != "" {
		serverQuery = serverQuery.Where("resources.project = ?", project)
	}

	if err := serverQuery.Group("resources.project").
		Order("count DESC").
		Find(&projectGpuServerCounts).Error; err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"template_gpu_model_counts": gpuModelCounts,
		"resource_gpu_model_counts": resourceGpuModelCounts,
		"chart_data":                chartData,
		"project_gpu_model_stats":   projectGpuModelMap,
		"project_gpu_server_counts": projectGpuServerCounts,
		"raw_data":                  projectGpuModelCounts,
		"project":                   project,
		"asset_type":                "gpu_server", // 标明只返回了GPU服务器数据
	}, nil
}

// hasMonthlyStats 检查指定年月是否有月度统计数据
func (r *resourceStatsRepository) hasMonthlyStats(ctx context.Context, year, month int) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.GPUServerMonthlyStats{}).
		Where("year = ? AND month = ?", year, month).
		Count(&count).Error

	return count > 0, err
}

// getServerStatsByProjectFromMonthlyStats 从月度统计数据获取服务器统计
func (r *resourceStatsRepository) getServerStatsByProjectFromMonthlyStats(ctx context.Context, project string, year, month int) (map[string]interface{}, error) {
	var stats []model.GPUServerMonthlyStats
	query := r.db.WithContext(ctx).Where("year = ? AND month = ?", year, month)

	if project != "" {
		query = query.Where("project = ?", project)
	}

	err := query.Find(&stats).Error
	if err != nil {
		return nil, err
	}

	// 按项目聚合数据
	projectStatsMap := make(map[string]*model.MonthlyStatsProjectInfo)
	for _, stat := range stats {
		if _, exists := projectStatsMap[stat.Project]; !exists {
			projectStatsMap[stat.Project] = &model.MonthlyStatsProjectInfo{
				Project:         stat.Project,
				TotalGPUServers: stat.TotalGPUServers,
				Clusters:        []model.MonthlyStatsClusterInfo{},
			}
		}

		projectStatsMap[stat.Project].Clusters = append(projectStatsMap[stat.Project].Clusters, model.MonthlyStatsClusterInfo{
			ClusterName:     stat.ClusterName,
			ClusterGPUCount: stat.ClusterGPUCount,
		})
	}

	// 转换为返回格式
	projectCounts := make([]map[string]interface{}, 0)
	projectAssetTypeCounts := make([]map[string]interface{}, 0)

	for projectName, projectInfo := range projectStatsMap {
		projectCounts = append(projectCounts, map[string]interface{}{
			"project": projectName,
			"count":   projectInfo.TotalGPUServers,
		})

		// 添加GPU服务器类型统计
		projectAssetTypeCounts = append(projectAssetTypeCounts, map[string]interface{}{
			"project":    projectName,
			"asset_type": "gpu_server",
			"count":      projectInfo.TotalGPUServers,
		})
	}

	return map[string]interface{}{
		"project_counts":           projectCounts,
		"project_asset_type_stats": projectAssetTypeCounts,
		"data_source":              "monthly_stats",
		"year":                     year,
		"month":                    month,
	}, nil
}

// getClusterStatsByProjectFromMonthlyStats 从月度统计数据获取集群统计
func (r *resourceStatsRepository) getClusterStatsByProjectFromMonthlyStats(ctx context.Context, project string, year, month int) (map[string]interface{}, error) {
	var stats []model.GPUServerMonthlyStats
	query := r.db.WithContext(ctx).Where("year = ? AND month = ?", year, month)

	if project != "" {
		query = query.Where("project = ?", project)
	}

	err := query.Find(&stats).Error
	if err != nil {
		return nil, err
	}

	// 按项目整理集群数据
	projectClusterMap := make(map[string][]map[string]interface{})
	for _, stat := range stats {
		if _, exists := projectClusterMap[stat.Project]; !exists {
			projectClusterMap[stat.Project] = make([]map[string]interface{}, 0)
		}

		projectClusterMap[stat.Project] = append(projectClusterMap[stat.Project], map[string]interface{}{
			"cluster": stat.ClusterName,
			"count":   stat.ClusterGPUCount,
		})
	}

	return map[string]interface{}{
		"project_cluster_stats": projectClusterMap,
		"data_source":           "monthly_stats",
		"year":                  year,
		"month":                 month,
	}, nil
}

// extractMonthFromDateRange 从日期范围中提取年月信息，判断是否为完整月份查询
func (r *resourceStatsRepository) extractMonthFromDateRange(startDate, endDate time.Time) (year, month int, isFullMonth bool) {
	// 检查是否为同一年同一月
	if startDate.Year() != endDate.Year() || startDate.Month() != endDate.Month() {
		return 0, 0, false
	}

	year = startDate.Year()
	month = int(startDate.Month())

	// 检查是否为完整月份查询
	// 开始日期应该是月初（1号）
	if startDate.Day() != 1 {
		return year, month, false
	}

	// 结束日期应该是月末
	nextMonth := startDate.AddDate(0, 1, 0)
	lastDayOfMonth := nextMonth.Add(-24 * time.Hour)

	// 允许一定的时间误差（比如23:59:59）
	if endDate.Year() == lastDayOfMonth.Year() &&
	   endDate.Month() == lastDayOfMonth.Month() &&
	   endDate.Day() == lastDayOfMonth.Day() {
		return year, month, true
	}

	return year, month, false
}
