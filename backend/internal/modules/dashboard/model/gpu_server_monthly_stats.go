package model

import (
	"time"

	"gorm.io/gorm"
)

// GPUServerMonthlyStats GPU服务器月度统计模型
type GPUServerMonthlyStats struct {
	ID               uint      `json:"id" gorm:"primaryKey"`
	Year             int       `json:"year" gorm:"not null;comment:年份"`
	Month            int       `json:"month" gorm:"not null;comment:月份"`
	Project          string    `json:"project" gorm:"size:100;not null;comment:项目名称"`
	TotalGPUServers  int       `json:"total_gpu_servers" gorm:"not null;default:0;comment:总GPU服务器数量"`
	ClusterName      string    `json:"cluster_name" gorm:"size:100;not null;comment:集群名称"`
	ClusterGPUCount  int       `json:"cluster_gpu_count" gorm:"not null;default:0;comment:集群GPU服务器数量"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// TableName 指定表名
func (GPUServerMonthlyStats) TableName() string {
	return "gpu_server_monthly_stats"
}

// MonthlyStatsClusterInfo 月度统计集群信息
type MonthlyStatsClusterInfo struct {
	ClusterName     string `json:"cluster_name"`
	ClusterGPUCount int    `json:"cluster_gpu_count"`
}

// MonthlyStatsProjectInfo 月度统计项目信息
type MonthlyStatsProjectInfo struct {
	Project         string                    `json:"project"`
	TotalGPUServers int                       `json:"total_gpu_servers"`
	Clusters        []MonthlyStatsClusterInfo `json:"clusters"`
}

// InitializeGPUServerMonthlyStatsData 初始化GPU服务器月度统计数据
func InitializeGPUServerMonthlyStatsData(db *gorm.DB) error {
	// 检查是否已有数据
	var count int64
	if err := db.Model(&GPUServerMonthlyStats{}).Count(&count).Error; err != nil {
		return err
	}

	// 如果已有数据，跳过初始化
	if count > 0 {
		return nil
	}

	// 初始化示例数据
	initialData := []GPUServerMonthlyStats{
		// 2025年1月cloud17项目的数据
		{Year: 2025, Month: 1, Project: "cloud17", TotalGPUServers: 773, ClusterName: "GPU_Cluster_G05_RoCE_EMR", ClusterGPUCount: 395, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{Year: 2025, Month: 1, Project: "cloud17", TotalGPUServers: 773, ClusterName: "GPU_Cluster_G05_RoCE_SPR", ClusterGPUCount: 378, CreatedAt: time.Now(), UpdatedAt: time.Now()},

		// 2025年2月cloud17项目的数据
		{Year: 2025, Month: 2, Project: "cloud17", TotalGPUServers: 778, ClusterName: "GPU_Cluster_G05_RoCE_EMR", ClusterGPUCount: 400, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{Year: 2025, Month: 2, Project: "cloud17", TotalGPUServers: 778, ClusterName: "GPU_Cluster_G05_RoCE_SPR", ClusterGPUCount: 378, CreatedAt: time.Now(), UpdatedAt: time.Now()},

		// 2025年3月cloud17项目的数据
		{Year: 2025, Month: 3, Project: "cloud17", TotalGPUServers: 1022, ClusterName: "GPU_Cluster_G05_RoCE_EMR", ClusterGPUCount: 644, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{Year: 2025, Month: 3, Project: "cloud17", TotalGPUServers: 1022, ClusterName: "GPU_Cluster_G05_RoCE_SPR", ClusterGPUCount: 378, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	// 批量插入数据
	return db.Create(&initialData).Error
}
