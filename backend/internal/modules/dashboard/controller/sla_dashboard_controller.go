package controller

import (
	"backend/internal/modules/dashboard/service"
	"backend/response"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// DashboardController 看板控制器
type DashboardController struct {
	service service.DashboardService
}

// NewDashboardController 创建看板控制器
func NewDashboardController(service service.DashboardService) *DashboardController {
	return &DashboardController{service: service}
}

// RegisterRoutes 注册路由
func (c *DashboardController) RegisterRoutes(router *gin.RouterGroup) {
	dashboardRouter := router.Group("")
	{
		// 获取报障单时间分布
		dashboardRouter.GET("/ticket/time-stats", c.GetTicketTimeStats)

		// 获取故障类型分布
		dashboardRouter.GET("/ticket/fault-types", c.GetFaultTypeStats)

		// 获取故障来源分布
		dashboardRouter.GET("/ticket/source-stats", c.GetSourceStats)

		// 获取具体故障类型分布
		dashboardRouter.GET("/ticket/fault-detail-types", c.GetFaultDetailTypeStats)

		// 获取报障单状态分布
		dashboardRouter.GET("/ticket/status-stats", c.GetStatusStats)

		// 获取处理时长统计
		dashboardRouter.GET("/ticket/duration-stats", c.GetDurationStats)

		// 获取SLA达标情况
		dashboardRouter.GET("/ticket/sla-stats", c.GetSLAStats)

		// 获取计入SLA且已完成报障单的业务影响总时长
		dashboardRouter.GET("/ticket/business-impact", c.GetBusinessImpactTotalTime)

		// 获取GPU卡故障比
		dashboardRouter.GET("/ticket/gpu-fault-ratio", c.GetGPUFaultRatio)

		// 获取项目故障单详情列表
		dashboardRouter.GET("/ticket/fault-details", c.GetFaultTicketDetails)

		// 获取故障总台数
		dashboardRouter.GET("/ticket/total-devices", c.GetTotalFaultDevices)

		// 获取工程师响应和修复时长统计
		dashboardRouter.GET("/ticket/engineer-stats", c.GetEngineerResponseAndFixTimeStats)

		// 获取故障设备厂商分布
		dashboardRouter.GET("/ticket/brand-distribution", c.GetFaultDeviceBrandDistribution)

		// 获取每天故障处理总时长趋势
		dashboardRouter.GET("/ticket/daily-impact-trend", c.GetDailyBusinessImpactTrend)

		// 获取每天硬件故障次数趋势
		dashboardRouter.GET("/ticket/hardware-fault-trend", c.GetHardwareFaultTrend)

		// 获取资源变更统计
		dashboardRouter.GET("/ticket/resource-changes", c.GetResourceChangesStats)
	}
}

// GetTicketTimeStats 获取报障单时间分布统计
// @Summary 获取报障单时间分布统计
// @Description 获取报障单的创建时间分布，支持按天、周、月、年统计
// @Tags 看板-工单统计
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围(today/week/month/year/custom)"
// @Param start_date query string false "开始日期，格式YYYY-MM-DD"
// @Param end_date query string false "结束日期，格式YYYY-MM-DD"
// @Param group_by query string false "分组方式(day/week/month)"
// @Param project query string false "项目名称，为空时返回全部项目数据"
// @Param count_in_sla query boolean false "是否只统计计入SLA的工单，默认为true"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/ticket/time-stats [get]
func (c *DashboardController) GetTicketTimeStats(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "month")
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")
	groupBy := ctx.DefaultQuery("group_by", "day")
	project := ctx.Query("project")
	countInSLAStr := ctx.DefaultQuery("count_in_sla", "true")

	// 处理自定义日期范围
	var startDate, endDate time.Time
	var err error

	if timeRange == "custom" {
		if startDateStr != "" {
			startDate, err = time.Parse("2006-01-02", startDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "开始日期格式错误")
				return
			}
		}

		if endDateStr != "" {
			endDate, err = time.Parse("2006-01-02", endDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "结束日期格式错误")
				return
			}
		}
	}

	// 处理count_in_sla参数
	var countInSLA *bool
	if countInSLAStr != "" {
		countInSLAValue := countInSLAStr == "true"
		countInSLA = &countInSLAValue
	}

	stats, err := c.service.GetTicketTimeStats(ctx, timeRange, startDate, endDate, groupBy, project, countInSLA)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取报障单时间统计失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取报障单时间统计成功")
}

// GetFaultTypeStats 获取故障类型分布统计
// @Summary 获取故障类型分布统计
// @Description 获取报障单的故障类型分布
// @Tags 看板-工单统计
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围(today/week/month/year/custom)"
// @Param start_date query string false "开始日期，格式YYYY-MM-DD"
// @Param end_date query string false "结束日期，格式YYYY-MM-DD"
// @Param project query string false "项目名称，为空时返回全部项目数据"
// @Param count_in_sla query boolean false "是否只统计计入SLA的工单，默认为true"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/ticket/fault-types [get]
func (c *DashboardController) GetFaultTypeStats(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "month")
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")
	project := ctx.Query("project")
	countInSLAStr := ctx.DefaultQuery("count_in_sla", "true")

	// 处理自定义日期范围
	var startDate, endDate time.Time
	var err error

	if timeRange == "custom" {
		if startDateStr != "" {
			startDate, err = time.Parse("2006-01-02", startDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "开始日期格式错误")
				return
			}
		}

		if endDateStr != "" {
			endDate, err = time.Parse("2006-01-02", endDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "结束日期格式错误")
				return
			}
		}
	}

	// 处理count_in_sla参数
	var countInSLA *bool
	if countInSLAStr != "" {
		countInSLAValue := countInSLAStr == "true"
		countInSLA = &countInSLAValue
	}

	stats, err := c.service.GetFaultTypeStats(ctx, timeRange, startDate, endDate, project, countInSLA)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取故障类型统计失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取故障类型统计成功")
}

// GetSourceStats 获取故障来源分布统计
// @Summary 获取故障来源分布统计
// @Description 获取报障单的故障来源分布，customer为客户报障，其他为内部监控
// @Tags 看板-工单统计
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围(today/week/month/year/custom)"
// @Param start_date query string false "开始日期，格式YYYY-MM-DD"
// @Param end_date query string false "结束日期，格式YYYY-MM-DD"
// @Param project query string false "项目名称，为空时返回全部项目数据"
// @Param count_in_sla query boolean false "是否只统计计入SLA的工单，默认为true"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/ticket/source-stats [get]
func (c *DashboardController) GetSourceStats(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "month")
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")
	project := ctx.Query("project")
	countInSLAStr := ctx.DefaultQuery("count_in_sla", "true")

	// 处理自定义日期范围
	var startDate, endDate time.Time
	var err error

	if timeRange == "custom" {
		if startDateStr != "" {
			startDate, err = time.Parse("2006-01-02", startDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "开始日期格式错误")
				return
			}
		}

		if endDateStr != "" {
			endDate, err = time.Parse("2006-01-02", endDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "结束日期格式错误")
				return
			}
		}
	}

	// 处理count_in_sla参数
	var countInSLA *bool
	if countInSLAStr != "" {
		countInSLAValue := countInSLAStr == "true"
		countInSLA = &countInSLAValue
	}

	stats, err := c.service.GetSourceStats(ctx, timeRange, startDate, endDate, project, countInSLA)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取故障来源统计失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取故障来源统计成功")
}

// GetFaultDetailTypeStats 获取具体故障类型分布统计
// @Summary 获取具体故障类型分布统计
// @Description 获取报障单的具体故障类型(FaultDetailType)分布
// @Tags 看板-工单统计
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围(today/week/month/year/custom)"
// @Param start_date query string false "开始日期，格式YYYY-MM-DD"
// @Param end_date query string false "结束日期，格式YYYY-MM-DD"
// @Param project query string false "项目名称，为空时返回全部项目数据"
// @Param count_in_sla query boolean false "是否只统计计入SLA的工单，默认为true"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/ticket/fault-detail-types [get]
func (c *DashboardController) GetFaultDetailTypeStats(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "month")
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")
	project := ctx.Query("project")
	countInSLAStr := ctx.DefaultQuery("count_in_sla", "true")

	// 处理自定义日期范围
	var startDate, endDate time.Time
	var err error

	if timeRange == "custom" {
		if startDateStr != "" {
			startDate, err = time.Parse("2006-01-02", startDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "开始日期格式错误")
				return
			}
		}

		if endDateStr != "" {
			endDate, err = time.Parse("2006-01-02", endDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "结束日期格式错误")
				return
			}
		}
	}

	// 处理count_in_sla参数
	var countInSLA *bool
	if countInSLAStr != "" {
		countInSLAValue := countInSLAStr == "true"
		countInSLA = &countInSLAValue
	}

	stats, err := c.service.GetFaultDetailTypeStats(ctx, timeRange, startDate, endDate, project, countInSLA)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取具体故障类型统计失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取具体故障类型统计成功")
}

// GetStatusStats 获取报障单状态分布统计
// @Summary 获取报障单状态分布统计
// @Description 获取报障单的状态分布
// @Tags 看板-工单统计
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围(today/week/month/year/custom)"
// @Param start_date query string false "开始日期，格式YYYY-MM-DD"
// @Param end_date query string false "结束日期，格式YYYY-MM-DD"
// @Param project query string false "项目名称，为空时返回全部项目数据"
// @Param count_in_sla query boolean false "是否只统计计入SLA的工单，默认为true"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/ticket/status-stats [get]
func (c *DashboardController) GetStatusStats(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "month")
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")
	project := ctx.Query("project")
	countInSLAStr := ctx.DefaultQuery("count_in_sla", "true")

	// 处理自定义日期范围
	var startDate, endDate time.Time
	var err error

	if timeRange == "custom" {
		if startDateStr != "" {
			startDate, err = time.Parse("2006-01-02", startDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "开始日期格式错误")
				return
			}
		}

		if endDateStr != "" {
			endDate, err = time.Parse("2006-01-02", endDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "结束日期格式错误")
				return
			}
		}
	}

	// 处理count_in_sla参数
	var countInSLA *bool
	if countInSLAStr != "" {
		countInSLAValue := countInSLAStr == "true"
		countInSLA = &countInSLAValue
	}

	stats, err := c.service.GetStatusStats(ctx, timeRange, startDate, endDate, project, countInSLA)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取状态统计失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取状态统计成功")
}

// GetDurationStats 获取处理时长统计
// @Summary 获取处理时长统计
// @Description 获取报障单的处理时长统计
// @Tags 看板-工单统计
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围(today/week/month/year/custom)"
// @Param start_date query string false "开始日期，格式YYYY-MM-DD"
// @Param end_date query string false "结束日期，格式YYYY-MM-DD"
// @Param project query string false "项目名称，为空时返回全部项目数据"
// @Param count_in_sla query boolean false "是否只统计计入SLA的工单，默认为true"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/ticket/duration-stats [get]
func (c *DashboardController) GetDurationStats(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "month")
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")
	project := ctx.Query("project")
	countInSLAStr := ctx.DefaultQuery("count_in_sla", "true")

	// 处理自定义日期范围
	var startDate, endDate time.Time
	var err error

	if timeRange == "custom" {
		if startDateStr != "" {
			startDate, err = time.Parse("2006-01-02", startDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "开始日期格式错误")
				return
			}
		}

		if endDateStr != "" {
			endDate, err = time.Parse("2006-01-02", endDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "结束日期格式错误")
				return
			}
		}
	}

	// 处理count_in_sla参数
	var countInSLA *bool
	if countInSLAStr != "" {
		countInSLAValue := countInSLAStr == "true"
		countInSLA = &countInSLAValue
	}

	stats, err := c.service.GetDurationStats(ctx, timeRange, startDate, endDate, project, countInSLA)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取处理时长统计失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取处理时长统计成功")
}

// GetSLAStats 获取SLA达标情况
// @Summary 获取SLA达标情况
// @Description 获取报障单的SLA达标情况
// @Tags 看板-工单统计
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围(today/week/month/year/custom)"
// @Param start_date query string false "开始日期，格式YYYY-MM-DD"
// @Param end_date query string false "结束日期，格式YYYY-MM-DD"
// @Param project query string false "项目名称，为空时返回全部项目数据"
// @Param count_in_sla query boolean false "是否只统计计入SLA的工单，默认为true"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/ticket/sla-stats [get]
func (c *DashboardController) GetSLAStats(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "month")
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")
	project := ctx.Query("project")
	countInSLAStr := ctx.DefaultQuery("count_in_sla", "true")

	// 处理自定义日期范围
	var startDate, endDate time.Time
	var err error

	if timeRange == "custom" {
		if startDateStr != "" {
			startDate, err = time.Parse("2006-01-02", startDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "开始日期格式错误")
				return
			}
		}

		if endDateStr != "" {
			endDate, err = time.Parse("2006-01-02", endDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "结束日期格式错误")
				return
			}
		}
	}

	// 处理count_in_sla参数
	var countInSLA *bool
	if countInSLAStr != "" {
		countInSLAValue := countInSLAStr == "true"
		countInSLA = &countInSLAValue
	}

	stats, err := c.service.GetSLAStats(ctx, timeRange, startDate, endDate, project, countInSLA)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取SLA统计失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取SLA统计成功")
}

// GetBusinessImpactTotalTime 获取计入SLA且已完成报障单的业务影响总时长
// @Summary 获取业务影响总时长
// @Description 计算计入SLA且已完成的报障单的业务影响总时长
// @Tags 看板-工单统计
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围(today/week/month/year/custom)"
// @Param start_date query string false "开始日期，格式YYYY-MM-DD"
// @Param end_date query string false "结束日期，格式YYYY-MM-DD"
// @Param project query string false "项目名称，为空时返回全部项目数据"
// @Param count_in_sla query boolean false "是否只统计计入SLA的工单，默认为true"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/ticket/business-impact [get]
func (c *DashboardController) GetBusinessImpactTotalTime(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "month")
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")
	project := ctx.Query("project")
	countInSLAStr := ctx.DefaultQuery("count_in_sla", "true")

	// 处理自定义日期范围
	var startDate, endDate time.Time
	var err error

	if timeRange == "custom" {
		if startDateStr != "" {
			startDate, err = time.Parse("2006-01-02", startDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "开始日期格式错误")
				return
			}
		}

		if endDateStr != "" {
			endDate, err = time.Parse("2006-01-02", endDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "结束日期格式错误")
				return
			}
		}
	}

	// 处理count_in_sla参数
	var countInSLA *bool
	if countInSLAStr != "" {
		countInSLAValue := countInSLAStr == "true"
		countInSLA = &countInSLAValue
	}

	stats, err := c.service.GetBusinessImpactTotalTime(ctx, timeRange, startDate, endDate, project, countInSLA)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取业务影响时长统计失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取业务影响时长统计成功")
}

// GetGPUFaultRatio 获取GPU卡故障比
// @Summary 获取GPU卡故障比
// @Description 计算指定项目的GPU卡故障比(GPU故障工单数/该项目的GPU服务器数*8)
// @Tags 看板-工单统计
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围(today/week/month/year/custom)"
// @Param start_date query string false "开始日期，格式YYYY-MM-DD"
// @Param end_date query string false "结束日期，格式YYYY-MM-DD"
// @Param project query string true "项目名称，必须指定项目"
// @Param count_in_sla query boolean false "是否只统计计入SLA的工单，默认为true"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/ticket/gpu-fault-ratio [get]
func (c *DashboardController) GetGPUFaultRatio(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "month")
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")
	project := ctx.Query("project")
	countInSLAStr := ctx.DefaultQuery("count_in_sla", "true")

	// 检查项目参数
	if project == "" {
		response.Fail(ctx, http.StatusBadRequest, "项目参数不能为空")
		return
	}

	// 处理自定义日期范围
	var startDate, endDate time.Time
	var err error

	if timeRange == "custom" {
		if startDateStr != "" {
			startDate, err = time.Parse("2006-01-02", startDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "开始日期格式错误")
				return
			}
		}

		if endDateStr != "" {
			endDate, err = time.Parse("2006-01-02", endDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "结束日期格式错误")
				return
			}
		}
	}

	// 处理count_in_sla参数
	var countInSLA *bool
	if countInSLAStr != "" {
		countInSLAValue := countInSLAStr == "true"
		countInSLA = &countInSLAValue
	}

	stats, err := c.service.GetGPUFaultRatio(ctx, timeRange, startDate, endDate, project, countInSLA)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取GPU卡故障比失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取GPU卡故障比成功")
}

// GetFaultTicketDetails 获取故障单详情列表
// @Summary 获取故障单详情列表
// @Description 获取指定项目的故障单详情，包括租户IP、故障原因、故障时长和是否冷迁移
// @Tags 看板-工单统计
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围(today/week/month/year/custom)"
// @Param start_date query string false "开始日期，格式YYYY-MM-DD"
// @Param end_date query string false "结束日期，格式YYYY-MM-DD"
// @Param project query string true "项目名称，必须指定项目"
// @Param count_in_sla query boolean false "是否只统计计入SLA的工单，默认为true"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/ticket/fault-details [get]
func (c *DashboardController) GetFaultTicketDetails(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "month")
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")
	project := ctx.Query("project")
	countInSLAStr := ctx.DefaultQuery("count_in_sla", "true")

	// 检查项目参数
	if project == "" {
		response.Fail(ctx, http.StatusBadRequest, "项目参数不能为空")
		return
	}

	// 处理自定义日期范围
	var startDate, endDate time.Time
	var err error

	if timeRange == "custom" {
		if startDateStr != "" {
			startDate, err = time.Parse("2006-01-02", startDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "开始日期格式错误")
				return
			}
		}

		if endDateStr != "" {
			endDate, err = time.Parse("2006-01-02", endDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "结束日期格式错误")
				return
			}
		}
	}

	// 处理count_in_sla参数
	var countInSLA *bool
	if countInSLAStr != "" {
		countInSLAValue := countInSLAStr == "true"
		countInSLA = &countInSLAValue
	}

	details, err := c.service.GetFaultTicketDetails(ctx, timeRange, startDate, endDate, project, countInSLA)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取故障单详情失败: "+err.Error())
		return
	}

	response.Success(ctx, details, "获取故障单详情成功")
}

// GetTotalFaultDevices 获取故障总台数
// @Summary 获取故障总台数
// @Description 获取根据不重复的device_sn统计的故障总台数
// @Tags 看板-工单统计
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围(today/week/month/year/custom)"
// @Param start_date query string false "开始日期，格式YYYY-MM-DD"
// @Param end_date query string false "结束日期，格式YYYY-MM-DD"
// @Param project query string false "项目名称，为空时返回全部项目数据"
// @Param count_in_sla query boolean false "是否只统计计入SLA的工单，默认为true"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/ticket/total-devices [get]
func (c *DashboardController) GetTotalFaultDevices(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "month")
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")
	project := ctx.Query("project")
	countInSLAStr := ctx.DefaultQuery("count_in_sla", "true")

	// 处理自定义日期范围
	var startDate, endDate time.Time
	var err error

	if timeRange == "custom" {
		if startDateStr != "" {
			startDate, err = time.Parse("2006-01-02", startDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "开始日期格式错误")
				return
			}
		}

		if endDateStr != "" {
			endDate, err = time.Parse("2006-01-02", endDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "结束日期格式错误")
				return
			}
		}
	}

	// 处理count_in_sla参数
	var countInSLA *bool
	if countInSLAStr != "" {
		countInSLAValue := countInSLAStr == "true"
		countInSLA = &countInSLAValue
	}

	stats, err := c.service.GetTotalFaultDevices(ctx, timeRange, startDate, endDate, project, countInSLA)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取故障总台数失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取故障总台数成功")
}

// GetEngineerResponseAndFixTimeStats 获取工程师响应和修复时长统计
// @Summary 获取工程师响应和修复时长统计
// @Description 获取软件修复分配人和硬件修复工程师的平均响应时长和修复时长统计
// @Tags 看板-工单统计
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围(today/week/month/year/custom)"
// @Param start_date query string false "开始日期，格式YYYY-MM-DD"
// @Param end_date query string false "结束日期，格式YYYY-MM-DD"
// @Param project query string false "项目名称，为空时返回全部项目数据"
// @Param count_in_sla query boolean false "是否只统计计入SLA的工单，默认为true"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/ticket/engineer-stats [get]
func (c *DashboardController) GetEngineerResponseAndFixTimeStats(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "month")
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")
	project := ctx.Query("project")
	countInSLAStr := ctx.DefaultQuery("count_in_sla", "true")

	// 处理自定义日期范围
	var startDate, endDate time.Time
	var err error

	if timeRange == "custom" {
		if startDateStr != "" {
			startDate, err = time.Parse("2006-01-02", startDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "开始日期格式错误")
				return
			}
		}

		if endDateStr != "" {
			endDate, err = time.Parse("2006-01-02", endDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "结束日期格式错误")
				return
			}
		}
	}

	// 处理count_in_sla参数
	var countInSLA *bool
	if countInSLAStr != "" {
		countInSLAValue := countInSLAStr == "true"
		countInSLA = &countInSLAValue
	}

	stats, err := c.service.GetEngineerResponseAndFixTimeStats(ctx, timeRange, startDate, endDate, project, countInSLA)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取工程师响应和修复时长统计失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取工程师响应和修复时长统计成功")
}

// GetFaultDeviceBrandDistribution 获取故障设备厂商分布统计
// @Summary 获取故障设备厂商分布统计
// @Description 获取故障设备的厂商分布情况
// @Tags 看板-工单统计
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围(today/week/month/year/custom)"
// @Param start_date query string false "开始日期，格式YYYY-MM-DD"
// @Param end_date query string false "结束日期，格式YYYY-MM-DD"
// @Param project query string false "项目名称，为空时返回全部项目数据"
// @Param count_in_sla query boolean false "是否只统计计入SLA的工单，默认为true"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/ticket/brand-distribution [get]
func (c *DashboardController) GetFaultDeviceBrandDistribution(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "month")
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")
	project := ctx.Query("project")
	countInSLAStr := ctx.DefaultQuery("count_in_sla", "true")

	// 处理自定义日期范围
	var startDate, endDate time.Time
	var err error

	if timeRange == "custom" {
		if startDateStr != "" {
			startDate, err = time.Parse("2006-01-02", startDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "开始日期格式错误")
				return
			}
		}

		if endDateStr != "" {
			endDate, err = time.Parse("2006-01-02", endDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "结束日期格式错误")
				return
			}
		}
	}

	// 处理count_in_sla参数
	var countInSLA *bool
	if countInSLAStr != "" {
		countInSLAValue := countInSLAStr == "true"
		countInSLA = &countInSLAValue
	}

	stats, err := c.service.GetFaultDeviceBrandDistribution(ctx, timeRange, startDate, endDate, project, countInSLA)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取故障设备厂商分布统计失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取故障设备厂商分布统计成功")
}

// GetDailyBusinessImpactTrend 获取每天业务影响时长趋势统计
// @Summary 获取每天故障处理总时长趋势统计
// @Description 获取每天故障处理总时长趋势及报障单数量趋势
// @Tags 看板-工单统计
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围(today/week/month/year/custom)"
// @Param start_date query string false "开始日期，格式YYYY-MM-DD"
// @Param end_date query string false "结束日期，格式YYYY-MM-DD"
// @Param group_by query string false "分组方式(day/week/month)"
// @Param project query string false "项目名称，为空时返回全部项目数据"
// @Param count_in_sla query boolean false "是否只统计计入SLA的工单，默认为false"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/ticket/daily-impact-trend [get]
func (c *DashboardController) GetDailyBusinessImpactTrend(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "month")
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")
	groupBy := ctx.DefaultQuery("group_by", "day")
	project := ctx.Query("project")
	countInSLAStr := ctx.DefaultQuery("count_in_sla", "false")

	// 处理自定义日期范围
	var startDate, endDate time.Time
	var err error

	if timeRange == "custom" {
		if startDateStr != "" {
			startDate, err = time.Parse("2006-01-02", startDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "开始日期格式错误")
				return
			}
		}

		if endDateStr != "" {
			endDate, err = time.Parse("2006-01-02", endDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "结束日期格式错误")
				return
			}
		}
	}

	// 处理count_in_sla参数
	var countInSLA *bool
	if countInSLAStr != "" {
		countInSLAValue := countInSLAStr == "true"
		countInSLA = &countInSLAValue
	}

	stats, err := c.service.GetDailyBusinessImpactTrend(ctx, timeRange, startDate, endDate, groupBy, project, countInSLA)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取每天业务影响时长趋势统计失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取每天业务影响时长趋势统计成功")
}

// GetHardwareFaultTrend 获取每天硬件故障次数趋势统计
// @Summary 获取每天硬件故障次数趋势统计
// @Description 获取每天硬件故障次数趋势统计
// @Tags 看板-工单统计
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围(today/week/month/year/custom)"
// @Param start_date query string false "开始日期，格式YYYY-MM-DD"
// @Param end_date query string false "结束日期，格式YYYY-MM-DD"
// @Param group_by query string false "分组方式(day/week/month)"
// @Param project query string false "项目名称，为空时返回全部项目数据"
// @Param count_in_sla query boolean false "是否只统计计入SLA的工单，默认为false"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/ticket/hardware-fault-trend [get]
func (c *DashboardController) GetHardwareFaultTrend(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "month")
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")
	groupBy := ctx.DefaultQuery("group_by", "day")
	project := ctx.Query("project")
	countInSLAStr := ctx.DefaultQuery("count_in_sla", "false")

	// 处理自定义日期范围
	var startDate, endDate time.Time
	var err error

	if timeRange == "custom" {
		if startDateStr != "" {
			startDate, err = time.Parse("2006-01-02", startDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "开始日期格式错误")
				return
			}
		}

		if endDateStr != "" {
			endDate, err = time.Parse("2006-01-02", endDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "结束日期格式错误")
				return
			}
		}
	}

	// 处理count_in_sla参数
	var countInSLA *bool
	if countInSLAStr != "" {
		countInSLAValue := countInSLAStr == "true"
		countInSLA = &countInSLAValue
	}

	stats, err := c.service.GetHardwareFaultTrend(ctx, timeRange, startDate, endDate, groupBy, project, countInSLA)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取每天硬件故障次数趋势统计失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取每天硬件故障次数趋势统计成功")
}

// GetResourceChangesStats 获取资源变更统计
// @Summary 获取资源变更统计
// @Description 获取软件上下线工单的资源变更统计，包括新增和下线的资源数量，按cluster原值进行分类统计
// @Tags Dashboard
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围" Enums(today,yesterday,last_7_days,last_30_days,this_month,last_month,custom) default(last_7_days)
// @Param start_date query string false "开始日期(YYYY-MM-DD)" format(date)
// @Param end_date query string false "结束日期(YYYY-MM-DD)" format(date)
// @Param project query string false "项目名称"
// @Success 200 {object} response.Response{data=map[string]interface{}} "成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/dashboard/ticket/resource-changes [get]
func (c *DashboardController) GetResourceChangesStats(ctx *gin.Context) {
	// 获取查询参数
	timeRange := ctx.DefaultQuery("time_range", "last_7_days")
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")
	project := ctx.Query("project")

	// 处理自定义日期范围
	var startDate, endDate time.Time
	var err error

	if timeRange == "custom" {
		if startDateStr != "" {
			startDate, err = time.Parse("2006-01-02", startDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "开始日期格式错误")
				return
			}
		}

		if endDateStr != "" {
			endDate, err = time.Parse("2006-01-02", endDateStr)
			if err != nil {
				response.Fail(ctx, http.StatusBadRequest, "结束日期格式错误")
				return
			}
		}
	}

	// 调用服务层获取统计数据
	stats, err := c.service.GetResourceChangesStats(ctx, timeRange, startDate, endDate, project)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取资源变更统计失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取资源变更统计成功")
}
