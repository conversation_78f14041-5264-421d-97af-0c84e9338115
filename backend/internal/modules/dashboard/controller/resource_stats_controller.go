package controller

import (
	"backend/internal/modules/dashboard/service"
	"backend/response"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// ResourceStatsController 资源统计控制器
type ResourceStatsController struct {
	service service.ResourceStatsService
}

// NewResourceStatsController 创建资源统计控制器
func NewResourceStatsController(service service.ResourceStatsService) *ResourceStatsController {
	return &ResourceStatsController{service: service}
}

// RegisterRoutes 注册路由
func (c *ResourceStatsController) RegisterRoutes(router *gin.RouterGroup) {
	resourceRouter := router.Group("/resources")
	{
		// 获取服务器统计信息
		resourceRouter.GET("/server-stats", c.GetServerStats)

		// 获取集群统计信息
		resourceRouter.GET("/cluster-stats", c.GetClusterStats)

		// 按项目获取服务器统计信息
		resourceRouter.GET("/server-stats-by-project", c.GetServerStatsByProject)

		// 按项目获取集群统计信息
		resourceRouter.GET("/cluster-stats-by-project", c.GetClusterStatsByProject)

		// 获取品牌分布统计
		resourceRouter.GET("/brand-distribution", c.GetBrandDistribution)

		// 获取服务器机房分布统计
		resourceRouter.GET("/room-distribution", c.GetRoomDistribution)

		// 获取GPU卡型号分布统计
		resourceRouter.GET("/gpu-model-distribution", c.GetGpuModelDistribution)
	}
}

// GetServerStats 获取服务器总数量及各状态数量
// @Summary 获取服务器统计信息
// @Description 获取服务器总数量及各状态数量统计，包括各资产类型(服务器、GPU服务器、网络设备、存储设备等)的数量
// @Tags 看板-资源统计
// @Accept json
// @Produce json
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/resources/server-stats [get]
func (c *ResourceStatsController) GetServerStats(ctx *gin.Context) {
	stats, err := c.service.GetServerStats(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取服务器统计信息失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取服务器统计信息成功")
}

// GetClusterStats 获取各集群类型的数量
// @Summary 获取集群统计信息
// @Description 获取各集群类型的数量统计
// @Tags 看板-资源统计
// @Accept json
// @Produce json
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/resources/cluster-stats [get]
func (c *ResourceStatsController) GetClusterStats(ctx *gin.Context) {
	stats, err := c.service.GetClusterStats(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取集群统计信息失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取集群统计信息成功")
}

// GetServerStatsByProject 按项目获取服务器数量
// @Summary 按项目获取服务器统计信息
// @Description 按项目获取服务器数量统计，包括各项目中不同资产类型(服务器、GPU服务器、网络设备、存储设备等)的数量，支持时间范围查询时自动使用月度统计数据
// @Tags 看板-资源统计
// @Accept json
// @Produce json
// @Param project query string false "项目名称，为空时返回所有项目数据"
// @Param start_date query string false "开始日期（YYYY-MM-DD格式）"
// @Param end_date query string false "结束日期（YYYY-MM-DD格式）"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/resources/server-stats-by-project [get]
func (c *ResourceStatsController) GetServerStatsByProject(ctx *gin.Context) {
	project := ctx.Query("project")
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")

	var startDate, endDate *time.Time
	var err error

	// 解析开始日期
	if startDateStr != "" {
		parsed, err := time.Parse("2006-01-02", startDateStr)
		if err != nil {
			response.Fail(ctx, http.StatusBadRequest, "开始日期格式错误，请使用YYYY-MM-DD格式")
			return
		}
		startDate = &parsed
	}

	// 解析结束日期
	if endDateStr != "" {
		parsed, err := time.Parse("2006-01-02", endDateStr)
		if err != nil {
			response.Fail(ctx, http.StatusBadRequest, "结束日期格式错误，请使用YYYY-MM-DD格式")
			return
		}
		// 设置为当天的23:59:59
		endOfDay := time.Date(parsed.Year(), parsed.Month(), parsed.Day(), 23, 59, 59, 999999999, parsed.Location())
		endDate = &endOfDay
	}

	stats, err := c.service.GetServerStatsByProject(ctx, project, startDate, endDate)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取项目服务器统计信息失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取项目服务器统计信息成功")
}

// GetClusterStatsByProject 按项目获取集群数量
// @Summary 按项目获取集群统计信息
// @Description 按项目获取集群数量统计，支持时间范围查询时自动使用月度统计数据
// @Tags 看板-资源统计
// @Accept json
// @Produce json
// @Param project query string false "项目名称，为空时返回所有项目数据"
// @Param start_date query string false "开始日期（YYYY-MM-DD格式）"
// @Param end_date query string false "结束日期（YYYY-MM-DD格式）"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/resources/cluster-stats-by-project [get]
func (c *ResourceStatsController) GetClusterStatsByProject(ctx *gin.Context) {
	project := ctx.Query("project")
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")

	var startDate, endDate *time.Time
	var err error

	// 解析开始日期
	if startDateStr != "" {
		parsed, err := time.Parse("2006-01-02", startDateStr)
		if err != nil {
			response.Fail(ctx, http.StatusBadRequest, "开始日期格式错误，请使用YYYY-MM-DD格式")
			return
		}
		startDate = &parsed
	}

	// 解析结束日期
	if endDateStr != "" {
		parsed, err := time.Parse("2006-01-02", endDateStr)
		if err != nil {
			response.Fail(ctx, http.StatusBadRequest, "结束日期格式错误，请使用YYYY-MM-DD格式")
			return
		}
		// 设置为当天的23:59:59
		endOfDay := time.Date(parsed.Year(), parsed.Month(), parsed.Day(), 23, 59, 59, 999999999, parsed.Location())
		endDate = &endOfDay
	}

	stats, err := c.service.GetClusterStatsByProject(ctx, project, startDate, endDate)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取项目集群统计信息失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取项目集群统计信息成功")
}

// GetBrandDistribution 获取设备品牌分布统计
// @Summary 获取设备品牌分布统计
// @Description 获取所有设备的品牌分布数量统计
// @Tags 看板-资源统计
// @Accept json
// @Produce json
// @Param project query string false "项目名称，为空时返回所有项目数据"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/resources/brand-distribution [get]
func (c *ResourceStatsController) GetBrandDistribution(ctx *gin.Context) {
	project := ctx.Query("project")
	stats, err := c.service.GetBrandDistribution(ctx, project)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取品牌分布统计信息失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取品牌分布统计信息成功")
}

// GetRoomDistribution 获取服务器机房分布统计
// @Summary 获取服务器机房分布统计
// @Description 获取服务器在各机房的分布数量统计
// @Tags 看板-资源统计
// @Accept json
// @Produce json
// @Param project query string false "项目名称，为空时返回所有项目数据"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/resources/room-distribution [get]
func (c *ResourceStatsController) GetRoomDistribution(ctx *gin.Context) {
	project := ctx.Query("project")
	stats, err := c.service.GetRoomDistribution(ctx, project)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取服务器机房分布统计信息失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取服务器机房分布统计信息成功")
}

// GetGpuModelDistribution 获取GPU卡型号分布统计
// @Summary 获取GPU卡型号分布统计
// @Description 获取GPU卡型号分布数量统计
// @Tags 看板-资源统计
// @Accept json
// @Produce json
// @Param project query string false "项目名称，为空时返回所有项目数据"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /dashboard/resources/gpu-model-distribution [get]
func (c *ResourceStatsController) GetGpuModelDistribution(ctx *gin.Context) {
	project := ctx.Query("project")
	stats, err := c.service.GetGpuModelDistribution(ctx, project)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取GPU卡型号分布统计信息失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取GPU卡型号分布统计信息成功")
}
