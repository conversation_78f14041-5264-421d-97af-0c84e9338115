package service

import (
	"backend/internal/modules/cmdb/model/outbound"
	"backend/internal/modules/cmdb/repository/asset"
	inboundRepository "backend/internal/modules/cmdb/repository/inbound"
	outboundRepository "backend/internal/modules/cmdb/repository/outbound"
	"backend/internal/modules/cmdb/repository/product"
	"backend/internal/modules/export/excel"
	"backend/internal/modules/export/model"
	"backend/internal/modules/export/repository"
	ticketModel "backend/internal/modules/ticket/model"
	ticketRepo "backend/internal/modules/ticket/repository"
	"context"
	"errors"
	"fmt"
	"path/filepath"
	"time"

	"github.com/xuri/excelize/v2"
)

// ExportService 导出服务接口
type ExportService interface {
	Export(ctx context.Context, req *model.ExportRequest) (*model.ExportResponse, error)

	// 出入库流程相关导出
	// 统一入口
	ExportInboundTemplate(ctx context.Context, TemplateType, inboundNo string, outboundID uint) (string, error)
	initInboundExcelize() *model.InboundExportProps
	// 具体导出
	exportPurchaseInboundTemplate(ctx context.Context, compros *model.InboundExportProps) (int, error)
	exportNewPartDetailsTemplate(ctx context.Context, compros *model.InboundExportProps, inboundNo string) (int, error)
	exportNewDeviceTemplate(ctx context.Context, compros *model.InboundExportProps, inboundNo string) (int, error)
	exportReturnRepairPartTemplate(ctx context.Context, compros *model.InboundExportProps, inboundNo string) (int, error)
	exportDismantledPartTemplate(ctx context.Context, compros *model.InboundExportProps, inboundNo string) (int, error)
	exportReplacePartTemplate(ctx context.Context, compros *model.InboundExportProps, outboundID uint) (int, error)
	exportAllocatePartTemplate(ctx context.Context, compros *model.InboundExportProps, outboundID uint) (int, error)
	exportRackDeviceTemplate(ctx context.Context, compros *model.InboundExportProps, outboundID uint) (int, error)
}

// exportService 导出服务实现
type exportService struct {
	repo              repository.ExportRepository
	exporter          model.Exporter
	outboundRepo      outboundRepository.OutboundTicketRepository
	productRepo       product.ProductRepository
	inboundTicketRepo ticketRepo.InboundTicketRepository
	inboundRepo       inboundRepository.Repository
	warehouseRepo     asset.WarehouseRepository
}

// NewExportService 创建导出服务
func NewExportService(repo repository.ExportRepository, storagePath string, productRepo product.ProductRepository, inboundTicketRepo ticketRepo.InboundTicketRepository, inboundRepo inboundRepository.Repository, warehouseRepository asset.WarehouseRepository, outboundRepo outboundRepository.OutboundTicketRepository) ExportService {
	// 创建Excel导出器，使用仓库作为数据提供者
	exporter := excel.NewExcelExporter(
		&exportDataProvider{repo: repo},
		storagePath,
	)

	return &exportService{
		repo:              repo,
		exporter:          exporter,
		outboundRepo:      outboundRepo,
		productRepo:       productRepo,
		inboundTicketRepo: inboundTicketRepo,
		inboundRepo:       inboundRepo,
		warehouseRepo:     warehouseRepository,
	}
}

// Export 执行导出
func (s *exportService) Export(ctx context.Context, req *model.ExportRequest) (*model.ExportResponse, error) {
	return s.exporter.Export(ctx, req)
}

// exportDataProvider 导出数据提供者，适配仓库接口到导出接口
type exportDataProvider struct {
	repo repository.ExportRepository
}

// GetData 获取指定ID的数据
func (p *exportDataProvider) GetData(ctx context.Context, tableName string, ids []int64, condition string) ([]map[string]interface{}, error) {
	return p.repo.GetData(ctx, tableName, ids, condition)
}

// GetAllData 获取所有数据
func (p *exportDataProvider) GetAllData(ctx context.Context, tableName string, condition string) ([]map[string]interface{}, error) {
	return p.repo.GetAllData(ctx, tableName, condition)
}

// ExportInboundTemplat 导出入库模板统一入口
func (s *exportService) ExportInboundTemplate(ctx context.Context, TemplateType, InboundNo string, OutboundID uint) (string, error) {
	var (
		//ExportResponse *model.ExportResponse
		index          int
		fileNamePrefix string
	)

	// 初始化表格信息
	comprops := s.initInboundExcelize()
	f := comprops.F

	// 根据入库模板导出
	switch TemplateType {
	case model.PruchaseInboundTemplate: // 采购入库信息模板
		pruchaseIndex, err := s.exportPurchaseInboundTemplate(ctx, comprops)
		if err != nil {
			return "", err
		}
		fileNamePrefix = "采购入库模板"
		index = pruchaseIndex
	case model.NewPartTemplate: // 新购配件入库模板
		if InboundNo == "" {
			return "", fmt.Errorf("缺少工单号参数")
		}
		newDetailIndex, err := s.exportNewPartDetailsTemplate(ctx, comprops, InboundNo)
		if err != nil {
			return "", err
		}
		fileNamePrefix = "新购配件入库模板"
		index = newDetailIndex
	case model.NewDeviceTemplate: // 新购设备入库模板
		newDeviceIndex, err := s.exportNewDeviceTemplate(ctx, comprops, InboundNo)
		if err != nil {
			return "", err
		}
		fileNamePrefix = "新购设备入库模板"
		index = newDeviceIndex
	case model.ReturnRepairPartTemplate: // 返修配件入库模板
		repairIndex, err := s.exportReturnRepairPartTemplate(ctx, comprops, InboundNo)
		if err != nil {
			return "", err
		}
		fileNamePrefix = "返修入库模板"
		index = repairIndex
	case model.DismantledPartTemplate: // 拆机配件入库模板
		dismantledIndex, err := s.exportDismantledPartTemplate(ctx, comprops, InboundNo)
		if err != nil {
			return "", err
		}
		fileNamePrefix = "拆机入库模板"
		index = dismantledIndex
	case model.ReplacePartTemplate:
		replaceIndex, err := s.exportReplacePartTemplate(ctx, comprops, OutboundID)
		if err != nil {
			return "", err
		}
		fileNamePrefix = "配件改配模板"
		index = replaceIndex
	case model.AllocatePartTemplate:
		allocateIndex, err := s.exportAllocatePartTemplate(ctx, comprops, OutboundID)
		if err != nil {
			return "", err
		}
		fileNamePrefix = "配件调拨模板"
		index = allocateIndex
	case model.ReturnRepairOutPartTemplate:
		returnRepairIndex, err := s.exportReturnRepairOutPartTemplate(ctx, comprops, OutboundID)
		if err != nil {
			return "", err
		}
		fileNamePrefix = "返修出库模板"
		index = returnRepairIndex
	case model.RepairOutPartTemplate:
		repairOutPartIndex, err := s.exportRepairOutPartTemplate(ctx, comprops, OutboundID)
		if err != nil {
			return "", err
		}
		fileNamePrefix = "维修出库模板"
		index = repairOutPartIndex
	case model.SellPartTemplate:
		sellPartIndex, err := s.exportSellPartTemplate(ctx, comprops, OutboundID)
		if err != nil {
			return "", err
		}
		fileNamePrefix = "配件销售模板"
		index = sellPartIndex
	case model.RackDeviceTemplate:
		rackDeviceIndex, err := s.exportRackDeviceTemplate(ctx, comprops, OutboundID)
		if err != nil {
			return "", err
		}
		fileNamePrefix = "设备上架导入模板"
		index = rackDeviceIndex
	case model.AllocateDeviceTemplate:
		allocateDeviceIndex, err := s.exportAllocateDeviceTemplate(ctx, comprops, OutboundID)
		if err != nil {
			return "", err
		}
		fileNamePrefix = "设备调拨导入模板"
		index = allocateDeviceIndex
	default:
		return "", errors.New("未收录该导入模板，请联系管理员")
	}
	// 创建Excel活动区
	f.SetActiveSheet(index)

	// 删除默认的Sheet1表格
	err := f.DeleteSheet("Sheet1")
	if err != nil {
		return "", err
	}
	fileName := fmt.Sprintf("%s-%s.xlsx", fileNamePrefix, time.Now().Format("2006-01-02 15.04.05"))

	// 构建文件路径
	filePath := filepath.Join("storage", "exports", fileName)

	if err := f.SaveAs(filePath); err != nil {
		return "", fmt.Errorf("保存文件失败：%w", err)
	}

	return fileName, nil
}

func (s *exportService) initInboundExcelize() *model.InboundExportProps {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()

	// 设置单元格公共样式
	commonStyle := &excelize.Style{
		Border: []excelize.Border{
			//{Type: "all", Color: "000000", Style: 6}, // 全部边框 黑色 实线
			{Type: "left", Color: "000000", Style: 6},   // 左边框
			{Type: "top", Color: "000000", Style: 6},    // 上边框
			{Type: "right", Color: "000000", Style: 6},  // 右边框
			{Type: "bottom", Color: "000000", Style: 6}, // 下边框
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center", // 水平居中
			Vertical:   "center", // 垂直居中
		},
	}

	PropsHeight := 20.0
	PropsWidth := 20.0
	// 设置工作表属性
	PropsOptions := &excelize.SheetPropsOptions{
		DefaultRowHeight: &PropsHeight,
		DefaultColWidth:  &PropsWidth,
	}
	props := &model.InboundExportProps{
		F:            f,
		CommonStyle:  commonStyle,
		PropsOptions: PropsOptions,
	}
	return props
}

// InitSheet 初始化工作表，创建工作表+初始化工作表属性+设置表头
func (s *exportService) initSheet(f *excelize.File, headerInfos []model.HeaderInfoInput, PropsOption *excelize.SheetPropsOptions) (int, error) {
	var index int
	for i, headerInfo := range headerInfos {
		// 创建工作表
		sheetIndex, err := f.NewSheet(headerInfo.SheetName) // 此处的index反映的是用户打开Excel文件后看到的第一张表索引
		if err != nil {
			return 0, fmt.Errorf("初始化%v失败: %w", headerInfo.SheetName, err)
		}
		if i == 0 {
			index = sheetIndex
		}
		// 初始化工作表属性
		if err := f.SetSheetProps(headerInfo.SheetName, PropsOption); err != nil {
			return 0, fmt.Errorf("设置%v初始表格属性失败: %w", headerInfo.SheetName, err)
		}
		// 设置表头
		if err := f.SetSheetRow(headerInfo.SheetName, "A1", &headerInfo.Headers); err != nil {
			return 0, fmt.Errorf("设置%v表头失败: %w", headerInfo.SheetName, err)
		}
		//fmt.Println("已初始化表：", headerInfo.SheetName)
	}
	return index, nil
}

// ExportPurchaseInboundTemplate 导出采购入库模板
func (s *exportService) exportPurchaseInboundTemplate(ctx context.Context, compros *model.InboundExportProps) (int, error) {
	f := compros.F
	commonStyle := compros.CommonStyle
	PropsOptions := compros.PropsOptions
	// 初始化工作表
	headerInfos := []model.HeaderInfoInput{
		{SheetName: model.PurchaseInboundSheet, Headers: model.PurchaseInboundHeaders},
		{SheetName: model.ProductSheet, Headers: model.ProductHeaders},
	}
	index, err := s.initSheet(f, headerInfos, PropsOptions)
	if err != nil {
		return 0, err
	}

	// 设置单元格
	UnlockStyle := &excelize.Style{
		Border:    commonStyle.Border,    // 继承边框样式
		Alignment: commonStyle.Alignment, // 继承对齐方式
		Protection: &excelize.Protection{
			Locked: false, // 允许编辑
		},
	}
	unLockStyle, err := f.NewStyle(UnlockStyle)
	if err != nil {
		return 0, fmt.Errorf("创建单元格解除保护样式失败: %w", err)
	}
	lockStyle := &excelize.Style{
		Border:    commonStyle.Border,    // 继承边框样式
		Alignment: commonStyle.Alignment, // 继承对齐方式
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"D3D3D3"}, // 灰色的RGB值
			Pattern: 1,                  // 纯色填充
		},
		Protection: &excelize.Protection{
			Locked: true, // 不允许编辑
		},
	}
	LockStyle, err := f.NewStyle(lockStyle)
	if err != nil {
		return 0, fmt.Errorf("创建单元格保护样式失败: %w", err)
	}

	// 设置单元格格式，保护部分列
	err = f.SetCellStyle(model.PurchaseInboundSheet, "A1", "C100", unLockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置列失败: %w", err)
	}
	err = f.SetCellStyle(model.PurchaseInboundSheet, "D1", "I100", LockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置列保护失败: %w", err)
	}

	// 设置工作表保护机制
	options := &excelize.SheetProtectionOptions{ //设置了密码之后，所有的选项都为false
		AlgorithmName:       "SHA-256",
		AutoFilter:          true,
		FormatColumns:       true,
		FormatRows:          true,
		Password:            "1qaz2wsx",
		Sort:                true,
		SelectLockedCells:   true,
		SelectUnlockedCells: true,
		// 其他选项保持默认 false
	}
	sheetProtect := []model.SheetProtectInput{
		{SheetName: model.PurchaseInboundSheet, ProtectOption: options},
		{SheetName: model.ProductSheet, ProtectOption: options},
	}

	if err := CreateSheetProtection(f, sheetProtect); err != nil {
		return 0, err
	}

	// 写入数据
	products, err := s.productRepo.ListAll(ctx)
	if err != nil {
		return 0, err
	}
	for rowIndex, p := range products {
		// 行号从2开始（第1行是表头）
		excelRow := rowIndex + 2

		// 构建数据行
		rowData := []interface{}{
			p.PN,                 // A列: PN号码
			p.Brand,              // B列: 品牌
			p.PN + "-" + p.Brand, // C列: PN-品牌
			p.MaterialType,       // D列: 物料类型
			p.ProductCategory,    // E列: 产品类别
			p.Spec,               // F列: 规格详情
			p.Model,              // G列: 型号
			p.ID,                 // H列: 规格ID（使用数据库ID）
		}
		// 写入行数据
		cell, err := excelize.CoordinatesToCellName(1, excelRow) // 计算A+行号
		if err != nil {
			return 0, fmt.Errorf("计算单元格位置失败: %w", err)
		}

		if err := f.SetSheetRow(model.ProductSheet, cell, &rowData); err != nil {
			return 0, fmt.Errorf("写入第%d行数据失败: %w", excelRow, err)
		}
	}

	// 设置自动过滤器
	if err := f.AutoFilter(model.ProductSheet, "B1:G1", []excelize.AutoFilterOptions{}); err != nil {
		return 0, fmt.Errorf("设置规格详情表自动过滤器失败: %w", err)
	}

	if err := f.AutoFilter(model.PurchaseInboundSheet, "B1:H1", []excelize.AutoFilterOptions{}); err != nil {
		return 0, fmt.Errorf("设置采购入库表自动过滤器失败: %w", err)
	}

	// 设置相应公式（在导入数据之后）
	if err := SetPurchaseInboundFormula(f, 100, len(products)); err != nil {
		return 0, err
	}

	// 添加下拉菜单（在导入数据之后）
	//brandDownMenu, err := SetDropDownMenu(f, model.ProductSheet, "B2:B100")
	brandDownMenu, err := SetDropDownMenuV2(f, model.ProductSheet, "A2:A100")
	if err != nil {
		return 0, err
	}
	//fmt.Println(brandDownMenu)
	if err := f.AddDataValidation(model.PurchaseInboundSheet, brandDownMenu); err != nil {
		return 0, fmt.Errorf("添加品牌下拉菜单失败: %w", err)
	}

	return index, nil
}

// ExportNewPartDetailsTemplate 导出新购配件入库--入库详情模板
func (s *exportService) exportNewPartDetailsTemplate(ctx context.Context, compros *model.InboundExportProps, inboundNo string) (int, error) {
	ticket, err := s.inboundTicketRepo.GetInboundTicketByNo(ctx, inboundNo)
	if err != nil {
		return 0, err
	}
	details, err := s.inboundTicketRepo.GetInboundDetailsByNo(ctx, ticket.InboundType, inboundNo)
	if err != nil {
		return 0, err
	}

	f := compros.F
	commonStyle := compros.CommonStyle
	PropsOptions := compros.PropsOptions
	// 初始化工作表
	headerInfos := []model.HeaderInfoInput{
		{SheetName: model.NewPartSheet, Headers: model.NewPartHeader},
	}
	index, err := s.initSheet(f, headerInfos, PropsOptions)
	if err != nil {
		return 0, err
	}

	// 设置单元格
	UnlockStyle := &excelize.Style{
		Border:    commonStyle.Border,    // 继承边框样式
		Alignment: commonStyle.Alignment, // 继承对齐方式
		Protection: &excelize.Protection{
			Locked: false, // 允许编辑
		},
	}
	unLockStyle, err := f.NewStyle(UnlockStyle)
	if err != nil {
		return 0, fmt.Errorf("创建单元格解除保护样式失败: %w", err)
	}
	lockStyle := &excelize.Style{
		Border:    commonStyle.Border,    // 继承边框样式
		Alignment: commonStyle.Alignment, // 继承对齐方式
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"D3D3D3"}, // 灰色的RGB值
			Pattern: 1,                  // 纯色填充
		},
		Protection: &excelize.Protection{
			Locked: true, // 不允许编辑
		},
	}
	LockStyle, err := f.NewStyle(lockStyle)
	if err != nil {
		return 0, fmt.Errorf("创建单元格保护样式失败: %w", err)
	}

	// 设置单元格格式，保护部分列
	err = f.SetCellStyle(model.NewPartSheet, "A1", fmt.Sprintf("B%d", len(details)+1), unLockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置列失败: %w", err)
	}
	err = f.SetCellStyle(model.NewPartSheet, "C1", fmt.Sprintf("J%d", len(details)+1), LockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置列保护失败: %w", err)
	}

	// 设置工作表保护机制
	options := &excelize.SheetProtectionOptions{ //设置了密码之后，所有的选项都为false
		AlgorithmName:       "SHA-256",
		AutoFilter:          true,
		FormatColumns:       true,
		FormatRows:          true,
		Password:            "1qaz2wsx",
		Sort:                true,
		SelectLockedCells:   true,
		SelectUnlockedCells: true,
		// 其他选项保持默认 false
	}
	sheetProtect := []model.SheetProtectInput{
		{SheetName: model.NewPartSheet, ProtectOption: options},
	}
	if err := CreateSheetProtection(f, sheetProtect); err != nil {
		return 0, err
	}

	// 写入数据
	for rowIndex, detail := range details {
		// 行号从2开始（第1行是表头）
		excelRow := rowIndex + 2

		// 构造数据行
		rowData := []interface{}{
			rowIndex + 1,                   // 序号
			detail.ComponentSN,             // SN
			detail.Product.PN,              // PN号码
			detail.Product.Brand,           // 品牌
			detail.Product.MaterialType,    // 物料类型
			detail.Product.ProductCategory, // 产品类别
			detail.Product.Spec,            // 规格详情
			detail.Product.Model,           // 型号
			detail.Product.ID,              // 规格ID
			detail.ID,                      // 入库ID
		}
		// 写入行数据
		cell, err := excelize.CoordinatesToCellName(1, excelRow)
		if err != nil {
			return 0, fmt.Errorf("计算单元格位置失败: %w", err)
		}

		if err := f.SetSheetRow(model.NewPartSheet, cell, &rowData); err != nil {
			return 0, fmt.Errorf("写入第%d行数据失败: %w", excelRow, err)
		}
	}

	// 设置自动过滤器
	if err := f.AutoFilter(model.NewPartSheet, "C1:H1", []excelize.AutoFilterOptions{}); err != nil {
		return 0, fmt.Errorf("设置%s自动过滤器失败: %w", model.NewPartSheet, err)
	}
	return index, nil
}

// exportNewDeviceTemplate 导出新购设备入库模板
func (s *exportService) exportNewDeviceTemplate(ctx context.Context, compros *model.InboundExportProps, inboundNo string) (int, error) {
	return s.exportDeviceTemplate(ctx, compros, model.NewDeviceSheet, inboundNo, 0, true)
}

// exportReturnRepairPartTemplate 导出返修配件入库模板
func (s *exportService) exportReturnRepairPartTemplate(ctx context.Context, compros *model.InboundExportProps, inboundNo string) (int, error) {
	ticket, err := s.inboundTicketRepo.GetInboundTicketByNo(ctx, inboundNo)
	if err != nil {
		return 0, err
	}
	details, err := s.inboundTicketRepo.GetInboundDetailsByNo(ctx, ticket.InboundType, inboundNo)
	if err != nil {
		return 0, err
	}

	f := compros.F
	commonStyle := compros.CommonStyle
	PropsOptions := compros.PropsOptions
	// 初始化工作表
	headerInfos := []model.HeaderInfoInput{
		{SheetName: model.ReturnRepairPartSheet, Headers: model.ReturnRepairPartHeader},
		{SheetName: "返修入库类型", Headers: []string{"对应映射", "类型"}},
		{SheetName: model.PartStatusSheet, Headers: model.PartStatusHeader},
	}
	index, err := s.initSheet(f, headerInfos, PropsOptions)
	if err != nil {
		return 0, err
	}
	// 设置单元格
	UnlockStyle := &excelize.Style{
		Border:    commonStyle.Border,    // 继承边框样式
		Alignment: commonStyle.Alignment, // 继承对齐方式
		Protection: &excelize.Protection{
			Locked: false, // 允许编辑
		},
	}
	unLockStyle, err := f.NewStyle(UnlockStyle)
	if err != nil {
		return 0, fmt.Errorf("创建单元格解除保护样式失败: %w", err)
	}
	lockStyle := &excelize.Style{
		Border:    commonStyle.Border,    // 继承边框样式
		Alignment: commonStyle.Alignment, // 继承对齐方式
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"D3D3D3"}, // 灰色的RGB值
			Pattern: 1,                  // 纯色填充
		},
		Protection: &excelize.Protection{
			Locked: true, // 不允许编辑
		},
	}
	LockStyle, err := f.NewStyle(lockStyle)
	if err != nil {
		return 0, fmt.Errorf("创建单元格保护样式失败: %w", err)
	}

	// 设置单元格格式，保护部分列
	err = f.SetCellStyle(model.ReturnRepairPartSheet, "A1", fmt.Sprintf("E%d", len(details)+1), unLockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置列失败: %w", err)
	}
	err = f.SetCellStyle(model.ReturnRepairPartSheet, "F1", fmt.Sprintf("O%d", len(details)+1), LockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置列失败: %w", err)
	}

	// 返修类型数据
	returnRepairData := [][]interface{}{
		{"replace", "换新"},
		{"repair", "维修"},
	}
	if err := f.SetCellStyle("返修入库类型", "A1", fmt.Sprintf("B%d", len(returnRepairData)+1), LockStyle); err != nil {
		return 0, fmt.Errorf("设置列失败: %w", err)
	}

	// 配件状态数据
	statusData := [][]interface{}{
		{"normal", "正常"},
		{"faulty", "故障"},
	}
	if err := f.SetCellStyle(model.PartStatusSheet, "A1", fmt.Sprintf("B%d", len(statusData)+1), LockStyle); err != nil {
		return 0, fmt.Errorf("设置列失败: %w", err)
	}

	// 设置工作表保护机制
	options := &excelize.SheetProtectionOptions{ //设置了密码之后，所有的选项都为false
		AlgorithmName:       "SHA-256",
		AutoFilter:          true,
		FormatColumns:       true,
		FormatRows:          true,
		Password:            "1qaz2wsx",
		Sort:                true,
		SelectLockedCells:   true,
		SelectUnlockedCells: true,
		// 其他选项保持默认 false
	}
	sheetProtect := []model.SheetProtectInput{
		{SheetName: model.ReturnRepairPartSheet, ProtectOption: options},
		{SheetName: model.PartStatusSheet, ProtectOption: options},
		{SheetName: "返修入库类型", ProtectOption: options},
	}
	if err := CreateSheetProtection(f, sheetProtect); err != nil {
		return 0, err
	}

	// 写入数据
	for i, row := range returnRepairData {
		cell, err := excelize.CoordinatesToCellName(1, i+2)
		if err != nil {
			return 0, err
		}
		err = f.SetSheetRow("返修入库类型", cell, &row)
		if err != nil {
			return 0, fmt.Errorf("生成%v数据失败: %w", "返修入库类型", err)
		}
	}

	for i, row := range statusData {
		cell, err := excelize.CoordinatesToCellName(1, i+2)
		if err != nil {
			return 0, err
		}
		err = f.SetSheetRow(model.PartStatusSheet, cell, &row)
		if err != nil {
			return 0, fmt.Errorf("写入配件状态数据失败: %w", err)
		}
	}

	for rowIndex, detail := range details {
		// 行号从2开始（第1行是表头）
		excelRow := rowIndex + 2

		// 构建数据行
		rowData := []interface{}{
			rowIndex + 1,                   // A列: 序号
			detail.ComponentSN,             // B列: 原SN
			"",                             // C列: 维修类型
			"",                             // D列: 换新SN
			"",                             // E列: 配件状态
			detail.Product.PN,              // F列: PN号码
			detail.Product.Brand,           // G列: 品牌
			detail.Product.MaterialType,    // H列: 物料类型
			detail.Product.ProductCategory, // I列: 产品类别
			detail.Product.Spec,            // J列: 规格详情
			detail.Product.Model,           // K列: 型号
			detail.Product.ID,              // L列: 规格ID
			"",                             // M列: 维修类型映射
			"",                             // N列: 配件状态映射
			detail.ID,                      // O列: 详情ID
		}

		// 写入行数据
		cell, err := excelize.CoordinatesToCellName(1, excelRow) // 计算A+行号
		if err != nil {
			return 0, fmt.Errorf("计算单元格位置失败: %w", err)
		}

		if err := f.SetSheetRow(model.ReturnRepairPartSheet, cell, &rowData); err != nil {
			return 0, fmt.Errorf("写入第%d行数据失败: %w", excelRow, err)
		}
	}

	// 设置自动过滤器
	if err := f.AutoFilter(model.ReturnRepairPartSheet, "F1:K1", []excelize.AutoFilterOptions{}); err != nil {
		return 0, fmt.Errorf("设置%s自动过滤器失败: %w", model.ReturnRepairPartSheet, err)
	}

	// 创建下拉菜单
	// 维修类型下拉菜单
	RepairDownMenu, err := SetDropDownMenu(f, "返修入库类型", fmt.Sprintf("C2:C%d", len(details)+1))
	if err != nil {
		return 0, fmt.Errorf("%s设置下拉菜单失败: %w", model.ReturnRepairPartSheet, err)
	}
	if err := f.AddDataValidation(model.ReturnRepairPartSheet, RepairDownMenu); err != nil {
		return 0, fmt.Errorf("添加维修类型下拉菜单失败: %w", err)
	}
	err = s.AssetMapFormula(f, model.ReturnRepairPartSheet, "返修入库类型", len(details)+1, "C", "M", len(returnRepairData)+1)
	if err != nil {
		return 0, err
	}

	// 配件状态下拉菜单
	StatusMenu, err := SetDropDownMenu(f, model.PartStatusSheet, fmt.Sprintf("E2:E%d", len(details)+1))
	if err != nil {
		return 0, fmt.Errorf("%s设置下拉菜单失败: %w", model.ReturnRepairPartSheet, err)
	}
	if err := f.AddDataValidation(model.ReturnRepairPartSheet, StatusMenu); err != nil {
		return 0, fmt.Errorf("添加配件状态类型下拉菜单失败: %w", err)
	}
	err = s.AssetMapFormula(f, model.ReturnRepairPartSheet, model.PartStatusSheet, len(details)+1, "E", "N", len(statusData)+1)
	if err != nil {
		return 0, err
	}

	return index, nil
}

func (s *exportService) exportDismantledPartTemplate(ctx context.Context, compros *model.InboundExportProps, inboundNo string) (int, error) {
	ticket, err := s.inboundTicketRepo.GetInboundTicketByNo(ctx, inboundNo)
	if err != nil {
		return 0, err
	}
	details, err := s.inboundTicketRepo.GetInboundDetailsByNo(ctx, ticket.InboundType, inboundNo)
	if err != nil {
		return 0, err
	}
	// 配件状态数据
	statusData := [][]interface{}{
		{"normal", "正常"},
		{"faulty", "故障"},
	}

	f := compros.F
	commonStyle := compros.CommonStyle
	PropsOptions := compros.PropsOptions
	// 初始化工作表
	headerInfos := []model.HeaderInfoInput{
		{SheetName: model.DismantlePartdSheet, Headers: model.DismantledPartHeader},
		{SheetName: model.PartStatusSheet, Headers: model.PartStatusHeader},
	}
	index, err := s.initSheet(f, headerInfos, PropsOptions)
	if err != nil {
		return 0, err
	}

	// 设置单元格样式
	UnlockStyle := &excelize.Style{
		Border:    commonStyle.Border,    // 继承边框样式
		Alignment: commonStyle.Alignment, // 继承对齐方式
		Protection: &excelize.Protection{
			Locked: false, // 允许编辑
		},
	}
	unLockStyle, err := f.NewStyle(UnlockStyle)
	if err != nil {
		return 0, fmt.Errorf("创建单元格解除保护样式失败: %w", err)
	}

	lockStyle := &excelize.Style{
		Border:    commonStyle.Border,    // 继承边框样式
		Alignment: commonStyle.Alignment, // 继承对齐方式
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"D3D3D3"}, // 灰色的RGB值
			Pattern: 1,                  // 纯色填充
		},
		Protection: &excelize.Protection{
			Locked: true, // 不允许编辑
		},
	}
	LockStyle, err := f.NewStyle(lockStyle)
	if err != nil {
		return 0, fmt.Errorf("创建单元格保护样式失败: %w", err)
	}

	// 设置单元格格式，保护部分列
	// 主表：只允许编辑SN、主设备SN、和配件状态列
	err = f.SetCellStyle(model.DismantlePartdSheet, "A1", fmt.Sprintf("D%d", len(details)+1), unLockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置列样式失败: %w", err)
	}
	err = f.SetCellStyle(model.DismantlePartdSheet, "E1", fmt.Sprintf("M%d", len(details)+1), LockStyle) // 信息列
	if err != nil {
		return 0, fmt.Errorf("设置列样式失败: %w", err)
	}

	if err = f.SetCellStyle(model.PartStatusSheet, "A1", fmt.Sprintf("B%d", len(statusData)+1), LockStyle); err != nil {
		return 0, fmt.Errorf("设置状态表样式失败: %w", err)
	}

	// 设置工作表保护机制
	options := &excelize.SheetProtectionOptions{
		AlgorithmName:       "SHA-256",
		AutoFilter:          true,
		FormatColumns:       true,
		FormatRows:          true,
		Password:            "1qaz2wsx",
		Sort:                true,
		SelectLockedCells:   true,
		SelectUnlockedCells: true,
	}
	sheetProtect := []model.SheetProtectInput{
		{SheetName: model.DismantlePartdSheet, ProtectOption: options},
		{SheetName: model.PartStatusSheet, ProtectOption: options},
	}
	if err = CreateSheetProtection(f, sheetProtect); err != nil {
		return 0, err
	}

	// 写入设备数据
	for rowIndex, detail := range details {
		// 行号从2开始（第1行是表头）
		excelRow := rowIndex + 2

		// 构建数据行
		rowData := []interface{}{
			rowIndex + 1,                   // A列: 序号
			detail.ComponentSN,             // B列: SN号码
			detail.DeviceSN,                // C列: 主设备SN
			"",                             // D列: 配件状态
			detail.Product.PN,              // E列: PN号码
			detail.Product.Brand,           // F列: 品牌
			detail.Product.MaterialType,    // G列: 物料类型
			detail.Product.ProductCategory, // H列: 产品类别
			detail.Product.Spec,            // I列: 规格详情
			detail.Product.Model,           // J列: 型号
			detail.Product.ID,              // K列: 规格ID（使用数据库ID）
			"",                             // L列: 配件状态映射
			detail.ID,                      // M列: 详情ID
		}
		// 写入行数据
		cell, err := excelize.CoordinatesToCellName(1, excelRow) // 计算A+行号
		if err != nil {
			return 0, fmt.Errorf("计算单元格位置失败: %w", err)
		}

		if err := f.SetSheetRow(model.DismantlePartdSheet, cell, &rowData); err != nil {
			return 0, fmt.Errorf("写入第%d行数据失败: %w", excelRow, err)
		}
	}

	for i, row := range statusData {
		cell, err := excelize.CoordinatesToCellName(1, i+2)
		if err != nil {
			return 0, err
		}
		err = f.SetSheetRow(model.PartStatusSheet, cell, &row)
		if err != nil {
			return 0, fmt.Errorf("写入配件状态数据失败: %w", err)
		}
	}

	// 设置自动过滤器
	if err := f.AutoFilter(model.DismantlePartdSheet, "D1:J1", []excelize.AutoFilterOptions{}); err != nil {
		return 0, fmt.Errorf("设置主表自动过滤器失败: %w", err)
	}

	// 状态下拉菜单
	statusDropDown, err := SetDropDownMenu(f, model.PartStatusSheet, fmt.Sprintf("D2:D%d", len(details)+1))
	if err != nil {
		return 0, fmt.Errorf("创建状态下拉菜单失败: %w", err)
	}
	if err := f.AddDataValidation(model.DismantlePartdSheet, statusDropDown); err != nil {
		return 0, fmt.Errorf("添加状态下拉菜单失败: %w", err)
	}

	err = s.AssetMapFormula(f, model.DismantlePartdSheet, model.PartStatusSheet, len(details)+1, "D", "L", len(statusData)+1)

	if err != nil {
		return 0, err
	}
	return index, nil
}

// exportReplacePartTemplate 导出配件改配出库模板
func (s *exportService) exportReplacePartTemplate(ctx context.Context, compros *model.InboundExportProps, outboundID uint) (int, error) {
	details, err := s.outboundRepo.GetDetailsByTicketID(ctx, outboundID)
	if err != nil {
		return 0, err
	}

	f := compros.F
	commonStyle := compros.CommonStyle
	PropsOptions := compros.PropsOptions

	// 初始化工作表
	headerInfos := []model.HeaderInfoInput{
		{SheetName: model.ReplacePartSheet, Headers: model.ReplacePartHeader},
	}
	index, err := s.initSheet(f, headerInfos, PropsOptions)
	if err != nil {
		return 0, err
	}

	// 设置单元格样式
	UnlockStyle := &excelize.Style{
		Border:    commonStyle.Border,    // 继承边框样式
		Alignment: commonStyle.Alignment, // 继承对齐方式
		Protection: &excelize.Protection{
			Locked: false, // 允许编辑
		},
	}
	unLockStyle, err := f.NewStyle(UnlockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置表格格式失败: %w", err)
	}
	// 设置只读单元格样式
	lockStyle := &excelize.Style{
		Border:    commonStyle.Border,    // 继承边框样式
		Alignment: commonStyle.Alignment, // 继承对齐方式
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"D3D3D3"}, // 灰色的RGB值
			Pattern: 1,                  // 纯色填充
		},
		Protection: &excelize.Protection{
			Locked: true, // 不允许编辑
		},
	}
	LockStyle, err := f.NewStyle(lockStyle)
	if err != nil {
		return 0, fmt.Errorf("创建单元格保护样式失败: %w", err)
	}
	// 设置单元格格式，保护部分列
	err = f.SetCellStyle(model.ReplacePartSheet, "A1", fmt.Sprintf("B%d", len(details)+1), unLockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置列样式失败: %w", err)
	}
	err = f.SetCellStyle(model.ReplacePartSheet, "C1", fmt.Sprintf("J%d", len(details)+1), LockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置列样式失败: %w", err)
	}

	// 设置工作表保护
	options := &excelize.SheetProtectionOptions{
		AlgorithmName:       "SHA-256",
		AutoFilter:          true,
		FormatColumns:       true,
		FormatRows:          true,
		Password:            "1qaz2wsx",
		Sort:                true,
		SelectLockedCells:   true,
		SelectUnlockedCells: true,
	}
	sheetProtect := []model.SheetProtectInput{
		{SheetName: model.ReplacePartSheet, ProtectOption: options},
	}
	if err := CreateSheetProtection(f, sheetProtect); err != nil {
		return 0, err
	}
	// 写入数据
	for i, detail := range details {
		rowIndex := i + 2 // 从第2行开始写入数据（第1行是表头）

		var rowData []interface{}
		if detail.Product != nil {
			rowData = []interface{}{
				detail.ComponentSN,             // 配件SN
				"",                             // 服务器SN
				detail.Product.PN,              // PN号码
				detail.Product.Brand,           // 品牌
				detail.Product.MaterialType,    // 物料类型
				detail.Product.ProductCategory, // 产品类别
				detail.Product.Spec,            // 规格详情
				detail.Product.Model,           // 型号
				detail.Product.ID,              // 规格ID
				detail.ID,                      // 详情ID
			}
		} else {
			// 如果没有Product相关信息，只填充已知字段
			rowData = []interface{}{
				detail.ComponentSN, // 配件SN
				detail.DeviceSN,    // 服务器SN
				"",                 // PN号码
				"",                 // 品牌
				"",                 // PN-品牌
				"",                 // 物料类型
				"",                 // 产品类别
				"",                 // 规格详情
				"",                 // 型号
				"",                 // 规格ID
				detail.ID,          // 详情ID
			}
		}

		// 写入行数据
		cell, err := excelize.CoordinatesToCellName(1, rowIndex) // 计算A+行号
		if err != nil {
			return 0, fmt.Errorf("计算单元格位置失败: %w", err)
		}

		if err := f.SetSheetRow(model.ReplacePartSheet, cell, &rowData); err != nil {
			return 0, fmt.Errorf("写入第%d行数据失败: %w", rowIndex, err)
		}
	}

	// 设置自动过滤器
	if err := f.AutoFilter(model.ReplacePartSheet, "C1:I1", []excelize.AutoFilterOptions{}); err != nil {
		return 0, fmt.Errorf("设置主表自动过滤器失败: %w", err)
	}
	return index, nil
}

func (s *exportService) exportAllocatePartTemplate(ctx context.Context, compros *model.InboundExportProps, outboundID uint) (int, error) {
	details, err := s.outboundRepo.GetDetailsByTicketID(ctx, outboundID)
	if err != nil {
		return 0, err
	}

	f := compros.F
	commonStyle := compros.CommonStyle
	PropsOptions := compros.PropsOptions

	// 初始化工作表
	headerInfos := []model.HeaderInfoInput{
		{SheetName: model.AllocatePartSheet, Headers: model.AllocatePartHeader},
	}
	index, err := s.initSheet(f, headerInfos, PropsOptions)
	if err != nil {
		return 0, err
	}

	// 设置单元格样式
	UnlockStyle := &excelize.Style{
		Border:    commonStyle.Border,    // 继承边框样式
		Alignment: commonStyle.Alignment, // 继承对齐方式
		Protection: &excelize.Protection{
			Locked: false, // 允许编辑
		},
	}
	unLockStyle, err := f.NewStyle(UnlockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置表格锁定失败: %w", err)
	}
	// 设置只读单元格样式
	lockStyle := &excelize.Style{
		Border:    commonStyle.Border,    // 继承边框样式
		Alignment: commonStyle.Alignment, // 继承对齐方式
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"D3D3D3"}, // 灰色的RGB值
			Pattern: 1,                  // 纯色填充
		},
		Protection: &excelize.Protection{
			Locked: true, // 不允许编辑
		},
	}
	LockStyle, err := f.NewStyle(lockStyle)
	if err != nil {
		return 0, fmt.Errorf("创建单元格保护样式失败: %w", err)
	}
	// 设置单元格格式，保护部分列
	err = f.SetCellStyle(model.AllocatePartSheet, "A1", fmt.Sprintf("A%d", len(details)+1), unLockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置列样式失败: %w", err)
	}
	err = f.SetCellStyle(model.AllocatePartSheet, "B1", fmt.Sprintf("I%d", len(details)+1), LockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置列样式失败: %w", err)
	}

	// 设置工作表保护
	options := &excelize.SheetProtectionOptions{
		AlgorithmName:       "SHA-256",
		AutoFilter:          true,
		FormatColumns:       true,
		FormatRows:          true,
		Password:            "1qaz2wsx",
		Sort:                true,
		SelectLockedCells:   true,
		SelectUnlockedCells: true,
	}
	sheetProtect := []model.SheetProtectInput{
		{SheetName: model.AllocatePartSheet, ProtectOption: options},
	}
	if err := CreateSheetProtection(f, sheetProtect); err != nil {
		return 0, err
	}
	// 写入数据
	for i, detail := range details {
		rowIndex := i + 2 // 从第2行开始写入数据（第1行是表头）

		var rowData []interface{}
		if detail.Product != nil {
			rowData = []interface{}{
				detail.ComponentSN,             // 配件SN
				detail.Product.PN,              // PN号码
				detail.Product.Brand,           // 品牌
				detail.Product.MaterialType,    // 物料类型
				detail.Product.ProductCategory, // 产品类别
				detail.Product.Spec,            // 规格详情
				detail.Product.Model,           // 型号
				detail.Product.ID,              // 规格ID
				detail.ID,                      // 对应ID
			}
		} else {
			// 如果没有Product相关信息，只填充已知字段
			rowData = []interface{}{
				detail.ComponentSN, // 配件SN
				"",                 // PN号码
				"",                 // 品牌
				"",                 // 物料类型
				"",                 // 产品类别
				"",                 // 规格详情
				"",                 // 型号
				"",                 // 规格ID
				detail.ID,          // 对应ID
			}
		}

		// 写入行数据
		cell, err := excelize.CoordinatesToCellName(1, rowIndex) // 计算A+行号
		if err != nil {
			return 0, fmt.Errorf("计算单元格位置失败: %w", err)
		}

		if err := f.SetSheetRow(model.AllocatePartSheet, cell, &rowData); err != nil {
			return 0, fmt.Errorf("写入第%d行数据失败: %w", rowIndex, err)
		}
	}

	// 设置自动过滤器
	if err := f.AutoFilter(model.AllocatePartSheet, "B1:H1", []excelize.AutoFilterOptions{}); err != nil {
		return 0, fmt.Errorf("设置主表自动过滤器失败: %w", err)
	}
	return index, nil
}

// exportReturnRepairOutPartTemplate 导出返修出库模板
func (s *exportService) exportReturnRepairOutPartTemplate(ctx context.Context, compros *model.InboundExportProps, outboundID uint) (int, error) {
	details, err := s.outboundRepo.GetDetailsByTicketID(ctx, outboundID)
	if err != nil {
		return 0, err
	}

	f := compros.F
	commonStyle := compros.CommonStyle
	PropsOptions := compros.PropsOptions

	// 初始化工作表
	headerInfos := []model.HeaderInfoInput{
		{SheetName: model.ReturnRepairOutPartSheet, Headers: model.ReturnRepairOutPartHeader},
	}
	index, err := s.initSheet(f, headerInfos, PropsOptions)
	if err != nil {
		return 0, err
	}

	// 设置单元格样式
	UnlockStyle := &excelize.Style{
		Border:    commonStyle.Border,    // 继承边框样式
		Alignment: commonStyle.Alignment, // 继承对齐方式
		Protection: &excelize.Protection{
			Locked: false, // 允许编辑
		},
	}
	unLockStyle, err := f.NewStyle(UnlockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置表格锁定失败: %w", err)
	}
	// 设置只读单元格样式
	lockStyle := &excelize.Style{
		Border:    commonStyle.Border,    // 继承边框样式
		Alignment: commonStyle.Alignment, // 继承对齐方式
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"D3D3D3"}, // 灰色的RGB值
			Pattern: 1,                  // 纯色填充
		},
		Protection: &excelize.Protection{
			Locked: true, // 不允许编辑
		},
	}
	LockStyle, err := f.NewStyle(lockStyle)
	if err != nil {
		return 0, fmt.Errorf("创建单元格保护样式失败: %w", err)
	}
	// 设置单元格格式，保护部分列
	err = f.SetCellStyle(model.ReturnRepairOutPartSheet, "A1", fmt.Sprintf("A%d", len(details)+1), unLockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置列样式失败: %w", err)
	}
	err = f.SetCellStyle(model.ReturnRepairOutPartSheet, "B1", fmt.Sprintf("I%d", len(details)+1), LockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置列样式失败: %w", err)
	}

	// 设置工作表保护
	options := &excelize.SheetProtectionOptions{
		AlgorithmName:       "SHA-256",
		AutoFilter:          true,
		FormatColumns:       true,
		FormatRows:          true,
		Password:            "1qaz2wsx",
		Sort:                true,
		SelectLockedCells:   true,
		SelectUnlockedCells: true,
	}
	sheetProtect := []model.SheetProtectInput{
		{SheetName: model.ReturnRepairOutPartSheet, ProtectOption: options},
	}
	if err := CreateSheetProtection(f, sheetProtect); err != nil {
		return 0, err
	}
	// 写入数据
	for i, detail := range details {
		rowIndex := i + 2 // 从第2行开始写入数据（第1行是表头）

		var rowData []interface{}
		if detail.Product != nil {
			rowData = []interface{}{
				detail.ComponentSN,             // 配件SN
				detail.Product.PN,              // PN号码
				detail.Product.Brand,           // 品牌
				detail.Product.MaterialType,    // 物料类型
				detail.Product.ProductCategory, // 产品类别
				detail.Product.Spec,            // 规格详情
				detail.Product.Model,           // 型号
				detail.Product.ID,              // 规格ID
				detail.ID,                      // 对应ID
			}
		} else {
			// 如果没有Product相关信息，只填充已知字段
			rowData = []interface{}{
				detail.ComponentSN, // 配件SN
				"",                 // PN号码
				"",                 // 品牌
				"",                 // 物料类型
				"",                 // 产品类别
				"",                 // 规格详情
				"",                 // 型号
				"",                 // 规格ID
				detail.ID,          // 对应ID
			}
		}

		// 写入行数据
		cell, err := excelize.CoordinatesToCellName(1, rowIndex) // 计算A+行号
		if err != nil {
			return 0, fmt.Errorf("计算单元格位置失败: %w", err)
		}

		if err := f.SetSheetRow(model.ReturnRepairOutPartSheet, cell, &rowData); err != nil {
			return 0, fmt.Errorf("写入第%d行数据失败: %w", rowIndex, err)
		}
	}

	// 设置自动过滤器
	if err := f.AutoFilter(model.ReturnRepairOutPartSheet, "B1:H1", []excelize.AutoFilterOptions{}); err != nil {
		return 0, fmt.Errorf("设置主表自动过滤器失败: %w", err)
	}
	return index, nil
}

// exportRepairOutPartTemplate 导出维修出库模板
func (s *exportService) exportRepairOutPartTemplate(ctx context.Context, compros *model.InboundExportProps, outboundID uint) (int, error) {
	details, err := s.outboundRepo.GetDetailsByTicketID(ctx, outboundID)
	if err != nil {
		return 0, err
	}

	f := compros.F
	commonStyle := compros.CommonStyle
	PropsOptions := compros.PropsOptions

	// 初始化工作表
	headerInfos := []model.HeaderInfoInput{
		{SheetName: model.RepairOutPartSheet, Headers: model.ReturnRepairOutPartHeader},
	}
	index, err := s.initSheet(f, headerInfos, PropsOptions)
	if err != nil {
		return 0, err
	}

	// 设置单元格样式
	UnlockStyle := &excelize.Style{
		Border:    commonStyle.Border,    // 继承边框样式
		Alignment: commonStyle.Alignment, // 继承对齐方式
		Protection: &excelize.Protection{
			Locked: false, // 允许编辑
		},
	}
	unLockStyle, err := f.NewStyle(UnlockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置表格锁定失败: %w", err)
	}
	// 设置只读单元格样式
	lockStyle := &excelize.Style{
		Border:    commonStyle.Border,    // 继承边框样式
		Alignment: commonStyle.Alignment, // 继承对齐方式
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"D3D3D3"}, // 灰色的RGB值
			Pattern: 1,                  // 纯色填充
		},
		Protection: &excelize.Protection{
			Locked: true, // 不允许编辑
		},
	}
	LockStyle, err := f.NewStyle(lockStyle)
	if err != nil {
		return 0, fmt.Errorf("创建单元格保护样式失败: %w", err)
	}
	// 设置单元格格式，保护部分列
	err = f.SetCellStyle(model.RepairOutPartSheet, "A1", fmt.Sprintf("A%d", len(details)+1), unLockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置列样式失败: %w", err)
	}
	err = f.SetCellStyle(model.RepairOutPartSheet, "B1", fmt.Sprintf("I%d", len(details)+1), LockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置列样式失败: %w", err)
	}

	// 设置工作表保护
	options := &excelize.SheetProtectionOptions{
		AlgorithmName:       "SHA-256",
		AutoFilter:          true,
		FormatColumns:       true,
		FormatRows:          true,
		Password:            "1qaz2wsx",
		Sort:                true,
		SelectLockedCells:   true,
		SelectUnlockedCells: true,
	}
	sheetProtect := []model.SheetProtectInput{
		{SheetName: model.RepairOutPartSheet, ProtectOption: options},
	}
	if err := CreateSheetProtection(f, sheetProtect); err != nil {
		return 0, err
	}
	// 写入数据
	for i, detail := range details {
		rowIndex := i + 2 // 从第2行开始写入数据（第1行是表头）

		var rowData []interface{}
		if detail.Product != nil {
			rowData = []interface{}{
				detail.ComponentSN,             // 配件SN
				detail.Product.PN,              // PN号码
				detail.Product.Brand,           // 品牌
				detail.Product.MaterialType,    // 物料类型
				detail.Product.ProductCategory, // 产品类别
				detail.Product.Spec,            // 规格详情
				detail.Product.Model,           // 型号
				detail.Product.ID,              // 规格ID
				detail.ID,                      // 对应ID
			}
		} else {
			// 如果没有Product相关信息，只填充已知字段
			rowData = []interface{}{
				detail.ComponentSN, // 配件SN
				"",                 // PN号码
				"",                 // 品牌
				"",                 // 物料类型
				"",                 // 产品类别
				"",                 // 规格详情
				"",                 // 型号
				"",                 // 规格ID
				detail.ID,          // 对应ID
			}
		}

		// 写入行数据
		cell, err := excelize.CoordinatesToCellName(1, rowIndex) // 计算A+行号
		if err != nil {
			return 0, fmt.Errorf("计算单元格位置失败: %w", err)
		}

		if err := f.SetSheetRow(model.RepairOutPartSheet, cell, &rowData); err != nil {
			return 0, fmt.Errorf("写入第%d行数据失败: %w", rowIndex, err)
		}
	}

	// 设置自动过滤器
	if err := f.AutoFilter(model.RepairOutPartSheet, "B1:H1", []excelize.AutoFilterOptions{}); err != nil {
		return 0, fmt.Errorf("设置主表自动过滤器失败: %w", err)
	}
	return index, nil
}

// exportSellPartTemplate 导出配件售卖出库模板
func (s *exportService) exportSellPartTemplate(ctx context.Context, compros *model.InboundExportProps, outboundID uint) (int, error) {
	details, err := s.outboundRepo.GetDetailsByTicketID(ctx, outboundID)
	if err != nil {
		return 0, err
	}

	f := compros.F
	commonStyle := compros.CommonStyle
	PropsOptions := compros.PropsOptions

	// 初始化工作表
	headerInfos := []model.HeaderInfoInput{
		{SheetName: model.SellPartSheet, Headers: model.ReturnRepairOutPartHeader},
	}
	index, err := s.initSheet(f, headerInfos, PropsOptions)
	if err != nil {
		return 0, err
	}

	// 设置单元格样式
	UnlockStyle := &excelize.Style{
		Border:    commonStyle.Border,    // 继承边框样式
		Alignment: commonStyle.Alignment, // 继承对齐方式
		Protection: &excelize.Protection{
			Locked: false, // 允许编辑
		},
	}
	unLockStyle, err := f.NewStyle(UnlockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置表格锁定失败: %w", err)
	}
	// 设置只读单元格样式
	lockStyle := &excelize.Style{
		Border:    commonStyle.Border,    // 继承边框样式
		Alignment: commonStyle.Alignment, // 继承对齐方式
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"D3D3D3"}, // 灰色的RGB值
			Pattern: 1,                  // 纯色填充
		},
		Protection: &excelize.Protection{
			Locked: true, // 不允许编辑
		},
	}
	LockStyle, err := f.NewStyle(lockStyle)
	if err != nil {
		return 0, fmt.Errorf("创建单元格保护样式失败: %w", err)
	}
	// 设置单元格格式，保护部分列
	err = f.SetCellStyle(model.SellPartSheet, "A1", fmt.Sprintf("A%d", len(details)+1), unLockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置列样式失败: %w", err)
	}
	err = f.SetCellStyle(model.SellPartSheet, "B1", fmt.Sprintf("I%d", len(details)+1), LockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置列样式失败: %w", err)
	}

	// 设置工作表保护
	options := &excelize.SheetProtectionOptions{
		AlgorithmName:       "SHA-256",
		AutoFilter:          true,
		FormatColumns:       true,
		FormatRows:          true,
		Password:            "1qaz2wsx",
		Sort:                true,
		SelectLockedCells:   true,
		SelectUnlockedCells: true,
	}
	sheetProtect := []model.SheetProtectInput{
		{SheetName: model.SellPartSheet, ProtectOption: options},
	}
	if err := CreateSheetProtection(f, sheetProtect); err != nil {
		return 0, err
	}

	// 写入数据
	for i, detail := range details {
		rowIndex := i + 2 // 从第2行开始写入数据（第1行是表头）

		var rowData []interface{}
		if detail.Product != nil {
			rowData = []interface{}{
				detail.ComponentSN,             // 配件SN
				detail.Product.PN,              // PN号码
				detail.Product.Brand,           // 品牌
				detail.Product.MaterialType,    // 物料类型
				detail.Product.ProductCategory, // 产品类别
				detail.Product.Spec,            // 规格详情
				detail.Product.Model,           // 型号
				detail.Product.ID,              // 规格ID
				detail.ID,                      // 对应ID
			}
		} else {
			// 如果没有Product相关信息，只填充已知字段
			rowData = []interface{}{
				detail.ComponentSN, // 配件SN
				"",                 // PN号码
				"",                 // 品牌
				"",                 // 物料类型
				"",                 // 产品类别
				"",                 // 规格详情
				"",                 // 型号
				"",                 // 规格ID
				detail.ID,          // 对应ID
			}
		}

		// 写入行数据
		cell, err := excelize.CoordinatesToCellName(1, rowIndex) // 计算A+行号
		if err != nil {
			return 0, fmt.Errorf("计算单元格位置失败: %w", err)
		}

		if err := f.SetSheetRow(model.SellPartSheet, cell, &rowData); err != nil {
			return 0, fmt.Errorf("写入第%d行数据失败: %w", rowIndex, err)
		}
	}

	// 设置自动过滤器
	if err := f.AutoFilter(model.SellPartSheet, "B1:H1", []excelize.AutoFilterOptions{}); err != nil {
		return 0, fmt.Errorf("设置主表自动过滤器失败: %w", err)
	}
	return index, nil
}

// exportRackDeviceTemplate 导出设备上架模板
func (s *exportService) exportRackDeviceTemplate(ctx context.Context, compros *model.InboundExportProps, outboundID uint) (int, error) {
	return s.exportDeviceTemplate(ctx, compros, model.RackDeviceSheet, "", outboundID, false)
}

// exportAllocateDeviceTemplate 导出设备调拨模板
func (s *exportService) exportAllocateDeviceTemplate(ctx context.Context, compros *model.InboundExportProps, outboundID uint) (int, error) {
	return s.exportDeviceTemplate(ctx, compros, model.AllocateDeviceSheet, "", outboundID, false)
}

// CreateSheetProtection 创建工作表保护机制
func CreateSheetProtection(f *excelize.File, SheetProtects []model.SheetProtectInput) error {
	for _, sheetProtect := range SheetProtects {
		if err := f.ProtectSheet(sheetProtect.SheetName, sheetProtect.ProtectOption); err != nil {
			return fmt.Errorf("工作表%v设置保护机制失败: %w", sheetProtect.SheetName, err)
		}
	}
	return nil
}

// SetDropDownMenu 设置下拉列表。最多支持32个选项
// f *excelize.File 文件指针, SheetName 下拉列表的数据来源, Sqref 下拉列表的作用范围 "D2:D1000"
func SetDropDownMenu(f *excelize.File, SheetName, Sqref string) (*excelize.DataValidation, error) {
	// 先获取信息表中的所有名称
	Sheet, err := f.GetCols(SheetName)
	if err != nil {
		return nil, fmt.Errorf("获取%v数据失败: %w", SheetName, err)
	}
	if len(Sheet) < 2 { // 确保有数据
		return nil, fmt.Errorf("%v数据不足: %w", SheetName, err)
	}
	// 构建下拉选项列表(跳过表头，从第2行开始)
	var Options []string
	for i := 1; i < len(Sheet[1]); i++ {
		Options = append(Options, Sheet[1][i])
	}
	// 创建数据验证对象
	dv := excelize.NewDataValidation(true)
	dv.SetSqref(Sqref)                                                      // 应用范围
	dv.SetError(excelize.DataValidationErrorStyleStop, "输入错误", "请从下拉列表中选择") // 设置错误提示
	if err := dv.SetDropList(Options); err != nil {                         //设置下拉列表
		return nil, fmt.Errorf("获取下拉表格失败: %w", err)
	}
	//fmt.Println("dv:", dv)
	return dv, nil
}

// SetDropDownMenuV2 使用Excel命名区域设置下拉选框
func SetDropDownMenuV2(f *excelize.File, SheetName, Sqref string) (*excelize.DataValidation, error) {
	// 获取信息表中的所有名称
	Sheet, err := f.GetCols(SheetName)
	if err != nil {
		return nil, fmt.Errorf("获取%v数据失败: %w", SheetName, err)
	}
	if len(Sheet) < 2 {
		return nil, fmt.Errorf("%v数据不足", SheetName)
	}

	// 创建一个隐藏的工作表来存储下拉选项
	hiddenSheet := "DropdownOptions"
	sheetIndex, err := f.GetSheetIndex(hiddenSheet)
	// 如果工作表不存在，创建它
	if err != nil || sheetIndex == -1 {
		_, err := f.NewSheet(hiddenSheet)
		if err != nil {
			return nil, fmt.Errorf("创建隐藏工作表失败: %w", err)
		}

		err = f.SetSheetVisible(hiddenSheet, false)
		if err != nil {
			return nil, fmt.Errorf("隐藏工作表失败: %w", err)
		} // 隐藏工作表
	}

	// 将选项写入隐藏工作表
	for i := 1; i < len(Sheet[1]); i++ {
		cell, err := excelize.CoordinatesToCellName(1, i)
		if err != nil {
			return nil, err
		}
		if err := f.SetCellValue(hiddenSheet, cell, Sheet[1][i]); err != nil {
			return nil, fmt.Errorf("设置下拉列表工作表单元格数值失败: %w", err)
		}
	}

	// 定义命名区域
	lastRow := len(Sheet[1])
	namedRange := fmt.Sprintf("%s!$A$1:$A$%d", hiddenSheet, lastRow)
	//f.AddDefinedName(namedRange, hiddenSheet, "DropdownList")

	// 创建数据验证对象，引用命名区域
	dv := excelize.NewDataValidation(true)
	dv.SetSqref(Sqref)
	dv.SetError(excelize.DataValidationErrorStyleStop, "输入错误", "请从下拉列表中选择")

	// 使用Formula1引用命名区域
	dv.Formula1 = namedRange

	// 将数据验证应用到工作表
	return dv, nil
}

// SetPurchaseInboundFormula 设置采购入库单的相关公式
// f *excelize.File 文件指针, rows int 支持导入的最大行数, nums 规格的数量
func SetPurchaseInboundFormula(f *excelize.File, rows, nums int) error {
	sourceLookupCol := 2                                                           // C列
	sourceLookupColLetter, err := excelize.ColumnNumberToName(sourceLookupCol + 1) // 转换为"C"
	if err != nil {
		return fmt.Errorf("%v的第%v列转换失败: %w", model.ProductSheet, sourceLookupCol, err)
	}

	// 源表数据范围（C列到H列）
	sourceRange := fmt.Sprintf("$%s:$H", sourceLookupColLetter)
	for i := 2; i <= nums+1; i++ {
		formula := fmt.Sprintf("=A%d&\"-\"&B%d", i, i)
		cell := fmt.Sprintf("C%d", i)
		if err := f.SetCellFormula(model.ProductSheet, cell, formula); err != nil {
			return fmt.Errorf("设置规格详情表表PN-Brand公式错误: %w", err)
		}
	}

	// 给每一行都设置公式
	for i := 2; i < rows; i++ {
		// 生成分隔符 PN-Brand
		formula := fmt.Sprintf("=A%d&\"-\"&B%d", i, i)
		cell := fmt.Sprintf("D%d", i)
		if err := f.SetCellFormula(model.PurchaseInboundSheet, cell, formula); err != nil {
			return fmt.Errorf("设置采购入库表PN-Brand公式错误: %w", err)
		}

		/*设置每一列的数据映射*/
		// VLOOKUP(查找值，查找范围，返回值所在列数，精确或模糊查找)

		// 设置PN-品牌查询条件，查询物料类型
		formula = fmt.Sprintf(`=IFERROR(VLOOKUP($D%d,%v!%v,2,FALSE),"")`, i, model.ProductSheet, sourceRange)
		if err := f.SetCellFormula(model.PurchaseInboundSheet, fmt.Sprintf("E%d", i), formula); err != nil {
			return fmt.Errorf("设置物料类型查找公式失败: %w", err)
		}

		// 设置PN-品牌查询条件，查询产品类别
		formula = fmt.Sprintf(`=IFERROR(VLOOKUP($D%d,%v!%v,3,FALSE),"")`, i, model.ProductSheet, sourceRange)
		if err := f.SetCellFormula(model.PurchaseInboundSheet, fmt.Sprintf("F%d", i), formula); err != nil {
			return fmt.Errorf("设置产品类别查找公式失败: %w", err)
		}

		// 设置PN-品牌查询条件，查询规格详情
		formula = fmt.Sprintf(`=IFERROR(VLOOKUP($D%d,%v!%v,4,FALSE),"")`, i, model.ProductSheet, sourceRange)
		if err := f.SetCellFormula(model.PurchaseInboundSheet, fmt.Sprintf("G%d", i), formula); err != nil {
			return fmt.Errorf("设置规格详情查找公式失败: %w", err)
		}

		// 设置PN-品牌查询条件，查询型号
		formula = fmt.Sprintf(`=IFERROR(VLOOKUP($D%d,%v!%v,5,FALSE),"")`, i, model.ProductSheet, sourceRange)
		if err := f.SetCellFormula(model.PurchaseInboundSheet, fmt.Sprintf("H%d", i), formula); err != nil {
			return fmt.Errorf("设置型号查找公式失败: %w", err)
		}

		// 设置PN-品牌查询条件，查询规格ID
		formula = fmt.Sprintf(`=IFERROR(VLOOKUP($D%d,%v!%v,6,FALSE),"")`, i, model.ProductSheet, sourceRange)
		if err := f.SetCellFormula(model.PurchaseInboundSheet, fmt.Sprintf("I%d", i), formula); err != nil {
			return fmt.Errorf("设置规格ID查找公式失败: %w", err)
		}

	}

	return nil
}

// SetNewPartDetailFormula rows 表示可以承载的最大数量，nums表示仓库表的行数
func SetNewPartDetailFormula(f *excelize.File, rows, nums int) error {
	for i := 2; i < rows+1; i++ {
		formula := fmt.Sprintf("=INDEX(%s!$A$1:$A$%d, MATCH(B%d, %s!$B$1:$B$%d, 0))", model.WarehouseSheet, nums, i, model.WarehouseSheet, nums)
		cell := fmt.Sprintf("K%d", i)
		if err := f.SetCellFormula(model.NewPartSheet, cell, formula); err != nil {
			return fmt.Errorf("设置%s单元格公式失败: %w", model.NewPartSheet, err)
		}
	}
	return nil
}

// SetRepairPartFormula rows 表示可以承载的最大数量，nums表示仓库表的行数
func SetRepairPartFormula(f *excelize.File, rows, nums int) error {
	for i := 2; i < rows+1; i++ {
		formula := fmt.Sprintf("=INDEX(%s!$A$1:$A$%d, MATCH(D%d, %s!$B$1:$B$%d, 0))", model.WarehouseSheet, nums, i, model.WarehouseSheet, nums)
		cell := fmt.Sprintf("F%d", i)
		if err := f.SetCellFormula(model.ReturnRepairPartSheet, cell, formula); err != nil {
			return fmt.Errorf("设置%s单元格公式失败: %w", model.ReturnRepairPartSheet, err)
		}
		formula = fmt.Sprintf("=INDEX(%s!$A$1:$A$%d, MATCH(B%d, %s!$B$1:$B$%d, 0))", "返修入库类型", nums, i, "返修入库类型", nums)
		cell = fmt.Sprintf("E%d", i)
		if err := f.SetCellFormula(model.ReturnRepairPartSheet, cell, formula); err != nil {
			return fmt.Errorf("设置%s单元格公式失败: %w", model.ReturnRepairPartSheet, err)
		}
	}
	return nil
}

func SetDismantledPartFormula(f *excelize.File, rows, nums int) error {
	// 从第二行开始设置公式（第一行是表头）
	for i := 2; i < rows+1; i++ {
		// 1. 根据PN和品牌自动填充产品信息
		// 在产品表中查找匹配的行，并填充相应信息
		// PN-品牌 (G列)
		formula := fmt.Sprintf("=E%d&\"-\"&F%d", i, i)
		cell := fmt.Sprintf("G%d", i)
		if err := f.SetCellFormula(model.DismantlePartdSheet, cell, formula); err != nil {
			return fmt.Errorf("设置PN-品牌公式失败: %w", err)
		}

		// 根据PN和品牌查找产品信息
		// VLOOKUP公式查找匹配的产品信息
		// 物料类型 (H列)
		formula = fmt.Sprintf("=VLOOKUP(G%d,%s!C:H,2,FALSE)", i, model.ProductSheet)
		cell = fmt.Sprintf("H%d", i)
		if err := f.SetCellFormula(model.DismantlePartdSheet, cell, formula); err != nil {
			return fmt.Errorf("设置物料类型公式失败: %w", err)
		}

		// 产品类别 (I列)
		formula = fmt.Sprintf("=VLOOKUP(G%d,%s!C:H,3,FALSE)", i, model.ProductSheet)
		cell = fmt.Sprintf("I%d", i)
		if err := f.SetCellFormula(model.DismantlePartdSheet, cell, formula); err != nil {
			return fmt.Errorf("设置产品类别公式失败: %w", err)
		}

		// 规格详情 (J列)
		formula = fmt.Sprintf("=VLOOKUP(G%d,%s!C:H,4,FALSE)", i, model.ProductSheet)
		cell = fmt.Sprintf("J%d", i)
		if err := f.SetCellFormula(model.DismantlePartdSheet, cell, formula); err != nil {
			return fmt.Errorf("设置规格详情公式失败: %w", err)
		}

		// 型号 (K列)
		formula = fmt.Sprintf("=VLOOKUP(G%d,%s!C:H,5,FALSE)", i, model.ProductSheet)
		cell = fmt.Sprintf("K%d", i)
		if err := f.SetCellFormula(model.DismantlePartdSheet, cell, formula); err != nil {
			return fmt.Errorf("设置型号公式失败: %w", err)
		}

		// 规格ID (L列)
		formula = fmt.Sprintf("=VLOOKUP(G%d,%s!C:H,6,FALSE)", i, model.ProductSheet)
		cell = fmt.Sprintf("L%d", i)
		if err := f.SetCellFormula(model.DismantlePartdSheet, cell, formula); err != nil {
			return fmt.Errorf("设置规格ID公式失败: %w", err)
		}

		// 2. 根据选择的仓库名称自动填充仓库ID (M列)
		formula = fmt.Sprintf("=INDEX(%s!$A$1:$A$%d,MATCH(D%d,%s!$B$1:$B$%d,0))",
			model.WarehouseSheet, nums, i, model.WarehouseSheet, nums)
		cell = fmt.Sprintf("M%d", i)
		if err := f.SetCellFormula(model.DismantlePartdSheet, cell, formula); err != nil {
			return fmt.Errorf("设置仓库ID公式失败: %w", err)
		}

		// 3. 根据选择的配件状态自动填充状态映射 (N列)
		formula = fmt.Sprintf("=INDEX(%s!$A$1:$A$%d,MATCH(C%d,%s!$B$1:$B$%d,0))",
			model.PartStatusSheet, nums, i, model.PartStatusSheet, nums)
		cell = fmt.Sprintf("N%d", i)
		if err := f.SetCellFormula(model.DismantlePartdSheet, cell, formula); err != nil {
			return fmt.Errorf("设置状态映射公式失败: %w", err)
		}
	}
	return nil
}

// AssetMapFormula 设置资产状态映射公式
// targetSheet 目标工作表，sourceSheet 数据来源工作表，targetSheetLen 目标工作表的行数，targetCol 目标工作表的列号，mapCol 被映射的列号,statusSheetLen 状态工作表的行数
func (s *exportService) AssetMapFormula(f *excelize.File, targetSheet, sourceSheet string, targetSheetLen int, targetCol, mapCol string, statusSheetLen int) error {
	for i := 2; i < targetSheetLen+1; i++ {
		formula := fmt.Sprintf("=INDEX(%s!$A$1:$A$%d,MATCH(%s%d,%s!$B$1:$B$%d,0))",
			sourceSheet, statusSheetLen, targetCol, i, sourceSheet, statusSheetLen)
		cell := fmt.Sprintf("%s%d", mapCol, i)
		if err := f.SetCellFormula(targetSheet, cell, formula); err != nil {
			return fmt.Errorf("设置状态映射公式失败: %w", err)
		}
	}
	return nil
}

// exportDeviceTemplate 统一的设备模板导出函数
func (s *exportService) exportDeviceTemplate(ctx context.Context, compros *model.InboundExportProps, sheetName, inboundNo string, outboundID uint, isInbound bool) (int, error) {
	var (
		inboundDetails  []ticketModel.InboundDetail
		outboundDetails []outbound.OutboundDetail
		lenDetails      int
	)
	var err error
	headers := model.DeviceInboundHeader

	// 根据是入库还是出库获取对应的数据
	if isInbound {
		// 获取入库单详情数据
		ticket, err := s.inboundTicketRepo.GetInboundTicketByNo(ctx, inboundNo)
		if err != nil {
			return 0, err
		}
		inboundDetails, err = s.inboundTicketRepo.GetInboundDetailsByNo(ctx, ticket.InboundType, inboundNo)
		if err != nil {
			return 0, err
		}
		lenDetails = len(inboundDetails)
	} else {
		// 获取出库单详情数据
		outboundDetails, err = s.outboundRepo.GetDetailsByTicketID(ctx, outboundID)
		if err != nil {
			return 0, err
		}
		lenDetails = len(outboundDetails)
	}

	f := compros.F
	commonStyle := compros.CommonStyle
	PropsOptions := compros.PropsOptions

	// 初始化工作表
	headerInfos := []model.HeaderInfoInput{
		{SheetName: sheetName, Headers: headers},
	}
	index, err := s.initSheet(f, headerInfos, PropsOptions)
	if err != nil {
		return 0, err
	}

	// 设置单元格样式
	UnlockStyle := &excelize.Style{
		Border:    commonStyle.Border,    // 继承边框样式
		Alignment: commonStyle.Alignment, // 继承对齐方式
		Protection: &excelize.Protection{
			Locked: false, // 允许编辑
		},
	}
	unLockStyle, err := f.NewStyle(UnlockStyle)
	if err != nil {
		return 0, fmt.Errorf("创建单元格解除保护样式失败: %w", err)
	}

	lockStyle := &excelize.Style{
		Border:    commonStyle.Border,    // 继承边框样式
		Alignment: commonStyle.Alignment, // 继承对齐方式
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"D3D3D3"}, // 灰色的RGB值
			Pattern: 1,                  // 纯色填充
		},
		Protection: &excelize.Protection{
			Locked: true, // 不允许编辑
		},
	}
	LockStyle, err := f.NewStyle(lockStyle)
	if err != nil {
		return 0, fmt.Errorf("创建单元格保护样式失败: %w", err)
	}

	// 设置单元格格式，保护部分列
	// 序号和设备SN列可编辑，其他列锁定
	err = f.SetCellStyle(sheetName, "A1", fmt.Sprintf("B%d", lenDetails+1), unLockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置可编辑列样式失败: %w", err)
	}
	err = f.SetCellStyle(sheetName, "C1", fmt.Sprintf("J%d", lenDetails+1), LockStyle)
	if err != nil {
		return 0, fmt.Errorf("设置锁定列样式失败: %w", err)
	}

	if isInbound {
		// 入库模板数据处理
		for i, d := range inboundDetails {
			rowIndex := i + 2 // 从第2行开始写入数据（第1行是表头）
			// 构造数据行
			rowData := []interface{}{
				i + 1,                     // 序号
				d.ComponentSN,             // SN
				d.Product.PN,              // PN号码
				d.Product.Brand,           // 品牌
				d.Product.MaterialType,    // 物料类型
				d.Product.ProductCategory, // 产品类别
				d.Product.Spec,            // 规格详情
				d.Product.Model,           // 型号
				d.Product.ID,              // 规格ID
				d.ID,                      // 入库ID
			}
			// 写入行数据
			cell, err := excelize.CoordinatesToCellName(1, rowIndex)
			if err != nil {
				return 0, fmt.Errorf("计算单元格位置失败: %w", err)
			}

			if err := f.SetSheetRow(sheetName, cell, &rowData); err != nil {
				return 0, fmt.Errorf("写入第%d行数据失败: %w", rowIndex, err)
			}
		}
	} else {
		// 出库模板数据处理
		for i, d := range outboundDetails {
			rowIndex := i + 2 // 从第2行开始写入数据（第1行是表头）
			// 构造数据行
			rowData := []interface{}{
				rowIndex + 1,              // 序号
				d.ComponentSN,             // SN
				d.Product.PN,              // PN号码
				d.Product.Brand,           // 品牌
				d.Product.MaterialType,    // 物料类型
				d.Product.ProductCategory, // 产品类别
				d.Product.Spec,            // 规格详情
				d.Product.Model,           // 型号
				d.Product.ID,              // 规格ID
				d.ID,                      // 入库ID
			}
			// 写入行数据
			cell, err := excelize.CoordinatesToCellName(1, rowIndex)
			if err != nil {
				return 0, fmt.Errorf("计算单元格位置失败: %w", err)
			}

			if err := f.SetSheetRow(sheetName, cell, &rowData); err != nil {
				return 0, fmt.Errorf("写入第%d行数据失败: %w", rowIndex, err)
			}
		}

	}

	// 设置工作表保护
	options := &excelize.SheetProtectionOptions{
		AlgorithmName:       "SHA-256",
		AutoFilter:          true,
		FormatColumns:       true,
		FormatRows:          true,
		Password:            "1qaz2wsx",
		Sort:                true,
		SelectLockedCells:   true,
		SelectUnlockedCells: true,
	}
	sheetProtect := []model.SheetProtectInput{
		{SheetName: sheetName, ProtectOption: options},
	}
	if err := CreateSheetProtection(f, sheetProtect); err != nil {
		return 0, err
	}

	// 设置自动过滤器
	if err := f.AutoFilter(sheetName, "C1:H1", []excelize.AutoFilterOptions{}); err != nil {
		return 0, fmt.Errorf("设置自动过滤器失败: %w", err)
	}

	return index, nil
}
