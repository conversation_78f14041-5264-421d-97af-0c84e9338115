package excel

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"backend/internal/modules/export/model"

	"github.com/xuri/excelize/v2"
)

// DataProvider 数据提供者接口
type DataProvider interface {
	GetData(ctx context.Context, tableName string, ids []int64, condition string) ([]map[string]interface{}, error)
	GetAllData(ctx context.Context, tableName string, condition string) ([]map[string]interface{}, error)
}

// ExcelExporter Excel导出器
type ExcelExporter struct {
	dataProvider DataProvider
	storagePath  string
}

// NewExcelExporter 创建Excel导出器
func NewExcelExporter(provider DataProvider, storagePath string) *ExcelExporter {
	// 确保存储目录存在
	if err := os.MkdirAll(storagePath, 0750); err != nil {
		fmt.Printf("创建导出目录失败: %v，将使用临时目录\n", err)
	}

	return &ExcelExporter{
		dataProvider: provider,
		storagePath:  storagePath,
	}
}

// Export 执行导出
func (e *ExcelExporter) Export(ctx context.Context, req *model.ExportRequest) (*model.ExportResponse, error) {
	// 创建新的Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Printf("关闭Excel文件失败: %v\n", err)
		}
	}()

	// 设置sheet名称
	if req.SheetName != "" {
		if err := f.SetSheetName("Sheet1", req.SheetName); err != nil {
			return nil, fmt.Errorf("设置工作表名称失败: %v", err)
		}
	}

	// 获取数据
	var data []map[string]interface{}
	var err error
	if req.Mode == "selected" && len(req.IDs) > 0 {
		data, err = e.dataProvider.GetData(ctx, req.TableName, req.IDs, req.Condition)
	} else {
		data, err = e.dataProvider.GetAllData(ctx, req.TableName, req.Condition)
	}
	if err != nil {
		return nil, fmt.Errorf("获取数据失败: %v", err)
	}

	// 写入表头
	if req.IsHeader {
		headers := make([]string, len(req.Fields))
		for i, field := range req.Fields {
			headers[i] = field.Title
		}
		if err := f.SetSheetRow(req.SheetName, "A1", &headers); err != nil {
			return nil, fmt.Errorf("写入表头失败: %v", err)
		}
	}

	// 写入数据
	startRow := 2
	if !req.IsHeader {
		startRow = 1
	}

	for i, row := range data {
		values := make([]interface{}, len(req.Fields))
		for j, field := range req.Fields {
			// 使用前端提供的字段名从数据中获取值
			values[j] = row[field.Field]
		}
		cell, err := excelize.CoordinatesToCellName(1, i+startRow)
		if err != nil {
			return nil, fmt.Errorf("生成单元格坐标失败: %v", err)
		}
		if err := f.SetSheetRow(req.SheetName, cell, &values); err != nil {
			return nil, fmt.Errorf("写入数据失败: %v", err)
		}
	}

	// 生成文件名
	if req.Filename == "" {
		req.Filename = fmt.Sprintf("export_%s.xlsx", time.Now().Format("20060102150405"))
	}

	// 保存文件
	filePath := filepath.Join(e.storagePath, req.Filename)

	// 再次确保存储目录存在
	if err := os.MkdirAll(e.storagePath, 0750); err != nil {
		return nil, fmt.Errorf("创建存储目录失败: %v", err)
	}

	if err := f.SaveAs(filePath); err != nil {
		return nil, fmt.Errorf("保存Excel文件失败: %v", err)
	}

	// 返回文件URL和过期时间
	return &model.ExportResponse{
		FileURL: fmt.Sprintf("/download/%s", req.Filename),
		Expired: time.Now().Add(24 * time.Hour), // 24小时后过期
	}, nil
}
