package model

import (
	"context"
	"time"

	"github.com/xuri/excelize/v2"
)

// Field 导出字段定义
type Field struct {
	Field string `json:"field"`
	Title string `json:"title"`
}

// ExportRequest 导出请求
type ExportRequest struct {
	TableName string  `json:"tableName"` // 表名
	Mode      string  `json:"mode"`      // 导出模式：all/selected
	IDs       []int64 `json:"ids"`       // 选定的ID列表
	Fields    []Field `json:"fields"`    // 导出字段
	Filename  string  `json:"filename"`  // 文件名
	SheetName string  `json:"sheetName"` // 工作表名称
	IsHeader  bool    `json:"isHeader"`  // 是否包含表头
	Condition string  `json:"condition"` // 额外的查询条件
}

// ExportResponse 导出响应
type ExportResponse struct {
	FileURL string    `json:"fileUrl"` // 文件下载URL
	Expired time.Time `json:"expired"` // 过期时间
}

// Exporter 导出器接口
type Exporter interface {
	Export(ctx context.Context, req *ExportRequest) (*ExportResponse, error)
}

// DataProvider 数据提供者接口
type DataProvider interface {
	GetData(ctx context.Context, tableName string, ids []int64, condition string) ([]map[string]interface{}, error)
	GetAllData(ctx context.Context, tableName string, condition string) ([]map[string]interface{}, error)
}

type InboundExportDTO struct {
	InboundType  string `json:"inbound_type"`                     // 入库类型
	TemplateType string `json:"template_type" binding:"required"` // 需要的模板
}

// InboundExportProps 入库导出的公共属性
type InboundExportProps struct {
	F            *excelize.File
	CommonStyle  *excelize.Style             // 单元格公共样式
	PropsOptions *excelize.SheetPropsOptions // 工作表公共属性
}

// 表头元素
type HeaderInfoInput struct {
	SheetName string
	Headers   []string
}

// 表格保护输入
type SheetProtectInput struct {
	SheetName     string
	ProtectOption *excelize.SheetProtectionOptions
}
