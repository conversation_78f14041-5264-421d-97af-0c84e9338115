package user

import (
	"backend/internal/infrastructure/app"
	"backend/internal/infrastructure/auth"
	"backend/internal/infrastructure/cache"
	"backend/internal/modules/user/controller"
	"backend/internal/modules/user/model"
	"backend/internal/modules/user/repository"
	"backend/internal/modules/user/service"
	"context"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// RegisterUserModule 注册用户模块
func RegisterUserModule(router *gin.RouterGroup, app *app.App) service.IUserService {
	// 获取JWT密钥
	jwtSecret := auth.GetJWTSecretKey()

	// 创建用户模块实例
	userModule := NewModule(app.DB, app.Logger, app.RedisCache, jwtSecret)

	// 初始化模块
	if err := userModule.Initialize(); err != nil {
		app.Logger.Error("初始化用户模块失败", zap.Error(err))
		return nil
	}

	// 执行数据库迁移
	if err := userModule.AutoMigrate(); err != nil {
		app.Logger.Error("用户模块数据库迁移失败", zap.Error(err))
		return nil
	}

	// 注册路由
	userModule.RegisterRoutes(router)

	// 返回用户服务实例
	return userModule.GetUserService()
}

// Module 用户模块
type Module struct {
	db         *gorm.DB
	logger     *zap.Logger
	redisCache *cache.RedisClient
	jwtSecret  string

	// 服务
	userService service.IUserService

	// 控制器
	userController *controller.UserController
}

// NewModule 创建用户模块
func NewModule(db *gorm.DB, logger *zap.Logger, redisCache *cache.RedisClient, jwtSecret string) *Module {
	return &Module{
		db:         db,
		logger:     logger,
		redisCache: redisCache,
		jwtSecret:  jwtSecret,
	}
}

// UserModuleInjector 用户模块依赖注入器
type UserModuleInjector struct {
	DB         *gorm.DB
	Logger     *zap.Logger
	RedisCache *cache.RedisClient
	JWTSecret  string
}

// NewUserModuleInjector 创建用户模块依赖注入器
func NewUserModuleInjector(db *gorm.DB, logger *zap.Logger, redisCache *cache.RedisClient, jwtSecret string) *UserModuleInjector {
	return &UserModuleInjector{
		DB:         db,
		Logger:     logger,
		RedisCache: redisCache,
		JWTSecret:  jwtSecret,
	}
}

// InjectUserController 注入用户控制器
func (i *UserModuleInjector) InjectUserController() (*controller.UserController, service.IUserService) {
	// 创建仓储层
	userRepo := repository.NewUserRepository(i.DB)

	// 创建服务层
	userService := service.NewUserService(userRepo, i.JWTSecret, auth.GetJWTExpireDuration(), i.RedisCache, i.Logger)

	// 创建控制器
	return controller.NewUserController(userService), userService
}

// Initialize 初始化模块
func (m *Module) Initialize() error {
	// 使用依赖注入器初始化控制器
	injector := NewUserModuleInjector(m.db, m.logger, m.redisCache, m.jwtSecret)

	// 注入控制器
	var userService service.IUserService
	m.userController, userService = injector.InjectUserController()
	m.userService = userService

	// 仅当logger不为空时才打印日志
	if m.logger != nil {
		// 初始化超级管理员账号
		m.logger.Info("开始初始化超级管理员账号...")
		if err := m.userService.InitSuperAdmin(context.Background()); err != nil {
			m.logger.Error("初始化超级管理员账号失败", zap.Error(err))
		} else {
			m.logger.Info("超级管理员账号初始化完成")
		}
	} else {
		// 没有logger时也要初始化超级管理员
		if err := m.userService.InitSuperAdmin(context.Background()); err != nil {
			return err
		}
	}

	return nil
}

// AutoMigrate 自动迁移数据库
func (m *Module) AutoMigrate() error {
	// 执行用户表的迁移
	if err := m.db.AutoMigrate(&model.User{}); err != nil {
		return err
	}

	return nil
}

// RegisterRoutes 注册路由
func (m *Module) RegisterRoutes(router *gin.RouterGroup) {
	// 认证相关路由（无需认证）
	authRouter := router.Group("/auth")
	{
		// authRouter.POST("/register", m.userController.Register) // 用户注册
		authRouter.POST("/login", m.userController.Login) // 用户登录

		// 飞书登录相关路由
		feishuRouter := authRouter.Group("/feishu")
		{
			feishuRouter.GET("/auth-url", m.userController.GetFeishuAuthURL)    // 获取飞书授权URL
			feishuRouter.POST("/login", m.userController.FeishuLogin)          // 飞书登录
			feishuRouter.POST("/bind", m.userController.BindFeishuAccount)     // 绑定飞书账号
		}
	}

	// 需要认证的路由
	protectedRouter := router.Group("/auth")
	// 全局已经添加了AuthMiddleware和AuthorizeWithCasbin，这里不需要重复添加
	// protectedRouter.Use(middleware.AuthMiddleware()) // JWT 认证中间件

	{
		protectedRouter.POST("/logout", m.userController.Logout)                                      // 用户登出
		protectedRouter.GET("/userInfo", m.userController.GetUserInfo)                                // 获取用户信息
		protectedRouter.GET("/codes", m.userController.GetAuthCodes)                                  // 获取用户按钮权限
		protectedRouter.GET("/check-permission-status", m.userController.CheckPermissionUpdateStatus) // 获取权限更新状态
		protectedRouter.POST("/change-password", m.userController.ChangePassword)                     // 修改密码
		protectedRouter.GET("/user-list", m.userController.GetUserList)                               // 获取用户列表
		protectedRouter.PUT("/user-update", m.userController.UpdateUser)                              // 更新用户信息
		protectedRouter.DELETE("/user/:id", m.userController.SoftDeleteUser)                          // 软删除用户
		protectedRouter.POST("/admin/create-user", m.userController.CreateUser)                       // 管理员创建用户
		protectedRouter.POST("/admin/reset-password", m.userController.ResetUserPassword)             // 超级管理员重置用户密码
		protectedRouter.POST("/clear-permission-flag", m.userController.ClearPermissionUpdateFlag)    // 清除权限更新标志位
	}
}

// GetUserService 获取用户服务实例，供其他模块使用
func (m *Module) GetUserService() service.IUserService {
	return m.userService
}
