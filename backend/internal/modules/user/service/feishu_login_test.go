package service

import (
	"backend/internal/modules/user/model"
	"backend/pkg/feishuapp"
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockFeishuOAuthClient 模拟飞书OAuth客户端
type MockFeishuOAuthClient struct {
	mock.Mock
}

func (m *MockFeishuOAuthClient) GenerateState() (string, error) {
	args := m.Called()
	return args.String(0), args.Error(1)
}

func (m *MockFeishuOAuthClient) GetAuthURL(state string) string {
	args := m.Called(state)
	return args.String(0)
}

func (m *MockFeishuOAuthClient) ExchangeToken(ctx context.Context, code string) (*feishuapp.TokenResponse, error) {
	args := m.Called(ctx, code)
	return args.Get(0).(*feishuapp.TokenResponse), args.Error(1)
}

func (m *MockFeishuOAuthClient) GetUserInfo(ctx context.Context, accessToken string) (*feishuapp.UserInfoResponse, error) {
	args := m.Called(ctx, accessToken)
	return args.Get(0).(*feishuapp.UserInfoResponse), args.Error(1)
}

func (m *MockFeishuOAuthClient) ValidateState(state string) error {
	args := m.Called(state)
	return args.Error(0)
}

// TestGetFeishuAuthURL 测试获取飞书授权URL
func TestGetFeishuAuthURL(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 创建测试路由
	router := gin.New()

	// 这里需要注入模拟的用户控制器
	// 实际测试中需要设置完整的依赖注入

	// 测试获取授权URL
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/v1/auth/feishu/auth-url", nil)
	router.ServeHTTP(w, req)

	// 验证响应
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// 验证响应结构
	assert.Contains(t, response, "data")
	data := response["data"].(map[string]interface{})
	assert.Contains(t, data, "authUrl")
	assert.Contains(t, data, "state")
}

// TestFeishuLogin 测试飞书登录
func TestFeishuLogin(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 准备测试数据
	loginReq := model.FeishuLoginRequest{
		Code:  "test_authorization_code",
		State: "test_state",
	}

	reqBody, _ := json.Marshal(loginReq)

	// 创建测试路由
	router := gin.New()

	// 测试飞书登录
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/v1/auth/feishu/login", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	router.ServeHTTP(w, req)

	// 这里需要根据实际的模拟设置来验证响应
	// 实际测试中需要模拟完整的登录流程
}

// TestBindFeishuAccount 测试绑定飞书账号
func TestBindFeishuAccount(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 准备测试数据
	bindReq := model.BindFeishuRequest{
		Username: "testuser",
		Password: "testpassword",
		Code:     "test_authorization_code",
		State:    "test_state",
	}

	reqBody, _ := json.Marshal(bindReq)

	// 创建测试路由
	router := gin.New()

	// 测试绑定飞书账号
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/v1/auth/feishu/bind", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	router.ServeHTTP(w, req)

	// 验证响应
	// 实际测试中需要模拟完整的绑定流程
}

// TestFeishuOAuthClient 测试飞书OAuth客户端
func TestFeishuOAuthClient(t *testing.T) {
	client := feishuapp.NewFeishuOAuthClient(
		"test_app_id",
		"test_app_secret",
		"http://localhost:8080/callback",
		"",
	)

	// 测试生成状态参数
	state, err := client.GenerateState()
	assert.NoError(t, err)
	assert.NotEmpty(t, state)
	assert.Equal(t, 32, len(state)) // 16字节转换为32个十六进制字符

	// 测试生成授权URL
	authURL := client.GetAuthURL(state)
	assert.Contains(t, authURL, "https://open.feishu.cn/open-apis/authen/v1/authorize")
	assert.Contains(t, authURL, "app_id=test_app_id")
	assert.Contains(t, authURL, "redirect_uri=http://localhost:8080/callback")
	assert.Contains(t, authURL, "state="+state)
}

// TestUserMatching 测试用户匹配逻辑
func TestUserMatching(t *testing.T) {
	// 模拟飞书用户信息
	feishuUser := &feishuapp.UserInfoData{
		OpenID:     "ou_test_open_id",
		UnionID:    "on_test_union_id",
		Name:       "测试用户",
		Email:      "<EMAIL>",
		Mobile:     "13800138000",
		EmployeeNo: "E001",
	}

	// 测试用户匹配逻辑
	// 这里需要模拟数据库操作

	// 1. 测试通过OpenID匹配
	// 2. 测试通过邮箱匹配
	// 3. 测试通过手机号匹配
	// 4. 测试自动创建用户

	assert.Equal(t, "ou_test_open_id", feishuUser.OpenID)
	assert.Equal(t, "<EMAIL>", feishuUser.Email)
	assert.Equal(t, "13800138000", feishuUser.Mobile)
}

// TestStateValidation 测试状态参数验证
func TestStateValidation(t *testing.T) {
	// 测试状态参数的生成和验证
	client := feishuapp.NewFeishuOAuthClient("test_app_id", "test_app_secret", "http://localhost:8080/callback", "")

	// 生成状态参数
	state, err := client.GenerateState()
	assert.NoError(t, err)
	assert.NotEmpty(t, state)

	// 验证状态参数
	err = client.ValidateState(state)
	assert.NoError(t, err)

	// 测试空状态参数
	err = client.ValidateState("")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "状态参数不能为空")
}

// TestClientIPExtraction 测试客户端IP提取
func TestClientIPExtraction(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 创建测试上下文
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	// 测试X-Forwarded-For头
	c.Request = httptest.NewRequest("GET", "/", nil)
	c.Request.Header.Set("X-Forwarded-For", "*************, ********")

	// 这里需要调用实际的getClientIP函数进行测试
	// 由于函数在控制器中，需要重构为可测试的独立函数

	// 测试X-Real-IP头
	c.Request.Header.Del("X-Forwarded-For")
	c.Request.Header.Set("X-Real-IP", "*************")

	// 测试RemoteAddr
	c.Request.Header.Del("X-Real-IP")
	c.Request.RemoteAddr = "192.168.1.300:12345"
}

// BenchmarkFeishuLogin 性能测试
func BenchmarkFeishuLogin(b *testing.B) {
	// 性能测试飞书登录流程
	for i := 0; i < b.N; i++ {
		// 模拟登录流程
		client := feishuapp.NewFeishuOAuthClient("test_app_id", "test_app_secret", "http://localhost:8080/callback", "")
		state, _ := client.GenerateState()
		_ = client.GetAuthURL(state)
	}
}

// TestConcurrentLogin 并发登录测试
func TestConcurrentLogin(t *testing.T) {
	// 测试并发登录场景
	concurrency := 10
	done := make(chan bool, concurrency)

	for i := 0; i < concurrency; i++ {
		go func() {
			defer func() { done <- true }()

			// 模拟并发登录
			client := feishuapp.NewFeishuOAuthClient("test_app_id", "test_app_secret", "http://localhost:8080/callback", "")
			state, err := client.GenerateState()
			assert.NoError(t, err)

			authURL := client.GetAuthURL(state)
			assert.NotEmpty(t, authURL)
		}()
	}

	// 等待所有goroutine完成
	for i := 0; i < concurrency; i++ {
		select {
		case <-done:
		case <-time.After(5 * time.Second):
			t.Fatal("并发测试超时")
		}
	}
}
