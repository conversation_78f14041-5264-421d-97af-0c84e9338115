package controller

import (
	"backend/internal/modules/user/model"
	"backend/internal/modules/user/service"
	"backend/response"
	"net"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

type UserController struct {
	userService service.IUserService
}

// NewUserController 创建用户控制器
func NewUserController(s service.IUserService) *UserController {
	return &UserController{
		userService: s,
	}
}

// Register godoc
// @Summary 用户注册
// @Description 用户注册
// @Tags 用户
// @Accept json
// @Produce json
// @Param req body model.RegisterRequest true "注册请求"
// @Success 200 {string} string "注册成功"
// @Failure 400 {string} string "请求参数错误"
// @Router /auth/register [post]
func (ctrl *UserController) Register(c *gin.Context) {
	var req model.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Response(c, http.StatusBadRequest, nil, "请求参数错误")
		return
	}
	if err := ctrl.userService.Register(c.Request.Context(), &req); err != nil {
		response.Response(c, http.StatusInternalServerError, nil, err.Error())
		return
	}
	response.Success(c, nil, "注册成功")
}

// Login godoc
// @Summary 用户登录
// @Description 用户登录
// @Tags 用户
// @Accept json
// @Produce json
// @Param req body model.LoginRequest true "登录请求"
// @Success 200 {object} model.AuthResponse "登录成功"
// @Failure 400 {string} string "请求参数错误"
// @Failure 401 {string} string "用户名或密码错误"
// @Router /auth/login [post]
func (ctrl *UserController) Login(c *gin.Context) {
	var req model.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Response(c, http.StatusBadRequest, nil, "请求参数错误")
		return
	}

	// 先检查用户是否存在
	_, err := ctrl.userService.CheckUserExists(c.Request.Context(), req.Username)
	if err != nil {
		// 用户不存在
		response.Response(c, http.StatusUnauthorized, nil, "用户未注册，请联系管理员进行注册！")
		return
	}

	// 用户存在，继续登录流程
	authResp, err := ctrl.userService.Login(c.Request.Context(), &req)
	if err != nil {
		response.Response(c, http.StatusUnauthorized, nil, err.Error())
		return
	}

	response.Success(c, authResp, "登录成功")
}

// Profile godoc
// @Summary 获取用户信息
// @Description 获取用户信息
// @Tags 用户
// @Security Bearer
// @Accept json
// @Produce json
// @Success 200 {object} string "获取用户信息成功"
// @Failure 401 {string} string "未授权"
// @Failure 500 {string} string "获取用户信息失败"
// @Router /auth/userInfo [get]
func (ctrl *UserController) GetUserInfo(c *gin.Context) {
	// 假设 AuthMiddleware 已将 "userID" 放入 Gin Context 中
	userIDVal, exists := c.Get("userID")
	if !exists {
		response.Response(c, http.StatusUnauthorized, nil, "用户未注册！")
		return
	}
	uid, ok := userIDVal.(uint)
	if !ok {
		response.Response(c, 422, nil, "用户ID格式错误")
		return
	}

	userInfo, err := ctrl.userService.GetUserInfo(c.Request.Context(), uid)
	if err != nil {
		response.Response(c, http.StatusInternalServerError, nil, "获取用户信息失败")
		return
	}

	response.Success(c, userInfo, "获取用户信息成功")
}

// Logout godoc
// @Summary 用户登出
// @Description 用户登出
// @Tags 用户
// @Security Bearer
// @Accept json
// @Produce json
// @Success 200 {string} string "登出成功"
// @Router /auth/logout [post]
func (ctrl *UserController) Logout(c *gin.Context) {
	token := c.GetHeader("Authorization")
	if token == "" {
		response.Response(c, http.StatusUnauthorized, nil, "未授权")
		return
	}

	token = strings.TrimSpace(strings.TrimPrefix(token, "Bearer "))
	if token == "" {
		response.Response(c, http.StatusUnauthorized, nil, "未授权")
		return
	}

	response.Success(c, nil, "登出成功")
}

// GetAuthCodes godoc
// @Summary 获取按钮权限
// @Description 获取按钮权限
// @Tags 用户
// @Security Bearer
// @Success 200 {object} string "获取按钮权限成功"
// @Failure 401 {string} string "未授权"
// @Failure 500 {string} string "获取按钮权限失败"
// @Router /auth/codes [get]
func (ctrl *UserController) GetAuthCodes(c *gin.Context) {
	userIDVal, exists := c.Get("userID")
	if !exists {
		response.Response(c, http.StatusUnauthorized, nil, "用户未注册！")
		return
	}
	uid, ok := userIDVal.(uint)
	if !ok {
		response.Response(c, 422, nil, "用户ID格式错误")
		return
	}

	accessCodes, err := ctrl.userService.GetAuthCodes(c.Request.Context(), uid)
	if err != nil {
		response.Response(c, http.StatusInternalServerError, nil, "获取按钮权限失败")
		return
	}

	response.Success(c, accessCodes, "获取按钮权限成功")
}

// ChangePassword 修改密码
// @Summary 修改密码
// @Description 修改当前用户的密码
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body model.ChangePasswordRequest true "密码修改信息"
// @Success 200 {object} response.ResponseStruct "修改成功"
// @Failure 400 {object} response.ResponseStruct "请求参数错误"
// @Failure 401 {object} response.ResponseStruct "未授权"
// @Failure 500 {object} response.ResponseStruct "服务器内部错误"
// @Router /auth/change-password [post]
// 修改密码
func (ctrl *UserController) ChangePassword(c *gin.Context) {
	var req model.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, http.StatusBadRequest, "请求参数错误")
		return
	}

	// 从上下文中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		response.Fail(c, http.StatusUnauthorized, "用户未注册！")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		response.Fail(c, http.StatusInternalServerError, "用户ID类型错误")
		return
	}

	if err := ctrl.userService.ChangePassword(c.Request.Context(), userIDUint, &req); err != nil {
		response.Fail(c, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(c, nil, "修改密码成功")
}

// GetUserList 获取用户列表
// @Summary 获取用户列表
// @Description 分页获取用户列表
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(10)
// @Param keyword query string false "搜索关键词"
// @Success 200 {object} response.ResponseStruct "获取成功"
// @Failure 401 {object} response.ResponseStruct "未授权"
// @Failure 500 {object} response.ResponseStruct "服务器内部错误"
// @Router /auth/user-list [get]
func (ctrl *UserController) GetUserList(c *gin.Context) {
	var query model.UserListQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		response.Fail(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	req, err := ctrl.userService.GetUserList(c.Request.Context(), &query)
	if err != nil {
		response.Fail(c, http.StatusInternalServerError, "获取用户列表失败: "+err.Error())
		return
	}

	response.Success(c, req, "获取用户列表成功")
}

// UpdateUser 更新用户信息
// @Summary 更新用户信息
// @Description 更新用户的基本信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body model.UpdateUserRequest true "用户信息"
// @Success 200 {object} response.ResponseStruct "更新成功"
// @Failure 400 {object} response.ResponseStruct "请求参数错误"
// @Failure 401 {object} response.ResponseStruct "未授权"
// @Failure 500 {object} response.ResponseStruct "服务器内部错误"
// @Router /auth/user-update [put]
func (ctrl *UserController) UpdateUser(c *gin.Context) {
	var req model.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	if err := ctrl.userService.UpdateUser(c.Request.Context(), &req); err != nil {
		response.Fail(c, http.StatusInternalServerError, "更新用户信息失败: "+err.Error())
		return
	}

	response.Success(c, nil, "更新用户信息成功")
}

// SoftDeleteUser 软删除用户
// @Summary 软删除用户
// @Description 将用户标记为已删除状态
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path uint true "用户ID"
// @Success 200 {object} response.ResponseStruct "删除成功"
// @Failure 400 {object} response.ResponseStruct "请求参数错误"
// @Failure 401 {object} response.ResponseStruct "未授权"
// @Failure 500 {object} response.ResponseStruct "服务器内部错误"
// @Router /auth/user/{id} [delete]
func (ctrl *UserController) SoftDeleteUser(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseUint(idParam, 10, 32)
	if err != nil {
		response.Fail(c, http.StatusBadRequest, "无效的ID")
		return
	}

	if err := ctrl.userService.SoftDeleteUser(c.Request.Context(), uint(id)); err != nil {
		response.Fail(c, http.StatusInternalServerError, "删除用户失败: "+err.Error())
		return
	}

	response.Success(c, nil, "删除用户成功")
}

// CreateUser 管理员创建用户
// @Summary 管理员创建用户
// @Description 超级管理员创建新用户
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body model.CreateUserRequest true "用户信息"
// @Success 200 {object} response.ResponseStruct "创建成功"
// @Failure 400 {object} response.ResponseStruct "请求参数错误"
// @Failure 401 {object} response.ResponseStruct "未授权"
// @Failure 500 {object} response.ResponseStruct "服务器内部错误"
// @Router /auth/admin/create-user [post]
func (ctrl *UserController) CreateUser(c *gin.Context) {
	// 检查当前用户是否有权限创建用户
	userIDVal, exists := c.Get("userID")
	if !exists {
		response.Fail(c, http.StatusUnauthorized, "用户未登录")
		return
	}

	// 获取当前用户信息，检查是否为超级管理员
	userID, ok := userIDVal.(uint)
	if !ok {
		response.Fail(c, http.StatusInternalServerError, "用户ID类型错误")
		return
	}

	userInfo, err := ctrl.userService.GetUserInfo(c.Request.Context(), userID)
	if err != nil {
		response.Fail(c, http.StatusInternalServerError, "获取用户信息失败")
		return
	}

	// 检查是否有超级管理员权限
	hasPermission := false
	for _, role := range userInfo.Roles {
		if role == "super" {
			hasPermission = true
			break
		}
	}

	if !hasPermission {
		response.Fail(c, http.StatusForbidden, "没有创建用户的权限")
		return
	}

	// 解析请求
	var req model.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	// 调用服务创建用户
	if err := ctrl.userService.CreateUser(c.Request.Context(), &req); err != nil {
		response.Fail(c, http.StatusInternalServerError, "创建用户失败: "+err.Error())
		return
	}

	response.Success(c, nil, "创建用户成功")
}

// ResetUserPassword 超级管理员重置用户密码
// @Summary 超级管理员重置用户密码
// @Description 超级管理员重置指定用户的密码
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body model.ResetPasswordRequest true "重置密码信息"
// @Success 200 {object} response.ResponseStruct "重置成功"
// @Failure 400 {object} response.ResponseStruct "请求参数错误"
// @Failure 401 {object} response.ResponseStruct "未授权"
// @Failure 403 {object} response.ResponseStruct "没有权限"
// @Failure 500 {object} response.ResponseStruct "服务器内部错误"
// @Router /auth/admin/reset-password [post]
func (ctrl *UserController) ResetUserPassword(c *gin.Context) {
	var req model.ResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	// 从上下文中获取管理员ID
	adminIDVal, exists := c.Get("userID")
	if !exists {
		response.Fail(c, http.StatusUnauthorized, "管理员未登录")
		return
	}

	adminID, ok := adminIDVal.(uint)
	if !ok {
		response.Fail(c, http.StatusInternalServerError, "管理员ID类型错误")
		return
	}

	// 调用服务重置密码
	if err := ctrl.userService.ResetUserPassword(c.Request.Context(), adminID, req.UserID, req.NewPassword); err != nil {
		if err.Error() == "没有重置密码的权限" {
			response.Fail(c, http.StatusForbidden, err.Error())
			return
		}
		response.Fail(c, http.StatusInternalServerError, "重置密码失败: "+err.Error())
		return
	}

	response.Success(c, nil, "重置密码成功")
}

// CheckPermissionUpdateStatus godoc
// @Summary 检查用户权限是否需要更新（基于角色标志）
// @Description 检查用户所属角色的权限是否有更新
// @Tags 用户
// @Security Bearer
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "返回是否需要更新权限"
// @Failure 401 {string} string "未授权"
// @Router /auth/check-permission-status [get]
func (ctrl *UserController) CheckPermissionUpdateStatus(c *gin.Context) {
	// 从上下文中获取用户ID
	userIDVal, exists := c.Get("userID")
	if !exists {
		response.Response(c, http.StatusUnauthorized, nil, "用户未登录")
		return
	}

	uid, ok := userIDVal.(uint)
	if !ok {
		response.Response(c, http.StatusUnprocessableEntity, nil, "用户ID格式错误")
		return
	}

	// 获取用户信息
	userInfo, err := ctrl.userService.GetUserInfo(c.Request.Context(), uid)
	if err != nil {
		response.Response(c, http.StatusInternalServerError, nil, "获取用户信息失败")
		return
	}

	// 检查用户所有角色是否有更新标志
	needRefresh, lastUpdateTime, err := ctrl.userService.CheckRolesPermissionUpdate(c.Request.Context(), userInfo.Roles)
	if err != nil {
		response.Response(c, http.StatusInternalServerError, nil, "检查权限更新失败")
		return
	}

	response.Success(c, map[string]interface{}{
		"needRefresh":    needRefresh,
		"lastUpdateTime": lastUpdateTime,
	}, "ok")
}

// ClearPermissionUpdateFlag godoc
// @Summary 清除用户权限更新标志
// @Description 在前端获取到最新权限后，手动清除权限更新标志
// @Tags 用户
// @Security Bearer
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "清除成功"
// @Failure 401 {string} string "未授权"
// @Router /auth/clear-permission-flag [post]
func (ctrl *UserController) ClearPermissionUpdateFlag(c *gin.Context) {
	// 从上下文中获取用户ID
	userIDVal, exists := c.Get("userID")
	if !exists {
		response.Response(c, http.StatusUnauthorized, nil, "用户未登录")
		return
	}

	uid, ok := userIDVal.(uint)
	if !ok {
		response.Response(c, http.StatusUnprocessableEntity, nil, "用户ID格式错误")
		return
	}

	// 获取用户信息
	userInfo, err := ctrl.userService.GetUserInfo(c.Request.Context(), uid)
	if err != nil {
		response.Response(c, http.StatusInternalServerError, nil, "获取用户信息失败")
		return
	}

	// 手动清除角色权限更新标志
	err = ctrl.userService.ClearRolePermissionUpdateFlag(c.Request.Context(), userInfo.Roles)
	if err != nil {
		response.Response(c, http.StatusInternalServerError, nil, "清除权限更新标志失败")
		return
	}

	response.Success(c, map[string]interface{}{
		"success": true,
	}, "清除成功")
}

// 飞书登录相关接口

// GetFeishuAuthURL godoc
// @Summary 获取飞书授权URL
// @Description 获取飞书OAuth授权URL，用于跳转到飞书登录页面
// @Tags 飞书登录
// @Accept json
// @Produce json
// @Success 200 {object} model.FeishuAuthURLResponse "获取成功"
// @Failure 500 {string} string "服务器内部错误"
// @Router /auth/feishu/auth-url [get]
func (ctrl *UserController) GetFeishuAuthURL(c *gin.Context) {
	authURLResp, err := ctrl.userService.GetFeishuAuthURL(c.Request.Context())
	if err != nil {
		response.Fail(c, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(c, authURLResp, "获取飞书授权URL成功")
}

// FeishuLogin godoc
// @Summary 飞书登录
// @Description 使用飞书授权码进行登录
// @Tags 飞书登录
// @Accept json
// @Produce json
// @Param req body model.FeishuLoginRequest true "飞书登录请求"
// @Success 200 {object} model.AuthResponse "登录成功"
// @Failure 400 {string} string "请求参数错误"
// @Failure 401 {string} string "登录失败"
// @Router /auth/feishu/login [post]
func (ctrl *UserController) FeishuLogin(c *gin.Context) {
	var req model.FeishuLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	// 获取客户端IP
	clientIP := getClientIP(c)

	authResp, err := ctrl.userService.FeishuLogin(c.Request.Context(), &req, clientIP)
	if err != nil {
		response.Fail(c, http.StatusUnauthorized, err.Error())
		return
	}

	response.Success(c, authResp, "飞书登录成功")
}

// BindFeishuAccount godoc
// @Summary 绑定飞书账号
// @Description 将飞书账号绑定到现有用户账号
// @Tags 飞书登录
// @Accept json
// @Produce json
// @Param req body model.BindFeishuRequest true "绑定飞书账号请求"
// @Success 200 {string} string "绑定成功"
// @Failure 400 {string} string "请求参数错误"
// @Failure 401 {string} string "绑定失败"
// @Router /auth/feishu/bind [post]
func (ctrl *UserController) BindFeishuAccount(c *gin.Context) {
	var req model.BindFeishuRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Fail(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	// 获取客户端IP
	clientIP := getClientIP(c)

	err := ctrl.userService.BindFeishuAccount(c.Request.Context(), &req, clientIP)
	if err != nil {
		response.Fail(c, http.StatusUnauthorized, err.Error())
		return
	}

	response.Success(c, nil, "绑定飞书账号成功")
}

// getClientIP 获取客户端真实IP
func getClientIP(c *gin.Context) string {
	// 尝试从X-Forwarded-For头获取
	xForwardedFor := c.GetHeader("X-Forwarded-For")
	if xForwardedFor != "" {
		ips := strings.Split(xForwardedFor, ",")
		if len(ips) > 0 {
			ip := strings.TrimSpace(ips[0])
			if net.ParseIP(ip) != nil {
				return ip
			}
		}
	}

	// 尝试从X-Real-IP头获取
	xRealIP := c.GetHeader("X-Real-IP")
	if xRealIP != "" {
		if net.ParseIP(xRealIP) != nil {
			return xRealIP
		}
	}

	// 使用RemoteAddr
	ip, _, err := net.SplitHostPort(c.Request.RemoteAddr)
	if err != nil {
		return c.Request.RemoteAddr
	}
	return ip
}
