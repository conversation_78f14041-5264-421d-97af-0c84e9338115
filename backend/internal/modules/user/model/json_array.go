package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// StringArray 自定义类型，用于处理字符串数组的 JSON 存储
type StringArray []string

// Value 实现 driver.Valuer 接口，将 StringArray 转换为 JSON 字符串
func (a StringArray) Value() (driver.Value, error) {
	if len(a) == 0 {
		return "[]", nil
	}
	b, err := json.Marshal(a)
	if err != nil {
		return nil, err
	}
	return string(b), nil
}

// Scan 实现 sql.Scanner 接口，将数据库中的 JSON 数据解析为 StringArray
func (a *StringArray) Scan(value interface{}) error {
	if value == nil {
		*a = []string{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("failed to convert %v to []byte", value)
	}

	return json.Unmarshal(bytes, a)
}
