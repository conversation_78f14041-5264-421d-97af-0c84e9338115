package repository

import (
	"backend/internal/modules/user/model"
	"context"

	"gorm.io/gorm"
)

type UserRepository struct {
	db *gorm.DB
}

func NewUserRepository(db *gorm.DB) *UserRepository {
	return &UserRepository{db: db}
}

// 创建用户
func (r *UserRepository) CreateUser(ctx context.Context, user *model.User) error {
	if len(user.Roles) == 0 {
		user.Roles = []string{"admin"} // 默认值
	}
	if len(user.AccessCodes) == 0 {
		user.AccessCodes = []string{"AC_100001"} // 默认值
	}
	return r.db.WithContext(ctx).Create(user).Error
}

// 根据用户名查询用户
func (r *UserRepository) FindByUsername(ctx context.Context, username string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).Where("username = ?", username).First(&user).Error
	return &user, err
}

// 根据用户ID查询用户信息
func (r *UserRepository) FindByID(ctx context.Context, id uint) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).First(&user, id).Error
	return &user, err
}

// 根据飞书OpenID查询用户
func (r *UserRepository) FindByFeishuOpenID(ctx context.Context, openID string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).Where("feishu_open_id = ?", openID).First(&user).Error
	return &user, err
}

// 根据飞书UnionID查询用户
func (r *UserRepository) FindByFeishuUnionID(ctx context.Context, unionID string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).Where("feishu_union_id = ?", unionID).First(&user).Error
	return &user, err
}

// 根据邮箱查询用户
func (r *UserRepository) FindByEmail(ctx context.Context, email string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).Where("email = ?", email).First(&user).Error
	return &user, err
}

// 根据手机号查询用户
func (r *UserRepository) FindByTelephone(ctx context.Context, telephone string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).Where("telephone = ?", telephone).First(&user).Error
	return &user, err
}

// 更新用户飞书信息
func (r *UserRepository) UpdateFeishuInfo(ctx context.Context, userID uint, openID, unionID string) error {
	return r.db.WithContext(ctx).Model(&model.User{}).Where("id = ?", userID).Updates(map[string]interface{}{
		"feishu_open_id":  openID,
		"feishu_union_id": unionID,
		"login_type":      "both", // 支持密码和飞书登录
	}).Error
}

// 更新最后登录信息
func (r *UserRepository) UpdateLastLogin(ctx context.Context, userID uint, loginIP string) error {
	return r.db.WithContext(ctx).Model(&model.User{}).Where("id = ?", userID).Updates(map[string]interface{}{
		"last_login_time": gorm.Expr("NOW()"),
		"last_login_ip":   loginIP,
	}).Error
}

// ... existing code ...

// 获取用户列表
func (r *UserRepository) GetUserList(ctx context.Context, query *model.UserListQuery) ([]model.User, int64, error) {
	var users []model.User
	var total int64

	db := r.db.WithContext(ctx).Model(&model.User{})

	// 添加查询条件
	if query.Query != "" {
		db = db.Where("username LIKE ? OR real_name LIKE ? OR email LIKE ?",
			"%"+query.Query+"%", "%"+query.Query+"%", "%"+query.Query+"%")
	}

	if query.Status != "" {
		db = db.Where("status = ?", query.Status)
	}

	// 计算总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (query.Page - 1) * query.PageSize
	if err := db.Offset(offset).Limit(query.PageSize).Find(&users).Error; err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

// 更新用户信息
func (r *UserRepository) UpdateUser(ctx context.Context, user *model.User) error {
	return r.db.WithContext(ctx).Model(user).Updates(user).Error
}

// 更新用户密码
func (r *UserRepository) UpdatePassword(ctx context.Context, userID uint, hashedPassword string) error {
	return r.db.WithContext(ctx).Model(&model.User{}).Where("id = ?", userID).
		Update("password", hashedPassword).Error
}

// 软删除用户（使用GORM的软删除功能）
func (r *UserRepository) SoftDeleteUser(ctx context.Context, userID uint) error {
	// 使用 DeletePartInbound 方法会自动设置 DeletedAt 字段
	return r.db.WithContext(ctx).Delete(&model.User{}, userID).Error
}

// GetDB 返回数据库连接
func (r *UserRepository) GetDB() *gorm.DB {
	return r.db
}
