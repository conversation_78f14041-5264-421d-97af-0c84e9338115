package acceptance

import (
	"backend/internal/common/constants"
	asset2 "backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/cmdb/model/component"
	"backend/internal/modules/cmdb/repository/asset"
	componentRepo "backend/internal/modules/cmdb/repository/component"
	fileModel "backend/internal/modules/file/model"
	"backend/internal/modules/hardware_maintenance/common"
	"backend/internal/modules/hardware_maintenance/common/types"
	model "backend/internal/modules/hardware_maintenance/model/acceptance"
	acceptanceRepo "backend/internal/modules/hardware_maintenance/repository/acceptance"
	"backend/internal/modules/hardware_maintenance/workflow"
	"backend/pkg/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"time"

	"go.temporal.io/sdk/client"
	"go.uber.org/zap"
)

var (
	// ErrAcceptanceOrderNotFound 验收工单不存在
	ErrAcceptanceOrderNotFound = errors.New("验收工单不存在")
	// ErrInvalidAcceptanceOrder 无效的验收工单数据
	ErrInvalidAcceptanceOrder = errors.New("无效的验收工单数据")

	ErrItemDeviceSNRepeat = errors.New("验收条目中存在SN重复项")
	ErrItemDeviceSNEmpty  = errors.New("存在设备SN为空条目")
	ErrItemTemplateEmpty  = errors.New("存在模板为空条目")
)

// AssetAcceptanceService 资产验收接口
type AcceptanceService interface {
	// 数据库操作
	CreateAcceptanceOrder(ctx context.Context, order *model.AcceptanceOrder, items []*model.AcceptanceItem) error
	GetAcceptanceOrderByID(ctx context.Context, id uint) (*model.AcceptanceOrder, error)
	GetAcceptanceOrderByIDWithItems(ctx context.Context, id uint) (*model.AcceptanceOrder, error)
	GetAcceptanceOrderByOrderId(ctx context.Context, ticketNo string) (*model.AcceptanceOrder, error)
	UpdateAcceptanceOrderWithItems(ctx context.Context, order *model.AcceptanceOrder, items []*model.AcceptanceItem) error
	UpdateAcceptanceOrderStatus(ctx context.Context, id uint, status string, operatorID uint, operatorName string) error
	UpdateAcceptanceOrderFields(ctx context.Context, id uint, fields map[string]interface{}) error
	ListAcceptanceItemsByOrderID(ctx context.Context, orderID uint) ([]*model.AcceptanceItem, error)

	// 记录历史
	RecordOperationHistory(ctx context.Context, orderId uint,
		operatorId uint, operatorName, preStatus, curStatus string,
		remark string, operationTime time.Time) error
	// 验收单业务操作
	AuditAcceptanceOrder(ctx context.Context, orderID uint, req *types.AuditAcceptanceOrderRequest) error
	CancelAcceptanceOrder(ctx context.Context, orderID uint, req *types.CancelAcceptanceOrderRequest) error
	AssignAcceptanceOrder(ctx context.Context, orderID uint, req *types.AssignAcceptanceOrderRequest) error
	CompleteAcceptanceOrder(ctx context.Context, orderID uint, items []*model.AcceptanceItem, req *types.CompleteAcceptanceOrderRequest) error
	CompleteOrderAndUpdateComponents(ctx context.Context, order *model.AcceptanceOrder, items []*model.AcceptanceItem, components []*component.ServerComponent, operatorID uint, operatorName string) error

	//SignalWorkflow(ctx context.Context, orderID uint, signal workflow.WorkflowControlSignal) error
	ListAcceptanceOrder(ctx context.Context, query types.AcceptanceOrderFilter) ([]*model.AcceptanceOrder, int64, error)
	ListAcceptanceOrderWithoutItems(ctx context.Context, query types.AcceptanceOrderFilter) ([]*model.AcceptanceOrder, int64, error)
	// 工作流相关
	TriggerWorkflow(ctx context.Context, orderID uint, signal workflow.WorkflowControlSignal) error
	UploadCheckTrigger(ctx context.Context, orderID uint, operatorID uint, operateName string, serverInfoList []*types.ServerInfoRaw) error

	// 一些工具
	// CheckSNs 检查设备SN是否存在以及是否重复，返回不存在的SN序列
	CheckSNs(ctx context.Context, items []*model.AcceptanceItem) ([]string, []string, error)
	// FilterNonExistingTemplates 检查模板名是否存在，返回不存在的模板名
	FilterNoExistingTemplates(ctx context.Context, items []*model.AcceptanceItem) ([]string, error)
	// FilterNoExistingPNs 检查模板名是否存在，返回不存在的模板名
	FilterNoExistingPNs(ctx context.Context, pns []string) ([]string, error)
	// DecodeUploadFile 将上传的文件进行解码
	DecodeUploadFile(fileModels []fileModel.File, order *model.AcceptanceOrder, items []*model.AcceptanceItem, onlyPass bool) (map[string]*types.ServerInfo, error)
	// BuildServerComponentData 构建组件信息
	BuildServerComponentData(productPnMap map[string]uint, deviceSnMap map[string]uint, serverMap map[string]*types.ServerInfo) ([]*component.ServerComponent, error)
	// 获取操作历史
	GetAcceptanceOrderHistories(ctx context.Context, orderId uint) ([]*model.AcceptanceOrderHistory, error)
	// 保存已验收的组件信息
	SaveAcceptedComponents(ctx context.Context, components []*model.InspectingComponentInfo) error
	// 新增：根据工单ID获取已验收组件
	ListAcceptedComponentsByOrderID(ctx context.Context, orderID uint) ([]*model.InspectingComponentInfo, error)
	// 新增：根据工单ID和itemIDs批量获取已验收组件
	ListAcceptedComponentsByOrderIDAndItemIDs(ctx context.Context, orderID uint, itemIDs []uint) ([]*model.InspectingComponentInfo, error)
	// 新增：根据itemIDs批量获取已验收组件
	ListAcceptedComponentsByItemIDs(ctx context.Context, itemIDs []uint) ([]*model.InspectingComponentInfo, error)
	// 批量更新正在验收的组件信息
	UpdateInspectingComponentInfo(ctx context.Context, req *types.UpdateInspectingComponentInfoReq) error

	// 获取组件SN列表
	ListServerComponentsBySNs(ctx context.Context, sns []string) ([]*component.ServerComponent, error)

	// 设备型号到PN映射相关方法
	CreateComponentModelPNMapping(ctx context.Context, req *types.CreateComponentModelPNMappingRequest) (*model.ComponentModelPNMapping, error)
	GetComponentModelPNMappingByID(ctx context.Context, id uint) (*model.ComponentModelPNMapping, error)
	UpdateComponentModelPNMapping(ctx context.Context, id uint, req *types.UpdateComponentModelPNMappingRequest) (*model.ComponentModelPNMapping, error)
	DeleteComponentModelPNMapping(ctx context.Context, id uint) error
	ListComponentModelPNMappings(ctx context.Context, filter types.ComponentModelPNMappingFilter) ([]*model.ComponentModelPNMapping, int64, error)
	GetComponentPNByModelComponent(ctx context.Context, model, componentType string) (*model.ComponentModelPNMapping, error)
	FillComponentPNs(ctx context.Context, itemIds []uint, overWriteExisting bool) error
}

// assetAcceptanceService 资产验收服务实现
type assetAcceptanceService struct {
	acceptanceOrderRepo         acceptanceRepo.AcceptanceOrderRepository
	acceptanceItemRepo          acceptanceRepo.AcceptanceItemRepository
	acceptedComponentRepo       acceptanceRepo.AcceptedComponentRepository
	componentModelPNMappingRepo acceptanceRepo.ComponentModelPNMappingRepository
	serverComponentRepo         componentRepo.ServerComponentRepository
	componentChangeLogRepo      componentRepo.ComponentChangeLogRepository
	resourceRepo                asset.ResourceRepository
	deviceRepo                  asset.DeviceRepository
	statusChangeRepo            asset.StatusChangeRepository
	logger                      *zap.Logger
	temporalClient              client.Client
}

// NewAcceptanceService 创建资产验收服务
func NewAcceptanceService(
	acceptanceOrderRepo acceptanceRepo.AcceptanceOrderRepository,
	acceptanceItemRepo acceptanceRepo.AcceptanceItemRepository,
	acceptedComponentRepo acceptanceRepo.AcceptedComponentRepository,
	deviceModelPNMappingRepo acceptanceRepo.ComponentModelPNMappingRepository,
	serverComponentRepo componentRepo.ServerComponentRepository,
	componentChangeLogRepo componentRepo.ComponentChangeLogRepository,
	deviceRepo asset.DeviceRepository,
	resourceRepo asset.ResourceRepository,
	statusChangeRepo asset.StatusChangeRepository,
	logger *zap.Logger,
	temporalClient client.Client) AcceptanceService {
	return &assetAcceptanceService{
		acceptanceItemRepo:          acceptanceItemRepo,
		acceptanceOrderRepo:         acceptanceOrderRepo,
		acceptedComponentRepo:       acceptedComponentRepo,
		componentModelPNMappingRepo: deviceModelPNMappingRepo,
		serverComponentRepo:         serverComponentRepo,
		componentChangeLogRepo:      componentChangeLogRepo,
		resourceRepo:                resourceRepo,
		deviceRepo:                  deviceRepo,
		statusChangeRepo:            statusChangeRepo,
		logger:                      logger,
		temporalClient:              temporalClient,
	}
}

// CreateAcceptanceOrder 创建验收工单
func (s *assetAcceptanceService) CreateAcceptanceOrder(ctx context.Context, order *model.AcceptanceOrder, items []*model.AcceptanceItem) error {
	if err := s.acceptanceOrderRepo.Create(ctx, order, items); err != nil {
		s.logger.Error("acceptanceOrderRepo.Create fail", zap.Error(err))
		return err
	}

	// 创建history
	history := &model.AcceptanceOrderHistory{
		AcceptanceOrderID: order.ID,
		PreviousStatus:    "",
		ActivityCategory:  common.History_Activity_Create,
		NewStatus:         string(common.OrderStatus_Pending),
		OperatorID:        order.InitiatorID,
		OperatorName:      order.InitiatorName,
		OperationTime:     time.Now(),
		Remarks:           "创建审批单",
	}
	if err := s.acceptanceOrderRepo.CreateStatusHistory(ctx, history); err != nil {
		s.logger.Error("CreateStatusHistory error", zap.Error(err))
	}

	// 启动工作流
	if s.temporalClient != nil {
		workflowID := fmt.Sprintf("%s%d", workflow.PrefixAcceptanceOrderWorkflowID, order.ID)
		_, err := s.temporalClient.ExecuteWorkflow(ctx, client.StartWorkflowOptions{
			ID:        workflowID,
			TaskQueue: workflow.AcceptanceOrderTaskQueue,
		}, workflow.AcceptanceOrderWorkflowV2, workflow.AcceptanceOrderWorkflowInput{
			OrderID:       order.ID,
			InitiatorID:   order.InitiatorID,
			InitiatorName: order.InitiatorName,
			OperatorID:    0,
			OperatorName:  "",
			Status:        common.OrderStatus_Pending,
			Completed:     false,
		})
		if err != nil {
			// 仅记录日志，不影响主流程
			fmt.Printf("启动工作流失败: %v\n", err)
		}
	} else {
		return errors.New("工作流未启动")
	}
	return nil
}
func (s *assetAcceptanceService) GetAcceptanceOrderByID(ctx context.Context, id uint) (*model.AcceptanceOrder, error) {
	orders, _, err := s.acceptanceOrderRepo.ListWithoutItems(ctx, types.AcceptanceOrderFilter{IDs: []uint{id}, Page: 1, PageSize: 1})
	if err != nil {
		return nil, err
	}

	if len(orders) < 1 {
		return nil, ErrAcceptanceOrderNotFound
	}

	return orders[0], err
}

func (s *assetAcceptanceService) GetAcceptanceOrderByIDWithItems(ctx context.Context, id uint) (*model.AcceptanceOrder, error) {
	orders, _, err := s.acceptanceOrderRepo.List(ctx, types.AcceptanceOrderFilter{IDs: []uint{id}, Page: 1, PageSize: 1})
	if err != nil {
		return nil, err
	}

	if len(orders) < 1 {
		return nil, ErrAcceptanceOrderNotFound
	}

	return orders[0], err
}

// TODO implement
func (s *assetAcceptanceService) GetAcceptanceOrderByOrderId(ctx context.Context, ticketNo string) (*model.AcceptanceOrder, error) {
	return nil, nil
}

func (s *assetAcceptanceService) UpdateAcceptanceOrderWithItems(ctx context.Context, order *model.AcceptanceOrder, items []*model.AcceptanceItem) error {
	return s.acceptanceOrderRepo.UpdateWithItems(ctx, order, items)
}

// TODO implement
func (s *assetAcceptanceService) UpdateAcceptanceOrderStatus(ctx context.Context, id uint, status string, operatorID uint, operatorName string) error {
	return nil
}

func (s *assetAcceptanceService) UpdateAcceptanceOrderFields(ctx context.Context, id uint, fields map[string]interface{}) error {
	return s.acceptanceOrderRepo.UpdateFields(ctx, id, fields)
}

// TODO 发送工作流后，监听工作流中的错误

// AuditAcceptanceOrder审核验收工单
func (s *assetAcceptanceService) AuditAcceptanceOrder(ctx context.Context, orderId uint, req *types.AuditAcceptanceOrderRequest) error {
	// 创建信号
	signal := workflow.WorkflowControlSignal{
		Status:       string(common.OrderStatus_Approved),
		OperatorID:   req.OperatorID,
		OperatorName: req.OperatorName,
		Comments:     req.Comment,
		Timestamp:    time.Now().Unix(),
	}

	if req.IsApproved {
		signal.Status = string(common.OrderStatus_Approved)
	} else {
		signal.Status = string(common.OrderStatus_Rejected)
	}

	// 发送信号给工作流，由工作流中的activity执行具体流程操作
	err := s.TriggerWorkflow(ctx, orderId, signal)
	if err != nil {
		s.logger.Error("TriggerWorkflow error", zap.Error(err))
		return err
	}

	return nil
}

// CancelAcceptanceOrder 取消验收工单
func (s *assetAcceptanceService) CancelAcceptanceOrder(ctx context.Context, orderId uint, req *types.CancelAcceptanceOrderRequest) error {
	// 创建信号
	signal := workflow.WorkflowControlSignal{
		Status:     string(common.OrderStatus_Canceled),
		OperatorID: req.OperatorID,
		Comments:   req.Comment,
		Timestamp:  time.Now().Unix(),
	}

	// 发送信号给工作流，由工作流中的activity执行具体流程操作
	return s.TriggerWorkflow(ctx, orderId, signal)
}

// AssignAcceptanceOrder 分配验收工单
func (s *assetAcceptanceService) AssignAcceptanceOrder(ctx context.Context, orderID uint, req *types.AssignAcceptanceOrderRequest) error {
	// 创建信号
	signal := workflow.WorkflowControlSignal{
		Status:     string(common.OrderStatus_In_Progress),
		OperatorID: req.OperatorID,
		Comments:   req.Comment,
		Data: map[string]interface{}{
			"assignedID": req.AssignedID,
		},
		Timestamp: time.Now().Unix(),
	}

	// 发送信号给工作流，由工作流中的activity执行具体流程操作
	return s.TriggerWorkflow(ctx, orderID, signal)
}

func (s *assetAcceptanceService) CompleteAcceptanceOrder(ctx context.Context, orderID uint, items []*model.AcceptanceItem, req *types.CompleteAcceptanceOrderRequest) error {
	// 创建信号
	itemsBs, err := json.Marshal(items)
	if err != nil {
		return err
	}
	if err != nil {
		return err
	}

	signal := workflow.WorkflowControlSignal{
		Status:       string(common.OrderStatus_Completed),
		OperatorID:   req.OperatorID,
		OperatorName: req.OperatorName,
		Comments:     req.Comment,
		Timestamp:    time.Now().Unix(),
		Data: map[string]interface{}{
			"orderItems": string(itemsBs),
		},
	}

	// 发送信号给工作流，由工作流中的activity执行具体流程操作
	return s.TriggerWorkflow(ctx, orderID, signal)
}

func (s *assetAcceptanceService) CompleteOrderAndUpdateComponents(ctx context.Context, order *model.AcceptanceOrder, items []*model.AcceptanceItem, components []*component.ServerComponent, operatorID uint, operatorName string) error {
	const (
		batchSize      = 300
		queryBatchSize = 500
	)
	var (
		total, passCnt uint
		err            error
		assetIDs       []uint
	)
	tx := s.acceptanceOrderRepo.GetDB().WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r) // 保证 panic 继续抛出
		}
	}()

	for _, item := range items {
		total++
		if item.IsPass != nil && *item.IsPass {
			passCnt++
		}
	}

	// 更新Order数据
	err = s.acceptanceOrderRepo.WithTx(tx).UpdateFields(ctx, order.ID, map[string]interface{}{
		"status":       order.Status,
		"handler_id":   order.HandlerID,
		"handler_name": order.HandlerName,
		"total":        total,
		"pass_count":   passCnt,
	})
	if err != nil {
		tx.Rollback()
		return err
	}

	// 更新条目
	itemRepo := s.acceptanceItemRepo.WithTx(tx)
	for _, item := range items {
		assetIDs = append(assetIDs, item.DeviceID)
		err = itemRepo.Update(ctx, item)
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	// 分批记录组件信息
	serverComponentRepo := s.serverComponentRepo.WithTx(tx)

	for i := 0; i < len(components); i += batchSize {
		end := i + batchSize
		if end > len(components) {
			end = len(components)
		}
		batch := components[i:end]
		err = serverComponentRepo.BatchInsert(ctx, batch)
		if err != nil {
			return err
		}
	}

	// 处理状态变更信息
	var (
		workFlowID             = fmt.Sprintf("%s%d", workflow.PrefixAcceptanceOrderWorkflowID, order.ID)
		reason                 = "资产验收，资产状态变更为已验收"
		newAssetStatus         = constants.AssetStatusVerify
		statusChangeRepo       = s.statusChangeRepo.WithTx(tx)
		deviceRepo             = s.deviceRepo.WithTx(tx)
		componentChangeLogRepo = s.componentChangeLogRepo.WithTx(tx)
		now                    = time.Now()
	)

	// 更新资产状态记录
	resources, err := s.resourceRepo.ListByAssetIDsWithDevice(ctx, assetIDs)
	if err != nil {
		tx.Rollback()
		return err
	}
	for _, res := range resources {
		statusChangeLog := &asset2.StatusChangeLog{
			AssetID:           res.AssetID,
			OldAssetStatus:    res.Device.AssetStatus,
			NewAssetStatus:    newAssetStatus,
			OldBizStatus:      res.BizStatus,
			NewBizStatus:      res.BizStatus,
			OldHardwareStatus: res.Device.HardwareStatus,
			NewHardwareStatus: res.Device.HardwareStatus,
			ChangeReason:      reason,
			OperatorID:        operatorID,
			OperatorName:      operatorName,
			ApproverID:        order.ApproverID,
			ApproverName:      order.ApproverName,
			WorkflowID:        workFlowID,
			Source:            constants.AssetStatusOnRack,
			TicketNo:          order.OrderNo,
		}
		err := statusChangeRepo.LogStatusChange(ctx, statusChangeLog)
		if err != nil {
			tx.Rollback()
			return err
		}
		// 只更改资产状态
		res.Device.AssetStatus = newAssetStatus
		res.Device.LastStatusChange = utils.Date(now)
		err = deviceRepo.Update(ctx, &(res.Device))
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	// 分批查询回填ID (避免超大IN查询)
	allSNs := make([]string, 0, len(components))
	for _, c := range components {
		allSNs = append(allSNs, c.SN)
	}

	for i := 0; i < len(allSNs); i += queryBatchSize {
		end := i + queryBatchSize
		if end > len(allSNs) {
			end = len(allSNs)
		}
		batchSNs := allSNs[i:end]

		var dbComponents []*component.ServerComponent
		if err = serverComponentRepo.GetDB().Select("id", "sn").
			Where("sn IN ?", batchSNs).
			Find(&dbComponents).Error; err != nil {
			tx.Rollback()
			return err
		}

	}

	// 创建组件变更记录
	for i := 0; i < len(components); i += batchSize {
		end := i + batchSize
		if end > len(components) {
			end = len(components)
		}

		// 构造本批次变更日志
		batchLogs := make([]*component.ComponentChangeLog, 0, end-i)
		for _, c := range components[i:end] {
			batchLogs = append(batchLogs, &component.ComponentChangeLog{
				ServerID:     c.ServerID,
				ComponentID:  c.ID,
				ChangeType:   "acceptance",
				Reason:       "资产验收，组件信息录入cmdb",
				OperatorID:   operatorID,
				OperatorName: operatorName,
				ChangeTime:   now,
				Remark:       "资产验收，组件信息录入cmdb",
			})
		}

		// 批量插入
		if err := componentChangeLogRepo.BatchInsert(ctx, batchLogs); err != nil {
			tx.Rollback()
			return err
		}
	}

	tx.Commit()
	return err
}

// ListAcceptanceOrder 列出列表
func (s *assetAcceptanceService) ListAcceptanceOrder(ctx context.Context, query types.AcceptanceOrderFilter) ([]*model.AcceptanceOrder, int64, error) {
	res, total, err := s.acceptanceOrderRepo.List(ctx, query)
	if err != nil {
		return nil, 0, err
	}
	return res, total, nil
}

// ListAcceptanceOrderWithoutItems 列出列表但不包含Items
func (s *assetAcceptanceService) ListAcceptanceOrderWithoutItems(ctx context.Context, query types.AcceptanceOrderFilter) ([]*model.AcceptanceOrder, int64, error) {
	res, total, err := s.acceptanceOrderRepo.ListWithoutItems(ctx, query)
	if err != nil {
		return nil, 0, err
	}
	return res, total, nil
}

// CheckSNs 检查设备SN是否存在及重复，返回
func (s *assetAcceptanceService) CheckSNs(ctx context.Context, items []*model.AcceptanceItem) (diffSNs []string, repeatSNs []string, err error) {
	sns := make([]string, 0, len(items))
	snMap := make(map[string]int)
	for _, item := range items {
		if snMap[item.DeviceSN] == 1 {
			repeatSNs = append(repeatSNs, item.DeviceSN)
		}
		if len(item.DeviceSN) == 0 {
			return nil, nil, ErrItemDeviceSNEmpty
		}
		sns = append(sns, item.DeviceSN)
		snMap[item.DeviceSN]++
	}

	// TODO 使用device的repo
	existingSNs, err := s.acceptanceItemRepo.FilterExistingSNs(ctx, sns)
	if err != nil {
		return nil, nil, err
	}
	diffSNs = common.StringDifference(sns, existingSNs)
	return
}

// FilterNonExistingTemplates 检查模板名是否存在，返回不存在的模板名
func (s *assetAcceptanceService) FilterNoExistingTemplates(ctx context.Context, items []*model.AcceptanceItem) ([]string, error) {
	templates := make([]string, 0, len(items))
	for _, item := range items {
		if len(item.TemplateName) == 0 {
			return nil, ErrItemTemplateEmpty
		}
		templates = append(templates, item.TemplateName)
	}

	// TODO 使用template的repo
	existingTemplates, err := s.acceptanceItemRepo.FilterExistingTemplates(ctx, templates)
	if err != nil {
		return nil, err
	}

	return common.StringDifference(templates, existingTemplates), nil
}

func (s *assetAcceptanceService) FilterNoExistingPNs(ctx context.Context, pns []string) ([]string, error) {
	if len(pns) == 0 {
		return nil, nil
	}

	// TODO 使用product的repo
	existingPNs, err := s.acceptanceItemRepo.FilterExistingPNs(ctx, pns)
	if err != nil {
		return nil, err
	}

	return common.StringDifference(pns, existingPNs), nil
}

// TriggerWorkflow 给工作流发送信号，监听发送信号后的结果
func (s *assetAcceptanceService) TriggerWorkflow(ctx context.Context, orderID uint, signal workflow.WorkflowControlSignal) error {

	// 构造工作流ID
	workflowID := fmt.Sprintf("%s%d", workflow.PrefixAcceptanceOrderWorkflowID, orderID)

	s.logger.Info("准备触发工作流信号",
		zap.String("workflowID", workflowID))

	// 更新工作流状态
	updateOptions := client.UpdateWorkflowOptions{
		WorkflowID:   workflowID,
		UpdateName:   workflow.SIG_AcceptanceOrder,
		WaitForStage: client.WorkflowUpdateStageCompleted,
		Args:         []interface{}{signal},
	}

	updateHandle, err := s.temporalClient.UpdateWorkflow(ctx, updateOptions)
	if err != nil {
		s.logger.Error("更新工作流状态失败",
			zap.Error(err),
			zap.String("workflowID", workflowID),
			zap.String("status", signal.Status),
			zap.String("operatorName", signal.OperatorName),
			zap.Uint("OrderId", orderID))
		return fmt.Errorf("更新工作流状态失败: %w", err)
	}

	err = updateHandle.Get(ctx, nil)
	if err != nil {
		s.logger.Error("Unable to get update result", zap.Error(err))
		return err
	}

	s.logger.Info("成功更新工作流",
		zap.String("workflowID", workflowID),
		zap.String("status", signal.Status),
		zap.String("operatorName", signal.OperatorName),
		zap.Uint("OrderId", orderID))

	return nil
}

func (s *assetAcceptanceService) UploadCheckTrigger(ctx context.Context, orderID uint, operatorID uint, operateName string, serverInfoList []*types.ServerInfoRaw) error {

	// 先序列化为json字符串
	serverInfoListStr := ""
	if serverInfoList != nil {
		if bs, err := json.Marshal(serverInfoList); err == nil {
			serverInfoListStr = string(bs)
		}
	}
	// 创建信号
	signal := workflow.WorkflowControlSignal{
		Status:       string(common.OrderStatus_Collected),
		OperatorID:   operatorID,
		OperatorName: operateName,
		Timestamp:    time.Now().Unix(),
		Data: map[string]interface{}{
			"serverInfoList": serverInfoListStr,
		},
	}

	// 发送信号给工作流，由工作流中的activity执行具体流程操作
	return s.TriggerWorkflow(ctx, orderID, signal)
}

func (s *assetAcceptanceService) DecodeUploadFile(fileModels []fileModel.File, order *model.AcceptanceOrder, items []*model.AcceptanceItem, onlyPass bool) (map[string]*types.ServerInfo, error) {
	// 解析文件
	if order == nil {
		return nil, errors.New("验收工单为空")
	}
	var (
		serverMap = make(map[string]*types.ServerInfo)
		passMap   = make(map[string]bool)
	)

	if onlyPass {
		for _, item := range items {
			if item.IsPass != nil && *item.IsPass {
				passMap[item.DeviceSN] = true
			}
		}
	}

	for _, file := range fileModels {
		data, err := os.ReadFile(file.StoragePath)
		if err != nil {
			return nil, err
		}
		var rawServers []types.ServerInfoRaw
		if err := json.Unmarshal(data, &rawServers); err != nil {
			return nil, err
		}
		for _, raw := range rawServers {
			var s types.ServerInfo
			s.ID = raw.ID
			// 不存在滤掉
			if onlyPass && !passMap[raw.ID] {
				continue
			}

			// 直接使用结构体字段，不需要json.Unmarshal
			s.Bios = raw.Bios
			s.Bmc = raw.Bmc
			s.CPU = raw.CPU
			s.Disk = raw.Disk
			s.GPU = raw.GPU
			s.Memory = raw.Memory
			s.Motherboard = raw.Motherboard
			s.Nic = raw.Nic
			s.Other = raw.Other
			s.Pcie = raw.Pcie

			serverMap[s.ID] = &s
		}
	}

	return serverMap, nil
}

func (s *assetAcceptanceService) BuildServerComponentData(productPnMap map[string]uint, deviceSnMap map[string]uint, serverMap map[string]*types.ServerInfo) ([]*component.ServerComponent, error) {
	var (
		components []*component.ServerComponent
	)

	// product
	for serverSN, server := range serverMap {

		//*****************CPU 和 GPU 没有PN号，不能在这里直接插入
		// cpu CPU无PN号
		//for _, cpu := range server.CPU.Detail {
		//	component := &component.ServerComponent{
		//		SN: cpu.SN,
		//		//PN:
		//		Model:         cpu.ModelName,
		//		Status:        "normal",
		//		ComponentType: "CPU",
		//	}
		//	components = append(components, component)
		//}
		//
		//// GPU GPU也没PN号
		//for _, gpu := range server.GPU.Detail {
		//	component := &component.ServerComponent{
		//		SN: gpu.Serial,
		//		//PN:            .PN,
		//		Status:          "normal",
		//		ComponentType:   "显卡",
		//		FirmwareVersion: gpu.VbiosVersion, // 显卡 VbiosVersion 版本
		//	}
		//	components = append(components, component)
		//}

		// 查内存
		for _, mem := range server.Memory.Detail {
			component := &component.ServerComponent{
				SN:            mem.SN,
				PN:            mem.PN,
				Model:         fmt.Sprintf("%s %s %s", mem.DdrType, mem.Size, mem.Frequency),
				Status:        "normal",
				ComponentType: "内存",
				Description:   fmt.Sprintf("%s 内存", mem.Size),
				ServerID:      deviceSnMap[serverSN],
				ProductID:     productPnMap[mem.PN],
			}
			components = append(components, component)
		}

		// 磁盘
		for _, disk := range server.Disk.Detail {
			component := &component.ServerComponent{
				SN:              disk.SN,
				PN:              disk.PN,
				Model:           disk.Size, // 错误的，磁盘缺少信号
				FirmwareVersion: disk.FirmwareVersion,
				Status:          "normal",
				ComponentType:   "硬盘",
				Description:     fmt.Sprintf("%s 硬盘", disk.Size),
				ServerID:        deviceSnMap[serverSN],
				ProductID:       productPnMap[disk.PN],
			}
			components = append(components, component)
		}

		// 主板
		components = append(components, &component.ServerComponent{
			PN:              server.Bmc.FruDetail.BoardPN,
			SN:              server.Bmc.FruDetail.BoardSN,
			Model:           server.Bmc.FruDetail.BoardMfg,
			FirmwareVersion: server.Bios.FirmwareVersion, //  Bios版本
			Status:          "normal",
			ComponentType:   "主板",
			Description:     fmt.Sprintf("%s 主板", server.Bmc.FruDetail.BoardMfg),
			ServerID:        deviceSnMap[serverSN],
			ProductID:       productPnMap[server.Bmc.FruDetail.BoardPN],
		})

		// vpc网卡
		for _, vpc := range server.Nic.VpcNic {
			components = append(components, &component.ServerComponent{
				PN:              vpc.PN,
				SN:              vpc.SN,
				Model:           vpc.Model,
				FirmwareVersion: vpc.FirmwareVersion,
				Status:          "normal",
				ComponentType:   "网卡",
				Description:     "vpc网卡",
				ServerID:        deviceSnMap[serverSN],
				ProductID:       productPnMap[vpc.PN],
			})
		}

		// rdma网卡
		for _, rdma := range server.Nic.RdmaNic {
			components = append(components, &component.ServerComponent{
				PN:              rdma.PN,
				SN:              rdma.SN,
				Model:           rdma.Model,
				FirmwareVersion: rdma.FirmwareVersion,
				Status:          "normal",
				ComponentType:   "网卡",
				Description:     "rdma网卡",
				ServerID:        deviceSnMap[serverSN],
				ProductID:       productPnMap[rdma.PN],
			})
		}
	}

	return components, nil
}

// ListAcceptanceItemsByOrderID 获取指定工单ID的验收项列表
func (s *assetAcceptanceService) ListAcceptanceItemsByOrderID(ctx context.Context, orderID uint) ([]*model.AcceptanceItem, error) {
	return s.acceptanceItemRepo.ListByOrderID(ctx, orderID)
}

func (s *assetAcceptanceService) RecordOperationHistory(ctx context.Context, orderId uint,
	operatorId uint, operatorName, preStatus, newStatus string,
	remark string, operationTime time.Time) error {
	activityCategory := ""
	// 查一下上一个记录
	if newStatus != string(common.OrderStatus_Pending) {
		lastHistory, err := s.acceptanceOrderRepo.GetLastOperationHistoryByOrderID(ctx, orderId)
		if err != nil {
			return err
		}
		// 修改上一个状态的持续时间
		lastHistory.Duration = int(operationTime.Sub(lastHistory.CreatedAt).Minutes())
		err = s.acceptanceOrderRepo.SaveOperationHistory(ctx, lastHistory)
		if err != nil {
			return err
		}
	}

	switch newStatus {
	case string(common.OrderStatus_Pending):
		activityCategory = common.History_Activity_Create
	case string(common.OrderStatus_Approved):
		activityCategory = common.History_Activity_Approve
	case string(common.OrderStatus_Rejected):
		activityCategory = common.History_Activity_Reject
	case string(common.OrderStatus_In_Progress):
		activityCategory = common.History_Activity_Handle
	case string(common.OrderStatus_Collected):
		activityCategory = common.History_Activity_Collect
	case string(common.OrderStatus_Completed):
		activityCategory = common.History_Activity_Complete
	}

	history := &model.AcceptanceOrderHistory{
		AcceptanceOrderID: orderId,
		PreviousStatus:    preStatus,
		NewStatus:         newStatus,
		OperatorID:        operatorId,
		OperatorName:      operatorName,
		OperationTime:     operationTime,
		ActivityCategory:  activityCategory,
		Remarks:           remark,
	}
	return s.acceptanceOrderRepo.SaveOperationHistory(ctx, history)
}

func (s *assetAcceptanceService) GetAcceptanceOrderHistories(ctx context.Context, orderId uint) ([]*model.AcceptanceOrderHistory, error) {
	return s.acceptanceOrderRepo.GetOperationHistoriesByOrderID(ctx, orderId)
}

func (s *assetAcceptanceService) SaveAcceptedComponents(ctx context.Context, components []*model.InspectingComponentInfo) error {
	if len(components) == 0 {
		return nil
	}
	return s.acceptedComponentRepo.BatchCreate(ctx, components)
}

// 新增：根据工单ID获取已验收组件
func (s *assetAcceptanceService) ListAcceptedComponentsByOrderID(ctx context.Context, orderID uint) ([]*model.InspectingComponentInfo, error) {
	return s.acceptedComponentRepo.ListByOrderID(ctx, orderID)
}

// 新增：根据工单ID和itemIDs批量获取已验收组件
func (s *assetAcceptanceService) ListAcceptedComponentsByOrderIDAndItemIDs(ctx context.Context, orderID uint, itemIDs []uint) ([]*model.InspectingComponentInfo, error) {
	return s.acceptedComponentRepo.ListByOrderIDAndItemIDs(ctx, orderID, itemIDs)
}

// 新增：根据itemIDs批量获取已验收组件
func (s *assetAcceptanceService) ListAcceptedComponentsByItemIDs(ctx context.Context, itemIDs []uint) ([]*model.InspectingComponentInfo, error) {
	const batchSize = 300
	var allComponents []*model.InspectingComponentInfo

	for i := 0; i < len(itemIDs); i += batchSize {
		end := i + batchSize
		if end > len(itemIDs) {
			end = len(itemIDs)
		}

		batchIDs := itemIDs[i:end]
		batchComponents, err := s.acceptedComponentRepo.ListByItemIDs(ctx, batchIDs)
		if err != nil {
			return nil, err
		}
		allComponents = append(allComponents, batchComponents...)
	}

	return allComponents, nil
}

func (s *assetAcceptanceService) ListServerComponentsBySNs(ctx context.Context, sns []string) ([]*component.ServerComponent, error) {
	return s.serverComponentRepo.ListBySNs(ctx, sns)
}

// 批量更新正在验收的组件信息
func (s *assetAcceptanceService) UpdateInspectingComponentInfo(ctx context.Context, req *types.UpdateInspectingComponentInfoReq) error {
	if req == nil || len(req.ComponentInfos) == 0 {
		return errors.New("无效的组件信息")
	}
	// 类型转换（如果必要）
	infos := make([]*model.InspectingComponentInfo, 0, len(req.ComponentInfos))
	for _, info := range req.ComponentInfos {
		infos = append(infos, &model.InspectingComponentInfo{
			ID:          info.ComponentID,
			PN:          info.PN,
			ItemID:      info.ItemId,
			Model:       info.Model,
			Status:      info.Status,
			Description: info.Description,
		})
		fmt.Printf("info , %v\n", *info)
	}

	return s.acceptedComponentRepo.BatchUpdate(ctx, infos)
}

// 设备型号到PN映射相关方法
func (s *assetAcceptanceService) CreateComponentModelPNMapping(ctx context.Context, req *types.CreateComponentModelPNMappingRequest) (*model.ComponentModelPNMapping, error) {
	mapping := &model.ComponentModelPNMapping{
		Model:         req.Model,
		ComponentType: req.ComponentType,
		PN:            req.PN,
		Description:   req.Description,
		Status:        req.Status,
	}

	// 如果状态为空，设置默认值
	if mapping.Status == "" {
		mapping.Status = "ACTIVE"
	}

	if err := s.componentModelPNMappingRepo.Create(ctx, mapping); err != nil {
		s.logger.Error("Failed to create device model PN mapping", zap.Error(err))
		return nil, err
	}

	return mapping, nil
}

func (s *assetAcceptanceService) GetComponentModelPNMappingByID(ctx context.Context, id uint) (*model.ComponentModelPNMapping, error) {
	mapping, err := s.componentModelPNMappingRepo.GetByID(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get device model PN mapping by ID", zap.Uint("id", id), zap.Error(err))
		return nil, err
	}
	return mapping, nil
}

func (s *assetAcceptanceService) UpdateComponentModelPNMapping(ctx context.Context, id uint, req *types.UpdateComponentModelPNMappingRequest) (*model.ComponentModelPNMapping, error) {
	// 先获取现有记录
	mapping, err := s.componentModelPNMappingRepo.GetByID(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get device model PN mapping for update", zap.Uint("id", id), zap.Error(err))
		return nil, err
	}

	// 更新字段
	if req.Model != nil {
		mapping.Model = *req.Model
	}
	if req.ComponentType != nil {
		mapping.ComponentType = *req.ComponentType
	}
	if req.PN != nil {
		mapping.PN = *req.PN
	}
	if req.Description != nil {
		mapping.Description = *req.Description
	}
	if req.Status != nil {
		mapping.Status = *req.Status
	}

	if err := s.componentModelPNMappingRepo.Update(ctx, mapping); err != nil {
		s.logger.Error("Failed to update device model PN mapping", zap.Uint("id", id), zap.Error(err))
		return nil, err
	}

	return mapping, nil
}

func (s *assetAcceptanceService) DeleteComponentModelPNMapping(ctx context.Context, id uint) error {
	if err := s.componentModelPNMappingRepo.Delete(ctx, id); err != nil {
		s.logger.Error("Failed to delete device model PN mapping", zap.Uint("id", id), zap.Error(err))
		return err
	}
	return nil
}

func (s *assetAcceptanceService) ListComponentModelPNMappings(ctx context.Context, filter types.ComponentModelPNMappingFilter) ([]*model.ComponentModelPNMapping, int64, error) {
	mappings, total, err := s.componentModelPNMappingRepo.List(ctx, filter)
	if err != nil {
		s.logger.Error("Failed to list device model PN mappings", zap.Error(err))
		return nil, 0, err
	}
	return mappings, total, nil
}

func (s *assetAcceptanceService) GetComponentPNByModelComponent(ctx context.Context, model, componentType string) (*model.ComponentModelPNMapping, error) {
	mapping, err := s.componentModelPNMappingRepo.GetByModelComponent(ctx, model, componentType)
	if err != nil {
		s.logger.Error("Failed to get device model PN mapping by model/component",
			zap.String("model", model),
			zap.String("componentType", componentType),
			zap.Error(err))
		return nil, err
	}
	return mapping, nil
}

func (s *assetAcceptanceService) FillComponentPNs(ctx context.Context, itemIds []uint, overWriteExisting bool) error {
	// 查询参数
	status := "ACTIVE"
	modelList := []string{}
	//fmt.Printf("itemIds list %v \n", itemIds)

	list, _, err := s.componentModelPNMappingRepo.List(ctx, types.ComponentModelPNMappingFilter{
		Status: &status,
	})

	if err != nil {
		return err
	}
	ModelPNMap := make(map[string]string)
	for _, m := range list {
		ModelPNMap[m.Model] = m.PN
		modelList = append(modelList, m.Model)
	}

	// 一次最多查5个itemId的组件信息，防止查询太多，数据库压力太大
	const (
		maxIDsPerQuery = 30
		batchSize      = 200
	)
	var inspectingComponents []*model.InspectingComponentInfo

	for i := 0; i < len(itemIds); i += maxIDsPerQuery {
		end := i + maxIDsPerQuery
		if end > len(itemIds) {
			end = len(itemIds)
		}
		batch := itemIds[i:end]

		components, err := s.acceptedComponentRepo.ListByItemIDsAndModels(ctx, batch, modelList)
		if err != nil {
			return err
		}
		inspectingComponents = append(inspectingComponents, components...)
	}

	//fmt.Printf("inspectingComponents %v \n", ModelPNMap)
	// 批量更新
	updateList := make([]*model.InspectingComponentInfo, 0, batchSize) // 预分配容量
	for _, cpt := range inspectingComponents {
		pn, ok := ModelPNMap[cpt.Model]
		if !ok {
			continue
		}

		if !overWriteExisting && len(cpt.PN) > 0 {
			continue
		}
		fmt.Println("cpt.Model: ", cpt.Model, "pn", pn)
		cpt.PN = pn
		updateList = append(updateList, cpt)

		// 每满 batchSize 就执行一次批量更新
		if len(updateList) >= batchSize {
			if err := s.acceptedComponentRepo.BatchUpdate(ctx, updateList); err != nil {
				return err
			}
			updateList = updateList[:0] // 清空切片准备下一批
		}
	}

	// 处理最后不足一批的数据
	if len(updateList) > 0 {
		if err := s.acceptedComponentRepo.BatchUpdate(ctx, updateList); err != nil {
			return err
		}
	}

	return nil
}
