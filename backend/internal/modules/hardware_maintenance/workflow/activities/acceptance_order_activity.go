package activities

import (
	componentModel "backend/internal/modules/cmdb/model/component"
	"backend/internal/modules/cmdb/service/asset"
	componentSvc "backend/internal/modules/cmdb/service/component"
	productSvc "backend/internal/modules/cmdb/service/product"
	fileSvc "backend/internal/modules/file/service"
	"backend/internal/modules/hardware_maintenance/common"
	"backend/internal/modules/hardware_maintenance/common/types"
	model "backend/internal/modules/hardware_maintenance/model/acceptance"
	acceptanceServ "backend/internal/modules/hardware_maintenance/service/acceptance"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"

	"go.temporal.io/sdk/activity"
	"gorm.io/gorm"
)

type AcceptanceOrderActivity struct {
	acceptanceService acceptanceServ.AcceptanceService
	fileService       fileSvc.FileService
	componentService  componentSvc.ServerComponentService
	productService    productSvc.ProductService
	deviceService     asset.DeviceService
	db                *gorm.DB
}

func InitAcceptanceOrderActivity(acceptanceService acceptanceServ.AcceptanceService, fileService fileSvc.FileService, componentService componentSvc.ServerComponentService, productService productSvc.ProductService, deviceService asset.DeviceService, db *gorm.DB) *AcceptanceOrderActivity {
	return &AcceptanceOrderActivity{acceptanceService: acceptanceService,
		fileService:      fileService,
		db:               db,
		componentService: componentService,
		productService:   productService,
		deviceService:    deviceService,
	}
}

// UpdateAcceptanceOrderStatusActivity 更新状态
func (a *AcceptanceOrderActivity) UpdateAcceptanceOrderStatusActivity(ctx context.Context, operatorID uint, operatorName string, status string, order *model.AcceptanceOrder, data map[string]interface{}) error {
	// 1. 修改验收单状态
	logger := activity.GetLogger(ctx)
	fields := map[string]interface{}{
		"id":     order.ID,
		"status": status,
	}
	var err error
	switch status {
	case string(common.OrderStatus_Approved), string(common.OrderStatus_Rejected):
		fields["approver_id"] = operatorID
		fields["approver_name"] = operatorName
		err = a.acceptanceService.UpdateAcceptanceOrderFields(ctx, order.ID, fields)
	case string(common.OrderStatus_In_Progress):
		fields["handler_id"] = operatorID
		fields["handler_name"] = operatorName
		err = a.acceptanceService.UpdateAcceptanceOrderFields(ctx, order.ID, fields)
	case string(common.OrderStatus_Collected):
		fields["uploader_id"] = operatorID
		fields["uploader_name"] = operatorName

		// 更新表单结构
		err = a.acceptanceService.UpdateAcceptanceOrderFields(ctx, order.ID, fields)
		return err
	case string(common.OrderStatus_Completed):
		// 需要同步更新items
		var items []*model.AcceptanceItem

		// 解析出参数
		str, ok := data["orderItems"].(string)
		if !ok {
			logger.Error("data[\"orderItems\"].(string) is not ok")
			return errors.New("data[\"orderItems\"].(string) is not ok")
		}

		err = json.Unmarshal([]byte(str), &items)
		if err != nil {
			logger.Error("bs unmarshal fail", "err", err, "str", str)
		}
		order.Status = string(common.OrderStatus_Completed)

		// 只保留isPass为true的items的DeviceID集合和itemID集合
		passDeviceIDs := make(map[uint]struct{})
		passItemIDs := make([]uint, 0)
		for _, item := range items {
			if item.IsPass != nil && *item.IsPass {
				passDeviceIDs[item.DeviceID] = struct{}{}
				passItemIDs = append(passItemIDs, item.ID)
			}
		}

		// 1. 从已验收组件表读取数据（仅isPass为true的itemID）
		acceptedComponents, err := a.acceptanceService.ListAcceptedComponentsByItemIDs(ctx, passItemIDs)
		if err != nil {
			logger.Error("ListAcceptedComponentsByOrderIDAndItemIDs fail", "err", err)
			return err
		}

		// 更新item中的bmcMac,vpcMAc
		var (
			bmcMacMap = make(map[uint][]string)
			vpcMacMap = make(map[uint][]string)
		)

		// 2. 转换为ServerComponent，仅导入isPass为true的设备
		// 先收集所有PN
		pnSet := make(map[string]struct{})
		for _, ac := range acceptedComponents {
			if len(ac.BmcMAC) > 0 && ac.ItemID > 0 {
				bmcMacMap[ac.ItemID] = append(bmcMacMap[ac.ItemID], ac.BmcMAC)
			}
			if len(ac.VpcMAC) > 0 && ac.ItemID > 0 {
				vpcMacMap[ac.ItemID] = append(vpcMacMap[ac.ItemID], ac.VpcMAC)
			}

			if ac.PN != "" {
				pnSet[ac.PN] = struct{}{}
			}
		}

		// 更新item bmcMac以及vpcMAc
		for i := range items {
			if bmcMacs, ok := bmcMacMap[items[i].ID]; ok {
				bmcMacBs, _ := json.Marshal(bmcMacs)
				items[i].BMCMac = string(bmcMacBs)
			}
			if vpcMacs, ok := vpcMacMap[items[i].ID]; ok {
				vpcMacsBs, _ := json.Marshal(vpcMacs)
				items[i].VPCMac = string(vpcMacsBs)
			}
		}

		pns := make([]string, 0, len(pnSet))
		for pn := range pnSet {
			pns = append(pns, pn)
		}
		// 批量查出PN对应的ProductID
		products, _ := a.productService.ListByPNs(ctx, pns)
		pnToProductID := make(map[string]uint)
		for _, prod := range products {
			pnToProductID[prod.PN] = prod.ID
		}
		var serverComponents []*componentModel.ServerComponent
		for _, ac := range acceptedComponents {
			// 跳过不存在或者PN未录入的项目
			if _, ok := passDeviceIDs[ac.DeviceID]; !ok || len(ac.PN) == 0 {
				continue
			}
			productID := pnToProductID[ac.PN]
			serverComponents = append(serverComponents, &componentModel.ServerComponent{
				SN:              ac.SN,
				PN:              ac.PN,
				Model:           ac.Model,
				FirmwareVersion: ac.FirmwareVersion,
				Status:          ac.Status,
				ComponentType:   ac.ComponentType,
				Description:     ac.Description,
				ServerID:        ac.DeviceID,
				ProductID:       productID,
			})
		}

		// 3. 修改order状态, 修改Item, cmdb更新配件表信息
		err = a.acceptanceService.CompleteOrderAndUpdateComponents(ctx, order, items, serverComponents, operatorID, operatorName)
		if err != nil {
			return err
		}
		fmt.Println("order.total")

	}

	if err != nil {
		logger.Error("acceptanceService.UpdateAcceptanceOrder fail", "err", err)
	}

	return err
}

func (a *AcceptanceOrderActivity) GetAcceptanceOrderByIdActivity(ctx context.Context, orderId uint) (*model.AcceptanceOrder, error) {
	return a.acceptanceService.GetAcceptanceOrderByID(ctx, orderId)
}

func (a *AcceptanceOrderActivity) SendFeiShuMsgActivity(ctx context.Context, orderId uint, sentTo uint, msg interface{}) error {
	// TODO 发送通知
	return nil
}

func (a *AcceptanceOrderActivity) RecordOperationHistoryActivity(ctx context.Context, id uint, operatorId uint,
	operatorName string, preStatus, curStatus, remark string, operationTime time.Time) error {
	return a.acceptanceService.RecordOperationHistory(ctx, id, operatorId,
		operatorName, preStatus, curStatus, remark, operationTime)
}

// ProcessServerInfoActivity 处理上传的serverInfo数据
func (a *AcceptanceOrderActivity) ProcessServerInfoActivity(ctx context.Context, orderID uint, serverInfoListStr string) error {
	// 解析JSON字符串
	var (
		serverInfoList []*types.ServerInfoRaw
		logger         = activity.GetLogger(ctx)
	)
	if err := json.Unmarshal([]byte(serverInfoListStr), &serverInfoList); err != nil {
		return fmt.Errorf("解析serverInfo数据失败: %w", err)
	}

	// 获取sn到itemId的映射
	order, err := a.acceptanceService.GetAcceptanceOrderByIDWithItems(ctx, orderID)
	if err != nil {
		return err
	}

	// 构建SN到ItemID的映射
	snToItemMap := make(map[string]*model.AcceptanceItem)
	for i := range order.Items {
		snToItemMap[order.Items[i].DeviceSN] = &order.Items[i]
	}

	// 将数据保存到InspectingComponentInfo表中
	var components []*model.InspectingComponentInfo
	for _, serverInfo := range serverInfoList {
		// 解析每个服务器的组件信息
		serverComponents, err := a.parseServerComponents(ctx, serverInfo, orderID, snToItemMap)
		if err != nil {
			logger.Error("a.parseServerComponents(ctx, serverInfo, orderID) fail", "error", err)
			continue
		}
		components = append(components, serverComponents...)
	}

	// 批量保存到数据库
	if len(components) > 0 {
		if err := a.acceptanceService.SaveAcceptedComponents(ctx, components); err != nil {
			return fmt.Errorf("保存组件信息失败: %w", err)
		}
	}

	return nil
}

// parseServerComponents 解析单个服务器的组件信息
func (a *AcceptanceOrderActivity) parseServerComponents(ctx context.Context, serverInfo *types.ServerInfoRaw, orderID uint, snToItemMap map[string]*model.AcceptanceItem) ([]*model.InspectingComponentInfo, error) {
	var (
		components []*model.InspectingComponentInfo
		existSNMap = make(map[string]bool) // 用于去重，导入的json文件中，可能多次检测到同一个，目前只有网卡会多次检测到同一个
	)

	// 只处理存在于order.Items的设备
	if snToItemMap[serverInfo.ID] == nil {
		return nil, errors.New("服务器信息为空")
	}

	// 1. 收集所有 DeviceSN
	deviceSNs := make([]string, 0)
	for deviceSN := range snToItemMap {
		deviceSNs = append(deviceSNs, deviceSN)
	}

	// 2. 查询设备表，获取 SN->DeviceID 映射
	devices, _, err := a.deviceService.ListBySNs(ctx, deviceSNs)
	snToDeviceID := make(map[string]uint)
	if err == nil {
		for _, dev := range devices {
			snToDeviceID[dev.SN] = dev.ID
		}
	}

	// 获取Model到PN的映射，想办法自动添加进去
	mappingStatus := "ACTIVE"
	ModelPNMap := make(map[string]string)
	componentModelPNs, _, err := a.acceptanceService.ListComponentModelPNMappings(ctx, types.ComponentModelPNMappingFilter{
		Status: &mappingStatus,
	})
	if err != nil {
		return nil, err
	}

	for _, v := range componentModelPNs {
		ModelPNMap[v.Model] = v.PN
	}

	// 设备基本信息
	deviceID := snToDeviceID[serverInfo.ID]
	item := snToItemMap[serverInfo.ID]

	// 解析CPU组件
	cpuInfo := serverInfo.CPU

	for _, cpu := range cpuInfo.Detail {
		deviceID := snToDeviceID[serverInfo.ID]
		component := &model.InspectingComponentInfo{
			DeviceSN:      serverInfo.ID,
			OrderID:       orderID,
			ComponentType: "CPU",
			SN:            cpu.SN,
			PN:            ModelPNMap[cpu.ModelName],
			Model:         cpu.ModelName,
			Status:        "normal",
			Description:   "CPU处理器",
			DeviceID:      deviceID,
		}
		if item != nil {
			component.ItemID = item.ID
		}
		components = append(components, component)
	}

	// 解析GPU组件
	gpuInfo := serverInfo.GPU
	for _, gpu := range gpuInfo.Detail {
		component := &model.InspectingComponentInfo{
			DeviceSN:        serverInfo.ID,
			OrderID:         orderID,
			ComponentType:   "GPU",
			PN:              ModelPNMap[gpu.Name],
			SN:              gpu.Serial,
			Model:           gpu.Name,
			Status:          "normal",
			Description:     "GPU显卡",
			FirmwareVersion: gpu.VbiosVersion,
			DeviceID:        deviceID,
		}
		if item != nil {
			component.ItemID = item.ID
		}
		components = append(components, component)
	}

	// 解析内存组件
	memoryInfo := serverInfo.Memory
	for _, mem := range memoryInfo.Detail {
		component := &model.InspectingComponentInfo{
			DeviceSN:      serverInfo.ID,
			OrderID:       orderID,
			ComponentType: "内存",
			SN:            mem.SN,
			PN:            mem.PN,
			Model:         fmt.Sprintf("%s %s %s", mem.Manufacturer, mem.Size, mem.Frequency),
			Status:        "normal",
			Description:   fmt.Sprintf("%s %s 内存", mem.DdrType, mem.Size),
			DeviceID:      deviceID,
		}
		if item != nil {
			component.ItemID = item.ID
		}
		components = append(components, component)
	}

	// 解析磁盘组件
	diskInfo := serverInfo.Disk
	for _, disk := range diskInfo.Detail {
		// 提取PN和品牌信息
		pn := disk.PN
		brandInfo := ""
		if pn != "" {
			// 先按空格分割
			parts := strings.Fields(pn)
			if len(parts) > 1 {
				// 前面部分作为品牌信息
				brandInfo = strings.Join(parts[:len(parts)-1], " ")
				pn = parts[len(parts)-1]
			}
			// 如果还有下划线，再取最后一段
			if idx := strings.LastIndex(pn, "_"); idx != -1 && idx+1 < len(pn) {
				if brandInfo == "" {
					brandInfo = pn[:idx]
				} else {
					brandInfo = brandInfo + " " + pn[:idx]
				}
				pn = pn[idx+1:]
			}
		}
		// 将品牌信息合并到Model中
		modelInfo := disk.Size
		if brandInfo != "" {
			modelInfo = brandInfo + " " + disk.Size
		}

		// 如果model Info中包含Virtual字样，则不进行解析插入
		if strings.Contains(strings.ToLower(modelInfo), "virtual") {
			continue
		}

		component := &model.InspectingComponentInfo{
			DeviceSN:        serverInfo.ID,
			OrderID:         orderID,
			ComponentType:   "硬盘",
			SN:              disk.SN,
			PN:              pn,
			Model:           disk.PN,
			Status:          "normal",
			Description:     fmt.Sprintf("%s 硬盘", disk.Size),
			FirmwareVersion: disk.FirmwareVersion,
			DeviceID:        deviceID,
		}
		if item != nil {
			component.ItemID = item.ID
		}
		components = append(components, component)
	}

	// 解析主板组件
	bmcInfo := serverInfo.Bmc
	// 额外解析bios固件版本
	biosInfo := serverInfo.Bios
	component := &model.InspectingComponentInfo{
		DeviceSN:        serverInfo.ID,
		OrderID:         orderID,
		ComponentType:   "主板",
		SN:              bmcInfo.FruDetail.BoardSN,
		PN:              bmcInfo.FruDetail.BoardPN,
		Model:           bmcInfo.FruDetail.BoardProduct,
		Status:          "normal",
		Description:     fmt.Sprintf("%s 主板", bmcInfo.FruDetail.BoardMfg),
		FirmwareVersion: biosInfo.FirmwareVersion,
		DeviceID:        deviceID,
		BmcMAC:          bmcInfo.LanDetail.MacAddress,
	}
	if item != nil {
		component.ItemID = item.ID
	}
	components = append(components, component)

	// 解析网卡组件
	nicInfo := serverInfo.Nic
	// VPC网卡
	for _, vpc := range nicInfo.VpcNic {
		component := &model.InspectingComponentInfo{
			DeviceSN:        serverInfo.ID,
			OrderID:         orderID,
			ComponentType:   "网卡",
			SN:              vpc.SN,
			PN:              vpc.PN,
			Model:           fmt.Sprintf("%s Rev %s", vpc.Model, vpc.Rev),
			Status:          "normal",
			Description:     fmt.Sprintf("vpc网卡,mac: %s, rev: %s", vpc.MacAddress, vpc.Rev),
			FirmwareVersion: vpc.FirmwareVersion,
			DeviceID:        deviceID,
			VpcMAC:          vpc.MacAddress,
		}
		if item != nil {
			component.ItemID = item.ID
		}
		// 如果该网卡之前被检测到过了，不重复记录
		if !existSNMap[component.SN] {
			components = append(components, component)
		}
		existSNMap[component.SN] = true
	}
	// RDMA网卡
	for _, rdma := range nicInfo.RdmaNic {
		component := &model.InspectingComponentInfo{
			DeviceSN:        serverInfo.ID,
			OrderID:         orderID,
			ComponentType:   "网卡",
			SN:              rdma.SN,
			PN:              rdma.PN,
			Model:           fmt.Sprintf("%s Rev %s", rdma.Model, rdma.Rev),
			Status:          "normal",
			Description:     fmt.Sprintf("rdma网卡,mac: %s, rev: %s", rdma.MacAddress, rdma.Rev),
			FirmwareVersion: rdma.FirmwareVersion,
			DeviceID:        deviceID,
			RdmaMAC:         rdma.MacAddress,
		}
		if item != nil {
			component.ItemID = item.ID
		}
		// 如果该网卡之前被检测到过了，不重复记录
		if !existSNMap[component.SN] {
			components = append(components, component)
		}
		existSNMap[component.SN] = true
	}

	// 如果其中有非法的SN,即未查到的，则设置拼接成特殊字符

	for _, component := range components {
		if common.InvalidSNMap[strings.ToLower(component.SN)] {
			var idStr string
			uuID, err := uuid.NewRandom()
			if err != nil {
				idStr = common.GenerateOrderNo("")
			} else {
				idStr = strconv.FormatInt(int64(uuID.ID()), 10)
			}

			component.SN = fmt.Sprintf("%s%s", common.InvalidSNStrPrefix, idStr)
		}
	}

	return components, nil
}
