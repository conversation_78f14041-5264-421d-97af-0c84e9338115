package workflow

import (
	"backend/internal/modules/hardware_maintenance/common"
	model "backend/internal/modules/hardware_maintenance/model/acceptance"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"

	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

type OrderWorkFlowState struct {
	OrderID              uint
	OperatorID           uint
	CurrentStatus        common.OrderStatus `json:"current_status"`
	IsCompleted          bool
	ChildWorkflowRunning bool
	ChildWorkflowSuccess *bool
	LastError            string
	ExecutionResult      string
	LastOperationTime    time.Time
}

// WorkflowQueryResult 工作流查询结果
type WorkflowQueryResult struct {
	Status            common.OrderStatus `json:"status"`
	LastError         string             `json:"last_error,omitempty"`
	ExecutionResult   string             `json:"execution_result,omitempty"`
	LastOperationTime time.Time          `json:"last_operation_time"`
	IsCompleted       bool               `json:"is_completed"`
}

// AcceptanceOrderWorkflowV2 验收工单工作流
func AcceptanceOrderWorkflowV2(ctx workflow.Context, input AcceptanceOrderWorkflowInput) error {
	if input.Completed {
		logger := workflow.GetLogger(ctx)
		logger.Info("工作流已完成，直接返回", "acceptance_order", input.OrderID)
		return nil
	}

	// 配置活动选项
	ao := workflow.ActivityOptions{
		StartToCloseTimeout: ActivityStartToCloseTimeout,
		HeartbeatTimeout:    ActivityHeartbeatTimeout,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:        InitialInterval,
			BackoffCoefficient:     BackoffCoefficient,
			MaximumInterval:        MaximumInterval,
			MaximumAttempts:        MaxAttempts,
			NonRetryableErrorTypes: []string{"InvalidInputError"},
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)

	logger := workflow.GetLogger(ctx)
	logger.Info("AcceptanceOrder workflow started", "OrderID", input.OrderID)

	state := &OrderWorkFlowState{
		OrderID:       input.OrderID,
		OperatorID:    input.OperatorID,
		CurrentStatus: common.OrderStatus_Pending,
	}

	var isWorkflowComplete bool
	completeChan := make(chan struct{})

	// 这测SetUpdateHandler,接收更新信号
	err := workflow.SetUpdateHandler(ctx, SIG_AcceptanceOrder, func(ctx workflow.Context, signal WorkflowControlSignal) error {
		logger.Info("收到UpdateHandler信号",
			"Acceptance OrderID", input.OrderID,
			"status", signal.Status,
			"operator", signal.OperatorName)
		ctx = workflow.WithActivityOptions(ctx, ao)

		fmt.Println("正在处理工作流信号")
		// 更新状态
		err := handleSignal(ctx, state, signal)
		if err != nil {
			return err
		}

		if state.IsCompleted {
			isWorkflowComplete = true
			close(completeChan)
		}
		return nil
	})

	if err != nil {
		logger.Info("SetQueryHandler failed.", "error", err)
		return err
	}

	err = workflow.Await(ctx, func() bool {
		select {
		case <-completeChan:
			return true
		default:
			return isWorkflowComplete
		}
	})

	if err != nil {
		logger.Error("Workflow await failed.", "error:", err, zap.Error(err))
		return err
	}
	return nil
}

// 处理信号
func handleSignal(ctx workflow.Context, state *OrderWorkFlowState, signal WorkflowControlSignal) error {
	logger := workflow.GetLogger(ctx)
	logger.Info("收到状态变更信号",
		"OrderID", state.OrderID,
		"From", state.CurrentStatus,
		"To", signal.Status,
		"Operator", signal.OperatorName)
	//fmt.Println("接收到信号%v\n", signal)

	// 更新操作时间
	state.LastOperationTime = workflow.Now(ctx)

	// check信号状态
	order, err := getAcceptanceOrderByIdActivity(ctx, state.OrderID)
	if err != nil {
		logger.Error("获取工单状态失败", "OrderID", state.OrderID, "Error", err)
		state.LastError = fmt.Sprintf("获取工单状态失败: %v", err)
		return err
	}

	if !checkOrderStatusTransfer(order.Status, signal.Status) {
		errorMsg := fmt.Sprintf("非法的状态转变: %s -> %s", order.Status, signal.Status)
		logger.Error(errorMsg, "currentStatus", order.Status, "targetStatus", signal.Status)
		state.LastError = errorMsg
		return errors.New(errorMsg)
	}

	// 清除之前的错误
	newStatus := common.OrderStatus(signal.Status)
	state.LastError = ""
	switch newStatus {
	case common.OrderStatus_Collected:
		if err := beforeCollected(ctx, state, signal); err != nil {
			return err
		}

	}

	// 更新状态前执行公共活动
	if err := updateOrderStatusActivity(ctx, signal.OperatorID, signal.OperatorName, signal.Status, order, signal.Data); err != nil {
		errorMsg := fmt.Sprintf("状态更新失败: %v", err)
		logger.Error(errorMsg, "OrderID", state.OrderID, "Error", err)
		state.LastError = errorMsg
		return err
	}

	// 执行状态特定逻辑
	switch newStatus {
	case common.OrderStatus_Approved:
		afterApproved(ctx, state, signal)
	case common.OrderStatus_Canceled:
		afterCanceled(ctx, state, signal)
	case common.OrderStatus_Rejected:
		afterRejected(ctx, state, signal)
		state.IsCompleted = true
	case common.OrderStatus_In_Progress:
		afterInProgress(ctx, state, signal)
	case common.OrderStatus_Collected:
		if err := afterCollected(ctx, state, signal); err != nil {
			return err
		}
	case common.OrderStatus_Completed:
		afterCompleted(ctx, state, signal)
		state.IsCompleted = true
	default:
		logger.Warn("未知状态", "Status", newStatus)
	}

	// 记一下操作历史
	if err := recordOperationHistoryActivity(ctx, order.ID, signal.OperatorID, signal.OperatorName, string(state.CurrentStatus), signal.Status, signal.Comments, state.LastOperationTime); err != nil {
		errorMsg := fmt.Sprintf("状态更新失败: %v", err)
		logger.Error(errorMsg, "OrderID", state.OrderID, "Error", err)
		state.LastError = errorMsg
		return err
	}

	// 更新工作流状态
	state.CurrentStatus = newStatus
	return err
}

// 更新工单状态活动
func updateOrderStatusActivity(ctx workflow.Context, operatorID uint, operatorName string, status string, order *model.AcceptanceOrder, data map[string]interface{}) error {
	err := workflow.ExecuteActivity(ctx, "UpdateAcceptanceOrderStatusActivity",
		operatorID, operatorName, status, order, data).Get(ctx, nil)
	if err != nil {
		fmt.Println("workflow.ExecuteActivity error, err is:", err)
		return err
	}
	return err
}

func checkOrderStatusTransfer(currentStatus, transferStatus string) bool {
	for _, status := range common.OrderStatusTransferMap[common.OrderStatus(currentStatus)] {
		if common.OrderStatus(transferStatus) == status {
			return true
		}
	}
	return false
}

// 获取工单信息活动
func getAcceptanceOrderByIdActivity(ctx workflow.Context, orderID uint) (*model.AcceptanceOrder, error) {
	var order model.AcceptanceOrder
	err := workflow.ExecuteActivity(ctx, "GetAcceptanceOrderByIdActivity", orderID).Get(ctx, &order)
	return &order, err
}

// 各状态处理函数
func afterApproved(ctx workflow.Context, state *OrderWorkFlowState, signal WorkflowControlSignal) {
	// TODO 发送飞书提醒发起人和验收员
	fmt.Println("xxxx审批单已审批")
}

func afterCanceled(ctx workflow.Context, state *OrderWorkFlowState, signal WorkflowControlSignal) {

}

func afterRejected(ctx workflow.Context, state *OrderWorkFlowState, signal WorkflowControlSignal) {
	// TODO 发送飞书给发起人
	fmt.Println("xxxxx审批单已驳回")
}

func afterInProgress(ctx workflow.Context, state *OrderWorkFlowState, signal WorkflowControlSignal) {
	// TODO 发送飞书给发起人
	fmt.Println("xxxxx验收单已分配")
}

func afterCollected(ctx workflow.Context, state *OrderWorkFlowState, signal WorkflowControlSignal) error {
	// TODO 发送飞书给发起人
	fmt.Println("xxxxx验收单验收结果已上传")
	return nil
}

func beforeCollected(ctx workflow.Context, state *OrderWorkFlowState, signal WorkflowControlSignal) error {
	// 处理上传的serverInfo数据
	if serverInfoListStr, ok := signal.Data["serverInfoList"].(string); ok && serverInfoListStr != "" {
		// 调用activity处理serverInfo数据
		err := workflow.ExecuteActivity(ctx, "ProcessServerInfoActivity", state.OrderID, serverInfoListStr).Get(ctx, nil)
		if err != nil {
			workflow.GetLogger(ctx).Error("处理serverInfo数据失败", "error", err)
			return err
		}
	}

	// TODO 发送飞书给发起人
	fmt.Println("xxxxx验收单已分配")
	return nil
}

func afterCompleted(ctx workflow.Context, state *OrderWorkFlowState, signal WorkflowControlSignal) {
	// TODO 发送飞书给发起人
	fmt.Println("xxxxx验收单已完成")
}

//func recordAcceptanceOperationHistoryActivity(ctx workflow.Context, ticketID uint, operatorId uint,
//	operatorName string, preStatus, curStatus, remark string, operationTime time.Time) error {
//	err := workflow.ExecuteActivity(ctx, "RecordOperationHistoryActivity",
//		ticketID, operatorId, operatorName, preStatus, curStatus, remark, operationTime).Get(ctx, nil)
//	if err != nil {
//		fmt.Println("workflow.ExecuteActivity error, err is:", err)
//		return err
//	}
//	return err
//}
