package racking

import (
	"backend/internal/modules/cmdb/model/asset"
	"time"

	"gorm.io/gorm"
)

const (
	RackingStatusPendingApproval = "PENDING"
	RackingStatusInProgress      = "IN_PROGRESS"
	RackingStatusCompleted       = "COMPLETED"
)

type AssetRackingTicket struct {
	gorm.Model
	TicketNo           string     `gorm:"column:ticket_no;index" json:"ticketNo"`
	Title              string     `gorm:"type:text;comment:上架标题" json:"title"`
	Status             string     `gorm:"type:enum('PENDING','APPROVED','REJECTED','IN_PROGRESS','COMPLETED');default:'PENDING';comment:工单状态" json:"status"`
	Project            string     `gorm:"type:varchar(50);comment:所属项目" json:"project"`
	Quantity           int        `gorm:"comment:上架数量" json:"quantity"`
	Remark             string     `gorm:"type:text;comment:备注" json:"remark"`
	PlannedRackingTime *time.Time `gorm:"comment:计划上架时间" json:"plannedRackingTime"`

	ApplicantID   uint       `gorm:"comment:申请人ID" json:"applicantId"`
	ApplicantName string     `gorm:"type:varchar(50);comment:申请人姓名" json:"applicantName"`
	ApproverID    uint       `gorm:"comment:审批人ID" json:"approverId"`
	ApproverName  string     `gorm:"type:varchar(50);comment:审批人姓名" json:"approverName"`
	Comment       string     `gorm:"type:text;comment:驳回或补充说明" json:"comment"`
	HandlerID     uint       `gorm:"comment:处理人ID" json:"handlerId"`
	HandlerName   string     `gorm:"type:varchar(50);comment:c处理人姓名" json:"handlerName"`
	ApprovedTime  *time.Time `gorm:"comment:审批通过时间" json:"approvedTime"`
	CompletedTime *time.Time `gorm:"comment:完成时间" json:"completedTime"`

	Items     []*AssetRackingItem          `gorm:"foreignKey:TicketID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"items"`
	Histories []*AssetRackingTicketHistory `gorm:"foreignKey:RackingTicketID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"histories"`
}

func (AssetRackingTicket) TableName() string {
	return "asset_racking_tickets"
}

type AssetRackingItem struct {
	gorm.Model
	TicketID uint          `gorm:"index;comment:关联的上架工单ID" json:"ticketId"`
	AssetID  uint          `json:"assetID" gorm:"not null;comment:资产设备关联ID"`
	Device   *asset.Device `json:"device" gorm:"foreignKey:AssetID;references:ID"`
	Status   string        `gorm:"size64;comment:状态" json:"status"` // 记录上架前的状态

	CabinetID    uint   `gorm:"comment:机柜关联ID" json:"cabinetId"`
	RoomID       uint   `gorm:"comment:房间关联ID" json:"roomId"`
	RackPosition int    `gorm:"comment:机架位" json:"rackPosition"`
	VpcMAC       string `gorm:"size:64;comment:业务口MAC地址" json:"vpcMac"`
	BmcMAC       string `gorm:"size:64;comment:管理口MAC地址" json:"bmcMac"`
}

func (AssetRackingItem) TableName() string {
	return "asset_racking_ticket_items"
}

// AssetRackingTicketHistory 上架工单历史记录表
type AssetRackingTicketHistory struct {
	ID               uint           `json:"id" gorm:"primarykey"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `json:"-" gorm:"index"`
	RackingTicketID  uint           `json:"racking_ticket_id" gorm:"comment:上架工单ID"`
	PreviousStatus   string         `json:"previous_status" gorm:"type:varchar(50);comment:先前状态"`
	NewStatus        string         `json:"new_status" gorm:"type:varchar(50);comment:新状态"`
	OperatorID       uint           `json:"operator_id" gorm:"comment:操作人ID"`
	OperatorName     string         `json:"operator_name" gorm:"type:varchar(50);comment:操作人姓名"`
	OperationTime    time.Time      `json:"operation_time" gorm:"comment:操作时间"`
	Duration         int            `json:"duration" gorm:"comment:状态持续时间(分钟)"`
	ActivityCategory string         `json:"activity_category" gorm:"type:varchar(50);comment:活动类别"`
	Remarks          string         `json:"remarks" gorm:"type:text;comment:备注"`
}

func (AssetRackingTicketHistory) TableName() string {
	return "asset_racking_ticket_histories"
}
