package acceptance

import (
	"backend/internal/modules/cmdb/model/component"
	"time"

	"gorm.io/datatypes"

	"gorm.io/gorm"
)

// AcceptanceOrder 验收工单
type AcceptanceOrder struct {
	ID            uint            `gorm:"primaryKey"`
	OrderNo       string          `gorm:"size:64;uniqueIndex;not null;comment:工单号"`
	Title         string          `gorm:"size:128;comment:工单标题"`
	DeviceCount   int             `gorm:"comment:设备数量"`
	Status        string          `gorm:"type:enum('PENDING','APPROVED','REJECTED','IN_PROGRESS','COMPLETED','COLLECTED','','CANCELLED');default:'PENDING';comment:工单状态"`
	InitiatorID   uint            `gorm:"comment:发起人id"`
	InitiatorName string          `gorm:"size:32;comment:发起人姓名"`
	ApproverID    uint            `gorm:"comment:审批人id"`
	ApproverName  string          `gorm:"size:32;comment:审批人姓名"`
	UploaderID    uint            `gorm:"comment:上传人id"`
	UploaderName  string          `gorm:"size:32;comment:上传人姓名"`
	HandlerID     uint            `gorm:"comment:处理人id"`
	HandlerName   string          `gorm:"size:32;comment:处理人姓名"`
	PassCount     uint            `gorm:"comment: 验收通过数量"`
	Total         uint            `gorm:"comment: 验收总数量"`
	Description   string          `gorm:"type:text;comment:说明"`
	Extra         *datatypes.JSON `gorm:"type:json;comment:扩展字段"`
	CreatedAt     time.Time       `gorm:"comment:创建时间"`
	UpdatedAt     time.Time       `gorm:"comment:修改时间"`
	DeletedAt     gorm.DeletedAt  `gorm:"index;comment:删除时间"`

	Items []AcceptanceItem `gorm:"foreignKey:OrderID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
	//History []AcceptanceOrderHistory `gorm:"foreignKey:AcceptanceOrderID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}

// TableName 指定表名
func (AcceptanceOrder) TableName() string {
	return "acceptance_orders"
}

// AcceptanceItem 验收项（设备级）
type AcceptanceItem struct {
	ID           uint            `json:"id" gorm:"primaryKey"`
	OrderID      uint            `json:"order_id" gorm:"index;comment:关联工单ID"`
	DeviceSN     string          `json:"device_sn" gorm:"size:100;index;comment:设备SN"`
	Vendor       string          `json:"vendor" gorm:"size:128;comment:厂商"`
	Model        string          `json:"model" gorm:"size:128;comment:型号"`
	TemplateName string          `json:"template_name" gorm:"type:varchar(100);comment:模板名称"`
	IsPass       *bool           `json:"is_pass" gorm:"type:TINYINT(1);comment:验收是否通过，NULL 表示未填写"`
	Reason       string          `json:"reason" gorm:"type:text;comment:验收不通过原因"`
	Extra        *datatypes.JSON `json:"extra" gorm:"type:json;comment:扩展字段"`
	VPCMac       string          `json:"vpc_mac" gorm:"type:varchar(200);comment:VPC 网卡 MAC 地址"`
	BMCMac       string          `json:"bmc_mac" gorm:"type:varchar(200);comment:BMC 网卡 MAC 地址"`
	DeviceID     uint            `json:"device_id" gorm:"column:device_id;index;comment:关联的设备ID"`
	CreatedAt    time.Time       `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt    time.Time       `json:"updated_at" gorm:"comment:修改时间"`
	DeletedAt    gorm.DeletedAt  `json:"deleted_at" gorm:"index;comment:删除时间"`

	Components []*component.ServerComponent `json:"components" gorm:"-"`
}

// TableName 指定表名
func (AcceptanceItem) TableName() string {
	return "acceptance_items"
}

// AssetAcceptanceOrderHistory 上架工单历史记录表
type AcceptanceOrderHistory struct {
	ID                uint           `json:"id" gorm:"primarykey"`
	CreatedAt         time.Time      `json:"created_at"`
	UpdatedAt         time.Time      `json:"updated_at"`
	DeletedAt         gorm.DeletedAt `json:"-" gorm:"index"`
	AcceptanceOrderID uint           `json:"acceptance_order_id" gorm:"comment:上架工单ID"`
	PreviousStatus    string         `json:"previous_status" gorm:"type:varchar(50);comment:先前状态"`
	NewStatus         string         `json:"new_status" gorm:"type:varchar(50);comment:新状态"`
	OperatorID        uint           `json:"operator_id" gorm:"comment:操作人ID"`
	OperatorName      string         `json:"operator_name" gorm:"type:varchar(50);comment:操作人姓名"`
	OperationTime     time.Time      `json:"operation_time" gorm:"comment:操作时间"`
	Duration          int            `json:"duration" gorm:"comment:状态持续时间(分钟)"`
	ActivityCategory  string         `json:"activity_category" gorm:"type:varchar(50);comment:活动类别"`
	Remarks           string         `json:"remarks" gorm:"type:text;comment:备注"`
}

// TableName 指定表名
func (AcceptanceOrderHistory) TableName() string {
	return "acceptance_order_histories"
}

// InspectingComponentInfo 正在验收的组件信息表
// 参考ServerComponent结构
// 只保留必要字段
type InspectingComponentInfo struct {
	ID              uint           `json:"id" gorm:"primaryKey"`
	OrderID         uint           `json:"order_id" gorm:"comment:验收工单ID（可选）"`
	ItemID          uint           `json:"item_id" gorm:"index:idx_item_model;comment:验收项ID"`
	DeviceSN        string         `json:"device_sn" gorm:"size:100;index;comment:设备SN"`
	DeviceID        uint           `json:"device_id" gorm:"index;comment:设备ID（可选）"` // 👈 单字段索引
	ComponentType   string         `json:"component_type" gorm:"type:varchar(50);comment:组件类型"`
	SN              string         `json:"sn" gorm:"type:varchar(150);comment:序列号"`
	PN              string         `json:"pn" gorm:"type:varchar(100);comment:部件号"`
	Model           string         `json:"model" gorm:"type:varchar(100);index:idx_item_model;comment:型号"`
	FirmwareVersion string         `json:"firmware" gorm:"type:varchar(100);comment:固件版本"`
	Status          string         `json:"status" gorm:"type:varchar(20);default:normal;comment:状态"`
	Description     string         `json:"description" gorm:"type:text;comment:描述"`
	RdmaMAC         string         `json:"rdma_mac" gorm:"type:varchar(128);comment:RDMA MAC地址"`
	BmcMAC          string         `json:"bmc_mac" gorm:"type:varchar(200);comment:BMC MAC地址"`
	VpcMAC          string         `json:"vpc_mac" gorm:"type:varchar(200);comment:VPC MAC地址"`
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"-" gorm:"index"`
}

func (InspectingComponentInfo) TableName() string {
	return "inspecting_component_infos"
}

// ComponentModelPNMapping 设备型号到PN映射表,用于自动填充检测的信息
type ComponentModelPNMapping struct {
	ID            uint           `json:"id" gorm:"primaryKey"`
	Model         string         `json:"model" gorm:"size:128;index;comment:设备型号"`
	ComponentType string         `json:"component_type" gorm:"size:64;index;comment:组件类型"`
	PN            string         `json:"pn" gorm:"size:128;comment:部件号"`
	Description   string         `json:"description" gorm:"type:text;comment:描述"`
	Status        string         `json:"status" gorm:"type:enum('ACTIVE','INACTIVE');default:'ACTIVE';comment:状态"`
	CreatedAt     time.Time      `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt     time.Time      `json:"updated_at" gorm:"comment:修改时间"`
	DeletedAt     gorm.DeletedAt `json:"deleted_at" gorm:"index;comment:删除时间"`
}

// TableName 指定表名
func (ComponentModelPNMapping) TableName() string {
	return "component_model_pn_mappings"
}
