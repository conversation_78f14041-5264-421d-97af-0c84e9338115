package racking

import (
	"backend/internal/common/constants"
	"backend/internal/modules/cmdb/service/asset"
	"backend/internal/modules/hardware_maintenance/common"
	"backend/internal/modules/hardware_maintenance/common/types"
	model "backend/internal/modules/hardware_maintenance/model/racking"
	"backend/internal/modules/hardware_maintenance/service/racking"
	"backend/internal/modules/hardware_maintenance/workflow"
	"backend/response"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// RackingController 资产上架控制器
type RackingController struct {
	service       racking.RackingService
	deviceService asset.DeviceService
}

// NewRackingController 创建资产上架控制器

func NewRackingController(service racking.RackingService, deviceService asset.DeviceService) *RackingController {
	return &RackingController{service: service, deviceService: deviceService}
}

// RegisterRoutes 注册路由
func (c *RackingController) RegisterRoutes(r *gin.RouterGroup) {
	rackingRouter := r.Group("/asset/racking")
	{
		rackingRouter.GET("", c.List)                                   //获取所有工单
		rackingRouter.GET("/:id", c.GetRackingTicketDetail)             //根据ID获取入库单详情
		rackingRouter.PUT("/:id/transition", c.TransitionRackingTicket) // 更新工单状态,
		rackingRouter.POST("/create_ticket", c.Create)                  //
		rackingRouter.GET("/:id/history", c.GetOperationHistory)        // 获取工单操作历史
	}
}

// ListRackingTickets 获取上架工单列表
// @Summary 获取上架工单列表
// @Description 分页获取上架工单列表，支持筛选
// @Tags 资产管理-上架工单
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Param ticket_no query string false "工单编号"
// @Param status query string false "工单状态"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /api/v1/cmdb/asset/rack [get]
func (c *RackingController) List(ctx *gin.Context) {

	var query types.RackingTicketFilter
	if err := ctx.ShouldBindQuery(&query); err != nil {
		fmt.Printf("err is %v\n", err)
		response.Fail(ctx, http.StatusBadRequest, "无效的查询参数")
		return
	}

	if query.Page < 1 {
		query.Page = common.DefaultPage
	}
	if query.PageSize < 1 {
		query.PageSize = common.DefaultPageSize
	}

	orders, total, err := c.service.ListRackingTickets(ctx, query)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取上架工单列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  orders,
		"total": total,
	}, "获取上架工单列表成功")
}

// GetRackingTicket 获取上架工单详情
// @Summary 获取上架工单详情
// @Description 根据ID获取上架工单详情
// @Tags 资产管理-上架工单
// @Accept json
// @Produce json
// @Param id path int true "上架工单ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /api/v1/cmdb/asset/rack/{id} [get]
func (c *RackingController) GetRackingTicketDetail(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	ticket, err := c.service.GetRackingTicketByIDWithItems(ctx, uint(id))
	// 把上架状态items中的资产状态修改成当前设备的资产状态
	//for i := range ticket.Items {
	//	if ticket.Items[i] != nil && ticket.Items[i].Device != nil {
	//		ticket.Items[i].Status = ticket.Items[i].Device.AssetStatus
	//	}
	//}

	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取上架工单失败: "+err.Error())
		return
	}

	response.Success(ctx, ticket, "获取上架工单成功")
}

// 创建工单
func (c *RackingController) Create(ctx *gin.Context) {
	var req types.CreateRackingTicketRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		fmt.Printf("err is %v\n", err)
		response.Fail(ctx, http.StatusBadRequest, "参数错误")
		return
	}

	// TODO 校验资产状态
	assetIDs := make([]uint, 0, len(req.RackItems))
	for _, item := range req.RackItems {
		assetIDs = append(assetIDs, item.AssetID)
	}
	devices, _, err := c.deviceService.ListByIDs(ctx, assetIDs)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "查询设备信息错误")
		return
	}

	invalidSNs := []string{}
	for _, d := range devices {
		if d.AssetStatus != constants.AssetStatusVerify {
			invalidSNs = append(invalidSNs, d.SN)
		}
	}

	if len(invalidSNs) > 0 {
		errStr := invalidSNs[0]
		for i := 1; i < len(invalidSNs); i++ {
			errStr += ", " + invalidSNs[i]
		}
		response.Fail(ctx, http.StatusBadRequest, fmt.Sprintf("只有已验收的设备才能上架，SN为 %s 的设备状态不符合要求", errStr))
		return
	}

	userID, userName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取用户信息失败")

	}
	orderNo := common.GenerateOrderNo(common.RackingTicketNoPrefix)
	order := model.AssetRackingTicket{
		TicketNo:           orderNo,
		Title:              req.Title,
		Status:             string(common.OrderStatus_Pending),
		ApplicantID:        userID,
		ApplicantName:      userName,
		Quantity:           len(req.RackItems),
		PlannedRackingTime: req.PlannedRackTime,
		Project:            req.Project,
		Remark:             req.Remark,
	}

	items := make([]*model.AssetRackingItem, 0, len(req.RackItems))
	for _, rackItem := range req.RackItems {
		item := &model.AssetRackingItem{
			Status:       rackItem.Status,
			AssetID:      rackItem.AssetID,
			RoomID:       rackItem.RoomID,
			CabinetID:    rackItem.CabinetID,
			RackPosition: rackItem.RackPosition,
		}
		items = append(items, item)
	}

	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "创建工单失败: "+err.Error())
		return
	}

	if err := c.service.CreateRackingTicket(ctx, &order, items, userID, userName); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建工单失败, err: "+err.Error())
		return
	}

	response.Success(ctx, nil, "创建工单成功")
}

func (c *RackingController) GetOperationHistory(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	histories, err := c.service.GetRackingOperationHistories(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取操作历史失败: "+err.Error())
		return
	}

	response.Success(ctx, histories, "获取操作历史成功")
}

func (c *RackingController) TransitionRackingTicket(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	userID, userName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取用户信息失败")

	}

	var req types.TransitionRackingTicketRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		fmt.Printf("err is %v\n", err)
		response.Fail(ctx, http.StatusBadRequest, "参数错误")
		return
	}

	if req.Status == string(common.OrderStatus_Completed) {
		if len(req.RackItems) == 0 {
			response.Fail(ctx, http.StatusBadRequest, "上架信息条目为空")
			return
		}
		// 去除校验Mac的填入合法性
		//for _, item := range req.RackItems {
		//	if _, err := net.ParseMAC(item.VpcMAC); len(item.VpcMAC) != 0 && err != nil {
		//		response.Fail(ctx, http.StatusBadRequest, fmt.Sprintf("非法的VPC MAC: %s", item.VpcMAC))
		//		return
		//	}
		//
		//	if _, err := net.ParseMAC(item.BmcMAC); len(item.BmcMAC) != 0 && err != nil {
		//		response.Fail(ctx, http.StatusBadRequest, fmt.Sprintf("非法的BMC MAC: %s", item.BmcMAC))
		//		return
		//	}
		//}
	}

	reqBs, err := json.Marshal(req)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "json.Marshal(req) fail, err: "+err.Error())
		return
	}

	signal := workflow.WorkflowControlSignal{
		Status:       req.Status,
		OperatorID:   userID,
		OperatorName: userName,
		Comments:     req.Comment,
		Data: map[string]interface{}{
			"req": string(reqBs),
		},
		Timestamp: 0,
	}

	err = c.service.TriggerWorkflow(ctx, uint(id), signal)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新工单状态失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新工单状态成功")
}

//// checkDeviceSNs 简单校验一下sn
//func (c *RackingController) checkDeviceSNs(ctx context.Context, checkSns []string) (repeatedSNs, noExistSNs []string, err error) {
//	snMap := make(map[string]int)
//	sns := make([]string, 0)
//	for _, sn := range checkSns {
//		if snMap[sn] == 1 {
//			repeatedSNs = append(repeatedSNs, sn)
//		}
//		checkSns = append(checkSns)
//		snMap[sn]++
//	}
//	if len(repeatedSNs) > 0 {
//		return repeatedSNs, nil, fmt.Errorf("重复设备SN：%s", repeatedSNs[0])
//	}
//
//	devices, _, err2 := c.deviceService.ListBySNs(ctx, sns)
//	if err2 != nil {
//		return nil, nil, fmt.Errorf("内部错误，err: %s", err2.Error())
//	}
//
//	existingSNs := make([]string, 0)
//	for _, device := range devices {
//		existingSNs = append(existingSNs, device.SN)
//	}
//
//	noExistSNs = common.StringDifference(existingSNs, sns)
//	if len(noExistSNs) > 0 {
//		return nil, noExistSNs, fmt.Errorf("设备SN: %d 不存在", noExistSNs[0])
//	}
//
//	return
//}
