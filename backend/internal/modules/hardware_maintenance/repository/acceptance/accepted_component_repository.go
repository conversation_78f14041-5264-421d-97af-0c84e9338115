package acceptance

import (
	"backend/internal/modules/hardware_maintenance/model/acceptance"
	"context"

	"gorm.io/gorm/clause"

	"gorm.io/gorm"
)

type AcceptedComponentRepository interface {
	Create(ctx context.Context, component *acceptance.InspectingComponentInfo) error
	BatchCreate(ctx context.Context, components []*acceptance.InspectingComponentInfo) error
	ListByOrderID(ctx context.Context, orderID uint) ([]*acceptance.InspectingComponentInfo, error)
	ListByDeviceID(ctx context.Context, deviceID uint) ([]*acceptance.InspectingComponentInfo, error)
	ListByOrderIDAndItemIDs(ctx context.Context, orderID uint, itemIDs []uint) ([]*acceptance.InspectingComponentInfo, error)
	ListByItemIDs(ctx context.Context, itemIDs []uint) ([]*acceptance.InspectingComponentInfo, error)
	ListByItemIDsAndModels(ctx context.Context, itemIDs []uint, models []string) ([]*acceptance.InspectingComponentInfo, error)
	ListByItemsIDsAndComponentType(ctx context.Context, itemIDs []uint, componentTypes []string) ([]*acceptance.InspectingComponentInfo, error)
	DeleteByOrderID(ctx context.Context, orderID uint) error
	GetDB() *gorm.DB
	WithTx(tx *gorm.DB) AcceptedComponentRepository
	BatchUpdate(ctx context.Context, infos []*acceptance.InspectingComponentInfo) error
}

type acceptedComponentRepo struct {
	db *gorm.DB
}

func NewAcceptedComponentRepository(db *gorm.DB) AcceptedComponentRepository {
	return &acceptedComponentRepo{db: db}
}

func (r *acceptedComponentRepo) Create(ctx context.Context, component *acceptance.InspectingComponentInfo) error {
	return r.db.WithContext(ctx).Create(component).Error
}

func (r *acceptedComponentRepo) BatchCreate(ctx context.Context, components []*acceptance.InspectingComponentInfo) error {
	return r.db.WithContext(ctx).Create(&components).Error
}

func (r *acceptedComponentRepo) ListByOrderID(ctx context.Context, orderID uint) ([]*acceptance.InspectingComponentInfo, error) {
	var components []*acceptance.InspectingComponentInfo
	err := r.db.WithContext(ctx).Where("order_id = ?", orderID).Find(&components).Error
	return components, err
}

func (r *acceptedComponentRepo) ListByDeviceID(ctx context.Context, deviceID uint) ([]*acceptance.InspectingComponentInfo, error) {
	var components []*acceptance.InspectingComponentInfo
	err := r.db.WithContext(ctx).Where("device_id = ?", deviceID).Find(&components).Error
	return components, err
}

func (r *acceptedComponentRepo) ListByOrderIDAndItemIDs(ctx context.Context, orderID uint, itemIDs []uint) ([]*acceptance.InspectingComponentInfo, error) {
	var components []*acceptance.InspectingComponentInfo
	if len(itemIDs) == 0 {
		return components, nil
	}
	err := r.db.WithContext(ctx).Where("order_id = ? AND item_id IN ?", orderID, itemIDs).Find(&components).Error
	return components, err
}

func (r *acceptedComponentRepo) ListByItemIDs(ctx context.Context, itemIDs []uint) ([]*acceptance.InspectingComponentInfo, error) {
	var components []*acceptance.InspectingComponentInfo
	if len(itemIDs) == 0 {
		return components, nil
	}
	err := r.db.WithContext(ctx).Where("item_id IN ?", itemIDs).Find(&components).Error
	return components, err
}

func (r *acceptedComponentRepo) ListByItemsIDsAndComponentType(ctx context.Context, itemIDs []uint, componentTypes []string) ([]*acceptance.InspectingComponentInfo, error) {
	var components []*acceptance.InspectingComponentInfo
	db := r.db.WithContext(ctx).Where("item_id IN ?", itemIDs)
	if len(componentTypes) > 0 {
		db = db.Where("component_type IN ?", componentTypes)
	}
	err := db.Find(&components).Error

	if err != nil {
		return nil, err
	}

	return components, nil

}

func (r *acceptedComponentRepo) DeleteByOrderID(ctx context.Context, orderID uint) error {
	return r.db.WithContext(ctx).Where("order_id = ?", orderID).Delete(&acceptance.InspectingComponentInfo{}).Error
}

func (r *acceptedComponentRepo) GetDB() *gorm.DB {
	return r.db
}

func (r *acceptedComponentRepo) WithTx(tx *gorm.DB) AcceptedComponentRepository {
	if tx == nil {
		return r
	}
	return &acceptedComponentRepo{db: tx}
}

func (r *acceptedComponentRepo) BatchUpdate(
	ctx context.Context,
	infos []*acceptance.InspectingComponentInfo,
) error {
	if len(infos) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).Clauses(clause.OnConflict{
		UpdateAll: false,
		DoUpdates: clause.AssignmentColumns([]string{
			"pn", "model", "status", "description",
		}),
	}).Create(&infos).Error
}

func (r *acceptedComponentRepo) ListByItemIDsAndModels(ctx context.Context, itemIDs []uint, models []string) ([]*acceptance.InspectingComponentInfo, error) {
	var components []*acceptance.InspectingComponentInfo
	db := r.db.WithContext(ctx).Where("item_id IN ?", itemIDs)
	if len(models) > 0 {
		db = db.Where("model IN ?", models)
	}
	err := db.Find(&components).Error

	if err != nil {
		return nil, err
	}

	return components, nil
}
