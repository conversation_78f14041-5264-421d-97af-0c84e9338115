package common

import (
	fileModel "backend/internal/modules/file/model"
	fileService "backend/internal/modules/file/service"
	"backend/response"
	cryptorand "crypto/rand"
	"encoding/binary"
	"fmt"
	"hash/fnv"
	"net/http"
	"path"
	"path/filepath"
	"time"

	"github.com/gin-gonic/gin"
)

// StringDifference 返回在 a 中但不在 b 中的字符串元素集合outbound_details
func StringDifference(a, b []string) []string {
	// 将 b 中的元素放入 map 方便快速查找
	bSet := make(map[string]struct{}, len(b))
	for _, item := range b {
		bSet[item] = struct{}{}
	}

	// 遍历 a，找出那些不在 bSet 中的
	var diff []string
	for _, item := range a {
		if _, found := bSet[item]; !found {
			diff = append(diff, item)
		}
	}

	return diff
}

// GenerateOrderNo 生成工单号
func GenerateOrderNo(prefix string) string {
	// 使用时间戳和随机数生成工单号
	timestamp := time.Now().Format("20060102150405")

	// 使用crypto/rand代替math/rand
	randomBytes := make([]byte, 4)
	var random int
	if _, err := cryptorand.Read(randomBytes); err != nil {
		// 如果加密随机数生成失败，使用时间纳秒作为备选
		random = int(time.Now().UnixNano() % 1000)
	} else {
		// 使用加密随机数，取模确保在0-999范围内
		random = int(binary.BigEndian.Uint32(randomBytes) % 1000)
	}

	return fmt.Sprintf("%s%s%03d", prefix, timestamp, random)
}

// GetCurrentUserInfo 从上下文获取当前用户信息
func GetCurrentUserInfo(ctx *gin.Context) (uint, string, error) {
	userIDVal, exists := ctx.Get("userID")
	if !exists {
		return 0, "", response.NewErrorWithStatusCode("未获取到用户ID", http.StatusUnauthorized)
	}
	userID, ok := userIDVal.(uint)
	if !ok {
		return 0, "", response.NewErrorWithStatusCode("用户ID格式错误", http.StatusUnauthorized)
	}

	// 获取用户信息
	userInfoVal, exists := ctx.Get("userInfo")
	if !exists {
		return 0, "", response.NewErrorWithStatusCode("未获取到用户信息", http.StatusUnauthorized)
	}
	userInfo, ok := userInfoVal.(map[string]interface{})
	if !ok {
		return 0, "", response.NewErrorWithStatusCode("用户信息格式错误", http.StatusUnauthorized)
	}

	// 获取用户真实姓名
	realName, ok := userInfo["realName"].(string)
	if !ok || realName == "" {
		return 0, "", response.NewErrorWithStatusCode("未获取到用户姓名", http.StatusUnauthorized)
	}

	return userID, realName, nil
}

// GetFileModuleType 获取文件module类型
func GetFileStoragePath(fileType fileModel.FileType, rawUrl string) (storagePath string) {
	storedFileName := path.Base(rawUrl)

	switch fileType {
	case fileModel.FileTypeImage:
		storagePath = filepath.Join(fileService.ImageDir, storedFileName)
	case fileModel.FileTypeDoc:
		storagePath = filepath.Join(fileService.DocDir, storedFileName)
	case fileModel.FileTypeCSV:
		storagePath = filepath.Join(fileService.DocDir, storedFileName)
	default:
		storagePath = filepath.Join(fileService.OtherDir, storedFileName)
	}
	return storagePath
}

// DeduplicateStrings 对字符串切片进行去重，保持原始顺序
func DeduplicateStrings(input []string) []string {
	if len(input) == 0 {
		return nil
	}

	// 使用 map 记录已出现的元素
	seen := make(map[string]struct{})
	result := make([]string, 0, len(input))

	for _, item := range input {
		// 如果元素尚未出现
		if _, exists := seen[item]; !exists {
			// 标记为已出现
			seen[item] = struct{}{}
			// 添加到结果切片
			result = append(result, item)
		}
	}
	return result
}

func StringHashToUint(s string) (uint, error) {
	h := fnv.New32a()
	if _, err := h.Write([]byte(s)); err != nil {
		return 0, err
	}
	return uint(h.Sum32()), nil
}

func CheckStatusTransfer(current, target OrderStatus, transferMap map[OrderStatus][]OrderStatus) bool {
	for _, status := range transferMap[current] {
		if status == target {
			return true
		}
	}
	return false
}

//
//func GetDeviceSNsAndPNsFromServerMap(serverMap map[string]*types.ServerInfo) (deviceSNs, PNs []string) {
//	for sn, s := range serverMap {
//		deviceSNs = append(deviceSNs, sn)
//		// 主板
//		PNs = append(PNs, s.Bmc.FruDetail.BoardPN)
//
//		// CPU 无PN
//
//		// GPU 无PN
//
//		// disk
//		for _, disk := range s.Disk.Detail {
//			PNs = append(PNs, disk.PN)
//		}
//
//		// rdma
//		for _, rdma := range s.Nic.RdmaNic {
//			PNs = append(PNs, rdma.PN)
//		}
//
//		for _, vpc := range s.Nic.VpcNic {
//			PNs = append(PNs, vpc.PN)
//		}
//
//		for _, mem := range s.Memory.Detail {
//			PNs = append(PNs, mem.PN)
//		}
//	}
//	return
//}
