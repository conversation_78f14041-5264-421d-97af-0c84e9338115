package workflow

import (
	"time"
)

// PurchaseRequestWorkflowInput 采购申请工作流输入参数
type PurchaseRequestWorkflowInput struct {
	RequestID    uint    `json:"request_id"`
	RequestNo    string  `json:"request_no"`
	RequesterID  uint    `json:"requester_id"`
	RequestType  string  `json:"request_type"`
	UrgencyLevel string  `json:"urgency_level"`
}

// PurchaseRequestWorkflowState 采购申请工作流状态
type PurchaseRequestWorkflowState struct {
	CurrentStage    string           `json:"current_stage"`
	ApprovalHistory []ApprovalSignal `json:"approval_history"`
	RollbackHistory []RollbackSignal `json:"rollback_history"`
	StartedAt       time.Time        `json:"started_at"`
	CompletedAt     *time.Time       `json:"completed_at"`
	Result          string           `json:"result"` // approved, rejected, cancelled
	CanRollback     bool             `json:"can_rollback"`
	StageHistory    []string         `json:"stage_history"` // 阶段历史，用于回退
}

// ApprovalSignal 审批信号
type ApprovalSignal struct {
	ApproverID   uint      `json:"approver_id"`
	Action       string    `json:"action"` // approve, reject, rollback
	Comments     string    `json:"comments"`
	Timestamp    time.Time `json:"timestamp"`
	RollbackTo   string    `json:"rollback_to,omitempty"`   // 回退到的阶段
	CurrentStage string    `json:"current_stage,omitempty"` // 当前阶段
}

// RollbackSignal 回退信号
type RollbackSignal struct {
	ApproverID   uint      `json:"approver_id"`
	RollbackTo   string    `json:"rollback_to"`   // 回退到的阶段
	CurrentStage string    `json:"current_stage"` // 当前阶段
	Comments     string    `json:"comments"`
	Timestamp    time.Time `json:"timestamp"`
}

// 工作流信号名称常量
const (
	SignalNameApproval = "approval_signal"
	SignalNameRollback = "rollback_signal"
)

// ApprovalResult 审批结果
type ApprovalResult struct {
	Action       string `json:"action"`        // approve, reject, rollback
	ApproverID   uint   `json:"approver_id"`
	OperatorName string `json:"operator_name"`
	Comments     string `json:"comments"`
}

// PurchaseApprovalStages 采购审批阶段
const (
	StageProjectManagerApproval    = "project_manager_approval"
	StagePurchasingManagerApproval = "purchasing_manager_approval"
	StageCTONotification           = "cto_notification"
	StageCompleted                 = "completed"
)
