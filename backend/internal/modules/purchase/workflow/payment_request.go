package workflow

import (
	"backend/internal/modules/purchase/constants"
	"time"
)

// 付款申请工作流常量
const (
	// PaymentRequestTaskQueue 付款申请工作流任务队列
	PaymentRequestTaskQueue = constants.TaskQueuePaymentRequest

	// PaymentRequestWorkflowName 付款申请工作流名称
	PaymentRequestWorkflowName = "PaymentRequestWorkflow"

	// SignalNamePaymentApproval 付款申请审批信号
	SignalNamePaymentApproval = "payment_approval_signal"

	// SignalNamePaymentRollback 付款申请回退信号
	SignalNamePaymentRollback = "payment_rollback_signal"
)

// PaymentRequestWorkflowInput 付款申请工作流输入
type PaymentRequestWorkflowInput struct {
	PaymentID         uint    `json:"payment_id"`          // 付款申请ID
	PaymentNo         string  `json:"payment_no"`          // 付款申请编号
	RequesterID       uint    `json:"requester_id"`        // 申请人ID
	ContractID        uint    `json:"contract_id"`         // 合同ID
	TotalAmount       float64 `json:"total_amount"`        // 付款总金额
	CompanyID         uint    `json:"company_id"`          // 公司ID
	CompanyName       string  `json:"company_name"`        // 公司名称
	ProjectName       string  `json:"project_name"`        // 项目名称
	BusinessType      string  `json:"business_type"`       // 业务类型
	CompanyEntityType string  `json:"company_entity_type"` // 公司主体类型
}

// PaymentApprovalSignal 付款申请审批信号
type PaymentApprovalSignal struct {
	ApproverID   uint      `json:"approver_id"`   // 审批人ID
	Action       string    `json:"action"`        // 动作：approve, reject
	Comments     string    `json:"comments"`      // 审批意见
	CurrentStage string    `json:"current_stage"` // 当前阶段
	Timestamp    time.Time `json:"timestamp"`     // 时间戳
}

// PaymentRollbackSignal 付款申请回退信号
type PaymentRollbackSignal struct {
	ApproverID   uint      `json:"approver_id"`   // 审批人ID
	RollbackTo   string    `json:"rollback_to"`   // 回退到的阶段
	Comments     string    `json:"comments"`      // 回退意见
	CurrentStage string    `json:"current_stage"` // 当前阶段
	Timestamp    time.Time `json:"timestamp"`     // 时间戳
}

// PaymentRequestWorkflowState 付款申请工作流状态
type PaymentRequestWorkflowState struct {
	CurrentStage      string                  `json:"current_stage"`       // 当前阶段
	CompanyEntityType string                  `json:"company_entity_type"` // 公司主体类型
	ApprovalHistory   []PaymentApprovalSignal `json:"approval_history"`    // 审批历史
	RollbackHistory   []PaymentRollbackSignal `json:"rollback_history"`    // 回退历史
	StartedAt         time.Time               `json:"started_at"`          // 开始时间
	CompletedAt       *time.Time              `json:"completed_at"`        // 完成时间
	CanRollback       bool                    `json:"can_rollback"`        // 是否可以回退
	StageHistory      []string                `json:"stage_history"`       // 阶段历史
}

// PaymentApprovalResult 付款申请审批结果
type PaymentApprovalResult struct {
	Action       string `json:"action"`        // 审批动作
	ApproverID   uint   `json:"approver_id"`   // 审批人ID
	OperatorName string `json:"operator_name"` // 操作人姓名
	Comments     string `json:"comments"`      // 审批意见
}

// GetNextPaymentStage 获取下一个付款申请阶段
func GetNextPaymentStage(currentStage string, companyEntityType string) string {
	switch currentStage {
	case constants.PaymentStagePurchaseSupplyChainReview:
		return constants.PaymentStageFinanceManagerReview
	case constants.PaymentStageFinanceManagerReview:
		return constants.PaymentStageEnterpriseManagerReview
	case constants.PaymentStageEnterpriseManagerReview:
		// 企业负责人审批后，根据公司主体类型直接路由到对应的复核阶段
		if companyEntityType == constants.CompanyEntityTypeCyber {
			return constants.PaymentStageCyberReview
		}
		return constants.PaymentStageOtherEntityReview
	case constants.PaymentStageCyberReview:
		return constants.PaymentStageFundManagerReview
	case constants.PaymentStageOtherEntityReview:
		return constants.PaymentStageFundManagerReview
	case constants.PaymentStageFundManagerReview:
		// 资金负责人审批后，根据公司主体类型直接路由到对应的经办阶段
		if companyEntityType == constants.CompanyEntityTypeCyber {
			return constants.PaymentStageCyberHandler
		}
		return constants.PaymentStageOtherEntityHandler
	case constants.PaymentStageCyberHandler:
		return constants.PaymentStageCyberReview2
	case constants.PaymentStageOtherEntityHandler:
		return constants.PaymentStageFinalReview
	case constants.PaymentStageCyberReview2:
		return constants.PaymentStageFinalReview
	case constants.PaymentStageFinalReview:
		return constants.PaymentStageCompleted
	default:
		return constants.PaymentStageCompleted
	}
}

// GetPreviousPaymentStage 获取上一个付款申请阶段
func GetPreviousPaymentStage(currentStage string, companyEntityType string) string {
	switch currentStage {
	case constants.PaymentStageFinanceManagerReview:
		return constants.PaymentStagePurchaseSupplyChainReview
	case constants.PaymentStageEnterpriseManagerReview:
		return constants.PaymentStageFinanceManagerReview

	case constants.PaymentStageCyberReview:
		return constants.PaymentStageEnterpriseManagerReview
	case constants.PaymentStageOtherEntityReview:
		return constants.PaymentStageEnterpriseManagerReview
	case constants.PaymentStageFundManagerReview:
		if companyEntityType == constants.CompanyEntityTypeCyber {
			return constants.PaymentStageCyberReview
		}
		return constants.PaymentStageOtherEntityReview

	case constants.PaymentStageCyberHandler:
		return constants.PaymentStageFundManagerReview
	case constants.PaymentStageOtherEntityHandler:
		return constants.PaymentStageFundManagerReview
	case constants.PaymentStageCyberReview2:
		return constants.PaymentStageCyberHandler
	case constants.PaymentStageFinalReview:
		if companyEntityType == constants.CompanyEntityTypeCyber {
			return constants.PaymentStageCyberReview2
		}
		return constants.PaymentStageOtherEntityHandler
	case constants.PaymentStageCompleted:
		return constants.PaymentStageFinalReview
	default:
		return constants.PaymentStagePurchaseSupplyChainReview
	}
}

// IsValidPaymentApprovalAction 验证付款申请审批动作是否有效
func IsValidPaymentApprovalAction(action string) bool {
	validActions := []string{
		constants.ActionApprove,
		constants.ActionReject,
	}

	for _, validAction := range validActions {
		if action == validAction {
			return true
		}
	}
	return false
}

// IsValidPaymentRollbackStage 验证付款申请回退阶段是否有效
func IsValidPaymentRollbackStage(stage string) bool {
	// 有效的回退目标阶段（不包括草稿阶段）
	validStages := []string{
		constants.PaymentStagePurchaseSupplyChainReview,
		constants.PaymentStageFinanceManagerReview,
		constants.PaymentStageEnterpriseManagerReview,
		constants.PaymentStageCyberReview,
		constants.PaymentStageOtherEntityReview,
		constants.PaymentStageFundManagerReview,
		constants.PaymentStageCyberHandler,
		constants.PaymentStageOtherEntityHandler,
		constants.PaymentStageCyberReview2,
		constants.PaymentStageFinalReview,
	}

	for _, validStage := range validStages {
		if stage == validStage {
			return true
		}
	}
	return false
}

// CanRollbackFromStage 检查是否可以从当前阶段回退
func CanRollbackFromStage(currentStage string) bool {
	// 草稿、完成状态不能回退
	if currentStage == constants.PaymentStageDraft ||
		currentStage == constants.PaymentStageCompleted ||
		currentStage == constants.PaymentStageRejected ||
		currentStage == constants.PaymentStageCancelled {
		return false
	}

	// 采购负责人阶段不允许回退（第一个审批阶段）
	if currentStage == constants.PaymentStagePurchaseSupplyChainReview {
		return false
	}

	return true
}

// GetPaymentStageTimeout 获取付款申请阶段超时时间
func GetPaymentStageTimeout(stage string) time.Duration {
	// 根据不同阶段设置不同的超时时间
	switch stage {
	case constants.PaymentStagePurchaseSupplyChainReview:
		return 24 * time.Hour // 采购与供应链审批：24小时
	case constants.PaymentStageFinanceManagerReview:
		return 48 * time.Hour // 财务负责人审批：48小时
	case constants.PaymentStageEnterpriseManagerReview:
		return 48 * time.Hour // 企业负责人审批：48小时
	case constants.PaymentStageCyberReview:
		return 24 * time.Hour // 赛博付款申请复核：24小时
	case constants.PaymentStageOtherEntityReview:
		return 24 * time.Hour // 其他主体付款申请复核：24小时
	case constants.PaymentStageFundManagerReview:
		return 48 * time.Hour // 资金负责人审批：48小时
	case constants.PaymentStageCyberHandler:
		return 24 * time.Hour // 赛博付款经办：24小时
	case constants.PaymentStageOtherEntityHandler:
		return 24 * time.Hour // 其他主体付款经办：24小时
	case constants.PaymentStageCyberReview2:
		return 24 * time.Hour // 赛博复核2：24小时
	case constants.PaymentStageFinalReview:
		return 24 * time.Hour // 最终复核：24小时
	default:
		return 24 * time.Hour // 默认：24小时
	}
}
