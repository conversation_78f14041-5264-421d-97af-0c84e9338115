package workflow

import (
	"fmt"
	"time"

	"backend/internal/modules/purchase/constants"
	"backend/internal/modules/purchase/workflow/activity/contract"

	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

// PurchaseContractApprovalWorkflow 采购合同审批工作流
func PurchaseContractApprovalWorkflow(ctx workflow.Context, input PurchaseContractWorkflowInput) error {
	logger := workflow.GetLogger(ctx)
	logger.Info("采购合同审批工作流开始", "contractID", input.ContractID, "contractNo", input.ContractNo)

	// 初始化工作流状态
	state := PurchaseContractWorkflowState{
		CurrentStage:    constants.ContractStagePurchaseReview, // 从采购负责人审批开始
		ApprovalHistory: []ContractApprovalSignal{},
		RollbackHistory: []ContractRollbackSignal{},
		StartedAt:       workflow.Now(ctx),
		CanRollback:     false, // 第一个阶段不能回退
		StageHistory:    []string{constants.ContractStagePurchaseReview},
	}

	// 注册状态查询处理器
	err := workflow.SetQueryHandler(ctx, "getContractWorkflowState", func() (PurchaseContractWorkflowState, error) {
		return state, nil
	})
	if err != nil {
		logger.Error("注册状态查询处理器失败", "error", err)
		return err
	}

	// 设置活动选项
	contractOptions := workflow.ActivityOptions{
		StartToCloseTimeout: 5 * time.Minute,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Second,
			BackoffCoefficient: 2.0,
			MaximumInterval:    time.Minute,
			MaximumAttempts:    5,
		},
	}
	contractCtx := workflow.WithActivityOptions(ctx, contractOptions)

	// 转换为活动需要的输入类型
	activityInput := contract.PurchaseContractWorkflowInput{
		ContractID:   input.ContractID,
		ContractNo:   input.ContractNo,
		RequesterID:  input.RequesterID,
		SupplierID:   input.SupplierID,
		TotalAmount:  input.TotalAmount,
		ContractType: input.ContractType,
	}

	// 发送工作流开始通知 - 待审批通知
	err = workflow.ExecuteActivity(contractCtx, contract.SendFeishuContractNotificationActivity,
		activityInput, "submitted", "采购负责人", "", "", "", "").Get(ctx, nil)
	if err != nil {
		logger.Error("发送合同工作流开始通知失败", "error", err)
		// 通知失败不影响流程继续
	}

	// 设置审批超时
	const approvalTimeout = 7 * 24 * time.Hour // 7天

	// 开始审批流程循环
	for {
		var err error

		switch state.CurrentStage {
		case constants.ContractStagePurchaseReview:
			// 同步工作流状态到数据库
			err = workflow.ExecuteActivity(contractCtx, contract.UpdateContractStatusActivity,
				input.ContractID, "purchase_review", input.RequesterID, "", "进入采购负责人审批阶段", constants.ActionSubmit).Get(ctx, nil)
			if err != nil {
				logger.Error("更新数据库状态失败", "error", err, "stage", state.CurrentStage)
			}

			// 采购负责人审批阶段的通知在工作流开始时已发送，不再重复发送

			approvalResult, err := handleContractApprovalStageWithRollback(ctx, constants.ContractStagePurchaseReview, approvalTimeout, &state, activityInput)
			if err != nil {
				logger.Error("采购负责人审批阶段出错", "error", err)
				return err
			}

			switch approvalResult.Action {
			case constants.ActionApprove:
				moveToNextContractStage(ctx, &state, constants.ContractStageFinanceReview)

				// 同步工作流状态到数据库 - 使用审批信号中的信息
				err = workflow.ExecuteActivity(contractCtx, contract.UpdateContractStatusActivity,
					input.ContractID, "finance_review", approvalResult.ApproverID, approvalResult.OperatorName, approvalResult.Comments, constants.ActionApprove).Get(ctx, nil)
				if err != nil {
					logger.Error("更新数据库状态失败", "error", err, "stage", constants.ContractStageFinanceReview)
				}

				// 发送财务负责人审批阶段通知
				err = workflow.ExecuteActivity(contractCtx, contract.SendFeishuContractNotificationActivity,
					activityInput, "submitted", "财务负责人", "", approvalResult.Comments, "", "").Get(ctx, nil)
				if err != nil {
					logger.Error("发送阶段变更通知失败", "error", err)
					// 通知失败不影响流程继续
				}
			case constants.ActionReject:
				// 拒绝申请
				state.CurrentStage = constants.ContractStageCompleted
				state.Result = "rejected"
				now := workflow.Now(ctx)
				state.CompletedAt = &now

				// 同步工作流状态到数据库
				err = workflow.ExecuteActivity(contractCtx, contract.UpdateContractStatusActivity,
					input.ContractID, "rejected", approvalResult.ApproverID, approvalResult.OperatorName, approvalResult.Comments, constants.ActionReject).Get(ctx, nil)
				if err != nil {
					logger.Error("更新数据库状态失败", "error", err)
				}

				// 发送合同被拒绝通知
				err = workflow.ExecuteActivity(contractCtx, contract.SendFeishuContractNotificationActivity,
					activityInput, "rejected", "", "", approvalResult.Comments, "", "").Get(ctx, nil)
				if err != nil {
					logger.Error("发送合同拒绝通知失败", "error", err)
				}

				logger.Info("采购合同被拒绝", "contractID", input.ContractID, "reason", approvalResult.Comments)
				return nil
			default:
				logger.Error("未知的审批动作", "action", approvalResult.Action)
				return fmt.Errorf("未知的审批动作: %s", approvalResult.Action)
			}

		case constants.ContractStageFinanceReview:
			approvalResult, err := handleContractApprovalStageWithRollback(ctx, constants.ContractStageFinanceReview, approvalTimeout, &state, activityInput)
			if err != nil {
				logger.Error("财务负责人审批阶段出错", "error", err)
				return err
			}

			switch approvalResult.Action {
			case constants.ActionApprove:
				moveToNextContractStage(ctx, &state, constants.ContractStageLegalReview)

				// 同步工作流状态到数据库
				err = workflow.ExecuteActivity(contractCtx, contract.UpdateContractStatusActivity,
					input.ContractID, "legal_review", approvalResult.ApproverID, approvalResult.OperatorName, approvalResult.Comments, constants.ActionApprove).Get(ctx, nil)
				if err != nil {
					logger.Error("更新数据库状态失败", "error", err, "stage", constants.ContractStageLegalReview)
				}

				// 发送法务负责人审批阶段通知
				err = workflow.ExecuteActivity(contractCtx, contract.SendFeishuContractNotificationActivity,
					activityInput, "submitted", "法务负责人", "", approvalResult.Comments, "", "").Get(ctx, nil)
				if err != nil {
					logger.Error("发送阶段变更通知失败", "error", err)
				}
			case constants.ActionReject:
				handleContractRejection(ctx, contractCtx, &state, activityInput, approvalResult)
				return nil
			case constants.ActionRollback:
				handleContractRollback(ctx, contractCtx, &state, activityInput, approvalResult, "finance_review")
				continue
			default:
				logger.Error("未知的审批动作", "action", approvalResult.Action)
				return fmt.Errorf("未知的审批动作: %s", approvalResult.Action)
			}

		case constants.ContractStageLegalReview:
			approvalResult, err := handleContractApprovalStageWithRollback(ctx, constants.ContractStageLegalReview, approvalTimeout, &state, activityInput)
			if err != nil {
				logger.Error("法务负责人审批阶段出错", "error", err)
				return err
			}

			switch approvalResult.Action {
			case constants.ActionApprove:
				moveToNextContractStage(ctx, &state, constants.ContractStageEnterpriseReview)

				// 同步工作流状态到数据库
				err = workflow.ExecuteActivity(contractCtx, contract.UpdateContractStatusActivity,
					input.ContractID, "enterprise_review", approvalResult.ApproverID, approvalResult.OperatorName, approvalResult.Comments, constants.ActionApprove).Get(ctx, nil)
				if err != nil {
					logger.Error("更新数据库状态失败", "error", err, "stage", constants.ContractStageEnterpriseReview)
				}

				// 发送企业负责人审批阶段通知
				err = workflow.ExecuteActivity(contractCtx, contract.SendFeishuContractNotificationActivity,
					activityInput, "submitted", "企业负责人", "", approvalResult.Comments, "", "").Get(ctx, nil)
				if err != nil {
					logger.Error("发送阶段变更通知失败", "error", err)
				}
			case constants.ActionReject:
				handleContractRejection(ctx, contractCtx, &state, activityInput, approvalResult)
				return nil
			case constants.ActionRollback:
				handleContractRollback(ctx, contractCtx, &state, activityInput, approvalResult, "legal_review")
				continue
			default:
				logger.Error("未知的审批动作", "action", approvalResult.Action)
				return fmt.Errorf("未知的审批动作: %s", approvalResult.Action)
			}

		case constants.ContractStageEnterpriseReview:
			approvalResult, err := handleContractApprovalStageWithRollback(ctx, constants.ContractStageEnterpriseReview, approvalTimeout, &state, activityInput)
			if err != nil {
				logger.Error("企业负责人审批阶段出错", "error", err)
				return err
			}

			switch approvalResult.Action {
			case constants.ActionApprove:
				moveToNextContractStage(ctx, &state, constants.ContractStageInternalSign)

				// 同步工作流状态到数据库
				err = workflow.ExecuteActivity(contractCtx, contract.UpdateContractStatusActivity,
					input.ContractID, "internal_sign", approvalResult.ApproverID, approvalResult.OperatorName, approvalResult.Comments, constants.ActionApprove).Get(ctx, nil)
				if err != nil {
					logger.Error("更新数据库状态失败", "error", err, "stage", constants.ContractStageInternalSign)
				}

				// 发送内部签章阶段通知
				err = workflow.ExecuteActivity(contractCtx, contract.SendFeishuContractNotificationActivity,
					activityInput, "submitted", "内部签章", "", approvalResult.Comments, "", "").Get(ctx, nil)
				if err != nil {
					logger.Error("发送阶段变更通知失败", "error", err)
				}
			case constants.ActionReject:
				handleContractRejection(ctx, contractCtx, &state, activityInput, approvalResult)
				return nil
			case constants.ActionRollback:
				handleContractRollback(ctx, contractCtx, &state, activityInput, approvalResult, "enterprise_review")
				continue
			default:
				logger.Error("未知的审批动作", "action", approvalResult.Action)
				return fmt.Errorf("未知的审批动作: %s", approvalResult.Action)
			}

		case constants.ContractStageInternalSign:
			approvalResult, err := handleContractApprovalStageWithRollback(ctx, constants.ContractStageInternalSign, approvalTimeout, &state, activityInput)
			if err != nil {
				logger.Error("内部签章阶段出错", "error", err)
				return err
			}

			switch approvalResult.Action {
			case constants.ActionApprove:
				moveToNextContractStage(ctx, &state, constants.ContractStageDoubleSign)

				// 同步工作流状态到数据库
				err = workflow.ExecuteActivity(contractCtx, contract.UpdateContractStatusActivity,
					input.ContractID, "double_sign", approvalResult.ApproverID, approvalResult.OperatorName, approvalResult.Comments, constants.ActionApprove).Get(ctx, nil)
				if err != nil {
					logger.Error("更新数据库状态失败", "error", err, "stage", constants.ContractStageDoubleSign)
				}

				// 发送双方签章阶段通知
				err = workflow.ExecuteActivity(contractCtx, contract.SendFeishuContractNotificationActivity,
					activityInput, "submitted", "双方签章", "", approvalResult.Comments, "", "").Get(ctx, nil)
				if err != nil {
					logger.Error("发送阶段变更通知失败", "error", err)
				}
			case constants.ActionReject:
				handleContractRejection(ctx, contractCtx, &state, activityInput, approvalResult)
				return nil
			case constants.ActionRollback:
				handleContractRollback(ctx, contractCtx, &state, activityInput, approvalResult, "internal_sign")
				continue
			default:
				logger.Error("未知的审批动作", "action", approvalResult.Action)
				return fmt.Errorf("未知的审批动作: %s", approvalResult.Action)
			}

		case constants.ContractStageDoubleSign:
			approvalResult, err := handleContractApprovalStageWithRollback(ctx, constants.ContractStageDoubleSign, approvalTimeout, &state, activityInput)
			if err != nil {
				logger.Error("双方签章阶段出错", "error", err)
				return err
			}

			switch approvalResult.Action {
			case constants.ActionApprove:
				// 审批完成
				moveToNextContractStage(ctx, &state, constants.ContractStageCompleted)
				state.Result = "approved"
				now := workflow.Now(ctx)
				state.CompletedAt = &now

				// 同步工作流状态到数据库
				err = workflow.ExecuteActivity(contractCtx, contract.UpdateContractStatusActivity,
					input.ContractID, "completed", approvalResult.ApproverID, approvalResult.OperatorName, approvalResult.Comments, constants.ActionApprove).Get(ctx, nil)
				if err != nil {
					logger.Error("更新数据库状态失败", "error", err)
				}

				// 发送合同审批完成通知
				err = workflow.ExecuteActivity(contractCtx, contract.SendFeishuContractNotificationActivity,
					activityInput, "approved", "", "", approvalResult.Comments, "", "").Get(ctx, nil)
				if err != nil {
					logger.Error("发送合同审批完成通知失败", "error", err)
				}

				logger.Info("采购合同审批完成", "contractID", input.ContractID)
				return nil

			case constants.ActionReject:
				handleContractRejection(ctx, contractCtx, &state, activityInput, approvalResult)
				return nil
			case constants.ActionRollback:
				handleContractRollback(ctx, contractCtx, &state, activityInput, approvalResult, "double_sign")
				continue
			default:
				logger.Error("未知的审批动作", "action", approvalResult.Action)
				return fmt.Errorf("未知的审批动作: %s", approvalResult.Action)
			}

		case constants.ContractStageCompleted:
			logger.Info("采购合同审批流程已完成", "contractID", input.ContractID, "result", state.Result)
			return nil

		default:
			logger.Error("未知的审批阶段", "stage", state.CurrentStage)
			return fmt.Errorf("未知的审批阶段: %s", state.CurrentStage)
		}
	}
}

// handleContractApprovalStageWithRollback 处理合同审批阶段（支持回退）
func handleContractApprovalStageWithRollback(ctx workflow.Context, stage string, timeout time.Duration, state *PurchaseContractWorkflowState, input contract.PurchaseContractWorkflowInput) (*ContractApprovalResult, error) {
	logger := workflow.GetLogger(ctx)
	logger.Info("开始处理合同审批阶段", "stage", stage)

	// 创建审批信号通道
	approvalChan := workflow.GetSignalChannel(ctx, SignalNameContractApproval)
	rollbackChan := workflow.GetSignalChannel(ctx, SignalNameContractRollback)

	// 创建超时计时器
	timerCancellation := workflow.NewTimer(ctx, timeout)

	// 选择器，用于等待信号或超时
	selector := workflow.NewSelector(ctx)
	var approvalSignal ContractApprovalSignal
	var rollbackSignal ContractRollbackSignal
	var timerFired bool
	var signalType = "" // 明确初始化

	// 添加审批信号处理
	selector.AddReceive(approvalChan, func(c workflow.ReceiveChannel, more bool) {
		c.Receive(ctx, &approvalSignal)
		signalType = constants.ActionApprove
		logger.Info("收到合同审批信号", "approverID", approvalSignal.ApproverID, "action", approvalSignal.Action)
	})

	// 添加回退信号处理
	selector.AddReceive(rollbackChan, func(c workflow.ReceiveChannel, more bool) {
		c.Receive(ctx, &rollbackSignal)
		signalType = constants.ActionRollback
		logger.Info("收到合同回退信号", "approverID", rollbackSignal.ApproverID, "rollbackTo", rollbackSignal.RollbackTo)
	})

	// 添加超时处理
	selector.AddFuture(timerCancellation, func(f workflow.Future) {
		timerFired = true
		signalType = constants.ActionRollback
		logger.Info("合同审批超时", "stage", stage)
	})

	// 等待信号或超时
	selector.Select(ctx)

	logger.Info("处理信号完成", "signalType", signalType, "timerFired", timerFired)

	// 如果超时，返回错误
	if timerFired {
		return nil, fmt.Errorf("合同审批阶段 %s 超时", stage)
	}

	// 处理审批信号
	if signalType == constants.ActionApprove {
		// 记录审批历史
		approvalSignal.CurrentStage = stage
		state.ApprovalHistory = append(state.ApprovalHistory, approvalSignal)

		// 获取用户真实姓名
		operatorName := GetUserRealName(ctx, approvalSignal.ApproverID)

		// 标准化动作，确保与常量匹配
		action := approvalSignal.Action
		switch action {
		case constants.ActionApprove:
			action = constants.ActionApprove
		case constants.ActionReject:
			action = constants.ActionReject
		}

		// 返回审批结果
		return &ContractApprovalResult{
			Action:       action,
			ApproverID:   approvalSignal.ApproverID,
			OperatorName: operatorName,
			Comments:     approvalSignal.Comments,
		}, nil
	}

	// 处理回退信号
	if signalType == constants.ActionRollback {
		// 检查是否可以回退
		if !canContractRollbackToStage(state, rollbackSignal.RollbackTo) {
			logger.Error("无法回退到指定阶段", "currentStage", stage, "rollbackTo", rollbackSignal.RollbackTo)
			return nil, fmt.Errorf("无法回退到指定阶段: %s", rollbackSignal.RollbackTo)
		}

		// 记录回退历史
		state.RollbackHistory = append(state.RollbackHistory, rollbackSignal)

		// 执行回退
		rollbackToContractStage(ctx, state, rollbackSignal.RollbackTo)

		// 获取用户真实姓名
		operatorName := GetUserRealName(ctx, rollbackSignal.ApproverID)

		// 返回回退结果
		return &ContractApprovalResult{
			Action:       constants.ActionRollback,
			ApproverID:   rollbackSignal.ApproverID,
			OperatorName: operatorName,
			Comments:     rollbackSignal.Comments,
		}, nil
	}

	// 如果没有收到任何有效信号，返回错误
	return nil, fmt.Errorf("合同审批阶段处理异常: 未收到有效信号, signalType=%s, timerFired=%v", signalType, timerFired)
}

// moveToNextContractStage 移动到下一个合同审批阶段
func moveToNextContractStage(ctx workflow.Context, state *PurchaseContractWorkflowState, nextStage string) {
	logger := workflow.GetLogger(ctx)
	logger.Info("合同审批阶段转换", "from", state.CurrentStage, "to", nextStage)

	state.CurrentStage = nextStage
	state.StageHistory = append(state.StageHistory, nextStage)

	// 根据阶段设置是否可以回退
	switch nextStage {
	case constants.ContractStagePurchaseReview:
		state.CanRollback = false // 第一阶段不能回退
	case constants.ContractStageCompleted:
		state.CanRollback = false // 已完成不能回退
	default:
		state.CanRollback = true // 其他阶段可以回退
	}
}

// canContractRollbackToStage 检查是否可以回退到指定阶段
func canContractRollbackToStage(state *PurchaseContractWorkflowState, targetStage string) bool {
	// 第一个阶段不能回退
	if state.CurrentStage == constants.ContractStagePurchaseReview {
		return false
	}

	// 检查目标阶段是否在历史中且在当前阶段之前
	targetFound := false
	for _, stage := range state.StageHistory {
		if stage == targetStage {
			targetFound = true
		}
		if stage == state.CurrentStage {
			break
		}
	}

	return targetFound
}

// rollbackToContractStage 回退到指定阶段
func rollbackToContractStage(ctx workflow.Context, state *PurchaseContractWorkflowState, targetStage string) {
	logger := workflow.GetLogger(ctx)
	logger.Info("合同回退到指定阶段", "from", state.CurrentStage, "to", targetStage)

	// 更新当前阶段
	state.CurrentStage = targetStage

	// 更新回退权限（第一阶段不能回退，其他阶段可以回退）
	state.CanRollback = targetStage != constants.ContractStagePurchaseReview

	// 清理目标阶段之后的历史记录
	newStageHistory := []string{}
	for _, stage := range state.StageHistory {
		newStageHistory = append(newStageHistory, stage)
		if stage == targetStage {
			break
		}
	}
	state.StageHistory = newStageHistory
}

// handleContractRejection 处理合同拒绝
func handleContractRejection(ctx workflow.Context, contractCtx workflow.Context, state *PurchaseContractWorkflowState, input contract.PurchaseContractWorkflowInput, approvalResult *ContractApprovalResult) {
	logger := workflow.GetLogger(ctx)

	// 拒绝申请
	state.CurrentStage = constants.ContractStageCompleted
	state.Result = "rejected"
	now := workflow.Now(ctx)
	state.CompletedAt = &now

	// 同步工作流状态到数据库
	err := workflow.ExecuteActivity(contractCtx, contract.UpdateContractStatusActivity,
		input.ContractID, "rejected", approvalResult.ApproverID, approvalResult.OperatorName, approvalResult.Comments, constants.ActionReject).Get(ctx, nil)
	if err != nil {
		logger.Error("更新数据库状态失败", "error", err)
	}

	// 发送合同被拒绝通知
	err = workflow.ExecuteActivity(contractCtx, contract.SendFeishuContractNotificationActivity,
		input, "rejected", "", "", approvalResult.Comments, "", "").Get(ctx, nil)
	if err != nil {
		logger.Error("发送合同拒绝通知失败", "error", err)
	}

	logger.Info("采购合同被拒绝", "contractID", input.ContractID, "reason", approvalResult.Comments)
}

// handleContractRollback 处理合同回退
func handleContractRollback(ctx workflow.Context, contractCtx workflow.Context, state *PurchaseContractWorkflowState, input contract.PurchaseContractWorkflowInput, approvalResult *ContractApprovalResult, fromStage string) {
	logger := workflow.GetLogger(ctx)

	// 发送回退通知
	err := workflow.ExecuteActivity(contractCtx, contract.SendFeishuContractNotificationActivity,
		input, "rollback", state.CurrentStage, "", approvalResult.Comments, fromStage, "").Get(ctx, nil)
	if err != nil {
		logger.Error("发送合同回退通知失败", "error", err)
		// 通知失败不影响流程继续
	}

	// 同步回退状态到数据库
	err = workflow.ExecuteActivity(contractCtx, contract.UpdateContractStatusActivity,
		input.ContractID, state.CurrentStage, approvalResult.ApproverID, approvalResult.OperatorName, approvalResult.Comments, constants.ActionRollback).Get(ctx, nil)
	if err != nil {
		logger.Error("更新回退状态失败", "error", err)
		// 数据库更新失败不影响工作流状态更新
	}
}

// GetUserRealName 获取用户真实姓名
func GetUserRealName(ctx workflow.Context, userID uint) string {
	// 设置活动选项
	activityOptions := workflow.ActivityOptions{
		StartToCloseTimeout: time.Minute,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Second,
			BackoffCoefficient: 2.0,
			MaximumInterval:    time.Minute,
			MaximumAttempts:    3,
		},
	}
	activityCtx := workflow.WithActivityOptions(ctx, activityOptions)

	var result string
	err := workflow.ExecuteActivity(activityCtx, contract.GetUserRealNameActivity, userID).Get(ctx, &result)
	if err != nil {
		// 如果获取失败，返回默认值
		return fmt.Sprintf("用户_%d", userID)
	}
	return result
}
