package contract

import (
	"backend/internal/modules/purchase/constants"
	"context"
	"fmt"
	"time"

	"backend/configs"
	"backend/internal/common/utils/notifier"
	"backend/internal/infrastructure/database"
	"backend/internal/modules/purchase/model"

	"go.uber.org/zap"

	"backend/internal/common/utils"

	"gorm.io/gorm"
)

// PurchaseContractWorkflowInput 采购合同工作流输入参数
type PurchaseContractWorkflowInput struct {
	ContractID   uint    `json:"contract_id"`
	ContractNo   string  `json:"contract_no"`
	RequesterID  uint    `json:"requester_id"`
	SupplierID   uint    `json:"supplier_id"`
	TotalAmount  float64 `json:"total_amount"`
	ContractType string  `json:"contract_type"`
}

// WorkflowStageToContractDBStatus 将合同工作流阶段映射到数据库状态
func WorkflowStageToContractDBStatus(stage string) string {
	// 返回工作流状态和执行状态
	switch stage {
	case "purchase_review":
		return constants.ContractStagePurchaseReview
	case "finance_review":
		return constants.ContractStageFinanceReview
	case "legal_review":
		return constants.ContractStageLegalReview
	case "enterprise_review":
		return constants.ContractStageEnterpriseReview
	case "internal_sign":
		return constants.ContractStageInternalSign
	case "double_sign":
		return constants.ContractStageDoubleSign
	case "completed":
		return constants.ContractStageCompleted
	case "rejected":
		return constants.ContractStageRejected
	default:
		return constants.ContractStagePurchaseReview
	}
}

// UpdateContractStatusActivity 更新采购合同状态活动
func UpdateContractStatusActivity(ctx context.Context, contractID uint, stage string, updatedBy uint, operatorName string, comments string, action string) error {
	// 连接数据库
	db, err := connectDB()
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}

	// 获取当前采购合同
	var contract model.PurchaseContract
	if err := db.First(&contract, contractID).Error; err != nil {
		return fmt.Errorf("获取采购合同失败: %w", err)
	}

	// 将工作流阶段转换为数据库状态
	Status := WorkflowStageToContractDBStatus(stage)

	// 如果状态没有变化，不需要更新
	if contract.Status == Status {
		return nil
	}

	// 记录旧状态
	oldStatus := contract.Status

	// 更新状态 - 明确指定要更新的字段
	if err := db.Model(&contract).Select("status", "updated_by", "updated_at").Updates(map[string]interface{}{
		"status":     Status,
		"updated_by": updatedBy,
		"updated_at": time.Now(),
	}).Error; err != nil {
		return fmt.Errorf("更新采购合同状态失败: %w", err)
	}

	// 如果没有传递操作人姓名，则查询获取
	if operatorName == "" {
		operatorName = getUserRealName(db, updatedBy)
	}

	// 如果没有传递action，根据状态推断
	if action == "" {
		switch stage {
		case "purchase_review":
			action = constants.ActionSubmit
		case "finance_review", "legal_review", "enterprise_review", "internal_sign", "double_sign", "completed":
			action = constants.ActionApprove
		case "rejected":
			action = constants.ActionReject
		default:
			action = constants.ActionUpdate
		}
	}

	// 记录状态变更历史
	now := time.Now()
	history := model.PurchaseApprovalHistory{
		BusinessType:   constants.BusinessTypePurchaseContract,
		BusinessID:     contractID,
		PreviousStatus: oldStatus,
		NewStatus:      Status,
		Action:         action,
		OperatorID:     updatedBy,
		OperatorName:   operatorName,
		OperationTime:  now,
		Comments:       comments,
		CreatedAt:      now,
	}

	if err := db.Create(&history).Error; err != nil {
		return fmt.Errorf("记录合同状态历史失败: %w", err)
	}

	return nil
}



// SendFeishuContractNotificationActivity 发送飞书合同通知活动
func SendFeishuContractNotificationActivity(ctx context.Context, input PurchaseContractWorkflowInput, notifyType string, toStage string, operatorName string, comments string, fromStage string, toStageDisplay string) error {
	// 连接数据库
	db, err := connectDB()
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}

	// 获取配置
	cfg, err := configs.LoadConfig()
	if err != nil {
		return fmt.Errorf("加载配置失败: %w", err)
	}

	// 获取飞书通知器
	baseNotifier, err := getFeishuNotifier()
	if err != nil {
		return fmt.Errorf("获取飞书通知器失败: %w", err)
	}

	// 创建采购专用通知器
	purchaseNotifier := notifier.NewPurchaseFeishuNotifier(
		baseNotifier,
		cfg.Feishu.PurchaseWebhookURL,
		cfg.Feishu.PurchaseSecret,
		cfg.Feishu.PurchaseUrlTemplates,
		cfg.Feishu.PurchaseEnabled,
	)

	// 设置数据库连接，用于查询审批历史
	purchaseNotifier.SetDB(db)

	// 获取采购合同详情
	var contract model.PurchaseContract
	if err := db.First(&contract, input.ContractID).Error; err != nil {
		return fmt.Errorf("获取采购合同详情失败: %w", err)
	}

	// 获取申请人姓名
	requesterName := getUserRealName(db, contract.CreatedBy)

	// 获取项目名称
	projectName := "无"
	if contract.ProjectID != nil && *contract.ProjectID > 0 {
		// 查询项目表获取项目名称
		type Project struct {
			ID          uint   `gorm:"column:id"`
			ProjectName string `gorm:"column:project_name"`
		}
		var project Project
		if err := db.Table("projects").Select("id, project_name").Where("id = ?", *contract.ProjectID).First(&project).Error; err == nil {
			projectName = project.ProjectName
		} else {
			fmt.Printf("获取项目名称失败: ID=%d, 错误=%v\n", *contract.ProjectID, err)
		}
	}

	// 构建通用通知参数
	params := notifier.GeneralApprovalNotifyParams{
		FlowType:      notifier.FlowTypeContract, // 使用合同流程类型
		BusinessID:    input.ContractID,
		BusinessNo:    input.ContractNo,
		RequesterName: requesterName,
		BusinessType:  "采购合同",
		ProjectName:   projectName,
	}

	// 根据通知类型设置对应的事件类型和其他参数
	switch notifyType {
	case "submitted":
		params.EventType = notifier.EventTypeSubmitted
		params.ToStage = toStage

	case "stage_changed":
		params.EventType = notifier.EventTypeStageChange
		params.ToStage = toStage

	case "approved":
		params.EventType = notifier.EventTypeApproved
		params.Comments = comments

	case "rejected":
		params.EventType = notifier.EventTypeRejected
		params.Comments = comments

	case "rollback":
		params.EventType = notifier.EventTypeRollback
		// 转换阶段名称为友好显示名称
		fromDisplayName := getStageDisplayName(fromStage)
		toDisplayName := getStageDisplayName(toStage)

		// 如果传入了显示名称，优先使用传入的
		if toStageDisplay != "" {
			toDisplayName = toStageDisplay
		}

		params.FromStage = fromDisplayName
		params.ToStage = toDisplayName
		params.Comments = comments

	default:
		return fmt.Errorf("未知的合同通知类型: %s", notifyType)
	}

	// 发送通用通知
	return purchaseNotifier.SendGeneralApprovalNotification(params)
}

// getStageDisplayName 获取阶段的友好显示名称
func getStageDisplayName(stage string) string {
	switch stage {
	case "purchase_review":
		return "采购负责人审批"
	case "finance_review":
		return "财务负责人审批"
	case "legal_review":
		return "法务负责人审批"
	case "enterprise_review":
		return "企业负责人审批"
	case "internal_sign":
		return "公司内部签章"
	case "double_sign":
		return "双方签章"
	case "completed":
		return "完成"
	default:
		return stage
	}
}

// GetUserRealNameActivity 获取用户真实姓名的活动
func GetUserRealNameActivity(ctx context.Context, userID uint) (string, error) {
	// 连接数据库
	db, err := connectDB()
	if err != nil {
		return fmt.Sprintf("用户_%d", userID), fmt.Errorf("连接数据库失败: %w", err)
	}

	// 调用内部函数获取用户真实姓名
	return getUserRealName(db, userID), nil
}

// 连接数据库
func connectDB() (*gorm.DB, error) {
	// 使用GORM获取数据库连接
	db := database.GetDB()
	if db == nil {
		// 如果获取不到全局DB，尝试初始化
		cfg, err := configs.LoadConfig()
		if err != nil {
			return nil, fmt.Errorf("加载配置失败: %w", err)
		}

		db = database.InitDB(cfg)
		if db == nil {
			return nil, fmt.Errorf("初始化数据库失败")
		}
	}

	return db, nil
}

// getUserRealName 获取用户真实姓名
func getUserRealName(db *gorm.DB, userID uint) string {
	// 使用共享的工具函数
	return utils.GetUserRealName(db, userID)
}

// 获取飞书通知器实例
func getFeishuNotifier() (*notifier.FeishuNotifier, error) {
	// 加载配置
	cfg, err := configs.LoadConfig()
	if err != nil {
		return nil, fmt.Errorf("加载配置失败: %w", err)
	}

	// 获取日志实例
	logger, err := zap.NewProduction()
	if err != nil {
		return nil, fmt.Errorf("创建日志实例失败: %w", err)
	}

	// 创建飞书通知器 - 使用配置项
	feishuNotifier := notifier.NewFeishuNotifier(
		cfg.Feishu.WebhookURL,
		cfg.Feishu.Secret,
		cfg.Feishu.TicketDetailUrlTemplate,
		cfg.Feishu.RepairTicketDetailUrlTemplate,
		nil, // 采购模块不使用项目webhook映射
		logger,
	)

	return feishuNotifier, nil
}
