package shipment

import (
	"backend/internal/infrastructure/database"
	"context"
	"fmt"
	"gorm.io/gorm"
	"log"
	"strings"
	"time"

	"backend/configs"
	"backend/internal/common/utils/notifier"
	"backend/internal/modules/purchase/constants"
	"backend/internal/modules/purchase/model"
	"backend/internal/modules/purchase/repository"
	"backend/internal/modules/purchase/workflow"
	userService "backend/internal/modules/user/service"
	"go.uber.org/zap"
)

// ShipmentActivities 发货管理工作流活动
type ShipmentActivities struct {
	shipmentRepo repository.ShipmentRepository
	contractRepo repository.PurchaseContractRepository
	userService  userService.IUserService
}

// NewShipmentActivities 创建发货管理工作流活动
func NewShipmentActivities(
	shipmentRepo repository.ShipmentRepository,
	contractRepo repository.PurchaseContractRepository,
	userService userService.IUserService,
) *ShipmentActivities {
	return &ShipmentActivities{
		shipmentRepo: shipmentRepo,
		contractRepo: contractRepo,
		userService:  userService,
	}
}

// UpdateShipmentStatusActivity 更新发货记录状态活动
func (a *ShipmentActivities) UpdateShipmentStatusActivity(ctx context.Context, input workflow.UpdateShipmentStatusInput) error {
	log.Printf("更新发货记录状态: ID=%d, 状态=%s, 操作人=%d", input.ShipmentID, input.Status, input.OperatorID)

	// 获取当前发货记录状态作为PreviousStatus
	currentShipment, err := a.shipmentRepo.GetByID(ctx, input.ShipmentID)
	if err != nil {
		log.Printf("获取发货记录当前状态失败: %v", err)
		return fmt.Errorf("获取发货记录当前状态失败: %w", err)
	}
	previousStatus := currentShipment.Status

	// 更新发货记录状态
	err = a.shipmentRepo.UpdateStatus(ctx, input.ShipmentID, input.Status, input.OperatorID)
	if err != nil {
		log.Printf("更新发货记录状态失败: %v", err)
		return fmt.Errorf("更新发货记录状态失败: %w", err)
	}

	// 记录状态历史
	operatorName := ""
	if a.userService != nil {
		if userInfo, err := a.userService.GetUserInfo(ctx, input.OperatorID); err == nil && userInfo != nil {
			operatorName = userInfo.RealName
		}
	}

	// 根据输入参数确定操作类型
	action := constants.ActionApprove
	if input.Action != "" {
		action = input.Action
	}

	now := time.Now()
	history := &model.PurchaseApprovalHistory{
		BusinessType:   constants.BusinessTypeShipment,
		BusinessID:     input.ShipmentID,
		PreviousStatus: previousStatus,
		NewStatus:      input.Status,
		Action:         action,
		OperatorID:     input.OperatorID,
		OperatorName:   operatorName,
		OperationTime:  now,
		Comments:       input.Comments,
		CreatedAt:      now,
	}

	if err := a.shipmentRepo.CreateStatusHistory(ctx, history); err != nil {
		log.Printf("记录发货记录状态历史失败: %v", err)
		// 不返回错误，因为这不是关键操作
	}

	log.Printf("发货记录状态更新成功: ID=%d, 新状态=%s", input.ShipmentID, input.Status)
	return nil
}

// SendShipmentNotificationActivity 发送发货通知活动
func (a *ShipmentActivities) SendShipmentNotificationActivity(ctx context.Context, input workflow.SendShipmentNotificationInput) error {
	// 获取发货记录信息
	shipment, err := a.shipmentRepo.GetByID(ctx, input.ShipmentID)
	if err != nil {
		log.Printf("获取发货记录失败: %v", err)
		return fmt.Errorf("获取发货记录失败: %w", err)
	}

	// 获取合同信息
	contract, err := a.contractRepo.GetByID(ctx, shipment.ContractID)
	if err != nil {
		log.Printf("获取合同信息失败: %v", err)
		return fmt.Errorf("获取合同信息失败: %w", err)
	}

	return a.sendFeishuNotification(ctx, shipment, contract, input)
}

// sendFeishuNotification 发送飞书通知
func (a *ShipmentActivities) sendFeishuNotification(ctx context.Context, shipment *model.Shipment, contract *model.PurchaseContract, input workflow.SendShipmentNotificationInput) error {
	// 获取飞书通知器实例
	baseNotifier, err := getFeishuNotifier()
	if err != nil {
		log.Printf("获取飞书通知器失败: %v", err)
		return fmt.Errorf("获取飞书通知器失败: %w", err)
	}

	// 加载配置
	cfg, err := loadConfig()
	if err != nil {
		log.Printf("加载配置失败: %v", err)
		return fmt.Errorf("加载配置失败: %w", err)
	}

	// 创建采购专用通知器
	purchaseNotifier := notifier.NewPurchaseFeishuNotifier(
		baseNotifier,
		cfg.Feishu.PurchaseWebhookURL,
		cfg.Feishu.PurchaseSecret,
		cfg.Feishu.PurchaseUrlTemplates,
		cfg.Feishu.PurchaseEnabled,
	)

	// 连接数据库获取详细信息
	db, err := connectDB()
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}

	// 获取申请人信息
	var requesterName string
	if shipment.CreatedBy != nil {
		requesterName = getUserRealName(db, *shipment.CreatedBy)
	} else {
		requesterName = "未知用户"
	}

	// 确定项目名称
	projectName := ""
	if contract != nil {
		projectName = contract.ProjectName
	}
	if projectName == "" {
		projectName = "test"
	}

	// 构建通知参数
	params := &notifier.GeneralApprovalNotifyParams{
		FlowType:      notifier.FlowTypeShipment, // 发货管理流程类型
		BusinessID:    shipment.ID,
		BusinessNo:    shipment.ShipmentNo,
		RequesterName: requesterName,
		ApproverName:  "", // 需要从用户服务获取
		BusinessType:  "发货管理",
		ProjectName:   projectName,
		Comments:      input.Comments,
	}

	// 根据操作类型设置事件类型和阶段信息
	switch input.Action {
	case constants.ActionSubmit:
		params.EventType = notifier.EventTypeSubmitted
		// 转换阶段名称为友好显示名称
		toDisplayName := cleanStageDisplayName(getShipmentStageDisplayName(input.Stage))
		params.ToStage = toDisplayName

	case constants.ActionApprove:
		if input.Stage == constants.ShipmentStageCompleted {
			// 最终完成，发送完成通知
			params.EventType = notifier.EventTypeCompleted
		} else {
			// 进入下一个审批阶段，发送待审批通知
			params.EventType = notifier.EventTypeSubmitted
			// 转换阶段名称为友好显示名称
			toDisplayName := cleanStageDisplayName(getShipmentStageDisplayName(input.Stage))
			params.ToStage = toDisplayName
		}
		params.Comments = input.Comments

	case constants.ActionReject:
		params.EventType = notifier.EventTypeRejected
		// 转换阶段名称为友好显示名称
		fromDisplayName := cleanStageDisplayName(getShipmentStageDisplayName(shipment.Status))
		params.FromStage = fromDisplayName
		params.Comments = input.Comments

	case constants.ActionRollback:
		params.EventType = notifier.EventTypeRollback
		// 转换阶段名称为友好显示名称
		fromDisplayName := cleanStageDisplayName(getShipmentStageDisplayName(shipment.Status))
		toDisplayName := cleanStageDisplayName(getShipmentStageDisplayName(input.Stage))

		params.FromStage = fromDisplayName
		params.ToStage = toDisplayName
		params.Comments = input.Comments

	default:
		return fmt.Errorf("未知的发货管理通知类型: %s", input.Action)
	}

	// 发送通用通知
	return purchaseNotifier.SendGeneralApprovalNotification(*params)
}

// getShipmentStageDisplayName 获取发货管理状态显示名称
func getShipmentStageDisplayName(stage string) string {
	return constants.GetShipmentStageDisplayName(stage)
}

// cleanStageDisplayName 清理阶段显示名称，移除"中"等后缀
func cleanStageDisplayName(displayName string) string {
	// 移除常见的后缀
	suffixes := []string{"中", "审批中", "处理中"}
	for _, suffix := range suffixes {
		if strings.HasSuffix(displayName, suffix) {
			return strings.TrimSuffix(displayName, suffix)
		}
	}
	return displayName
}

// getFeishuNotifier 获取飞书通知器实例
func getFeishuNotifier() (*notifier.FeishuNotifier, error) {
	// 加载配置
	cfg, err := configs.LoadConfig()
	if err != nil {
		return nil, fmt.Errorf("加载配置失败: %w", err)
	}

	// 获取日志实例
	logger, err := zap.NewProduction()
	if err != nil {
		return nil, fmt.Errorf("创建日志实例失败: %w", err)
	}

	// 创建飞书通知器 - 使用配置项
	feishuNotifier := notifier.NewFeishuNotifier(
		cfg.Feishu.WebhookURL,
		cfg.Feishu.Secret,
		cfg.Feishu.TicketDetailUrlTemplate,
		cfg.Feishu.RepairTicketDetailUrlTemplate,
		nil, // 采购模块不使用项目webhook映射
		logger,
	)

	return feishuNotifier, nil
}

// loadConfig 加载配置
func loadConfig() (*configs.Config, error) {
	return configs.LoadConfig()
}

// getUserRealName 获取用户真实姓名
func getUserRealName(db *gorm.DB, userID uint) string {
	if userID == 0 {
		return "系统"
	}

	var user struct {
		RealName string `json:"real_name"`
	}

	if err := db.Table("users").Select("real_name").Where("id = ?", userID).First(&user).Error; err != nil {
		return fmt.Sprintf("用户%d", userID)
	}

	if user.RealName == "" {
		return fmt.Sprintf("用户%d", userID)
	}

	return user.RealName
}

// connectDB 连接数据库
func connectDB() (*gorm.DB, error) {
	cfg, err := configs.LoadConfig()
	if err != nil {
		return nil, fmt.Errorf("加载配置失败: %w", err)
	}

	db := database.InitDB(cfg)
	return db, nil
}
