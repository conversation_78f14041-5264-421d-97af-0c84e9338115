package arrival

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"backend/configs"
	"backend/internal/common/utils/notifier"
	"backend/internal/infrastructure/database"
	"backend/internal/modules/purchase/constants"
	"backend/internal/modules/purchase/model"
	"backend/internal/modules/purchase/repository"
	"backend/internal/modules/purchase/workflow"
	userModel "backend/internal/modules/user/model"
	userService "backend/internal/modules/user/service"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ArrivalActivities 到货管理活动结构体
type ArrivalActivities struct {
	arrivalRepo  repository.ArrivalRepository
	contractRepo repository.PurchaseContractRepository
	userService  userService.IUserService
}

// NewArrivalActivities 创建到货管理活动实例
func NewArrivalActivities(
	arrivalRepo repository.ArrivalRepository,
	contractRepo repository.PurchaseContractRepository,
	userService userService.IUserService,
) *ArrivalActivities {
	return &ArrivalActivities{
		arrivalRepo:  arrivalRepo,
		contractRepo: contractRepo,
		userService:  userService,
	}
}

// UpdateArrivalStatusActivity 更新到货记录状态活动
func (a *ArrivalActivities) UpdateArrivalStatusActivity(ctx context.Context, input workflow.UpdateArrivalStatusInput) error {
	log.Printf("更新到货记录状态: ID=%d, 状态=%s, 操作人=%d", input.ArrivalID, input.Status, input.OperatorID)

	// 获取当前到货记录状态作为PreviousStatus
	currentArrival, err := a.arrivalRepo.GetByID(ctx, input.ArrivalID)
	if err != nil {
		log.Printf("获取到货记录当前状态失败: %v", err)
		return fmt.Errorf("获取到货记录当前状态失败: %w", err)
	}
	previousStatus := currentArrival.Status

	// 更新到货记录状态
	err = a.arrivalRepo.UpdateStatus(ctx, input.ArrivalID, input.Status, input.OperatorID)
	if err != nil {
		log.Printf("更新到货记录状态失败: %v", err)
		return fmt.Errorf("更新到货记录状态失败: %w", err)
	}

	// 记录状态历史
	operatorName := ""
	if a.userService != nil {
		if userInfo, err := a.userService.GetUserInfo(ctx, input.OperatorID); err == nil && userInfo != nil {
			operatorName = userInfo.RealName
		}
	}

	// 根据输入参数确定操作类型
	action := constants.ActionApprove
	if input.Action != "" {
		action = input.Action
	}

	now := time.Now()
	history := &model.PurchaseApprovalHistory{
		BusinessType:   constants.BusinessTypeArrival,
		BusinessID:     input.ArrivalID,
		PreviousStatus: previousStatus,
		NewStatus:      input.Status,
		Action:         action,
		OperatorID:     input.OperatorID,
		OperatorName:   operatorName,
		OperationTime:  now,
		Comments:       input.Comments,
		CreatedAt:      now,
	}

	if err := a.arrivalRepo.CreateStatusHistory(ctx, history); err != nil {
		log.Printf("记录到货记录状态历史失败: %v", err)
		// 不返回错误，因为这不是关键操作
	}

	log.Printf("到货记录状态更新成功: ID=%d, 新状态=%s", input.ArrivalID, input.Status)
	return nil
}

// SendArrivalNotificationActivity 发送到货通知活动
func (a *ArrivalActivities) SendArrivalNotificationActivity(ctx context.Context, input workflow.SendArrivalNotificationInput) error {

	// 获取到货记录信息
	arrival, err := a.arrivalRepo.GetByID(ctx, input.ArrivalID)
	if err != nil {
		log.Printf("获取到货记录失败: %v", err)
		return fmt.Errorf("获取到货记录失败: %w", err)
	}

	// 获取合同信息
	contract, err := a.contractRepo.GetByID(ctx, arrival.ContractID)
	if err != nil {
		log.Printf("获取合同信息失败: %v", err)
		return fmt.Errorf("获取合同信息失败: %w", err)
	}

	// 构建通知内容
	notificationContent := fmt.Sprintf(
		"到货通知单 %s 需要您的处理\n"+
			"合同编号: %s\n"+
			"到货总金额: %.2f\n"+
			"当前阶段: %s\n"+
			"操作: %s\n"+
			"备注: %s",
		arrival.ArrivalNo,
		contract.ContractNo,
		arrival.TotalAmount,
		input.Stage,
		input.Action,
		input.Comments,
	)

	// TODO: 这里可以集成实际的通知系统（如飞书、邮件等）
	log.Printf("发送通知内容: %s", notificationContent)
	log.Printf("通知接收人: %v", input.RecipientIDs)

	// 模拟发送通知
	for _, recipientID := range input.RecipientIDs {
		log.Printf("向用户 %d 发送到货通知", recipientID)
		// 这里可以调用实际的通知服务
	}

	log.Printf("到货通知发送完成: ID=%d", input.ArrivalID)
	return nil
}

// GetArrivalRequestTypeActivity 获取到货记录的采购申请类型活动
func (a *ArrivalActivities) GetArrivalRequestTypeActivity(ctx context.Context, input workflow.GetArrivalRequestTypeInput) (*workflow.GetArrivalRequestTypeOutput, error) {
	log.Printf("获取到货记录的采购申请类型: ID=%d", input.ArrivalID)

	requestType, err := a.arrivalRepo.GetRequestTypeByArrivalID(ctx, input.ArrivalID)
	if err != nil {
		log.Printf("获取采购申请类型失败: %v", err)
		return &workflow.GetArrivalRequestTypeOutput{
			RequestType: "other", // 默认为其他类型
			Error:       err.Error(),
		}, nil // 不返回错误，使用默认值
	}

	log.Printf("获取到采购申请类型: %s", requestType)
	return &workflow.GetArrivalRequestTypeOutput{
		RequestType: requestType,
		Error:       "",
	}, nil
}

// GetUserRealNameActivity 获取用户真实姓名活动
func (a *ArrivalActivities) GetUserRealNameActivity(ctx context.Context, userID uint) (string, error) {
	if a.userService == nil {
		return "", fmt.Errorf("用户服务未初始化")
	}

	userInfo, err := a.userService.GetUserInfo(ctx, userID)
	if err != nil {
		log.Printf("获取用户信息失败: userID=%d, error=%v", userID, err)
		return "", err
	}

	if userInfo == nil {
		return "", fmt.Errorf("用户不存在: userID=%d", userID)
	}

	return userInfo.RealName, nil
}

// SendFeishuArrivalNotificationActivity 发送飞书到货通知活动
func (a *ArrivalActivities) SendFeishuArrivalNotificationActivity(ctx context.Context, input workflow.SendArrivalNotificationInput) error {
	log.Printf("发送飞书到货通知: ID=%d, 阶段=%s", input.ArrivalID, input.Stage)

	// 连接数据库
	db, err := connectDB()
	if err != nil {
		log.Printf("连接数据库失败: %v", err)
		return fmt.Errorf("连接数据库失败: %w", err)
	}

	// 获取配置
	cfg, err := configs.LoadConfig()
	if err != nil {
		return fmt.Errorf("加载配置失败: %w", err)
	}

	// 获取飞书通知器
	baseNotifier, err := getFeishuNotifier()
	if err != nil {
		return fmt.Errorf("获取飞书通知器失败: %w", err)
	}

	// 创建采购专用通知器
	purchaseNotifier := notifier.NewPurchaseFeishuNotifier(
		baseNotifier,
		cfg.Feishu.PurchaseWebhookURL,
		cfg.Feishu.PurchaseSecret,
		cfg.Feishu.PurchaseUrlTemplates,
		cfg.Feishu.PurchaseEnabled,
	)

	// 设置数据库连接，用于查询审批历史
	purchaseNotifier.SetDB(db)

	// 获取到货记录详情
	var arrival model.Arrival
	if err := db.Preload("Contract").First(&arrival, input.ArrivalID).Error; err != nil {
		return fmt.Errorf("获取到货记录详情失败: %w", err)
	}

	// 获取申请人信息
	var requesterName string
	if arrival.CreatedBy != nil {
		requesterName = getUserRealName(db, *arrival.CreatedBy)
	} else {
		requesterName = "未知用户"
	}

	// 确定业务类型
	businessType := "到货管理"

	// 确定项目名称
	projectName := ""
	if arrival.Contract != nil {
		projectName = arrival.Contract.ProjectName
	}
	if projectName == "" {
		projectName = "test"
	}

	// 构建通知参数
	params := notifier.GeneralApprovalNotifyParams{
		FlowType:      "arrival", // 使用字符串而不是常量
		BusinessID:    input.ArrivalID,
		BusinessNo:    arrival.ArrivalNo,
		RequesterName: requesterName,
		ApproverName:  "", // SendArrivalNotificationInput 中没有 OperatorName 字段
		BusinessType:  businessType,
		ProjectName:   projectName,
		Comments:      input.Comments,
		FromStage:     "", // SendArrivalNotificationInput 中没有 FromStage 字段
		ToStage:       input.Stage,
		CustomFields: map[string]string{
			"amount":      fmt.Sprintf("%.2f", arrival.TotalAmount),
			"contract_no": "",
		},
	}

	// 如果有合同信息，添加合同编号
	if arrival.Contract != nil {
		params.CustomFields["contract_no"] = arrival.Contract.ContractNo
	}

	// 根据通知类型设置事件类型和参数
	switch input.Action {
	case constants.ActionSubmit:
		params.EventType = notifier.EventTypeSubmitted
		// 转换阶段名称为友好显示名称，去掉"审批中"等后缀
		toDisplayName := cleanStageDisplayName(getArrivalStageDisplayName(input.Stage))
		params.ToStage = toDisplayName

	case constants.ActionApprove:
		// 判断是否为流程完成
		if input.Stage == constants.ArrivalStageCompleted {
			params.EventType = notifier.EventTypeApproved
			// 流程完成，不需要设置ToStage
			params.FromStage = cleanStageDisplayName(getArrivalStageDisplayName(arrival.Status))
			params.Comments = input.Comments
		} else {
			// 进入下一阶段，发送待审批通知
			params.EventType = notifier.EventTypeSubmitted
			toDisplayName := cleanStageDisplayName(getArrivalStageDisplayName(input.Stage))
			params.ToStage = toDisplayName
			params.Comments = input.Comments
		}

	case constants.ActionReject:
		params.EventType = notifier.EventTypeRejected
		// 转换阶段名称为友好显示名称
		fromDisplayName := cleanStageDisplayName(getArrivalStageDisplayName(arrival.Status))
		params.FromStage = fromDisplayName
		params.Comments = input.Comments

	case constants.ActionRollback:
		params.EventType = notifier.EventTypeRollback
		// 转换阶段名称为友好显示名称
		fromDisplayName := cleanStageDisplayName(getArrivalStageDisplayName(arrival.Status))
		toDisplayName := cleanStageDisplayName(getArrivalStageDisplayName(input.Stage))

		params.FromStage = fromDisplayName
		params.ToStage = toDisplayName
		params.Comments = input.Comments

	default:
		return fmt.Errorf("未知的到货管理通知类型: %s", input.Action)
	}

	// 发送通用通知
	return purchaseNotifier.SendGeneralApprovalNotification(params)
}

// getFeishuNotifier 获取飞书通知器
func getFeishuNotifier() (*notifier.FeishuNotifier, error) {
	cfg, err := configs.LoadConfig()
	if err != nil {
		return nil, fmt.Errorf("加载配置失败: %w", err)
	}

	// 创建日志实例
	logger, err := zap.NewProduction()
	if err != nil {
		return nil, fmt.Errorf("创建日志实例失败: %w", err)
	}

	return notifier.NewFeishuNotifier(
		cfg.Feishu.WebhookURL,
		cfg.Feishu.Secret,
		cfg.Feishu.TicketDetailUrlTemplate,
		cfg.Feishu.RepairTicketDetailUrlTemplate,
		nil, // 采购模块不使用项目webhook映射
		logger,
	), nil
}

// connectDB 连接数据库
func connectDB() (*gorm.DB, error) {
	cfg, err := configs.LoadConfig()
	if err != nil {
		return nil, fmt.Errorf("加载配置失败: %w", err)
	}

	db := database.InitDB(cfg)
	return db, nil
}

// getUserRealName 获取用户真实姓名
func getUserRealName(db *gorm.DB, userID uint) string {
	var user userModel.User
	if err := db.First(&user, userID).Error; err != nil {
		log.Printf("获取用户信息失败: userID=%d, error=%v", userID, err)
		return ""
	}
	return user.RealName
}

// getArrivalStageDisplayName 获取到货管理阶段显示名称
func getArrivalStageDisplayName(stage string) string {
	return constants.GetArrivalStageDisplayName(stage)
}

// cleanStageDisplayName 清理阶段显示名称，去掉"审批中"等后缀
func cleanStageDisplayName(displayName string) string {
	// 去掉常见的后缀
	suffixes := []string{"审批中", "审批", "中"}

	for _, suffix := range suffixes {
		displayName = strings.TrimSuffix(displayName, suffix)
	}

	return strings.TrimSpace(displayName)
}
