package payment

import (
	"backend/internal/modules/purchase/constants"
	"backend/internal/modules/purchase/model"
	"backend/internal/common/utils/notifier"
	"context"
	"fmt"
	"time"

	"backend/configs"
	"backend/internal/infrastructure/database"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// connectDB 连接数据库
func connectDB() (*gorm.DB, error) {
	// 使用GORM获取数据库连接
	db := database.GetDB()
	if db == nil {
		// 如果获取不到全局DB，尝试初始化
		cfg, err := configs.LoadConfig()
		if err != nil {
			return nil, fmt.Errorf("加载配置失败: %w", err)
		}

		db = database.InitDB(cfg)
		if db == nil {
			return nil, fmt.Errorf("初始化数据库失败")
		}
	}

	return db, nil
}

// getUserRealName 获取用户真实姓名
func getUserRealName(db *gorm.DB, userID uint) string {
	if userID == 0 {
		return "系统"
	}

	var user struct {
		RealName string `json:"real_name"`
	}

	if err := db.Table("users").Select("real_name").Where("id = ?", userID).First(&user).Error; err != nil {
		return fmt.Sprintf("用户%d", userID)
	}

	if user.RealName == "" {
		return fmt.Sprintf("用户%d", userID)
	}

	return user.RealName
}

// getFeishuNotifier 获取飞书通知器
func getFeishuNotifier() (*notifier.FeishuNotifier, error) {
	cfg, err := configs.LoadConfig()
	if err != nil {
		return nil, fmt.Errorf("加载配置失败: %w", err)
	}

	// 创建日志实例
	logger, err := zap.NewProduction()
	if err != nil {
		return nil, fmt.Errorf("创建日志实例失败: %w", err)
	}

	return notifier.NewFeishuNotifier(
		cfg.Feishu.WebhookURL,
		cfg.Feishu.Secret,
		cfg.Feishu.TicketDetailUrlTemplate,
		cfg.Feishu.RepairTicketDetailUrlTemplate,
		nil, // 采购模块不使用项目webhook映射
		logger,
	), nil
}

// UpdatePaymentStatusActivity 更新付款申请状态活动
func UpdatePaymentStatusActivity(ctx context.Context, paymentID uint, status string, updatedBy uint, operatorName, comments, action string) error {
	// 连接数据库
	db, err := connectDB()
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}

	// 获取当前付款申请
	var paymentRequest model.PaymentRequest
	if err := db.First(&paymentRequest, paymentID).Error; err != nil {
		return fmt.Errorf("获取付款申请失败: %w", err)
	}

	// 如果状态没有变化，不需要更新
	if paymentRequest.Status == status {
		return nil
	}

	// 记录旧状态
	oldStatus := paymentRequest.Status

	// 更新状态 - 明确指定要更新的字段
	if err := db.Model(&paymentRequest).Select("status", "updated_by", "updated_at").Updates(map[string]interface{}{
		"status":     status,
		"updated_by": updatedBy,
		"updated_at": time.Now(),
	}).Error; err != nil {
		return fmt.Errorf("更新付款申请状态失败: %w", err)
	}

	// 如果没有传递操作人姓名，则查询获取
	if operatorName == "" {
		operatorName = getUserRealName(db, updatedBy)
	}

	// 如果没有传递action，根据状态推断
	if action == "" {
		switch status {
		case constants.PaymentStagePurchaseSupplyChainReview:
			action = constants.ActionSubmit
		case constants.PaymentStageFinanceManagerReview, constants.PaymentStageEnterpriseManagerReview,
			constants.PaymentStageCyberReview, constants.PaymentStageOtherEntityReview,
			constants.PaymentStageFundManagerReview, constants.PaymentStageCyberHandler,
			constants.PaymentStageOtherEntityHandler, constants.PaymentStageCyberReview2,
			constants.PaymentStageFinalReview, constants.PaymentStageCompleted:
			action = constants.ActionApprove
		case constants.PaymentStageRejected:
			action = constants.ActionReject
		default:
			action = constants.ActionUpdate
		}
	}

	// 记录状态变更历史
	now := time.Now()
	history := model.PurchaseApprovalHistory{
		BusinessType:   constants.BusinessTypePaymentRequest,
		BusinessID:     paymentID,
		PreviousStatus: oldStatus,
		NewStatus:      status,
		Action:         action,
		OperatorID:     updatedBy,
		OperatorName:   operatorName,
		OperationTime:  now,
		Comments:       comments,
		CreatedAt:      now,
	}

	if err := db.Create(&history).Error; err != nil {
		return fmt.Errorf("记录付款申请状态历史失败: %w", err)
	}

	return nil
}

// PaymentRequestWorkflowInput 付款申请工作流输入
type PaymentRequestWorkflowInput struct {
	PaymentID     uint    `json:"payment_id"`
	PaymentNo     string  `json:"payment_no"`
	RequesterID   uint    `json:"requester_id"`
	ContractID    uint    `json:"contract_id"`
	TotalAmount   float64 `json:"total_amount"`
	CompanyID     uint    `json:"company_id"`
	CompanyName   string  `json:"company_name"`
	ProjectName   string  `json:"project_name"`
	BusinessType  string  `json:"business_type"`
}

// SendFeishuPaymentNotificationActivity 发送飞书付款申请通知活动
func SendFeishuPaymentNotificationActivity(ctx context.Context, input PaymentRequestWorkflowInput, notifyType string, toStage string, operatorName string, comments string, fromStage string, toStageDisplay string) error {
	// 连接数据库
	db, err := connectDB()
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}

	// 获取配置
	cfg, err := configs.LoadConfig()
	if err != nil {
		return fmt.Errorf("加载配置失败: %w", err)
	}

	// 获取飞书通知器
	baseNotifier, err := getFeishuNotifier()
	if err != nil {
		return fmt.Errorf("获取飞书通知器失败: %w", err)
	}

	// 创建采购专用通知器
	purchaseNotifier := notifier.NewPurchaseFeishuNotifier(
		baseNotifier,
		cfg.Feishu.PurchaseWebhookURL,
		cfg.Feishu.PurchaseSecret,
		cfg.Feishu.PurchaseUrlTemplates,
		cfg.Feishu.PurchaseEnabled,
	)

	// 设置数据库连接，用于查询审批历史
	purchaseNotifier.SetDB(db)

	// 获取付款申请详情
	var paymentRequest model.PaymentRequest
	if err := db.Preload("Company").Preload("Contract").First(&paymentRequest, input.PaymentID).Error; err != nil {
		return fmt.Errorf("获取付款申请详情失败: %w", err)
	}

	// 获取申请人信息
	requesterName := getUserRealName(db, input.RequesterID)

	// 确定业务类型
	businessType := input.BusinessType
	if businessType == "" {
		businessType = "付款申请"
	}

	// 确定项目名称
	projectName := input.ProjectName
	if projectName == "" && paymentRequest.Contract != nil {
		projectName = paymentRequest.Contract.ProjectName
	}
	if projectName == "" {
		projectName = "test"
	}

	// 构建通知参数
	params := notifier.GeneralApprovalNotifyParams{
		FlowType:      notifier.FlowTypePayment,
		BusinessID:    input.PaymentID,
		BusinessNo:    input.PaymentNo,
		RequesterName: requesterName,
		ApproverName:  operatorName,
		BusinessType:  businessType,
		ProjectName:   projectName,
		Comments:      comments,
		FromStage:     fromStage,
		ToStage:       toStage,
		CustomFields: map[string]string{
			"amount":      fmt.Sprintf("%.2f", input.TotalAmount),
			"company":     input.CompanyName,
			"contract_no": "",
		},
	}

	// 如果有合同信息，添加合同编号
	if paymentRequest.Contract != nil {
		params.CustomFields["contract_no"] = paymentRequest.Contract.ContractNo
	}

	// 根据通知类型设置事件类型和参数
	switch notifyType {
	case "submitted":
		params.EventType = notifier.EventTypeSubmitted
		// 转换阶段名称为友好显示名称
		toDisplayName := getPaymentStageDisplayName(toStage)
		params.ToStage = toDisplayName

	case "approved":
		params.EventType = notifier.EventTypeApproved
		// 转换阶段名称为友好显示名称
		fromDisplayName := getPaymentStageDisplayName(fromStage)
		toDisplayName := getPaymentStageDisplayName(toStage)

		// 如果传入了显示名称，优先使用传入的
		if toStageDisplay != "" {
			toDisplayName = toStageDisplay
		}

		params.FromStage = fromDisplayName
		params.ToStage = toDisplayName
		params.Comments = comments

	case "rejected":
		params.EventType = notifier.EventTypeRejected
		// 转换阶段名称为友好显示名称
		fromDisplayName := getPaymentStageDisplayName(fromStage)
		params.FromStage = fromDisplayName
		params.Comments = comments

	case "rollback":
		params.EventType = notifier.EventTypeRollback
		// 转换阶段名称为友好显示名称
		fromDisplayName := getPaymentStageDisplayName(fromStage)
		toDisplayName := getPaymentStageDisplayName(toStage)

		// 如果传入了显示名称，优先使用传入的
		if toStageDisplay != "" {
			toDisplayName = toStageDisplay
		}

		params.FromStage = fromDisplayName
		params.ToStage = toDisplayName
		params.Comments = comments

	default:
		return fmt.Errorf("未知的付款申请通知类型: %s", notifyType)
	}

	// 发送通用通知
	return purchaseNotifier.SendGeneralApprovalNotification(params)
}

// getPaymentStageDisplayName 获取付款申请阶段显示名称
func getPaymentStageDisplayName(stage string) string {
	return constants.GetPaymentStageDisplayName(stage)
}
