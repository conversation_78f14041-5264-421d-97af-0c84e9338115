package service

import (
	"backend/internal/common/utils"
	"backend/internal/modules/purchase/constants"
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/model"
	"backend/internal/modules/purchase/repository"
	"backend/internal/modules/purchase/workflow"
	userService "backend/internal/modules/user/service"
	pkgUtils "backend/pkg/utils"
	"context"
	"errors"
	"fmt"
	"time"

	"go.temporal.io/sdk/client"
)

var (
	// ErrPurchaseRequestNotFound 采购申请不存在错误
	ErrPurchaseRequestNotFound = errors.New("采购申请不存在")

	// ErrDuplicateRequestNo 申请单号重复错误
	ErrDuplicateRequestNo = errors.New("申请单号已存在")

	// ErrInvalidPurchaseRequestData 无效的采购申请数据
	ErrInvalidPurchaseRequestData = errors.New("无效的采购申请数据")

	// ErrInvalidPurchaseRequestStatus 无效的采购申请状态
	ErrInvalidPurchaseRequestStatus = errors.New("无效的采购申请状态")

	// ErrWorkflowStartFailed 工作流启动失败
	ErrWorkflowStartFailed = errors.New("审批工作流启动失败")
)

// 使用模型中定义的状态常量

// 定义工作流相关常量
const (
	WorkflowPurchaseRequestApproval = "PurchaseRequestApprovalWorkflow"
)

// PurchaseRequestService 采购申请服务接口
type PurchaseRequestService interface {
	// CreatePurchaseRequest 创建采购申请
	CreatePurchaseRequest(ctx context.Context, dto dto.CreatePurchaseRequestDTO) (*model.PurchaseRequest, error)

	// UpdatePurchaseRequest 更新采购申请
	UpdatePurchaseRequest(ctx context.Context, dto dto.UpdatePurchaseRequestDTO) (*model.PurchaseRequest, error)

	// GetPurchaseRequestByID 根据ID获取采购申请
	GetPurchaseRequestByID(ctx context.Context, id uint) (*model.PurchaseRequest, error)

	// GetPurchaseRequestByRequestNo 根据申请单号获取采购申请
	GetPurchaseRequestByRequestNo(ctx context.Context, requestNo string) (*model.PurchaseRequest, error)

	// ListPurchaseRequests 获取采购申请列表
	ListPurchaseRequests(ctx context.Context, query dto.PurchaseRequestListQuery) (*dto.PurchaseRequestListResult, error)

	// SubmitPurchaseRequest 提交采购申请进入审批流程
	SubmitPurchaseRequest(ctx context.Context, id uint, updatedBy uint) error

	// ApprovePurchaseRequest 审批采购申请
	ApprovePurchaseRequest(ctx context.Context, approvalDto dto.WorkflowApprovalDTO) error

	// GetPurchaseRequestHistory 获取采购申请历史记录
	GetPurchaseRequestHistory(ctx context.Context, requestID uint) ([]dto.PurchaseRequestHistoryDTO, error)

	// RollbackPurchaseRequest 回退采购申请
	RollbackPurchaseRequest(ctx context.Context, rollbackDto dto.WorkflowRollbackDTO) error

	// CancelPurchaseRequest 取消采购申请
	CancelPurchaseRequest(ctx context.Context, id uint, updatedBy uint) error

	// DeletePurchaseRequest 删除采购申请
	DeletePurchaseRequest(ctx context.Context, id uint, deletedBy uint) error

	// GenerateRequestNo 生成申请单号
	GenerateRequestNo(ctx context.Context) (string, error)
}

// purchaseRequestService 采购申请服务实现
type purchaseRequestService struct {
	repo           repository.PurchaseRequestRepository
	temporalClient client.Client
	userHelper     *UserHelper
	projectService ProjectService
}

// NewPurchaseRequestService 创建采购申请服务实例
func NewPurchaseRequestService(repo repository.PurchaseRequestRepository, temporalClient client.Client, userService userService.IUserService, projectService ProjectService) PurchaseRequestService {
	// 初始化编码生成器
	utils.InitCodeGenerator()
	userHelper := NewUserHelper(userService)
	return &purchaseRequestService{
		repo:           repo,
		temporalClient: temporalClient,
		userHelper:     userHelper,
		projectService: projectService,
	}
}

// GenerateRequestNo 生成申请单号
func (s *purchaseRequestService) GenerateRequestNo(ctx context.Context) (string, error) {
	// 定义申请单号检查函数
	codeExistChecker := func(ctx context.Context, code string) (bool, error) {
		request, err := s.repo.GetByRequestNo(ctx, code)
		if err != nil {
			return false, nil
		}
		return request != nil, nil
	}

	// 设置申请单号生成选项
	options := utils.CodeGeneratorOptions{
		Prefix:       "PR",
		DateFormat:   "20060102150405",
		RandomLength: 4,
		Delimiter:    "",
	}

	// 生成唯一编码
	return utils.GenerateUniqueCode(ctx, codeExistChecker, options)
}

// CreatePurchaseRequest 创建采购申请
func (s *purchaseRequestService) CreatePurchaseRequest(ctx context.Context, createDto dto.CreatePurchaseRequestDTO) (*model.PurchaseRequest, error) {
	// 生成申请单号
	requestNo, err := s.GenerateRequestNo(ctx)
	if err != nil {
		return nil, fmt.Errorf("生成申请单号失败: %w", err)
	}

	// 直接使用DTO中的CreatedBy（controller层已从JWT获取）
	userID := createDto.CreatedBy

	// 将RequestType数组转换为JSON字符串
	requestTypeJSON, err := pkgUtils.ToJSONString(createDto.RequestType)
	if err != nil {
		return nil, fmt.Errorf("转换申请类型失败: %w", err)
	}

	// 创建采购申请实体 - 直接进入项目经理审批
	request := &model.PurchaseRequest{
		RequestNo:            requestNo,
		ProjectID:            createDto.ProjectID,
		RequestType:          requestTypeJSON,
		UrgencyLevel:         createDto.UrgencyLevel,
		ExpectedDeliveryDate: createDto.ExpectedDeliveryDate,
		Reason:               createDto.Reason,
		ReceiveAddress:       createDto.ReceiveAddress,
		Receiver:             createDto.Receiver,
		ReceiverPhone:        createDto.ReceiverPhone,
		Status:               constants.StatusProjectManagerReview, // 直接进入项目经理审批
		CreatedBy:            userID,
		UpdatedBy:            userID,
	}

	// 处理明细项
	request.Items = make([]model.PurchaseRequestItem, len(createDto.Items))
	for i, itemDto := range createDto.Items {
		request.Items[i] = model.PurchaseRequestItem{
			ProductID:      itemDto.ProductID,
			MaterialType:   itemDto.MaterialType,
			Model:          itemDto.Model,
			Brand:          itemDto.Brand,
			PN:             itemDto.PN,
			Specifications: itemDto.Specifications,
			Unit:           itemDto.Unit,
			Quantity:       itemDto.Quantity,
			Remark:         itemDto.Remark,
		}
	}

	// 保存到数据库
	if err := s.repo.Create(ctx, request); err != nil {
		return nil, err
	}

	// 记录创建历史
	operatorName := s.userHelper.GetUserRealName(ctx, userID)
	history := &model.PurchaseApprovalHistory{
		BusinessType:   constants.BusinessTypePurchaseRequest,
		BusinessID:     request.ID,
		PreviousStatus: "",
		NewStatus:      constants.StatusProjectManagerReview,
		Action:         constants.ActionCreate,
		OperatorID:     userID,
		OperatorName:   operatorName,
		OperationTime:  time.Now(),
		Comments:       "创建采购申请",
		CreatedAt:      time.Now(),
	}

	// 创建历史记录
	if err := s.repo.CreateStatusHistory(ctx, history); err != nil {
		// 仅记录错误，不影响主流程
		fmt.Printf("记录采购申请状态历史失败: %v\n", err)
	}

	// 启动工作流
	workflowID := fmt.Sprintf("purchase-request-%d", request.ID)
	workflowOptions := client.StartWorkflowOptions{
		ID:        workflowID,
		TaskQueue: constants.TaskQueuePurchaseRequest,
	}

	// 准备工作流输入参数
	input := workflow.PurchaseRequestWorkflowInput{
		RequestID:    request.ID,
		RequestNo:    request.RequestNo,
		RequesterID:  request.CreatedBy,
		RequestType:  request.RequestType,
		UrgencyLevel: request.UrgencyLevel,
	}

	// 执行工作流
	_, err = s.temporalClient.ExecuteWorkflow(ctx, workflowOptions, workflow.PurchaseRequestApprovalWorkflow, input)
	if err != nil {
		// 记录错误，但不影响主流程返回
		fmt.Printf("启动采购申请工作流失败: %v, requestID: %d\n", err, request.ID)
	}

	return request, nil
}

// UpdatePurchaseRequest 更新采购申请
func (s *purchaseRequestService) UpdatePurchaseRequest(ctx context.Context, updateDto dto.UpdatePurchaseRequestDTO) (*model.PurchaseRequest, error) {
	// 检查采购申请是否存在
	request, err := s.repo.GetByID(ctx, updateDto.ID)
	if err != nil {
		return nil, ErrPurchaseRequestNotFound
	}

	// 只有草稿状态的申请才能更新
	if request.Status != constants.StatusDraft {
		return nil, ErrInvalidPurchaseRequestStatus
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		// 如果获取不到用户ID，使用默认值0或者DTO中的值
		if updateDto.UpdatedBy > 0 {
			userID = updateDto.UpdatedBy
		} else {
			userID = 0
		}
	}

	// 更新基本信息
	if updateDto.ProjectID != nil {
		request.ProjectID = updateDto.ProjectID
	}
	if len(updateDto.RequestType) > 0 {
		// 将RequestType数组转换为JSON字符串
		requestTypeJSON, err := pkgUtils.ToJSONString(updateDto.RequestType)
		if err != nil {
			return nil, fmt.Errorf("转换申请类型失败: %w", err)
		}
		request.RequestType = requestTypeJSON
	}
	if updateDto.UrgencyLevel != "" {
		request.UrgencyLevel = updateDto.UrgencyLevel
	}
	if updateDto.ExpectedDeliveryDate != nil {
		request.ExpectedDeliveryDate = updateDto.ExpectedDeliveryDate
	}
	if updateDto.Reason != "" {
		request.Reason = updateDto.Reason
	}
	if updateDto.ReceiveAddress != "" {
		request.ReceiveAddress = updateDto.ReceiveAddress
	}
	if updateDto.Receiver != "" {
		request.Receiver = updateDto.Receiver
	}
	if updateDto.ReceiverPhone != "" {
		request.ReceiverPhone = updateDto.ReceiverPhone
	}
	request.UpdatedBy = userID

	// 处理明细项
	if len(updateDto.Items) > 0 {
		request.Items = make([]model.PurchaseRequestItem, len(updateDto.Items))
		for i, itemDto := range updateDto.Items {
			request.Items[i] = model.PurchaseRequestItem{
				ID:             itemDto.ID,
				RequestID:      updateDto.ID,
				ProductID:      itemDto.ProductID,
				MaterialType:   itemDto.MaterialType,
				Model:          itemDto.Model,
				Brand:          itemDto.Brand,
				PN:             itemDto.PN,
				Specifications: itemDto.Specifications,
				Unit:           itemDto.Unit,
				Quantity:       itemDto.Quantity,
				Remark:         itemDto.Remark,
			}
		}
	}

	// 保存到数据库
	if err := s.repo.Update(ctx, request); err != nil {
		return nil, err
	}

	return request, nil
}

// GetPurchaseRequestByID 根据ID获取采购申请
func (s *purchaseRequestService) GetPurchaseRequestByID(ctx context.Context, id uint) (*model.PurchaseRequest, error) {
	request, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, ErrPurchaseRequestNotFound
	}

	// 填充项目名称
	if request.ProjectID != nil && *request.ProjectID > 0 {
		project, err := s.projectService.GetProjectByID(ctx, *request.ProjectID)
		if err == nil && project != nil {
			request.ProjectName = project.ProjectName
		}
	}

	// 填充创建人姓名
	if request.CreatedBy > 0 {
		request.CreatorName = s.userHelper.GetUserRealName(ctx, request.CreatedBy)
	}

	// 解析RequestType JSON字符串为数组
	if request.RequestType != "" {
		var requestTypes []string
		if err := pkgUtils.FromJSONString(request.RequestType, &requestTypes); err == nil {
			request.RequestTypeArray = requestTypes
		}
	}

	return request, nil
}

// GetPurchaseRequestByRequestNo 根据申请单号获取采购申请
func (s *purchaseRequestService) GetPurchaseRequestByRequestNo(ctx context.Context, requestNo string) (*model.PurchaseRequest, error) {
	request, err := s.repo.GetByRequestNo(ctx, requestNo)
	if err != nil {
		return nil, ErrPurchaseRequestNotFound
	}

	// 填充项目名称
	if request.ProjectID != nil && *request.ProjectID > 0 {
		project, err := s.projectService.GetProjectByID(ctx, *request.ProjectID)
		if err == nil && project != nil {
			request.ProjectName = project.ProjectName
		}
	}

	// 填充创建人姓名
	if request.CreatedBy > 0 {
		request.CreatorName = s.userHelper.GetUserRealName(ctx, request.CreatedBy)
	}

	// 解析RequestType JSON字符串为数组
	if request.RequestType != "" {
		var requestTypes []string
		if err := pkgUtils.FromJSONString(request.RequestType, &requestTypes); err == nil {
			request.RequestTypeArray = requestTypes
		}
	}

	return request, nil
}

// ListPurchaseRequests 获取采购申请列表
func (s *purchaseRequestService) ListPurchaseRequests(ctx context.Context, query dto.PurchaseRequestListQuery) (*dto.PurchaseRequestListResult, error) {
	requests, total, err := s.repo.List(ctx, query.PurchaseRequestFilter, query.PaginationOptions)
	if err != nil {
		return nil, err
	}

	// 填充项目名称和创建人姓名
	for i := range requests {
		// 填充项目名称
		if requests[i].ProjectID != nil && *requests[i].ProjectID > 0 {
			project, err := s.projectService.GetProjectByID(ctx, *requests[i].ProjectID)
			if err == nil && project != nil {
				requests[i].ProjectName = project.ProjectName
			}
		}

		// 填充创建人姓名
		if requests[i].CreatedBy > 0 {
			requests[i].CreatorName = s.userHelper.GetUserRealName(ctx, requests[i].CreatedBy)
		}

		// 解析RequestType JSON字符串为数组
		if requests[i].RequestType != "" {
			var requestTypes []string
			if err := pkgUtils.FromJSONString(requests[i].RequestType, &requestTypes); err == nil {
				requests[i].RequestTypeArray = requestTypes
			}
		}
	}

	return &dto.PurchaseRequestListResult{
		Total: total,
		List:  requests,
	}, nil
}

// SubmitPurchaseRequest 提交采购申请进入审批流程
func (s *purchaseRequestService) SubmitPurchaseRequest(ctx context.Context, id uint, updatedBy uint) error {
	// 检查采购申请是否存在
	request, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return ErrPurchaseRequestNotFound
	}

	// 只有草稿状态的申请才能提交
	if request.Status != constants.StatusDraft {
		return ErrInvalidPurchaseRequestStatus
	}

	// 直接使用传入的updatedBy（controller层已从JWT获取）
	userID := updatedBy

	// 获取操作人姓名
	operatorName := s.userHelper.GetUserRealName(ctx, userID)

	// 更新状态为项目经理审批中并记录历史
	err = s.repo.UpdateStatusWithHistory(
		ctx,
		id,
		constants.StatusProjectManagerReview,
		constants.ActionSubmit,
		userID,
		operatorName,
		"提交采购申请进入审批流程",
	)
	if err != nil {
		return err
	}

	// 启动工作流
	workflowID := fmt.Sprintf("purchase-request-%d", id)
	workflowOptions := client.StartWorkflowOptions{
		ID:        workflowID,
		TaskQueue: constants.TaskQueuePurchaseRequest,
	}

	// 准备工作流输入参数
	input := workflow.PurchaseRequestWorkflowInput{
		RequestID:    id,
		RequestNo:    request.RequestNo,
		RequesterID:  request.CreatedBy,
		RequestType:  request.RequestType,
		UrgencyLevel: request.UrgencyLevel,
	}

	// 执行工作流
	_, err = s.temporalClient.ExecuteWorkflow(ctx, workflowOptions, workflow.PurchaseRequestApprovalWorkflow, input)
	if err != nil {
		// 回滚状态
		_ = s.repo.UpdateStatusWithHistory(
			ctx,
			id,
			constants.StatusDraft,
			constants.ActionRollback,
			userID,
			operatorName,
			"启动工作流失败，回滚到草稿状态",
		)
		return ErrWorkflowStartFailed
	}

	return nil
}

// ApprovePurchaseRequest 审批采购申请
func (s *purchaseRequestService) ApprovePurchaseRequest(ctx context.Context, approvalDto dto.WorkflowApprovalDTO) error {
	// 检查采购申请是否存在
	request, err := s.repo.GetByID(ctx, approvalDto.RequestID)
	if err != nil {
		return ErrPurchaseRequestNotFound
	}

	// 只有审批中状态的申请才能审批
	if request.Status != constants.StatusProjectManagerReview && request.Status != constants.StatusPurchaseManagerReview {
		return ErrInvalidPurchaseRequestStatus
	}

	// 验证当前阶段与状态匹配 - 直接比较数据库状态值
	if request.Status != approvalDto.CurrentStage {
		return fmt.Errorf("当前阶段与审批状态不匹配")
	}

	// 从JWT获取用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		// 如果无法从JWT获取，审批操作必须有操作人，所以返回错误
		return fmt.Errorf("无法获取当前用户信息")
	}

	// 构建工作流信号
	approvalSignal := workflow.ApprovalSignal{
		ApproverID:   userID,
		Action:       approvalDto.Action,
		Comments:     approvalDto.Comments,
		CurrentStage: approvalDto.CurrentStage,
		Timestamp:    time.Now(),
	}

	// 发送审批信号到工作流
	workflowID := fmt.Sprintf("purchase-request-%d", approvalDto.RequestID)
	err = s.temporalClient.SignalWorkflow(ctx, workflowID, "", workflow.SignalNameApproval, approvalSignal)
	if err != nil {
		return fmt.Errorf("发送工作流信号失败: %w", err)
	}

	return nil
}

// GetPurchaseRequestHistory 获取采购申请历史记录
func (s *purchaseRequestService) GetPurchaseRequestHistory(ctx context.Context, requestID uint) ([]dto.PurchaseRequestHistoryDTO, error) {
	// 检查采购申请是否存在
	_, err := s.repo.GetByID(ctx, requestID)
	if err != nil {
		return nil, ErrPurchaseRequestNotFound
	}

	// 从数据库获取历史记录
	historyRecords, err := s.repo.GetHistory(ctx, requestID)
	if err != nil {
		return nil, err
	}

	// 转换为DTO
	historyDTOs := make([]dto.PurchaseRequestHistoryDTO, len(historyRecords))
	for i, record := range historyRecords {
		historyDTOs[i] = dto.PurchaseRequestHistoryDTO{
			ID:             record.ID,
			BusinessType:   record.BusinessType,
			BusinessID:     record.BusinessID,
			PreviousStatus: record.PreviousStatus,
			NewStatus:      record.NewStatus,
			Action:         record.Action,
			OperatorID:     record.OperatorID,
			OperatorName:   record.OperatorName,
			OperationTime:  record.OperationTime,
			Comments:       record.Comments,
			CreatedAt:      record.CreatedAt,
		}
	}

	return historyDTOs, nil
}

// RollbackPurchaseRequest 回退采购申请
func (s *purchaseRequestService) RollbackPurchaseRequest(ctx context.Context, rollbackDto dto.WorkflowRollbackDTO) error {
	// 检查采购申请是否存在
	request, err := s.repo.GetByID(ctx, rollbackDto.RequestID)
	if err != nil {
		return ErrPurchaseRequestNotFound
	}

	// 只有审批中状态的申请才能回退
	validStatuses := []string{constants.StatusPurchaseManagerReview, constants.StatusProjectManagerReview}
	canRollback := false
	for _, status := range validStatuses {
		if request.Status == status {
			canRollback = true
			break
		}
	}

	if !canRollback {
		return ErrInvalidPurchaseRequestStatus
	}

	// 直接使用DTO中的ApproverID（controller层已从JWT获取）
	userID := rollbackDto.ApproverID
	if err != nil {
		// 回退操作允许使用传入的审批人ID作为备选
		userID = rollbackDto.ApproverID
	}

	// 转换数据库状态到工作流阶段名称
	convertToWorkflowStage := func(dbStatus string) string {
		switch dbStatus {
		case constants.StatusProjectManagerReview:
			return workflow.StageProjectManagerApproval
		case constants.StatusPurchaseManagerReview:
			return workflow.StagePurchasingManagerApproval
		case constants.StatusDraft:
			return constants.StatusDraft
		default:
			return dbStatus
		}
	}

	// 转换当前阶段和回退目标阶段
	workflowCurrentStage := convertToWorkflowStage(rollbackDto.CurrentStage)
	workflowRollbackTo := convertToWorkflowStage(rollbackDto.RollbackTo)

	// 构建回退信号
	rollbackSignal := workflow.RollbackSignal{
		ApproverID:   userID,
		RollbackTo:   workflowRollbackTo,
		CurrentStage: workflowCurrentStage,
		Comments:     rollbackDto.Comments,
		Timestamp:    time.Now(),
	}

	// 发送回退信号到工作流
	workflowID := fmt.Sprintf("purchase-request-%d", rollbackDto.RequestID)
	err = s.temporalClient.SignalWorkflow(ctx, workflowID, "", workflow.SignalNameRollback, rollbackSignal)
	if err != nil {
		return fmt.Errorf("发送工作流回退信号失败: %w", err)
	}

	return nil
}

// CancelPurchaseRequest 取消采购申请
func (s *purchaseRequestService) CancelPurchaseRequest(ctx context.Context, id uint, updatedBy uint) error {
	// 检查采购申请是否存在
	request, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return ErrPurchaseRequestNotFound
	}

	// 只有草稿和审批中状态的申请才能取消
	validStatuses := []string{constants.StatusDraft, constants.StatusProjectManagerReview, constants.StatusPurchaseManagerReview}
	canCancel := false
	for _, status := range validStatuses {
		if request.Status == status {
			canCancel = true
			break
		}
	}

	if !canCancel {
		return ErrInvalidPurchaseRequestStatus
	}

	// 优先从JWT获取用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		// 如果JWT中获取失败，再使用传入的值
		userID = updatedBy
	}

	// 获取操作人姓名
	operatorName := s.userHelper.GetUserRealName(ctx, userID)

	// 更新状态为已取消并记录历史
	return s.repo.UpdateStatusWithHistory(
		ctx,
		id,
		constants.StatusCancelled,
		constants.ActionCancel,
		userID,
		operatorName,
		"取消采购申请",
	)
}

// DeletePurchaseRequest 删除采购申请
func (s *purchaseRequestService) DeletePurchaseRequest(ctx context.Context, id uint, deletedBy uint) error {
	// 检查采购申请是否存在
	request, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return ErrPurchaseRequestNotFound
	}

	// 只有草稿和已取消状态的申请才能删除
	if request.Status != constants.StatusDraft && request.Status != constants.StatusCancelled {
		return ErrInvalidPurchaseRequestStatus
	}

	// 删除采购申请
	return s.repo.Delete(ctx, id, deletedBy)
}
