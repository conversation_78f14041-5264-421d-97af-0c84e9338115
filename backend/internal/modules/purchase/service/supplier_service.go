package service

import (
	"backend/internal/common/utils"
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/model"
	"backend/internal/modules/purchase/repository"
	userModel "backend/internal/modules/user/model"
	"context"
	"errors"
	"fmt"
)

var (
	// ErrSupplierNotFound 供应商不存在错误
	ErrSupplierNotFound = errors.New("供应商不存在")

	// ErrDuplicateSupplierCode 供应商编码重复错误
	ErrDuplicateSupplierCode = errors.New("供应商编码已存在")

	// ErrInvalidSupplierData 无效的供应商数据
	ErrInvalidSupplierData = errors.New("无效的供应商数据")
)

// SupplierService 供应商信息服务接口
type SupplierService interface {
	// CreateSupplier 创建供应商信息
	CreateSupplier(ctx context.Context, dto dto.CreateSupplierDTO) (*model.Supplier, error)

	// UpdateSupplier 更新供应商信息
	UpdateSupplier(ctx context.Context, dto dto.UpdateSupplierDTO) (*model.Supplier, error)

	// GetSupplierByID 根据ID获取供应商信息
	GetSupplierByID(ctx context.Context, id uint) (*model.Supplier, error)

	// GetSupplierByCode 根据供应商编码获取供应商信息
	GetSupplierByCode(ctx context.Context, code string) (*model.Supplier, error)

	// ListSuppliers 获取供应商信息列表
	ListSuppliers(ctx context.Context, query dto.SupplierListQuery) (*dto.SupplierListResult, error)

	// DeleteSupplier 删除供应商信息
	DeleteSupplier(ctx context.Context, id uint) error

	// GenerateSupplierCode 生成供应商编码
	GenerateSupplierCode(ctx context.Context) (string, error)
}

// supplierService 供应商信息服务实现
type supplierService struct {
	repo repository.SupplierRepository
}

// NewSupplierService 创建供应商信息服务实例
func NewSupplierService(repo repository.SupplierRepository) SupplierService {
	// 初始化编码生成器
	utils.InitCodeGenerator()
	return &supplierService{repo: repo}
}

// GenerateSupplierCode 生成供应商编码
func (s *supplierService) GenerateSupplierCode(ctx context.Context) (string, error) {
	// 定义供应商编码检查函数
	codeExistChecker := func(ctx context.Context, code string) (bool, error) {
		supplier, err := s.repo.GetBySupplierCode(ctx, code)
		if err != nil {
			return false, nil
		}
		return supplier != nil, nil
	}

	// 设置供应商编码生成选项
	options := utils.CodeGeneratorOptions{
		Prefix:       "S",
		DateFormat:   "20060102",
		RandomLength: 4,
		Delimiter:    "-",
	}

	// 生成唯一编码
	return utils.GenerateUniqueCode(ctx, codeExistChecker, options)
}

// CreateSupplier 创建供应商信息
func (s *supplierService) CreateSupplier(ctx context.Context, createDto dto.CreateSupplierDTO) (*model.Supplier, error) {
	var supplierCode string
	var err error

	// 如果提供了供应商编码，检查是否已存在
	if createDto.SupplierCode != "" {
		supplierCode = createDto.SupplierCode
		existingSupplier, err := s.repo.GetBySupplierCode(ctx, supplierCode)
		if err == nil && existingSupplier != nil {
			return nil, ErrDuplicateSupplierCode
		}
	} else {
		// 如果未提供供应商编码，自动生成
		supplierCode, err = s.GenerateSupplierCode(ctx)
		if err != nil {
			return nil, fmt.Errorf("生成供应商编码失败: %w", err)
		}
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		// 如果获取不到用户ID，使用默认值0或者DTO中的值
		if createDto.CreatedBy > 0 {
			userID = createDto.CreatedBy
		} else {
			userID = 0
		}
	}

	// 创建供应商实体
	supplier := &model.Supplier{
		SupplierCode:      supplierCode,
		SupplierName:      createDto.SupplierName,
		ShortName:         createDto.ShortName,
		ContactPerson:     createDto.ContactPerson,
		ContactPhone:      createDto.ContactPhone,
		ContactEmail:      createDto.ContactEmail,
		Address:           createDto.Address,
		TaxNumber:         createDto.TaxNumber,
		BankName:          createDto.BankName,
		BankAccount:       createDto.BankAccount,
		BusinessLicense:   createDto.BusinessLicense,
		Categories:        userModel.StringArray(createDto.Categories),
		ContractStartDate: createDto.ContractStartDate,
		ContractEndDate:   createDto.ContractEndDate,
		CreditLevel:       createDto.CreditLevel,
		Status:            createDto.Status,
		Remark:            createDto.Remark,
		CreatedBy:         userID,
	}

	// 保存到数据库
	if err := s.repo.Create(ctx, supplier); err != nil {
		return nil, err
	}

	return supplier, nil
}

// UpdateSupplier 更新供应商信息
func (s *supplierService) UpdateSupplier(ctx context.Context, updateDto dto.UpdateSupplierDTO) (*model.Supplier, error) {
	// 检查供应商是否存在
	supplier, err := s.repo.GetByID(ctx, updateDto.ID)
	if err != nil {
		return nil, ErrSupplierNotFound
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		// 如果获取不到用户ID，使用默认值0或者DTO中的值
		if updateDto.UpdatedBy > 0 {
			userID = updateDto.UpdatedBy
		} else {
			userID = 0
		}
	}

	// 更新供应商信息
	if updateDto.SupplierName != "" {
		supplier.SupplierName = updateDto.SupplierName
	}
	if updateDto.ShortName != "" {
		supplier.ShortName = updateDto.ShortName
	}
	if updateDto.ContactPerson != "" {
		supplier.ContactPerson = updateDto.ContactPerson
	}
	if updateDto.ContactPhone != "" {
		supplier.ContactPhone = updateDto.ContactPhone
	}
	if updateDto.ContactEmail != "" {
		supplier.ContactEmail = updateDto.ContactEmail
	}
	if updateDto.Address != "" {
		supplier.Address = updateDto.Address
	}
	if updateDto.TaxNumber != "" {
		supplier.TaxNumber = updateDto.TaxNumber
	}
	if updateDto.BankName != "" {
		supplier.BankName = updateDto.BankName
	}
	if updateDto.BankAccount != "" {
		supplier.BankAccount = updateDto.BankAccount
	}
	if updateDto.BusinessLicense != "" {
		supplier.BusinessLicense = updateDto.BusinessLicense
	}
	if len(updateDto.Categories) > 0 {
		supplier.Categories = userModel.StringArray(updateDto.Categories)
	}
	if updateDto.ContractStartDate != nil {
		supplier.ContractStartDate = updateDto.ContractStartDate
	}
	if updateDto.ContractEndDate != nil {
		supplier.ContractEndDate = updateDto.ContractEndDate
	}
	if updateDto.CreditLevel != 0 {
		supplier.CreditLevel = updateDto.CreditLevel
	}
	if updateDto.Status != nil {
		supplier.Status = *updateDto.Status
	}
	if updateDto.Remark != "" {
		supplier.Remark = updateDto.Remark
	}
	supplier.UpdatedBy = userID

	// 保存到数据库
	if err := s.repo.Update(ctx, supplier); err != nil {
		return nil, err
	}

	return supplier, nil
}

// GetSupplierByID 根据ID获取供应商信息
func (s *supplierService) GetSupplierByID(ctx context.Context, id uint) (*model.Supplier, error) {
	supplier, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, ErrSupplierNotFound
	}
	return supplier, nil
}

// GetSupplierByCode 根据供应商编码获取供应商信息
func (s *supplierService) GetSupplierByCode(ctx context.Context, code string) (*model.Supplier, error) {
	supplier, err := s.repo.GetBySupplierCode(ctx, code)
	if err != nil {
		return nil, ErrSupplierNotFound
	}
	return supplier, nil
}

// ListSuppliers 获取供应商信息列表
func (s *supplierService) ListSuppliers(ctx context.Context, query dto.SupplierListQuery) (*dto.SupplierListResult, error) {
	suppliers, total, err := s.repo.List(ctx, query.SupplierFilter, query.PaginationOptions)
	if err != nil {
		return nil, err
	}

	return &dto.SupplierListResult{
		Total: total,
		List:  suppliers,
	}, nil
}

// DeleteSupplier 删除供应商信息
func (s *supplierService) DeleteSupplier(ctx context.Context, id uint) error {
	// 检查供应商是否存在
	_, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return ErrSupplierNotFound
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)

	if err != nil {
		// 如果获取不到用户ID，使用默认值0
		userID = 0
	}

	// 删除供应商
	return s.repo.Delete(ctx, id, userID)
}
