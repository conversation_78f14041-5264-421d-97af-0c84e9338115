package service

import (
	"backend/internal/common/utils"
	"backend/internal/modules/purchase/constants"
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/model"
	"backend/internal/modules/purchase/repository"
	"backend/internal/modules/purchase/workflow"
	inquiryActivity "backend/internal/modules/purchase/workflow/activity/inquiry"
	userService "backend/internal/modules/user/service"
	"context"
	"errors"
	"fmt"
	"time"

	"go.temporal.io/sdk/client"
)

var (
	// ErrPurchaseInquiryNotFound 采购询价不存在错误
	ErrPurchaseInquiryNotFound = errors.New("采购询价不存在")

	// ErrDuplicateInquiryNo 询价单号重复错误
	ErrDuplicateInquiryNo = errors.New("询价单号已存在")

	// ErrInvalidPurchaseInquiryData 无效的采购询价数据
	ErrInvalidPurchaseInquiryData = errors.New("无效的采购询价数据")

	// ErrInvalidPurchaseInquiryStatus 无效的采购询价状态
	ErrInvalidPurchaseInquiryStatus = errors.New("无效的采购询价状态")

	// ErrInquiryWorkflowStartFailed 询价工作流启动失败
	ErrInquiryWorkflowStartFailed = errors.New("询价审批工作流启动失败")

	// ErrInvalidQuantity 无效的询价数量
	ErrInvalidQuantity = errors.New("询价数量无效")

	// ErrInquirySupplierNotFound 供应商不存在（询价服务专用）
	ErrInquirySupplierNotFound = errors.New("供应商不存在")

	// ErrInquiryDeleteNotAllowed 询价删除不允许
	ErrInquiryDeleteNotAllowed = errors.New("只有草稿状态的询价才能删除")
)

// 定义工作流相关常量已统一到constants包中

// PurchaseInquiryService 采购询价服务接口
type PurchaseInquiryService interface {
	// CreatePurchaseInquiry 创建采购询价
	CreatePurchaseInquiry(ctx context.Context, dto dto.CreatePurchaseInquiryDTO) (*model.PurchaseInquiry, error)

	// AddInquiryItems 向现有询价单添加询价明细
	AddInquiryItems(ctx context.Context, dto dto.AddInquiryItemsDTO) (*model.PurchaseInquiry, error)

	// UpdatePurchaseInquiry 更新采购询价
	UpdatePurchaseInquiry(ctx context.Context, dto dto.UpdatePurchaseInquiryDTO) (*model.PurchaseInquiry, error)

	// GetPurchaseInquiryByID 根据ID获取采购询价
	GetPurchaseInquiryByID(ctx context.Context, id uint) (*model.PurchaseInquiry, error)

	// GetPurchaseInquiryByInquiryNo 根据询价单号获取采购询价
	GetPurchaseInquiryByInquiryNo(ctx context.Context, inquiryNo string) (*model.PurchaseInquiry, error)

	// ListPurchaseInquiries 获取采购询价列表
	ListPurchaseInquiries(ctx context.Context, query dto.PurchaseInquiryListQuery) (*dto.PurchaseInquiryListResult, error)

	// SubmitPurchaseInquiry 提交采购询价进入审批流程
	SubmitPurchaseInquiry(ctx context.Context, id uint, updatedBy uint) error

	// ApprovePurchaseInquiry 审批采购询价
	ApprovePurchaseInquiry(ctx context.Context, approvalDto dto.InquiryApprovalDTO) error

	// GetPurchaseInquiryHistory 获取采购询价历史记录
	GetPurchaseInquiryHistory(ctx context.Context, inquiryID uint) ([]dto.PurchaseInquiryHistoryDTO, error)

	// RollbackPurchaseInquiry 回退采购询价
	RollbackPurchaseInquiry(ctx context.Context, rollbackDto dto.InquiryRollbackDTO) error

	// CancelPurchaseInquiry 取消采购询价
	CancelPurchaseInquiry(ctx context.Context, id uint, updatedBy uint) error

	// DeletePurchaseInquiry 删除采购询价
	DeletePurchaseInquiry(ctx context.Context, id uint, deletedBy uint) error

	// GenerateInquiryNo 生成询价单号
	GenerateInquiryNo(ctx context.Context) (string, error)

	// GetInquiryStatistics 获取询价统计信息
	GetInquiryStatistics(ctx context.Context, requestID uint) (*dto.InquiryStatisticsDTO, error)

	// ValidateInquiryQuantity 验证询价数量
	ValidateInquiryQuantity(ctx context.Context, dto dto.InquiryQuantityValidationDTO) error

	// UpdateInquiredQuantity 更新已询价数量
	UpdateInquiredQuantity(ctx context.Context, requestItemID uint, quantity int) error

	// CheckInquiryCompletionStatus 检查询价完成状态
	CheckInquiryCompletionStatus(ctx context.Context, inquiryID uint) error

	// GetPurchaseRequestWithInquiryStatus 获取带询价状态的采购申请明细
	GetPurchaseRequestWithInquiryStatus(ctx context.Context, requestID uint) (*model.PurchaseRequest, error)

	// GetInquiredQuantityForRequestItem 获取指定申请明细的已询价数量
	GetInquiredQuantityForRequestItem(ctx context.Context, requestItemID uint) (int, error)

	// GetRequestItemsInquiryStatus 获取采购申请明细的询价状态信息
	GetRequestItemsInquiryStatus(ctx context.Context, requestID uint) (*dto.RequestItemsInquiryStatusResult, error)
}

// purchaseInquiryService 采购询价服务实现
type purchaseInquiryService struct {
	repo            repository.PurchaseInquiryRepository
	requestRepo     repository.PurchaseRequestRepository
	temporalClient  client.Client
	userHelper      *UserHelper
	projectService  ProjectService
	supplierService SupplierService
}

// NewPurchaseInquiryService 创建采购询价服务实例
func NewPurchaseInquiryService(
	repo repository.PurchaseInquiryRepository,
	requestRepo repository.PurchaseRequestRepository,
	temporalClient client.Client,
	userService userService.IUserService,
	projectService ProjectService,
	supplierService SupplierService,
) PurchaseInquiryService {
	// 初始化编码生成器
	utils.InitCodeGenerator()
	userHelper := NewUserHelper(userService)
	return &purchaseInquiryService{
		repo:            repo,
		requestRepo:     requestRepo,
		temporalClient:  temporalClient,
		userHelper:      userHelper,
		projectService:  projectService,
		supplierService: supplierService,
	}
}

// calculateTotalAmount 计算询价单总金额
func (s *purchaseInquiryService) calculateTotalAmount(items []model.PurchaseInquiryItem) float64 {
	var totalAmount float64
	for _, item := range items {
		if item.BudgetAmount != nil {
			totalAmount += *item.BudgetAmount
		}
	}
	return totalAmount
}

// GenerateInquiryNo 生成询价单号
func (s *purchaseInquiryService) GenerateInquiryNo(ctx context.Context) (string, error) {
	// 定义询价单号检查函数
	codeExistChecker := func(ctx context.Context, code string) (bool, error) {
		inquiry, err := s.repo.GetByInquiryNo(ctx, code)
		if err != nil {
			return false, nil
		}
		return inquiry != nil, nil
	}

	// 设置询价单号生成选项
	options := utils.CodeGeneratorOptions{
		Prefix:       "INQ",
		DateFormat:   "20060102150405",
		RandomLength: 4,
		Delimiter:    "",
	}

	// 生成唯一编码
	return utils.GenerateUniqueCode(ctx, codeExistChecker, options)
}

// CreatePurchaseInquiry 创建采购询价
func (s *purchaseInquiryService) CreatePurchaseInquiry(ctx context.Context, createDto dto.CreatePurchaseInquiryDTO) (*model.PurchaseInquiry, error) {
	// 验证供应商是否存在
	if _, err := s.supplierService.GetSupplierByID(ctx, createDto.SupplierID); err != nil {
		return nil, ErrInquirySupplierNotFound
	}

	// 如果有关联的采购申请，验证其存在性
	if createDto.RequestID != nil {
		if _, err := s.requestRepo.GetByID(ctx, *createDto.RequestID); err != nil {
			return nil, fmt.Errorf("关联的采购申请不存在: %w", err)
		}
	}

	// 生成询价单号
	inquiryNo, err := s.GenerateInquiryNo(ctx)
	if err != nil {
		return nil, fmt.Errorf("生成询价单号失败: %w", err)
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		if createDto.CreatedBy > 0 {
			userID = createDto.CreatedBy
		} else {
			userID = 0
		}
	}

	// 验证询价申请关联项
	for _, item := range createDto.RequestItems {
		if item.CurrentInquiryQuantity <= 0 {
			return nil, model.ErrInvalidInquiryQuantity
		}
	}

	// 创建采购询价实体
	inquiry := &model.PurchaseInquiry{
		InquiryNo:           inquiryNo,
		RequestID:           createDto.RequestID,
		ProjectID:           createDto.ProjectID,
		SupplierID:          createDto.SupplierID,
		SupplierDescription: createDto.SupplierDescription,
		Status:              constants.InquiryStatusFinanceReview, // 直接设置为财务审批状态
		CreatedBy:           userID,
		UpdatedBy:           userID,
	}

	// 处理询价申请关联项 - 直接创建询价明细
	inquiry.Items = make([]model.PurchaseInquiryItem, len(createDto.RequestItems))
	for i, itemDto := range createDto.RequestItems {
		// 获取申请明细信息，以获取物料信息
		requestItem, err := s.requestRepo.GetItemByID(ctx, itemDto.RequestItemID)
		if err != nil {
			return nil, fmt.Errorf("获取申请明细失败: %w", err)
		}

		item := model.PurchaseInquiryItem{
			RequestItemID:          itemDto.RequestItemID,
			CurrentInquiryQuantity: itemDto.CurrentInquiryQuantity,
			// 保存物料信息
			MaterialType:   requestItem.MaterialType,
			Model:          requestItem.Model,
			Brand:          requestItem.Brand,
			PN:             itemDto.PN, // 使用传入的PN，可能与申请时的不同
			Specifications: requestItem.Specifications,
			Unit:           requestItem.Unit,
			// 其他字段
			InquiredQuantity: 0, // 已询比价数量初始为0
			BudgetPrice:      itemDto.BudgetPrice,
			Remark:           itemDto.Remark,
		}

		// 计算预算总金额
		item.CalculateBudgetAmount()

		inquiry.Items[i] = item
	}

	// 计算总金额
	inquiry.TotalAmount = s.calculateTotalAmount(inquiry.Items)

	// 保存到数据库
	if err := s.repo.Create(ctx, inquiry); err != nil {
		return nil, err
	}

	// 记录创建历史
	operatorName := s.userHelper.GetUserRealName(ctx, userID)
	history := &model.PurchaseApprovalHistory{
		BusinessType:   constants.BusinessTypePurchaseInquiry,
		BusinessID:     inquiry.ID,
		PreviousStatus: "",
		NewStatus:      constants.InquiryStatusFinanceReview,
		Action:         constants.ActionCreate,
		OperatorID:     userID,
		OperatorName:   operatorName,
		OperationTime:  time.Now(),
		Comments:       "创建采购询价并提交审批",
		CreatedAt:      time.Now(),
	}

	// 创建历史记录
	if err := s.repo.CreateStatusHistory(ctx, history); err != nil {
		fmt.Printf("记录采购询价状态历史失败: %v\n", err)
	}

	// 直接启动工作流，不需要单独提交
	workflowID := fmt.Sprintf("purchase-inquiry-%d", inquiry.ID)
	workflowOptions := client.StartWorkflowOptions{
		ID:        workflowID,
		TaskQueue: constants.TaskQueuePurchaseInquiry,
	}

	// 准备工作流输入参数
	input := inquiryActivity.PurchaseInquiryWorkflowInput{
		InquiryID:   inquiry.ID,
		InquiryNo:   inquiry.InquiryNo,
		RequesterID: inquiry.CreatedBy,
		SupplierID:  inquiry.SupplierID,
		TotalAmount: inquiry.TotalAmount,
	}

	// 执行工作流
	_, err = s.temporalClient.ExecuteWorkflow(ctx, workflowOptions, workflow.PurchaseInquiryApprovalWorkflow, input)
	if err != nil {
		fmt.Printf("启动采购询价工作流失败: %v, inquiryID: %d\n", err, inquiry.ID)
		// 不返回错误，因为询价已经创建成功
	}

	return inquiry, nil
}

// AddInquiryItems 向现有询价单添加询价明细
func (s *purchaseInquiryService) AddInquiryItems(ctx context.Context, addDto dto.AddInquiryItemsDTO) (*model.PurchaseInquiry, error) {
	// 获取现有询价单
	inquiry, err := s.repo.GetByID(ctx, addDto.InquiryID)
	if err != nil {
		return nil, ErrPurchaseInquiryNotFound
	}

	// 检查是否可以添加明细
	if err := inquiry.CanAddItems(); err != nil {
		return nil, err
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		if addDto.UpdatedBy > 0 {
			userID = addDto.UpdatedBy
		} else {
			userID = 0
		}
	}

	// 验证新增的询价明细
	for _, item := range addDto.Items {
		if item.CurrentInquiryQuantity <= 0 {
			return nil, model.ErrInvalidInquiryQuantity
		}
	}

	// 创建新的询价明细
	newItems := make([]model.PurchaseInquiryItem, len(addDto.Items))
	for i, itemDto := range addDto.Items {
		// 获取申请明细信息，以获取物料信息
		requestItem, err := s.requestRepo.GetItemByID(ctx, itemDto.RequestItemID)
		if err != nil {
			return nil, fmt.Errorf("获取申请明细失败: %w", err)
		}

		newItem := model.PurchaseInquiryItem{
			InquiryID:              addDto.InquiryID,
			RequestItemID:          itemDto.RequestItemID,
			CurrentInquiryQuantity: itemDto.CurrentInquiryQuantity,
			// 保存物料信息
			MaterialType:   requestItem.MaterialType,
			Model:          requestItem.Model,
			Brand:          requestItem.Brand,
			PN:             itemDto.PN, // 使用传入的PN，可能与申请时的不同
			Specifications: requestItem.Specifications,
			Unit:           requestItem.Unit,
			// 其他字段
			InquiredQuantity: 0, // 已询比价数量初始为0
			BudgetPrice:      itemDto.BudgetPrice,
			Remark:           itemDto.Remark,
		}

		// 计算预算总金额
		newItem.CalculateBudgetAmount()

		newItems[i] = newItem
	}

	// 将新明细添加到询价单
	inquiry.Items = append(inquiry.Items, newItems...)
	inquiry.UpdatedBy = userID

	// 重新计算总金额
	inquiry.TotalAmount = s.calculateTotalAmount(inquiry.Items)

	// 更新到数据库
	if err := s.repo.Update(ctx, inquiry); err != nil {
		return nil, err
	}

	// 记录添加明细历史
	operatorName := s.userHelper.GetUserRealName(ctx, userID)
	history := &model.PurchaseApprovalHistory{
		BusinessType:   constants.BusinessTypePurchaseInquiry,
		BusinessID:     inquiry.ID,
		PreviousStatus: inquiry.Status,
		NewStatus:      inquiry.Status,
		Action:         constants.ActionUpdate,
		OperatorID:     userID,
		OperatorName:   operatorName,
		OperationTime:  time.Now(),
		Comments:       fmt.Sprintf("添加询价明细 %d 个", len(addDto.Items)),
		CreatedAt:      time.Now(),
	}

	// 创建历史记录
	if err := s.repo.CreateStatusHistory(ctx, history); err != nil {
		fmt.Printf("记录采购询价状态历史失败: %v\n", err)
	}

	// 直接更新状态并启动工作流，不需要单独提交
	// 更新状态为财务审批
	if inquiry.Status == constants.InquiryStatusDraft {
		inquiry.Status = constants.InquiryStatusFinanceReview
		if err := s.repo.Update(ctx, inquiry); err != nil {
			fmt.Printf("更新询价状态失败: %v\n", err)
			// 不返回错误，因为明细已经添加成功
		} else {
			// 记录状态变更历史
			historyStatus := &model.PurchaseApprovalHistory{
				BusinessType:   constants.BusinessTypePurchaseInquiry,
				BusinessID:     inquiry.ID,
				PreviousStatus: constants.InquiryStatusDraft,
				NewStatus:      constants.InquiryStatusFinanceReview,
				Action:         constants.ActionUpdate,
				OperatorID:     userID,
				OperatorName:   operatorName,
				OperationTime:  time.Now(),
				Comments:       "添加询价明细并提交审批",
				CreatedAt:      time.Now(),
			}
			if err := s.repo.CreateStatusHistory(ctx, historyStatus); err != nil {
				fmt.Printf("记录状态变更历史失败: %v\n", err)
			}

			// 启动工作流
			workflowID := fmt.Sprintf("purchase-inquiry-%d", inquiry.ID)
			workflowOptions := client.StartWorkflowOptions{
				ID:        workflowID,
				TaskQueue: constants.TaskQueuePurchaseInquiry,
			}

			// 准备工作流输入参数
			input := inquiryActivity.PurchaseInquiryWorkflowInput{
				InquiryID:   inquiry.ID,
				InquiryNo:   inquiry.InquiryNo,
				RequesterID: inquiry.CreatedBy,
				SupplierID:  inquiry.SupplierID,
				TotalAmount: inquiry.TotalAmount,
			}

			// 执行工作流
			_, err = s.temporalClient.ExecuteWorkflow(ctx, workflowOptions, workflow.PurchaseInquiryApprovalWorkflow, input)
			if err != nil {
				fmt.Printf("启动采购询价工作流失败: %v, inquiryID: %d\n", err, inquiry.ID)
			}
		}
	}

	return inquiry, nil
}

// UpdatePurchaseInquiry 更新采购询价
func (s *purchaseInquiryService) UpdatePurchaseInquiry(ctx context.Context, updateDto dto.UpdatePurchaseInquiryDTO) (*model.PurchaseInquiry, error) {
	// 检查采购询价是否存在
	inquiry, err := s.repo.GetByID(ctx, updateDto.ID)
	if err != nil {
		return nil, ErrPurchaseInquiryNotFound
	}

	// 允许在任何状态下更新询价
	// 不再检查状态限制

	// 验证供应商是否存在
	if _, err := s.supplierService.GetSupplierByID(ctx, updateDto.SupplierID); err != nil {
		return nil, ErrInquirySupplierNotFound
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		if updateDto.UpdatedBy > 0 {
			userID = updateDto.UpdatedBy
		} else {
			userID = 0
		}
	}

	// 更新基本信息
	inquiry.RequestID = updateDto.RequestID
	inquiry.ProjectID = updateDto.ProjectID
	inquiry.SupplierID = updateDto.SupplierID
	inquiry.SupplierDescription = updateDto.SupplierDescription
	inquiry.UpdatedBy = userID

	// 处理明细项 - 更新以支持新数据结构
	if len(updateDto.Items) > 0 {
		inquiry.Items = make([]model.PurchaseInquiryItem, len(updateDto.Items))
		for i, itemDto := range updateDto.Items {
			item := model.PurchaseInquiryItem{
				ID:                     itemDto.ID,
				InquiryID:              updateDto.ID,
				RequestItemID:          itemDto.RequestItemID,
				PN:                     itemDto.PN,
				InquiredQuantity:       itemDto.InquiredQuantity,
				CurrentInquiryQuantity: itemDto.CurrentInquiryQuantity,
				BudgetPrice:            itemDto.BudgetPrice,
				BudgetAmount:           itemDto.BudgetAmount,
				Remark:                 itemDto.Remark,
			}

			// 验证询价明细
			if err := item.Validate(); err != nil {
				return nil, err
			}

			// 计算预算金额
			item.CalculateBudgetAmount()

			inquiry.Items[i] = item
		}

		// 重新计算总金额
		inquiry.TotalAmount = s.calculateTotalAmount(inquiry.Items)
	}

	// 保存到数据库
	if err := s.repo.Update(ctx, inquiry); err != nil {
		return nil, err
	}

	// 记录更新历史
	operatorName := s.userHelper.GetUserRealName(ctx, userID)
	history := &model.PurchaseApprovalHistory{
		BusinessType:   constants.BusinessTypePurchaseInquiry,
		BusinessID:     inquiry.ID,
		PreviousStatus: inquiry.Status,
		NewStatus:      inquiry.Status,
		Action:         constants.ActionUpdate,
		OperatorID:     userID,
		OperatorName:   operatorName,
		OperationTime:  time.Now(),
		Comments:       "更新采购询价",
		CreatedAt:      time.Now(),
	}

	// 创建历史记录
	if err := s.repo.CreateStatusHistory(ctx, history); err != nil {
		fmt.Printf("记录采购询价状态历史失败: %v\n", err)
	}

	return inquiry, nil
}

// GetPurchaseInquiryByID 根据ID获取采购询价
func (s *purchaseInquiryService) GetPurchaseInquiryByID(ctx context.Context, id uint) (*model.PurchaseInquiry, error) {
	inquiry, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取项目名称
	if inquiry.ProjectID != nil && *inquiry.ProjectID > 0 {
		project, err := s.projectService.GetProjectByID(ctx, *inquiry.ProjectID)
		if err == nil && project != nil {
			inquiry.ProjectName = project.ProjectName
		}
	}

	// 获取供应商名称
	supplier, err := s.supplierService.GetSupplierByID(ctx, inquiry.SupplierID)
	if err == nil && supplier != nil {
		inquiry.SupplierName = supplier.SupplierName
		inquiry.SupplierContact = supplier.ContactPerson
	}

	// 获取创建人真实姓名
	if inquiry.CreatedBy > 0 {
		creatorName := s.userHelper.GetUserRealName(ctx, inquiry.CreatedBy)
		inquiry.CreatorName = creatorName
	}

	// 获取关联采购申请单号
	if inquiry.RequestID != nil && *inquiry.RequestID > 0 {
		request, err := s.requestRepo.GetByID(ctx, *inquiry.RequestID)
		if err == nil && request != nil {
			// 只设置申请单号，不返回完整的Request对象
			inquiry.RequestNo = request.RequestNo
			// 将Request设为nil，确保不返回完整的Request对象
			inquiry.Request = nil
		}
	}

	// 补充询价明细中的申请需求数量
	if len(inquiry.Items) > 0 {
		for i, item := range inquiry.Items {
			if item.RequestItem != nil {
				// 获取申请明细的原始数据
				inquiry.Items[i].RequestQuantity = item.RequestItem.Quantity
				inquiry.Items[i].MaterialType = item.RequestItem.MaterialType
				inquiry.Items[i].Model = item.RequestItem.Model
				inquiry.Items[i].Brand = item.RequestItem.Brand
				inquiry.Items[i].Specifications = item.RequestItem.Specifications
				inquiry.Items[i].Unit = item.RequestItem.Unit

				// 计算未询价数量
				inquiredQuantity, err := s.GetInquiredQuantityForRequestItem(ctx, item.RequestItemID)
				if err == nil {
					inquiry.Items[i].UninquiredQuantity = item.RequestItem.Quantity - inquiredQuantity
				}

				// 移除引用，不返回完整的 request_item 数据
				inquiry.Items[i].RequestItem = nil
			}
		}
	}

	return inquiry, nil
}

// GetPurchaseInquiryByInquiryNo 根据询价单号获取采购询价
func (s *purchaseInquiryService) GetPurchaseInquiryByInquiryNo(ctx context.Context, inquiryNo string) (*model.PurchaseInquiry, error) {
	inquiry, err := s.repo.GetByInquiryNo(ctx, inquiryNo)
	if err != nil {
		return nil, err
	}

	// 获取项目名称
	if inquiry.ProjectID != nil && *inquiry.ProjectID > 0 {
		project, err := s.projectService.GetProjectByID(ctx, *inquiry.ProjectID)
		if err == nil && project != nil {
			inquiry.ProjectName = project.ProjectName
		}
	}

	// 获取供应商名称
	supplier, err := s.supplierService.GetSupplierByID(ctx, inquiry.SupplierID)
	if err == nil && supplier != nil {
		inquiry.SupplierName = supplier.SupplierName
		inquiry.SupplierContact = supplier.ContactPerson
	}

	// 获取创建人真实姓名
	if inquiry.CreatedBy > 0 {
		creatorName := s.userHelper.GetUserRealName(ctx, inquiry.CreatedBy)
		inquiry.CreatorName = creatorName
	}

	// 获取关联采购申请单号
	if inquiry.RequestID != nil && *inquiry.RequestID > 0 {
		request, err := s.requestRepo.GetByID(ctx, *inquiry.RequestID)
		if err == nil && request != nil {
			// 只设置申请单号，不返回完整的Request对象
			inquiry.RequestNo = request.RequestNo
			// 将Request设为nil，确保不返回完整的Request对象
			inquiry.Request = nil
		}
	}

	// 补充询价明细中的申请需求数量
	if len(inquiry.Items) > 0 {
		for i, item := range inquiry.Items {
			if item.RequestItem != nil {
				// 获取申请明细的原始数据
				inquiry.Items[i].RequestQuantity = item.RequestItem.Quantity
				inquiry.Items[i].MaterialType = item.RequestItem.MaterialType
				inquiry.Items[i].Model = item.RequestItem.Model
				inquiry.Items[i].Brand = item.RequestItem.Brand
				inquiry.Items[i].Specifications = item.RequestItem.Specifications
				inquiry.Items[i].Unit = item.RequestItem.Unit

				// 计算未询价数量
				inquiredQuantity, err := s.GetInquiredQuantityForRequestItem(ctx, item.RequestItemID)
				if err == nil {
					inquiry.Items[i].UninquiredQuantity = item.RequestItem.Quantity - inquiredQuantity
				}

				// 移除引用，不返回完整的 request_item 数据
				inquiry.Items[i].RequestItem = nil
			}
		}
	}

	return inquiry, nil
}

// ListPurchaseInquiries 获取采购询价列表
func (s *purchaseInquiryService) ListPurchaseInquiries(ctx context.Context, query dto.PurchaseInquiryListQuery) (*dto.PurchaseInquiryListResult, error) {
	inquiries, total, err := s.repo.List(ctx, query.PurchaseInquiryFilter, query.PaginationOptions)
	if err != nil {
		return nil, err
	}

	// 构建完整的询价列表响应
	resultInquiries := make([]*model.PurchaseInquiry, len(inquiries))
	for i, inquiry := range inquiries {
		// 创建带有完整明细信息的询价对象
		enhancedInquiry := &model.PurchaseInquiry{
			ID:                  inquiry.ID,
			InquiryNo:           inquiry.InquiryNo,
			RequestID:           inquiry.RequestID,
			ProjectID:           inquiry.ProjectID,
			SupplierID:          inquiry.SupplierID,
			TotalAmount:         inquiry.TotalAmount,
			SupplierDescription: inquiry.SupplierDescription,
			Status:              inquiry.Status,
			CreatedBy:           inquiry.CreatedBy,
			CreatedAt:           inquiry.CreatedAt,
			UpdatedBy:           inquiry.UpdatedBy,
			UpdatedAt:           inquiry.UpdatedAt,
		}

		// 获取项目名称
		if inquiry.ProjectID != nil && *inquiry.ProjectID > 0 {
			project, err := s.projectService.GetProjectByID(ctx, *inquiry.ProjectID)
			if err == nil && project != nil {
				enhancedInquiry.ProjectName = project.ProjectName
			}
		}

		// 获取供应商名称
		supplier, err := s.supplierService.GetSupplierByID(ctx, inquiry.SupplierID)
		if err == nil && supplier != nil {
			enhancedInquiry.SupplierName = supplier.SupplierName
			enhancedInquiry.SupplierContact = supplier.ContactPerson
		}

		// 获取创建人真实姓名
		if inquiry.CreatedBy > 0 {
			creatorName := s.userHelper.GetUserRealName(ctx, inquiry.CreatedBy)
			enhancedInquiry.CreatorName = creatorName
		}

		// 获取关联采购申请单号
		if inquiry.RequestID != nil && *inquiry.RequestID > 0 {
			request, err := s.requestRepo.GetByID(ctx, *inquiry.RequestID)
			if err == nil && request != nil {
				// 只设置申请单号，不返回完整的Request对象
				enhancedInquiry.RequestNo = request.RequestNo
				// 将Request设为nil，确保不返回完整的Request对象
				enhancedInquiry.Request = nil
			}
		}

		// 处理询价明细项，添加物料信息但移除request_item
		if len(inquiry.Items) > 0 {
			enhancedInquiry.Items = make([]model.PurchaseInquiryItem, len(inquiry.Items))
			for j, item := range inquiry.Items {
				enhancedItem := item

				// 如果有关联的申请明细，添加物料信息和需求数量
				if item.RequestItem != nil {
					// 获取申请明细的原始需求数量
					enhancedItem.RequestQuantity = item.RequestItem.Quantity

					// 计算未询价数量，使用GetInquiredQuantityForRequestItem方法获取准确的已询价数量
					inquiredQuantity, err := s.GetInquiredQuantityForRequestItem(ctx, item.RequestItemID)
					if err == nil {
						enhancedItem.UninquiredQuantity = item.RequestItem.Quantity - inquiredQuantity
					} else {
						// 如果获取已询价数量失败，则设置为0（保守估计）
						enhancedItem.UninquiredQuantity = item.RequestItem.Quantity
					}

					// 移除RequestItem引用，避免返回不必要的数据
					enhancedItem.RequestItem = nil
				}

				enhancedInquiry.Items[j] = enhancedItem
			}
		}

		resultInquiries[i] = enhancedInquiry
	}

	return &dto.PurchaseInquiryListResult{
		Total: total,
		List:  resultInquiries,
	}, nil
}

// SubmitPurchaseInquiry 提交采购询价进入审批流程
func (s *purchaseInquiryService) SubmitPurchaseInquiry(ctx context.Context, id uint, updatedBy uint) error {
	// 获取询价信息
	inquiry, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return ErrPurchaseInquiryNotFound
	}

	// 只有草稿状态才能提交
	if inquiry.Status != constants.InquiryStatusDraft {
		return ErrInvalidPurchaseInquiryStatus
	}

	// 直接使用传入的updatedBy（controller层已从JWT获取）
	userID := updatedBy

	// 更新状态为财务审批
	newStatus := constants.InquiryStatusFinanceReview
	operatorName := s.userHelper.GetUserRealName(ctx, userID)

	if err := s.repo.UpdateStatusWithHistory(ctx, id, newStatus, constants.ActionSubmit, userID, operatorName, "提交询价审批"); err != nil {
		return err
	}

	// 启动工作流
	workflowID := fmt.Sprintf("purchase-inquiry-%d", inquiry.ID)
	workflowOptions := client.StartWorkflowOptions{
		ID:        workflowID,
		TaskQueue: constants.TaskQueuePurchaseInquiry,
	}

	// 准备工作流输入参数
	input := inquiryActivity.PurchaseInquiryWorkflowInput{
		InquiryID:   inquiry.ID,
		InquiryNo:   inquiry.InquiryNo,
		RequesterID: inquiry.CreatedBy,
		SupplierID:  inquiry.SupplierID,
		TotalAmount: inquiry.TotalAmount,
	}

	// 执行工作流
	_, err = s.temporalClient.ExecuteWorkflow(ctx, workflowOptions, workflow.PurchaseInquiryApprovalWorkflow, input)
	if err != nil {
		fmt.Printf("启动采购询价工作流失败: %v, inquiryID: %d\n", err, inquiry.ID)
		return ErrInquiryWorkflowStartFailed
	}

	return nil
}

// ApprovePurchaseInquiry 审批采购询价
func (s *purchaseInquiryService) ApprovePurchaseInquiry(ctx context.Context, approvalDto dto.InquiryApprovalDTO) error {
	// 验证动作有效性
	if !constants.IsValidAction(approvalDto.Action) {
		return fmt.Errorf("无效的审批动作: %s", approvalDto.Action)
	}

	// 获取询价信息
	inquiry, err := s.repo.GetByID(ctx, approvalDto.InquiryID)
	if err != nil {
		return ErrPurchaseInquiryNotFound
	}

	// 验证当前阶段与状态匹配 - 直接比较数据库状态值
	if inquiry.Status != approvalDto.CurrentStage {
		return fmt.Errorf("当前阶段与询价审批状态不匹配")
	}

	// 直接使用DTO中的ApproverID（controller层已从JWT获取并设置）
	userID := approvalDto.ApproverID
	if userID == 0 {
		return fmt.Errorf("审批人ID不能为空")
	}

	// 构造审批信号数据
	approvalSignal := workflow.InquiryApprovalSignal{
		ApproverID:   userID,
		Action:       approvalDto.Action,
		Comments:     approvalDto.Comments,
		CurrentStage: approvalDto.CurrentStage,
		Timestamp:    time.Now(),
	}

	// 发送审批信号到工作流
	workflowID := fmt.Sprintf("purchase-inquiry-%d", approvalDto.InquiryID)
	err = s.temporalClient.SignalWorkflow(ctx, workflowID, "", workflow.SignalNameInquiryApproval, approvalSignal)
	if err != nil {
		return fmt.Errorf("发送审批信号失败: %w", err)
	}

	return nil
}

// GetPurchaseInquiryHistory 获取采购询价历史记录
func (s *purchaseInquiryService) GetPurchaseInquiryHistory(ctx context.Context, inquiryID uint) ([]dto.PurchaseInquiryHistoryDTO, error) {
	histories, err := s.repo.GetHistory(ctx, inquiryID)
	if err != nil {
		return nil, err
	}

	result := make([]dto.PurchaseInquiryHistoryDTO, len(histories))
	for i, history := range histories {
		result[i] = dto.PurchaseInquiryHistoryDTO{
			ID:             history.ID,
			BusinessType:   history.BusinessType,
			BusinessID:     history.BusinessID,
			PreviousStatus: history.PreviousStatus,
			NewStatus:      history.NewStatus,
			Action:         history.Action,
			OperatorID:     history.OperatorID,
			OperatorName:   history.OperatorName,
			OperationTime:  history.OperationTime,
			Comments:       history.Comments,
			CreatedAt:      history.CreatedAt,
		}
	}

	return result, nil
}

// RollbackPurchaseInquiry 回退采购询价
func (s *purchaseInquiryService) RollbackPurchaseInquiry(ctx context.Context, rollbackDto dto.InquiryRollbackDTO) error {
	// 直接使用DTO中的ApproverID（controller层已从JWT获取）
	userID := rollbackDto.ApproverID

	// 构造回退信号数据
	rollbackSignal := workflow.InquiryRollbackSignal{
		ApproverID:   userID,
		RollbackTo:   rollbackDto.RollbackTo,
		CurrentStage: rollbackDto.CurrentStage,
		Comments:     rollbackDto.Comments,
		Timestamp:    time.Now(),
	}

	// 发送回退信号到工作流
	workflowID := fmt.Sprintf("purchase-inquiry-%d", rollbackDto.InquiryID)
	err := s.temporalClient.SignalWorkflow(ctx, workflowID, "", workflow.SignalNameInquiryRollback, rollbackSignal)
	if err != nil {
		return fmt.Errorf("发送回退信号失败: %w", err)
	}

	return nil
}

// CancelPurchaseInquiry 取消采购询价
func (s *purchaseInquiryService) CancelPurchaseInquiry(ctx context.Context, id uint, updatedBy uint) error {
	// 获取询价信息
	inquiry, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return ErrPurchaseInquiryNotFound
	}

	// 只有草稿状态或审批中的询价才能取消
	if inquiry.Status != constants.InquiryStatusDraft &&
		inquiry.Status != constants.InquiryStatusFinanceReview &&
		inquiry.Status != constants.InquiryStatusEnterpriseReview {
		return ErrInvalidPurchaseInquiryStatus
	}

	// 直接使用传入的updatedBy（controller层已从JWT获取）
	userID := updatedBy

	// 更新状态为已取消
	operatorName := s.userHelper.GetUserRealName(ctx, userID)
	return s.repo.UpdateStatusWithHistory(ctx, id, constants.InquiryStatusCancelled, constants.ActionCancel, userID, operatorName, "取消询价")
}

// DeletePurchaseInquiry 删除采购询价
func (s *purchaseInquiryService) DeletePurchaseInquiry(ctx context.Context, id uint, deletedBy uint) error {
	// 获取询价信息
	inquiry, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return ErrPurchaseInquiryNotFound
	}

	// 只有草稿状态的询价才能删除
	if inquiry.Status != constants.InquiryStatusDraft {
		return ErrInquiryDeleteNotAllowed
	}

	return s.repo.Delete(ctx, id, deletedBy)
}

// GetInquiryStatistics 获取询价统计信息
func (s *purchaseInquiryService) GetInquiryStatistics(ctx context.Context, requestID uint) (*dto.InquiryStatisticsDTO, error) {
	// 获取询价列表
	inquiries, err := s.repo.GetByRequestID(ctx, requestID)
	if err != nil {
		return nil, err
	}

	// 统计询价状态
	totalInquiries := len(inquiries)
	pendingInquiries := 0

	supplierStats := make(map[uint]*dto.InquirySupplierStatisticsDTO)

	for _, inquiry := range inquiries {
		if inquiry.Status == constants.InquiryStatusCompleted {
			// completedInquiries++ // This line is removed
		} else {
			pendingInquiries++
		}

		// 供应商统计
		if stats, exists := supplierStats[inquiry.SupplierID]; exists {
			stats.InquiryCount++
			if inquiry.Status == constants.InquiryStatusQuoted {
				stats.QuotedCount++
			} else {
				stats.PendingCount++
			}
		} else {
			supplierStats[inquiry.SupplierID] = &dto.InquirySupplierStatisticsDTO{
				SupplierID:   inquiry.SupplierID,
				SupplierName: inquiry.SupplierName,
				InquiryCount: 1,
				QuotedCount:  0,
				PendingCount: 1,
			}
			if inquiry.Status == constants.InquiryStatusQuoted {
				supplierStats[inquiry.SupplierID].QuotedCount = 1
				supplierStats[inquiry.SupplierID].PendingCount = 0
			}
		}
	}

	// 计算报价率
	var supplierStatsList []dto.InquirySupplierStatisticsDTO
	for _, stats := range supplierStats {
		if stats.InquiryCount > 0 {
			stats.QuoteRate = float64(stats.QuotedCount) / float64(stats.InquiryCount) * 100
		}
		supplierStatsList = append(supplierStatsList, *stats)
	}

	// 获取物料统计
	itemSummaries, err := s.repo.GetQuantitySummary(ctx, requestID)
	if err != nil {
		return nil, err
	}

	var itemStatsList []dto.InquiryItemStatisticsDTO
	for _, summary := range itemSummaries {
		progress := float64(0)
		if summary.TotalQuantity > 0 {
			progress = float64(summary.InquiredQuantity) / float64(summary.TotalQuantity) * 100
		}

		itemStatsList = append(itemStatsList, dto.InquiryItemStatisticsDTO{
			RequestItemID:      summary.RequestItemID,
			TotalQuantity:      summary.TotalQuantity,
			InquiredQuantity:   summary.InquiredQuantity,
			UninquiredQuantity: summary.UninquiredQuantity,
			InquiryProgress:    progress,
		})
	}

	return &dto.InquiryStatisticsDTO{
		RequestID:           requestID,
		TotalInquiries:      totalInquiries,
		InProgressInquiries: pendingInquiries,
		PendingInquiries:    pendingInquiries,
		ItemStatistics:      itemStatsList,
		SupplierStatistics:  supplierStatsList,
	}, nil
}

// ValidateInquiryQuantity 验证询价数量
func (s *purchaseInquiryService) ValidateInquiryQuantity(ctx context.Context, dto dto.InquiryQuantityValidationDTO) error {
	// 获取数量汇总
	summaries, err := s.repo.GetQuantitySummary(ctx, dto.RequestItemID)
	if err != nil {
		return err
	}

	for _, summary := range summaries {
		if summary.RequestItemID == dto.RequestItemID {
			if dto.CurrentInquiryQuantity > summary.UninquiredQuantity {
				return model.ErrInquiryQuantityExceedsRemaining
			}
			break
		}
	}

	return nil
}

// UpdateInquiredQuantity 更新已询价数量
func (s *purchaseInquiryService) UpdateInquiredQuantity(ctx context.Context, requestItemID uint, quantity int) error {
	return s.repo.UpdateInquiredQuantity(ctx, requestItemID, quantity)
}

// CheckInquiryCompletionStatus 检查询价完成状态
func (s *purchaseInquiryService) CheckInquiryCompletionStatus(ctx context.Context, inquiryID uint) error {
	// 获取询价单信息
	inquiry, err := s.repo.GetByID(ctx, inquiryID)
	if err != nil {
		return ErrPurchaseInquiryNotFound
	}

	// 如果没有关联采购申请，不进行完成状态检查
	if inquiry.RequestID == nil {
		return nil
	}

	return nil
}

// GetPurchaseRequestWithInquiryStatus 获取带询价状态的采购申请明细
func (s *purchaseInquiryService) GetPurchaseRequestWithInquiryStatus(ctx context.Context, requestID uint) (*model.PurchaseRequest, error) {
	// 获取采购申请及其明细
	request, err := s.requestRepo.GetByID(ctx, requestID)
	if err != nil {
		return nil, err
	}

	// 获取所有已审批通过的询价单
	approvedInquiries, err := s.repo.GetByRequestIDAndStatus(ctx, requestID, constants.InquiryStatusCompleted)
	if err != nil {
		return nil, err
	}

	// 统计每个申请明细的已询价数量
	requestItemInquiredMap := make(map[uint]int)
	for _, inquiry := range approvedInquiries {
		for _, item := range inquiry.Items {
			requestItemInquiredMap[item.RequestItemID] += item.CurrentInquiryQuantity
		}
	}

	// 为每个申请明细添加询价状态信息
	for i := range request.Items {
		inquiredQty := requestItemInquiredMap[request.Items[i].ID]
		// 这里可以添加自定义字段来存储询价状态，或者通过其他方式返回
		// 由于模型结构限制，这里只返回基础信息
		_ = inquiredQty // 暂时不使用，等待前端需要时再添加相应字段
	}

	return request, nil
}

// GetInquiredQuantityForRequestItem 获取指定申请明细的已询价数量
func (s *purchaseInquiryService) GetInquiredQuantityForRequestItem(ctx context.Context, requestItemID uint) (int, error) {
	// 查询所有包含该申请明细的询价单，排除已取消和已拒绝的询价单
	var inquiryItems []model.PurchaseInquiryItem
	err := s.repo.GetDB().WithContext(ctx).
		Table("purchase_inquiry_items").
		Joins("JOIN purchase_inquiries ON purchase_inquiry_items.inquiry_id = purchase_inquiries.id").
		Where("purchase_inquiry_items.request_item_id = ?", requestItemID).
		Where("purchase_inquiries.status NOT IN (?)", []string{constants.InquiryStatusCancelled, constants.InquiryStatusRejected}).
		Select("purchase_inquiry_items.*").
		Find(&inquiryItems).Error
	if err != nil {
		return 0, err
	}

	totalInquired := 0
	for _, item := range inquiryItems {
		totalInquired += item.CurrentInquiryQuantity
	}

	return totalInquired, nil
}

// GetRequestItemsInquiryStatus 获取采购申请明细的询价状态信息
func (s *purchaseInquiryService) GetRequestItemsInquiryStatus(ctx context.Context, requestID uint) (*dto.RequestItemsInquiryStatusResult, error) {
	// 获取采购申请及其明细
	request, err := s.requestRepo.GetByID(ctx, requestID)
	if err != nil {
		return nil, err
	}

	// 构建返回结果
	result := &dto.RequestItemsInquiryStatusResult{
		RequestID: requestID,
		RequestNo: request.RequestNo,
		Items:     make([]dto.RequestItemInquiryStatus, 0, len(request.Items)),
	}

	// 为每个申请明细获取询价状态
	for _, item := range request.Items {
		// 获取已询价数量
		inquiredQuantity, err := s.GetInquiredQuantityForRequestItem(ctx, item.ID)
		if err != nil {
			return nil, fmt.Errorf("获取询价数量失败: %w", err)
		}

		// 计算未询价数量
		uninquiredQuantity := item.Quantity - inquiredQuantity

		// 计算询价进度百分比
		var inquiryProgress float64
		if item.Quantity > 0 {
			inquiryProgress = float64(inquiredQuantity) / float64(item.Quantity) * 100
		}

		// 添加到结果
		itemStatus := dto.RequestItemInquiryStatus{
			RequestItemID:      item.ID,
			MaterialType:       item.MaterialType,
			Model:              item.Model,
			Brand:              item.Brand,
			PN:                 item.PN,
			Specifications:     item.Specifications,
			Unit:               item.Unit,
			TotalQuantity:      item.Quantity,
			InquiredQuantity:   inquiredQuantity,
			UninquiredQuantity: uninquiredQuantity,
			InquiryProgress:    inquiryProgress,
		}

		result.Items = append(result.Items, itemStatus)
	}

	return result, nil
}
