package service

import (
	"backend/internal/common/utils"
	"backend/internal/modules/purchase/constants"
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/model"
	"backend/internal/modules/purchase/repository"
	"backend/internal/modules/purchase/workflow"
	"context"
	"errors"
	"fmt"
	"log"
	"time"

	productModel "backend/internal/modules/cmdb/model/product"
	userService "backend/internal/modules/user/service"

	"go.temporal.io/sdk/client"
	"gorm.io/gorm"
)

// PurchaseContractService 采购合同服务接口
type PurchaseContractService interface {
	// CreatePurchaseContract 创建采购合同
	CreatePurchaseContract(ctx context.Context, createDTO dto.CreatePurchaseContractDTO, createdBy uint) (*model.PurchaseContract, error)

	// GetPurchaseContractByID 根据ID获取采购合同
	GetPurchaseContractByID(ctx context.Context, id uint) (*dto.PurchaseContractDetailDTO, error)

	// GetPurchaseContractByContractNo 根据合同编号获取采购合同
	GetPurchaseContractByContractNo(ctx context.Context, contractNo string) (*dto.PurchaseContractDetailDTO, error)

	// ListPurchaseContracts 获取采购合同列表
	ListPurchaseContracts(ctx context.Context, query dto.PurchaseContractListQuery) (*dto.PurchaseContractListResult, error)

	// UpdatePurchaseContract 更新采购合同
	UpdatePurchaseContract(ctx context.Context, updateDTO dto.UpdatePurchaseContractDTO) (*model.PurchaseContract, error)

	// SubmitPurchaseContract 提交采购合同
	SubmitPurchaseContract(ctx context.Context, id uint, updatedBy uint) error

	// ApprovePurchaseContract 审批采购合同
	ApprovePurchaseContract(ctx context.Context, approvalDTO dto.ContractApprovalDTO) error

	// RollbackPurchaseContract 回退采购合同审批
	RollbackPurchaseContract(ctx context.Context, rollbackDTO dto.ContractRollbackDTO) error

	// CancelPurchaseContract 取消采购合同
	CancelPurchaseContract(ctx context.Context, id uint, updatedBy uint) error

	// DeletePurchaseContract 删除采购合同
	DeletePurchaseContract(ctx context.Context, id uint, deletedBy uint) error

	// GetPurchaseContractHistory 获取采购合同历史记录
	GetPurchaseContractHistory(ctx context.Context, id uint) ([]dto.ContractHistoryDTO, error)

	// GetInquiryItemsPurchaseStats 获取询价明细的采购统计信息
	GetInquiryItemsPurchaseStats(ctx context.Context, inquiryID uint) ([]dto.InquiryItemPurchaseStatsDTO, error)
}

// 定义可能的错误
var (
	// ErrPurchaseContractNotFound 合同不存在
	ErrPurchaseContractNotFound = errors.New("采购合同不存在")

	// ErrInvalidPurchaseContractData 无效的合同数据
	ErrInvalidPurchaseContractData = errors.New("无效的采购合同数据")

	// ErrContractDeleteNotAllowed 合同删除不允许
	ErrContractDeleteNotAllowed = errors.New("只有草稿状态的合同才能删除")

	// ErrContractSubmitNotAllowed 合同提交不允许
	ErrContractSubmitNotAllowed = errors.New("只有草稿状态的合同才能提交")

	// ErrContractSupplierNotFound 合同供应商不存在
	ErrContractSupplierNotFound = errors.New("供应商不存在")

	// ErrInvalidContractStatus 无效的合同状态
	ErrInvalidContractStatus = errors.New("无效的合同状态")

	// ErrInvalidContractApprovalStage 无效的合同审批阶段
	ErrInvalidContractApprovalStage = errors.New("当前阶段与合同审批状态不匹配")

	// ErrDuplicateContractNo 合同编号重复
	ErrDuplicateContractNo = errors.New("合同编号已存在")
)

// ProductService 产品服务接口（用于依赖注入）
type ProductService interface {
	GetByID(ctx context.Context, id uint) (*productModel.Product, error)
	GetByPNAndBrand(ctx context.Context, pn, brand string) (*productModel.Product, error)
	UpdateUnit(ctx context.Context, productID uint, unit string) error
	Create(ctx context.Context, dto productModel.CreateProductDTO) (*productModel.Product, error)
}

// 定义工作流相关常量已统一到constants包中

// inferProductCategory 根据物料类型推断产品类别
func inferProductCategory(materialType string) string {
	// 根据物料类型映射到产品类别（与前端定义保持一致）
	categoryMap := map[string]string{
		"CPU":     "配件",
		"内存":      "配件",
		"硬盘":      "配件",
		"SSD":     "配件",
		"显卡":      "配件",
		"主板":      "配件",
		"机箱":      "配件",
		"电源模块":    "配件",
		"风扇":      "配件",
		"散热器":     "配件",
		"跳线":      "配件",
		"BMC板":    "配件",
		"网卡":      "网络设备配件",
		"线缆":      "线缆",
		"光模块":     "网络设备配件",
		"raid卡":   "配件",
		"GPU底板":   "配件",
		"Switch板": "网络设备配件",
		"背板":      "配件",
		"服务器":     "服务器",
		"交换机":     "网络设备",
		"路由器":     "网络设备",
		"维保":      "维保",
		"AOC":     "AOC",
	}

	if category, exists := categoryMap[materialType]; exists {
		return category
	}

	// 如果没有匹配的映射，返回默认类别
	return "其他"
}

// purchaseContractService 采购合同服务实现
type purchaseContractService struct {
	repo            repository.PurchaseContractRepository
	temporalClient  client.Client
	userHelper      *UserHelper
	supplierService SupplierService
	projectService  ProjectService
	companyService  CompanyService
	productService  ProductService
	inquiryService  PurchaseInquiryService
}

// NewPurchaseContractService 创建采购合同服务实例
func NewPurchaseContractService(
	repo repository.PurchaseContractRepository,
	temporalClient client.Client,
	userService userService.IUserService,
	supplierService SupplierService,
	projectService ProjectService,
	companyService CompanyService,
	productService ProductService,
	inquiryService PurchaseInquiryService,
) PurchaseContractService {
	// 初始化编码生成器
	utils.InitCodeGenerator()
	userHelper := NewUserHelper(userService)
	return &purchaseContractService{
		repo:            repo,
		temporalClient:  temporalClient,
		userHelper:      userHelper,
		supplierService: supplierService,
		projectService:  projectService,
		companyService:  companyService,
		productService:  productService,
		inquiryService:  inquiryService,
	}
}

// CreatePurchaseContract 创建采购合同
func (s *purchaseContractService) CreatePurchaseContract(ctx context.Context, createDTO dto.CreatePurchaseContractDTO, createdBy uint) (*model.PurchaseContract, error) {
	// 验证供应商是否存在
	if _, err := s.supplierService.GetSupplierByID(ctx, createDTO.SupplierID); err != nil {
		return nil, ErrContractSupplierNotFound
	}

	// 检查合同编号是否重复
	exists, err := s.repo.CheckContractNoExists(ctx, createDTO.ContractNo)
	if err != nil {
		return nil, fmt.Errorf("检查合同编号失败: %w", err)
	}
	if exists {
		return nil, ErrDuplicateContractNo
	}

	// 创建合同对象
	contract := &model.PurchaseContract{
		ContractNo:      createDTO.ContractNo,
		InquiryID:       createDTO.InquiryID,
		ProjectID:       createDTO.ProjectID,
		OurCompanyID:    createDTO.OurCompanyID,
		SupplierID:      createDTO.SupplierID,
		ContractTitle:   createDTO.ContractTitle,
		ContractType:    createDTO.ContractType,
		SigningDate:     createDTO.SigningDate,
		DeliveryAddress: createDTO.DeliveryAddress,
		WarrantyPeriod:  createDTO.WarrantyPeriod,
		PaymentTerms:    createDTO.PaymentTerms,
		TotalAmount:     calculateTotalAmount(createDTO.Items),
		Status:          constants.ContractStagePurchaseReview, // 直接进入采购负责人审批阶段
		CreatedBy:       createdBy,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// 创建合同明细
	for _, itemDTO := range createDTO.Items {
		// 处理产品ID：如果前端没传productID，根据PN和品牌查找
		productID := itemDTO.ProductID
		if productID == nil && itemDTO.PN != "" && itemDTO.Brand != "" {
			if product, err := s.productService.GetByPNAndBrand(ctx, itemDTO.PN, itemDTO.Brand); err == nil {
				productID = &product.ID
				log.Printf("根据PN(%s)和品牌(%s)找到产品ID: %d", itemDTO.PN, itemDTO.Brand, product.ID)
			} else {
				log.Printf("根据PN(%s)和品牌(%s)未找到产品: %v", itemDTO.PN, itemDTO.Brand, err)

				// 如果没找到产品，使用明细数据创建新产品
				if itemDTO.MaterialType != "" && itemDTO.Model != "" && itemDTO.Spec != "" {
					createProductDTO := productModel.CreateProductDTO{
						MaterialType:    itemDTO.MaterialType,
						Brand:           itemDTO.Brand,
						Model:           itemDTO.Model,
						Spec:            itemDTO.Spec,
						PN:              itemDTO.PN,
						ProductCategory: inferProductCategory(itemDTO.MaterialType), // 根据物料类型推断产品类别
						Unit:            itemDTO.Unit,
						ReferencePrice:  itemDTO.ContractPrice, // 使用合同单价作为参考价格
						WarrantyPeriod:  0,                     // 默认质保期
						SupplierIDs:     []uint{},              // 空的供应商列表
						Status:          1,                     // 启用状态
						Description:     fmt.Sprintf("由采购合同 %s 自动创建", contract.ContractNo),
					}

					if newProduct, createErr := s.productService.Create(ctx, createProductDTO); createErr == nil {
						productID = &newProduct.ID
						log.Printf("成功创建新产品: PN=%s, Brand=%s, ProductID=%d, 来源合同=%s",
							itemDTO.PN, itemDTO.Brand, newProduct.ID, contract.ContractNo)
					} else {
						log.Printf("创建新产品失败: PN=%s, Brand=%s, 错误=%v", itemDTO.PN, itemDTO.Brand, createErr)
					}
				} else {
					log.Printf("明细数据不完整，无法创建产品: PN=%s, Brand=%s, MaterialType=%s, Model=%s, Spec=%s",
						itemDTO.PN, itemDTO.Brand, itemDTO.MaterialType, itemDTO.Model, itemDTO.Spec)
				}
			}
		}

		item := model.PurchaseContractItem{
			InquiryItemID:    itemDTO.InquiryItemID,
			ProductID:        productID,
			MaterialType:     itemDTO.MaterialType,
			Model:            itemDTO.Model,
			Brand:            itemDTO.Brand,
			PN:               itemDTO.PN,
			Spec:             itemDTO.Spec,
			Unit:             itemDTO.Unit,
			ContractQuantity: itemDTO.ContractQuantity,
			ContractPrice:    itemDTO.ContractPrice,
			ContractAmount:   itemDTO.ContractAmount,
			Remark:           itemDTO.Remark,
			CreatedAt:        time.Now(),
			UpdatedAt:        time.Now(),
		}
		contract.Items = append(contract.Items, item)

		// 如果找到了产品ID，检查该产品的单位是否为空，为空时才更新
		if productID != nil && itemDTO.Unit != "" {
			// 先查询产品信息，检查单位是否为空
			if product, err := s.productService.GetByID(ctx, *productID); err == nil {
				// 只有当产品的单位为空时才进行更新
				if product.Unit == "" {
					if updateErr := s.productService.UpdateUnit(ctx, *productID, itemDTO.Unit); updateErr != nil {
						log.Printf("更新产品ID(%d)单位失败: %v", *productID, updateErr)
						// 更新单位失败不影响合同创建，只记录日志
					} else {
						log.Printf("成功更新产品ID(%d)的单位为: %s（原单位为空）", *productID, itemDTO.Unit)
					}
				} else {
					log.Printf("产品ID(%d)已有单位(%s)，跳过更新", *productID, product.Unit)
				}
			} else {
				log.Printf("查询产品ID(%d)信息失败: %v", *productID, err)
			}
		}
	}

	// 保存合同
	if err := s.repo.Create(ctx, contract); err != nil {
		return nil, fmt.Errorf("创建合同失败: %w", err)
	}

	// 记录创建历史
	operatorName := s.userHelper.GetUserRealName(ctx, createdBy)
	history := &model.PurchaseApprovalHistory{
		BusinessType:   constants.BusinessTypePurchaseContract,
		BusinessID:     contract.ID,
		PreviousStatus: "",
		NewStatus:      constants.ContractStageDraft,
		Action:         constants.ActionCreate,
		OperatorID:     createdBy,
		OperatorName:   operatorName,
		OperationTime:  time.Now(),
		Comments:       "创建采购合同",
		CreatedAt:      time.Now(),
	}

	// 创建历史记录
	if err := s.repo.CreateStatusHistory(ctx, history); err != nil {
		fmt.Printf("[ERROR] 记录采购合同状态历史失败: %v\n", err)
		// 不返回错误，因为合同已经创建成功
	}

	// 启动工作流
	if s.temporalClient != nil {
		workflowOptions := client.StartWorkflowOptions{
			ID:        fmt.Sprintf("purchase_contract_%d", contract.ID),
			TaskQueue: constants.TaskQueuePurchaseContract,
		}

		// 准备工作流输入参数
		input := workflow.PurchaseContractWorkflowInput{
			ContractID:   contract.ID,
			ContractNo:   contract.ContractNo,
			RequesterID:  createdBy,
			SupplierID:   contract.SupplierID,
			TotalAmount:  contract.TotalAmount,
			ContractType: contract.ContractType,
		}

		// 启动工作流
		_, err = s.temporalClient.ExecuteWorkflow(ctx, workflowOptions, workflow.PurchaseContractApprovalWorkflow, input)
		if err != nil {
			// 记录错误但不阻止合同创建
			log.Printf("[ERROR] 启动合同审批工作流失败: %v", err)
		}
	}

	return contract, nil
}

// GetPurchaseContractByID 根据ID获取采购合同
func (s *purchaseContractService) GetPurchaseContractByID(ctx context.Context, id uint) (*dto.PurchaseContractDetailDTO, error) {
	contract, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrPurchaseContractNotFound
		}
		return nil, err
	}

	// 获取关联信息
	if contract.SupplierID != 0 {
		supplier, err := s.supplierService.GetSupplierByID(ctx, contract.SupplierID)
		if err == nil && supplier != nil {
			contract.SupplierName = supplier.SupplierName // 使用SupplierName字段
		}
	}

	// 获取创建人和更新人信息
	contract.CreatorName = s.userHelper.GetUserRealName(ctx, contract.CreatedBy)

	if contract.UpdatedBy != nil {
		contract.UpdaterName = s.userHelper.GetUserRealName(ctx, *contract.UpdatedBy)
	}

	// 构建返回对象
	detailDTO := &dto.PurchaseContractDetailDTO{
		PurchaseContract: contract,
	}

	// 获取项目名称
	if contract.ProjectID != nil && *contract.ProjectID > 0 {
		project, err := s.projectService.GetProjectByID(ctx, *contract.ProjectID)
		if err == nil && project != nil {
			detailDTO.ProjectName = project.ProjectName
		}
	}

	// 获取我方公司名称
	if contract.OurCompanyID > 0 {
		company, err := s.companyService.GetCompanyByID(ctx, contract.OurCompanyID)
		if err == nil && company != nil {
			detailDTO.OurCompanyName = company.CompanyName
		}
	}

	return detailDTO, nil
}

// GetPurchaseContractByContractNo 根据合同编号获取采购合同
func (s *purchaseContractService) GetPurchaseContractByContractNo(ctx context.Context, contractNo string) (*dto.PurchaseContractDetailDTO, error) {
	contract, err := s.repo.GetByContractNo(ctx, contractNo)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrPurchaseContractNotFound
		}
		return nil, err
	}

	return s.GetPurchaseContractByID(ctx, contract.ID)
}

// ListPurchaseContracts 获取采购合同列表
func (s *purchaseContractService) ListPurchaseContracts(ctx context.Context, query dto.PurchaseContractListQuery) (*dto.PurchaseContractListResult, error) {
	contracts, total, err := s.repo.List(ctx, query.PurchaseContractFilter, query.Page, query.PageSize)
	if err != nil {
		return nil, err
	}

	// 加载关联信息
	for _, contract := range contracts {
		if contract.SupplierID != 0 {
			supplier, err := s.supplierService.GetSupplierByID(ctx, contract.SupplierID)
			if err == nil && supplier != nil {
				contract.SupplierName = supplier.SupplierName // 使用SupplierName字段
			}
		}

		// 获取项目名称
		if contract.ProjectID != nil && *contract.ProjectID > 0 {
			project, err := s.projectService.GetProjectByID(ctx, *contract.ProjectID)
			if err == nil && project != nil {
				contract.ProjectName = project.ProjectName
			}
		}

		// 获取我方公司名称
		if contract.OurCompanyID > 0 {
			company, err := s.companyService.GetCompanyByID(ctx, contract.OurCompanyID)
			if err == nil && company != nil {
				contract.OurCompanyName = company.CompanyName
			}
		}

		// 获取关联询价单号
		if contract.InquiryID != nil && *contract.InquiryID > 0 {
			inquiry, err := s.inquiryService.GetPurchaseInquiryByID(ctx, *contract.InquiryID)
			if err == nil && inquiry != nil {
				contract.InquiryNo = inquiry.InquiryNo
			}
		}

		// 获取创建人信息
		contract.CreatorName = s.userHelper.GetUserRealName(ctx, contract.CreatedBy)
	}

	return &dto.PurchaseContractListResult{
		Total: total,
		List:  contracts,
	}, nil
}

// UpdatePurchaseContract 更新采购合同
func (s *purchaseContractService) UpdatePurchaseContract(ctx context.Context, updateDTO dto.UpdatePurchaseContractDTO) (*model.PurchaseContract, error) {
	// 检查合同是否存在
	existingContract, err := s.repo.GetByID(ctx, updateDTO.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrPurchaseContractNotFound
		}
		return nil, err
	}

	// 验证供应商是否存在
	if _, err := s.supplierService.GetSupplierByID(ctx, updateDTO.SupplierID); err != nil {
		return nil, ErrContractSupplierNotFound
	}

	// 更新合同信息
	existingContract.InquiryID = updateDTO.InquiryID
	existingContract.ProjectID = updateDTO.ProjectID
	existingContract.OurCompanyID = updateDTO.OurCompanyID
	existingContract.SupplierID = updateDTO.SupplierID
	existingContract.ContractTitle = updateDTO.ContractTitle
	existingContract.ContractType = updateDTO.ContractType
	existingContract.SigningDate = updateDTO.SigningDate
	existingContract.DeliveryAddress = updateDTO.DeliveryAddress
	existingContract.PaymentTerms = updateDTO.PaymentTerms
	existingContract.TotalAmount = updateDTO.TotalAmount
	existingContract.UpdatedBy = &updateDTO.UpdatedBy
	existingContract.UpdatedAt = time.Now()

	// 更新明细
	existingContract.Items = []model.PurchaseContractItem{}
	for _, itemDTO := range updateDTO.Items {
		item := model.PurchaseContractItem{
			ID:               itemDTO.ID,
			ContractID:       existingContract.ID,
			InquiryItemID:    itemDTO.InquiryItemID,
			ProductID:        itemDTO.ProductID,
			MaterialType:     itemDTO.MaterialType,
			Model:            itemDTO.Model,
			Brand:            itemDTO.Brand,
			PN:               itemDTO.PN,
			Spec:             itemDTO.Spec,
			Unit:             itemDTO.Unit,
			ContractQuantity: itemDTO.ContractQuantity,
			ContractPrice:    itemDTO.ContractPrice,
			ContractAmount:   itemDTO.ContractAmount,
			Remark:           itemDTO.Remark,
			CreatedAt:        time.Now(),
			UpdatedAt:        time.Now(),
		}
		existingContract.Items = append(existingContract.Items, item)
	}

	// 保存更新
	if err := s.repo.Update(ctx, existingContract); err != nil {
		return nil, fmt.Errorf("更新合同失败: %w", err)
	}

	// 记录更新历史
	operatorName := s.userHelper.GetUserRealName(ctx, updateDTO.UpdatedBy)
	history := &model.PurchaseApprovalHistory{
		BusinessType:   constants.BusinessTypePurchaseContract,
		BusinessID:     existingContract.ID,
		PreviousStatus: existingContract.Status,
		NewStatus:      existingContract.Status,
		Action:         constants.ActionUpdate,
		OperatorID:     updateDTO.UpdatedBy,
		OperatorName:   operatorName,
		OperationTime:  time.Now(),
		Comments:       "更新采购合同信息",
		CreatedAt:      time.Now(),
	}

	// 创建历史记录
	if err := s.repo.CreateStatusHistory(ctx, history); err != nil {
		fmt.Printf("[ERROR] 记录采购合同状态历史失败: %v\n", err)
		// 不返回错误，因为合同已经更新成功
	}

	return existingContract, nil
}

// SubmitPurchaseContract 提交采购合同
func (s *purchaseContractService) SubmitPurchaseContract(ctx context.Context, id uint, updatedBy uint) error {
	// 检查合同是否存在
	contract, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrPurchaseContractNotFound
		}
		return err
	}

	// 只有草稿状态的合同才能提交
	if contract.Status != constants.ContractStageDraft {
		return ErrContractSubmitNotAllowed
	}

	// 更新状态为已提交
	err = s.repo.UpdateStatus(ctx, id, constants.ContractStagePurchaseReview, updatedBy)
	if err != nil {
		return err
	}

	// 记录提交历史
	operatorName := s.userHelper.GetUserRealName(ctx, updatedBy)
	history := &model.PurchaseApprovalHistory{
		BusinessType:   constants.BusinessTypePurchaseContract,
		BusinessID:     id,
		PreviousStatus: contract.Status,
		NewStatus:      constants.ContractStagePurchaseReview,
		Action:         constants.ActionSubmit,
		OperatorID:     updatedBy,
		OperatorName:   operatorName,
		OperationTime:  time.Now(),
		Comments:       "提交采购合同审批",
		CreatedAt:      time.Now(),
	}

	// 创建历史记录
	if err := s.repo.CreateStatusHistory(ctx, history); err != nil {
		fmt.Printf("[ERROR] 记录采购合同状态历史失败: %v\n", err)
	}

	// 启动工作流
	if s.temporalClient != nil {
		workflowOptions := client.StartWorkflowOptions{
			ID:        fmt.Sprintf("purchase_contract_%d", id),
			TaskQueue: constants.TaskQueuePurchaseContract,
		}

		// 准备工作流输入参数
		input := workflow.PurchaseContractWorkflowInput{
			ContractID:   id,
			ContractNo:   contract.ContractNo,
			RequesterID:  contract.CreatedBy,
			SupplierID:   contract.SupplierID,
			TotalAmount:  contract.TotalAmount,
			ContractType: contract.ContractType,
		}

		// 启动工作流
		_, err = s.temporalClient.ExecuteWorkflow(ctx, workflowOptions, workflow.PurchaseContractApprovalWorkflow, input)
		if err != nil {
			return fmt.Errorf("启动合同审批工作流失败: %w", err)
		}
	}

	return nil
}

// ApprovePurchaseContract 审批采购合同
func (s *purchaseContractService) ApprovePurchaseContract(ctx context.Context, approvalDTO dto.ContractApprovalDTO) error {
	// 验证动作有效性
	if approvalDTO.Action != constants.ActionApprove && approvalDTO.Action != constants.ActionReject {
		return fmt.Errorf("无效的审批动作: %s", approvalDTO.Action)
	}

	// 检查合同是否存在
	contract, err := s.repo.GetByID(ctx, approvalDTO.ContractID)
	if err != nil {
		return ErrPurchaseContractNotFound
	}

	// 检查当前阶段是否匹配
	if contract.Status != approvalDTO.CurrentStage {
		return ErrInvalidContractApprovalStage
	}

	// 优先从JWT获取用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		// 如果无法从JWT获取，尝试使用DTO中的ApproverID
		if approvalDTO.ApproverID > 0 {
			userID = approvalDTO.ApproverID
		} else {
			// 如果两种方式都无法获取用户ID，则返回错误
			return fmt.Errorf("无法获取当前用户信息")
		}
	}

	// 构造审批信号数据
	approvalSignal := workflow.ContractApprovalSignal{
		ApproverID:   userID,
		Action:       approvalDTO.Action,
		Comments:     approvalDTO.Comments,
		CurrentStage: approvalDTO.CurrentStage,
		Timestamp:    time.Now(),
	}

	// 发送审批信号到工作流
	workflowID := fmt.Sprintf("purchase_contract_%d", approvalDTO.ContractID)
	err = s.temporalClient.SignalWorkflow(ctx, workflowID, "", workflow.SignalNameContractApproval, approvalSignal)
	if err != nil {
		return fmt.Errorf("发送审批信号失败: %w", err)
	}

	return nil
}

// RollbackPurchaseContract 回退采购合同审批
func (s *purchaseContractService) RollbackPurchaseContract(ctx context.Context, rollbackDTO dto.ContractRollbackDTO) error {
	// 检查合同是否存在
	contract, err := s.repo.GetByID(ctx, rollbackDTO.ContractID)
	if err != nil {
		return ErrPurchaseContractNotFound
	}

	// 检查当前阶段是否匹配
	if contract.Status != rollbackDTO.CurrentStage {
		return ErrInvalidContractApprovalStage
	}

	// 从JWT获取用户ID作为回退人ID，如果无法获取则使用DTO中提供的ApproverID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		// 回退操作允许使用传入的审批人ID作为备选
		userID = rollbackDTO.ApproverID
	}

	// 构造回退信号数据
	rollbackSignal := workflow.ContractRollbackSignal{
		ApproverID:   userID,
		RollbackTo:   rollbackDTO.RollbackTo,
		CurrentStage: rollbackDTO.CurrentStage,
		Comments:     rollbackDTO.Comments,
		Timestamp:    time.Now(),
	}

	// 发送回退信号到工作流
	workflowID := fmt.Sprintf("purchase_contract_%d", rollbackDTO.ContractID)
	err = s.temporalClient.SignalWorkflow(ctx, workflowID, "", workflow.SignalNameContractRollback, rollbackSignal)
	if err != nil {
		return fmt.Errorf("发送回退信号失败: %w", err)
	}

	return nil
}

// CancelPurchaseContract 取消采购合同
func (s *purchaseContractService) CancelPurchaseContract(ctx context.Context, id uint, updatedBy uint) error {
	// 检查合同是否存在
	contract, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrPurchaseContractNotFound
		}
		return err
	}

	// 检查状态是否允许取消
	if contract.Status == constants.ContractStageCompleted {
		return ErrInvalidContractStatus
	}

	// 更新状态为已取消
	if err := s.repo.UpdateStatus(ctx, id, constants.ContractStageCancelled, updatedBy); err != nil {
		return err
	}

	// 记录取消历史
	operatorName := s.userHelper.GetUserRealName(ctx, updatedBy)
	history := &model.PurchaseApprovalHistory{
		BusinessType:   constants.BusinessTypePurchaseContract,
		BusinessID:     id,
		PreviousStatus: contract.Status,
		NewStatus:      constants.ContractStageCancelled,
		Action:         constants.ActionCancel,
		OperatorID:     updatedBy,
		OperatorName:   operatorName,
		OperationTime:  time.Now(),
		Comments:       "取消采购合同",
		CreatedAt:      time.Now(),
	}

	// 创建历史记录
	if err := s.repo.CreateStatusHistory(ctx, history); err != nil {
		fmt.Printf("[ERROR] 记录采购合同状态历史失败: %v\n", err)
		// 不返回错误，因为合同已经取消成功
	}

	return nil
}

// DeletePurchaseContract 删除采购合同
func (s *purchaseContractService) DeletePurchaseContract(ctx context.Context, id uint, deletedBy uint) error {
	// 检查合同是否存在
	contract, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrPurchaseContractNotFound
		}
		return err
	}

	// 只有草稿状态的合同才能删除
	if contract.Status != constants.ContractStageDraft {
		return ErrContractDeleteNotAllowed
	}

	// 记录删除历史
	operatorName := s.userHelper.GetUserRealName(ctx, deletedBy)
	history := &model.PurchaseApprovalHistory{
		BusinessType:   constants.BusinessTypePurchaseContract,
		BusinessID:     id,
		PreviousStatus: contract.Status,
		NewStatus:      "deleted",
		Action:         constants.ActionDelete,
		OperatorID:     deletedBy,
		OperatorName:   operatorName,
		OperationTime:  time.Now(),
		Comments:       "删除采购合同",
		CreatedAt:      time.Now(),
	}

	// 创建历史记录
	if err := s.repo.CreateStatusHistory(ctx, history); err != nil {
		fmt.Printf("[ERROR] 记录采购合同状态历史失败: %v\n", err)
		// 不返回错误，继续执行删除操作
	}

	// 删除合同
	return s.repo.Delete(ctx, id)
}

// GetPurchaseContractHistory 获取采购合同历史记录
func (s *purchaseContractService) GetPurchaseContractHistory(ctx context.Context, id uint) ([]dto.ContractHistoryDTO, error) {
	// 检查合同是否存在
	exists, err := s.repo.Exists(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("检查合同是否存在失败: %w", err)
	}
	if !exists {
		return nil, ErrPurchaseContractNotFound
	}

	// 从仓库获取历史记录
	histories, err := s.repo.GetContractHistory(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("获取合同历史记录失败: %w", err)
	}

	// 构造返回结果
	result := make([]dto.ContractHistoryDTO, 0, len(histories))
	for _, h := range histories {
		historyDTO := dto.ContractHistoryDTO{
			ID:             h.ID,
			BusinessType:   h.BusinessType,
			BusinessID:     h.BusinessID,
			PreviousStatus: h.PreviousStatus,
			NewStatus:      h.NewStatus,
			Action:         h.Action,
			OperatorID:     h.OperatorID,
			OperatorName:   h.OperatorName,
			OperationTime:  h.OperationTime,
			Comments:       h.Comments,
			CreatedAt:      h.CreatedAt,
		}
		result = append(result, historyDTO)
	}

	return result, nil
}

// calculateTotalAmount 计算合同总金额
func calculateTotalAmount(items []dto.CreateContractItemDTO) float64 {
	var total float64
	for _, item := range items {
		total += item.ContractAmount
	}
	return total
}

// GetInquiryItemsPurchaseStats 获取询价明细的采购统计信息
func (s *purchaseContractService) GetInquiryItemsPurchaseStats(ctx context.Context, inquiryID uint) ([]dto.InquiryItemPurchaseStatsDTO, error) {
	// 获取询价明细信息
	inquiryItems, err := s.repo.GetInquiryItemsByInquiryID(ctx, inquiryID)
	if err != nil {
		return nil, fmt.Errorf("获取询价明细失败: %w", err)
	}

	var result []dto.InquiryItemPurchaseStatsDTO
	for _, item := range inquiryItems {
		// 计算已采购数量和金额（排除已取消的合同）
		purchasedQuantity, purchasedAmount, err := s.repo.GetPurchasedStatsForInquiryItem(ctx, item.ID)
		if err != nil {
			return nil, fmt.Errorf("获取采购统计失败: %w", err)
		}

		// 计算询价预算
		inquiryBudget := float64(0)
		if item.BudgetPrice != nil {
			inquiryBudget = *item.BudgetPrice * float64(item.CurrentInquiryQuantity)
		}

		// 计算未采购数量和预算
		unpurchasedQuantity := item.CurrentInquiryQuantity - purchasedQuantity
		if unpurchasedQuantity < 0 {
			unpurchasedQuantity = 0
		}

		unpurchasedBudget := float64(0)
		if item.BudgetPrice != nil && unpurchasedQuantity > 0 {
			unpurchasedBudget = *item.BudgetPrice * float64(unpurchasedQuantity)
		}

		statsDTO := dto.InquiryItemPurchaseStatsDTO{
			InquiryItemID:       item.ID,
			MaterialType:        item.MaterialType,
			Model:               item.Model,
			Brand:               item.Brand,
			PN:                  item.PN,
			Spec:                item.Specifications,
			Unit:                item.Unit,
			InquiryQuantity:     item.CurrentInquiryQuantity,
			InquiryBudget:       inquiryBudget,
			PurchasedQuantity:   purchasedQuantity,
			PurchasedAmount:     purchasedAmount,
			UnpurchasedQuantity: unpurchasedQuantity,
			UnpurchasedBudget:   unpurchasedBudget,
		}

		result = append(result, statsDTO)
	}

	return result, nil
}
