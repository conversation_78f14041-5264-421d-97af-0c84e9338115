package service

import (
	"backend/internal/common/utils"
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/model"
	"backend/internal/modules/purchase/repository"
	"context"
	"errors"
	"fmt"
)

var (
	// ErrCompanyNotFound 公司不存在错误
	ErrCompanyNotFound = errors.New("公司不存在")

	// ErrDuplicateCompanyCode 公司编码重复错误
	ErrDuplicateCompanyCode = errors.New("公司编码已存在")

	// ErrInvalidCompanyData 无效的公司数据
	ErrInvalidCompanyData = errors.New("无效的公司数据")
)

// CompanyService 公司信息服务接口
type CompanyService interface {
	// CreateCompany 创建公司信息
	CreateCompany(ctx context.Context, dto dto.CreateCompanyDTO) (*model.Company, error)

	// UpdateCompany 更新公司信息
	UpdateCompany(ctx context.Context, dto dto.UpdateCompanyDTO) (*model.Company, error)

	// GetCompanyByID 根据ID获取公司信息
	GetCompanyByID(ctx context.Context, id uint) (*model.Company, error)

	// GetCompanyByCode 根据公司编码获取公司信息
	GetCompanyByCode(ctx context.Context, code string) (*model.Company, error)

	// ListCompanies 获取公司信息列表
	ListCompanies(ctx context.Context, query dto.CompanyListQuery) (*dto.CompanyListResult, error)

	// DeleteCompany 删除公司信息
	DeleteCompany(ctx context.Context, id uint) error

	// GenerateCompanyCode 生成公司编码
	GenerateCompanyCode(ctx context.Context) (string, error)
}

// companyService 公司信息服务实现
type companyService struct {
	repo repository.CompanyRepository
}

// NewCompanyService 创建公司信息服务实例
func NewCompanyService(repo repository.CompanyRepository) CompanyService {
	// 初始化编码生成器
	utils.InitCodeGenerator()
	return &companyService{repo: repo}
}

// GenerateCompanyCode 生成公司编码
func (s *companyService) GenerateCompanyCode(ctx context.Context) (string, error) {
	// 定义公司编码检查函数
	codeExistChecker := func(ctx context.Context, code string) (bool, error) {
		company, err := s.repo.GetByCompanyCode(ctx, code)
		if err != nil {
			return false, nil
		}
		return company != nil, nil
	}

	// 设置公司编码生成选项
	options := utils.CodeGeneratorOptions{
		Prefix:       "C",
		DateFormat:   "20060102",
		RandomLength: 4,
		Delimiter:    "-",
	}

	// 生成唯一编码
	return utils.GenerateUniqueCode(ctx, codeExistChecker, options)
}

// CreateCompany 创建公司信息
func (s *companyService) CreateCompany(ctx context.Context, createDto dto.CreateCompanyDTO) (*model.Company, error) {
	var companyCode string
	var err error

	// 如果提供了公司编码，检查是否已存在
	if createDto.CompanyCode != "" {
		companyCode = createDto.CompanyCode
		existingCompany, err := s.repo.GetByCompanyCode(ctx, companyCode)
		if err == nil && existingCompany != nil {
			return nil, ErrDuplicateCompanyCode
		}
	} else {
		// 如果未提供公司编码，自动生成
		companyCode, err = s.GenerateCompanyCode(ctx)
		if err != nil {
			return nil, fmt.Errorf("生成公司编码失败: %w", err)
		}
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		// 如果获取不到用户ID，使用默认值0或者DTO中的值
		if createDto.CreatedBy > 0 {
			userID = createDto.CreatedBy
		} else {
			userID = 0
		}
	}

	// 创建公司实体
	company := &model.Company{
		CompanyCode:       companyCode,
		CompanyName:       createDto.CompanyName,
		ShortName:         createDto.ShortName,
		LegalPerson:       createDto.LegalPerson,
		UnifiedCreditCode: createDto.UnifiedCreditCode,
		TaxNumber:         createDto.TaxNumber,
		RegisteredAddress: createDto.RegisteredAddress,
		BusinessAddress:   createDto.BusinessAddress,
		BankName:          createDto.BankName,
		BankAccount:       createDto.BankAccount,
		ContactPerson:     createDto.ContactPerson,
		ContactPhone:      createDto.ContactPhone,
		InvoiceTitle:      createDto.InvoiceTitle,
		InvoiceAddress:    createDto.InvoiceAddress,
		Status:            createDto.Status,
		CreatedBy:         userID,
	}

	// 保存到数据库
	if err := s.repo.Create(ctx, company); err != nil {
		return nil, err
	}

	return company, nil
}

// UpdateCompany 更新公司信息
func (s *companyService) UpdateCompany(ctx context.Context, updateDto dto.UpdateCompanyDTO) (*model.Company, error) {
	// 检查公司是否存在
	company, err := s.repo.GetByID(ctx, updateDto.ID)
	if err != nil {
		return nil, ErrCompanyNotFound
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		// 如果获取不到用户ID，使用默认值0或者DTO中的值
		if updateDto.UpdatedBy > 0 {
			userID = updateDto.UpdatedBy
		} else {
			userID = 0
		}
	}

	// 更新公司信息
	if updateDto.CompanyName != "" {
		company.CompanyName = updateDto.CompanyName
	}
	if updateDto.ShortName != "" {
		company.ShortName = updateDto.ShortName
	}
	if updateDto.LegalPerson != "" {
		company.LegalPerson = updateDto.LegalPerson
	}
	if updateDto.UnifiedCreditCode != "" {
		company.UnifiedCreditCode = updateDto.UnifiedCreditCode
	}
	if updateDto.TaxNumber != "" {
		company.TaxNumber = updateDto.TaxNumber
	}
	if updateDto.RegisteredAddress != "" {
		company.RegisteredAddress = updateDto.RegisteredAddress
	}
	if updateDto.BusinessAddress != "" {
		company.BusinessAddress = updateDto.BusinessAddress
	}
	if updateDto.BankName != "" {
		company.BankName = updateDto.BankName
	}
	if updateDto.BankAccount != "" {
		company.BankAccount = updateDto.BankAccount
	}
	if updateDto.ContactPerson != "" {
		company.ContactPerson = updateDto.ContactPerson
	}
	if updateDto.ContactPhone != "" {
		company.ContactPhone = updateDto.ContactPhone
	}
	if updateDto.InvoiceTitle != "" {
		company.InvoiceTitle = updateDto.InvoiceTitle
	}
	if updateDto.InvoiceAddress != "" {
		company.InvoiceAddress = updateDto.InvoiceAddress
	}
	if updateDto.Status != nil {
		company.Status = *updateDto.Status
	}
	company.UpdatedBy = userID

	// 保存到数据库
	if err := s.repo.Update(ctx, company); err != nil {
		return nil, err
	}

	return company, nil
}

// GetCompanyByID 根据ID获取公司信息
func (s *companyService) GetCompanyByID(ctx context.Context, id uint) (*model.Company, error) {
	company, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, ErrCompanyNotFound
	}
	return company, nil
}

// GetCompanyByCode 根据公司编码获取公司信息
func (s *companyService) GetCompanyByCode(ctx context.Context, code string) (*model.Company, error) {
	company, err := s.repo.GetByCompanyCode(ctx, code)
	if err != nil {
		return nil, ErrCompanyNotFound
	}
	return company, nil
}

// ListCompanies 获取公司信息列表
func (s *companyService) ListCompanies(ctx context.Context, query dto.CompanyListQuery) (*dto.CompanyListResult, error) {
	companies, total, err := s.repo.List(ctx, query.CompanyFilter, query.PaginationOptions)
	if err != nil {
		return nil, err
	}

	return &dto.CompanyListResult{
		Total: total,
		List:  companies,
	}, nil
}

// DeleteCompany 删除公司信息
func (s *companyService) DeleteCompany(ctx context.Context, id uint) error {
	// 检查公司是否存在
	_, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return ErrCompanyNotFound
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		// 如果获取不到用户ID，使用默认值0
		userID = 0
	}

	// 删除公司
	return s.repo.Delete(ctx, id, userID)
}
