package service

import (
	"backend/internal/common/utils"
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/model"
	"backend/internal/modules/purchase/repository"
	"context"
	"errors"
	"fmt"
)

var (
	// ErrProjectNotFound 项目不存在错误
	ErrProjectNotFound = errors.New("项目不存在")

	// ErrDuplicateProjectCode 项目编号重复错误
	ErrDuplicateProjectCode = errors.New("项目编号已存在")

	// ErrInvalidProjectData 无效的项目数据
	ErrInvalidProjectData = errors.New("无效的项目数据")
)

// ProjectService 项目信息服务接口
type ProjectService interface {
	// CreateProject 创建项目信息
	CreateProject(ctx context.Context, dto dto.CreateProjectDTO) (*model.Project, error)

	// UpdateProject 更新项目信息
	UpdateProject(ctx context.Context, dto dto.UpdateProjectDTO) (*model.Project, error)

	// GetProjectByID 根据ID获取项目信息
	GetProjectByID(ctx context.Context, id uint) (*model.Project, error)

	// GetProjectByCode 根据项目编号获取项目信息
	GetProjectByCode(ctx context.Context, code string) (*model.Project, error)

	// ListProjects 获取项目信息列表
	ListProjects(ctx context.Context, query dto.ProjectListQuery) (*dto.ProjectListResult, error)

	// DeleteProject 删除项目信息
	DeleteProject(ctx context.Context, id uint) error

	// GenerateProjectCode 生成项目编号
	GenerateProjectCode(ctx context.Context) (string, error)
}

// projectService 项目信息服务实现
type projectService struct {
	repo repository.ProjectRepository
}

// NewProjectService 创建项目信息服务实例
func NewProjectService(repo repository.ProjectRepository) ProjectService {
	// 初始化编码生成器
	utils.InitCodeGenerator()
	return &projectService{repo: repo}
}

// GenerateProjectCode 生成项目编号
func (s *projectService) GenerateProjectCode(ctx context.Context) (string, error) {
	// 定义项目编号检查函数
	codeExistChecker := func(ctx context.Context, code string) (bool, error) {
		project, err := s.repo.GetByProjectCode(ctx, code)
		if err != nil {
			return false, nil
		}
		return project != nil, nil
	}

	// 设置项目编号生成选项
	options := utils.CodeGeneratorOptions{
		Prefix:       "P",
		DateFormat:   "20060102",
		RandomLength: 4,
		Delimiter:    "-",
	}

	// 生成唯一编号
	return utils.GenerateUniqueCode(ctx, codeExistChecker, options)
}

// CreateProject 创建项目信息
func (s *projectService) CreateProject(ctx context.Context, createDto dto.CreateProjectDTO) (*model.Project, error) {
	var projectCode string
	var err error

	// 如果提供了项目编号，检查是否已存在
	if createDto.ProjectCode != "" {
		projectCode = createDto.ProjectCode
		existingProject, err := s.repo.GetByProjectCode(ctx, projectCode)
		if err == nil && existingProject != nil {
			return nil, ErrDuplicateProjectCode
		}
	} else {
		// 如果未提供项目编号，自动生成
		projectCode, err = s.GenerateProjectCode(ctx)
		if err != nil {
			return nil, fmt.Errorf("生成项目编号失败: %w", err)
		}
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		// 如果获取不到用户ID，使用默认值0或者DTO中的值
		if createDto.CreatedBy > 0 {
			userID = createDto.CreatedBy
		} else {
			userID = 0
		}
	}

	// 创建项目实体
	project := &model.Project{
		ProjectCode:    projectCode,
		ProjectName:    createDto.ProjectName,
		CustomerName:   createDto.CustomerName,
		ContractInfo:   createDto.ContractInfo,
		ProjectManager: createDto.ProjectManager,
		ProjectAddress: createDto.ProjectAddress,
		ContactPerson:  createDto.ContactPerson,
		ContactPhone:   createDto.ContactPhone,
		ContactEmail:   createDto.ContactEmail,
		ProjectStatus:  createDto.ProjectStatus,
		StartDate:      createDto.StartDate,
		EndDate:        createDto.EndDate,
		Description:    createDto.Description,
		CreatedBy:      userID,
	}

	// 保存到数据库
	if err := s.repo.Create(ctx, project); err != nil {
		return nil, err
	}

	return project, nil
}

// UpdateProject 更新项目信息
func (s *projectService) UpdateProject(ctx context.Context, updateDto dto.UpdateProjectDTO) (*model.Project, error) {
	// 检查项目是否存在
	project, err := s.repo.GetByID(ctx, updateDto.ID)
	if err != nil {
		return nil, ErrProjectNotFound
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		// 如果获取不到用户ID，使用默认值0或者DTO中的值
		if updateDto.UpdatedBy > 0 {
			userID = updateDto.UpdatedBy
		} else {
			userID = 0
		}
	}

	// 更新项目信息
	if updateDto.ProjectName != "" {
		project.ProjectName = updateDto.ProjectName
	}
	if updateDto.CustomerName != "" {
		project.CustomerName = updateDto.CustomerName
	}
	if updateDto.ContractInfo != "" {
		project.ContractInfo = updateDto.ContractInfo
	}
	if updateDto.ProjectManager != "" {
		project.ProjectManager = updateDto.ProjectManager
	}
	if updateDto.ProjectAddress != "" {
		project.ProjectAddress = updateDto.ProjectAddress
	}
	if updateDto.ContactPerson != "" {
		project.ContactPerson = updateDto.ContactPerson
	}
	if updateDto.ContactPhone != "" {
		project.ContactPhone = updateDto.ContactPhone
	}
	if updateDto.ContactEmail != "" {
		project.ContactEmail = updateDto.ContactEmail
	}
	if updateDto.ProjectStatus != nil {
		project.ProjectStatus = *updateDto.ProjectStatus
	}
	if updateDto.StartDate != nil {
		project.StartDate = updateDto.StartDate
	}
	if updateDto.EndDate != nil {
		project.EndDate = updateDto.EndDate
	}
	if updateDto.Description != "" {
		project.Description = updateDto.Description
	}
	project.UpdatedBy = userID

	// 保存到数据库
	if err := s.repo.Update(ctx, project); err != nil {
		return nil, err
	}

	return project, nil
}

// GetProjectByID 根据ID获取项目信息
func (s *projectService) GetProjectByID(ctx context.Context, id uint) (*model.Project, error) {
	project, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, ErrProjectNotFound
	}
	return project, nil
}

// GetProjectByCode 根据项目编号获取项目信息
func (s *projectService) GetProjectByCode(ctx context.Context, code string) (*model.Project, error) {
	project, err := s.repo.GetByProjectCode(ctx, code)
	if err != nil {
		return nil, ErrProjectNotFound
	}
	return project, nil
}

// ListProjects 获取项目信息列表
func (s *projectService) ListProjects(ctx context.Context, query dto.ProjectListQuery) (*dto.ProjectListResult, error) {
	projects, total, err := s.repo.List(ctx, query.ProjectFilter, query.PaginationOptions)
	if err != nil {
		return nil, err
	}

	return &dto.ProjectListResult{
		Total: total,
		List:  projects,
	}, nil
}

// DeleteProject 删除项目信息
func (s *projectService) DeleteProject(ctx context.Context, id uint) error {
	// 检查项目是否存在
	_, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return ErrProjectNotFound
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)

	if err != nil {
		// 如果获取不到用户ID，使用默认值0
		userID = 0
	}

	// 删除项目
	return s.repo.Delete(ctx, id, userID)
}
