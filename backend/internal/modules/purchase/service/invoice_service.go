package service

import (
	"backend/internal/common/utils"
	"backend/internal/common/utils/notifier"
	"backend/internal/modules/purchase/constants"
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/model"
	"backend/internal/modules/purchase/repository"
	"backend/internal/modules/user/service"
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// 定义错误
var (
	// ErrInvoiceNotFound 发票不存在
	ErrInvoiceNotFound = errors.New("发票不存在")

	// ErrInvoiceNoExists 发票号已存在
	ErrInvoiceNoExists = errors.New("发票号已存在")

	// ErrInvalidInvoiceAmount 无效的开票金额
	ErrInvalidInvoiceAmount = errors.New("无效的开票金额")

	// ErrInvoiceAmountExceedsContract 开票金额超过合同余额
	ErrInvoiceAmountExceedsContract = errors.New("开票金额超过合同余额")

	// ErrInvoiceDeleteNotAllowed 不允许删除发票
	ErrInvoiceDeleteNotAllowed = errors.New("不允许删除发票")

	// ErrInvoiceContractNotFound 合同不存在
	ErrInvoiceContractNotFound = errors.New("合同不存在")

	// ErrContractStatusInvalid 合同状态无效
	ErrContractStatusInvalid = errors.New("合同状态无效，无法开票")

	// ErrInvoiceUpdateNotAllowed 不允许更新发票
	ErrInvoiceUpdateNotAllowed = errors.New("不允许更新发票")
)

// InvoiceService 发票服务接口
type InvoiceService interface {
	// Create 创建发票
	Create(ctx context.Context, createDTO *dto.CreateInvoiceDTO) (*model.Invoice, error)

	// GetByID 根据ID获取发票
	GetByID(ctx context.Context, id uint) (*dto.InvoiceDetailDTO, error)

	// GetByInvoiceNo 根据发票号获取发票
	GetByInvoiceNo(ctx context.Context, invoiceNo string) (*dto.InvoiceDetailDTO, error)

	// Update 更新发票
	Update(ctx context.Context, id uint, updateDTO *dto.UpdateInvoiceDTO) error

	// Delete 删除发票
	Delete(ctx context.Context, id uint, deletedBy uint) error

	// List 获取发票列表
	List(ctx context.Context, query *dto.InvoiceQueryDTO) ([]*dto.InvoiceListDTO, int64, error)

	// GetByContractID 根据合同ID获取发票列表
	GetByContractID(ctx context.Context, contractID uint) ([]*model.Invoice, error)

	// ValidateInvoiceAmount 验证开票金额
	ValidateInvoiceAmount(ctx context.Context, contractID uint, invoiceAmount float64, excludeInvoiceID *uint) error

	// GetInvoiceSummaryByContractID 获取合同的发票汇总信息
	GetInvoiceSummaryByContractID(ctx context.Context, contractID uint) (*dto.InvoiceSummaryDTO, error)
}

// invoiceService 发票服务实现
type invoiceService struct {
	repo         repository.InvoiceRepository
	contractRepo repository.PurchaseContractRepository
	userHelper   *UserHelper
	notifier     *notifier.PurchaseFeishuNotifier
}

// NewInvoiceService 创建发票服务实例
func NewInvoiceService(
	repo repository.InvoiceRepository,
	contractRepo repository.PurchaseContractRepository,
	userService service.IUserService,
	notifier *notifier.PurchaseFeishuNotifier,
) InvoiceService {
	return &invoiceService{
		repo:         repo,
		contractRepo: contractRepo,
		userHelper:   NewUserHelper(userService),
		notifier:     notifier,
	}
}

// Create 创建发票
func (s *invoiceService) Create(ctx context.Context, createDTO *dto.CreateInvoiceDTO) (*model.Invoice, error) {
	// 优先从JWT获取用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		userID = createDTO.CreatedBy
	}

	// 验证发票号是否已存在
	exists, err := s.repo.ExistsByInvoiceNo(ctx, createDTO.InvoiceNo, nil)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, ErrInvoiceNoExists
	}

	// 验证合同是否存在（获取包含项目信息的详细数据）
	contract, err := s.contractRepo.GetByIDWithDetails(ctx, createDTO.ContractID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrInvoiceContractNotFound
		}
		return nil, err
	}

	// 验证合同状态（只有已审批完成的合同才能开票）
	if contract.Status != constants.ContractStageCompleted {
		return nil, ErrContractStatusInvalid
	}

	// 验证开票金额
	if err := s.ValidateInvoiceAmount(ctx, createDTO.ContractID, createDTO.InvoiceAmount, nil); err != nil {
		return nil, err
	}

	// 创建发票实体
	invoice := &model.Invoice{
		InvoiceNo:     createDTO.InvoiceNo,
		ContractID:    createDTO.ContractID,
		InvoiceAmount: createDTO.InvoiceAmount,
		Remark:        createDTO.Remark,
		CreatedBy:     userID,
		UpdatedBy:     &userID,
	}

	// 保存到数据库
	if err := s.repo.Create(ctx, invoice); err != nil {
		return nil, err
	}

	// 发送发票创建通知
	go s.sendInvoiceCreatedNotification(ctx, invoice, contract)

	return invoice, nil
}

// GetByID 根据ID获取发票
func (s *invoiceService) GetByID(ctx context.Context, id uint) (*dto.InvoiceDetailDTO, error) {
	invoice, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrInvoiceNotFound
		}
		return nil, err
	}

	return s.buildInvoiceDetailDTO(ctx, invoice)
}

// GetByInvoiceNo 根据发票号获取发票
func (s *invoiceService) GetByInvoiceNo(ctx context.Context, invoiceNo string) (*dto.InvoiceDetailDTO, error) {
	invoice, err := s.repo.GetByInvoiceNo(ctx, invoiceNo)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrInvoiceNotFound
		}
		return nil, err
	}

	return s.buildInvoiceDetailDTO(ctx, invoice)
}

// Update 更新发票
func (s *invoiceService) Update(ctx context.Context, id uint, updateDTO *dto.UpdateInvoiceDTO) error {
	// 优先从JWT获取用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		userID = updateDTO.UpdatedBy
	}

	// 获取现有发票
	invoice, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrInvoiceNotFound
		}
		return err
	}

	// 如果更新了发票号，验证新发票号是否已存在
	if updateDTO.InvoiceNo != "" && updateDTO.InvoiceNo != invoice.InvoiceNo {
		exists, err := s.repo.ExistsByInvoiceNo(ctx, updateDTO.InvoiceNo, &id)
		if err != nil {
			return err
		}
		if exists {
			return ErrInvoiceNoExists
		}
		invoice.InvoiceNo = updateDTO.InvoiceNo
	}

	// 如果更新了开票金额，验证新金额
	if updateDTO.InvoiceAmount > 0 && updateDTO.InvoiceAmount != invoice.InvoiceAmount {
		if err := s.ValidateInvoiceAmount(ctx, invoice.ContractID, updateDTO.InvoiceAmount, &id); err != nil {
			return err
		}
		invoice.InvoiceAmount = updateDTO.InvoiceAmount
	}

	// 更新其他字段
	if updateDTO.Remark != "" {
		invoice.Remark = updateDTO.Remark
	}

	invoice.UpdatedBy = &userID
	invoice.UpdatedAt = time.Now()

	return s.repo.Update(ctx, invoice)
}

// Delete 删除发票
func (s *invoiceService) Delete(ctx context.Context, id uint, deletedBy uint) error {
	// 获取发票
	_, err := s.repo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrInvoiceNotFound
		}
		return err
	}

	return s.repo.Delete(ctx, id)
}

// List 获取发票列表
func (s *invoiceService) List(ctx context.Context, query *dto.InvoiceQueryDTO) ([]*dto.InvoiceListDTO, int64, error) {
	invoices, total, err := s.repo.List(ctx, query)
	if err != nil {
		return nil, 0, err
	}

	var result []*dto.InvoiceListDTO
	for _, invoice := range invoices {
		listDTO := &dto.InvoiceListDTO{
			ID:            invoice.ID,
			InvoiceNo:     invoice.InvoiceNo,
			ContractID:    invoice.ContractID,
			InvoiceAmount: invoice.InvoiceAmount,
			Remark:        invoice.Remark,
			CreatedBy:     invoice.CreatedBy,
			CreatedAt:     invoice.CreatedAt,
			UpdatedBy:     invoice.UpdatedBy,
			UpdatedAt:     invoice.UpdatedAt,
		}

		// 填充关联信息
		if invoice.Contract != nil {
			listDTO.ContractNo = invoice.Contract.ContractNo
			if invoice.Contract.Supplier != nil {
				listDTO.SupplierName = invoice.Contract.Supplier.SupplierName
			}
			if invoice.Contract.Project != nil {
				listDTO.ProjectName = invoice.Contract.Project.ProjectName
			}
			if invoice.Contract.OurCompany != nil {
				listDTO.OurCompanyName = invoice.Contract.OurCompany.CompanyName
			}
		}

		// 填充用户名称
		listDTO.CreatorName = s.userHelper.GetUserRealName(ctx, invoice.CreatedBy)
		if invoice.UpdatedBy != nil {
			listDTO.UpdaterName = s.userHelper.GetUserRealName(ctx, *invoice.UpdatedBy)
		}

		result = append(result, listDTO)
	}

	return result, total, nil
}

// GetByContractID 根据合同ID获取发票列表
func (s *invoiceService) GetByContractID(ctx context.Context, contractID uint) ([]*model.Invoice, error) {
	return s.repo.GetByContractID(ctx, contractID)
}

// ValidateInvoiceAmount 验证开票金额
func (s *invoiceService) ValidateInvoiceAmount(ctx context.Context, contractID uint, invoiceAmount float64, excludeInvoiceID *uint) error {
	if invoiceAmount <= 0 {
		return ErrInvalidInvoiceAmount
	}

	// 获取合同信息
	contract, err := s.contractRepo.GetByID(ctx, contractID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrInvoiceContractNotFound
		}
		return err
	}

	// 获取已开票金额
	totalInvoiced, err := s.repo.GetTotalInvoiceAmountByContractID(ctx, contractID, excludeInvoiceID)
	if err != nil {
		return err
	}

	// 计算剩余可开票金额
	remainingAmount := contract.TotalAmount - totalInvoiced

	// 检查开票金额是否超过剩余金额
	if invoiceAmount > remainingAmount {
		return fmt.Errorf("%w：合同总金额%.2f，已开票金额%.2f，剩余可开票金额%.2f，本次开票金额%.2f",
			ErrInvoiceAmountExceedsContract, contract.TotalAmount, totalInvoiced, remainingAmount, invoiceAmount)
	}

	return nil
}

// GetInvoiceSummaryByContractID 获取合同的发票汇总信息
func (s *invoiceService) GetInvoiceSummaryByContractID(ctx context.Context, contractID uint) (*dto.InvoiceSummaryDTO, error) {
	return s.repo.GetInvoiceSummaryByContractID(ctx, contractID)
}

// buildInvoiceDetailDTO 构建发票详情DTO
func (s *invoiceService) buildInvoiceDetailDTO(ctx context.Context, invoice *model.Invoice) (*dto.InvoiceDetailDTO, error) {
	detailDTO := &dto.InvoiceDetailDTO{
		Invoice: invoice,
	}

	// 填充合同相关信息和金额统计
	if invoice.Contract != nil {
		detailDTO.ContractTotalAmount = invoice.Contract.TotalAmount

		// 获取已开票总金额
		totalInvoiced, err := s.repo.GetTotalInvoiceAmountByContractID(ctx, invoice.ContractID, &invoice.ID)
		if err != nil {
			return nil, err
		}

		detailDTO.TotalInvoicedAmount = totalInvoiced + invoice.InvoiceAmount // 包含当前发票
		detailDTO.RemainingAmount = detailDTO.ContractTotalAmount - detailDTO.TotalInvoicedAmount

		// 填充合同编号
		invoice.ContractNo = invoice.Contract.ContractNo

		// 填充关联名称信息
		if invoice.Contract.Supplier != nil {
			invoice.SupplierName = invoice.Contract.Supplier.SupplierName
		}
		if invoice.Contract.Project != nil {
			invoice.ProjectName = invoice.Contract.Project.ProjectName
		}
		if invoice.Contract.OurCompany != nil {
			invoice.OurCompanyName = invoice.Contract.OurCompany.CompanyName
		}

		// 清空Contract对象，不返回给前端
		invoice.Contract = nil
	}

	// 填充用户名称
	invoice.CreatorName = s.userHelper.GetUserRealName(ctx, invoice.CreatedBy)
	if invoice.UpdatedBy != nil {
		invoice.UpdaterName = s.userHelper.GetUserRealName(ctx, *invoice.UpdatedBy)
	}

	return detailDTO, nil
}

// sendInvoiceCreatedNotification 发送发票创建通知
func (s *invoiceService) sendInvoiceCreatedNotification(ctx context.Context, invoice *model.Invoice, contract *model.PurchaseContract) {
	if s.notifier == nil {
		return
	}

	// 获取创建人姓名
	creatorName := s.userHelper.GetUserRealName(ctx, invoice.CreatedBy)

	// 获取项目名称
	projectName := ""
	if contract.Project != nil {
		projectName = contract.Project.ProjectName
	}

	// 获取供应商名称
	supplierName := ""
	if contract.Supplier != nil {
		supplierName = contract.Supplier.SupplierName
	}

	// 发送发票创建专用通知
	err := s.notifier.SendInvoiceCreatedNotification(
		invoice.ID,
		invoice.InvoiceNo,
		contract.ContractNo,
		creatorName,
		projectName,
		supplierName,
		invoice.InvoiceAmount,
	)

	if err != nil {
		// 通知发送失败，记录日志但不影响主流程
		fmt.Printf("发送发票创建通知失败: %v\n", err)
	}
}
