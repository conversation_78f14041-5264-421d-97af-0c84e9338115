package controller

import (
	"backend/internal/common/utils"
	"backend/internal/modules/purchase/constants"
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/service"
	"backend/response"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// PurchaseContractController 采购合同控制器
type PurchaseContractController struct {
	service service.PurchaseContractService
}

// NewPurchaseContractController 创建采购合同控制器实例
func NewPurchaseContractController(service service.PurchaseContractService) *PurchaseContractController {
	return &PurchaseContractController{service: service}
}

// RegisterRoutes 注册路由
func (c *PurchaseContractController) RegisterRoutes(router *gin.RouterGroup) {
	// /purchase/contracts
	contractsGroup := router.Group("/contracts")
	{
		// 创建采购合同
		contractsGroup.POST("", c.CreatePurchaseContract)
		// 获取采购合同列表
		contractsGroup.GET("", c.ListPurchaseContracts)
		// 根据ID获取采购合同
		contractsGroup.GET("/:id", c.GetPurchaseContractByID)
		// 根据合同编号获取采购合同
		contractsGroup.GET("/no/:contract_no", c.GetPurchaseContractByContractNo)
		// 更新采购合同
		contractsGroup.PUT("/:id", c.UpdatePurchaseContract)
		// 提交采购合同
		contractsGroup.POST("/:id/submit", c.SubmitPurchaseContract)
		// 审批采购合同
		contractsGroup.POST("/approve", c.ApprovePurchaseContract)
		// 回退采购合同
		contractsGroup.POST("/rollback", c.RollbackPurchaseContract)
		// 取消采购合同
		contractsGroup.POST("/:id/cancel", c.CancelPurchaseContract)
		// 删除采购合同
		contractsGroup.DELETE("/:id", c.DeletePurchaseContract)
		// 获取采购合同历史记录
		contractsGroup.GET("/:id/history", c.GetPurchaseContractHistory)
		// 获取询价明细采购统计信息
		contractsGroup.GET("/inquiry/:inquiry_id/stats", c.GetInquiryItemsPurchaseStats)
	}
}

// CreatePurchaseContract 创建采购合同
// @Summary 创建采购合同
// @Description 创建新的采购合同，合同编号由前端提供
// @Tags 采购合同管理
// @Accept json
// @Produce json
// @Param request body dto.CreatePurchaseContractDTO true "采购合同信息"
// @Success 201 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/contracts [post]
func (c *PurchaseContractController) CreatePurchaseContract(ctx *gin.Context) {
	var createDto dto.CreatePurchaseContractDTO
	if err := ctx.ShouldBindJSON(&createDto); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求数据: "+err.Error())
		return
	}

	// 从JWT获取当前用户ID
	createdBy, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}

	contract, err := c.service.CreatePurchaseContract(ctx, createDto, createdBy)
	if err != nil {
		if err == service.ErrInvalidPurchaseContractData {
			response.Fail(ctx, http.StatusBadRequest, "无效的采购合同数据")
			return
		}
		if err == service.ErrContractSupplierNotFound {
			response.Fail(ctx, http.StatusBadRequest, "供应商不存在")
			return
		}
		if err == service.ErrDuplicateContractNo {
			response.Fail(ctx, http.StatusBadRequest, "合同编号已存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "创建采购合同失败: "+err.Error())
		return
	}

	response.Success(ctx, contract, "创建采购合同成功")
}

// GetPurchaseContractByID 根据ID获取采购合同
// @Summary 获取采购合同详情
// @Description 通过采购合同ID查询采购合同详情
// @Tags 采购合同管理
// @Accept json
// @Produce json
// @Param id path int true "采购合同ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购合同不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/contracts/{id} [get]
func (c *PurchaseContractController) GetPurchaseContractByID(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	contract, err := c.service.GetPurchaseContractByID(ctx, uint(id))
	if err != nil {
		if err == service.ErrPurchaseContractNotFound {
			response.Fail(ctx, http.StatusNotFound, "采购合同不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "获取采购合同失败: "+err.Error())
		return
	}

	response.Success(ctx, contract, "获取采购合同成功")
}

// GetPurchaseContractByContractNo 根据合同编号获取采购合同
// @Summary 通过合同编号查询采购合同
// @Description 通过合同编号查询采购合同详情
// @Tags 采购合同管理
// @Accept json
// @Produce json
// @Param contract_no path string true "合同编号"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购合同不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/contracts/no/{contract_no} [get]
func (c *PurchaseContractController) GetPurchaseContractByContractNo(ctx *gin.Context) {
	contractNo := ctx.Param("contract_no")
	if contractNo == "" {
		response.Fail(ctx, http.StatusBadRequest, "合同编号不可为空")
		return
	}

	contract, err := c.service.GetPurchaseContractByContractNo(ctx, contractNo)
	if err != nil {
		if err == service.ErrPurchaseContractNotFound {
			response.Fail(ctx, http.StatusNotFound, "采购合同不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "获取采购合同失败: "+err.Error())
		return
	}

	response.Success(ctx, contract, "获取采购合同成功")
}

// ListPurchaseContracts 获取采购合同列表
// @Summary 查询采购合同列表
// @Description 支持分页和筛选条件
// @Tags 采购合同管理
// @Accept json
// @Produce json
// @Param contract_no query string false "合同编号（模糊查询）"
// @Param contract_type query string false "合同类型"
// @Param inquiry_id query int false "询价单ID"
// @Param inquiry_no query string false "询价单号（模糊查询）"
// @Param project_id query int false "项目ID"
// @Param supplier_id query int false "供应商ID"
// @Param flow_status query string false "工作流状态"
// @Param created_by query int false "创建人ID"
// @Param start_date query string false "创建日期起始（格式：YYYY-MM-DD）"
// @Param end_date query string false "创建日期结束（格式：YYYY-MM-DD）"
// @Param page query int true "页码（从1开始）"
// @Param pageSize query int true "每页数量（最大100）"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/contracts [get]
func (c *PurchaseContractController) ListPurchaseContracts(ctx *gin.Context) {
	var query dto.PurchaseContractListQuery
	if err := ctx.ShouldBindQuery(&query); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的查询参数: "+err.Error())
		return
	}

	result, err := c.service.ListPurchaseContracts(ctx, query)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取采购合同列表失败: "+err.Error())
		return
	}

	response.Success(ctx, result, "获取采购合同列表成功")
}

// UpdatePurchaseContract 更新采购合同
// @Summary 更新采购合同
// @Description 更新指定ID的采购合同信息，仅草稿状态可更新
// @Tags 采购合同管理
// @Accept json
// @Produce json
// @Param id path int true "采购合同ID"
// @Param request body dto.UpdatePurchaseContractDTO true "采购合同信息"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购合同不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/contracts/{id} [put]
func (c *PurchaseContractController) UpdatePurchaseContract(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var updateDto dto.UpdatePurchaseContractDTO
	if err := ctx.ShouldBindJSON(&updateDto); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求数据: "+err.Error())
		return
	}

	updateDto.ID = uint(id)

	contract, err := c.service.UpdatePurchaseContract(ctx, updateDto)
	if err != nil {
		if err == service.ErrPurchaseContractNotFound {
			response.Fail(ctx, http.StatusNotFound, "采购合同不存在")
			return
		}
		if err == service.ErrContractSupplierNotFound {
			response.Fail(ctx, http.StatusBadRequest, "供应商不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "更新采购合同失败: "+err.Error())
		return
	}

	response.Success(ctx, contract, "更新采购合同成功")
}

// SubmitPurchaseContract 提交采购合同
// @Summary 提交采购合同
// @Description 提交采购合同进入审批流程
// @Tags 采购合同管理
// @Accept json
// @Produce json
// @Param id path int true "采购合同ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购合同不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/contracts/{id}/submit [post]
func (c *PurchaseContractController) SubmitPurchaseContract(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	// 从上下文获取当前用户ID
	updatedBy := utils.GetUserIDWithDefault(ctx, 0)

	err = c.service.SubmitPurchaseContract(ctx, uint(id), updatedBy)
	if err != nil {
		if err == service.ErrPurchaseContractNotFound {
			response.Fail(ctx, http.StatusNotFound, "采购合同不存在")
			return
		}
		if err == service.ErrContractSubmitNotAllowed {
			response.Fail(ctx, http.StatusBadRequest, "只有草稿状态的合同才能提交")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "提交采购合同失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "提交采购合同成功")
}

// ApprovePurchaseContract 审批采购合同
// @Summary 审批采购合同
// @Description 审批或拒绝采购合同
// @Tags 采购合同管理
// @Accept json
// @Produce json
// @Param request body dto.ContractApprovalDTO true "审批信息"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/contracts/approve [post]
func (c *PurchaseContractController) ApprovePurchaseContract(ctx *gin.Context) {
	var approvalDto dto.ContractApprovalDTO
	if err := ctx.ShouldBindJSON(&approvalDto); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求数据: "+err.Error())
		return
	}

	// 检查审批动作是否有效
	if approvalDto.Action != constants.ActionApprove && approvalDto.Action != constants.ActionReject {
		response.Fail(ctx, http.StatusBadRequest, "无效的审批动作，必须是approve或reject")
		return
	}

	// 检查当前阶段是否有效
	if approvalDto.CurrentStage == "" {
		response.Fail(ctx, http.StatusBadRequest, "当前审批阶段不能为空")
		return
	}

	// 提示用户current_stage应该与数据库status值匹配
	validStages := []string{
		constants.ContractStagePurchaseReview,
		constants.ContractStageFinanceReview,
		constants.ContractStageLegalReview,
		constants.ContractStageEnterpriseReview,
		constants.ContractStageInternalSign,
		constants.ContractStageDoubleSign,
	}
	isValidStage := false
	for _, stage := range validStages {
		if approvalDto.CurrentStage == stage {
			isValidStage = true
			break
		}
	}
	if !isValidStage {
		response.Fail(ctx, http.StatusBadRequest, "无效的审批阶段，当前阶段应与数据库状态匹配")
		return
	}

	// 从JWT获取当前用户ID并设置为审批人ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}
	approvalDto.ApproverID = userID

	err = c.service.ApprovePurchaseContract(ctx, approvalDto)
	if err != nil {
		if strings.Contains(err.Error(), "当前阶段与合同审批状态不匹配") {
			response.Fail(ctx, http.StatusBadRequest, err.Error())
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "审批采购合同失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "审批采购合同成功")
}

// RollbackPurchaseContract 回退采购合同
// @Summary 回退采购合同
// @Description 将采购合同回退到之前的审批阶段
// @Tags 采购合同管理
// @Accept json
// @Produce json
// @Param request body dto.ContractRollbackDTO true "回退信息"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/contracts/rollback [post]
func (c *PurchaseContractController) RollbackPurchaseContract(ctx *gin.Context) {
	var rollbackDto dto.ContractRollbackDTO
	if err := ctx.ShouldBindJSON(&rollbackDto); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求数据: "+err.Error())
		return
	}

	// 检查当前阶段是否有效
	if rollbackDto.CurrentStage == "" {
		response.Fail(ctx, http.StatusBadRequest, "当前审批阶段不能为空")
		return
	}

	// 检查回退目标阶段是否有效
	if rollbackDto.RollbackTo == "" {
		response.Fail(ctx, http.StatusBadRequest, "回退目标阶段不能为空")
		return
	}

	// 验证回退目标阶段
	validTargetStages := []string{
		constants.ContractStagePurchaseReview,
		constants.ContractStageFinanceReview,
		constants.ContractStageLegalReview,
		constants.ContractStageEnterpriseReview,
	}
	isValidTarget := false
	for _, stage := range validTargetStages {
		if rollbackDto.RollbackTo == stage {
			isValidTarget = true
			break
		}
	}
	if !isValidTarget {
		response.Fail(ctx, http.StatusBadRequest, "无效的回退目标阶段")
		return
	}

	// 从JWT获取当前用户ID并设置为审批人ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}
	rollbackDto.ApproverID = userID

	err = c.service.RollbackPurchaseContract(ctx, rollbackDto)
	if err != nil {
		if strings.Contains(err.Error(), "当前阶段与合同审批状态不匹配") {
			response.Fail(ctx, http.StatusBadRequest, err.Error())
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "回退采购合同失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "回退采购合同成功")
}

// CancelPurchaseContract 取消采购合同
// @Summary 取消采购合同
// @Description 取消指定的采购合同
// @Tags 采购合同管理
// @Accept json
// @Produce json
// @Param id path int true "采购合同ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购合同不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/contracts/{id}/cancel [post]
func (c *PurchaseContractController) CancelPurchaseContract(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	// 从上下文获取当前用户ID
	updatedBy := utils.GetUserIDWithDefault(ctx, 0)

	err = c.service.CancelPurchaseContract(ctx, uint(id), updatedBy)
	if err != nil {
		if err == service.ErrPurchaseContractNotFound {
			response.Fail(ctx, http.StatusNotFound, "采购合同不存在")
			return
		}
		if err == service.ErrInvalidContractStatus {
			response.Fail(ctx, http.StatusBadRequest, "当前状态无法取消")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "取消采购合同失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "取消采购合同成功")
}

// DeletePurchaseContract 删除采购合同
// @Summary 删除采购合同
// @Description 删除指定的采购合同，仅草稿状态可删除
// @Tags 采购合同管理
// @Accept json
// @Produce json
// @Param id path int true "采购合同ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购合同不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/contracts/{id} [delete]
func (c *PurchaseContractController) DeletePurchaseContract(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	// 从上下文获取当前用户ID
	deletedBy := utils.GetUserIDWithDefault(ctx, 0)

	err = c.service.DeletePurchaseContract(ctx, uint(id), deletedBy)
	if err != nil {
		if err == service.ErrPurchaseContractNotFound {
			response.Fail(ctx, http.StatusNotFound, "采购合同不存在")
			return
		}
		if err == service.ErrContractDeleteNotAllowed {
			response.Fail(ctx, http.StatusBadRequest, "只有草稿状态的合同才能删除")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "删除采购合同失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "删除采购合同成功")
}

// GetPurchaseContractHistory 获取采购合同历史记录
// @Summary 获取采购合同历史记录
// @Description 查询指定采购合同的操作历史记录
// @Tags 采购合同管理
// @Accept json
// @Produce json
// @Param id path int true "采购合同ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购合同不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/contracts/{id}/history [get]
func (c *PurchaseContractController) GetPurchaseContractHistory(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	history, err := c.service.GetPurchaseContractHistory(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取采购合同历史记录失败: "+err.Error())
		return
	}

	response.Success(ctx, history, "获取采购合同历史记录成功")
}

// GetInquiryItemsPurchaseStats 获取询价明细采购统计信息
// @Summary 获取询价明细采购统计信息
// @Description 获取指定询价单的明细采购统计信息，包括已采购数量、金额、未采购数量、预算等
// @Tags 采购合同管理
// @Accept json
// @Produce json
// @Param inquiry_id path int true "询价单ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/contracts/inquiry/{inquiry_id}/stats [get]
func (c *PurchaseContractController) GetInquiryItemsPurchaseStats(ctx *gin.Context) {
	inquiryIDStr := ctx.Param("inquiry_id")
	inquiryID, err := strconv.ParseUint(inquiryIDStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的询价单ID")
		return
	}

	stats, err := c.service.GetInquiryItemsPurchaseStats(ctx, uint(inquiryID))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取询价明细采购统计信息失败: "+err.Error())
		return
	}

	response.Success(ctx, stats, "获取询价明细采购统计信息成功")
}
