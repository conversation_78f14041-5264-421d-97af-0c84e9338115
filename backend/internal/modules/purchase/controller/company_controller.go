package controller

import (
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"backend/response"
)

// CompanyController 公司信息控制器
type CompanyController struct {
	service service.CompanyService
}

// NewCompanyController 创建公司信息控制器实例
func NewCompanyController(service service.CompanyService) *CompanyController {
	return &CompanyController{service: service}
}

// RegisterRoutes 注册路由
func (c *CompanyController) RegisterRoutes(router *gin.RouterGroup) {
	// api/v1/purchase/companies
	companiesGroup := router.Group("/companies")
	{
		// 创建公司信息
		companiesGroup.POST("", c.CreateCompany)
		// 获取公司信息列表
		companiesGroup.GET("", c.ListCompanies)
		// 根据ID获取公司信息
		companiesGroup.GET("/:id", c.GetCompanyByID)
		// 根据公司编码获取公司信息
		companiesGroup.GET("/code/:code", c.GetCompanyByCode)
		// 更新公司信息
		companiesGroup.PUT("/:id", c.UpdateCompany)
		// 删除公司信息
		companiesGroup.DELETE("/:id", c.DeleteCompany)
	}
}

// CreateCompany 创建公司信息
// @Summary 创建公司信息
// @Description 创建新的公司信息，公司编码可选，不提供则自动生成
// @Tags 公司管理
// @Accept json
// @Produce json
// @Param company body dto.CreateCompanyDTO true "公司信息"
// @Success 201 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/companies [post]
func (c *CompanyController) CreateCompany(ctx *gin.Context) {
	var createDto dto.CreateCompanyDTO
	if err := ctx.ShouldBindJSON(&createDto); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求数据"+err.Error())
		return
	}

	company, err := c.service.CreateCompany(ctx, createDto)
	if err != nil {
		if err == service.ErrInvalidCompanyData {
			response.Fail(ctx, http.StatusBadRequest, "无效的公司数据")
			return
		}
		if err == service.ErrDuplicateCompanyCode {
			response.Fail(ctx, http.StatusBadRequest, "公司编码已存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "创建公司失败: "+err.Error())
		return
	}

	response.Success(ctx, company, "创建公司成功")
}

// GetCompanyByID 根据ID获取公司信息
// @Summary 获取公司信息详情
// @Description 通过公司ID查询公司详情
// @Tags 公司管理
// @Accept json
// @Produce json
// @Param id path int true "公司ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "公司不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/companies/{id} [get]
func (c *CompanyController) GetCompanyByID(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	company, err := c.service.GetCompanyByID(ctx, uint(id))
	if err != nil {
		if err == service.ErrCompanyNotFound {
			response.Fail(ctx, http.StatusNotFound, "公司不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "获取公司失败")
		return
	}

	response.Success(ctx, company, "获取公司成功")
}

// GetCompanyByCode 根据公司编码获取公司信息
// @Summary 通过公司编码查询公司信息
// @Description 通过公司编码查询公司详情
// @Tags 公司管理
// @Accept json
// @Produce json
// @Param code path string true "公司编码"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "公司不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/companies/code/{code} [get]
func (c *CompanyController) GetCompanyByCode(ctx *gin.Context) {
	code := ctx.Param("code")
	if code == "" {
		response.Fail(ctx, http.StatusBadRequest, "公司编码不可为空")
		return
	}

	company, err := c.service.GetCompanyByCode(ctx, code)
	if err != nil {
		if err == service.ErrCompanyNotFound {
			response.Fail(ctx, http.StatusNotFound, "公司不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "获取公司失败")
		return
	}

	response.Success(ctx, company, "获取公司成功")
}

// ListCompanies 获取公司信息列表
// @Summary 查询公司列表
// @Description 支持分页和筛选条件
// @Tags 公司管理
// @Accept json
// @Produce json
// @Param company_code query string false "公司编码（模糊查询）"
// @Param company_name query string false "公司名称（模糊查询）"
// @Param short_name query string false "简称（模糊查询）"
// @Param contact_person query string false "联系人（模糊查询）"
// @Param contact_phone query string false "联系电话（模糊查询）"
// @Param status query int false "状态"
// @Param page query int true "页码（从1开始）"
// @Param page_size query int true "每页数量（最大100）"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/companies [get]
func (c *CompanyController) ListCompanies(ctx *gin.Context) {
	var query dto.CompanyListQuery
	if err := ctx.ShouldBindQuery(&query); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的查询参数")
		return
	}

	result, err := c.service.ListCompanies(ctx, query)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取公司列表失败")
		return
	}

	response.Success(ctx, result, "获取公司列表成功")
}

// UpdateCompany 更新公司信息
// @Summary 更新公司信息
// @Description 更新指定ID的公司信息
// @Tags 公司管理
// @Accept json
// @Produce json
// @Param id path int true "公司ID"
// @Param company body dto.UpdateCompanyDTO true "公司信息"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "公司不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/companies/{id} [put]
func (c *CompanyController) UpdateCompany(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var updateDto dto.UpdateCompanyDTO
	if err := ctx.ShouldBindJSON(&updateDto); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求数据"+err.Error())
		return
	}
	updateDto.ID = uint(id)

	company, err := c.service.UpdateCompany(ctx, updateDto)
	if err != nil {
		if err == service.ErrCompanyNotFound {
			response.Fail(ctx, http.StatusNotFound, "公司不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "更新公司失败: "+err.Error())
		return
	}

	response.Success(ctx, company, "更新公司成功")
}

// DeleteCompany 删除公司信息
// @Summary 删除公司信息
// @Description 删除指定ID的公司信息
// @Tags 公司管理
// @Accept json
// @Produce json
// @Param id path int true "公司ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "公司不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/companies/{id} [delete]
func (c *CompanyController) DeleteCompany(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	err = c.service.DeleteCompany(ctx, uint(id))
	if err != nil {
		if err == service.ErrCompanyNotFound {
			response.Fail(ctx, http.StatusNotFound, "公司不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "删除公司失败")
		return
	}

	response.Success(ctx, nil, "删除公司成功")
}
