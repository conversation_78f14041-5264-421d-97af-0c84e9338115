package controller

import (
	"net/http"
	"strconv"

	"backend/internal/common/utils"
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/service"
	"backend/response"

	"github.com/gin-gonic/gin"
)

// ArrivalController 到货管理控制器
type ArrivalController struct {
	service service.ArrivalService
}

// NewArrivalController 创建到货管理控制器实例
func NewArrivalController(service service.ArrivalService) *ArrivalController {
	return &ArrivalController{
		service: service,
	}
}

// RegisterRoutes 注册到货管理相关路由
func (c *ArrivalController) RegisterRoutes(router *gin.RouterGroup) {
	arrivalGroup := router.Group("/arrivals")
	{
		// 基本CRUD操作
		arrivalGroup.POST("", c.Create)                          // 创建到货记录
		arrivalGroup.GET("", c.List)                             // 获取到货记录列表
		arrivalGroup.GET("/:id", c.GetByID)                      // 根据ID获取到货记录
		arrivalGroup.GET("/by-no/:arrival_no", c.GetByArrivalNo) // 根据到货通知单号获取到货记录
		arrivalGroup.PUT("/:id", c.Update)                       // 更新到货记录
		arrivalGroup.DELETE("/:id", c.Delete)                    // 删除到货记录

		// 工作流操作
		arrivalGroup.POST("/:id/submit", c.Submit) // 提交到货记录
		arrivalGroup.POST("/:id/cancel", c.Cancel) // 取消到货记录
		arrivalGroup.POST("/approve", c.Approve)   // 审批到货记录
		arrivalGroup.POST("/rollback", c.Rollback) // 回退到货记录

		// 查询操作
		arrivalGroup.GET("/by-contract/:contract_id", c.GetByContractID) // 根据合同ID获取到货记录列表
		arrivalGroup.GET("/:id/history", c.GetArrivalHistory)            // 获取到货记录历史记录
		arrivalGroup.GET("/statistics", c.GetStatistics)                 // 获取到货统计信息
	}
}

// Create 创建到货记录
// @Summary 创建到货记录
// @Description 创建新的到货记录
// @Tags 到货管理
// @Accept json
// @Produce json
// @Param arrival body dto.CreateArrivalDTO true "到货记录信息"
// @Success 200 {object} response.ResponseStruct{data=model.Arrival} "创建成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/arrivals [post]
func (c *ArrivalController) Create(ctx *gin.Context) {
	var createDTO dto.CreateArrivalDTO
	if err := ctx.ShouldBindJSON(&createDTO); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误")
		return
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}
	createDTO.CreatedBy = userID

	arrival, err := c.service.Create(ctx.Request.Context(), &createDTO)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建到货记录失败")
		return
	}

	response.Success(ctx, arrival, "创建到货记录成功")
}

// GetByID 根据ID获取到货记录
// @Summary 根据ID获取到货记录
// @Description 根据ID获取到货记录详情
// @Tags 到货管理
// @Accept json
// @Produce json
// @Param id path int true "到货记录ID"
// @Success 200 {object} response.ResponseStruct{data=dto.ArrivalDetailDTO} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "到货记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/arrivals/{id} [get]
func (c *ArrivalController) GetByID(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的到货记录ID")
		return
	}

	arrival, err := c.service.GetByID(ctx.Request.Context(), uint(id))
	if err != nil {
		if err.Error() == "到货记录不存在" {
			response.Fail(ctx, http.StatusNotFound, "到货记录不存在")
		} else {
			response.Fail(ctx, http.StatusInternalServerError, "获取到货记录失败")
		}
		return
	}

	response.Success(ctx, arrival, "获取到货记录成功")
}

// GetByArrivalNo 根据到货通知单号获取到货记录
// @Summary 根据到货通知单号获取到货记录
// @Description 根据到货通知单号获取到货记录详情
// @Tags 到货管理
// @Accept json
// @Produce json
// @Param arrival_no path string true "到货通知单号"
// @Success 200 {object} response.ResponseStruct{data=dto.ArrivalDetailDTO} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "到货记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/arrivals/by-no/{arrival_no} [get]
func (c *ArrivalController) GetByArrivalNo(ctx *gin.Context) {
	arrivalNo := ctx.Param("arrival_no")
	if arrivalNo == "" {
		response.Fail(ctx, http.StatusBadRequest, "到货通知单号不能为空")
		return
	}

	arrival, err := c.service.GetByArrivalNo(ctx.Request.Context(), arrivalNo)
	if err != nil {
		if err.Error() == "到货记录不存在" {
			response.Fail(ctx, http.StatusNotFound, "到货记录不存在")
		} else {
			response.Fail(ctx, http.StatusInternalServerError, "获取到货记录失败")
		}
		return
	}

	response.Success(ctx, arrival, "获取到货记录成功")
}

// Update 更新到货记录
// @Summary 更新到货记录
// @Description 更新到货记录信息
// @Tags 到货管理
// @Accept json
// @Produce json
// @Param id path int true "到货记录ID"
// @Param arrival body dto.UpdateArrivalDTO true "到货记录信息"
// @Success 200 {object} response.Response "更新成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "到货记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/arrivals/{id} [put]
func (c *ArrivalController) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的到货记录ID")
		return
	}

	var updateDTO dto.UpdateArrivalDTO
	if err := ctx.ShouldBindJSON(&updateDTO); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误")
		return
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}
	updateDTO.UpdatedBy = userID

	err = c.service.Update(ctx.Request.Context(), uint(id), &updateDTO)
	if err != nil {
		if err.Error() == "到货记录不存在" {
			response.Fail(ctx, http.StatusNotFound, "到货记录不存在")
		} else {
			response.Fail(ctx, http.StatusInternalServerError, "更新到货记录失败")
		}
		return
	}

	response.Success(ctx, nil, "更新到货记录成功")
}

// Delete 删除到货记录
// @Summary 删除到货记录
// @Description 删除到货记录
// @Tags 到货管理
// @Accept json
// @Produce json
// @Param id path int true "到货记录ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "到货记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/arrivals/{id} [delete]
func (c *ArrivalController) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的到货记录ID")
		return
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}

	err = c.service.Delete(ctx.Request.Context(), uint(id), userID)
	if err != nil {
		if err.Error() == "到货记录不存在" {
			response.Fail(ctx, http.StatusNotFound, "到货记录不存在")
		} else {
			response.Fail(ctx, http.StatusInternalServerError, "删除到货记录失败")
		}
		return
	}

	response.Success(ctx, nil, "删除到货记录成功")
}

// List 获取到货记录列表
// @Summary 获取到货记录列表
// @Description 获取到货记录列表，支持分页和筛选
// @Tags 到货管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param arrival_no query string false "到货通知单号"
// @Param contract_id query int false "合同ID"
// @Param supplier_id query int false "供应商ID"
// @Param status query string false "状态"
// @Param is_complete query bool false "是否全部到货"
// @Param created_by query int false "创建人ID"
// @Param start_date query string false "开始日期"
// @Param end_date query string false "结束日期"
// @Param min_amount query number false "最小金额"
// @Param max_amount query number false "最大金额"
// @Success 200 {object} response.ResponseStruct{data=object{list=[]dto.ArrivalListDTO,total=int}} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/arrivals [get]
func (c *ArrivalController) List(ctx *gin.Context) {
	var query dto.ArrivalQueryDTO
	if err := ctx.ShouldBindQuery(&query); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误")
		return
	}

	arrivals, total, err := c.service.List(ctx.Request.Context(), &query)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取到货记录列表失败")
		return
	}

	// 返回标准的分页结构
	result := map[string]interface{}{
		"list":  arrivals,
		"total": total,
	}

	response.Success(ctx, result, "获取到货记录列表成功")
}

// Submit 提交到货记录
// @Summary 提交到货记录
// @Description 提交到货记录进入审批流程
// @Tags 到货管理
// @Accept json
// @Produce json
// @Param id path int true "到货记录ID"
// @Success 200 {object} response.Response "提交成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "到货记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/arrivals/{id}/submit [post]
func (c *ArrivalController) Submit(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的到货记录ID")
		return
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}

	err = c.service.Submit(ctx.Request.Context(), uint(id), userID)
	if err != nil {
		if err.Error() == "到货记录不存在" {
			response.Fail(ctx, http.StatusNotFound, "到货记录不存在")
		} else {
			response.Fail(ctx, http.StatusInternalServerError, "提交到货记录失败")
		}
		return
	}

	response.Success(ctx, nil, "提交到货记录成功")
}

// Cancel 取消到货记录
// @Summary 取消到货记录
// @Description 取消到货记录
// @Tags 到货管理
// @Accept json
// @Produce json
// @Param id path int true "到货记录ID"
// @Success 200 {object} response.Response "取消成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "到货记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/arrivals/{id}/cancel [post]
func (c *ArrivalController) Cancel(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的到货记录ID")
		return
	}

	// 获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}

	err = c.service.Cancel(ctx.Request.Context(), uint(id), userID)
	if err != nil {
		if err.Error() == "到货记录不存在" {
			response.Fail(ctx, http.StatusNotFound, "到货记录不存在")
		} else {
			response.Fail(ctx, http.StatusInternalServerError, "取消到货记录失败")
		}
		return
	}

	response.Success(ctx, nil, "取消到货记录成功")
}

// Approve 审批到货记录
// @Summary 审批到货记录
// @Description 审批到货记录（通过或拒绝）
// @Tags 到货管理
// @Accept json
// @Produce json
// @Param approval body dto.ArrivalApprovalDTO true "审批信息"
// @Success 200 {object} response.Response "审批成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "到货记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/arrivals/approve [post]
func (c *ArrivalController) Approve(ctx *gin.Context) {
	var approvalDTO dto.ArrivalApprovalDTO
	if err := ctx.ShouldBindJSON(&approvalDTO); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误")
		return
	}

	// 获取当前用户ID
	userIDVal, exists := ctx.Get("userID")
	if !exists {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}
	userID, ok := userIDVal.(uint)
	if !ok {
		response.Fail(ctx, http.StatusUnauthorized, "用户ID格式错误")
		return
	}

	// 获取用户姓名
	userInfoVal, exists := ctx.Get("userInfo")
	if !exists {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}
	userInfo, ok := userInfoVal.(map[string]interface{})
	if !ok {
		response.Fail(ctx, http.StatusUnauthorized, "用户信息格式错误")
		return
	}

	userName, ok := userInfo["realName"].(string)
	if !ok {
		userName = "未知用户" // 设置默认值
	}

	// 验证当前阶段
	arrival, err := c.service.GetByID(ctx.Request.Context(), approvalDTO.ID)
	if err != nil {
		if err.Error() == "到货记录不存在" {
			response.Fail(ctx, http.StatusNotFound, "到货记录不存在")
		} else {
			response.Fail(ctx, http.StatusInternalServerError, "获取到货记录失败")
		}
		return
	}

	if arrival.Status != approvalDTO.CurrentStage {
		response.Fail(ctx, http.StatusBadRequest, "当前阶段不匹配，请刷新页面后重试")
		return
	}

	err = c.service.Approve(ctx.Request.Context(), &approvalDTO, userID, userName)
	if err != nil {
		if err.Error() == "到货记录不存在" {
			response.Fail(ctx, http.StatusNotFound, "到货记录不存在")
		} else {
			response.Fail(ctx, http.StatusInternalServerError, "审批到货记录失败")
		}
		return
	}

	response.Success(ctx, nil, "审批到货记录成功")
}

// Rollback 回退到货记录
// @Summary 回退到货记录
// @Description 回退到货记录到上一个状态
// @Tags 到货管理
// @Accept json
// @Produce json
// @Param rollback body dto.ArrivalRollbackDTO true "回退信息"
// @Success 200 {object} response.Response "回退成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "到货记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/arrivals/rollback [post]
func (c *ArrivalController) Rollback(ctx *gin.Context) {
	var rollbackDTO dto.ArrivalRollbackDTO
	if err := ctx.ShouldBindJSON(&rollbackDTO); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误")
		return
	}

	// 获取当前用户ID
	userIDVal, exists := ctx.Get("userID")
	if !exists {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}
	userID, ok := userIDVal.(uint)
	if !ok {
		response.Fail(ctx, http.StatusUnauthorized, "用户ID格式错误")
		return
	}

	// 获取用户姓名
	userInfoVal, exists := ctx.Get("userInfo")
	if !exists {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}
	userInfo, ok := userInfoVal.(map[string]interface{})
	if !ok {
		response.Fail(ctx, http.StatusUnauthorized, "用户信息格式错误")
		return
	}

	userName, ok := userInfo["realName"].(string)
	if !ok {
		userName = "未知用户" // 设置默认值
	}

	// 验证当前阶段
	arrival, err := c.service.GetByID(ctx.Request.Context(), rollbackDTO.ID)
	if err != nil {
		if err.Error() == "到货记录不存在" {
			response.Fail(ctx, http.StatusNotFound, "到货记录不存在")
		} else {
			response.Fail(ctx, http.StatusInternalServerError, "获取到货记录失败")
		}
		return
	}

	if arrival.Status != rollbackDTO.CurrentStage {
		response.Fail(ctx, http.StatusBadRequest, "当前阶段不匹配，请刷新页面后重试")
		return
	}

	err = c.service.Rollback(ctx.Request.Context(), &rollbackDTO, userID, userName)
	if err != nil {
		if err.Error() == "到货记录不存在" {
			response.Fail(ctx, http.StatusNotFound, "到货记录不存在")
		} else {
			response.Fail(ctx, http.StatusInternalServerError, "回退到货记录失败")
		}
		return
	}

	response.Success(ctx, nil, "回退到货记录成功")
}

// GetByContractID 根据合同ID获取到货记录列表
// @Summary 根据合同ID获取到货记录列表
// @Description 根据合同ID获取相关的到货记录列表
// @Tags 到货管理
// @Accept json
// @Produce json
// @Param contract_id path int true "合同ID"
// @Success 200 {object} response.ResponseStruct{data=[]model.Arrival} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/arrivals/by-contract/{contract_id} [get]
func (c *ArrivalController) GetByContractID(ctx *gin.Context) {
	contractIDStr := ctx.Param("contract_id")
	contractID, err := strconv.ParseUint(contractIDStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的合同ID")
		return
	}

	arrivals, err := c.service.GetByContractID(ctx.Request.Context(), uint(contractID))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取到货记录列表失败")
		return
	}

	response.Success(ctx, arrivals, "获取到货记录列表成功")
}

// GetArrivalHistory 获取到货记录历史记录
// @Summary 获取到货记录历史记录
// @Description 获取到货记录的审批历史记录
// @Tags 到货管理
// @Accept json
// @Produce json
// @Param id path int true "到货记录ID"
// @Success 200 {object} response.ResponseStruct{data=[]dto.ArrivalHistoryDTO} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "到货记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/arrivals/{id}/history [get]
func (c *ArrivalController) GetArrivalHistory(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的到货记录ID")
		return
	}

	histories, err := c.service.GetArrivalHistory(ctx.Request.Context(), uint(id))
	if err != nil {
		if err.Error() == "到货记录不存在" {
			response.Fail(ctx, http.StatusNotFound, "到货记录不存在")
		} else {
			response.Fail(ctx, http.StatusInternalServerError, "获取到货记录历史记录失败")
		}
		return
	}

	response.Success(ctx, histories, "获取到货记录历史记录成功")
}

// GetStatistics 获取到货统计信息
// @Summary 获取到货统计信息
// @Description 获取到货记录的统计信息
// @Tags 到货管理
// @Accept json
// @Produce json
// @Success 200 {object} response.ResponseStruct{data=dto.ArrivalStatisticsDTO} "获取成功"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/arrivals/statistics [get]
func (c *ArrivalController) GetStatistics(ctx *gin.Context) {
	statistics, err := c.service.GetStatistics(ctx.Request.Context())
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取到货统计信息失败")
		return
	}

	response.Success(ctx, statistics, "获取到货统计信息成功")
}
