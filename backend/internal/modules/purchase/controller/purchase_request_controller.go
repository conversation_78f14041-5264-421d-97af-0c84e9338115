package controller

import (
	"backend/internal/common/utils"
	"backend/internal/modules/purchase/constants"
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/service"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"

	"backend/response"
)

// PurchaseRequestController 采购申请控制器
type PurchaseRequestController struct {
	service service.PurchaseRequestService
}

// NewPurchaseRequestController 创建采购申请控制器实例
func NewPurchaseRequestController(service service.PurchaseRequestService) *PurchaseRequestController {
	return &PurchaseRequestController{service: service}
}

// RegisterRoutes 注册路由
func (c *PurchaseRequestController) RegisterRoutes(router *gin.RouterGroup) {
	// /purchase/requests
	requestsGroup := router.Group("/requests")
	{
		// 创建采购申请
		requestsGroup.POST("", c.CreatePurchaseRequest)
		// 获取采购申请列表
		requestsGroup.GET("", c.ListPurchaseRequests)
		// 根据ID获取采购申请
		requestsGroup.GET("/:id", c.GetPurchaseRequestByID)
		// 根据申请单号获取采购申请
		requestsGroup.GET("/no/:request_no", c.GetPurchaseRequestByRequestNo)
		// 更新采购申请
		//requestsGroup.PUT("/:id", c.UpdatePurchaseRequest)
		// 提交采购申请进入审批流程
		requestsGroup.POST("/:id/submit", c.SubmitPurchaseRequest)
		// 审批采购申请
		requestsGroup.POST("/approve", c.ApprovePurchaseRequest)
		// 回退采购申请
		requestsGroup.POST("/rollback", c.RollbackPurchaseRequest)
		// 获取采购申请历史记录
		requestsGroup.GET("/:id/history", c.GetPurchaseRequestHistory)
		// 取消采购申请
		requestsGroup.POST("/:id/cancel", c.CancelPurchaseRequest)
		// 删除采购申请
		//requestsGroup.DELETE("/:id", c.DeletePurchaseRequest)
	}
}

// CreatePurchaseRequest 创建采购申请
// @Summary 创建采购申请
// @Description 创建新的采购申请，申请单号自动生成
// @Tags 采购申请管理
// @Accept json
// @Produce json
// @Param request body dto.CreatePurchaseRequestDTO true "采购申请信息"
// @Success 201 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/requests [post]
func (c *PurchaseRequestController) CreatePurchaseRequest(ctx *gin.Context) {
	var createDto dto.CreatePurchaseRequestDTO
	if err := ctx.ShouldBindJSON(&createDto); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求数据: "+err.Error())
		return
	}

	// 从JWT获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}

	// 设置创建人ID
	createDto.CreatedBy = userID

	request, err := c.service.CreatePurchaseRequest(ctx, createDto)
	if err != nil {
		if err == service.ErrInvalidPurchaseRequestData {
			response.Fail(ctx, http.StatusBadRequest, "无效的采购申请数据")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "创建采购申请失败: "+err.Error())
		return
	}

	response.Success(ctx, request, "创建采购申请成功")
}

// GetPurchaseRequestByID 根据ID获取采购申请
// @Summary 获取采购申请详情
// @Description 通过采购申请ID查询采购申请详情
// @Tags 采购申请管理
// @Accept json
// @Produce json
// @Param id path int true "采购申请ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购申请不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/requests/{id} [get]
func (c *PurchaseRequestController) GetPurchaseRequestByID(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	request, err := c.service.GetPurchaseRequestByID(ctx, uint(id))
	if err != nil {
		if err == service.ErrPurchaseRequestNotFound {
			response.Fail(ctx, http.StatusNotFound, "采购申请不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "获取采购申请失败: "+err.Error())
		return
	}

	response.Success(ctx, request, "获取采购申请成功")
}

// GetPurchaseRequestByRequestNo 根据申请单号获取采购申请
// @Summary 通过申请单号查询采购申请
// @Description 通过申请单号查询采购申请详情
// @Tags 采购申请管理
// @Accept json
// @Produce json
// @Param request_no path string true "申请单号"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购申请不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/requests/no/{request_no} [get]
func (c *PurchaseRequestController) GetPurchaseRequestByRequestNo(ctx *gin.Context) {
	requestNo := ctx.Param("request_no")
	if requestNo == "" {
		response.Fail(ctx, http.StatusBadRequest, "申请单号不可为空")
		return
	}

	request, err := c.service.GetPurchaseRequestByRequestNo(ctx, requestNo)
	if err != nil {
		if err == service.ErrPurchaseRequestNotFound {
			response.Fail(ctx, http.StatusNotFound, "采购申请不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "获取采购申请失败: "+err.Error())
		return
	}

	response.Success(ctx, request, "获取采购申请成功")
}

// ListPurchaseRequests 获取采购申请列表
// @Summary 查询采购申请列表
// @Description 支持分页和筛选条件
// @Tags 采购申请管理
// @Accept json
// @Produce json
// @Param request_no query string false "申请单号（模糊查询）"
// @Param project_id query int false "项目ID"
// @Param request_type query string false "申请类型"
// @Param urgency_level query string false "紧急程度"
// @Param status query string false "状态"
// @Param created_by query int false "创建人ID"
// @Param start_date query string false "创建日期起始（格式：YYYY-MM-DD）"
// @Param end_date query string false "创建日期结束（格式：YYYY-MM-DD）"
// @Param page query int true "页码（从1开始）"
// @Param page_size query int true "每页数量（最大100）"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/requests [get]
func (c *PurchaseRequestController) ListPurchaseRequests(ctx *gin.Context) {
	var query dto.PurchaseRequestListQuery
	if err := ctx.ShouldBindQuery(&query); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的查询参数: "+err.Error())
		return
	}

	result, err := c.service.ListPurchaseRequests(ctx, query)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取采购申请列表失败: "+err.Error())
		return
	}

	response.Success(ctx, result, "获取采购申请列表成功")
}

// UpdatePurchaseRequest 更新采购申请
// @Summary 更新采购申请
// @Description 更新指定ID的采购申请信息，仅草稿状态可更新
// @Tags 采购申请管理
// @Accept json
// @Produce json
// @Param id path int true "采购申请ID"
// @Param request body dto.UpdatePurchaseRequestDTO true "采购申请信息"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购申请不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/requests/{id} [put]
func (c *PurchaseRequestController) UpdatePurchaseRequest(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var updateDto dto.UpdatePurchaseRequestDTO
	if err := ctx.ShouldBindJSON(&updateDto); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求数据: "+err.Error())
		return
	}
	updateDto.ID = uint(id)

	request, err := c.service.UpdatePurchaseRequest(ctx, updateDto)
	if err != nil {
		if err == service.ErrPurchaseRequestNotFound {
			response.Fail(ctx, http.StatusNotFound, "采购申请不存在")
			return
		}
		if err == service.ErrInvalidPurchaseRequestStatus {
			response.Fail(ctx, http.StatusBadRequest, "当前状态不允许更新，仅草稿状态可更新")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "更新采购申请失败: "+err.Error())
		return
	}

	response.Success(ctx, request, "更新采购申请成功")
}

// SubmitPurchaseRequest 提交采购申请进入审批流程
// @Summary 提交采购申请
// @Description 提交采购申请进入审批流程，仅草稿状态可提交
// @Tags 采购申请管理
// @Accept json
// @Produce json
// @Param id path int true "采购申请ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购申请不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/requests/{id}/submit [post]
func (c *PurchaseRequestController) SubmitPurchaseRequest(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	// 从JWT获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}

	err = c.service.SubmitPurchaseRequest(ctx, uint(id), userID)
	if err != nil {
		if err == service.ErrPurchaseRequestNotFound {
			response.Fail(ctx, http.StatusNotFound, "采购申请不存在")
			return
		}
		if err == service.ErrInvalidPurchaseRequestStatus {
			response.Fail(ctx, http.StatusBadRequest, "当前状态不允许提交，仅草稿状态可提交")
			return
		}
		if err == service.ErrWorkflowStartFailed {
			response.Fail(ctx, http.StatusInternalServerError, "启动审批流程失败")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "提交采购申请失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "采购申请提交成功")
}

// ApprovePurchaseRequest 审批采购申请
// @Summary 审批采购申请
// @Description 审批或拒绝采购申请
// @Tags 采购申请管理
// @Accept json
// @Produce json
// @Param approval body dto.WorkflowApprovalDTO true "审批信息"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购申请不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/requests/approve [post]
func (c *PurchaseRequestController) ApprovePurchaseRequest(ctx *gin.Context) {
	var approvalDto dto.WorkflowApprovalDTO
	if err := ctx.ShouldBindJSON(&approvalDto); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求数据: "+err.Error())
		return
	}

	// 检查审批动作是否有效
	if approvalDto.Action != constants.ActionApprove && approvalDto.Action != constants.ActionReject {
		response.Fail(ctx, http.StatusBadRequest, "无效的审批动作，必须是approve或reject")
		return
	}

	// 检查当前阶段是否有效
	if approvalDto.CurrentStage == "" {
		response.Fail(ctx, http.StatusBadRequest, "当前审批阶段不能为空")
		return
	}

	// 提示用户current_stage应该与数据库status值匹配
	validStages := []string{constants.StatusDraft, constants.StatusProjectManagerReview, constants.StatusPurchaseManagerReview}
	isValidStage := false
	for _, stage := range validStages {
		if approvalDto.CurrentStage == stage {
			isValidStage = true
			break
		}
	}
	if !isValidStage {
		response.Fail(ctx, http.StatusBadRequest, "无效的审批阶段，当前阶段应与数据库状态匹配，例如: project_manager_review, purchase_manager_review")
		return
	}

	// 从JWT获取当前用户ID并设置为审批人ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}
	approvalDto.ApproverID = userID

	err = c.service.ApprovePurchaseRequest(ctx, approvalDto)
	if err != nil {
		if err == service.ErrPurchaseRequestNotFound {
			response.Fail(ctx, http.StatusNotFound, "采购申请不存在")
			return
		}
		if err == service.ErrInvalidPurchaseRequestStatus {
			response.Fail(ctx, http.StatusBadRequest, "当前状态不允许审批")
			return
		}
		if strings.Contains(err.Error(), "当前阶段与审批状态不匹配") {
			response.Fail(ctx, http.StatusBadRequest, err.Error())
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "审批采购申请失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "审批操作成功")
}

// RollbackPurchaseRequest 回退采购申请
// @Summary 回退采购申请
// @Description 回退采购申请到指定的审批节点
// @Tags 采购申请管理
// @Accept json
// @Produce json
// @Param rollback body dto.WorkflowRollbackDTO true "回退信息"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购申请不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/requests/rollback [post]
func (c *PurchaseRequestController) RollbackPurchaseRequest(ctx *gin.Context) {
	var rollbackDto dto.WorkflowRollbackDTO
	if err := ctx.ShouldBindJSON(&rollbackDto); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的请求数据: "+err.Error())
		return
	}

	// 检查回退阶段是否有效
	validStages := []string{"project_manager_review", "draft"}
	isValidStage := false
	for _, stage := range validStages {
		if rollbackDto.RollbackTo == stage {
			isValidStage = true
			break
		}
	}
	if !isValidStage {
		response.Fail(ctx, http.StatusBadRequest, "无效的回退阶段，支持的回退阶段: project_manager_review, draft")
		return
	}

	// 从JWT获取当前用户ID并设置为审批人ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}
	rollbackDto.ApproverID = userID

	err = c.service.RollbackPurchaseRequest(ctx, rollbackDto)
	if err != nil {
		if err == service.ErrPurchaseRequestNotFound {
			response.Fail(ctx, http.StatusNotFound, "采购申请不存在")
			return
		}
		if err == service.ErrInvalidPurchaseRequestStatus {
			response.Fail(ctx, http.StatusBadRequest, "当前状态不允许回退")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "回退采购申请失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "回退操作成功")
}

// GetPurchaseRequestHistory 获取采购申请历史记录
// @Summary 获取采购申请历史记录
// @Description 获取采购申请的审批历史记录信息
// @Tags 采购申请管理
// @Accept json
// @Produce json
// @Param id path int true "采购申请ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购申请不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/requests/{id}/history [get]
func (c *PurchaseRequestController) GetPurchaseRequestHistory(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	history, err := c.service.GetPurchaseRequestHistory(ctx, uint(id))
	if err != nil {
		if err == service.ErrPurchaseRequestNotFound {
			response.Fail(ctx, http.StatusNotFound, "采购申请不存在")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "获取采购申请历史记录失败: "+err.Error())
		return
	}

	response.Success(ctx, history, "获取采购申请历史记录成功")
}

// CancelPurchaseRequest 取消采购申请
// @Summary 取消采购申请
// @Description 取消采购申请，仅草稿、已提交和审批中状态可取消
// @Tags 采购申请管理
// @Accept json
// @Produce json
// @Param id path int true "采购申请ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购申请不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/requests/{id}/cancel [post]
func (c *PurchaseRequestController) CancelPurchaseRequest(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	// 从JWT获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}

	err = c.service.CancelPurchaseRequest(ctx, uint(id), userID)
	if err != nil {
		if err == service.ErrPurchaseRequestNotFound {
			response.Fail(ctx, http.StatusNotFound, "采购申请不存在")
			return
		}
		if err == service.ErrInvalidPurchaseRequestStatus {
			response.Fail(ctx, http.StatusBadRequest, "当前状态不允许取消")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "取消采购申请失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "采购申请取消成功")
}

// DeletePurchaseRequest 删除采购申请
// @Summary 删除采购申请
// @Description 删除采购申请，仅草稿和已取消状态可删除
// @Tags 采购申请管理
// @Accept json
// @Produce json
// @Param id path int true "采购申请ID"
// @Success 200 {object} response.Response "操作成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "采购申请不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /purchase/requests/{id} [delete]
func (c *PurchaseRequestController) DeletePurchaseRequest(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	// 从JWT获取当前用户ID
	userID, err := utils.GetUserID(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败")
		return
	}

	err = c.service.DeletePurchaseRequest(ctx, uint(id), userID)
	if err != nil {
		if err == service.ErrPurchaseRequestNotFound {
			response.Fail(ctx, http.StatusNotFound, "采购申请不存在")
			return
		}
		if err == service.ErrInvalidPurchaseRequestStatus {
			response.Fail(ctx, http.StatusBadRequest, "当前状态不允许删除，仅草稿和已取消状态可删除")
			return
		}
		response.Fail(ctx, http.StatusInternalServerError, "删除采购申请失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "采购申请删除成功")
}
