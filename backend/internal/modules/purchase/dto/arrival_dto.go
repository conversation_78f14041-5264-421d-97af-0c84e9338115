package dto

import (
	"backend/internal/modules/purchase/model"
	"time"
)

// CreateArrivalDTO 创建到货记录的数据传输对象
type CreateArrivalDTO struct {
	ContractID      uint                   `json:"contract_id" binding:"required"`     // 关联合同ID
	MayArriveAt     *time.Time             `json:"may_arrive_at"`                      // 预计到货日期
	TrackingInfo    string                 `json:"tracking_info"`                      // 物流信息
	ArrivalAddress  string                 `json:"arrival_address"`                    // 到货地址
	ArrivalNotice   string                 `json:"arrival_notice"`                     // 到货通知内容
	Remark          string                 `json:"remark"`                             // 备注
	Items           []CreateArrivalItemDTO `json:"items" binding:"required,min=1"`     // 到货明细项
	CreatedBy       uint                   `json:"-"`                                  // 创建人ID（系统自动设置）
}

// CreateArrivalItemDTO 创建到货明细项的数据传输对象
type CreateArrivalItemDTO struct {
	ContractItemID         uint       `json:"contract_item_id" binding:"required"`               // 合同明细ID
	CurrentArrivalQuantity int        `json:"current_arrival_quantity" binding:"required,gte=0"` // 本次到货数量
	CurrentArrivalAmount   float64    `json:"current_arrival_amount" binding:"required,gt=0"`    // 本次到货金额
	ExpectedArrivalDate    *time.Time `json:"expected_arrival_date"`                             // 预计到货时间
	SerialNumbers          []string   `json:"serial_numbers"`                                    // SN号列表
	Remark                 string     `json:"remark"`                                            // 备注
}

// UpdateArrivalDTO 更新到货记录的数据传输对象
type UpdateArrivalDTO struct {
	MayArriveAt     *time.Time             `json:"may_arrive_at"`                      // 预计到货日期
	TrackingInfo    string                 `json:"tracking_info"`                      // 物流信息
	ArrivalAddress  string                 `json:"arrival_address"`                    // 到货地址
	ArrivalNotice   string                 `json:"arrival_notice"`                     // 到货通知内容
	Remark          string                 `json:"remark"`                             // 备注
	Items           []UpdateArrivalItemDTO `json:"items"`                              // 到货明细项
	UpdatedBy       uint                   `json:"updated_by" binding:"required"`      // 更新人ID
}

// UpdateArrivalItemDTO 更新到货明细项的数据传输对象
type UpdateArrivalItemDTO struct {
	ID                     uint       `json:"id"`                                                // 明细ID
	ContractItemID         uint       `json:"contract_item_id" binding:"required"`               // 合同明细ID
	CurrentArrivalQuantity int        `json:"current_arrival_quantity" binding:"required,gte=0"` // 本次到货数量
	CurrentArrivalAmount   float64    `json:"current_arrival_amount" binding:"required,gt=0"`    // 本次到货金额
	ExpectedArrivalDate    *time.Time `json:"expected_arrival_date"`                             // 预计到货时间
	SerialNumbers          []string   `json:"serial_numbers"`                                    // SN号列表
	Remark                 string     `json:"remark"`                                            // 备注
}

// ArrivalQueryDTO 到货记录查询参数
type ArrivalQueryDTO struct {
	Page        int      `form:"page,default=1" binding:"min=1"`               // 页码
	PageSize    int      `form:"page_size,default=10" binding:"min=1,max=100"` // 每页数量
	ArrivalNo   string   `form:"arrival_no"`                                   // 到货通知单号
	ContractID  *uint    `form:"contract_id"`                                  // 关联合同ID
	ContractNo  string   `form:"contract_no"`                                  // 合同编号
	SupplierID  *uint    `form:"supplier_id"`                                  // 供应商ID
	Status      string   `form:"status"`                                       // 状态
	IsComplete  *bool    `form:"is_complete"`                                  // 是否全部到货
	CreatedBy   *uint    `form:"created_by"`                                   // 创建人ID
	StartDate   string   `form:"start_date"`                                   // 开始日期
	EndDate     string   `form:"end_date"`                                     // 结束日期
	MinAmount   *float64 `form:"min_amount"`                                   // 最小金额
	MaxAmount   *float64 `form:"max_amount"`                                   // 最大金额
}

// ArrivalDetailDTO 到货记录详情数据传输对象（简化版本）
type ArrivalDetailDTO struct {
	ID               uint                    `json:"id"`
	ArrivalNo        string                  `json:"arrival_no"`
	ContractID       uint                    `json:"contract_id"`
	SupplierID       uint                    `json:"supplier_id"`
	MayArriveAt      *time.Time              `json:"may_arrive_at"`
	TrackingInfo     string                  `json:"tracking_info"`
	ArrivalAddress   string                  `json:"arrival_address"`
	ArrivalNotice    string                  `json:"arrival_notice"`
	TotalQuantity    int                     `json:"total_quantity"`
	TotalAmount      float64                 `json:"total_amount"`
	IsComplete       bool                    `json:"is_complete"`
	Remark           string                  `json:"remark"`
	Status           string                  `json:"status"`
	CreatedBy        *uint                   `json:"created_by"`
	CreatedAt        time.Time               `json:"created_at"`
	UpdatedBy        *uint                   `json:"updated_by"`
	UpdatedAt        *time.Time              `json:"updated_at"`
	Items            []ArrivalItemDetailDTO  `json:"items"`
	// 附加信息（只包含必要的关联信息）
	ContractNo       string                  `json:"contract_no"`       // 合同编号
	SupplierName     string                  `json:"supplier_name"`     // 供应商名称
	OurCompanyName   string                  `json:"our_company_name"`  // 我方公司名称
	CreatorName      string                  `json:"creator_name"`      // 创建人姓名
	UpdaterName      string                  `json:"updater_name"`      // 更新人姓名
}

// ArrivalItemDetailDTO 到货明细详情数据传输对象
type ArrivalItemDetailDTO struct {
	*model.ArrivalItem
	// 关联的合同明细信息
	ContractItem *model.PurchaseContractItem `json:"contract_item,omitempty"` // 合同明细
	// 付款相关信息
	PaidQuantity     float64 `json:"paid_quantity"`      // 已付款数量
	PaidAmount       float64 `json:"paid_amount"`        // 已付款金额
	UnpaidQuantity   float64 `json:"unpaid_quantity"`    // 未付款数量
	UnpaidAmount     float64 `json:"unpaid_amount"`      // 未付款金额
	// 附加信息
	ContractItemNo string   `json:"contract_item_no,omitempty"` // 合同明细编号
	MaterialType   string   `json:"material_type,omitempty"`    // 物料类型
	Model          string   `json:"model,omitempty"`            // 型号
	Brand          string   `json:"brand,omitempty"`            // 品牌
	PN             string   `json:"pn,omitempty"`               // 原厂PN
	Spec           string   `json:"spec,omitempty"`             // 规格
	Unit           string   `json:"unit,omitempty"`             // 单位
	SerialNumberList []string `json:"serial_number_list,omitempty"` // SN号列表（解析后的）
}

// ArrivalListDTO 到货记录列表数据传输对象
type ArrivalListDTO struct {
	ID              uint                     `json:"id"`
	ArrivalNo       string                   `json:"arrival_no"`
	ContractID      uint                     `json:"contract_id"`
	ContractNo      string                   `json:"contract_no"`
	SupplierID      uint                     `json:"supplier_id"`
	SupplierName    string                   `json:"supplier_name"`
	OurCompanyName  string                   `json:"our_company_name"`
	MayArriveAt     *time.Time               `json:"may_arrive_at"`
	TrackingInfo    string                   `json:"tracking_info"`    // 物流信息
	ArrivalAddress  string                   `json:"arrival_address"`  // 到货地址
	ArrivalNotice   string                   `json:"arrival_notice"`   // 到货通知
	TotalQuantity   int                      `json:"total_quantity"`
	TotalAmount     float64                  `json:"total_amount"`
	IsComplete      bool                     `json:"is_complete"`
	Status          string                   `json:"status"`
	CreatedBy       *uint                    `json:"created_by"`
	CreatorName     string                   `json:"creator_name"`
	CreatedAt       time.Time                `json:"created_at"`
	Items           []*ArrivalItemDetailDTO  `json:"items"` // 到货明细列表
}

// ArrivalApprovalDTO 到货记录审批数据传输对象
type ArrivalApprovalDTO struct {
	ID           uint   `json:"id" binding:"required"`           // 到货记录ID
	CurrentStage string `json:"current_stage" binding:"required"` // 当前阶段
	Action       string `json:"action" binding:"required"`       // 审批动作 (approve/reject)
	Comments     string `json:"comments"`                        // 审批意见
}

// ArrivalRollbackDTO 到货记录回退数据传输对象
type ArrivalRollbackDTO struct {
	ID           uint   `json:"id" binding:"required"`           // 到货记录ID
	CurrentStage string `json:"current_stage" binding:"required"` // 当前阶段
	RollbackTo   string `json:"rollback_to" binding:"required"`   // 回退到的阶段
	Comments     string `json:"comments"`                        // 回退原因
}

// ArrivalHistoryDTO 到货记录历史数据传输对象
type ArrivalHistoryDTO struct {
	ID           uint      `json:"id"`
	ArrivalID    uint      `json:"arrival_id"`
	Action       string    `json:"action"`
	FromStatus   string    `json:"from_status"`
	ToStatus     string    `json:"to_status"`
	Comments     string    `json:"comments"`
	ApproverID   uint      `json:"approver_id"`
	ApproverName string    `json:"approver_name"`
	CreatedAt    time.Time `json:"created_at"`
}

// ArrivalStatisticsDTO 到货统计数据传输对象
type ArrivalStatisticsDTO struct {
	TotalCount      int64   `json:"total_count"`      // 总记录数
	DraftCount      int64   `json:"draft_count"`      // 草稿数量
	PendingCount    int64   `json:"pending_count"`    // 待审批数量
	CompletedCount  int64   `json:"completed_count"`  // 已完成数量
	CancelledCount  int64   `json:"cancelled_count"`  // 已取消数量
	TotalAmount     float64 `json:"total_amount"`     // 总金额
	CompletedAmount float64 `json:"completed_amount"` // 已完成金额
}
