package dto

import (
	"backend/internal/modules/purchase/model"
)

// CreateCompanyDTO 创建公司信息的数据传输对象
type CreateCompanyDTO struct {
	CompanyCode       string `json:"company_code"`
	CompanyName       string `json:"company_name" binding:"required"`
	ShortName         string `json:"short_name"`
	LegalPerson       string `json:"legal_person"`
	UnifiedCreditCode string `json:"unified_credit_code"`
	TaxNumber         string `json:"tax_number"`
	RegisteredAddress string `json:"registered_address"`
	BusinessAddress   string `json:"business_address"`
	BankName          string `json:"bank_name"`
	BankAccount       string `json:"bank_account"`
	ContactPerson     string `json:"contact_person"`
	ContactPhone      string `json:"contact_phone"`
	InvoiceTitle      string `json:"invoice_title"`
	InvoiceAddress    string `json:"invoice_address"`
	Status            int8   `json:"status"`
	CreatedBy         uint   `json:"created_by"`
}

// UpdateCompanyDTO 更新公司信息的数据传输对象
type UpdateCompanyDTO struct {
	ID                uint   `json:"id" binding:"required"`
	CompanyName       string `json:"company_name"`
	ShortName         string `json:"short_name"`
	LegalPerson       string `json:"legal_person"`
	UnifiedCreditCode string `json:"unified_credit_code"`
	TaxNumber         string `json:"tax_number"`
	RegisteredAddress string `json:"registered_address"`
	BusinessAddress   string `json:"business_address"`
	BankName          string `json:"bank_name"`
	BankAccount       string `json:"bank_account"`
	ContactPerson     string `json:"contact_person"`
	ContactPhone      string `json:"contact_phone"`
	InvoiceTitle      string `json:"invoice_title"`
	InvoiceAddress    string `json:"invoice_address"`
	Status            *int8  `json:"status"`
	UpdatedBy         uint   `json:"updated_by"`
}

// CompanyListQuery 公司列表查询参数
type CompanyListQuery struct {
	model.CompanyFilter
	model.PaginationOptions
}

// CompanyListResult 公司列表查询结果
type CompanyListResult struct {
	Total int64            `json:"total"`
	List  []*model.Company `json:"list"`
}
