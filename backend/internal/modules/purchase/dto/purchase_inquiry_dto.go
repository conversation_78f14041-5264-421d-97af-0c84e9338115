package dto

import (
	"backend/internal/modules/purchase/model"
	"time"
)

// CreatePurchaseInquiryDTO 创建采购询价的数据传输对象
type CreatePurchaseInquiryDTO struct {
	RequestID           *uint                         `json:"request_id"`
	ProjectID           *uint                         `json:"project_id"`
	SupplierID          uint                          `json:"supplier_id" binding:"required"`
	SupplierDescription string                        `json:"supplier_description"`
	RequestItems        []CreateInquiryRequestItemDTO `json:"request_items" binding:"required,min=1"`
	CreatedBy           uint                          `json:"created_by"`
}

// CreateInquiryRequestItemDTO 创建询价申请关联项的数据传输对象
type CreateInquiryRequestItemDTO struct {
	RequestItemID          uint     `json:"request_item_id" binding:"required"`
	CurrentInquiryQuantity int      `json:"current_inquiry_quantity" binding:"required,min=1"`
	PN                     string   `json:"pn"`
	BudgetPrice            *float64 `json:"budget_price"`
	Remark                 string   `json:"remark"`
}

// AddInquiryItemsDTO 向询价单添加明细的数据传输对象
type AddInquiryItemsDTO struct {
	InquiryID uint                          `json:"inquiry_id" binding:"required"`
	Items     []CreateInquiryRequestItemDTO `json:"items" binding:"required,min=1"`
	UpdatedBy uint                          `json:"updated_by"`
}

// UpdateBudgetDTO 更新预算数据传输对象
type UpdateBudgetDTO struct {
	InquiryID   uint            `json:"inquiry_id" binding:"required"`
	BudgetItems []BudgetItemDTO `json:"budget_items" binding:"required,min=1"`
	UpdatedBy   uint            `json:"updated_by"`
}

// BudgetItemDTO 预算明细数据传输对象
type BudgetItemDTO struct {
	InquiryItemID uint     `json:"inquiry_item_id" binding:"required"`
	PN            string   `json:"pn"`
	BudgetPrice   *float64 `json:"budget_price"`
	Remark        string   `json:"remark"`
}

// CreatePurchaseInquiryItemDTO 创建采购询价明细的数据传输对象
type CreatePurchaseInquiryItemDTO struct {
	RequestItemID          uint     `json:"request_item_id" binding:"required"`
	PN                     string   `json:"pn"`
	InquiredQuantity       int      `json:"inquired_quantity"`
	CurrentInquiryQuantity int      `json:"current_inquiry_quantity" binding:"required,min=1"`
	BudgetPrice            *float64 `json:"budget_price"`
	BudgetAmount           *float64 `json:"budget_amount"`
	Remark                 string   `json:"remark"`
}

// UpdatePurchaseInquiryDTO 更新采购询价的数据传输对象
type UpdatePurchaseInquiryDTO struct {
	ID                  uint                           `json:"id" binding:"required"`
	RequestID           *uint                          `json:"request_id"`
	ProjectID           *uint                          `json:"project_id"`
	SupplierID          uint                           `json:"supplier_id"`
	SupplierDescription string                         `json:"supplier_description"`
	Items               []UpdatePurchaseInquiryItemDTO `json:"items"`
	UpdatedBy           uint                           `json:"updated_by"`
}

// UpdatePurchaseInquiryItemDTO 更新采购询价明细的数据传输对象
type UpdatePurchaseInquiryItemDTO struct {
	ID                     uint     `json:"id"`
	RequestItemID          uint     `json:"request_item_id"`
	PN                     string   `json:"pn"`
	InquiredQuantity       int      `json:"inquired_quantity"`
	CurrentInquiryQuantity int      `json:"current_inquiry_quantity"`
	BudgetPrice            *float64 `json:"budget_price"`
	BudgetAmount           *float64 `json:"budget_amount"`
	Remark                 string   `json:"remark"`
}

// UpdatePurchaseInquiryStatusDTO 更新采购询价状态的数据传输对象
type UpdatePurchaseInquiryStatusDTO struct {
	ID        uint   `json:"id" binding:"required"`
	Status    string `json:"status" binding:"required"`
	UpdatedBy uint   `json:"updated_by"`
}

// PurchaseInquiryListQuery 采购询价列表查询参数
type PurchaseInquiryListQuery struct {
	model.PurchaseInquiryFilter
	model.PaginationOptions
}

// PurchaseInquiryListResult 采购询价列表查询结果
type PurchaseInquiryListResult struct {
	Total int64                    `json:"total"`
	List  []*model.PurchaseInquiry `json:"list"`
}

// InquiryApprovalDTO 询价审批数据传输对象
type InquiryApprovalDTO struct {
	InquiryID    uint   `json:"inquiry_id" binding:"required"`
	ApproverID   uint   `json:"approver_id"`               // 从JWT自动获取，前端无需传递
	Action       string `json:"action" binding:"required"` // approve, reject
	Comments     string `json:"comments"`
	CurrentStage string `json:"current_stage" binding:"required"` // 当前审批阶段
}

// InquiryRollbackDTO 询价回退数据传输对象
type InquiryRollbackDTO struct {
	InquiryID    uint   `json:"inquiry_id" binding:"required"`
	ApproverID   uint   `json:"approver_id"`                      // 从JWT自动获取，前端无需传递
	RollbackTo   string `json:"rollback_to" binding:"required"`   // 回退到的阶段
	CurrentStage string `json:"current_stage" binding:"required"` // 当前阶段
	Comments     string `json:"comments"`
}

// InquiryWorkflowStatusQueryDTO 询价工作流状态查询数据
type InquiryWorkflowStatusQueryDTO struct {
	InquiryID uint `json:"inquiry_id" binding:"required"`
}

// InquiryWorkflowStatusResponseDTO 询价工作流状态响应数据
type InquiryWorkflowStatusResponseDTO struct {
	InquiryID       uint                        `json:"inquiry_id"`
	CurrentStage    string                      `json:"current_stage"`
	CanRollback     bool                        `json:"can_rollback"`
	StageHistory    []string                    `json:"stage_history"`
	ApprovalHistory []InquiryApprovalHistoryDTO `json:"approval_history"`
	RollbackHistory []InquiryRollbackHistoryDTO `json:"rollback_history"`
	Result          string                      `json:"result"`
	StartedAt       time.Time                   `json:"started_at"`
	CompletedAt     *time.Time                  `json:"completed_at"`
}

// InquiryApprovalHistoryDTO 询价审批历史数据
type InquiryApprovalHistoryDTO struct {
	ApproverID   uint      `json:"approver_id"`
	Action       string    `json:"action"`
	Comments     string    `json:"comments"`
	CurrentStage string    `json:"current_stage"`
	Timestamp    time.Time `json:"timestamp"`
}

// InquiryRollbackHistoryDTO 询价回退历史数据
type InquiryRollbackHistoryDTO struct {
	ApproverID   uint      `json:"approver_id"`
	RollbackTo   string    `json:"rollback_to"`
	CurrentStage string    `json:"current_stage"`
	Comments     string    `json:"comments"`
	Timestamp    time.Time `json:"timestamp"`
}

// PurchaseInquiryHistoryDTO 采购询价历史记录数据
type PurchaseInquiryHistoryDTO struct {
	ID             uint      `json:"id"`
	BusinessType   string    `json:"business_type"`
	BusinessID     uint      `json:"business_id"`
	PreviousStatus string    `json:"previous_status"`
	NewStatus      string    `json:"new_status"`
	Action         string    `json:"action"`
	OperatorID     uint      `json:"operator_id"`
	OperatorName   string    `json:"operator_name"`
	OperationTime  time.Time `json:"operation_time"`
	Comments       string    `json:"comments"`
	CreatedAt      time.Time `json:"created_at"`
}

// PurchaseInquiryDetailDTO 采购询价详情数据传输对象（简化版本）
type PurchaseInquiryDetailDTO struct {
	*model.PurchaseInquiry
	ItemsDetail []InquiryItemDetailDTO `json:"items_detail"`
}

// InquiryItemDetailDTO 询价明细详情数据传输对象
type InquiryItemDetailDTO struct {
	*model.PurchaseInquiryItem
	// 从申请明细中读取的物料信息
	MaterialType       string `json:"material_type"`
	Model              string `json:"model"`
	Brand              string `json:"brand"`
	OriginalPN         string `json:"original_pn"`
	Spec               string `json:"spec"`
	Unit               string `json:"unit"`
	TotalQuantity      int    `json:"total_quantity"`
	UninquiredQuantity int    `json:"uninquired_quantity"`
}

// InquiryQuantityValidationDTO 询价数量验证数据传输对象
type InquiryQuantityValidationDTO struct {
	RequestItemID          uint `json:"request_item_id"`
	CurrentInquiryQuantity int  `json:"current_inquiry_quantity"`
}

// BatchCreateInquiryDTO 批量创建询价数据传输对象
type BatchCreateInquiryDTO struct {
	RequestID         uint                 `json:"request_id" binding:"required"`
	SupplierInquiries []SupplierInquiryDTO `json:"supplier_inquiries" binding:"required,min=1"`
	CreatedBy         uint                 `json:"created_by"`
}

// SupplierInquiryDTO 供应商询价数据传输对象
type SupplierInquiryDTO struct {
	SupplierID          uint                           `json:"supplier_id" binding:"required"`
	InquiryDate         *time.Time                     `json:"inquiry_date"`
	QuoteDeadline       *time.Time                     `json:"quote_deadline"`
	SupplierDescription string                         `json:"supplier_description"`
	Items               []CreatePurchaseInquiryItemDTO `json:"items" binding:"required,min=1"`
}

// InquiryStatisticsDTO 询价统计数据传输对象
type InquiryStatisticsDTO struct {
	RequestID           uint                           `json:"request_id"`
	TotalInquiries      int                            `json:"total_inquiries"`
	InProgressInquiries int                            `json:"in_progress_inquiries"`
	PendingInquiries    int                            `json:"pending_inquiries"`
	ItemStatistics      []InquiryItemStatisticsDTO     `json:"item_statistics"`
	SupplierStatistics  []InquirySupplierStatisticsDTO `json:"supplier_statistics"`
}

// InquiryItemStatisticsDTO 询价物料统计数据传输对象
type InquiryItemStatisticsDTO struct {
	RequestItemID      uint    `json:"request_item_id"`
	ProductName        string  `json:"product_name"`
	TotalQuantity      int     `json:"total_quantity"`
	InquiredQuantity   int     `json:"inquired_quantity"`
	UninquiredQuantity int     `json:"uninquired_quantity"`
	InquiryProgress    float64 `json:"inquiry_progress"`
	InquiryCount       int     `json:"inquiry_count"`
}

// InquirySupplierStatisticsDTO 询价供应商统计数据传输对象
type InquirySupplierStatisticsDTO struct {
	SupplierID   uint    `json:"supplier_id"`
	SupplierName string  `json:"supplier_name"`
	InquiryCount int     `json:"inquiry_count"`
	QuotedCount  int     `json:"quoted_count"`
	PendingCount int     `json:"pending_count"`
	QuoteRate    float64 `json:"quote_rate"`
}

// RequestItemsInquiryStatusResult 采购申请明细询价状态结果
type RequestItemsInquiryStatusResult struct {
	RequestID uint                       `json:"request_id"`
	RequestNo string                     `json:"request_no"`
	Items     []RequestItemInquiryStatus `json:"items"`
}

// RequestItemInquiryStatus 采购申请明细询价状态
type RequestItemInquiryStatus struct {
	RequestItemID      uint    `json:"request_item_id"`
	MaterialType       string  `json:"material_type"`
	Model              string  `json:"model"`
	Brand              string  `json:"brand"`
	PN                 string  `json:"pn"`
	Specifications     string  `json:"specifications"`
	Unit               string  `json:"unit"`
	TotalQuantity      int     `json:"total_quantity"`
	InquiredQuantity   int     `json:"inquired_quantity"`
	UninquiredQuantity int     `json:"uninquired_quantity"`
	InquiryProgress    float64 `json:"inquiry_progress"`
}
