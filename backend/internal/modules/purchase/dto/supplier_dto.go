package dto

import (
	"backend/internal/modules/purchase/model"
	"time"
)

// CreateSupplierDTO 创建供应商信息的数据传输对象
type CreateSupplierDTO struct {
	SupplierCode      string     `json:"supplier_code"`
	SupplierName      string     `json:"supplier_name" binding:"required"`
	ShortName         string     `json:"short_name"`
	Contact<PERSON>erson     string     `json:"contact_person"`
	ContactPhone      string     `json:"contact_phone"`
	ContactEmail      string     `json:"contact_email"`
	Address           string     `json:"address"`
	TaxNumber         string     `json:"tax_number"`
	BankName          string     `json:"bank_name"`
	BankAccount       string     `json:"bank_account"`
	BusinessLicense   string     `json:"business_license"`
	Categories        []string   `json:"categories"`
	ContractStartDate *time.Time `json:"contract_start_date"`
	ContractEndDate   *time.Time `json:"contract_end_date"`
	CreditLevel       int        `json:"credit_level"`
	Status            int8       `json:"status"`
	Remark            string     `json:"remark"`
	CreatedBy         uint       `json:"created_by"`
}

// UpdateSupplierDTO 更新供应商信息的数据传输对象
type UpdateSupplierDTO struct {
	ID                uint       `json:"id" binding:"required"`
	SupplierName      string     `json:"supplier_name"`
	ShortName         string     `json:"short_name"`
	ContactPerson     string     `json:"contact_person"`
	ContactPhone      string     `json:"contact_phone"`
	ContactEmail      string     `json:"contact_email"`
	Address           string     `json:"address"`
	TaxNumber         string     `json:"tax_number"`
	BankName          string     `json:"bank_name"`
	BankAccount       string     `json:"bank_account"`
	BusinessLicense   string     `json:"business_license"`
	Categories        []string   `json:"categories"`
	ContractStartDate *time.Time `json:"contract_start_date"`
	ContractEndDate   *time.Time `json:"contract_end_date"`
	CreditLevel       int        `json:"credit_level"`
	Status            *int8      `json:"status"`
	Remark            string     `json:"remark"`
	UpdatedBy         uint       `json:"updated_by"`
}

// SupplierListQuery 供应商列表查询参数
type SupplierListQuery struct {
	model.SupplierFilter
	model.PaginationOptions
}

// SupplierListResult 供应商列表查询结果
type SupplierListResult struct {
	Total int64             `json:"total"`
	List  []*model.Supplier `json:"list"`
}
