package dto

import (
	"backend/internal/modules/purchase/model"
	"time"
)

// CreateProjectDTO 创建项目信息的数据传输对象
type CreateProjectDTO struct {
	ProjectCode    string     `json:"project_code"`
	ProjectName    string     `json:"project_name" binding:"required"`
	CustomerName   string     `json:"customer_name" binding:"required"`
	ContractInfo   string     `json:"contract_info"`
	ProjectManager string     `json:"project_manager"`
	ProjectAddress string     `json:"project_address"`
	ContactPerson  string     `json:"contact_person"`
	ContactPhone   string     `json:"contact_phone"`
	ContactEmail   string     `json:"contact_email"`
	ProjectStatus  int8       `json:"project_status"`
	StartDate      *time.Time `json:"start_date"`
	EndDate        *time.Time `json:"end_date"`
	Description    string     `json:"description"`
	CreatedBy      uint       `json:"created_by"`
}

// UpdateProjectDTO 更新项目信息的数据传输对象
type UpdateProjectDTO struct {
	ID             uint       `json:"id" binding:"required"`
	ProjectName    string     `json:"project_name"`
	CustomerName   string     `json:"customer_name"`
	ContractInfo   string     `json:"contract_info"`
	ProjectManager string     `json:"project_manager"`
	ProjectAddress string     `json:"project_address"`
	ContactPerson  string     `json:"contact_person"`
	ContactPhone   string     `json:"contact_phone"`
	ContactEmail   string     `json:"contact_email"`
	ProjectStatus  *int8      `json:"project_status"`
	StartDate      *time.Time `json:"start_date"`
	EndDate        *time.Time `json:"end_date"`
	Description    string     `json:"description"`
	UpdatedBy      uint       `json:"updated_by"`
}

// ProjectListQuery 项目列表查询参数
type ProjectListQuery struct {
	model.ProjectFilter
	model.PaginationOptions
}

// ProjectListResult 项目列表查询结果
type ProjectListResult struct {
	Total int64            `json:"total"`
	List  []*model.Project `json:"list"`
}
