package repository

import (
	"backend/internal/modules/purchase/model"
	"context"

	"gorm.io/gorm"
)

// CompanyRepository 公司信息仓库接口
type CompanyRepository interface {
	// GetByID 根据ID获取公司信息
	GetByID(ctx context.Context, id uint) (*model.Company, error)

	// GetByCompanyCode 根据公司编码获取公司信息
	GetByCompanyCode(ctx context.Context, companyCode string) (*model.Company, error)

	// List 获取公司信息列表
	List(ctx context.Context, filter model.CompanyFilter, pagination model.PaginationOptions) ([]*model.Company, int64, error)

	// Create 创建公司信息
	Create(ctx context.Context, company *model.Company) error

	// Update 更新公司信息
	Update(ctx context.Context, company *model.Company) error

	// Delete 删除公司信息
	Delete(ctx context.Context, id uint, deletedBy uint) error

	// GetDB 获取数据库实例
	GetDB() *gorm.DB
}

// companyRepository 公司信息仓库实现
type companyRepository struct {
	db *gorm.DB
}

// NewCompanyRepository 创建公司信息仓库实例
func NewCompanyRepository(db *gorm.DB) CompanyRepository {
	return &companyRepository{db: db}
}

// GetByID 根据ID获取公司信息
func (r *companyRepository) GetByID(ctx context.Context, id uint) (*model.Company, error) {
	var company model.Company
	if err := r.db.WithContext(ctx).First(&company, id).Error; err != nil {
		return nil, err
	}
	return &company, nil
}

// GetByCompanyCode 根据公司编码获取公司信息
func (r *companyRepository) GetByCompanyCode(ctx context.Context, companyCode string) (*model.Company, error) {
	var company model.Company
	if err := r.db.WithContext(ctx).Where("company_code = ?", companyCode).First(&company).Error; err != nil {
		return nil, err
	}
	return &company, nil
}

// List 获取公司信息列表
func (r *companyRepository) List(ctx context.Context, filter model.CompanyFilter, pagination model.PaginationOptions) ([]*model.Company, int64, error) {
	var companies []*model.Company
	var total int64

	query := r.db.WithContext(ctx).Model(&model.Company{})

	// 应用过滤条件
	if filter.CompanyCode != "" {
		query = query.Where("company_code LIKE ?", "%"+filter.CompanyCode+"%")
	}
	if filter.CompanyName != "" {
		query = query.Where("company_name LIKE ?", "%"+filter.CompanyName+"%")
	}
	if filter.ShortName != "" {
		query = query.Where("short_name LIKE ?", "%"+filter.ShortName+"%")
	}
	if filter.ContactPerson != "" {
		query = query.Where("contact_person LIKE ?", "%"+filter.ContactPerson+"%")
	}
	if filter.ContactPhone != "" {
		query = query.Where("contact_phone LIKE ?", "%"+filter.ContactPhone+"%")
	}
	if filter.Status != nil {
		query = query.Where("status = ?", *filter.Status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 应用分页
	if pagination.Page > 0 && pagination.PageSize > 0 {
		offset := (pagination.Page - 1) * pagination.PageSize
		query = query.Offset(offset).Limit(pagination.PageSize)
	}

	// 排序
	query = query.Order("created_at DESC")

	// 执行查询
	if err := query.Find(&companies).Error; err != nil {
		return nil, 0, err
	}

	return companies, total, nil
}

// Create 创建公司信息
func (r *companyRepository) Create(ctx context.Context, company *model.Company) error {
	return r.db.WithContext(ctx).Create(company).Error
}

// Update 更新公司信息
func (r *companyRepository) Update(ctx context.Context, company *model.Company) error {
	return r.db.WithContext(ctx).Save(company).Error
}

// Delete 删除公司信息
func (r *companyRepository) Delete(ctx context.Context, id uint, deletedBy uint) error {
	// 先更新操作人ID到updated_by字段
	if err := r.db.WithContext(ctx).Model(&model.Company{}).
		Where("id = ?", id).
		Update("updated_by", deletedBy).Error; err != nil {
		return err
	}

	// 然后执行软删除
	return r.db.WithContext(ctx).Delete(&model.Company{}, id).Error
}

// GetDB 获取数据库实例
func (r *companyRepository) GetDB() *gorm.DB {
	return r.db
}
