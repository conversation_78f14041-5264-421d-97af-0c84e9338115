package repository

import (
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/model"
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// InvoiceRepository 发票仓库接口
type InvoiceRepository interface {
	// Create 创建发票
	Create(ctx context.Context, invoice *model.Invoice) error

	// GetByID 根据ID获取发票
	GetByID(ctx context.Context, id uint) (*model.Invoice, error)

	// GetByInvoiceNo 根据发票号获取发票
	GetByInvoiceNo(ctx context.Context, invoiceNo string) (*model.Invoice, error)

	// Update 更新发票
	Update(ctx context.Context, invoice *model.Invoice) error

	// Delete 删除发票
	Delete(ctx context.Context, id uint) error

	// List 获取发票列表
	List(ctx context.Context, query *dto.InvoiceQueryDTO) ([]*model.Invoice, int64, error)

	// GetByContractID 根据合同ID获取发票列表
	GetByContractID(ctx context.Context, contractID uint) ([]*model.Invoice, error)

	// GetTotalInvoiceAmountByContractID 获取合同的总开票金额
	GetTotalInvoiceAmountByContractID(ctx context.Context, contractID uint, excludeInvoiceID *uint) (float64, error)

	// ExistsByInvoiceNo 检查发票号是否存在
	ExistsByInvoiceNo(ctx context.Context, invoiceNo string, excludeID *uint) (bool, error)

	// GetInvoiceSummaryByContractID 获取合同的发票汇总信息
	GetInvoiceSummaryByContractID(ctx context.Context, contractID uint) (*dto.InvoiceSummaryDTO, error)
}

// invoiceRepository 发票仓库实现
type invoiceRepository struct {
	db *gorm.DB
}

// NewInvoiceRepository 创建发票仓库实例
func NewInvoiceRepository(db *gorm.DB) InvoiceRepository {
	return &invoiceRepository{db: db}
}

// Create 创建发票
func (r *invoiceRepository) Create(ctx context.Context, invoice *model.Invoice) error {
	return r.db.WithContext(ctx).Create(invoice).Error
}

// GetByID 根据ID获取发票
func (r *invoiceRepository) GetByID(ctx context.Context, id uint) (*model.Invoice, error) {
	var invoice model.Invoice
	err := r.db.WithContext(ctx).
		Preload("Contract", func(db *gorm.DB) *gorm.DB {
			return db.Select("id, contract_no, total_amount, supplier_id, project_id, our_company_id").
				Preload("Supplier", func(db *gorm.DB) *gorm.DB {
					return db.Select("id, supplier_name")
				}).
				Preload("Project", func(db *gorm.DB) *gorm.DB {
					return db.Select("id, project_name")
				}).
				Preload("OurCompany", func(db *gorm.DB) *gorm.DB {
					return db.Select("id, company_name")
				})
		}).
		First(&invoice, id).Error
	if err != nil {
		return nil, err
	}
	return &invoice, nil
}

// GetByInvoiceNo 根据发票号获取发票
func (r *invoiceRepository) GetByInvoiceNo(ctx context.Context, invoiceNo string) (*model.Invoice, error) {
	var invoice model.Invoice
	err := r.db.WithContext(ctx).
		Preload("Contract", func(db *gorm.DB) *gorm.DB {
			return db.Select("id, contract_no, total_amount, supplier_id, project_id, our_company_id").
				Preload("Supplier", func(db *gorm.DB) *gorm.DB {
					return db.Select("id, supplier_name")
				}).
				Preload("Project", func(db *gorm.DB) *gorm.DB {
					return db.Select("id, project_name")
				}).
				Preload("OurCompany", func(db *gorm.DB) *gorm.DB {
					return db.Select("id, company_name")
				})
		}).
		Where("invoice_no = ?", invoiceNo).
		First(&invoice).Error
	if err != nil {
		return nil, err
	}
	return &invoice, nil
}

// Update 更新发票
func (r *invoiceRepository) Update(ctx context.Context, invoice *model.Invoice) error {
	return r.db.WithContext(ctx).Save(invoice).Error
}

// Delete 删除发票
func (r *invoiceRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.Invoice{}, id).Error
}

// List 获取发票列表
func (r *invoiceRepository) List(ctx context.Context, query *dto.InvoiceQueryDTO) ([]*model.Invoice, int64, error) {
	var invoices []*model.Invoice
	var total int64

	db := r.db.WithContext(ctx).Model(&model.Invoice{})

	// 构建查询条件
	if query.InvoiceNo != "" {
		db = db.Where("invoice_no LIKE ?", "%"+query.InvoiceNo+"%")
	}
	if query.ContractID != nil {
		db = db.Where("contract_id = ?", *query.ContractID)
	}
	if query.CreatedBy != nil {
		db = db.Where("created_by = ?", *query.CreatedBy)
	}
	if query.MinAmount != nil {
		db = db.Where("invoice_amount >= ?", *query.MinAmount)
	}
	if query.MaxAmount != nil {
		db = db.Where("invoice_amount <= ?", *query.MaxAmount)
	}

	// 日期范围查询
	if query.StartDate != "" {
		if startDate, err := time.Parse("2006-01-02", query.StartDate); err == nil {
			db = db.Where("created_at >= ?", startDate)
		}
	}
	if query.EndDate != "" {
		if endDate, err := time.Parse("2006-01-02", query.EndDate); err == nil {
			endDate = endDate.Add(24*time.Hour - time.Second) // 包含结束日期的整天
			db = db.Where("created_at <= ?", endDate)
		}
	}

	// 合同编号查询（需要关联查询）
	if query.ContractNo != "" {
		db = db.Joins("JOIN purchase_contracts ON invoices.contract_id = purchase_contracts.id").
			Where("purchase_contracts.contract_no LIKE ?", "%"+query.ContractNo+"%")
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (query.Page - 1) * query.PageSize
	err := db.
		Preload("Contract").
		Preload("Contract.Supplier").
		Preload("Contract.Project").
		Preload("Contract.OurCompany").
		Order("created_at DESC").
		Offset(offset).
		Limit(query.PageSize).
		Find(&invoices).Error

	return invoices, total, err
}

// GetByContractID 根据合同ID获取发票列表
func (r *invoiceRepository) GetByContractID(ctx context.Context, contractID uint) ([]*model.Invoice, error) {
	var invoices []*model.Invoice
	err := r.db.WithContext(ctx).
		Where("contract_id = ?", contractID).
		Order("created_at DESC").
		Find(&invoices).Error
	return invoices, err
}

// GetTotalInvoiceAmountByContractID 获取合同的总开票金额
func (r *invoiceRepository) GetTotalInvoiceAmountByContractID(ctx context.Context, contractID uint, excludeInvoiceID *uint) (float64, error) {
	var totalAmount float64
	db := r.db.WithContext(ctx).Model(&model.Invoice{}).
		Where("contract_id = ?", contractID).
		Select("COALESCE(SUM(invoice_amount), 0)")

	if excludeInvoiceID != nil {
		db = db.Where("id != ?", *excludeInvoiceID)
	}

	err := db.Scan(&totalAmount).Error
	return totalAmount, err
}

// ExistsByInvoiceNo 检查发票号是否存在
func (r *invoiceRepository) ExistsByInvoiceNo(ctx context.Context, invoiceNo string, excludeID *uint) (bool, error) {
	var count int64
	db := r.db.WithContext(ctx).Model(&model.Invoice{}).Where("invoice_no = ?", invoiceNo)

	if excludeID != nil {
		db = db.Where("id != ?", *excludeID)
	}

	err := db.Count(&count).Error
	return count > 0, err
}

// GetInvoiceSummaryByContractID 获取合同的发票汇总信息
func (r *invoiceRepository) GetInvoiceSummaryByContractID(ctx context.Context, contractID uint) (*dto.InvoiceSummaryDTO, error) {
	var summary dto.InvoiceSummaryDTO

	// 获取合同信息
	var contract model.PurchaseContract
	if err := r.db.WithContext(ctx).First(&contract, contractID).Error; err != nil {
		return nil, fmt.Errorf("获取合同信息失败 (合同ID: %d): %w", contractID, err)
	}

	summary.ContractID = contract.ID
	summary.ContractNo = contract.ContractNo
	summary.ContractTotalAmount = contract.TotalAmount

	// 获取发票汇总信息
	var invoiceStats struct {
		TotalInvoicedAmount float64 `json:"total_invoiced_amount"`
		InvoiceCount        int     `json:"invoice_count"`
	}

	err := r.db.WithContext(ctx).Model(&model.Invoice{}).
		Where("contract_id = ?", contractID).
		Select("COALESCE(SUM(invoice_amount), 0) as total_invoiced_amount, COUNT(*) as invoice_count").
		Scan(&invoiceStats).Error

	if err != nil {
		return nil, fmt.Errorf("获取发票汇总信息失败 (合同ID: %d): %w", contractID, err)
	}

	summary.TotalInvoicedAmount = invoiceStats.TotalInvoicedAmount
	summary.InvoiceCount = invoiceStats.InvoiceCount
	summary.RemainingAmount = summary.ContractTotalAmount - summary.TotalInvoicedAmount

	return &summary, nil
}
