package repository

import (
	"backend/internal/modules/purchase/constants"
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/model"
	"context"
	"time"

	"gorm.io/gorm"
)

// PaymentRequestRepository 付款申请仓库接口
type PaymentRequestRepository interface {
	// Create 创建付款申请
	Create(ctx context.Context, paymentRequest *model.PaymentRequest) error

	// GetByID 根据ID获取付款申请
	GetByID(ctx context.Context, id uint) (*model.PaymentRequest, error)

	// GetByPaymentNo 根据付款申请单号获取付款申请
	GetByPaymentNo(ctx context.Context, paymentNo string) (*model.PaymentRequest, error)

	// Update 更新付款申请
	Update(ctx context.Context, paymentRequest *model.PaymentRequest) error

	// UpdateStatus 更新付款申请状态
	UpdateStatus(ctx context.Context, id uint, status string, updatedBy uint) error

	// Delete 删除付款申请
	Delete(ctx context.Context, id uint) error

	// List 获取付款申请列表
	List(ctx context.Context, query *dto.PaymentRequestQueryDTO) ([]*model.PaymentRequest, int64, error)

	// GetByContractID 根据合同ID获取付款申请列表
	GetByContractID(ctx context.Context, contractID uint) ([]*model.PaymentRequest, error)

	// GetItemsByContractItemID 根据合同明细ID获取付款申请明细列表
	GetItemsByContractItemID(ctx context.Context, contractItemID uint) ([]*model.PaymentRequestItem, error)

	// GetBySupplierID 根据供应商ID获取付款申请列表
	GetBySupplierID(ctx context.Context, supplierID uint) ([]*model.PaymentRequest, error)

	// IsPaymentNoExists 检查付款申请单号是否存在
	IsPaymentNoExists(ctx context.Context, paymentNo string) (bool, error)

	// GetTotalPaidAmountByContract 获取合同的总已付款金额
	GetTotalPaidAmountByContract(ctx context.Context, contractID uint) (float64, error)

	// GetPaymentRequestHistory 获取付款申请历史记录
	GetPaymentRequestHistory(ctx context.Context, paymentID uint) ([]*model.PurchaseApprovalHistory, error)

	// CreateStatusHistory 创建付款申请状态历史记录
	CreateStatusHistory(ctx context.Context, history *model.PurchaseApprovalHistory) error
}

// paymentRequestRepository 付款申请仓库实现
type paymentRequestRepository struct {
	db *gorm.DB
}

// NewPaymentRequestRepository 创建付款申请仓库实例
func NewPaymentRequestRepository(db *gorm.DB) PaymentRequestRepository {
	return &paymentRequestRepository{db: db}
}

// Create 创建付款申请
func (r *paymentRequestRepository) Create(ctx context.Context, paymentRequest *model.PaymentRequest) error {
	return r.db.WithContext(ctx).Create(paymentRequest).Error
}

// GetByID 根据ID获取付款申请
func (r *paymentRequestRepository) GetByID(ctx context.Context, id uint) (*model.PaymentRequest, error) {
	var paymentRequest model.PaymentRequest
	err := r.db.WithContext(ctx).
		Preload("Contract").
		Preload("Contract.OurCompany").
		Preload("Supplier").
		Preload("Company").
		Preload("Items").
		Preload("Items.ContractItem").
		First(&paymentRequest, id).Error
	if err != nil {
		return nil, err
	}
	return &paymentRequest, nil
}

// GetByPaymentNo 根据付款申请单号获取付款申请
func (r *paymentRequestRepository) GetByPaymentNo(ctx context.Context, paymentNo string) (*model.PaymentRequest, error) {
	var paymentRequest model.PaymentRequest
	err := r.db.WithContext(ctx).
		Preload("Contract").
		Preload("Supplier").
		Preload("Company").
		Preload("Items").
		Preload("Items.ContractItem").
		Where("payment_no = ?", paymentNo).
		First(&paymentRequest).Error
	if err != nil {
		return nil, err
	}
	return &paymentRequest, nil
}

// Update 更新付款申请
func (r *paymentRequestRepository) Update(ctx context.Context, paymentRequest *model.PaymentRequest) error {
	return r.db.WithContext(ctx).Save(paymentRequest).Error
}

// UpdateStatus 更新付款申请状态
func (r *paymentRequestRepository) UpdateStatus(ctx context.Context, id uint, status string, updatedBy uint) error {
	now := time.Now()
	return r.db.WithContext(ctx).Model(&model.PaymentRequest{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":     status,
			"updated_by": updatedBy,
			"updated_at": &now,
		}).Error
}

// Delete 删除付款申请
func (r *paymentRequestRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.PaymentRequest{}, id).Error
}

// List 获取付款申请列表
func (r *paymentRequestRepository) List(ctx context.Context, query *dto.PaymentRequestQueryDTO) ([]*model.PaymentRequest, int64, error) {
	var paymentRequests []*model.PaymentRequest
	var total int64

	db := r.db.WithContext(ctx).Model(&model.PaymentRequest{})

	// 构建查询条件
	if query.PaymentNo != "" {
		db = db.Where("payment_requests.payment_no LIKE ?", "%"+query.PaymentNo+"%")
	}
	if query.ContractID != nil {
		db = db.Where("payment_requests.contract_id = ?", *query.ContractID)
	}
	if query.ContractNo != "" {
		// 根据合同编号过滤，需要JOIN合同表
		db = db.Joins("JOIN purchase_contracts ON payment_requests.contract_id = purchase_contracts.id").
			Where("purchase_contracts.contract_no LIKE ?", "%"+query.ContractNo+"%")
	}
	if query.SupplierID != nil {
		db = db.Where("payment_requests.supplier_id = ?", *query.SupplierID)
	}
	if query.PaymentType != "" {
		db = db.Where("payment_requests.payment_type = ?", query.PaymentType)
	}
	if query.Status != "" {
		db = db.Where("payment_requests.status = ?", query.Status)
	}
	if query.CreatedBy != nil {
		db = db.Where("payment_requests.created_by = ?", *query.CreatedBy)
	}
	if query.StartDate != "" {
		db = db.Where("payment_requests.created_at >= ?", query.StartDate)
	}
	if query.EndDate != "" {
		db = db.Where("payment_requests.created_at <= ?", query.EndDate)
	}
	if query.MinAmount != nil {
		db = db.Where("payment_requests.current_payment_amount >= ?", *query.MinAmount)
	}
	if query.MaxAmount != nil {
		db = db.Where("payment_requests.current_payment_amount <= ?", *query.MaxAmount)
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (query.Page - 1) * query.PageSize
	err := db.Preload("Contract").
		Preload("Contract.OurCompany").
		Preload("Supplier").
		Preload("Items").
		Preload("Items.ContractItem").
		Offset(offset).
		Limit(query.PageSize).
		Order("payment_requests.created_at DESC").
		Find(&paymentRequests).Error

	return paymentRequests, total, err
}

// GetByContractID 根据合同ID获取付款申请列表
func (r *paymentRequestRepository) GetByContractID(ctx context.Context, contractID uint) ([]*model.PaymentRequest, error) {
	var paymentRequests []*model.PaymentRequest
	err := r.db.WithContext(ctx).
		Preload("Supplier").
		Preload("Items").
		Where("contract_id = ?", contractID).
		Order("created_at DESC").
		Find(&paymentRequests).Error
	return paymentRequests, err
}

// GetBySupplierID 根据供应商ID获取付款申请列表
func (r *paymentRequestRepository) GetBySupplierID(ctx context.Context, supplierID uint) ([]*model.PaymentRequest, error) {
	var paymentRequests []*model.PaymentRequest
	err := r.db.WithContext(ctx).
		Preload("Contract").
		Preload("Items").
		Where("supplier_id = ?", supplierID).
		Order("created_at DESC").
		Find(&paymentRequests).Error
	return paymentRequests, err
}

// IsPaymentNoExists 检查付款申请单号是否存在
func (r *paymentRequestRepository) IsPaymentNoExists(ctx context.Context, paymentNo string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.PaymentRequest{}).
		Where("payment_no = ?", paymentNo).
		Count(&count).Error
	return count > 0, err
}

// GetTotalPaidAmountByContract 获取合同的总已付款金额
func (r *paymentRequestRepository) GetTotalPaidAmountByContract(ctx context.Context, contractID uint) (float64, error) {
	var totalAmount float64
	err := r.db.WithContext(ctx).Model(&model.PaymentRequest{}).
		Where("contract_id = ? AND status = ?", contractID, "completed").
		Select("COALESCE(SUM(current_payment_amount), 0)").
		Scan(&totalAmount).Error
	return totalAmount, err
}

// GetItemsByContractItemID 根据合同明细ID获取付款申请明细列表
func (r *paymentRequestRepository) GetItemsByContractItemID(ctx context.Context, contractItemID uint) ([]*model.PaymentRequestItem, error) {
	var items []*model.PaymentRequestItem
	err := r.db.WithContext(ctx).
		Preload("PaymentRequest").
		Where("contract_item_id = ?", contractItemID).
		Find(&items).Error
	return items, err
}

// GetPaymentRequestHistory 获取付款申请历史记录
func (r *paymentRequestRepository) GetPaymentRequestHistory(ctx context.Context, paymentID uint) ([]*model.PurchaseApprovalHistory, error) {
	var histories []*model.PurchaseApprovalHistory
	if err := r.db.WithContext(ctx).
		Where("business_type = ? AND business_id = ?", constants.BusinessTypePaymentRequest, paymentID).
		Order("created_at DESC").
		Find(&histories).Error; err != nil {
		return nil, err
	}
	return histories, nil
}

// CreateStatusHistory 创建付款申请状态历史记录
func (r *paymentRequestRepository) CreateStatusHistory(ctx context.Context, history *model.PurchaseApprovalHistory) error {
	return r.db.WithContext(ctx).Create(history).Error
}
