package repository

import (
	"backend/internal/modules/purchase/constants"
	"backend/internal/modules/purchase/model"
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// PurchaseRequestRepository 采购申请仓库接口
type PurchaseRequestRepository interface {
	// GetByID 根据ID获取采购申请信息
	GetByID(ctx context.Context, id uint) (*model.PurchaseRequest, error)

	// GetByRequestNo 根据申请单号获取采购申请信息
	GetByRequestNo(ctx context.Context, requestNo string) (*model.PurchaseRequest, error)

	// List 获取采购申请列表
	List(ctx context.Context, filter model.PurchaseRequestFilter, pagination model.PaginationOptions) ([]*model.PurchaseRequest, int64, error)

	// Create 创建采购申请
	Create(ctx context.Context, request *model.PurchaseRequest) error

	// Update 更新采购申请
	Update(ctx context.Context, request *model.PurchaseRequest) error

	// UpdateStatus 更新采购申请状态
	UpdateStatus(ctx context.Context, id uint, status string, updatedBy uint) error

	// Delete 删除采购申请
	Delete(ctx context.Context, id uint, deletedBy uint) error

	// CreateItem 创建采购申请明细
	CreateItem(ctx context.Context, item *model.PurchaseRequestItem) error

	// UpdateItem 更新采购申请明细
	UpdateItem(ctx context.Context, item *model.PurchaseRequestItem) error

	// DeleteItem 删除采购申请明细
	DeleteItem(ctx context.Context, id uint) error

	// DeleteItemsByRequestID 删除采购申请的所有明细
	DeleteItemsByRequestID(ctx context.Context, requestID uint) error

	// GetItemsByRequestID 获取采购申请的所有明细
	GetItemsByRequestID(ctx context.Context, requestID uint) ([]model.PurchaseRequestItem, error)

	// GetItemByID 根据ID获取单个采购申请明细
	GetItemByID(ctx context.Context, id uint) (*model.PurchaseRequestItem, error)

	// GetDB 获取数据库实例
	GetDB() *gorm.DB

	// GetHistory 获取采购申请历史记录
	GetHistory(ctx context.Context, requestID uint) ([]*model.PurchaseApprovalHistory, error)

	// CreateStatusHistory 创建状态历史记录
	CreateStatusHistory(ctx context.Context, history *model.PurchaseApprovalHistory) error

	// UpdateStatusWithHistory 更新状态并记录历史
	UpdateStatusWithHistory(ctx context.Context, id uint, newStatus string, action string, operatorID uint, operatorName string, comments string) error
}

// purchaseRequestRepository 采购申请仓库实现
type purchaseRequestRepository struct {
	db *gorm.DB
}

// NewPurchaseRequestRepository 创建采购申请仓库实例
func NewPurchaseRequestRepository(db *gorm.DB) PurchaseRequestRepository {
	return &purchaseRequestRepository{db: db}
}

// GetByID 根据ID获取采购申请信息
func (r *purchaseRequestRepository) GetByID(ctx context.Context, id uint) (*model.PurchaseRequest, error) {
	var request model.PurchaseRequest
	if err := r.db.WithContext(ctx).Preload("Items").First(&request, id).Error; err != nil {
		return nil, err
	}
	return &request, nil
}

// GetByRequestNo 根据申请单号获取采购申请信息
func (r *purchaseRequestRepository) GetByRequestNo(ctx context.Context, requestNo string) (*model.PurchaseRequest, error) {
	var request model.PurchaseRequest
	if err := r.db.WithContext(ctx).Where("request_no = ?", requestNo).Preload("Items").First(&request).Error; err != nil {
		return nil, err
	}
	return &request, nil
}

// List 获取采购申请列表
func (r *purchaseRequestRepository) List(ctx context.Context, filter model.PurchaseRequestFilter, pagination model.PaginationOptions) ([]*model.PurchaseRequest, int64, error) {
	var requests []*model.PurchaseRequest
	var total int64

	query := r.db.WithContext(ctx).Model(&model.PurchaseRequest{})

	// 应用过滤条件
	if filter.RequestNo != "" {
		query = query.Where("request_no LIKE ?", "%"+filter.RequestNo+"%")
	}
	if filter.ProjectID != nil {
		query = query.Where("project_id = ?", *filter.ProjectID)
	}
	if len(filter.RequestType) > 0 {
		// 对于数组类型的RequestType，查找任何含有这些值的记录
		for _, requestType := range filter.RequestType {
			query = query.Where("JSON_CONTAINS(request_type, ?)", fmt.Sprintf(`"%s"`, requestType))
		}
	}
	if filter.UrgencyLevel != "" {
		query = query.Where("urgency_level = ?", filter.UrgencyLevel)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.CreatedBy != nil {
		query = query.Where("created_by = ?", *filter.CreatedBy)
	}
	if filter.StartDate != "" {
		query = query.Where("created_at >= ?", filter.StartDate)
	}
	if filter.EndDate != "" {
		query = query.Where("created_at <= ?", filter.EndDate)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 应用分页
	if pagination.Page > 0 && pagination.PageSize > 0 {
		offset := (pagination.Page - 1) * pagination.PageSize
		query = query.Offset(offset).Limit(pagination.PageSize)
	}

	// 排序
	query = query.Order("created_at DESC")

	// 执行查询
	if err := query.Preload("Items").Find(&requests).Error; err != nil {
		return nil, 0, err
	}

	return requests, total, nil
}

// Create 创建采购申请
func (r *purchaseRequestRepository) Create(ctx context.Context, request *model.PurchaseRequest) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 确保明细项的ID为0，让数据库自动分配
		for i := range request.Items {
			request.Items[i].ID = 0
		}

		// 创建采购申请及其明细（GORM会自动处理关联关系）
		if err := tx.Create(request).Error; err != nil {
			return err
		}

		return nil
	})
}

// Update 更新采购申请
func (r *purchaseRequestRepository) Update(ctx context.Context, request *model.PurchaseRequest) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 更新采购申请基本信息
		if err := tx.Model(request).Omit("Items").Updates(map[string]interface{}{
			"project_id":             request.ProjectID,
			"request_type":           request.RequestType,
			"urgency_level":          request.UrgencyLevel,
			"expected_delivery_date": request.ExpectedDeliveryDate,
			"reason":                 request.Reason,
			"receive_address":        request.ReceiveAddress,
			"receiver":               request.Receiver,
			"receiver_phone":         request.ReceiverPhone,
			"status":                 request.Status,
			"updated_by":             request.UpdatedBy,
		}).Error; err != nil {
			return err
		}

		// 处理明细项
		if len(request.Items) > 0 {
			// 删除原有明细
			if err := tx.Where("request_id = ?", request.ID).Delete(&model.PurchaseRequestItem{}).Error; err != nil {
				return err
			}

			for i := range request.Items {
				item := &request.Items[i]
				item.RequestID = request.ID

				// 创建新明细
				if err := tx.Create(item).Error; err != nil {
					return err
				}
			}
		}

		return nil
	})
}

// UpdateStatus 更新采购申请状态
func (r *purchaseRequestRepository) UpdateStatus(ctx context.Context, id uint, status string, updatedBy uint) error {
	return r.db.WithContext(ctx).Model(&model.PurchaseRequest{}).Where("id = ?", id).Updates(map[string]interface{}{
		"status":     status,
		"updated_by": updatedBy,
	}).Error
}

// Delete 删除采购申请
func (r *purchaseRequestRepository) Delete(ctx context.Context, id uint, deletedBy uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 先更新操作人ID到updated_by字段
		if err := tx.Model(&model.PurchaseRequest{}).
			Where("id = ?", id).
			Update("updated_by", deletedBy).Error; err != nil {
			return err
		}

		// 然后执行软删除
		return tx.Delete(&model.PurchaseRequest{}, id).Error
	})
}

// CreateItem 创建采购申请明细
func (r *purchaseRequestRepository) CreateItem(ctx context.Context, item *model.PurchaseRequestItem) error {
	return r.db.WithContext(ctx).Create(item).Error
}

// UpdateItem 更新采购申请明细
func (r *purchaseRequestRepository) UpdateItem(ctx context.Context, item *model.PurchaseRequestItem) error {
	return r.db.WithContext(ctx).Save(item).Error
}

// DeleteItem 删除采购申请明细
func (r *purchaseRequestRepository) DeleteItem(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.PurchaseRequestItem{}, id).Error
}

// DeleteItemsByRequestID 删除采购申请的所有明细
func (r *purchaseRequestRepository) DeleteItemsByRequestID(ctx context.Context, requestID uint) error {
	return r.db.WithContext(ctx).Where("request_id = ?", requestID).Delete(&model.PurchaseRequestItem{}).Error
}

// GetItemsByRequestID 获取采购申请的所有明细
func (r *purchaseRequestRepository) GetItemsByRequestID(ctx context.Context, requestID uint) ([]model.PurchaseRequestItem, error) {
	var items []model.PurchaseRequestItem
	if err := r.db.WithContext(ctx).Where("request_id = ?", requestID).Find(&items).Error; err != nil {
		return nil, err
	}
	return items, nil
}

// GetItemByID 根据ID获取单个采购申请明细
func (r *purchaseRequestRepository) GetItemByID(ctx context.Context, id uint) (*model.PurchaseRequestItem, error) {
	var item model.PurchaseRequestItem
	if err := r.db.WithContext(ctx).First(&item, id).Error; err != nil {
		return nil, err
	}
	return &item, nil
}

// GetDB 获取数据库实例
func (r *purchaseRequestRepository) GetDB() *gorm.DB {
	return r.db
}

// GetHistory 获取采购申请历史记录
func (r *purchaseRequestRepository) GetHistory(ctx context.Context, requestID uint) ([]*model.PurchaseApprovalHistory, error) {
	var history []*model.PurchaseApprovalHistory
	if err := r.db.WithContext(ctx).
		Where("business_type = ? AND business_id = ?", constants.BusinessTypePurchaseRequest, requestID).
		Order("operation_time DESC").
		Find(&history).Error; err != nil {
		return nil, err
	}
	return history, nil
}

// CreateStatusHistory 创建状态历史记录
func (r *purchaseRequestRepository) CreateStatusHistory(ctx context.Context, history *model.PurchaseApprovalHistory) error {
	return r.db.WithContext(ctx).Create(history).Error
}

// UpdateStatusWithHistory 更新状态并记录历史
func (r *purchaseRequestRepository) UpdateStatusWithHistory(ctx context.Context, id uint, newStatus string, action string, operatorID uint, operatorName string, comments string) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取当前状态
		var request model.PurchaseRequest
		if err := tx.Select("status").First(&request, id).Error; err != nil {
			return err
		}

		previousStatus := request.Status
		now := time.Now()

		// 更新状态
		if err := tx.Model(&model.PurchaseRequest{}).Where("id = ?", id).Updates(map[string]interface{}{
			"status":     newStatus,
			"updated_by": operatorID,
		}).Error; err != nil {
			return err
		}

		// 创建历史记录
		history := &model.PurchaseApprovalHistory{
			BusinessType:   constants.BusinessTypePurchaseRequest,
			BusinessID:     id,
			PreviousStatus: previousStatus,
			NewStatus:      newStatus,
			Action:         action,
			OperatorID:     operatorID,
			OperatorName:   operatorName,
			OperationTime:  now,
			Comments:       comments,
			CreatedAt:      now,
		}

		return tx.Create(history).Error
	})
}
