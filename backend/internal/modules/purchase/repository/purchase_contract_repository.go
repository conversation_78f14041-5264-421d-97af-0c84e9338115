package repository

import (
	"backend/internal/modules/purchase/model"
	"context"
	"time"

	"gorm.io/gorm"
)

// PurchaseContractRepository 采购合同存储库接口
type PurchaseContractRepository interface {
	// Create 创建采购合同
	Create(ctx context.Context, contract *model.PurchaseContract) error

	// GetByID 根据ID获取采购合同
	GetByID(ctx context.Context, id uint) (*model.PurchaseContract, error)

	// GetByIDWithDetails 根据ID获取采购合同（包含公司和项目信息）
	GetByIDWithDetails(ctx context.Context, id uint) (*model.PurchaseContract, error)

	// GetByContractNo 根据合同编号获取采购合同
	GetByContractNo(ctx context.Context, contractNo string) (*model.PurchaseContract, error)

	// List 获取采购合同列表
	List(ctx context.Context, filter model.PurchaseContractFilter, page, pageSize int) ([]*model.PurchaseContract, int64, error)

	// Update 更新采购合同
	Update(ctx context.Context, contract *model.PurchaseContract) error

	// UpdateStatus 更新采购合同状态
	UpdateStatus(ctx context.Context, id uint, Status string, updatedBy uint) error

	// Delete 删除采购合同
	Delete(ctx context.Context, id uint) error

	// CheckContractNoExists 检查合同编号是否已存在
	CheckContractNoExists(ctx context.Context, contractNo string) (bool, error)

	// Exists 检查合同是否存在
	Exists(ctx context.Context, id uint) (bool, error)

	// GetContractHistory 获取合同历史记录
	GetContractHistory(ctx context.Context, contractID uint) ([]*model.PurchaseApprovalHistory, error)

	// CreateStatusHistory 创建合同状态历史记录
	CreateStatusHistory(ctx context.Context, history *model.PurchaseApprovalHistory) error

	// GetInquiryItemsByInquiryID 根据询价ID获取询价明细
	GetInquiryItemsByInquiryID(ctx context.Context, inquiryID uint) ([]*model.PurchaseInquiryItem, error)

	// GetPurchasedStatsForInquiryItem 获取询价明细的已采购统计信息
	GetPurchasedStatsForInquiryItem(ctx context.Context, inquiryItemID uint) (int, float64, error)

	// GetItemByID 根据ID获取合同明细
	GetItemByID(ctx context.Context, id uint) (*model.PurchaseContractItem, error)

	// GetContractItemByID 根据ID获取合同明细（别名方法，为了保持一致性）
	GetContractItemByID(ctx context.Context, id uint) (*model.PurchaseContractItem, error)

	// GetContractItemsByContractID 根据合同ID获取所有合同明细
	GetContractItemsByContractID(ctx context.Context, contractID uint) ([]*model.PurchaseContractItem, error)
}

// purchaseContractRepository 采购合同存储库实现
type purchaseContractRepository struct {
	db *gorm.DB
}

// NewPurchaseContractRepository 创建采购合同存储库实例
func NewPurchaseContractRepository(db *gorm.DB) PurchaseContractRepository {
	return &purchaseContractRepository{db: db}
}

// Create 创建采购合同
func (r *purchaseContractRepository) Create(ctx context.Context, contract *model.PurchaseContract) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(contract).Error; err != nil {
			return err
		}

		return nil
	})
}

// GetByID 根据ID获取采购合同
func (r *purchaseContractRepository) GetByID(ctx context.Context, id uint) (*model.PurchaseContract, error) {
	var contract model.PurchaseContract
	if err := r.db.WithContext(ctx).Preload("Items").First(&contract, id).Error; err != nil {
		return nil, err
	}
	return &contract, nil
}

// GetByIDWithDetails 根据ID获取采购合同（包含公司和项目信息）
func (r *purchaseContractRepository) GetByIDWithDetails(ctx context.Context, id uint) (*model.PurchaseContract, error) {
	var contract model.PurchaseContract
	if err := r.db.WithContext(ctx).
		Preload("Items").
		Preload("OurCompany").
		Preload("Project").
		Preload("Supplier").
		First(&contract, id).Error; err != nil {
		return nil, err
	}
	return &contract, nil
}

// GetByContractNo 根据合同编号获取采购合同
func (r *purchaseContractRepository) GetByContractNo(ctx context.Context, contractNo string) (*model.PurchaseContract, error) {
	var contract model.PurchaseContract
	if err := r.db.WithContext(ctx).Preload("Items").Where("contract_no = ?", contractNo).First(&contract).Error; err != nil {
		return nil, err
	}
	return &contract, nil
}

// List 获取采购合同列表
func (r *purchaseContractRepository) List(ctx context.Context, filter model.PurchaseContractFilter, page, pageSize int) ([]*model.PurchaseContract, int64, error) {
	var contracts []*model.PurchaseContract
	var total int64

	query := r.db.WithContext(ctx).Model(&model.PurchaseContract{})

	// 应用过滤条件
	if filter.ContractNo != "" {
		query = query.Where("contract_no LIKE ?", "%"+filter.ContractNo+"%")
	}
	if filter.ContractType != "" {
		query = query.Where("contract_type = ?", filter.ContractType)
	}
	if filter.InquiryID != nil {
		query = query.Where("inquiry_id = ?", *filter.InquiryID)
	}
	if filter.InquiryNo != "" {
		query = query.Joins("JOIN purchase_inquiries ON purchase_contracts.inquiry_id = purchase_inquiries.id").
			Where("purchase_inquiries.inquiry_no LIKE ?", "%"+filter.InquiryNo+"%")
	}
	if filter.RequestID != nil {
		query = query.Where("request_id = ?", *filter.RequestID)
	}
	if filter.ProjectID != nil {
		query = query.Where("project_id = ?", *filter.ProjectID)
	}
	if filter.SupplierID != nil {
		query = query.Where("supplier_id = ?", *filter.SupplierID)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.CreatedBy != nil {
		query = query.Where("created_by = ?", *filter.CreatedBy)
	}
	if filter.StartDate != nil {
		query = query.Where("created_at >= ?", *filter.StartDate)
	}
	if filter.EndDate != nil {
		// 添加一天以包含结束日期当天
		endDate := filter.EndDate.AddDate(0, 0, 1)
		query = query.Where("created_at < ?", endDate)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Preload("Items").Find(&contracts).Error; err != nil {
		return nil, 0, err
	}

	return contracts, total, nil
}

// Update 更新采购合同
func (r *purchaseContractRepository) Update(ctx context.Context, contract *model.PurchaseContract) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 更新合同主信息
		if err := tx.Model(contract).Updates(map[string]interface{}{
			"inquiry_id":       contract.InquiryID,
			"project_id":       contract.ProjectID,
			"our_company_id":   contract.OurCompanyID,
			"supplier_id":      contract.SupplierID,
			"contract_title":   contract.ContractTitle,
			"contract_type":    contract.ContractType,
			"signing_date":     contract.SigningDate,
			"delivery_address": contract.DeliveryAddress,
			"payment_terms":    contract.PaymentTerms,
			"total_amount":     contract.TotalAmount,
			"status":           contract.Status,
			"updated_by":       contract.UpdatedBy,
			"updated_at":       time.Now(),
		}).Error; err != nil {
			return err
		}

		// 处理明细项
		if len(contract.Items) > 0 {
			// 删除原有明细
			if err := tx.Where("contract_id = ?", contract.ID).Delete(&model.PurchaseContractItem{}).Error; err != nil {
				return err
			}

			// 添加新的明细
			for i := range contract.Items {
				contract.Items[i].ContractID = contract.ID
				if err := tx.Create(&contract.Items[i]).Error; err != nil {
					return err
				}
			}
		}

		return nil
	})
}

// UpdateStatus 更新采购合同状态
func (r *purchaseContractRepository) UpdateStatus(ctx context.Context, id uint, Status string, updatedBy uint) error {
	return r.db.WithContext(ctx).Model(&model.PurchaseContract{}).Where("id = ?", id).Updates(map[string]interface{}{
		"status":     Status,
		"updated_by": updatedBy,
		"updated_at": time.Now(),
	}).Error
}

// Delete 删除采购合同
func (r *purchaseContractRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 删除明细项
		if err := tx.Where("contract_id = ?", id).Delete(&model.PurchaseContractItem{}).Error; err != nil {
			return err
		}

		// 删除合同
		if err := tx.Delete(&model.PurchaseContract{}, id).Error; err != nil {
			return err
		}

		return nil
	})
}

// CheckContractNoExists 检查合同编号是否已存在
func (r *purchaseContractRepository) CheckContractNoExists(ctx context.Context, contractNo string) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&model.PurchaseContract{}).Where("contract_no = ?", contractNo).Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}

// Exists 检查合同是否存在
func (r *purchaseContractRepository) Exists(ctx context.Context, id uint) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&model.PurchaseContract{}).Where("id = ?", id).Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}

// GetContractHistory 获取合同历史记录
func (r *purchaseContractRepository) GetContractHistory(ctx context.Context, contractID uint) ([]*model.PurchaseApprovalHistory, error) {
	var histories []*model.PurchaseApprovalHistory
	if err := r.db.WithContext(ctx).
		Where("business_type = ? AND business_id = ?", "purchase_contract", contractID).
		Order("created_at DESC").
		Find(&histories).Error; err != nil {
		return nil, err
	}
	return histories, nil
}

// CreateStatusHistory 创建合同状态历史记录
func (r *purchaseContractRepository) CreateStatusHistory(ctx context.Context, history *model.PurchaseApprovalHistory) error {
	return r.db.WithContext(ctx).Create(history).Error
}

// GetInquiryItemsByInquiryID 根据询价ID获取询价明细
func (r *purchaseContractRepository) GetInquiryItemsByInquiryID(ctx context.Context, inquiryID uint) ([]*model.PurchaseInquiryItem, error) {
	var items []*model.PurchaseInquiryItem
	if err := r.db.WithContext(ctx).
		Where("inquiry_id = ?", inquiryID).
		Find(&items).Error; err != nil {
		return nil, err
	}
	return items, nil
}

// GetPurchasedStatsForInquiryItem 获取询价明细的已采购统计信息
func (r *purchaseContractRepository) GetPurchasedStatsForInquiryItem(ctx context.Context, inquiryItemID uint) (int, float64, error) {
	var result struct {
		TotalQuantity int     `json:"total_quantity"`
		TotalAmount   float64 `json:"total_amount"`
	}

	// 查询所有关联该询价明细的合同明细项，排除已取消和已拒绝的合同
	err := r.db.WithContext(ctx).
		Table("purchase_contract_items pci").
		Select("COALESCE(SUM(pci.contract_quantity), 0) as total_quantity, COALESCE(SUM(pci.contract_amount), 0) as total_amount").
		Joins("JOIN purchase_contracts pc ON pci.contract_id = pc.id").
		Where("pci.inquiry_item_id = ? AND pc.status NOT IN (?, ?) AND pc.deleted_at IS NULL", inquiryItemID, "cancelled", "rejected").
		Scan(&result).Error

	if err != nil {
		return 0, 0, err
	}

	return result.TotalQuantity, result.TotalAmount, nil
}

// GetItemByID 根据ID获取合同明细
func (r *purchaseContractRepository) GetItemByID(ctx context.Context, id uint) (*model.PurchaseContractItem, error) {
	var item model.PurchaseContractItem
	err := r.db.WithContext(ctx).First(&item, id).Error
	if err != nil {
		return nil, err
	}
	return &item, nil
}

// GetContractItemByID 根据ID获取合同明细（别名方法，为了保持一致性）
func (r *purchaseContractRepository) GetContractItemByID(ctx context.Context, id uint) (*model.PurchaseContractItem, error) {
	return r.GetItemByID(ctx, id)
}

// GetContractItemsByContractID 根据合同ID获取所有合同明细
func (r *purchaseContractRepository) GetContractItemsByContractID(ctx context.Context, contractID uint) ([]*model.PurchaseContractItem, error) {
	var items []*model.PurchaseContractItem
	err := r.db.WithContext(ctx).
		Where("contract_id = ?", contractID).
		Find(&items).Error
	if err != nil {
		return nil, err
	}
	return items, nil
}
