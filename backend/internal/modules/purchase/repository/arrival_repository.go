package repository

import (
	"backend/internal/modules/purchase/dto"
	"backend/internal/modules/purchase/model"
	"context"
	"gorm.io/gorm"
	"strings"
)

// ArrivalRepository 到货记录仓库接口
type ArrivalRepository interface {
	// Create 创建到货记录
	Create(ctx context.Context, arrival *model.Arrival) error

	// GetByID 根据ID获取到货记录（简化版本）
	GetByID(ctx context.Context, id uint) (*model.Arrival, error)

	// GetByIDWithFullDetails 根据ID获取到货记录（完整版本，包含所有关联数据）
	GetByIDWithFullDetails(ctx context.Context, id uint) (*model.Arrival, error)

	// GetByArrivalNo 根据到货通知单号获取到货记录
	GetByArrivalNo(ctx context.Context, arrivalNo string) (*model.Arrival, error)

	// Update 更新到货记录
	Update(ctx context.Context, arrival *model.Arrival) error

	// UpdateStatus 更新到货记录状态
	UpdateStatus(ctx context.Context, id uint, status string, updatedBy uint) error

	// Delete 删除到货记录
	Delete(ctx context.Context, id uint) error

	// List 获取到货记录列表
	List(ctx context.Context, query *dto.ArrivalQueryDTO) ([]*model.Arrival, int64, error)

	// GetByContractID 根据合同ID获取到货记录列表
	GetByContractID(ctx context.Context, contractID uint) ([]*model.Arrival, error)

	// GetBySupplierID 根据供应商ID获取到货记录列表
	GetBySupplierID(ctx context.Context, supplierID uint) ([]*model.Arrival, error)

	// IsArrivalNoExists 检查到货通知单号是否存在
	IsArrivalNoExists(ctx context.Context, arrivalNo string) (bool, error)

	// GetArrivalHistory 获取到货记录历史记录
	GetArrivalHistory(ctx context.Context, arrivalID uint) ([]*model.PurchaseApprovalHistory, error)

	// CreateStatusHistory 创建到货记录状态历史记录
	CreateStatusHistory(ctx context.Context, history *model.PurchaseApprovalHistory) error

	// GetStatistics 获取到货统计信息
	GetStatistics(ctx context.Context) (*dto.ArrivalStatisticsDTO, error)

	// GetRequestTypeByArrivalID 根据到货记录ID获取关联的采购申请类型
	GetRequestTypeByArrivalID(ctx context.Context, arrivalID uint) (string, error)

	// GetContractItemPaymentInfo 获取合同明细的付款信息
	GetContractItemPaymentInfo(ctx context.Context, contractItemID uint) (paidQuantity float64, paidAmount float64, unpaidQuantity float64, unpaidAmount float64, err error)

	// GetArrivalsByContractItemID 根据合同明细ID获取所有相关的到货记录
	GetArrivalsByContractItemID(ctx context.Context, contractItemID uint) ([]*model.Arrival, error)
}

// arrivalRepository 到货记录仓库实现
type arrivalRepository struct {
	db *gorm.DB
}

// NewArrivalRepository 创建到货记录仓库实例
func NewArrivalRepository(db *gorm.DB) ArrivalRepository {
	return &arrivalRepository{db: db}
}

// Create 创建到货记录
func (r *arrivalRepository) Create(ctx context.Context, arrival *model.Arrival) error {
	return r.db.WithContext(ctx).Create(arrival).Error
}

// GetByID 根据ID获取到货记录（简化版本，只包含必要信息）
func (r *arrivalRepository) GetByID(ctx context.Context, id uint) (*model.Arrival, error) {
	var arrival model.Arrival
	err := r.db.WithContext(ctx).
		Preload("Contract").
		Preload("Contract.OurCompany").
		Preload("Supplier").
		Preload("Items").
		Preload("Items.ContractItem"). // 预加载到货明细的合同明细信息
		First(&arrival, id).Error
	return &arrival, err
}

// GetByIDWithFullDetails 根据ID获取到货记录（完整版本，包含所有关联数据）
func (r *arrivalRepository) GetByIDWithFullDetails(ctx context.Context, id uint) (*model.Arrival, error) {
	var arrival model.Arrival
	err := r.db.WithContext(ctx).
		Preload("Contract").
		Preload("Contract.Inquiry").
		Preload("Contract.Inquiry.Request").
		Preload("Supplier").
		Preload("Items").
		Preload("Items.ContractItem").
		First(&arrival, id).Error
	return &arrival, err
}

// GetByArrivalNo 根据到货通知单号获取到货记录（简化版本）
func (r *arrivalRepository) GetByArrivalNo(ctx context.Context, arrivalNo string) (*model.Arrival, error) {
	var arrival model.Arrival
	err := r.db.WithContext(ctx).
		Preload("Items").
		Preload("Items.ContractItem"). // 预加载到货明细的合同明细信息
		Where("arrival_no = ?", arrivalNo).
		First(&arrival).Error
	return &arrival, err
}

// Update 更新到货记录
func (r *arrivalRepository) Update(ctx context.Context, arrival *model.Arrival) error {
	return r.db.WithContext(ctx).Save(arrival).Error
}

// UpdateStatus 更新到货记录状态
func (r *arrivalRepository) UpdateStatus(ctx context.Context, id uint, status string, updatedBy uint) error {
	return r.db.WithContext(ctx).Model(&model.Arrival{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":     status,
			"updated_by": updatedBy,
		}).Error
}

// Delete 删除到货记录
func (r *arrivalRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.Arrival{}, id).Error
}

// List 获取到货记录列表
func (r *arrivalRepository) List(ctx context.Context, query *dto.ArrivalQueryDTO) ([]*model.Arrival, int64, error) {
	var arrivals []*model.Arrival
	var total int64

	db := r.db.WithContext(ctx).Model(&model.Arrival{})

	// 判断是否需要JOIN合同表
	needContractJoin := query.ContractNo != ""

	// 添加必要的JOIN
	if needContractJoin {
		db = db.Joins("LEFT JOIN purchase_contracts ON arrivals.contract_id = purchase_contracts.id")
	}

	// 构建查询条件
	if query.ArrivalNo != "" {
		db = db.Where("arrivals.arrival_no LIKE ?", "%"+query.ArrivalNo+"%")
	}
	if query.ContractID != nil {
		db = db.Where("arrivals.contract_id = ?", *query.ContractID)
	}
	if query.ContractNo != "" {
		db = db.Where("purchase_contracts.contract_no LIKE ?", "%"+query.ContractNo+"%")
	}
	if query.SupplierID != nil {
		db = db.Where("arrivals.supplier_id = ?", *query.SupplierID)
	}
	if query.Status != "" {
		db = db.Where("arrivals.status = ?", query.Status)
	}
	if query.IsComplete != nil {
		db = db.Where("arrivals.is_complete = ?", *query.IsComplete)
	}
	if query.CreatedBy != nil {
		db = db.Where("arrivals.created_by = ?", *query.CreatedBy)
	}
	if query.StartDate != "" {
		db = db.Where("arrivals.created_at >= ?", query.StartDate)
	}
	if query.EndDate != "" {
		db = db.Where("arrivals.created_at <= ?", query.EndDate)
	}
	if query.MinAmount != nil {
		db = db.Where("arrivals.total_amount >= ?", *query.MinAmount)
	}
	if query.MaxAmount != nil {
		db = db.Where("arrivals.total_amount <= ?", *query.MaxAmount)
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询（预加载关联数据）
	offset := (query.Page - 1) * query.PageSize
	err := db.Preload("Contract").
		Preload("Contract.OurCompany").
		Preload("Supplier").
		Preload("Items").
		Offset(offset).
		Limit(query.PageSize).
		Order("arrivals.created_at DESC").
		Find(&arrivals).Error

	return arrivals, total, err
}

// GetByContractID 根据合同ID获取到货记录列表
func (r *arrivalRepository) GetByContractID(ctx context.Context, contractID uint) ([]*model.Arrival, error) {
	var arrivals []*model.Arrival
	err := r.db.WithContext(ctx).
		Preload("Supplier").
		Preload("Items").
		Where("contract_id = ?", contractID).
		Order("created_at DESC").
		Find(&arrivals).Error
	return arrivals, err
}

// GetBySupplierID 根据供应商ID获取到货记录列表
func (r *arrivalRepository) GetBySupplierID(ctx context.Context, supplierID uint) ([]*model.Arrival, error) {
	var arrivals []*model.Arrival
	err := r.db.WithContext(ctx).
		Preload("Contract").
		Preload("Items").
		Where("supplier_id = ?", supplierID).
		Order("created_at DESC").
		Find(&arrivals).Error
	return arrivals, err
}

// IsArrivalNoExists 检查到货通知单号是否存在
func (r *arrivalRepository) IsArrivalNoExists(ctx context.Context, arrivalNo string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.Arrival{}).
		Where("arrival_no = ?", arrivalNo).
		Count(&count).Error
	return count > 0, err
}

// GetArrivalHistory 获取到货记录历史记录
func (r *arrivalRepository) GetArrivalHistory(ctx context.Context, arrivalID uint) ([]*model.PurchaseApprovalHistory, error) {
	var histories []*model.PurchaseApprovalHistory
	err := r.db.WithContext(ctx).
		Where("business_type = ? AND business_id = ?", "arrival", arrivalID).
		Order("created_at DESC").
		Find(&histories).Error
	return histories, err
}

// CreateStatusHistory 创建到货记录状态历史记录
func (r *arrivalRepository) CreateStatusHistory(ctx context.Context, history *model.PurchaseApprovalHistory) error {
	return r.db.WithContext(ctx).Create(history).Error
}

// GetStatistics 获取到货统计信息
func (r *arrivalRepository) GetStatistics(ctx context.Context) (*dto.ArrivalStatisticsDTO, error) {
	var stats dto.ArrivalStatisticsDTO

	// 获取总记录数和总金额
	err := r.db.WithContext(ctx).Model(&model.Arrival{}).
		Select("COUNT(*) as total_count, COALESCE(SUM(total_amount), 0) as total_amount").
		Scan(&stats).Error
	if err != nil {
		return nil, err
	}

	// 获取各状态的统计
	var statusStats []struct {
		Status string
		Count  int64
		Amount float64
	}

	err = r.db.WithContext(ctx).Model(&model.Arrival{}).
		Select("status, COUNT(*) as count, COALESCE(SUM(total_amount), 0) as amount").
		Group("status").
		Scan(&statusStats).Error
	if err != nil {
		return nil, err
	}

	// 填充各状态统计
	for _, stat := range statusStats {
		switch stat.Status {
		case "draft":
			stats.DraftCount = stat.Count
		case "completed":
			stats.CompletedCount = stat.Count
			stats.CompletedAmount = stat.Amount
		case "cancelled":
			stats.CancelledCount = stat.Count
		default:
			stats.PendingCount += stat.Count
		}
	}

	return &stats, nil
}

// GetRequestTypeByArrivalID 根据到货记录ID获取关联的采购申请类型
func (r *arrivalRepository) GetRequestTypeByArrivalID(ctx context.Context, arrivalID uint) (string, error) {
	var requestType string

	// 通过复杂的JOIN查询获取申请类型
	err := r.db.WithContext(ctx).
		Table("arrivals").
		Select("purchase_requests.request_type").
		Joins("LEFT JOIN purchase_contracts ON arrivals.contract_id = purchase_contracts.id").
		Joins("LEFT JOIN purchase_inquiries ON purchase_contracts.inquiry_id = purchase_inquiries.id").
		Joins("LEFT JOIN purchase_requests ON purchase_inquiries.request_id = purchase_requests.id").
		Where("arrivals.id = ?", arrivalID).
		Scan(&requestType).Error

	if err != nil {
		return "other", err
	}

	// 如果没有找到申请类型，返回默认值
	if requestType == "" {
		return "other", nil
	}

	// 这里需要解析JSON格式的申请类型
	// 为了简化，我们假设申请类型是单一的，或者取第一个
	// 实际应该解析JSON数组格式：["equipment", "software"]
	// 这里简化处理，假设大多数情况下只有一个类型
	if requestType != "" {
		// 简单的JSON解析，去掉方括号和引号
		requestType = strings.Trim(requestType, `[]"`)
		if strings.Contains(requestType, ",") {
			// 如果有多个类型，取第一个
			parts := strings.Split(requestType, ",")
			if len(parts) > 0 {
				requestType = strings.Trim(parts[0], `"`)
			}
		}
	}

	return requestType, nil
}

// GetContractItemPaymentInfo 获取合同明细的付款信息
func (r *arrivalRepository) GetContractItemPaymentInfo(ctx context.Context, contractItemID uint) (paidQuantity float64, paidAmount float64, unpaidQuantity float64, unpaidAmount float64, err error) {
	// 首先获取合同明细的基本信息（总数量和单价）
	var contractItem struct {
		ContractQuantity int     `json:"contract_quantity"`
		ContractPrice    float64 `json:"contract_price"`
	}

	err = r.db.WithContext(ctx).
		Table("purchase_contract_items").
		Select("contract_quantity, contract_price").
		Where("id = ?", contractItemID).
		Scan(&contractItem).Error

	if err != nil {
		return 0, 0, 0, 0, err
	}

	// 查询该合同明细在所有已完成付款申请中的累计已付款数量
	var totalPaidQuantity float64
	err = r.db.WithContext(ctx).
		Table("payment_request_items pri").
		Select("COALESCE(SUM(pri.current_payment_quantity), 0)").
		Joins("LEFT JOIN payment_requests pr ON pri.payment_id = pr.id").
		Where("pri.contract_item_id = ? AND pr.status = 'completed' AND pr.deleted_at IS NULL", contractItemID).
		Scan(&totalPaidQuantity).Error

	if err != nil {
		return 0, 0, 0, 0, err
	}

	// 计算各项数值
	paidQuantity = totalPaidQuantity
	unpaidQuantity = float64(contractItem.ContractQuantity) - totalPaidQuantity
	if unpaidQuantity < 0 {
		unpaidQuantity = 0 // 防止负数
	}

	paidAmount = paidQuantity * contractItem.ContractPrice
	unpaidAmount = unpaidQuantity * contractItem.ContractPrice

	return paidQuantity, paidAmount, unpaidQuantity, unpaidAmount, nil
}

// GetArrivalsByContractItemID 根据合同明细ID获取所有相关的到货记录
func (r *arrivalRepository) GetArrivalsByContractItemID(ctx context.Context, contractItemID uint) ([]*model.Arrival, error) {
	var arrivals []*model.Arrival

	err := r.db.WithContext(ctx).
		Joins("JOIN arrival_items ON arrivals.id = arrival_items.arrival_id").
		Where("arrival_items.contract_item_id = ?", contractItemID).
		Preload("Items", "contract_item_id = ?", contractItemID).
		Find(&arrivals).Error

	if err != nil {
		return nil, err
	}

	return arrivals, nil
}
