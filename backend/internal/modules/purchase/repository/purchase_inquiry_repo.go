package repository

import (
	"backend/internal/modules/purchase/constants"
	"backend/internal/modules/purchase/model"
	"context"
	"time"

	"gorm.io/gorm"
)

// PurchaseInquiryRepository 采购询价仓库接口
type PurchaseInquiryRepository interface {
	// GetByID 根据ID获取采购询价信息
	GetByID(ctx context.Context, id uint) (*model.PurchaseInquiry, error)

	// GetByInquiryNo 根据询价单号获取采购询价信息
	GetByInquiryNo(ctx context.Context, inquiryNo string) (*model.PurchaseInquiry, error)

	// List 获取采购询价列表
	List(ctx context.Context, filter model.PurchaseInquiryFilter, pagination model.PaginationOptions) ([]*model.PurchaseInquiry, int64, error)

	// Create 创建采购询价
	Create(ctx context.Context, inquiry *model.PurchaseInquiry) error

	// Update 更新采购询价
	Update(ctx context.Context, inquiry *model.PurchaseInquiry) error

	// UpdateStatus 更新采购询价状态
	UpdateStatus(ctx context.Context, id uint, status string, updatedBy uint) error

	// Delete 删除采购询价
	Delete(ctx context.Context, id uint, deletedBy uint) error

	// CreateItem 创建采购询价明细
	CreateItem(ctx context.Context, item *model.PurchaseInquiryItem) error

	// UpdateItem 更新采购询价明细
	UpdateItem(ctx context.Context, item *model.PurchaseInquiryItem) error

	// DeleteItem 删除采购询价明细
	DeleteItem(ctx context.Context, id uint) error

	// DeleteItemsByInquiryID 删除采购询价的所有明细
	DeleteItemsByInquiryID(ctx context.Context, inquiryID uint) error

	// GetDB 获取数据库实例
	GetDB() *gorm.DB

	// GetHistory 获取采购询价历史记录
	GetHistory(ctx context.Context, inquiryID uint) ([]*model.PurchaseApprovalHistory, error)

	// CreateStatusHistory 创建状态历史记录
	CreateStatusHistory(ctx context.Context, history *model.PurchaseApprovalHistory) error

	// UpdateStatusWithHistory 更新状态并记录历史
	UpdateStatusWithHistory(ctx context.Context, id uint, newStatus string, action string, operatorID uint, operatorName string, comments string) error

	// GetByRequestID 根据采购申请ID获取询价列表
	GetByRequestID(ctx context.Context, requestID uint) ([]*model.PurchaseInquiry, error)

	// GetBySupplierID 根据供应商ID获取询价列表
	GetBySupplierID(ctx context.Context, supplierID uint) ([]*model.PurchaseInquiry, error)

	// GetQuantitySummary 获取询价数量汇总
	GetQuantitySummary(ctx context.Context, requestID uint) ([]model.InquiryQuantitySummary, error)

	// UpdateInquiredQuantity 更新已询价数量
	UpdateInquiredQuantity(ctx context.Context, requestItemID uint, quantity int) error

	// BatchCreate 批量创建采购询价
	BatchCreate(ctx context.Context, inquiries []*model.PurchaseInquiry) error

	// CountByStatus 根据状态统计询价数量
	CountByStatus(ctx context.Context, status string) (int64, error)

	// GetTotalAmount 获取询价总金额
	GetTotalAmount(ctx context.Context, filter model.PurchaseInquiryFilter) (float64, error)

	// GetByRequestIDAndStatus 根据采购申请ID和状态获取询价列表
	GetByRequestIDAndStatus(ctx context.Context, requestID uint, status string) ([]*model.PurchaseInquiry, error)

	// GetApprovedInquiriesForRequestItem 获取指定申请明细的已审批通过的询价单
	GetApprovedInquiriesForRequestItem(ctx context.Context, requestItemID uint) ([]*model.PurchaseInquiry, error)
}

// purchaseInquiryRepository 采购询价仓库实现
type purchaseInquiryRepository struct {
	db *gorm.DB
}

// NewPurchaseInquiryRepository 创建采购询价仓库实例
func NewPurchaseInquiryRepository(db *gorm.DB) PurchaseInquiryRepository {
	return &purchaseInquiryRepository{db: db}
}

// GetByID 根据ID获取采购询价信息
func (r *purchaseInquiryRepository) GetByID(ctx context.Context, id uint) (*model.PurchaseInquiry, error) {
	var inquiry model.PurchaseInquiry
	if err := r.db.WithContext(ctx).
		Preload("Items.RequestItem").
		First(&inquiry, id).Error; err != nil {
		return nil, err
	}
	return &inquiry, nil
}

// GetByInquiryNo 根据询价单号获取采购询价信息
func (r *purchaseInquiryRepository) GetByInquiryNo(ctx context.Context, inquiryNo string) (*model.PurchaseInquiry, error) {
	var inquiry model.PurchaseInquiry
	if err := r.db.WithContext(ctx).
		Where("inquiry_no = ?", inquiryNo).
		Preload("Items.RequestItem").
		First(&inquiry).Error; err != nil {
		return nil, err
	}
	return &inquiry, nil
}

// List 获取采购询价列表
func (r *purchaseInquiryRepository) List(ctx context.Context, filter model.PurchaseInquiryFilter, pagination model.PaginationOptions) ([]*model.PurchaseInquiry, int64, error) {
	var inquiries []*model.PurchaseInquiry
	var total int64

	query := r.db.WithContext(ctx).Model(&model.PurchaseInquiry{})

	// 应用过滤条件
	if filter.InquiryNo != "" {
		query = query.Where("inquiry_no LIKE ?", "%"+filter.InquiryNo+"%")
	}
	if filter.RequestID != nil {
		query = query.Where("request_id = ?", *filter.RequestID)
	}
	if filter.RequestNo != "" {
		query = query.Joins("JOIN purchase_requests ON purchase_inquiries.request_id = purchase_requests.id").
			Where("purchase_requests.request_no LIKE ?", "%"+filter.RequestNo+"%")
	}
	if filter.ProjectID != nil {
		query = query.Where("project_id = ?", *filter.ProjectID)
	}
	if filter.SupplierID != nil {
		query = query.Where("supplier_id = ?", *filter.SupplierID)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.CreatedBy != nil {
		query = query.Where("created_by = ?", *filter.CreatedBy)
	}
	if filter.StartDate != "" {
		query = query.Where("created_at >= ?", filter.StartDate)
	}
	if filter.EndDate != "" {
		query = query.Where("created_at <= ?", filter.EndDate)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 应用分页
	if pagination.Page > 0 && pagination.PageSize > 0 {
		offset := (pagination.Page - 1) * pagination.PageSize
		query = query.Offset(offset).Limit(pagination.PageSize)
	}

	// 排序
	query = query.Order("created_at DESC")

	// 执行查询
	if err := query.Preload("Items.RequestItem").Find(&inquiries).Error; err != nil {
		return nil, 0, err
	}

	return inquiries, total, nil
}

// Create 创建采购询价
func (r *purchaseInquiryRepository) Create(ctx context.Context, inquiry *model.PurchaseInquiry) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 确保明细项的ID为0，让数据库自动分配
		for i := range inquiry.Items {
			inquiry.Items[i].ID = 0
		}

		// 创建采购询价及其明细（GORM会自动处理关联关系）
		if err := tx.Create(inquiry).Error; err != nil {
			return err
		}

		return nil
	})
}

// Update 更新采购询价
func (r *purchaseInquiryRepository) Update(ctx context.Context, inquiry *model.PurchaseInquiry) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 更新采购询价基本信息
		if err := tx.Model(inquiry).Omit("Items").Updates(map[string]interface{}{
			"request_id":           inquiry.RequestID,
			"project_id":           inquiry.ProjectID,
			"supplier_id":          inquiry.SupplierID,
			"supplier_description": inquiry.SupplierDescription,
			"status":               inquiry.Status,
			"updated_by":           inquiry.UpdatedBy,
		}).Error; err != nil {
			return err
		}

		// 处理明细项
		if len(inquiry.Items) > 0 {
			// 删除原有明细
			if err := tx.Where("inquiry_id = ?", inquiry.ID).Delete(&model.PurchaseInquiryItem{}).Error; err != nil {
				return err
			}

			for i := range inquiry.Items {
				item := &inquiry.Items[i]
				item.InquiryID = inquiry.ID

				// 创建新明细
				if err := tx.Create(item).Error; err != nil {
					return err
				}
			}
		}

		return nil
	})
}

// UpdateStatus 更新采购询价状态
func (r *purchaseInquiryRepository) UpdateStatus(ctx context.Context, id uint, status string, updatedBy uint) error {
	return r.db.WithContext(ctx).Model(&model.PurchaseInquiry{}).Where("id = ?", id).Updates(map[string]interface{}{
		"status":     status,
		"updated_by": updatedBy,
	}).Error
}

// Delete 删除采购询价
func (r *purchaseInquiryRepository) Delete(ctx context.Context, id uint, deletedBy uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 先更新操作人ID到updated_by字段
		if err := tx.Model(&model.PurchaseInquiry{}).
			Where("id = ?", id).
			Update("updated_by", deletedBy).Error; err != nil {
			return err
		}

		// 删除明细项
		if err := tx.Where("inquiry_id = ?", id).Delete(&model.PurchaseInquiryItem{}).Error; err != nil {
			return err
		}

		// 删除询价记录
		return tx.Delete(&model.PurchaseInquiry{}, id).Error
	})
}

// CreateItem 创建采购询价明细
func (r *purchaseInquiryRepository) CreateItem(ctx context.Context, item *model.PurchaseInquiryItem) error {
	return r.db.WithContext(ctx).Create(item).Error
}

// UpdateItem 更新采购询价明细
func (r *purchaseInquiryRepository) UpdateItem(ctx context.Context, item *model.PurchaseInquiryItem) error {
	return r.db.WithContext(ctx).Save(item).Error
}

// DeleteItem 删除采购询价明细
func (r *purchaseInquiryRepository) DeleteItem(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.PurchaseInquiryItem{}, id).Error
}

// DeleteItemsByInquiryID 删除采购询价的所有明细
func (r *purchaseInquiryRepository) DeleteItemsByInquiryID(ctx context.Context, inquiryID uint) error {
	return r.db.WithContext(ctx).Where("inquiry_id = ?", inquiryID).Delete(&model.PurchaseInquiryItem{}).Error
}

// GetDB 获取数据库实例
func (r *purchaseInquiryRepository) GetDB() *gorm.DB {
	return r.db
}

// GetHistory 获取采购询价历史记录
func (r *purchaseInquiryRepository) GetHistory(ctx context.Context, inquiryID uint) ([]*model.PurchaseApprovalHistory, error) {
	var history []*model.PurchaseApprovalHistory
	if err := r.db.WithContext(ctx).
		Where("business_type = ? AND business_id = ?", constants.BusinessTypePurchaseInquiry, inquiryID).
		Order("operation_time DESC").
		Find(&history).Error; err != nil {
		return nil, err
	}
	return history, nil
}

// CreateStatusHistory 创建状态历史记录
func (r *purchaseInquiryRepository) CreateStatusHistory(ctx context.Context, history *model.PurchaseApprovalHistory) error {
	return r.db.WithContext(ctx).Create(history).Error
}

// UpdateStatusWithHistory 更新状态并记录历史
func (r *purchaseInquiryRepository) UpdateStatusWithHistory(ctx context.Context, id uint, newStatus string, action string, operatorID uint, operatorName string, comments string) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取当前状态
		var inquiry model.PurchaseInquiry
		if err := tx.Select("status").First(&inquiry, id).Error; err != nil {
			return err
		}

		previousStatus := inquiry.Status
		now := time.Now()

		// 更新状态
		if err := tx.Model(&model.PurchaseInquiry{}).Where("id = ?", id).Updates(map[string]interface{}{
			"status":     newStatus,
			"updated_by": operatorID,
		}).Error; err != nil {
			return err
		}

		// 创建历史记录
		history := &model.PurchaseApprovalHistory{
			BusinessType:   constants.BusinessTypePurchaseInquiry,
			BusinessID:     id,
			PreviousStatus: previousStatus,
			NewStatus:      newStatus,
			Action:         action,
			OperatorID:     operatorID,
			OperatorName:   operatorName,
			OperationTime:  now,
			Comments:       comments,
			CreatedAt:      now,
		}

		return tx.Create(history).Error
	})
}

// GetByRequestID 根据采购申请ID获取询价列表
func (r *purchaseInquiryRepository) GetByRequestID(ctx context.Context, requestID uint) ([]*model.PurchaseInquiry, error) {
	var inquiries []*model.PurchaseInquiry
	if err := r.db.WithContext(ctx).
		Where("request_id = ?", requestID).
		Preload("Items.RequestItem").
		Find(&inquiries).Error; err != nil {
		return nil, err
	}
	return inquiries, nil
}

// GetBySupplierID 根据供应商ID获取询价列表
func (r *purchaseInquiryRepository) GetBySupplierID(ctx context.Context, supplierID uint) ([]*model.PurchaseInquiry, error) {
	var inquiries []*model.PurchaseInquiry
	if err := r.db.WithContext(ctx).
		Where("supplier_id = ?", supplierID).
		Preload("Items.RequestItem").
		Find(&inquiries).Error; err != nil {
		return nil, err
	}
	return inquiries, nil
}

// GetQuantitySummary 获取询价数量汇总
func (r *purchaseInquiryRepository) GetQuantitySummary(ctx context.Context, requestID uint) ([]model.InquiryQuantitySummary, error) {
	var summaries []model.InquiryQuantitySummary

	// 通过原生SQL查询获取数量汇总，排除已取消和已拒绝的询价单
	sql := `
		SELECT
			pri.id as request_item_id,
			pri.quantity as total_quantity,
			IFNULL(SUM(CASE WHEN pi.status NOT IN ('cancelled', 'rejected') THEN pii.current_inquiry_quantity ELSE 0 END), 0) as inquired_quantity,
			pri.quantity - IFNULL(SUM(CASE WHEN pi.status NOT IN ('cancelled', 'rejected') THEN pii.current_inquiry_quantity ELSE 0 END), 0) as uninquired_quantity
		FROM purchase_request_items pri
		LEFT JOIN purchase_inquiry_items pii ON pri.id = pii.request_item_id
		LEFT JOIN purchase_inquiries pi ON pii.inquiry_id = pi.id
		WHERE pri.request_id = ?
		GROUP BY pri.id, pri.quantity
	`

	if err := r.db.WithContext(ctx).Raw(sql, requestID).Scan(&summaries).Error; err != nil {
		return nil, err
	}

	return summaries, nil
}

// UpdateInquiredQuantity 更新已询价数量
func (r *purchaseInquiryRepository) UpdateInquiredQuantity(ctx context.Context, requestItemID uint, quantity int) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 更新询价明细中的已询价数量
		if err := tx.Model(&model.PurchaseInquiryItem{}).
			Where("request_item_id = ?", requestItemID).
			UpdateColumn("inquired_quantity", gorm.Expr("inquired_quantity + ?", quantity)).Error; err != nil {
			return err
		}

		// 重新计算未询价数量
		if err := tx.Model(&model.PurchaseInquiryItem{}).
			Where("request_item_id = ?", requestItemID).
			UpdateColumn("uninquired_quantity", gorm.Expr("total_quantity - inquired_quantity")).Error; err != nil {
			return err
		}

		return nil
	})
}

// BatchCreate 批量创建采购询价
func (r *purchaseInquiryRepository) BatchCreate(ctx context.Context, inquiries []*model.PurchaseInquiry) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, inquiry := range inquiries {
			// 确保明细项的ID为0，让数据库自动分配
			for i := range inquiry.Items {
				inquiry.Items[i].ID = 0
			}

			// 创建采购询价及其明细
			if err := tx.Create(inquiry).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// CountByStatus 根据状态统计询价数量
func (r *purchaseInquiryRepository) CountByStatus(ctx context.Context, status string) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&model.PurchaseInquiry{}).Where("status = ?", status).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// GetTotalAmount 获取询价总金额
func (r *purchaseInquiryRepository) GetTotalAmount(ctx context.Context, filter model.PurchaseInquiryFilter) (float64, error) {
	var total float64

	query := r.db.WithContext(ctx).Model(&model.PurchaseInquiry{})

	// 应用过滤条件
	if filter.InquiryNo != "" {
		query = query.Where("inquiry_no LIKE ?", "%"+filter.InquiryNo+"%")
	}
	if filter.RequestID != nil {
		query = query.Where("request_id = ?", *filter.RequestID)
	}
	if filter.ProjectID != nil {
		query = query.Where("project_id = ?", *filter.ProjectID)
	}
	if filter.SupplierID != nil {
		query = query.Where("supplier_id = ?", *filter.SupplierID)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.CreatedBy != nil {
		query = query.Where("created_by = ?", *filter.CreatedBy)
	}
	if filter.StartDate != "" {
		query = query.Where("created_at >= ?", filter.StartDate)
	}
	if filter.EndDate != "" {
		query = query.Where("created_at <= ?", filter.EndDate)
	}

	if err := query.Select("IFNULL(SUM(total_amount), 0)").Scan(&total).Error; err != nil {
		return 0, err
	}

	return total, nil
}

// GetByRequestIDAndStatus 根据采购申请ID和状态获取询价列表
func (r *purchaseInquiryRepository) GetByRequestIDAndStatus(ctx context.Context, requestID uint, status string) ([]*model.PurchaseInquiry, error) {
	var inquiries []*model.PurchaseInquiry
	if err := r.db.WithContext(ctx).
		Preload("Items.RequestItem").
		Where("request_id = ? AND status = ?", requestID, status).
		Find(&inquiries).Error; err != nil {
		return nil, err
	}
	return inquiries, nil
}

// GetApprovedInquiriesForRequestItem 获取指定申请明细的已审批通过的询价单
func (r *purchaseInquiryRepository) GetApprovedInquiriesForRequestItem(ctx context.Context, requestItemID uint) ([]*model.PurchaseInquiry, error) {
	var inquiries []*model.PurchaseInquiry
	if err := r.db.WithContext(ctx).
		Preload("Items.RequestItem").
		Joins("JOIN purchase_inquiry_items ON purchase_inquiry_items.inquiry_id = purchase_inquiries.id").
		Where("purchase_inquiry_items.request_item_id = ? AND purchase_inquiries.status = ?", requestItemID, constants.InquiryStatusCompleted).
		Find(&inquiries).Error; err != nil {
		return nil, err
	}
	return inquiries, nil
}
