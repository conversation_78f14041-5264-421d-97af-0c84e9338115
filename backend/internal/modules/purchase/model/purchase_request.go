package model

import (
	"time"

	"gorm.io/gorm"
)

// PurchaseRequest 采购申请表模型
type PurchaseRequest struct {
	ID                   uint                  `gorm:"column:id;primaryKey;autoIncrement;comment:主键ID" json:"id"`
	RequestNo            string                `gorm:"column:request_no;type:varchar(50);uniqueIndex:uk_request_no;not null;comment:申请单号" json:"request_no"`
	ProjectID            *uint                 `gorm:"column:project_id;index:idx_project_id;comment:项目ID" json:"project_id"`
	ProjectName          string                `gorm:"-" json:"project_name"`                               // 项目名称（不保存到数据库）
	CreatorName          string                `gorm:"-" json:"creator_name"`                               // 创建人姓名（不保存到数据库）
	RequestType          string                `gorm:"column:request_type;type:text;comment:申请类型" json:"-"` // JSON字符串格式，隐藏在JSON输出中
	RequestTypeArray     []string              `gorm:"-" json:"request_type"`                               // 用于JSON输出的请求类型数组
	UrgencyLevel         string                `gorm:"column:urgency_level;type:varchar(20);comment:紧急程度" json:"urgency_level"`
	ExpectedDeliveryDate *time.Time            `gorm:"column:expected_delivery_date;type:date;comment:期望交货日期" json:"expected_delivery_date"`
	Reason               string                `gorm:"column:reason;type:text;comment:申请原因" json:"reason"`
	ReceiveAddress       string                `gorm:"column:receive_address;type:varchar(255);comment:接收地址" json:"receive_address"`
	Receiver             string                `gorm:"column:receiver;type:varchar(50);comment:接收人" json:"receiver"`
	ReceiverPhone        string                `gorm:"column:receiver_phone;type:varchar(20);comment:接收电话" json:"receiver_phone"`
	Status               string                `gorm:"column:status;type:varchar(50);default:draft;index:idx_status;comment:状态/阶段" json:"status"`
	CreatedBy            uint                  `gorm:"column:created_by;comment:创建人ID" json:"created_by"`
	CreatedAt            time.Time             `gorm:"column:created_at;not null" json:"created_at"`
	UpdatedBy            uint                  `gorm:"column:updated_by;comment:更新人ID" json:"updated_by"`
	UpdatedAt            time.Time             `json:"updated_at"`
	DeletedAt            gorm.DeletedAt        `gorm:"column:deleted_at;index;comment:删除时间" json:"deleted_at"`
	Items                []PurchaseRequestItem `gorm:"foreignKey:RequestID" json:"items"`
}

// TableName 定义表名
func (PurchaseRequest) TableName() string {
	return "purchase_requests"
}

// PurchaseRequestItem 采购申请明细表模型
type PurchaseRequestItem struct {
	ID             uint      `gorm:"column:id;primaryKey;autoIncrement;comment:主键ID" json:"id"`
	RequestID      uint      `gorm:"column:request_id;index:idx_request_id;not null;comment:采购申请ID" json:"request_id"`
	ProductID      *uint     `gorm:"column:product_id;index:idx_product_id;comment:产品ID" json:"product_id"`
	MaterialType   string    `gorm:"column:material_type;type:varchar(50);comment:物料类型" json:"material_type"`
	Model          string    `gorm:"column:model;type:varchar(100);comment:型号" json:"model"`
	Brand          string    `gorm:"column:brand;type:varchar(100);comment:品牌" json:"brand"`
	PN             string    `gorm:"column:pn;type:varchar(100);comment:PN号" json:"pn"`
	Specifications string    `gorm:"column:spec;type:text;comment:规格说明" json:"spec"`
	Unit           string    `gorm:"column:unit;type:varchar(20);comment:单位" json:"unit"`
	Quantity       int       `gorm:"column:quantity;type:int(11);not null;comment:申请数量" json:"quantity"`
	Remark         string    `gorm:"column:remark;type:text;comment:备注" json:"remark"`
	CreatedAt      time.Time `gorm:"column:created_at;not null" json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// TableName 定义表名
func (PurchaseRequestItem) TableName() string {
	return "purchase_request_items"
}

// PurchaseRequestFilter 采购申请查询过滤条件
type PurchaseRequestFilter struct {
	RequestNo    string   `form:"request_no"`
	ProjectID    *uint    `form:"project_id"`
	RequestType  []string `form:"request_type"`
	UrgencyLevel string   `form:"urgency_level"`
	Status       string   `form:"status"`
	CreatedBy    *uint    `form:"created_by"`
	StartDate    string   `form:"start_date"`
	EndDate      string   `form:"end_date"`
}
