package model

import (
	"time"
)

// PurchaseApprovalHistory 采购审批历史记录（通用表，支持多种采购审批流程）
type PurchaseApprovalHistory struct {
	ID             uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	BusinessType   string    `json:"business_type" gorm:"size:50;not null;index;comment:业务类型(purchase_request,purchase_contract,etc)"`
	BusinessID     uint      `json:"business_id" gorm:"not null;index;comment:业务ID"`
	PreviousStatus string    `json:"previous_status" gorm:"size:50;comment:先前状态"`
	NewStatus      string    `json:"new_status" gorm:"size:50;not null;comment:新状态"`
	Action         string    `json:"action" gorm:"size:20;not null;comment:触发动作"`
	OperatorID     uint      `json:"operator_id" gorm:"not null;comment:操作人ID"`
	OperatorName   string    `json:"operator_name" gorm:"size:50;comment:操作人姓名"`
	OperationTime  time.Time `json:"operation_time" gorm:"comment:操作时间"`
	Comments       string    `json:"comments" gorm:"type:text;comment:操作说明"`
	CreatedAt      time.Time `json:"created_at" gorm:"column:created_at;not null"`
}

// TableName 指定表名
func (PurchaseApprovalHistory) TableName() string {
	return "purchase_approval_histories"
}
