package model

import (
	"time"

	"backend/internal/modules/purchase/constants"
	"gorm.io/gorm"
)

// Shipment 发货管理表
type Shipment struct {
	ID              uint           `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	ShipmentNo      string         `gorm:"type:varchar(50);not null;uniqueIndex:uk_shipment_no;comment:发货通知单号" json:"shipment_no"`
	ContractID      uint           `gorm:"not null;index:idx_contract_id;comment:关联合同ID" json:"contract_id"`
	SupplierID      uint           `gorm:"not null;index:idx_supplier_id;comment:供应商ID" json:"supplier_id"`
	TrackingInfo        string     `gorm:"type:varchar(500);comment:物流信息" json:"tracking_info"`
	DeliveryAddress     string     `gorm:"type:varchar(500);comment:收货地址" json:"delivery_address"`
	ExpectedArrivalDate *time.Time `gorm:"type:date;comment:预计到货时间" json:"expected_arrival_date"`
	ShipmentNotice      string     `gorm:"type:text;comment:发货通知" json:"shipment_notice"`
	TotalQuantity   int            `gorm:"default:0;comment:发货总数量" json:"total_quantity"`
	TotalAmount     float64        `gorm:"type:decimal(15,2);default:0.00;comment:发货总金额" json:"total_amount"`
	IsComplete      bool           `gorm:"default:false;comment:是否全部发货" json:"is_complete"`
	Remark          string         `gorm:"type:text;comment:备注" json:"remark"`
	Status          string         `gorm:"type:varchar(20);default:'draft';index:idx_status;comment:状态(工作流状态)" json:"status"`
	CreatedBy       *uint          `gorm:"index;comment:创建人ID" json:"created_by"`
	CreatedAt       time.Time      `gorm:"type:datetime(3);not null;comment:创建时间" json:"created_at"`
	UpdatedBy       *uint          `gorm:"index;comment:更新人ID" json:"updated_by"`
	UpdatedAt       *time.Time     `gorm:"type:datetime(3);comment:更新时间" json:"updated_at"`
	DeletedAt       gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`

	// 关联关系
	Contract *PurchaseContract `gorm:"foreignKey:ContractID" json:"contract,omitempty"` // 关联合同
	Supplier *Supplier         `gorm:"foreignKey:SupplierID" json:"supplier,omitempty"` // 关联供应商
	Items    []ShipmentItem    `gorm:"foreignKey:ShipmentID" json:"items,omitempty"`    // 发货明细
}

// ShipmentItem 发货明细表
type ShipmentItem struct {
	ID                      uint       `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	ShipmentID              uint       `gorm:"not null;index:idx_shipment_id;comment:发货ID" json:"shipment_id"`
	ContractItemID          uint       `gorm:"not null;index:idx_contract_item_id;comment:合同明细ID" json:"contract_item_id"`
	ShippedQuantity         int        `gorm:"default:0;comment:已发货数量" json:"shipped_quantity"`
	ShippedAmount           float64    `gorm:"type:decimal(15,2);default:0.00;comment:已发货金额" json:"shipped_amount"`
	UnshippedQuantity       int        `gorm:"default:0;comment:未发货数量" json:"unshipped_quantity"`
	UnshippedAmount         float64    `gorm:"type:decimal(15,2);default:0.00;comment:未发货金额" json:"unshipped_amount"`
	CurrentShipmentQuantity int        `gorm:"not null;comment:本次发货数量" json:"current_shipment_quantity"`
	CurrentShipmentAmount   float64    `gorm:"type:decimal(15,2);not null;comment:本次发货金额" json:"current_shipment_amount"`
	SerialNumbers           string     `gorm:"type:text;comment:SN号列表(JSON格式)" json:"serial_numbers"`
	Remark                  string     `gorm:"type:text;comment:备注" json:"remark"`
	CreatedAt               time.Time  `gorm:"type:datetime(3);not null;comment:创建时间" json:"created_at"`
	UpdatedAt               *time.Time `gorm:"type:datetime(3);comment:更新时间" json:"updated_at"`

	// 关联关系
	Shipment     *Shipment             `gorm:"foreignKey:ShipmentID" json:"shipment,omitempty"`          // 关联发货记录
	ContractItem *PurchaseContractItem `gorm:"foreignKey:ContractItemID" json:"contract_item,omitempty"` // 关联合同明细
}

// TableName 设置表名
func (Shipment) TableName() string {
	return "shipments"
}

// TableName 设置表名
func (ShipmentItem) TableName() string {
	return "shipment_items"
}

// BeforeCreate 创建前的钩子
func (s *Shipment) BeforeCreate(tx *gorm.DB) error {
	if s.Status == "" {
		s.Status = constants.ShipmentStagePurchaseReview
	}
	return nil
}

// CalculateTotals 计算总数量和总金额
func (s *Shipment) CalculateTotals() {
	s.TotalQuantity = 0
	s.TotalAmount = 0.0

	for _, item := range s.Items {
		s.TotalQuantity += item.CurrentShipmentQuantity
		s.TotalAmount += item.CurrentShipmentAmount
	}
}

// IsEditable 判断是否可编辑
func (s *Shipment) IsEditable() bool {
	return false // 发货记录创建后直接进入审批流程，不可编辑
}

// IsCancellable 判断是否可取消
func (s *Shipment) IsCancellable() bool {
	return s.Status != constants.ShipmentStageCompleted && s.Status != constants.ShipmentStageCancelled
}

// IsApprovable 判断是否可审批
func (s *Shipment) IsApprovable() bool {
	return s.Status != constants.ShipmentStageDraft &&
		s.Status != constants.ShipmentStageCompleted &&
		s.Status != constants.ShipmentStageCancelled
}

// ShipmentFilter 发货管理查询过滤条件
type ShipmentFilter struct {
	ShipmentNo string     `form:"shipment_no" json:"shipment_no"` // 发货通知单号
	ContractID *uint      `form:"contract_id" json:"contract_id"` // 合同ID
	ContractNo string     `form:"contract_no" json:"contract_no"` // 合同编号
	SupplierID *uint      `form:"supplier_id" json:"supplier_id"` // 供应商ID
	Status     string     `form:"status" json:"status"`           // 状态
	IsComplete *bool      `form:"is_complete" json:"is_complete"` // 是否全部发货
	CreatedBy  *uint      `form:"created_by" json:"created_by"`   // 创建人
	StartDate  *time.Time `form:"start_date" json:"start_date"`   // 开始日期
	EndDate    *time.Time `form:"end_date" json:"end_date"`       // 结束日期
	MinAmount  *float64   `form:"min_amount" json:"min_amount"`   // 最小金额
	MaxAmount  *float64   `form:"max_amount" json:"max_amount"`   // 最大金额
}
