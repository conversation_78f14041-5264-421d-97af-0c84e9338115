package model

import (
	"time"

	userModel "backend/internal/modules/user/model"

	"gorm.io/gorm"
)

// Supplier 供应商档案表模型
type Supplier struct {
	ID                uint                  `gorm:"column:id;primaryKey;autoIncrement;comment:主键ID" json:"id"`
	SupplierCode      string                `gorm:"column:supplier_code;type:varchar(50);uniqueIndex:uk_supplier_code;not null;comment:供应商编码" json:"supplier_code"`
	SupplierName      string                `gorm:"column:supplier_name;type:varchar(200);index:idx_supplier_name;not null;comment:供应商名称" json:"supplier_name"`
	ShortName         string                `gorm:"column:short_name;type:varchar(100);comment:简称" json:"short_name"`
	ContactPerson     string                `gorm:"column:contact_person;type:varchar(100);comment:联系人" json:"contact_person"`
	ContactPhone      string                `gorm:"column:contact_phone;type:varchar(50);comment:联系电话" json:"contact_phone"`
	ContactEmail      string                `gorm:"column:contact_email;type:varchar(100);comment:联系邮箱" json:"contact_email"`
	Address           string                `gorm:"column:address;type:varchar(500);comment:地址" json:"address"`
	TaxNumber         string                `gorm:"column:tax_number;type:varchar(50);comment:税号" json:"tax_number"`
	BankName          string                `gorm:"column:bank_name;type:varchar(200);comment:开户银行" json:"bank_name"`
	BankAccount       string                `gorm:"column:bank_account;type:varchar(50);comment:银行账号" json:"bank_account"`
	BusinessLicense   string                `gorm:"column:business_license;type:varchar(50);comment:营业执照号" json:"business_license"`
	Categories        userModel.StringArray `gorm:"column:categories;type:json;comment:所属品类(JSON格式)" json:"categories"`
	ContractStartDate *time.Time            `gorm:"column:contract_start_date;type:date;comment:合同生效日期" json:"contract_start_date"`
	ContractEndDate   *time.Time            `gorm:"column:contract_end_date;type:date;comment:合同到期日期" json:"contract_end_date"`
	CreditLevel       int                   `gorm:"column:credit_level;type:tinyint(1);default:0;comment:信用等级(0-5)" json:"credit_level"`
	Status            int8                  `gorm:"column:status;type:tinyint(1);default:1;comment:状态(1:合作中,0:已停用)" json:"status"`
	Remark            string                `gorm:"column:remark;type:text;comment:备注" json:"remark"`
	CreatedBy         uint                  `gorm:"column:created_by;type:bigint(20);comment:创建人ID" json:"created_by"`
	CreatedAt         time.Time             `gorm:"column:created_at;not null" json:"created_at"`
	UpdatedBy         uint                  `gorm:"column:updated_by;type:bigint(20);comment:更新人ID" json:"updated_by"`
	UpdatedAt         time.Time             `json:"updated_at"`
	DeletedAt         gorm.DeletedAt        `gorm:"column:deleted_at;index;comment:删除时间" json:"deleted_at"`
}

// TableName 定义表名
func (Supplier) TableName() string {
	return "suppliers"
}

// SetCategories 设置品类信息的辅助方法
func (s *Supplier) SetCategories(categories []string) error {
	if categories == nil {
		categories = []string{}
	}
	s.Categories = categories
	return nil
}

// GetCategories 获取品类信息的辅助方法
func (s *Supplier) GetCategories() ([]string, error) {
	if len(s.Categories) == 0 {
		return []string{}, nil
	}
	return s.Categories, nil
}

// BeforeCreate 创建前的钩子
func (s *Supplier) BeforeCreate(tx *gorm.DB) error {
	// 如果Categories为空，设置默认值为空数组
	if len(s.Categories) == 0 {
		s.Categories = userModel.StringArray{}
	}
	return nil
}

// NewSupplier 创建新的供应商实例
func NewSupplier() *Supplier {
	supplier := &Supplier{}
	// 在创建时就设置好默认的空数组
	supplier.Categories = userModel.StringArray{}
	return supplier
}

// SupplierFilter 供应商查询过滤条件
type SupplierFilter struct {
	SupplierCode      string `form:"supplier_code"`
	SupplierName      string `form:"supplier_name"`
	ShortName         string `form:"short_name"`
	ContactPerson     string `form:"contact_person"`
	ContactPhone      string `form:"contact_phone"`
	Status            *int8  `form:"status"`
	ContractDateBegin string `form:"contract_date_begin"`
	ContractDateEnd   string `form:"contract_date_end"`
}
