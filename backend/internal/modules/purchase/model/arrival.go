package model

import (
	"time"

	"gorm.io/gorm"
)

// Arrival 到货管理表
type Arrival struct {
	ID             uint           `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	ArrivalNo      string         `gorm:"type:varchar(50);not null;uniqueIndex:uk_arrival_no;comment:到货通知单号" json:"arrival_no"`
	ContractID     uint           `gorm:"not null;index:idx_contract_id;comment:关联合同ID" json:"contract_id"`
	SupplierID     uint           `gorm:"not null;index:idx_supplier_id;comment:供应商ID" json:"supplier_id"`
	MayArriveAt    *time.Time     `gorm:"type:date;comment:预计到货日期" json:"may_arrive_at"`
	TrackingInfo   string         `gorm:"type:varchar(500);comment:物流信息" json:"tracking_info"`
	ArrivalAddress string         `gorm:"type:varchar(500);comment:到货地址" json:"arrival_address"`
	ArrivalNotice  string         `gorm:"type:text;comment:到货通知" json:"arrival_notice"`
	TotalQuantity  int            `gorm:"default:0;comment:到货总数量" json:"total_quantity"`
	TotalAmount    float64        `gorm:"type:decimal(15,2);default:0.00;comment:到货总金额" json:"total_amount"`
	IsComplete     bool           `gorm:"default:false;comment:是否全部到货" json:"is_complete"`
	Remark         string         `gorm:"type:text;comment:备注" json:"remark"`
	Status         string         `gorm:"type:varchar(20);default:'draft';index:idx_status;comment:状态(工作流状态)" json:"status"`
	CreatedBy      *uint          `gorm:"index;comment:创建人ID" json:"created_by"`
	CreatedAt      time.Time      `gorm:"type:datetime(3);not null;comment:创建时间" json:"created_at"`
	UpdatedBy      *uint          `gorm:"index;comment:更新人ID" json:"updated_by"`
	UpdatedAt      *time.Time     `gorm:"type:datetime(3);comment:更新时间" json:"updated_at"`
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`

	// 关联关系
	Contract *PurchaseContract `gorm:"foreignKey:ContractID" json:"contract,omitempty"` // 关联合同
	Supplier *Supplier         `gorm:"foreignKey:SupplierID" json:"supplier,omitempty"` // 关联供应商
	Items    []ArrivalItem     `gorm:"foreignKey:ArrivalID" json:"items,omitempty"`     // 到货明细
}

// ArrivalItem 到货明细表
type ArrivalItem struct {
	ID                     uint       `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	ArrivalID              uint       `gorm:"not null;index:idx_arrival_id;comment:到货ID" json:"arrival_id"`
	ContractItemID         uint       `gorm:"not null;index:idx_contract_item_id;comment:合同明细ID" json:"contract_item_id"`
	ReceivedQuantity       int        `gorm:"default:0;comment:已提货数量" json:"received_quantity"`
	ReceivedAmount         float64    `gorm:"type:decimal(15,2);default:0.00;comment:已提货金额" json:"received_amount"`
	UnreceivedQuantity     int        `gorm:"default:0;comment:未提货数量" json:"unreceived_quantity"`
	UnreceivedAmount       float64    `gorm:"type:decimal(15,2);default:0.00;comment:未提货金额" json:"unreceived_amount"`
	CurrentArrivalQuantity int        `gorm:"not null;comment:本次到货数量" json:"current_arrival_quantity"`
	CurrentArrivalAmount   float64    `gorm:"type:decimal(15,2);not null;comment:本次到货金额" json:"current_arrival_amount"`
	ExpectedArrivalDate    *time.Time `gorm:"type:date;comment:预计到货时间" json:"expected_arrival_date"`
	SerialNumbers          string     `gorm:"type:text;comment:SN号列表(JSON格式)" json:"serial_numbers"`
	Remark                 string     `gorm:"type:text;comment:备注" json:"remark"`
	CreatedAt              time.Time  `gorm:"type:datetime(3);not null;comment:创建时间" json:"created_at"`
	UpdatedAt              *time.Time `gorm:"type:datetime(3);comment:更新时间" json:"updated_at"`

	// 关联关系
	Arrival      *Arrival              `gorm:"foreignKey:ArrivalID" json:"arrival,omitempty"`            // 关联到货记录
	ContractItem *PurchaseContractItem `gorm:"foreignKey:ContractItemID" json:"contract_item,omitempty"` // 关联合同明细
}

// TableName 设置表名
func (Arrival) TableName() string {
	return "arrivals"
}

// TableName 设置表名
func (ArrivalItem) TableName() string {
	return "arrival_items"
}

// BeforeCreate 创建前的钩子
func (a *Arrival) BeforeCreate(tx *gorm.DB) error {
	if a.Status == "" {
		a.Status = "draft"
	}
	return nil
}

// GetPrimaryRequestType 获取主要申请类型（用于工作流分流）
// 通过合同 -> 询价 -> 采购申请的关联链获取申请类型
func (a *Arrival) GetPrimaryRequestType() string {
	// 如果有关联的合同，且合同有关联的询价，且询价有关联的采购申请
	if a.Contract != nil && a.Contract.Inquiry != nil && a.Contract.Inquiry.Request != nil {
		// 解析采购申请的申请类型（JSON字符串格式）
		if a.Contract.Inquiry.Request.RequestType != "" {
			// 这里简化处理，假设只取第一个申请类型
			// 实际应该解析JSON数组，但为了简化，我们假设大多数情况下只有一个类型
			// 或者可以根据业务规则选择主要类型
			return "equipment" // 默认返回设备采购类型，实际应该解析JSON
		}
	}
	return "other" // 默认为其他类型
}

// CalculateTotals 计算总数量和总金额
func (a *Arrival) CalculateTotals() {
	a.TotalQuantity = 0
	a.TotalAmount = 0.0

	for _, item := range a.Items {
		a.TotalQuantity += item.CurrentArrivalQuantity
		a.TotalAmount += item.CurrentArrivalAmount
	}
}

// IsEditable 判断是否可编辑
func (a *Arrival) IsEditable() bool {
	return a.Status == "purchase_review" // 只有采购负责人审批阶段可以编辑
}

// IsCancellable 判断是否可取消
func (a *Arrival) IsCancellable() bool {
	return a.Status != "completed" && a.Status != "cancelled"
}

// IsApprovable 判断是否可审批
func (a *Arrival) IsApprovable() bool {
	return a.Status != "draft" && a.Status != "completed" && a.Status != "cancelled"
}

// ArrivalFilter 到货管理查询过滤条件
type ArrivalFilter struct {
	ArrivalNo  string     `form:"arrival_no" json:"arrival_no"`   // 到货通知单号
	ContractID *uint      `form:"contract_id" json:"contract_id"` // 合同ID
	SupplierID *uint      `form:"supplier_id" json:"supplier_id"` // 供应商ID
	Status     string     `form:"status" json:"status"`           // 状态
	IsComplete *bool      `form:"is_complete" json:"is_complete"` // 是否全部到货
	CreatedBy  *uint      `form:"created_by" json:"created_by"`   // 创建人
	StartDate  *time.Time `form:"start_date" json:"start_date"`   // 开始日期
	EndDate    *time.Time `form:"end_date" json:"end_date"`       // 结束日期
	MinAmount  *float64   `form:"min_amount" json:"min_amount"`   // 最小金额
	MaxAmount  *float64   `form:"max_amount" json:"max_amount"`   // 最大金额
}
