package constants

// 业务类型常量
const (
	// BusinessTypePurchaseRequest 采购申请业务类型
	BusinessTypePurchaseRequest = "purchase_request"

	// BusinessTypePurchaseInquiry 采购询价业务类型
	BusinessTypePurchaseInquiry = "purchase_inquiry"

	// BusinessTypePurchaseContract 采购合同业务类型
	BusinessTypePurchaseContract = "purchase_contract"

	// BusinessTypePurchaseOrder 采购订单业务类型
	BusinessTypePurchaseOrder = "purchase_order"

	// BusinessTypePurchaseInvoice 采购发票业务类型
	BusinessTypePurchaseInvoice = "purchase_invoice"

	// BusinessTypePurchasePayment 采购付款业务类型
	BusinessTypePurchasePayment = "purchase_payment"

	// BusinessTypePaymentRequest 付款申请业务类型
	BusinessTypePaymentRequest = "payment_request"

	// BusinessTypeArrival 到货管理业务类型
	BusinessTypeArrival = "arrival"

	// BusinessTypeShipment 发货管理业务类型
	BusinessTypeShipment = "shipment"

	// BusinessTypePurchaseReturn 采购退货业务类型
	BusinessTypePurchaseReturn = "purchase_return"

	// BusinessTypeBudgetApplication 预算申请业务类型
	BusinessTypeBudgetApplication = "budget_application"

	// BusinessTypeVendorEvaluation 供应商评估业务类型
	BusinessTypeVendorEvaluation = "vendor_evaluation"
)

// 工作流类型常量
const (
	// WorkflowTypeApproval 常规审批流程
	WorkflowTypeApproval = "approval"

	// WorkflowTypeEmergencyApproval 紧急审批流程
	WorkflowTypeEmergencyApproval = "emergency_approval"

	// WorkflowTypeAmendment 变更流程
	WorkflowTypeAmendment = "amendment"

	// WorkflowTypeRenewal 续期流程
	WorkflowTypeRenewal = "renewal"

	// WorkflowTypeCancel 取消流程
	WorkflowTypeCancel = "cancel"

	// WorkflowTypeReturn 退货流程
	WorkflowTypeReturn = "return"

	// WorkflowTypePayment 付款流程
	WorkflowTypePayment = "payment"
)

// GetBusinessTypeDisplayName 获取业务类型显示名称
func GetBusinessTypeDisplayName(businessType string) string {
	businessTypeMap := map[string]string{
		BusinessTypePurchaseRequest:   "采购申请",
		BusinessTypePurchaseContract:  "采购合同",
		BusinessTypePurchaseOrder:     "采购订单",
		BusinessTypePurchaseInvoice:   "采购发票",
		BusinessTypePurchasePayment:   "采购付款",
		BusinessTypePaymentRequest:    "付款申请",
		BusinessTypeArrival:           "到货管理",
		BusinessTypeShipment:          "发货管理",
		BusinessTypePurchaseReturn:    "采购退货",
		BusinessTypeBudgetApplication: "预算申请",
		BusinessTypeVendorEvaluation:  "供应商评估",
		BusinessTypePurchaseInquiry:   "采购询比价",
	}

	if displayName, exists := businessTypeMap[businessType]; exists {
		return displayName
	}
	return businessType
}

// GetWorkflowTypeDisplayName 获取工作流类型显示名称
func GetWorkflowTypeDisplayName(workflowType string) string {
	workflowTypeMap := map[string]string{
		WorkflowTypeApproval:          "常规审批流程",
		WorkflowTypeEmergencyApproval: "紧急审批流程",
		WorkflowTypeAmendment:         "变更流程",
		WorkflowTypeRenewal:           "续期流程",
		WorkflowTypeCancel:            "取消流程",
		WorkflowTypeReturn:            "退货流程",
		WorkflowTypePayment:           "付款流程",
	}

	if displayName, exists := workflowTypeMap[workflowType]; exists {
		return displayName
	}
	return workflowType
}

// GetAllBusinessTypes 获取所有业务类型
func GetAllBusinessTypes() []string {
	return []string{
		BusinessTypePurchaseRequest,
		BusinessTypePurchaseContract,
		BusinessTypePurchaseOrder,
		BusinessTypePurchaseInvoice,
		BusinessTypePurchasePayment,
		BusinessTypePaymentRequest,
		BusinessTypeArrival,
		BusinessTypeShipment,
		BusinessTypePurchaseReturn,
		BusinessTypeBudgetApplication,
		BusinessTypeVendorEvaluation,
		BusinessTypePurchaseInquiry,
	}
}

// GetAllWorkflowTypes 获取所有工作流类型
func GetAllWorkflowTypes() []string {
	return []string{
		WorkflowTypeApproval,
		WorkflowTypeEmergencyApproval,
		WorkflowTypeAmendment,
		WorkflowTypeRenewal,
		WorkflowTypeCancel,
		WorkflowTypeReturn,
		WorkflowTypePayment,
	}
}

// IsValidBusinessType 验证业务类型是否有效
func IsValidBusinessType(businessType string) bool {
	validTypes := GetAllBusinessTypes()
	for _, validType := range validTypes {
		if businessType == validType {
			return true
		}
	}
	return false
}

// IsValidWorkflowType 验证工作流类型是否有效
func IsValidWorkflowType(workflowType string) bool {
	validTypes := GetAllWorkflowTypes()
	for _, validType := range validTypes {
		if workflowType == validType {
			return true
		}
	}
	return false
}
