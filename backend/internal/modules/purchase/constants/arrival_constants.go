package constants

// 到货管理状态常量
const (
	// ArrivalStageDraft 草稿状态
	ArrivalStageDraft = "draft"

	// ArrivalStagePurchaseReview 采购负责人审批阶段
	ArrivalStagePurchaseReview = "purchase_review"

	// ArrivalStageServerAdminReview 服务器管理员审批阶段
	ArrivalStageServerAdminReview = "server_admin_review"

	// ArrivalStageNetworkAdminReview 网络管理员审批阶段
	ArrivalStageNetworkAdminReview = "network_admin_review"

	// ArrivalStageAssetAdminReview 资产管理审批阶段
	ArrivalStageAssetAdminReview = "asset_admin_review"

	// ArrivalStageCompleted 完成阶段
	ArrivalStageCompleted = "completed"

	// ArrivalStageCancelled 已取消
	ArrivalStageCancelled = "cancelled"
)

// GetArrivalStageDisplayName 获取到货管理状态显示名称
func GetArrivalStageDisplayName(stage string) string {
	stageMap := map[string]string{
		ArrivalStageDraft:              "草稿",
		ArrivalStagePurchaseReview:     "采购负责人审批中",
		ArrivalStageServerAdminReview:  "服务器管理员审批中",
		ArrivalStageNetworkAdminReview: "网络管理员审批中",
		ArrivalStageAssetAdminReview:   "资产管理审批中",
		ArrivalStageCompleted:          "已完成",
		ArrivalStageCancelled:          "已取消",
	}

	if displayName, exists := stageMap[stage]; exists {
		return displayName
	}
	return stage
}

// GetAllArrivalStages 获取所有到货管理状态
func GetAllArrivalStages() []string {
	return []string{
		ArrivalStageDraft,
		ArrivalStagePurchaseReview,
		ArrivalStageServerAdminReview,
		ArrivalStageNetworkAdminReview,
		ArrivalStageAssetAdminReview,
		ArrivalStageCompleted,
		ArrivalStageCancelled,
	}
}

// IsValidArrivalStage 验证到货管理状态是否有效
func IsValidArrivalStage(stage string) bool {
	validStages := GetAllArrivalStages()
	for _, validStage := range validStages {
		if stage == validStage {
			return true
		}
	}
	return false
}

// IsEquipmentType 判断是否为服务器采购类型
func IsEquipmentType(requestType string) bool {
	equipmentTypes := []string{
		"服务器",
	}
	for _, eType := range equipmentTypes {
		if requestType == eType {
			return true
		}
	}
	return false
}

// IsSoftwareType 判断是否为软件采购类型
func IsSoftwareType(requestType string) bool {
	softwareTypes := []string{
		"软件",
		"软件采购",
		"软件服务",
		"software", // 兼容英文
	}
	for _, sType := range softwareTypes {
		if requestType == sType {
			return true
		}
	}
	return false
}

// GetNextStageByRequestType 根据采购申请类型获取下一个审批阶段
func GetNextStageByRequestType(requestType string) string {
	if IsEquipmentType(requestType) {
		return ArrivalStageServerAdminReview // 服务器采购 -> 服务器管理员审批
	}
	if IsSoftwareType(requestType) {
		return ArrivalStageNetworkAdminReview // 软件采购 -> 网络管理员审批
	}
	// 其他类型（服务、维保、办公用品、咨询、培训、营销等）-> 资产管理审批
	return ArrivalStageAssetAdminReview
}

// GetRequestTypeDisplayName 获取申请类型显示名称
