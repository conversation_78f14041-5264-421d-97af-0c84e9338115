package constants

// 采购合同工作流阶段常量 - 统一使用这套常量
const (
	// ContractStageDraft 草稿状态
	ContractStageDraft = "draft"

	// ContractStagePurchaseReview 采购负责人审批阶段
	ContractStagePurchaseReview = "purchase_review"

	// ContractStageFinanceReview 财务负责人审批阶段
	ContractStageFinanceReview = "finance_review"

	// ContractStageLegalReview 法务负责人审批阶段
	ContractStageLegalReview = "legal_review"

	// ContractStageEnterpriseReview 企业负责人审批阶段
	ContractStageEnterpriseReview = "enterprise_review"

	// ContractStageInternalSign 内部签章阶段
	ContractStageInternalSign = "internal_sign"

	// ContractStageDoubleSign 双方签章阶段
	ContractStageDoubleSign = "double_sign"

	// ContractStageCompleted 完成阶段
	ContractStageCompleted = "completed"

	// ContractStageRejected 合同已拒绝
	ContractStageRejected = "rejected"

	// ContractStageCancelled 合同已取消
	ContractStageCancelled = "cancelled"
)

// 采购合同工作流信号常量
const (
	// SignalNameContractApproval 合同审批信号
	SignalNameContractApproval = "contract_approval_signal"

	// SignalNameContractRollback 合同回退信号
	SignalNameContractRollback = "contract_rollback_signal"
)

// GetContractStageDisplayName 获取合同阶段显示名称
func GetContractStageDisplayName(stage string) string {
	stageMap := map[string]string{
		ContractStageDraft:            "草稿",
		ContractStagePurchaseReview:   "采购负责人审批",
		ContractStageFinanceReview:    "财务负责人审批",
		ContractStageLegalReview:      "法务负责人审批",
		ContractStageEnterpriseReview: "企业负责人审批",
		ContractStageInternalSign:     "内部签章",
		ContractStageDoubleSign:       "双方签章",
		ContractStageCompleted:        "已完成",
		ContractStageRejected:         "已拒绝",
		ContractStageCancelled:        "已取消",
	}

	if displayName, exists := stageMap[stage]; exists {
		return displayName
	}
	return stage
}

// GetAllContractStages 获取所有合同阶段
func GetAllContractStages() []string {
	return []string{
		ContractStageDraft,
		ContractStagePurchaseReview,
		ContractStageFinanceReview,
		ContractStageLegalReview,
		ContractStageEnterpriseReview,
		ContractStageInternalSign,
		ContractStageDoubleSign,
		ContractStageCompleted,
		ContractStageRejected,
		ContractStageCancelled,
	}
}

// IsValidContractStage 验证合同阶段是否有效
func IsValidContractStage(stage string) bool {
	validStages := GetAllContractStages()
	for _, validStage := range validStages {
		if stage == validStage {
			return true
		}
	}
	return false
}

// 合同类型常量
const (
	// ContractTypePurchase 采购合同
	ContractTypePurchase = "purchase"

	// ContractTypeSale 销售合同
	ContractTypeSale = "sale"

	// ContractTypeService 服务合同
	ContractTypeService = "service"

	// ContractTypeLease 租赁合同
	ContractTypeLease = "lease"

	// ContractTypeSupplement 补充合同
	ContractTypeSupplement = "supplement"

	// ContractTypeOther 其他合同
	ContractTypeOther = "other"
)
