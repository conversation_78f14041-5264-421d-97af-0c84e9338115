package service

import (
	"context"
)

// SpareService 备件服务接口
type SpareService interface {
	GetSpare(ctx context.Context, id uint) (interface{}, error)
	UseSpare(ctx context.Context, id uint, quantity int) error
	ReturnSpare(ctx context.Context, id uint, quantity int) error
	GetSpareInventory(ctx context.Context, id uint) (int, error)
	ListSpares(ctx context.Context, page, pageSize int, query string, categoryID uint) ([]interface{}, int64, error)
}
