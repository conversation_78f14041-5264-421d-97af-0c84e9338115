package model

import (
	filemodel "backend/internal/modules/file/model"
)

// ImportResult 导入结果
type ImportResult struct {
	Total        int                     `json:"total"`        // 总记录数
	SuccessCount int                     `json:"successCount"` // 成功数量
	FailCount    int                     `json:"failCount"`    // 失败数量
	Data         interface{}             `json:"data"`         // 成功导入的数据
	Failures     []ImportError           `json:"failures"`     // 失败的记录详情
	FileInfo     *filemodel.FileResponse `json:"fileInfo"`     // 导入文件信息
}

// ImportError 导入错误详情
type ImportError struct {
	Row     int               `json:"row"`     // 行号
	Message string            `json:"message"` // 错误信息
	Data    map[string]string `json:"data"`    // 行数据
}
