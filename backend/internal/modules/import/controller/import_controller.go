package controller

import (
	"context"
	"encoding/csv"
	"fmt"
	"log"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"

	"github.com/gin-gonic/gin"

	"backend/internal/modules/import/service"
	"backend/response"
)

// 为context key创建自定义类型，避免与其他包的字符串key冲突
type contextKey string

const (
	// GinContextKey 是Gin上下文的键
	ginContextKey contextKey = "ginContext"
	// FileNameKey 是文件名的键
	fileNameKey contextKey = "fileName"
	// ModuleTypeKey 是模块类型的键
	moduleTypeKey contextKey = "moduleType"
)

// ImportController 通用导入控制器
type ImportController struct {
	service service.ImportService
}

func NewImportController(service service.ImportService) *ImportController {
	return &ImportController{service: service}
}

// @Summary 导入数据
// @Description 导入CSV/Excel文件数据
// @Tags 数据导入
// @Accept multipart/form-data
// @Produce json
// @Param Authorization header string true "JWT token"
// @Param model formData string true "模型类型"
// @Param file formData file true "要导入的文件"
// @Param import_mode formData string false "导入模式: overwrite(覆盖), append_bottom(底部追加), append_top(顶部追加)" Enums(overwrite, append_bottom, append_top)
// @Success 200 {object} response.ResponseStruct "导入结果"
// @Failure 400 {object} response.ResponseStruct "请求错误"
// @Failure 500 {object} response.ResponseStruct "服务器错误"
// @Router /api/v1/import/data [post]
// @Security BearerAuth
func (c *ImportController) ImportData(ctx *gin.Context) {
	// 获取模型类型
	modelType := ctx.PostForm("model")
	if modelType == "" {
		response.Fail(ctx, http.StatusBadRequest, "缺少模型类型参数")
		return
	}

	// 获取导入模式
	importMode := ctx.PostForm("import_mode")
	if importMode == "" {
		importMode = "overwrite" // 默认为覆盖模式
	}

	// 验证导入模式是否有效
	validModes := map[string]bool{
		"overwrite":     true,
		"append_bottom": true,
		"append_top":    true,
	}

	if !validModes[importMode] {
		response.Fail(ctx, http.StatusBadRequest, "无效的导入模式: "+importMode)
		return
	}

	// 获取上传的文件
	file, fileHeader, err := ctx.Request.FormFile("file")
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "文件上传失败: "+err.Error())
		return
	}
	defer func() {
		if err := file.Close(); err != nil {
			fmt.Printf("关闭文件失败: %v\n", err)
		}
	}()

	// 创建导入选项
	options := service.ImportOptions{
		ImportMode: importMode,
		Overwrite:  importMode == "overwrite", // 当模式为overwrite时，设置覆盖标志
	}

	// 如果是覆盖模式，自动处理软删除的记录（恢复它们）
	if importMode == "overwrite" {
		options.HandleSoftDeleted = "restore" // 覆盖模式下自动恢复软删除的记录
	}

	// 创建包含gin上下文和文件名的上下文
	importCtx := context.WithValue(ctx.Request.Context(), ginContextKey, ctx)
	importCtx = context.WithValue(importCtx, fileNameKey, fileHeader.Filename)
	importCtx = context.WithValue(importCtx, moduleTypeKey, modelType)

	// 调用服务进行导入
	result, err := c.service.ImportData(importCtx, modelType, file, options)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "导入失败: "+err.Error())
		return
	}

	response.Success(ctx, result, "导入成功")
}

// @Summary 获取导入模板
// @Description 下载指定模型的导入模板
// @Tags CMDB-数据导入
// @Produce octet-stream
// @Param model path string true "模型类型(region/az/device/resource)"
// @Success 200 {file} file "CSV模板文件"
// @Router /import/template/{model} [get]
func (c *ImportController) GetImportTemplate(ctx *gin.Context) {
	// 修改参数名称与路由定义匹配
	modelType := ctx.Param("model")
	// 添加日志
	log.Printf("请求的模型类型: '%s'", modelType)

	if modelType == "" {
		response.Fail(ctx, http.StatusBadRequest, "模型类型不能为空")
		return
	}

	// 获取模板内容
	content, filename, err := c.service.GetImportTemplate(modelType)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取模板失败: "+err.Error())
		return
	}

	// 设置响应头并返回文件
	ctx.Header("Content-Disposition", "attachment; filename="+filename)
	ctx.Header("Content-Type", "text/csv; charset=utf-8")
	ctx.Data(http.StatusOK, "text/csv", content)
}

func (c *ImportController) GetOutboundImportTemplate(ctx *gin.Context) {
	// 修改参数名称与路由定义匹配
	modelType := ctx.Param("model")
	// 添加日志
	log.Printf("请求的模型类型: '%s'", modelType)

	if modelType == "" {
		response.Fail(ctx, http.StatusBadRequest, "模型类型不能为空")
		return
	}

	// 获取模板内容
	content, filename, err := c.service.GetImportTemplate(modelType)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取模板失败: "+err.Error())
		return
	}

	csvFileName := fmt.Sprintf("%s.csv", filename)
	err = saveBytesToCSV(content, csvFileName)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "保存csv文件失败: "+err.Error())
		return
	}

	// 检测分隔符
	delimiter := detectDelimiter(content)

	excelFileName := fmt.Sprintf("%s-%s.xlsx", filename, time.Now().Format("20060102150405"))
	targetExcelName := filepath.Join("storage", excelFileName)
	err = csvToExcel(csvFileName, targetExcelName, delimiter)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "文件转换失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"fileName": excelFileName,
		"filePath": targetExcelName,
	}, "获取文件成功")
}

func (c *ImportController) ImportOutboundSpareData(ctx *gin.Context) {
	// 获取模型类型
	modelType := ctx.PostForm("model")
	if modelType != "outbound-spare" {
		response.Fail(ctx, http.StatusBadRequest, "类型参数错误")
		return
	}

	// 获取上传的文件
	file, fileHeader, err := ctx.Request.FormFile("file")
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "文件上传失败: "+err.Error())
		return
	}
	defer func(file multipart.File) {
		err := file.Close()
		if err != nil {
			fmt.Printf("关闭文件: %s, 失败: %v\n", file, err)
		}
	}(file)

	// 创建导入选项
	options := service.ImportOptions{
		//ImportMode: importMode,
		//Overwrite:  importMode == "overwrite", // 当模式为overwrite时，设置覆盖标志
	}

	// 创建包含gin上下文和文件名的上下文
	importCtx := context.WithValue(ctx.Request.Context(), ginContextKey, ctx)
	importCtx = context.WithValue(importCtx, fileNameKey, fileHeader.Filename)
	importCtx = context.WithValue(importCtx, moduleTypeKey, modelType)

	// 调用服务进行导入
	result, err := c.service.ImportOutboundSpareData(importCtx, "outbound-spare", file, options)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "导入失败: "+err.Error())
		return
	}

	response.Success(ctx, result, "校验成功")
}

// CSV转Excel
func csvToExcel(csvPath, excelPath string, delimiter rune) error {
	// 打开CSV文件
	// #nosec G304 -- 文件路径由程序内部生成，不是直接来自用户输入
	csvFile, err := os.Open(csvPath)
	if err != nil {
		return fmt.Errorf("无法打开CSV文件: %v", err)
	}
	defer func(csvFile *os.File) {
		err := csvFile.Close()
		if err != nil {
			fmt.Printf("关闭CSV失败: %s : %v\n", csvPath, err)
		}
	}(csvFile)

	// 读取CSV数据
	reader := csv.NewReader(csvFile)
	reader.Comma = delimiter // 设置检测到的分隔符
	reader.LazyQuotes = true
	reader.TrimLeadingSpace = true
	reader.FieldsPerRecord = -1 // 允许每行的字段数不同
	records, err := reader.ReadAll()
	if err != nil {
		return fmt.Errorf("读取CSV数据失败: %v", err)
	}

	// 创建Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()

	// 获取默认工作表
	sheetName := f.GetSheetName(0)

	// 写入数据
	for rowIndex, row := range records {
		for colIndex, cellValue := range row {
			// 计算单元格坐标 (例如: A1, B1, C1...)
			cell, err := excelize.CoordinatesToCellName(colIndex+1, rowIndex+1)
			if err != nil {
				return fmt.Errorf("转换单元格坐标失败: %v", err)
			}
			// 写入单元格值
			if err := f.SetCellValue(sheetName, cell, cellValue); err != nil {
				return fmt.Errorf("写入单元格失败: %v", err)
			}
		}
	}

	// 保存Excel文件
	if err := f.SaveAs(excelPath); err != nil {
		return fmt.Errorf("保存Excel文件失败: %v", err)
	}

	return nil
}

// 将数据保存到 CSV 文件
func saveBytesToCSV(data []byte, filePath string) error {
	// 将字节数据转换为字符串
	content := string(data)

	// 创建文件 - 确保路径安全性
	// 创建一个绝对路径
	absPath, err := filepath.Abs(filePath)
	if err != nil {
		return fmt.Errorf("获取绝对路径失败: %w", err)
	}

	// 检查路径是否在预期目录下
	storageDir, err := filepath.Abs("storage")
	if err != nil {
		return fmt.Errorf("获取存储目录绝对路径失败: %w", err)
	}

	if !strings.HasPrefix(absPath, storageDir) {
		return fmt.Errorf("文件路径不在允许的目录范围内")
	}

	// 安全地创建文件 - 已经验证了路径安全性
	// #nosec G304 -- 已经在上面验证了文件路径在storage目录下，不会导致目录遍历漏洞
	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("创建文件失败: %w", err)
	}
	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
			fmt.Printf("关闭CSV失败: %s : %v\n", filePath, err)
		}
	}(file)

	// 创建 CSV 写入器
	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 按行分割数据
	lines := strings.Split(content, "\n")

	// 逐行写入 CSV
	for _, line := range lines {
		// 跳过空行
		if strings.TrimSpace(line) == "" {
			continue
		}

		// 按逗号分割每行的数据
		fields := strings.Split(line, ",")

		// 写入 CSV 行
		if err := writer.Write(fields); err != nil {
			return fmt.Errorf("写入 CSV 行失败: %w", err)
		}
	}

	// 检查是否有写入错误
	if err := writer.Error(); err != nil {
		return fmt.Errorf("CSV 写入错误: %w", err)
	}

	fmt.Printf("CSV 文件已成功保存到: %s\n", filePath)
	return nil
}

// 检测CSV文件的分隔符
func detectDelimiter(data []byte) rune {
	// 检查前几行来确定可能的分隔符
	dataStr := string(data)
	lines := strings.Split(dataStr, "\n")

	// 只分析前10行或文件的所有行（取较小值）
	maxLines := 10
	if len(lines) < maxLines {
		maxLines = len(lines)
	}

	// 候选分隔符
	delimiters := []rune{',', '\t', ';', '|'}
	counts := make(map[rune]int)

	for i := 0; i < maxLines; i++ {
		if len(lines[i]) == 0 {
			continue
		}

		for _, d := range delimiters {
			counts[d] += strings.Count(lines[i], string(d))
		}
	}

	// 找到出现次数最多的分隔符
	maxCount := 0
	var bestDelimiter = ',' // 默认为逗号

	for d, count := range counts {
		if count > maxCount {
			maxCount = count
			bestDelimiter = d
		}
	}

	return bestDelimiter
}
