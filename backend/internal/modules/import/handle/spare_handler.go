package importhandler

import (
	"context"
	"errors"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"

	"backend/internal/modules/cmdb/model/asset"
	fileservice "backend/internal/modules/file/service"
	"backend/pkg/utils"
)

// SpareHandler 备件导入处理器
type SpareHandler struct {
	*BaseHandler
	DB *gorm.DB
}

// NewSpareHandler 创建备件导入处理器
func NewSpareHandler(db *gorm.DB) *SpareHandler {
	// 创建文件服务
	fileSvc := fileservice.NewFileService(db)

	return &SpareHandler{
		BaseHandler: NewBaseHandler(db, fileSvc),
		DB:          db,
	}
}

// GetRequiredHeaders 获取必需的表头字段
func (h *SpareHandler) GetRequiredHeaders() []string {
	return []string{"SN", "产品PN号码", "仓库", "来源类型", "资产状态", "硬件状态"}
}

// ValidateHeaders 验证CSV表头
func (h *SpareHandler) ValidateHeaders(headers []string) error {
	requiredHeaders := h.GetRequiredHeaders()

	for _, required := range requiredHeaders {
		found := false
		for _, header := range headers {
			// 清理表头，移除可能的星号(*)标记和空白字符
			cleanHeader := strings.TrimSpace(header)
			if strings.HasPrefix(cleanHeader, "*") {
				cleanHeader = strings.TrimSpace(cleanHeader[1:])
			}

			if cleanHeader == required {
				found = true
				break
			}
		}

		if !found {
			return errors.New("CSV文件缺少必需字段: " + required)
		}
	}

	return nil
}

// ParseRecord 解析CSV记录为备件对象
func (h *SpareHandler) ParseRecord(record []string, headerMap map[string]int, rowNum int) (interface{}, error) {
	// 获取字段索引
	snIndex, hasSN := headerMap["SN"]
	productPnIndex, hasProductPn := headerMap["产品PN号码"]
	warehouseIndex, hasWarehouse := headerMap["仓库"]
	sourceTypeIndex, hasSourceType := headerMap["来源类型"]
	assetStatusIndex, hasAssetStatus := headerMap["资产状态"]
	hardwareStatusIndex, hasHardwareStatus := headerMap["硬件状态"]
	priceIndex, hasPrice := headerMap["金额"]
	locationIndex, hasLocation := headerMap["存放位置"]
	remarkIndex, hasRemark := headerMap["备注"]
	firmwareVersionIndex, hasFirmwareVersion := headerMap["固件版本"]
	purchaseDateIndex, hasPurchaseDate := headerMap["购买时间"]
	warrantyExpireIndex, hasWarrantyExpire := headerMap["过保时间"]
	relatedAssetSnIndex, hasRelatedAssetSn := headerMap["关联资产SN"]
	brandIndex, hasBrand := headerMap["品牌"]

	// 获取SN字段，如果没有或为空则设为空字符串
	var sn string
	if hasSN && snIndex < len(record) {
		sn = strings.TrimSpace(record[snIndex])
	}

	if !hasProductPn || productPnIndex >= len(record) {
		return nil, errors.New("缺少产品PN号码")
	}
	productPn := strings.TrimSpace(record[productPnIndex])
	if productPn == "" {
		return nil, errors.New("产品PN号码不能为空")
	}

	// 获取品牌信息（如果有）
	var brand string
	if hasBrand && brandIndex < len(record) {
		brand = strings.TrimSpace(record[brandIndex])
	}

	if !hasWarehouse || warehouseIndex >= len(record) {
		return nil, errors.New("缺少仓库")
	}
	warehouseValue := strings.TrimSpace(record[warehouseIndex])
	if warehouseValue == "" {
		return nil, errors.New("仓库不能为空")
	}

	if !hasSourceType || sourceTypeIndex >= len(record) {
		return nil, errors.New("缺少来源类型")
	}
	sourceType := strings.TrimSpace(record[sourceTypeIndex])
	if sourceType == "" {
		return nil, errors.New("来源类型不能为空")
	}

	// 定义中文状态到英文代码的映射函数
	mapAssetStatus := func(status string) string {
		statusMap := map[string]string{
			"待入库": "pending_in",
			"已入库": "in_stock",
			"待出库": "pending_out",
			"已出库": "out_stock",
			"闲置中": "idle",
			"使用中": "in_use",
			"维修中": "repairing",
			"待报废": "pending_scrap",
			"已报废": "scrapped",
		}

		if code, exists := statusMap[status]; exists {
			return code
		}
		return status // 如果不在映射表中，保持原值
	}

	mapHardwareStatus := func(status string) string {
		statusMap := map[string]string{
			"正常": "normal",
			"故障": "faulty",
			"警告": "warning",
		}

		if code, exists := statusMap[status]; exists {
			return code
		}
		return status // 如果不在映射表中，保持原值
	}

	sourceTypeMap := func(sourceType string) string {
		sourceTypeMap := map[string]string{
			"新购": "new_purchase",
			"拆机": "dismantled",
			"返修": "return_repaired",
			"调拨": "Allocate",
			"其他": "other",
		}

		if code, exists := sourceTypeMap[sourceType]; exists {
			return code
		}
		return sourceType // 如果不在映射表中，保持原值
	}

	// 获取可选字段
	var assetStatus, hardwareStatus, location, remark, firmwareVersion string
	var purchaseDate, warrantyExpire time.Time
	var relatedAssetSn string
	var price float64

	if hasAssetStatus && assetStatusIndex < len(record) {
		assetStatus = strings.TrimSpace(record[assetStatusIndex])
		// 转换可能的中文状态为英文代码
		assetStatus = mapAssetStatus(assetStatus)
	} else {
		assetStatus = "pending_in" // 默认待入库
	}

	if hasHardwareStatus && hardwareStatusIndex < len(record) {
		hardwareStatus = strings.TrimSpace(record[hardwareStatusIndex])
		// 转换可能的中文状态为英文代码
		hardwareStatus = mapHardwareStatus(hardwareStatus)
	} else {
		hardwareStatus = "normal" // 默认正常
	}

	if  sourceTypeIndex < len(record) {
		sourceType = strings.TrimSpace(record[sourceTypeIndex])
		// 转换来源类型
		sourceType = sourceTypeMap(sourceType)
	} else {
		return nil, errors.New("缺少来源类型")
	}

	if hasPrice && priceIndex < len(record) {
		priceStr := strings.TrimSpace(record[priceIndex])
		if priceStr != "" {
			var err error
			price, err = strconv.ParseFloat(priceStr, 64)
			if err != nil {
				return nil, errors.New("金额格式不正确")
			}
		}
	}

	if hasLocation && locationIndex < len(record) {
		location = strings.TrimSpace(record[locationIndex])
	}

	if hasRemark && remarkIndex < len(record) {
		remark = strings.TrimSpace(record[remarkIndex])
	}

	if hasFirmwareVersion && firmwareVersionIndex < len(record) {
		firmwareVersion = strings.TrimSpace(record[firmwareVersionIndex])
	}

	if hasPurchaseDate && purchaseDateIndex < len(record) {
		dateStr := strings.TrimSpace(record[purchaseDateIndex])
		if dateStr != "" {
			// 尝试多种格式解析日期
			formats := []string{
				"2006-01-02",
				"2006/01/02",
				"2006-01-02 15:04:05",
				"2006/01/02 15:04:05",
			}

			for _, format := range formats {
				if date, err := time.Parse(format, dateStr); err == nil {
					purchaseDate = date
					break
				}
			}
		}
	}

	if hasWarrantyExpire && warrantyExpireIndex < len(record) {
		dateStr := strings.TrimSpace(record[warrantyExpireIndex])
		if dateStr != "" {
			// 尝试多种格式解析日期
			formats := []string{
				"2006-01-02",
				"2006/01/02",
				"2006-01-02 15:04:05",
				"2006/01/02 15:04:05",
			}

			for _, format := range formats {
				if date, err := time.Parse(format, dateStr); err == nil {
					warrantyExpire = date
					break
				}
			}
		}
	}

	if hasRelatedAssetSn && relatedAssetSnIndex < len(record) {
		relatedAssetSn = strings.TrimSpace(record[relatedAssetSnIndex])
	}

	// 根据PN号码和品牌（如果有）查询产品
	var product struct {
		ID           uint   `json:"id"`
		PN           string `json:"pn"`
		Brand        string `json:"brand"`
		Model        string `json:"model"`
		MaterialType string `json:"material_type"`
	}

	query := h.DB.Table("products").Where("pn = ?", productPn)

	// 如果提供了品牌，使用品牌作为额外的过滤条件
	if brand != "" {
		query = query.Where("brand = ?", brand)
	}

	if err := query.First(&product).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果使用品牌查询未找到，但有提供品牌，尝试查找所有匹配PN的产品
			if brand != "" {
				var products []struct {
					ID    uint   `json:"id"`
					PN    string `json:"pn"`
					Brand string `json:"brand"`
					Model string `json:"model"`
				}

				if err := h.DB.Table("products").Where("pn = ?", productPn).Find(&products).Error; err != nil {
					return nil, errors.New("查询产品时出错: " + err.Error())
				}

				if len(products) > 0 {
					// 找到了匹配PN的产品，但品牌不匹配
					brandList := make([]string, 0, len(products))
					for _, p := range products {
						if p.Brand != "" {
							brandList = append(brandList, p.Brand)
						}
					}

					if len(brandList) > 0 {
						return nil, errors.New("未找到PN为 " + productPn + " 且品牌为 '" + brand + "' 的产品。可用的品牌有: " + strings.Join(brandList, ", "))
					}

					// 如果没有找到有品牌的产品，尝试使用无品牌的产品
					for _, p := range products {
						if p.Brand == "" {
							product.ID = p.ID
							product.PN = p.PN
							product.Brand = p.Brand
							product.Model = p.Model
							break
						}
					}

					// 如果仍然没有有效的产品ID，返回第一个找到的产品
					if product.ID == 0 && len(products) > 0 {
						product.ID = products[0].ID
						product.PN = products[0].PN
						product.Brand = products[0].Brand
						product.Model = products[0].Model
					}
				} else {
					return nil, errors.New("未找到PN号码为 " + productPn + " 的产品")
				}
			} else {
				// 没有提供品牌，查找所有匹配PN的产品
				var products []struct {
					ID    uint   `json:"id"`
					PN    string `json:"pn"`
					Brand string `json:"brand"`
					Model string `json:"model"`
				}

				if err := h.DB.Table("products").Where("pn = ?", productPn).Find(&products).Error; err != nil {
					return nil, errors.New("查询产品时出错: " + err.Error())
				}

				if len(products) == 0 {
					return nil, errors.New("未找到PN号码为 " + productPn + " 的产品")
				} else if len(products) > 1 {
					// 找到多个产品，优先选择无品牌的，否则要求用户提供品牌
					var nobrands []string
					var brands []string

					for _, p := range products {
						if p.Brand == "" {
							nobrands = append(nobrands, "无品牌 [型号:"+p.Model+"]")
						} else {
							brands = append(brands, p.Brand+" [型号:"+p.Model+"]")
						}
					}

					// 如果只有一个无品牌产品，使用它
					if len(nobrands) == 1 && len(brands) == 0 {
						for _, p := range products {
							if p.Brand == "" {
								product.ID = p.ID
								product.PN = p.PN
								product.Brand = p.Brand
								product.Model = p.Model
								break
							}
						}
					} else {
						// 提示用户提供品牌信息
						allBrands := append(nobrands, brands...)
						return nil, errors.New("PN号码 " + productPn + " 对应多个产品，请在CSV中提供'品牌'列。可用选项: " + strings.Join(allBrands, ", "))
					}
				} else {
					// 只有一个产品，直接使用
					product.ID = products[0].ID
					product.PN = products[0].PN
					product.Brand = products[0].Brand
					product.Model = products[0].Model
				}
			}
		} else {
			return nil, errors.New("查询产品时出错: " + err.Error())
		}
	}

	// 确保我们有有效的产品ID
	if product.ID == 0 {
		errorMsg := "无法确定产品: PN=" + productPn
		if brand != "" {
			errorMsg += ", 品牌=" + brand
		}
		return nil, errors.New(errorMsg)
	}

	// 根据仓库编码或名称查询仓库
	var warehouse struct {
		ID   uint   `json:"id"`
		Name string `json:"name"`
		Code string `json:"code"`
		Type string `json:"type"`
	}

	// 先尝试精确匹配仓库编码
	if err := h.DB.Table("warehouses").Where("code = ?", warehouseValue).First(&warehouse).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果按编码找不到，则尝试按名称查询
			if err := h.DB.Table("warehouses").Where("name = ?", warehouseValue).First(&warehouse).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					// 如果还找不到，尝试模糊匹配
					var warehouses []struct {
						ID   uint   `json:"id"`
						Name string `json:"name"`
						Code string `json:"code"`
					}

					if err := h.DB.Table("warehouses").
						Where("name LIKE ? OR code LIKE ?", "%"+warehouseValue+"%", "%"+warehouseValue+"%").
						Limit(5).Find(&warehouses).Error; err != nil {
						return nil, errors.New("查询仓库时出错: " + err.Error())
					}

					if len(warehouses) == 0 {
						return nil, errors.New("未找到名称或编码包含 '" + warehouseValue + "' 的仓库")
					} else if len(warehouses) > 1 {
						// 如果找到多个仓库，返回模糊匹配错误
						warehouseNames := make([]string, len(warehouses))
						for i, w := range warehouses {
							warehouseNames[i] = w.Name + " [" + w.Code + "]"
						}
						return nil, errors.New("仓库名称或编码 '" + warehouseValue + "' 匹配到多个仓库: " + strings.Join(warehouseNames, ", ") + ". 请提供完整准确的仓库名称或编码")
					}

					// 如果只找到一个，使用它
					warehouse.ID = warehouses[0].ID
					warehouse.Name = warehouses[0].Name
					warehouse.Code = warehouses[0].Code
				} else {
					return nil, errors.New("查询仓库时出错: " + err.Error())
				}
			}
		} else {
			return nil, errors.New("查询仓库时出错: " + err.Error())
		}
	}

	// 确保我们有有效的仓库ID
	if warehouse.ID == 0 {
		return nil, errors.New("找不到有效的仓库: " + warehouseValue)
	}

	// 创建备件对象
	spare := &asset.AssetSpare{
		SN:              sn,
		ProductID:       product.ID,
		WarehouseID:     warehouse.ID,
		SourceType:      sourceType,
		AssetStatus:     assetStatus,
		HardwareStatus:  hardwareStatus,
		Price:           price,
		Location:        location,
		Remark:          remark,
		FirmwareVersion: firmwareVersion,
		RelatedAssetSN:  relatedAssetSn,
	}

	// 设置可选的日期字段
	if !purchaseDate.IsZero() {
		spare.PurchaseDate = utils.Date(purchaseDate)
	}

	if !warrantyExpire.IsZero() {
		spare.WarrantyExpire = utils.Date(warrantyExpire)
	}

	return spare, nil
}

// SaveRecord 保存备件记录到数据库
func (h *SpareHandler) SaveRecord(ctx context.Context, tx *gorm.DB, record interface{}) error {
	spare, ok := record.(*asset.AssetSpare)
	if !ok {
		return errors.New("记录类型错误")
	}

	// 从上下文中获取导入选项
	var options ImportOptions
	optionsValue := ctx.Value(importOptionsKey)
	if optionsValue != nil {
		var ok bool
		options, ok = optionsValue.(ImportOptions)
		if !ok {
			return errors.New("导入选项类型错误")
		}
	}

	// 如果SN为空，直接创建新记录
	if spare.SN == "" {
		if err := tx.Create(spare).Error; err != nil {
			return errors.New("创建备件失败: " + err.Error())
		}
		return nil
	}

	// 检查是否存在相同SN的备件（包括软删除的记录）
	var existingSpare asset.AssetSpare
	var isExist bool
	var isSoftDeleted bool

	// 如果覆盖模式，查询所有记录（包括软删除的）
	if options.Overwrite {
		if err := tx.Unscoped().Where("sn = ?", spare.SN).First(&existingSpare).Error; err == nil {
			isExist = true
			// 检查是否为软删除状态
			isSoftDeleted = !existingSpare.DeletedAt.Time.IsZero()
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
	} else {
		// 非覆盖模式下，只查询未删除的记录
		if err := tx.Where("sn = ?", spare.SN).First(&existingSpare).Error; err == nil {
			isExist = true
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
	}

	// 如果存在相同的备件且未设置覆盖选项，则返回错误
	if isExist && !options.Overwrite {
		return errors.New("已存在相同SN的备件: " + spare.SN)
	}

	// 如果设置了覆盖选项且存在相同的备件（无论是否被软删除），则更新现有记录
	if isExist && options.Overwrite {
		// 更新备件数据，保留原ID和创建时间
		spare.ID = existingSpare.ID
		spare.CreatedAt = existingSpare.CreatedAt

		// 如果记录被软删除，先恢复它
		if isSoftDeleted {
			if err := tx.Unscoped().Model(&existingSpare).Update("deleted_at", nil).Error; err != nil {
				return errors.New("恢复软删除的备件失败: " + err.Error())
			}
		}

		// 更新备件记录
		if err := tx.Model(&existingSpare).Updates(spare).Error; err != nil {
			return errors.New("更新备件失败: " + err.Error())
		}

		return nil
	}

	// 创建新备件
	if err := tx.Create(spare).Error; err != nil {
		return errors.New("创建备件失败: " + err.Error())
	}

	return nil
}
