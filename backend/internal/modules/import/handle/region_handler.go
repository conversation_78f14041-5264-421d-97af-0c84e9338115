package importhandler

import (
	"context"
	"errors"
	"strings"

	"gorm.io/gorm"

	"backend/internal/modules/cmdb/model/location"
	fileservice "backend/internal/modules/file/service"
)

// RegionHandler Region导入处理器
type RegionHandler struct {
	*BaseHandler
}

// NewRegionHandler 创建Region导入处理器
func NewRegionHandler(db *gorm.DB) *RegionHandler {
	// 创建文件服务
	fileSvc := fileservice.NewFileService(db)

	return &RegionHandler{
		BaseHandler: NewBaseHandler(db, fileSvc),
	}
}

// GetRequiredHeaders 获取必需的表头字段
func (h *RegionHandler) GetRequiredHeaders() []string {
	return []string{"Region名称"}
}

// ValidateHeaders 验证CSV表头
func (h *RegionHandler) ValidateHeaders(headers []string) error {
	requiredHeaders := h.GetRequiredHeaders()

	// 检查必需字段
	for _, required := range requiredHeaders {
		found := false
		for _, header := range headers {
			if strings.TrimSpace(header) == required {
				found = true
				break
			}
		}

		if !found {
			return errors.New("CSV文件缺少必需字段: " + required)
		}
	}

	return nil
}

// ParseRecord 解析CSV记录为Region对象
func (h *RegionHandler) ParseRecord(record []string, headerMap map[string]int, rowNum int) (interface{}, error) {
	// 获取字段索引
	nameIndex, hasName := headerMap["Region名称"]
	statusIndex, hasStatus := headerMap["状态"]
	descIndex, hasDesc := headerMap["描述信息"]

	if !hasName || nameIndex >= len(record) {
		return nil, errors.New("缺少Region名称")
	}

	name := strings.TrimSpace(record[nameIndex])
	if name == "" {
		return nil, errors.New("Region名称不能为空")
	}

	// 创建Region对象
	region := &location.Region{
		Name: name,
	}

	// 设置状态
	if hasStatus && statusIndex < len(record) {
		status := strings.TrimSpace(record[statusIndex])
		if status != "" {
			// 将中文状态转换为英文代码
			switch status {
			case "启用":
				region.Status = "active"
			case "禁用":
				region.Status = "disabled"
			default:
				region.Status = status
			}
		} else {
			region.Status = "active" // 默认状态
		}
	} else {
		region.Status = "active" // 默认状态
	}

	// 设置描述
	if hasDesc && descIndex < len(record) {
		region.Description = strings.TrimSpace(record[descIndex])
	}

	return region, nil
}

// SaveRecord 保存Region记录到数据库
func (h *RegionHandler) SaveRecord(ctx context.Context, tx *gorm.DB, record interface{}) error {
	region, ok := record.(*location.Region)
	if !ok {
		return errors.New("记录类型错误")
	}

	// 检查是否已存在同名区域
	var count int64
	if err := tx.Model(&location.Region{}).Where("name = ?", region.Name).Count(&count).Error; err != nil {
		return err
	}

	if count > 0 {
		return errors.New("已存在同名区域: " + region.Name)
	}

	// 创建区域
	return tx.Create(region).Error
}
