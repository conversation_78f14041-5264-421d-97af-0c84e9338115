package repository

import (
	"backend/internal/modules/server/model"
	"time"

	"gorm.io/gorm"
)

// ServerRepository 定义服务器数据访问接口
type ServerRepository interface {
	GetServerInfoBySN(sn string) (*model.ServerInfo, error)
	// 批量查询存在的资产（服务器、交换机）SN
	ListServerSNs(sns []string) ([]string, error)
}

type serverRepository struct {
	db *gorm.DB
}

// NewServerRepository 创建新的 ServerRepository 实例
func NewServerRepository(db *gorm.DB) ServerRepository {
	return &serverRepository{db: db}
}

// GetServerInfoBySN 通过 SN 获取服务器详细信息（多表关联查询）
// 此处使用 LEFT JOIN 联查 asset_devices、server_assets、machine_templates、resources、
// cabinets、rooms、data_centers、az、regions 以及采购合同和供应商表以补充展示字段，
// 对于模型中缺失的字段，默认返回 'N/A'、空串或默认数值（例如设备 U 数使用 1）
func (r *serverRepository) GetServerInfoBySN(sn string) (*model.ServerInfo, error) {
	var serverInfo model.ServerInfo

	query := `
SELECT
    ad.sn as sn,
    ad.asset_id as asset_id,
    mt.template_name as template_name,
    IFNULL(sa.host_name, ad.sn) as host_name,
    mt.cpu_model as cpu_info,
    CONCAT(mt.memory_capacity, 'GB') as memory_info,
    'N/A' as hdd_info,
    'N/A' as ssd_info,
    mt.gpu_model as gpu_info,
    'N/A' as power_info,
    'N/A' as nic_info,
    1 as device_u,
    ad.asset_type as server_type,
    ad.asset_status as server_status,
    ad.purchase_date as arrival_time,
    ad.warranty_expire as warranty_expire,
    reg.name as region_name,
    az.name as az_name,
    dc.name as data_center_name,
    rm.name as room_name,
    CAST(r.rack_position AS CHAR) as rack_position,
    s.name as supplier_name,
    ad.brand as brand,
    ad.purchase_order as order_number,
    sa.os as os_version,
    'N/A' as power_consumption,
    '' as parent_sn,
    '' as uplink_network_sn,
    IFNULL(ad.created_at, NOW()) as create_time,
    IFNULL(ad.updated_at, NOW()) as update_time,
    sa.ip as internal_ip,
    sa.ip as ip_info,
    mt.component_list as component_info
FROM asset_devices ad
LEFT JOIN server_assets sa ON ad.asset_id = sa.asset_id
LEFT JOIN machine_templates mt ON sa.template_id = mt.template_id
LEFT JOIN resources r ON ad.asset_id = r.asset_id
LEFT JOIN cabinets c ON r.cabinet_id = c.cabinet_id
LEFT JOIN rooms rm ON c.room_id = rm.room_id
LEFT JOIN data_centers dc ON rm.data_center_id = dc.data_center_id
LEFT JOIN az ON dc.az_id = az.az_id
LEFT JOIN regions reg ON az.region_id = reg.region_id
LEFT JOIN purchase_orders po ON ad.purchase_order = po.po_number
LEFT JOIN suppliers s ON po.supplier_id = s.supplier_id
WHERE ad.sn = ?
LIMIT 1
`
	result := r.db.Raw(query, sn).Scan(&serverInfo)
	if result.Error != nil {
		return nil, result.Error
	}
	// 对于时间字段如果为空，设置默认值（例如当前时间）
	if serverInfo.ArrivalTime.IsZero() {
		serverInfo.ArrivalTime = time.Now()
	}
	if serverInfo.WarrantyExpire.IsZero() {
		serverInfo.WarrantyExpire = time.Now()
	}
	return &serverInfo, nil
}

func (r *serverRepository) ListServerSNs(sns []string) ([]string, error) {
	var existingSNs []string
	err := r.db.Model(&model.AssetDevice{}).
		Where("sn IN ?", sns).
		Distinct("sn"). // 去重处理（可选）
		Pluck("sn", &existingSNs).
		Error
	return existingSNs, err
}
