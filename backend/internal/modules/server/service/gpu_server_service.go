package service

import (
	"backend/internal/modules/server/model"
	"backend/internal/modules/server/repository"
	"context"
	"errors"
)

type GpuServerService struct {
	gpuRepo *repository.GpuServerRepository
}

func NewGpuServerService(repo *repository.GpuServerRepository) *GpuServerService {
	return &GpuServerService{
		gpuRepo: repo,
	}
}

// CreateGpuServer 创建一条新的 GPU 服务器记录
func (s *GpuServerService) CreateGpuServer(ctx context.Context, gpu *model.GpuServer) error {
	// 校验必填字段：项目、主机名和 SN 为必填
	if gpu.Project == "" || gpu.Hostname == "" || gpu.SN == "" {
		return errors.New("项目、主机名和SN为必填字段")
	}
	return s.gpuRepo.CreateGpuServer(ctx, gpu)
}

// GetGpuServerByID 根据 ID 获取 GPU 服务器记录
func (s *GpuServerService) GetGpuServerByID(ctx context.Context, id uint) (*model.GpuServer, error) {
	return s.gpuRepo.GetGpuServerByID(ctx, id)
}

// UpdateGpuServer 更新 GPU 服务器记录
func (s *GpuServerService) UpdateGpuServer(ctx context.Context, gpu *model.GpuServer) error {
	// 先判断记录是否存在
	if _, err := s.gpuRepo.GetGpuServerByID(ctx, gpu.ID); err != nil {
		return errors.New("GPU 服务器记录不存在")
	}
	return s.gpuRepo.UpdateGpuServer(ctx, gpu)
}

// DeleteGpuServer 删除 GPU 服务器记录
func (s *GpuServerService) DeleteGpuServer(ctx context.Context, id uint) error {
	// 判断记录是否存在
	if _, err := s.gpuRepo.GetGpuServerByID(ctx, id); err != nil {
		return errors.New("GPU 服务器记录不存在")
	}
	return s.gpuRepo.DeleteGpuServer(ctx, id)
}

// QueryGpuServers 根据查询条件进行分页查询
func (s *GpuServerService) QueryGpuServers(ctx context.Context, query *model.GpuServerQuery) ([]model.GpuServer, int64, error) {
	return s.gpuRepo.QueryGpuServers(ctx, query)
}

// UpdateGpuServerPartial 根据传入的部分字段更新 GPU 服务器记录
func (s *GpuServerService) UpdateGpuServerPartial(ctx context.Context, id uint, updates map[string]interface{}) error {
	// 检查记录是否存在
	if _, err := s.gpuRepo.GetGpuServerByID(ctx, id); err != nil {
		return errors.New("GPU 服务器记录不存在")
	}
	return s.gpuRepo.UpdateGpuServerFields(ctx, id, updates)
}
