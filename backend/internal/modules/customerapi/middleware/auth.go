package middleware

import (
	"backend/internal/modules/customerapi/model"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CustomerAuthMiddleware 客户认证中间件
type CustomerAuthMiddleware struct {
	customers model.CustomerMap // 允许的客户信息映射表
	logger    *zap.Logger       // 日志记录器
}

// NewCustomerAuthMiddleware 创建客户认证中间件
func NewCustomerAuthMiddleware(customers model.CustomerMap, logger *zap.Logger) *CustomerAuthMiddleware {
	// 如果没有提供logger，使用空日志记录器
	if logger == nil {
		logger = zap.NewNop()
	}

	return &CustomerAuthMiddleware{
		customers: customers,
		logger:    logger,
	}
}

// AuthRequired JWT认证中间件（已废弃，保留兼容性）
func (m *CustomerAuthMiddleware) AuthRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		m.logger.Warn("使用了不安全的JWT认证方式，请改用签名认证")
		c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"code": 1, "msg": "JWT认证已禁用，请使用签名认证"})
	}
}

// SignatureAuth 基于签名的认证中间件
func (m *CustomerAuthMiddleware) SignatureAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取认证参数
		customerID := c.GetHeader("X-Customer-ID")
		timestamp := c.GetHeader("X-Timestamp")
		signature := c.GetHeader("X-Signature")

		// 验证必要参数
		if customerID == "" || timestamp == "" || signature == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"code": 1, "msg": "缺少认证参数"})
			return
		}

		// 验证客户ID是否为允许的值
		customer, exists := m.customers[customerID]
		if !exists {
			m.logger.Warn("无效的客户ID尝试访问",
				zap.String("customer_id", customerID),
				zap.String("ip", c.ClientIP()))

			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"code": 1,
				"msg": "无效的客户ID"})
			return
		}

		// 验证时间戳是否在有效期内（5分钟）
		ts, err := strconv.ParseInt(timestamp, 10, 64)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"code": 1, "msg": "无效的时间戳"})
			return
		}

		now := time.Now().Unix()
		if now-ts > 300 || ts-now > 300 {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"code": 1, "msg": "时间戳已过期"})
			return
		}

		// 计算期望的签名，使用客户特定的密钥
		// 签名算法: HMAC-SHA256(customerID + timestamp, customerSecret)
		expectedSignature := m.CalculateSignatureWithSecret(customerID, timestamp, customer.Secret)

		// 验证签名
		if signature != expectedSignature {
			m.logger.Warn("签名验证失败",
				zap.String("customer_id", customerID),
				zap.String("ip", c.ClientIP()),
				zap.String("provided_signature", signature))

			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"code": 1, "msg": "签名无效"})
			return
		}

		// 认证通过，存储客户信息
		c.Set("customerAuthenticated", true)
		c.Set("customerID", customerID)
		c.Set("customerName", customer.Name) // 存储客户名称
		c.Next()
	}
}

// CalculateSignatureWithSecret 计算API调用签名（使用指定密钥）
func (m *CustomerAuthMiddleware) CalculateSignatureWithSecret(customerID, timestamp, secret string) string {
	message := customerID + timestamp
	mac := hmac.New(sha256.New, []byte(secret))
	mac.Write([]byte(message))
	return hex.EncodeToString(mac.Sum(nil))
}
