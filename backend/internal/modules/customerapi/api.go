package customerapi

import (
	"backend/internal/modules/customerapi/controller"
	"backend/internal/modules/customerapi/middleware"
	"backend/internal/modules/customerapi/model"
	"backend/internal/modules/customerapi/repository"
	"backend/internal/modules/customerapi/service"
	ticketService "backend/internal/modules/ticket/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ModuleConfig 模块配置
type ModuleConfig struct {
	Customers         model.CustomerMap // 客户信息映射表
	FeishuWebhookURL  string            // 飞书通知Webhook URL
	FeishuSecret      string            // 飞书通知密钥
	DetailURLTemplate string            // 工单详情URL模板
}

// InitCustomerAPI 初始化客户API模块
func InitCustomerAPI(router *gin.Engine, faultTicketService ticketService.FaultTicketService, config ModuleConfig, logger *zap.Logger) {
	// 如果未提供日志记录器，使用空日志记录器
	if logger == nil {
		logger = zap.NewNop()
	}

	// 创建客户认证中间件
	authMiddleware := middleware.NewCustomerAuthMiddleware(config.Customers, logger)

	// 创建API组
	apiGroup := router.Group("/event/api")

	// 注册认证中间件 - 使用签名认证
	apiGroup.Use(authMiddleware.SignatureAuth())

	// 初始化服务层
	customerService := service.NewCustomerTicketService(
		faultTicketService,
		config.FeishuWebhookURL,
		config.FeishuSecret,
		config.DetailURLTemplate,
	)

	// 初始化控制器
	ticketController := controller.NewCustomerTicketController(customerService, logger)

	// 注册路由
	ticketController.RegisterRoutes(apiGroup)
}

// InitCustomerAPIWithLogging 初始化客户API模块，包含日志记录功能
func InitCustomerAPIWithLogging(router *gin.Engine, customerTicketService service.CustomerTicketService, db *gorm.DB, logger *zap.Logger, config ModuleConfig) {
	// 如果未提供日志记录器，使用空日志记录器
	if logger == nil {
		logger = zap.NewNop()
	}

	// 自动迁移API日志表
	if err := db.AutoMigrate(&model.CustomerAPILog{}); err != nil {
		logger.Error("迁移客户API日志表失败", zap.Error(err))
		// 不中断程序，继续初始化其他功能
	} else {
		logger.Info("客户API日志表迁移成功")
	}

	// 创建API日志相关组件
	apiLogRepo := repository.NewCustomerAPILogRepository(db)
	apiLogService := service.NewCustomerAPILogService(apiLogRepo, logger)

	// 创建API日志记录中间件
	apiLoggerMiddleware := middleware.NewAPILoggerMiddleware(apiLogService, logger)

	// 创建客户认证中间件
	authMiddleware := middleware.NewCustomerAuthMiddleware(config.Customers, logger)

	// 创建API组
	apiGroup := router.Group("/event/api")

	// 注册中间件 - 先认证后记录日志
	apiGroup.Use(authMiddleware.SignatureAuth())
	apiGroup.Use(apiLoggerMiddleware.LoggingMiddleware())

	// 初始化控制器 - 传递日志记录器
	ticketController := controller.NewCustomerTicketController(customerTicketService, logger)

	// 注册路由
	ticketController.RegisterRoutes(apiGroup)

	logger.Info("客户API模块（带日志记录）初始化完成")
}

// 为了向后兼容保留原有函数签名
func InitCustomerAPIWithLoggingLegacy(router *gin.Engine, customerTicketService service.CustomerTicketService, db *gorm.DB, logger *zap.Logger, secretKey string, allowedCustomerIDs []string) {
	// 创建简单的客户映射
	customers := make(model.CustomerMap)
	for _, id := range allowedCustomerIDs {
		customers[id] = &model.Customer{
			ID:     id,
			Secret: secretKey,
			Name:   id,
		}
	}

	config := ModuleConfig{
		Customers:         customers,
		FeishuWebhookURL:  "",
		FeishuSecret:      "",
		DetailURLTemplate: "",
	}

	InitCustomerAPIWithLogging(router, customerTicketService, db, logger, config)
}

// NewModuleConfig 创建模块配置
func NewModuleConfig(customers model.CustomerMap, feishuWebhookURL, feishuSecret, detailURLTemplate string) ModuleConfig {
	// 如果未提供customers，创建默认客户
	if len(customers) == 0 {
		// 返回空配置，客户必须由调用者提供
		logger := zap.NewExample()
		logger.Warn("未提供客户配置，请确保提供了有效的客户列表")
	}

	return ModuleConfig{
		Customers:         customers,
		FeishuWebhookURL:  feishuWebhookURL,
		FeishuSecret:      feishuSecret,
		DetailURLTemplate: detailURLTemplate,
	}
}

// NewCustomerMap 从客户列表创建客户映射
func NewCustomerMap(customers []*model.Customer) model.CustomerMap {
	customerMap := make(model.CustomerMap)
	for _, customer := range customers {
		if customer.ID != "" && customer.Secret != "" {
			customerMap[customer.ID] = customer
		}
	}
	return customerMap
}
