package controller

import (
	"backend/internal/modules/customerapi/model"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// 创建CustomerTicketService的模拟实现
type MockCustomerTicketService struct {
	mock.Mock
}

func (m *MockCustomerTicketService) CreateTicket(ctx context.Context, request *model.CustomerTicketRequest) (*model.CustomerTicketResponse, error) {
	args := m.Called(ctx, request)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	result, ok := args.Get(0).(*model.CustomerTicketResponse)
	if !ok {
		return nil, fmt.Errorf("类型断言失败: 期望 *model.CustomerTicketResponse")
	}
	return result, args.Error(1)
}

func (m *MockCustomerTicketService) BatchCreateTickets(ctx context.Context, request *model.BatchCreateTicketRequest) (*model.BatchCreateTicketResponse, error) {
	args := m.Called(ctx, request)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	result, ok := args.Get(0).(*model.BatchCreateTicketResponse)
	if !ok {
		return nil, fmt.Errorf("类型断言失败: 期望 *model.BatchCreateTicketResponse")
	}
	return result, args.Error(1)
}

func (m *MockCustomerTicketService) QueryTickets(ctx context.Context, request *model.CustomerTicketQueryRequest) (*model.CustomerTicketQueryResponse, error) {
	args := m.Called(ctx, request)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	result, ok := args.Get(0).(*model.CustomerTicketQueryResponse)
	if !ok {
		return nil, fmt.Errorf("类型断言失败: 期望 *model.CustomerTicketQueryResponse")
	}
	return result, args.Error(1)
}

func (m *MockCustomerTicketService) SetAuthority(ctx context.Context, request *model.CustomerTicketAuthorityRequest) error {
	args := m.Called(ctx, request)
	return args.Error(0)
}

// 设置测试环境
func setupTest() (*gin.Context, *httptest.ResponseRecorder, *MockCustomerTicketService, *CustomerTicketController) {
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	mockService := new(MockCustomerTicketService)
	// 使用空日志记录器
	controller := NewCustomerTicketController(mockService, nil)
	return ctx, w, mockService, controller
}

// 测试CreateTicket成功的情况
func TestCreateTicket_Success(t *testing.T) {
	ctx, w, mockService, controller := setupTest()

	// 设置请求内容
	requestBody := model.CustomerTicketRequest{
		TicketVmIP:    "*************",
		TicketContent: "服务器CPU使用率过高",
	}
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		t.Fatalf("无法序列化请求体: %v", err)
	}
	ctx.Request = httptest.NewRequest("POST", "/event/api/create", bytes.NewBuffer(jsonData))
	ctx.Request.Header.Set("Content-Type", "application/json")

	// 设置模拟服务的行为
	mockResponse := &model.CustomerTicketResponse{
		TicketID: "i202503232626000",
	}
	mockService.On("CreateTicket", mock.Anything, &requestBody).Return(mockResponse, nil)

	// 执行请求
	controller.CreateTicket(ctx)

	// 验证结果
	assert.Equal(t, http.StatusOK, w.Code)

	var response model.APIResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, "创建成功", response.Msg)

	// 验证响应数据 - 需要根据实际类型进行断言
	responseData, ok := response.Data.(map[string]interface{})
	if !ok {
		t.Fatalf("响应数据类型断言失败")
	}
	assert.Equal(t, "i202503232626000", responseData["ticketId"])

	// 验证是否按预期调用了服务
	mockService.AssertExpectations(t)
}

// 测试CreateTicket参数错误的情况
func TestCreateTicket_InvalidParams(t *testing.T) {
	ctx, w, _, controller := setupTest()

	// 设置一个没有必填字段的请求
	requestBody := model.CustomerTicketRequest{
		// 缺少TicketVmIP和TicketContent
	}
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		t.Fatalf("无法序列化请求体: %v", err)
	}
	ctx.Request = httptest.NewRequest("POST", "/event/api/create", bytes.NewBuffer(jsonData))
	ctx.Request.Header.Set("Content-Type", "application/json")

	// 执行请求
	controller.CreateTicket(ctx)

	// 验证结果
	assert.Equal(t, http.StatusOK, w.Code)

	var response model.APIResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 1, response.Code)
	assert.Contains(t, response.Msg, "参数错误")
}

// 测试CreateTicket服务返回错误的情况
func TestCreateTicket_ServiceError(t *testing.T) {
	ctx, w, mockService, controller := setupTest()

	// 设置请求内容
	requestBody := model.CustomerTicketRequest{
		TicketVmIP:    "*************",
		TicketContent: "服务器CPU使用率过高",
	}
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		t.Fatalf("无法序列化请求体: %v", err)
	}
	ctx.Request = httptest.NewRequest("POST", "/event/api/create", bytes.NewBuffer(jsonData))
	ctx.Request.Header.Set("Content-Type", "application/json")

	// 设置模拟服务返回错误
	mockService.On("CreateTicket", mock.Anything, &requestBody).Return(nil, errors.New("服务错误"))

	// 执行请求
	controller.CreateTicket(ctx)

	// 验证结果
	assert.Equal(t, http.StatusOK, w.Code)

	var response model.APIResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 1, response.Code)
	assert.Contains(t, response.Msg, "创建工单失败")
}

// 测试CreateTicket工单已存在的情况
func TestCreateTicket_ExistingTicket(t *testing.T) {
	ctx, w, mockService, controller := setupTest()

	// 设置请求内容
	requestBody := model.CustomerTicketRequest{
		TicketVmIP:    "*************",
		TicketContent: "服务器CPU使用率过高",
	}
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		t.Fatalf("无法序列化请求体: %v", err)
	}
	ctx.Request = httptest.NewRequest("POST", "/event/api/create", bytes.NewBuffer(jsonData))
	ctx.Request.Header.Set("Content-Type", "application/json")

	// 设置模拟服务返回已存在工单的情况
	mockResponse := &model.CustomerTicketResponse{
		TicketID:       "i202503232626000",
		ExistingTicket: true,
	}
	mockService.On("CreateTicket", mock.Anything, &requestBody).Return(mockResponse, errors.New("工单已存在"))

	// 执行请求
	controller.CreateTicket(ctx)

	// 验证结果
	assert.Equal(t, http.StatusOK, w.Code)

	var response model.APIResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Contains(t, response.Msg, "已存在IP为")
}

// 测试QueryTickets成功的情况
func TestQueryTickets_Success(t *testing.T) {
	ctx, w, mockService, controller := setupTest()

	// 设置查询参数
	ctx.Request = httptest.NewRequest("GET", "/event/api/query?ticketStatus=open&page=1&pageSize=20", nil)
	ctx.Request.Header.Set("Content-Type", "application/json")

	// 准备预期的请求和响应
	expectedRequest := &model.CustomerTicketQueryRequest{
		TicketStatus: "open",
		TicketFrom:   model.FromAll,
		Page:         1,
		PageSize:     20,
	}

	// 按照实际模型结构创建响应
	mockResponse := &model.CustomerTicketQueryResponse{
		Total:    1,
		Page:     1,
		PageSize: 20,
		Data: []model.TicketDetail{
			{
				TicketID:        "i202503232626000",
				TicketContent:   "服务器CPU使用率过高",
				TicketVmIP:      "*************",
				TicketFrom:      "customer",
				TicketStatus:    "open",
				TicketCreateAt:  time.Now(),
				TicketUpdatedAt: time.Now(),
				IsAuthorized:    true,
			},
		},
	}

	mockService.On("QueryTickets", mock.Anything, mock.MatchedBy(func(req *model.CustomerTicketQueryRequest) bool {
		return req.TicketStatus == expectedRequest.TicketStatus &&
			req.TicketFrom == expectedRequest.TicketFrom &&
			req.Page == expectedRequest.Page &&
			req.PageSize == expectedRequest.PageSize
	})).Return(mockResponse, nil)

	// 执行请求
	controller.QueryTickets(ctx)

	// 验证结果
	assert.Equal(t, http.StatusOK, w.Code)

	var response model.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, "查询成功", response.Msg)

	mockService.AssertExpectations(t)
}

// 测试SetAuthority成功的情况
func TestSetAuthority_Success(t *testing.T) {
	ctx, w, mockService, controller := setupTest()

	// 设置请求内容
	requestBody := model.CustomerTicketAuthorityRequest{
		TicketID: "i202503232626000",
	}
	jsonData, _ := json.Marshal(requestBody)
	ctx.Request = httptest.NewRequest("POST", "/event/api/set_authority", bytes.NewBuffer(jsonData))
	ctx.Request.Header.Set("Content-Type", "application/json")

	// 设置模拟服务的行为
	mockService.On("SetAuthority", mock.Anything, &requestBody).Return(nil)

	// 执行请求
	controller.SetAuthority(ctx)

	// 验证结果
	assert.Equal(t, http.StatusOK, w.Code)

	var response model.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, "操作成功", response.Msg)

	mockService.AssertExpectations(t)
}

// 测试BatchCreateTickets成功的情况
func TestBatchCreateTickets_Success(t *testing.T) {
	ctx, w, mockService, controller := setupTest()

	// 设置请求内容
	requestBody := model.BatchCreateTicketRequest{
		Tickets: []model.CustomerTicketRequest{
			{
				TicketVmIP:    "*************",
				TicketContent: "服务器CPU使用率过高",
			},
			{
				TicketVmIP:    "*************",
				TicketContent: "数据库连接异常",
			},
		},
	}
	jsonData, _ := json.Marshal(requestBody)
	ctx.Request = httptest.NewRequest("POST", "/event/api/batch_create", bytes.NewBuffer(jsonData))
	ctx.Request.Header.Set("Content-Type", "application/json")

	// 设置模拟服务的行为，根据实际结构体字段
	mockResponse := &model.BatchCreateTicketResponse{
		SuccessCount: 2,
		FailCount:    0,
		Results: []model.BatchCreateTicketResult{
			{
				TicketVmIP: "*************",
				Success:    true,
				TicketID:   "i202503232626001",
				Detail: &model.CustomerTicketRequest{
					TicketVmIP:    "*************",
					TicketContent: "服务器CPU使用率过高",
				},
			},
			{
				TicketVmIP: "*************",
				Success:    true,
				TicketID:   "i202503232626002",
				Detail: &model.CustomerTicketRequest{
					TicketVmIP:    "*************",
					TicketContent: "数据库连接异常",
				},
			},
		},
	}
	mockService.On("BatchCreateTickets", mock.Anything, &requestBody).Return(mockResponse, nil)

	// 执行请求
	controller.BatchCreateTickets(ctx)

	// 验证结果
	assert.Equal(t, http.StatusOK, w.Code)

	var response model.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Contains(t, response.Msg, "批量创建完成")

	mockService.AssertExpectations(t)
}
