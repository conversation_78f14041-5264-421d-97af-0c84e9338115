package service

import (
	"backend/internal/modules/customerapi/model"
	"backend/internal/modules/customerapi/repository"
	"context"
	"time"

	"go.uber.org/zap"
)

// CustomerAPILogService 客户API日志服务接口
type CustomerAPILogService interface {
	// LogAPIOperation 记录API操作日志
	LogAPIOperation(ctx context.Context, log *model.CustomerAPILog) error

	// QueryLogs 查询API日志
	QueryLogs(ctx context.Context, customerID, resourceIP, action string, startTime, endTime time.Time, page, pageSize int) ([]*model.CustomerAPILog, int64, error)
}

// customerAPILogService 客户API日志服务实现
type customerAPILogService struct {
	repo   repository.CustomerAPILogRepository
	logger *zap.Logger
}

// NewCustomerAPILogService 创建客户API日志服务
func NewCustomerAPILogService(repo repository.CustomerAPILogRepository, logger *zap.Logger) CustomerAPILogService {
	return &customerAPILogService{
		repo:   repo,
		logger: logger,
	}
}

// LogAPIOperation 记录API操作
func (s *customerAPILogService) LogAPIOperation(ctx context.Context, log *model.CustomerAPILog) error {
	// 确保操作时间设置
	if log.OperationTime.IsZero() {
		log.OperationTime = time.Now()
	}

	// 记录到日志文件
	s.logToFile(log)

	// 查询操作只记录到日志文件，不记录到数据库
	if log.Action == "query" {
		return nil
	}

	// 保存到数据库（非查询操作）
	return s.repo.Create(ctx, log)
}

// QueryLogs 查询API日志
func (s *customerAPILogService) QueryLogs(ctx context.Context, customerID, resourceIP, action string, startTime, endTime time.Time, page, pageSize int) ([]*model.CustomerAPILog, int64, error) {
	return s.repo.ListByQuery(ctx, customerID, resourceIP, action, startTime, endTime, page, pageSize)
}

// logToFile 将操作记录到日志文件
func (s *customerAPILogService) logToFile(log *model.CustomerAPILog) {
	// 创建日志字段
	fields := []zap.Field{
		zap.String("action", log.Action),
		zap.String("source_ip", log.SourceIP),
		zap.String("customer_id", log.CustomerID),
		zap.String("path", log.RequestPath),
		zap.String("method", log.RequestMethod),
	}

	// 添加资源IP和工单号（如果存在）
	if log.ResourceIP != "" {
		fields = append(fields, zap.String("resource_ip", log.ResourceIP))
	}

	if log.TicketNo != "" {
		fields = append(fields, zap.String("ticket_no", log.TicketNo))
	}

	// 添加请求体（适当处理敏感信息）
	if log.RequestBody != "" {
		// 可以在这里对requestBody做脱敏处理
		fields = append(fields, zap.String("request_body", log.RequestBody))
	}

	// 添加响应信息
	fields = append(fields,
		zap.Int("response_code", log.ResponseCode),
		zap.String("response_msg", log.ResponseMsg),
		zap.Bool("success", log.Success),
	)

	// 写入日志
	if log.Success {
		s.logger.Info("客户API操作", fields...)
	} else {
		s.logger.Warn("客户API操作失败", fields...)
	}
}
