package service

import (
	scheduleService "backend/internal/modules/schedule/service"
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	ticketService "backend/internal/modules/ticket/service"
	"backend/internal/modules/ticket/workflow"
	"context"
	"crypto/rand"
	"fmt"
	"math/big"
	"time"

	"go.temporal.io/sdk/client"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// NewWorkflowEnabledFaultTicketService 创建支持工作流的故障工单服务适配器
func NewWorkflowEnabledFaultTicketService(
	db *gorm.DB,
	repo repository.FaultTicketRepository,
	temporalClient client.Client,
	logger *zap.Logger,
	scheduleSvc scheduleService.SoftScheduleService,
) ticketService.FaultTicketService {
	return &workflowEnabledFaultTicketService{
		db:              db,
		repo:            repo,
		temporalClient:  temporalClient,
		logger:          logger,
		scheduleService: scheduleSvc,
	}
}

// workflowEnabledFaultTicketService 是FaultTicketService的简化实现
// 专门用于客户API场景，提供工作流集成能力
type workflowEnabledFaultTicketService struct {
	db              *gorm.DB
	repo            repository.FaultTicketRepository
	temporalClient  client.Client
	logger          *zap.Logger
	scheduleService scheduleService.SoftScheduleService
}

// 实现FaultTicketService接口的方法

func (s *workflowEnabledFaultTicketService) CreateFaultTicket(ctx context.Context, ticket *model.FaultTicket) error {
	// 确保创建时间字段被设置
	if ticket.CreationTime.IsZero() {
		ticket.CreationTime = time.Now()
	}

	// 生成工单号（如果没有）
	if ticket.TicketNo == "" {
		now := time.Now()
		// 使用安全的随机数生成器
		max := big.NewInt(1000)
		randomBig, err := rand.Int(rand.Reader, max)
		if err != nil {
			// 如果生成随机数失败，使用纳秒作为备选
			random := time.Now().UnixNano() % 1000
			ticket.TicketNo = fmt.Sprintf("i%s%03d", now.Format("20060102150405"), random)
		} else {
			random := randomBig.Int64()
			ticket.TicketNo = fmt.Sprintf("i%s%03d", now.Format("20060102150405"), random)
		}
	}

	// 根据排班表自动设置接单人
	if s.scheduleService != nil {
		// 获取当前时间
		now := time.Now()

		// 判断当前时间是否在早上9点前
		currentHour := now.Hour()
		var targetDate string

		if currentHour < 9 {
			// 如果当前时间在早上9点前，则取前一天的值班信息
			yesterday := now.AddDate(0, 0, -1)
			targetDate = yesterday.Format("2006-01-02")
		} else {
			// 如果当前时间在早上9点后，则取当天的值班信息
			targetDate = now.Format("2006-01-02")
		}

		// 获取目标日期的值班信息
		schedule, err := s.scheduleService.GetByDate(ctx, targetDate)
		if err == nil && schedule != nil && schedule.PrimaryUserName != "" {
			// 设置AssignedTo为主值班人员
			ticket.AssignedTo = schedule.PrimaryUserName
			s.logger.Info("客户API自动设置报障单分配给当天主值班人员",
				zap.String("ticketNo", ticket.TicketNo),
				zap.String("assignedTo", ticket.AssignedTo),
				zap.String("date", targetDate))
		} else {
			s.logger.Warn("客户API无法获取当天值班信息，未自动分配报障单",
				zap.String("ticketNo", ticket.TicketNo),
				zap.String("targetDate", targetDate),
				zap.Error(err))
		}
	}

	// 创建报障单
	err := s.repo.Create(ctx, ticket)
	if err != nil {
		return err
	}

	// 记录状态历史
	history := &model.FaultTicketStatusHistory{
		FaultTicketID:  ticket.ID,
		PreviousStatus: "",
		NewStatus:      ticket.Status,
		OperatorID:     ticket.ReporterID,
		OperatorName:   ticket.ReporterName,
		OperationTime:  time.Now(),
		Remarks:        "创建报障单",
	}

	// 尝试创建状态历史记录，失败只记录日志不影响主流程
	if err := s.repo.CreateStatusHistory(ctx, history); err != nil {
		s.logger.Error("记录状态历史失败",
			zap.Error(err),
			zap.Uint("ticketID", ticket.ID),
			zap.String("ticketNo", ticket.TicketNo))
	} else {
		s.logger.Info("成功记录工单创建历史",
			zap.Uint("ticketID", ticket.ID),
			zap.String("ticketNo", ticket.TicketNo))
	}

	return nil
}

func (s *workflowEnabledFaultTicketService) GetFaultTicketByID(ctx context.Context, id uint) (*model.FaultTicket, error) {
	return s.repo.GetByID(ctx, id)
}

func (s *workflowEnabledFaultTicketService) GetFaultTicketByTicketNo(ctx context.Context, ticketNo string) (*model.FaultTicket, error) {
	return s.repo.GetByTicketNo(ctx, ticketNo)
}

func (s *workflowEnabledFaultTicketService) UpdateFaultTicket(ctx context.Context, ticket *model.FaultTicket) error {
	return s.repo.Update(ctx, ticket)
}

func (s *workflowEnabledFaultTicketService) ListFaultTickets(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*model.FaultTicket, int64, error) {
	return s.repo.List(ctx, page, pageSize, filters)
}

func (s *workflowEnabledFaultTicketService) GetFaultTicketStatusHistory(ctx context.Context, ticketID uint) ([]*model.FaultTicketStatusHistory, error) {
	return s.repo.GetStatusHistory(ctx, ticketID)
}

func (s *workflowEnabledFaultTicketService) UpdateFaultTicketStatus(ctx context.Context, id uint, status string, operatorID uint, operatorName string) error {
	return s.repo.UpdateFields(ctx, id, map[string]interface{}{
		"status": status,
	})
}

func (s *workflowEnabledFaultTicketService) AssignFaultTicket(ctx context.Context, id uint, engineerID uint) error {
	return s.repo.UpdateFields(ctx, id, map[string]interface{}{
		"assigned_to":     engineerID,
		"assignment_time": time.Now(),
	})
}

func (s *workflowEnabledFaultTicketService) CloseFaultTicket(ctx context.Context, id uint, summary string) error {
	now := time.Now()
	return s.repo.UpdateFields(ctx, id, map[string]interface{}{
		"fault_summary": summary,
		"status":        "completed",
		"close_time":    now,
	})
}

// UpdateFaultTicketFields 更新工单的指定字段
func (s *workflowEnabledFaultTicketService) UpdateFaultTicketFields(ctx context.Context, id uint, fields map[string]interface{}) error {
	return s.repo.UpdateFields(ctx, id, fields)
}

func (s *workflowEnabledFaultTicketService) StartFaultTicketWorkflow(ctx context.Context, ticketID uint) error {
	// 从数据库读取工单详情
	ticket, err := s.repo.GetByID(ctx, ticketID)
	if err != nil {
		return fmt.Errorf("获取工单失败: %w", err)
	}

	// 构造工作流ID
	workflowID := fmt.Sprintf("fault_ticket_%d", ticketID)

	// 启动工作流
	_, err = s.temporalClient.ExecuteWorkflow(ctx, client.StartWorkflowOptions{
		ID:        workflowID,
		TaskQueue: "FAULT_TICKET_TASK_QUEUE",
	}, workflow.FaultTicketWorkflow, workflow.FaultTicketWorkflowInput{
		TicketID:         ticket.ID,
		ReporterID:       ticket.ReporterID,
		ReporterName:     ticket.ReporterName,
		FaultType:        ticket.FaultType,
		FaultDescription: ticket.FaultDescription,
		Priority:         ticket.Priority,
		Source:           ticket.Source,
		Status:           ticket.Status,
		TicketNo:         ticket.TicketNo,
	})

	if err != nil {
		s.logger.Error("启动工作流失败",
			zap.Error(err),
			zap.Uint("ticketID", ticketID))
		return fmt.Errorf("启动工作流失败: %w", err)
	}

	s.logger.Info("成功启动工作流",
		zap.Uint("ticketID", ticketID),
		zap.String("workflowID", workflowID))

	return nil
}

func (s *workflowEnabledFaultTicketService) RecoverInconsistentWorkflows(ctx context.Context) error {
	// 简化实现，实际应该查询状态不一致的工单并重新触发工作流
	return nil
}

func (s *workflowEnabledFaultTicketService) TriggerWorkflowStage(ctx context.Context, ticketID uint, stage string, operatorID uint, operatorName string, comments string, data map[string]interface{}) error {
	// 构造工作流ID
	workflowID := fmt.Sprintf("fault_ticket_%d", ticketID)

	// 构造工作流控制信号
	signal := workflow.WorkflowControlSignal{
		Stage:        stage,
		OperatorID:   operatorID,
		OperatorName: operatorName,
		Comments:     comments,
		Data:         data,
	}

	s.logger.Info("准备触发工作流信号",
		zap.String("workflowID", workflowID),
		zap.String("stage", stage),
		zap.Uint("ticketID", ticketID))

	// 触发工作流信号
	if err := s.temporalClient.SignalWorkflow(ctx, workflowID, "", "workflow_control_signal", signal); err != nil {
		s.logger.Error("触发工作流信号失败",
			zap.Error(err),
			zap.String("workflowID", workflowID),
			zap.String("stage", stage),
			zap.Uint("ticketID", ticketID))
		return fmt.Errorf("触发工作流信号失败: %w", err)
	}

	s.logger.Info("成功触发工作流信号",
		zap.String("workflowID", workflowID),
		zap.String("stage", stage),
		zap.Uint("ticketID", ticketID))

	return nil
}

func (s *workflowEnabledFaultTicketService) CreateRepairSelection(ctx context.Context, selection *model.RepairSelection) error {
	if selection == nil {
		return fmt.Errorf("维修选择信息不能为空")
	}
	return s.db.Create(selection).Error
}

func (s *workflowEnabledFaultTicketService) CreateCustomerApproval(ctx context.Context, approval *model.CustomerApproval) error {
	if approval == nil {
		return fmt.Errorf("客户审批信息不能为空")
	}

	// 设置响应时间
	approval.ResponseTime = time.Now()

	// 使用事务处理
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 保存审批记录
		if err := tx.Create(approval).Error; err != nil {
			return fmt.Errorf("保存客户审批失败: %w", err)
		}

		// 更新工单状态
		ticketStatus := "approved_waiting_action"
		if approval.Status != "approved" {
			ticketStatus = "rejected"
		}

		// 更新工单状态
		if err := tx.Model(&model.FaultTicket{}).Where("id = ?", approval.TicketID).
			Updates(map[string]interface{}{
				"status": ticketStatus,
			}).Error; err != nil {
			return fmt.Errorf("更新工单状态失败: %w", err)
		}

		// 触发工作流信号 (在事务外执行)
		return s.TriggerWorkflowStage(ctx, approval.TicketID, workflow.StageCustomerApproval, approval.CustomerID, approval.CustomerName, approval.Comments, map[string]interface{}{
			"approved": approval.Status == "approved",
			"comments": approval.Comments,
		})
	})
}

func (s *workflowEnabledFaultTicketService) CreateVerification(ctx context.Context, verification *model.Verification) error {
	if verification == nil {
		return fmt.Errorf("验证结果信息不能为空")
	}
	return s.db.Create(verification).Error
}

func (s *workflowEnabledFaultTicketService) CreateRepairTicket(ctx context.Context, faultTicketID uint, repairType string) (*model.RepairTicket, error) {
	// 创建一个简单的维修单记录
	repairTicket := &model.RepairTicket{
		FaultTicketID: faultTicketID,
		RepairType:    repairType,
		Status:        "waiting_accept",
		CreatedTime:   time.Now(),
	}

	// 生成安全的随机数
	max := big.NewInt(10000)
	randomBig, err := rand.Int(rand.Reader, max)
	if err != nil {
		// 如果生成随机数失败，使用纳秒作为备选
		random := time.Now().UnixNano() % 10000
		repairTicket.TicketNo = fmt.Sprintf("RT%s%04d", time.Now().Format("20060102"), random)
	} else {
		random := randomBig.Int64()
		repairTicket.TicketNo = fmt.Sprintf("RT%s%04d", time.Now().Format("20060102"), random)
	}

	err = s.db.Create(repairTicket).Error
	return repairTicket, err
}

// CountDeviceFaults 根据设备SN和查询条件统计故障次数
func (s *workflowEnabledFaultTicketService) CountDeviceFaults(ctx context.Context, filters map[string]interface{}) (int64, error) {
	// 解析查询条件
	deviceSN, ok := filters["deviceSN"].(string)
	if !ok || deviceSN == "" {
		return 0, fmt.Errorf("设备SN不能为空或格式不正确")
	}

	// 获取开始和结束时间
	startTimeInterface, startOk := filters["startTime"]
	endTimeInterface, endOk := filters["endTime"]

	if !startOk || !endOk {
		return 0, fmt.Errorf("开始时间和结束时间不能为空")
	}

	startTime, startOk := startTimeInterface.(*time.Time)
	endTime, endOk := endTimeInterface.(*time.Time)

	if !startOk || !endOk || startTime == nil || endTime == nil {
		return 0, fmt.Errorf("开始时间和结束时间格式不正确")
	}

	// 调用仓库层方法获取数据
	return s.repo.CountMonthlyFaultsByDeviceSN(ctx, deviceSN, *startTime, *endTime)
}
