package service

import (
	"backend/internal/modules/customerapi/model"
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.uber.org/zap"
)

// MockCustomerAPILogRepository 模拟实现API日志存储库
type MockCustomerAPILogRepository struct {
	mock.Mock
}

func (m *MockCustomerAPILogRepository) Create(ctx context.Context, log *model.CustomerAPILog) error {
	args := m.Called(ctx, log)
	return args.Error(0)
}

func (m *MockCustomerAPILogRepository) ListByQuery(ctx context.Context, customerID, resourceIP, action string, startTime, endTime time.Time, page, pageSize int) ([]*model.CustomerAPILog, int64, error) {
	args := m.Called(ctx, customerID, resourceIP, action, startTime, endTime, page, pageSize)
	return args.Get(0).([]*model.CustomerAPILog), args.Get(1).(int64), args.Error(2)
}

// setupLogServiceTest 设置测试环境
func setupLogServiceTest() (*MockCustomerAPILogRepository, *zap.Logger, *customerAPILogService) {
	mockRepo := new(MockCustomerAPILogRepository)

	// 使用zap.NewNop()创建一个不记录任何日志的记录器，避免nil指针异常
	logger := zap.NewNop()

	service := NewCustomerAPILogService(mockRepo, logger).(*customerAPILogService)
	return mockRepo, logger, service
}

// TestLogAPIOperation_Query 测试记录查询操作
func TestLogAPIOperation_Query(t *testing.T) {
	mockRepo, _, service := setupLogServiceTest()

	// 创建一个查询操作日志
	log := &model.CustomerAPILog{
		CustomerID:    "customer123",
		SourceIP:      "********",
		Action:        "query",
		RequestPath:   "/event/api/query",
		RequestMethod: "GET",
		ResourceIP:    "*************",
		RequestBody:   "{}",
		ResponseCode:  0,
		ResponseMsg:   "查询成功",
		Success:       true,
		OperationTime: time.Now(),
	}

	// 执行操作
	err := service.LogAPIOperation(context.Background(), log)

	// 验证查询操作只写入到日志不写入数据库
	assert.Nil(t, err)
	mockRepo.AssertNotCalled(t, "Create")
}

// TestLogAPIOperation_Create 测试记录创建操作
func TestLogAPIOperation_Create(t *testing.T) {
	mockRepo, _, service := setupLogServiceTest()

	// 创建一个非查询操作日志
	log := &model.CustomerAPILog{
		CustomerID:    "customer123",
		SourceIP:      "********",
		Action:        "create",
		RequestPath:   "/event/api/create",
		RequestMethod: "POST",
		ResourceIP:    "*************",
		TicketNo:      "i202503232626000",
		RequestBody:   `{"ticketVmIP":"*************","ticketContent":"服务器CPU使用率过高"}`,
		ResponseCode:  0,
		ResponseMsg:   "创建成功",
		Success:       true,
		OperationTime: time.Now(),
	}

	// 设置模拟存储库行为
	mockRepo.On("Create", mock.Anything, log).Return(nil)

	// 执行操作
	err := service.LogAPIOperation(context.Background(), log)

	// 验证结果
	assert.Nil(t, err)
	mockRepo.AssertExpectations(t)
}

// TestLogAPIOperation_SetTime 测试设置操作时间
func TestLogAPIOperation_SetTime(t *testing.T) {
	mockRepo, _, service := setupLogServiceTest()

	// 创建一个没有设置时间的日志
	log := &model.CustomerAPILog{
		CustomerID:    "customer123",
		SourceIP:      "********",
		Action:        "create",
		RequestPath:   "/event/api/create",
		RequestMethod: "POST",
		ResourceIP:    "*************",
		TicketNo:      "i202503232626000",
		RequestBody:   `{"ticketVmIP":"*************","ticketContent":"服务器CPU使用率过高"}`,
		ResponseCode:  0,
		ResponseMsg:   "创建成功",
		Success:       true,
		// OperationTime未设置
	}

	// 设置模拟存储库行为，捕获传入的日志对象
	mockRepo.On("Create", mock.Anything, mock.MatchedBy(func(captured *model.CustomerAPILog) bool {
		// 验证操作时间是否被自动设置
		return !captured.OperationTime.IsZero()
	})).Return(nil)

	// 执行操作
	err := service.LogAPIOperation(context.Background(), log)

	// 验证结果
	assert.Nil(t, err)
	assert.False(t, log.OperationTime.IsZero(), "OperationTime应该被自动设置")
	mockRepo.AssertExpectations(t)
}

// TestQueryLogs 测试查询日志
func TestQueryLogs(t *testing.T) {
	mockRepo, _, service := setupLogServiceTest()

	// 设置测试参数
	ctx := context.Background()
	customerID := "customer123"
	resourceIP := "*************"
	action := "create"
	startTime := time.Now().Add(-24 * time.Hour)
	endTime := time.Now()
	page := 1
	pageSize := 20

	// 准备模拟返回数据
	expectedLogs := []*model.CustomerAPILog{
		{
			CustomerID:    customerID,
			SourceIP:      "********",
			Action:        action,
			RequestPath:   "/event/api/create",
			RequestMethod: "POST",
			ResourceIP:    resourceIP,
			TicketNo:      "i202503232626000",
			RequestBody:   `{"ticketVmIP":"*************","ticketContent":"服务器CPU使用率过高"}`,
			ResponseCode:  0,
			ResponseMsg:   "创建成功",
			Success:       true,
			OperationTime: time.Now().Add(-12 * time.Hour),
		},
	}
	expectedTotal := int64(1)

	// 设置模拟存储库行为
	mockRepo.On("ListByQuery", ctx, customerID, resourceIP, action, startTime, endTime, page, pageSize).
		Return(expectedLogs, expectedTotal, nil)

	// 执行查询
	logs, total, err := service.QueryLogs(ctx, customerID, resourceIP, action, startTime, endTime, page, pageSize)

	// 验证结果
	assert.Nil(t, err)
	assert.Equal(t, expectedTotal, total)
	assert.Equal(t, expectedLogs, logs)
	mockRepo.AssertExpectations(t)
}

// TestLogAPIOperation_Error 测试创建日志失败的情况
func TestLogAPIOperation_Error(t *testing.T) {
	mockRepo, _, service := setupLogServiceTest()

	// 创建一个非查询操作日志
	log := &model.CustomerAPILog{
		CustomerID:    "customer123",
		SourceIP:      "********",
		Action:        "create",
		RequestPath:   "/event/api/create",
		RequestMethod: "POST",
		ResourceIP:    "*************",
		TicketNo:      "i202503232626000",
		RequestBody:   `{"ticketVmIP":"*************","ticketContent":"服务器CPU使用率过高"}`,
		ResponseCode:  0,
		ResponseMsg:   "创建成功",
		Success:       true,
		OperationTime: time.Now(),
	}

	// 设置模拟存储库返回错误
	expectedError := assert.AnError
	mockRepo.On("Create", mock.Anything, log).Return(expectedError)

	// 执行操作
	err := service.LogAPIOperation(context.Background(), log)

	// 验证结果
	assert.Equal(t, expectedError, err)
	mockRepo.AssertExpectations(t)
}

// TestQueryLogs_Error 测试查询日志失败的情况
func TestQueryLogs_Error(t *testing.T) {
	mockRepo, _, service := setupLogServiceTest()

	// 设置测试参数
	ctx := context.Background()
	customerID := "customer123"
	resourceIP := "*************"
	action := "create"
	startTime := time.Now().Add(-24 * time.Hour)
	endTime := time.Now()
	page := 1
	pageSize := 20

	// 设置模拟存储库返回错误
	expectedError := assert.AnError
	mockRepo.On("ListByQuery", ctx, customerID, resourceIP, action, startTime, endTime, page, pageSize).
		Return([]*model.CustomerAPILog{}, int64(0), expectedError)

	// 执行查询
	logs, total, err := service.QueryLogs(ctx, customerID, resourceIP, action, startTime, endTime, page, pageSize)

	// 验证结果
	assert.Equal(t, expectedError, err)
	assert.Equal(t, int64(0), total)
	assert.Len(t, logs, 0)
	mockRepo.AssertExpectations(t)
}
