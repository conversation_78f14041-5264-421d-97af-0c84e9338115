package file

import (
	"backend/internal/middleware"
	"backend/internal/modules/file/controller"
	"backend/internal/modules/file/model"
	"backend/internal/modules/file/service"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Module 文件管理模块
type Module struct {
	db *gorm.DB

	// 服务
	fileService service.FileService

	// 控制器
	fileController *controller.FileController
}

// NewModule 创建文件管理模块
func NewModule(db *gorm.DB) *Module {
	return &Module{
		db: db,
	}
}

// Initialize 初始化模块
func (m *Module) Initialize() error {
	// 初始化服务
	m.fileService = service.NewFileService(m.db)

	// 初始化控制器
	m.fileController = controller.NewFileController(m.fileService)

	return nil
}

// RegisterRoutes 注册路由
func (m *Module) RegisterRoutes(router *gin.RouterGroup) {
	// 需要认证的路由
	fileAPI := router.Group("/file")
	fileAPI.Use(middleware.AuthMiddleware())
	{
		fileAPI.POST("/upload", m.fileController.UploadFile)
		fileAPI.POST("/upload/batch", m.fileController.UploadFiles)
		fileAPI.GET("/list-by-module", m.fileController.GetFilesByModule)
		fileAPI.DELETE("/:id", m.fileController.DeleteFile)
	}

	// 不需要认证的路由（供文件访问）
	fileView := router.Group("/file")
	{
		fileView.GET("/view/:filename", m.fileController.ViewFile)
		fileView.GET("/thumbnail/:filename", m.fileController.ViewThumbnail)
	}
}

// AutoMigrate 自动迁移数据库
func (m *Module) AutoMigrate() error {
	return m.db.AutoMigrate(
		&model.File{},
	)
}

// GetFileService 获取文件服务实例，供其他模块使用
func (m *Module) GetFileService() service.FileService {
	return m.fileService
}
