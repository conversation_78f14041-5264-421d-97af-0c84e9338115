package controller

import (
	"backend/internal/modules/file/model"
	"backend/internal/modules/file/service"
	"backend/response"
	"net/http"
	"os"
	"path/filepath"
	"strconv"

	"github.com/gin-gonic/gin"
)

// FileController 文件控制器
type FileController struct {
	service service.FileService
}

// NewFileController 创建文件控制器实例
func NewFileController(service service.FileService) *FileController {
	return &FileController{service: service}
}

// UploadFile 上传单个文件
// @Summary 上传单个文件
// @Description 上传单个文件并返回文件信息
// @Tags 文件管理
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "文件"
// @Param module_type formData string false "关联模块类型"
// @Param module_id formData int false "关联模块ID"
// @Param description formData string false "文件描述"
// @Success 200 {object} response.ResponseStruct{data=model.FileResponse} "上传成功"
// @Failure 400 {object} response.ResponseStruct "请求错误"
// @Failure 500 {object} response.ResponseStruct "服务器错误"
// @Router /file/upload [post]
// @Security BearerAuth
func (c *FileController) UploadFile(ctx *gin.Context) {
	// 获取上传文件
	file, err := ctx.FormFile("file")
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取上传文件失败: "+err.Error())
		return
	}

	// 获取关联参数
	moduleType := ctx.PostForm("module_type")
	moduleIDStr := ctx.PostForm("module_id")
	description := ctx.PostForm("description")

	// 处理模块ID
	var moduleID uint = 0
	if moduleIDStr != "" {
		id, err := strconv.ParseUint(moduleIDStr, 10, 32)
		if err == nil {
			moduleID = uint(id)
		}
	}

	// 调用服务上传文件
	result, err := c.service.UploadFile(ctx, file, moduleType, moduleID, description)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "上传文件失败: "+err.Error())
		return
	}

	response.Success(ctx, result, "上传成功")
}

// UploadFiles 批量上传文件
// @Summary 批量上传文件
// @Description 批量上传多个文件并返回文件信息
// @Tags 文件管理
// @Accept multipart/form-data
// @Produce json
// @Param files formData file true "多个文件" collectionFormat=multi
// @Param module_type formData string false "关联模块类型"
// @Param module_id formData int false "关联模块ID"
// @Param description formData string false "文件描述"
// @Success 200 {object} response.ResponseStruct{data=model.FileBatchResponse} "上传成功"
// @Failure 400 {object} response.ResponseStruct "请求错误"
// @Failure 500 {object} response.ResponseStruct "服务器错误"
// @Router /file/upload/batch [post]
// @Security BearerAuth
func (c *FileController) UploadFiles(ctx *gin.Context) {
	// 获取表单文件
	form, err := ctx.MultipartForm()
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取上传文件失败: "+err.Error())
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		response.Fail(ctx, http.StatusBadRequest, "未选择任何文件")
		return
	}

	// 获取关联参数
	moduleType := ctx.PostForm("module_type")
	moduleIDStr := ctx.PostForm("module_id")
	description := ctx.PostForm("description")

	// 处理模块ID
	var moduleID uint = 0
	if moduleIDStr != "" {
		id, err := strconv.ParseUint(moduleIDStr, 10, 32)
		if err == nil {
			moduleID = uint(id)
		}
	}

	// 调用服务批量上传文件
	result, err := c.service.UploadFiles(ctx, files, moduleType, moduleID, description)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "批量上传文件失败: "+err.Error())
		return
	}

	response.Success(ctx, result, "上传成功")
}

// ViewFile 查看/下载文件
// @Summary 查看或下载文件
// @Description 根据文件名查看或下载文件
// @Tags 文件管理
// @Produce octet-stream
// @Param filename path string true "文件名"
// @Success 200 {file} binary "文件内容"
// @Failure 404 {object} response.ResponseStruct "文件不存在"
// @Router /file/view/{filename} [get]
func (c *FileController) ViewFile(ctx *gin.Context) {
	filename := ctx.Param("filename")

	// 首先尝试从数据库查找文件记录
	var file model.File
	if err := c.service.GetFileByFilename(filename, &file); err == nil {
		// 如果找到文件记录，直接使用存储路径
		if _, err := os.Stat(file.StoragePath); err == nil {
			ctx.File(file.StoragePath)
			return
		}
	}

	// 如果数据库中没有找到或文件不存在，则在各目录中查找
	baseDirs := []string{
		service.ImageDir,
		service.DocDir,
		service.OtherDir,
		service.WorkflowDir,
	}

	// 遍历基础目录
	for _, baseDir := range baseDirs {
		// 首先检查基础目录
		path := filepath.Join(baseDir, filename)
		if _, err := os.Stat(path); err == nil {
			ctx.File(path)
			return
		}

		// 然后遍历该基础目录下的所有子目录
		if entries, err := os.ReadDir(baseDir); err == nil {
			for _, entry := range entries {
				if entry.IsDir() {
					subDir := filepath.Join(baseDir, entry.Name())
					path := filepath.Join(subDir, filename)
					if _, err := os.Stat(path); err == nil {
						ctx.File(path)
						return
					}
				}
			}
		}
	}

	// 如果所有目录都没找到，返回404
	response.Fail(ctx, http.StatusNotFound, "文件不存在")
}

// ViewThumbnail 查看缩略图
// @Summary 查看图片缩略图
// @Description 根据缩略图文件名查看缩略图
// @Tags 文件管理
// @Produce image/*
// @Param filename path string true "缩略图文件名"
// @Success 200 {file} binary "缩略图内容"
// @Failure 404 {object} response.ResponseStruct "缩略图不存在"
// @Router /file/thumbnail/{filename} [get]
func (c *FileController) ViewThumbnail(ctx *gin.Context) {
	filename := ctx.Param("filename")

	thumbnailPath := filepath.Join(service.ThumbnailDir, filename)

	if _, err := os.Stat(thumbnailPath); os.IsNotExist(err) {
		response.Fail(ctx, http.StatusNotFound, "缩略图不存在")
		return
	}

	// 提供缩略图
	ctx.File(thumbnailPath)
}

// GetFilesByModule 获取模块关联的文件列表
// @Summary 获取模块关联的文件列表
// @Description 根据模块类型和ID获取关联的文件列表
// @Tags 文件管理
// @Produce json
// @Param module_type query string true "模块类型"
// @Param module_id query int true "模块ID"
// @Success 200 {object} response.ResponseStruct{data=[]model.File} "文件列表"
// @Failure 400 {object} response.ResponseStruct "请求参数错误"
// @Failure 500 {object} response.ResponseStruct "服务器错误"
// @Router /file/list-by-module [get]
// @Security BearerAuth
func (c *FileController) GetFilesByModule(ctx *gin.Context) {
	moduleType := ctx.Query("module_type")
	moduleIDStr := ctx.Query("module_id")

	if moduleType == "" {
		response.Fail(ctx, http.StatusBadRequest, "模块类型不能为空")
		return
	}

	if moduleIDStr == "" {
		response.Fail(ctx, http.StatusBadRequest, "模块ID不能为空")
		return
	}

	moduleID, err := strconv.ParseUint(moduleIDStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的模块ID")
		return
	}

	files, err := c.service.GetFilesByModule(moduleType, uint(moduleID))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取文件列表失败: "+err.Error())
		return
	}

	response.Success(ctx, files, "获取文件列表成功")
}

// DeleteFile 删除文件
// @Summary 删除文件
// @Description 删除指定ID的文件
// @Tags 文件管理
// @Produce json
// @Param id path int true "文件ID"
// @Success 200 {object} response.ResponseStruct "删除成功"
// @Failure 400 {object} response.ResponseStruct "请求参数错误"
// @Failure 403 {object} response.ResponseStruct "无权限"
// @Failure 500 {object} response.ResponseStruct "服务器错误"
// @Router /file/{id} [delete]
// @Security BearerAuth
func (c *FileController) DeleteFile(ctx *gin.Context) {
	idStr := ctx.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的文件ID")
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("userID")
	if !exists {
		response.Fail(ctx, http.StatusForbidden, "用户未认证")
		return
	}

	// 安全地进行类型断言
	userIDValue, ok := userID.(uint)
	if !ok {
		response.Fail(ctx, http.StatusInternalServerError, "用户ID类型错误")
		return
	}

	err = c.service.DeleteFile(uint(id), userIDValue)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "删除文件失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "文件已删除")
}
