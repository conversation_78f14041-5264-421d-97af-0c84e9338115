package audit

import (
	"backend/internal/middleware"
	"backend/internal/modules/audit/controller"
	"backend/internal/modules/audit/hooks"
	audit "backend/internal/modules/audit/model"
	"backend/internal/modules/audit/service"
	"context"
	"log"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// RegisterAuditModule 注册审计模块
func RegisterAuditModule(router *gin.RouterGroup, db *gorm.DB, logger *zap.Logger) {
	// 执行数据库迁移
	if err := autoMigrate(db); err != nil {
		log.Printf("审计模块数据库迁移失败: %v", err)
	} else {
		log.Println("审计模块数据库迁移成功")
	}

	// 初始化服务
	operationLogService := service.NewOperationLogService(db, logger)

	// 初始化控制器
	operationLogController := controller.NewOperationLogController(operationLogService)

	// 注册路由
	auditGroup := router.Group("/audit")
	auditGroup.Use(middleware.AuthMiddleware()) // 添加认证中间件

	// 操作日志路由
	auditGroup.GET("/operation-logs", operationLogController.List)

	// 注册GORM钩子
	auditManager := hooks.NewAuditManager(db)
	auditManager.RegisterHooks()

	// 启动日志清理定时任务
	startLogCleanupTask(operationLogService, logger)
}

// startLogCleanupTask 启动日志清理定时任务
// 每天凌晨3点清理超过15天的操作日志
func startLogCleanupTask(logService service.OperationLogService, logger *zap.Logger) {
	const retentionDays = 15 // 保留日志的天数

	go func() {
		logger.Info("启动操作日志清理定时任务", zap.Int("保留天数", retentionDays))

		// 计算第一次执行的延迟时间，确保在凌晨3点执行
		now := time.Now()
		next := time.Date(now.Year(), now.Month(), now.Day(), 3, 0, 0, 0, now.Location())
		if now.After(next) {
			next = next.Add(24 * time.Hour)
		}

		initialDelay := next.Sub(now)
		logger.Info("操作日志清理任务将在以下时间首次执行",
			zap.Duration("等待时间", initialDelay),
			zap.Time("执行时间", next))

		// 等待到第一次执行时间
		time.Sleep(initialDelay)

		// 首次执行
		executeLogCleanup(logService, logger, retentionDays)

		// 之后每24小时执行一次
		ticker := time.NewTicker(24 * time.Hour)
		defer ticker.Stop()

		for range ticker.C {
			executeLogCleanup(logService, logger, retentionDays)
		}
	}()
}

// executeLogCleanup 执行日志清理
func executeLogCleanup(logService service.OperationLogService, logger *zap.Logger, retentionDays int) {
	logger.Info("开始执行操作日志清理", zap.Int("保留天数", retentionDays))

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	count, err := logService.CleanupLogs(ctx, retentionDays)
	if err != nil {
		logger.Error("操作日志清理失败",
			zap.Error(err),
			zap.Int("保留天数", retentionDays))
		return
	}

	logger.Info("操作日志清理完成",
		zap.Int64("清理记录数", count),
		zap.Int("保留天数", retentionDays))
}

// autoMigrate 自动迁移数据库表结构
func autoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(&audit.OperationLog{})
}

// Module 审计模块
type Module struct {
	db     *gorm.DB
	logger *zap.Logger

	// 服务
	operationLogService service.OperationLogService

	// 控制器
	operationLogController *controller.OperationLogController

	// 审计管理器
	auditManager *hooks.AuditManager
}

// NewModule 创建审计模块
func NewModule(db *gorm.DB) *Module {
	return &Module{
		db: db,
	}
}

// Initialize 初始化模块
func (m *Module) Initialize() error {
	// 获取默认logger
	m.logger = zap.L()

	// 初始化服务
	m.operationLogService = service.NewOperationLogService(m.db, m.logger)

	// 初始化控制器
	m.operationLogController = controller.NewOperationLogController(m.operationLogService)

	// 初始化审计管理器并注册钩子
	m.auditManager = hooks.NewAuditManager(m.db)
	m.auditManager.RegisterHooks()

	// 启动日志清理定时任务
	startLogCleanupTask(m.operationLogService, m.logger)

	return nil
}

// AutoMigrate 执行数据库迁移
func (m *Module) AutoMigrate() error {
	return autoMigrate(m.db)
}

// RegisterRoutes 注册路由
func (m *Module) RegisterRoutes(router *gin.RouterGroup) {
	auditGroup := router.Group("/audit")
	auditGroup.Use(middleware.AuthMiddleware()) // 添加认证中间件

	// 操作日志路由
	auditGroup.GET("/operation-logs", m.operationLogController.List)
}
