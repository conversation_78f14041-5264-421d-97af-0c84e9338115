package service

import (
	audit "backend/internal/modules/audit/model"
	"context"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// OperationLogService 操作日志服务接口
type OperationLogService interface {
	// 记录操作日志
	CreateLog(ctx context.Context, log *audit.OperationLog) error

	// 查询操作日志
	GetLogs(ctx context.Context, module, operation, username, requestMethod string, startTime, endTime time.Time, page, pageSize int) ([]*audit.OperationLog, int64, error)

	// 清理过期日志
	CleanupLogs(ctx context.Context, retentionDays int) (int64, error)
}

// operationLogService 操作日志服务实现
type operationLogService struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewOperationLogService 创建操作日志服务
func NewOperationLogService(db *gorm.DB, logger *zap.Logger) OperationLogService {
	return &operationLogService{
		db:     db,
		logger: logger,
	}
}

// CreateLog 创建操作日志
func (s *operationLogService) CreateLog(ctx context.Context, log *audit.OperationLog) error {
	// 使用SkipHooks选项，确保创建日志时不会触发钩子，避免循环记录
	return s.db.WithContext(ctx).Session(&gorm.Session{
		SkipHooks: true, // 跳过所有钩子
	}).Create(log).Error
}

// GetLogs 查询操作日志
func (s *operationLogService) GetLogs(ctx context.Context, module, operation, username, requestMethod string, startTime, endTime time.Time, page, pageSize int) ([]*audit.OperationLog, int64, error) {
	var logs []*audit.OperationLog
	var total int64

	db := s.db.WithContext(ctx).Model(&audit.OperationLog{})

	if module != "" {
		db = db.Where("module = ?", module)
	}

	if operation != "" {
		db = db.Where("operation = ?", operation)
	}

	if username != "" {
		db = db.Where("username = ?", username)
	}

	if requestMethod != "" {
		db = db.Where("request_method = ?", requestMethod)
	}

	if !startTime.IsZero() {
		db = db.Where("operation_time >= ?", startTime)
	}

	if !endTime.IsZero() {
		db = db.Where("operation_time <= ?", endTime)
	}

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		db = db.Offset(offset).Limit(pageSize)
	}

	// 按操作时间倒序排列
	db = db.Order("operation_time DESC")

	if err := db.Find(&logs).Error; err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// CleanupLogs 清理超过指定天数的日志
// 返回已删除的记录数和可能的错误
func (s *operationLogService) CleanupLogs(ctx context.Context, retentionDays int) (int64, error) {
	if retentionDays <= 0 {
		retentionDays = 30 // 默认保留30天
	}

	// GET请求保留30天，其他请求保留90天
	getRequestRetention := retentionDays
	otherRequestRetention := 90 // 其他请求默认保留90天

	// 计算GET请求截止日期
	getCutoffDate := time.Now().AddDate(0, 0, -getRequestRetention)

	// 计算其他请求截止日期
	otherCutoffDate := time.Now().AddDate(0, 0, -otherRequestRetention)

	// 使用硬删除清理GET请求的旧日志
	getResult := s.db.WithContext(ctx).
		Session(&gorm.Session{SkipHooks: true}). // 跳过钩子避免触发审计记录
		Unscoped().                              // 添加Unscoped()执行物理删除
		Where("operation_time < ? AND request_method = ?", getCutoffDate, "GET").
		Delete(&audit.OperationLog{})

	// 使用硬删除清理其他请求的旧日志
	otherResult := s.db.WithContext(ctx).
		Session(&gorm.Session{SkipHooks: true}). // 跳过钩子避免触发审计记录
		Unscoped().                              // 添加Unscoped()执行物理删除
		Where("operation_time < ? AND request_method != ?", otherCutoffDate, "GET").
		Delete(&audit.OperationLog{})

	// 合并错误和删除记录数
	var totalDeleted int64
	var err error = nil

	if getResult.Error != nil {
		err = getResult.Error
	}

	if otherResult.Error != nil && err == nil {
		err = otherResult.Error
	}

	totalDeleted = getResult.RowsAffected + otherResult.RowsAffected

	if err != nil {
		if s.logger != nil {
			s.logger.Error("清理操作日志失败",
				zap.Error(err),
				zap.Time("GET请求截止日期", getCutoffDate),
				zap.Time("其他请求截止日期", otherCutoffDate),
				zap.Int("GET请求保留天数", getRequestRetention),
				zap.Int("其他请求保留天数", otherRequestRetention))
		}
		return totalDeleted, err
	}

	if s.logger != nil {
		s.logger.Info("操作日志清理完成",
			zap.Int64("总删除记录数", totalDeleted),
			zap.Int64("GET请求删除记录数", getResult.RowsAffected),
			zap.Int64("其他请求删除记录数", otherResult.RowsAffected),
			zap.Time("GET请求截止日期", getCutoffDate),
			zap.Time("其他请求截止日期", otherCutoffDate),
			zap.Int("GET请求保留天数", getRequestRetention),
			zap.Int("其他请求保留天数", otherRequestRetention))
	}

	return totalDeleted, nil
}
