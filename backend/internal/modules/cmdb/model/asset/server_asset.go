package asset

import (
	"time"

	"backend/internal/modules/cmdb/model/template"

	"gorm.io/gorm"
)

// ServerAsset 服务器资产扩展表
type ServerAsset struct {
	ID         uint                     `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt  time.Time                `json:"created_at" example:"2023-01-01T00:00:00Z"`
	UpdatedAt  time.Time                `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	DeletedAt  gorm.DeletedAt           `json:"-" gorm:"index"`
	AssetID    uint                     `json:"asset_id" gorm:"not null;unique;comment:资产ID" example:"1"`
	Asset      Device                   `json:"asset" gorm:"foreignKey:AssetID"`
	TemplateID uint                     `json:"template_id" gorm:"comment:套餐模板ID" example:"1"`
	Template   template.MachineTemplate `json:"template" gorm:"foreignKey:TemplateID"`
	IP         string                   `json:"ip" gorm:"type:varchar(50);comment:IP地址" example:"*************"`
	OS         string                   `json:"os" gorm:"type:varchar(100);comment:操作系统" example:"CentOS 7.9"`
}

// TableName 指定表名
func (ServerAsset) TableName() string {
	return "server_assets"
}
