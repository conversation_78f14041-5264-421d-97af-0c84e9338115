package asset

import (
	"backend/internal/modules/cmdb/model/location"
	"time"

	"gorm.io/gorm"
)

// Warehouse 仓库模型
type Warehouse struct {
	ID          uint           `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt   time.Time      `json:"created_at" example:"2023-01-01T00:00:00Z"`
	UpdatedAt   time.Time      `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	Name        string         `json:"name" gorm:"type:varchar(100);comment:仓库名称" example:"主数据中心备件仓"`
	Code        string         `json:"code" gorm:"type:varchar(50);uniqueIndex;comment:仓库编码" example:"WH-001"`
	Type        string         `json:"type" gorm:"type:varchar(50);comment:仓库类型" example:"备件仓"`
	RoomID      uint           `json:"room_id" gorm:"default:0;comment:房间ID" example:"1"`
	Room        *location.Room `json:"room" gorm:"foreignKey:RoomID"`
	Description string         `json:"description" gorm:"type:text;comment:描述" example:"存放CPU和内存备件"`
	Status      string         `json:"status" gorm:"type:varchar(20);default:active;comment:状态" example:"active"`
}

// TableName 指定表名
func (Warehouse) TableName() string {
	return "warehouses"
}

// WarehouseWithStats 带统计信息的仓库
type WarehouseWithStats struct {
	Warehouse
	ProductCount   int `json:"product_count"`
	TotalItems     int `json:"total_items"`
	AvailableItems int `json:"available_items"`
}
