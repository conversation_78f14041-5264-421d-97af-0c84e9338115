package asset

import (
	"backend/internal/modules/cmdb/model/template"
	"backend/pkg/utils"
	"time"

	"gorm.io/gorm"
)

// 资产状态常量
const (
	// 资产状态
	AssetStatusPendingStorage  = "pending_storage"  // 待入库
	AssetStatusInStorage       = "in_storage"       // 已入库
	AssetStatusPendingOutbound = "pending_outbound" // 待出库
	AssetStatusOutbound        = "outbound"         // 已出库
	AssetStatusIdle            = "idle"             // 闲置中
	AssetStatusInUse           = "in_use"           // 使用中
	AssetStatusMaintaining     = "maintaining"      // 维修中
	AssetStatusPendingScrap    = "pending_scrap"    // 待报废
	AssetStatusScrapped        = "scrapped"         // 已报废

	// 硬件状态
	HardwareStatusNormal = "normal"  // 正常
	HardwareStatusFaulty = "faulty"  // 故障
	HardwareStatusWaning = "warning" // 警告

	// 资产类型
	AssetTypeServer       = "server"       // 服务器
	AssetTypeGPUServer    = "gpu_server"   // GPU服务器
	AssetTypeNetwork      = "network"      // 网络设备
	AssetTypeStorage      = "storage"      // 存储设备
	AssetTypeSwitch       = "switch"       // 交换机
	AssetTypeRouter       = "router"       // 路由器
	AssetTypeFirewall     = "firewall"     // 防火墙
	AssetTypeLoadBalancer = "loadbalancer" // 负载均衡器
)

// Device 资产设备模型
type Device struct {
	ID                       uint                      `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt                time.Time                 `gorm:"column:created_at;not null" json:"created_at"`
	UpdatedAt                time.Time                 `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	DeletedAt                gorm.DeletedAt            `gorm:"index" json:"-"`
	PurchaseOrder            string                    `json:"purchaseOrder,omitempty" gorm:"type:varchar(100);comment:采购合同"`
	SN                       string                    `json:"sn" gorm:"type:varchar(100);not null;unique;comment:资产SN"`
	Brand                    string                    `json:"brand" gorm:"type:varchar(50);comment:厂商"`
	Model                    string                    `json:"model" gorm:"type:varchar(100);comment:型号"`
	PurchaseDate             utils.Date                `json:"purchaseDate,omitzero" gorm:"comment:购买时间"`
	WarrantyExpire           utils.Date                `json:"warrantyExpire,omitzero" gorm:"comment:过保时间"`
	AssetStatus              string                    `json:"assetStatus" gorm:"type:varchar(20);default:pending_storage;comment:资产状态"`
	HardwareStatus           string                    `json:"hardwareStatus" gorm:"type:varchar(20);default:normal;comment:硬件状态"`
	Price                    float64                   `json:"price,omitempty" gorm:"comment:金额"`
	ResidualValue            float64                   `json:"residualValue,omitempty" gorm:"comment:残值"`
	AssetType                string                    `json:"assetType" gorm:"type:varchar(50);not null;comment:资产类型(server:服务器,gpu_server:GPU服务器,network:网络设备,storage:存储设备)"`
	Remark                   string                    `json:"remark,omitempty" gorm:"type:text;comment:备注"`
	LastStatusChange         utils.Date                `json:"lastStatusChange" gorm:"comment:最后状态变更时间"`
	LastHardwareStatusChange utils.Date                `json:"lastHardwareStatusChange" gorm:"comment:最后硬件状态变更时间"`
	Resource                 *Resource                 `json:"resource" gorm:"foreignKey:AssetID;references:ID;constraint:OnDelete:CASCADE"`
	ProductID                uint                      `json:"productID" gorm:"comment:关联的产品ID;default:null"`
	TemplateID               uint                      `json:"templateID" gorm:"comment:关联的套餐模板ID;default:null"`
	Template                 *template.MachineTemplate `json:"template" gorm:"foreignKey:TemplateID;references:ID"`
	NetworkDevice            *NetworkDevice            `json:"networkDevice" gorm:"foreignKey:DeviceID"`
}

// TableName 指定表名
func (Device) TableName() string {
	return "asset_devices"
}

// IsValidAssetStatus 检查资产状态是否有效
func IsValidAssetStatus(status string) bool {
	validStatuses := []string{
		AssetStatusPendingStorage,
		AssetStatusInStorage,
		AssetStatusPendingOutbound,
		AssetStatusOutbound,
		AssetStatusIdle,
		AssetStatusInUse,
		AssetStatusMaintaining,
		AssetStatusPendingScrap,
		AssetStatusScrapped,
	}

	for _, s := range validStatuses {
		if s == status {
			return true
		}
	}
	return false
}

// IsValidAssetType 检查资产类型是否有效
func IsValidAssetType(assetType string) bool {
	validTypes := []string{
		AssetTypeServer,
		AssetTypeGPUServer,
		AssetTypeNetwork,
		AssetTypeStorage,
		AssetTypeSwitch,
		AssetTypeRouter,
		AssetTypeFirewall,
		AssetTypeLoadBalancer,
	}

	for _, t := range validTypes {
		if t == assetType {
			return true
		}
	}
	return false
}

// IsValidHardwareStatus 检查硬件状态是否有效
func IsValidHardwareStatus(status string) bool {
	validStatuses := []string{
		HardwareStatusNormal,
		HardwareStatusFaulty,
		HardwareStatusWaning,
	}

	for _, s := range validStatuses {
		if s == status {
			return true
		}
	}
	return false
}

// 定义请求结构
type DeviceAmountReq struct {
	TemplateID  uint   `json:"template_id" binding:"required"`
	AssetStatus string `json:"asset_status"`
	WarehouseID uint   `json:"warehouse_id" binding:"required"`
	AssetType   string `json:"asset_type"`
	Project     string `json:"project"`
}

type DeviceAmountRes struct {
	TemplateID uint   `json:"template_id"`
	Amount     int    `json:"amount"`
	Project    string `json:"project"`
}
