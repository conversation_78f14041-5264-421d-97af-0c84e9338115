package asset

import (
	"time"

	"gorm.io/gorm"
)

// StatusChangeLog 资产状态变更记录
type StatusChangeLog struct {
	ID                uint           `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt         time.Time      `json:"created_at" example:"2023-01-01T00:00:00Z"`
	UpdatedAt         time.Time      `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	DeletedAt         gorm.DeletedAt `json:"-" gorm:"index"`
	AssetID           uint           `json:"asset_id" gorm:"not null;comment:资产ID" example:"1"`
	Asset             Device         `json:"asset" gorm:"foreignKey:AssetID"`
	OldAssetStatus    string         `json:"old_asset_status" gorm:"type:varchar(20);comment:旧资产状态" example:"in_storage"`
	NewAssetStatus    string         `json:"new_asset_status" gorm:"type:varchar(20);comment:新资产状态" example:"in_use"`
	OldBizStatus      string         `json:"old_biz_status" gorm:"type:varchar(20);comment:旧业务状态" example:"maintaining"`
	NewBizStatus      string         `json:"new_biz_status" gorm:"type:varchar(20);comment:新业务状态" example:"active"`
	OldHardwareStatus string         `json:"old_hardware_status" gorm:"type:varchar(20);comment:旧硬件状态" example:"normal"`
	NewHardwareStatus string         `json:"new_hardware_status" gorm:"type:varchar(20);comment:新硬件状态" example:"faulty"`
	ChangeReason      string         `json:"change_reason" gorm:"type:text;comment:变更原因" example:"资产交付客户使用"`
	OperatorID        uint           `json:"operator_id" gorm:"comment:操作人ID" example:"1"`
	OperatorName      string         `json:"operator_name" gorm:"type:varchar(50);comment:操作人姓名" example:"张三"`
	ApproverID        uint           `json:"approver_id" gorm:"comment:审批人ID" example:"2"`
	ApproverName      string         `json:"approver_name" gorm:"type:varchar(50);comment:审批人姓名" example:"李四"`
	TicketNo          string         `json:"ticket_no" gorm:"type:varchar(50);comment:工单号" example:"TICKET-2023-001"`
	WorkflowID        string         `json:"workflow_id" gorm:"type:varchar(50);comment:工作流ID" example:"WF-2023-001"`
	Source            string         `json:"source" gorm:"type:varchar(50);comment:来源系统" example:"维修系统"`
}

// TableName 指定表名
func (StatusChangeLog) TableName() string {
	return "asset_status_change_logs"
}

type ListParams struct {
	Page       int
	PageSize   int
	StartDate  time.Time
	EndDate    time.Time
	OperatorID uint
}
