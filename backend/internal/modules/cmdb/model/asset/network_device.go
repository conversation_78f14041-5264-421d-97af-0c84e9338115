package asset

import (
	"time"

	"gorm.io/gorm"
)

// 网络设备角色类型常量
const (
	// 网络设备角色
	NetworkRoleS0  = "S0"  // 核心交换机角色S0
	NetworkRoleS1  = "S1"  // 核心交换机角色S1
	NetworkRoleGS0 = "GS0" // 汇聚交换机角色GS0
	NetworkRoleGS1 = "GS1" // 汇聚交换机角色GS1
	NetworkRoleRGW = "RGW" // 路由网关
	NetworkRoleRSW = "RSW" // 路由交换机
	NetworkRoleDAS = "DAS" // 直连存储交换机
	NetworkRoleMS0 = "MS0" // 管理交换机MS0
	NetworkRoleMS1 = "MS1" // 管理交换机MS1
	NetworkRoleBS0 = "BS0" // 边界交换机BS0
	NetworkRoleBS1 = "BS1" // 边界交换机BS1
	NetworkRoleDCI = "DCI" // 数据中心互联
)

// 角色与套餐模板名称的映射关系
var NetworkRoleToPackageMap = map[string]string{
	NetworkRoleS0:  "8Q-TOR-T3-B",
	NetworkRoleS1:  "32Q-TOR-T3-B",
	NetworkRoleGS0: "COREB-TH5",
	NetworkRoleGS1: "COREB-TH5",
	NetworkRoleRGW: "TOR-P4-6.4T",
	NetworkRoleRSW: "COREB-TH3",
	NetworkRoleDAS: "COREC-Macsec-2",
	NetworkRoleMS0: "TOR-1G",
	NetworkRoleMS1: "TOR-1G",
	NetworkRoleBS0: "TOR-1G",
	NetworkRoleBS1: "TOR-1G",
	NetworkRoleDCI: "COREC-Macsec-1",
}

// NetworkDevice 网络设备模型
type NetworkDevice struct {
	ID        uint           `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt time.Time      `gorm:"column:created_at;not null" json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联设备ID (外键)
	DeviceID uint    `json:"deviceID" gorm:"column:device_id;not null;uniqueIndex;comment:关联的设备ID"`
	Device   *Device `json:"device,omitempty" gorm:"foreignKey:DeviceID"`

	// 网络设备特有属性
	Role              string `json:"role" gorm:"type:varchar(20);comment:设备角色(S0/S1/GS0等)"`
	FirmwareVersion   string `json:"firmwareVersion" gorm:"type:varchar(100);comment:固件版本"`
	LoopbackAddress   string `json:"loopbackAddress" gorm:"type:varchar(50);comment:Loopback地址"`
	ManagementAddress string `json:"managementAddress" gorm:"type:varchar(50);comment:管理地址"`

	// 其他网络设备特有属性
	Ports            int    `json:"ports" gorm:"comment:端口数量"`
	PortSpeed        string `json:"portSpeed" gorm:"type:varchar(50);comment:端口速率"`
	StackSupport     bool   `json:"stackSupport" gorm:"comment:是否支持堆叠"`
	StackID          int    `json:"stackID" gorm:"comment:堆叠ID"`
	StackRole        string `json:"stackRole" gorm:"type:varchar(20);comment:在堆叠中的角色"`
	Layer            int    `json:"layer" gorm:"comment:网络层级(2/3)"`
	RoutingProtocols string `json:"routingProtocols" gorm:"type:varchar(255);comment:支持的路由协议"`
}

// TableName 指定表名
func (NetworkDevice) TableName() string {
	return "network_devices"
}

// 网络设备用户界面显示信息
type NetworkDeviceWithDeviceInfo struct {
	// 网络设备信息
	ID                uint      `json:"id"`
	Role              string    `json:"role"`
	FirmwareVersion   string    `json:"firmwareVersion"`
	LoopbackAddress   string    `json:"loopbackAddress"`
	ManagementAddress string    `json:"managementAddress"`
	Ports             int       `json:"ports"`
	PortSpeed         string    `json:"portSpeed"`
	StackSupport      bool      `json:"stackSupport"`
	StackID           int       `json:"stackID"`
	StackRole         string    `json:"stackRole"`
	Layer             int       `json:"layer"`
	RoutingProtocols  string    `json:"routingProtocols"`
	CreatedAt         time.Time `json:"createdAt"`
	UpdatedAt         time.Time `json:"updatedAt"`

	// 基本设备信息
	DeviceID       uint   `json:"deviceID"`
	SN             string `json:"sn"`
	Brand          string `json:"brand"`
	Model          string `json:"model"`
	AssetType      string `json:"assetType"`
	AssetStatus    string `json:"assetStatus"`
	HardwareStatus string `json:"hardwareStatus"`
	TemplateName   string `json:"templateName"` // 添加套餐模板名称
	Remark         string `json:"remark"`

	// 财务信息
	PurchaseOrder  string  `json:"purchaseOrder"`
	PurchaseDate   string  `json:"purchaseDate"`
	WarrantyExpire string  `json:"warrantyExpire"`
	Price          float64 `json:"price"`
	ResidualValue  float64 `json:"residualValue"`

	// 资源信息
	ResourceID     uint   `json:"resourceID"`
	Project        string `json:"project"`
	CabinetID      uint   `json:"cabinetID"`
	CabinetName    string `json:"cabinetName"`
	RackPosition   int    `json:"rackPosition"`
	UnitHeight     int    `json:"unitHeight"`
	BizStatus      string `json:"bizStatus"`
	ResStatus      string `json:"resStatus"`
	RackingTime    string `json:"rackingTime"`
	DeliveryTime   string `json:"deliveryTime"`
	BmcIP          string `json:"bmcIP"`
	VpcIP          string `json:"vpcIP"`
	Cluster        string `json:"cluster"`
	IsBackup       bool   `json:"isBackup"`
	ResourceRemark string `json:"resourceRemark"`

	// 位置信息
	RoomID            uint   `json:"roomID"`
	RoomName          string `json:"roomName"`
	DataCenterID      uint   `json:"dataCenterID"`
	DataCenterName    string `json:"dataCenterName"`
	DataCenterAddress string `json:"dataCenterAddress"`
	AZID              uint   `json:"azID"`
	AZName            string `json:"azName"`
	RegionID          uint   `json:"regionID"`
	RegionName        string `json:"regionName"`

	// 机柜信息
	CabinetRow         string  `json:"cabinetRow"`
	CabinetColumn      string  `json:"cabinetColumn"`
	CabinetType        string  `json:"cabinetType"`
	NetworkEnvironment string  `json:"networkEnvironment"`
	BondType           string  `json:"bondType"`
	TotalPower         float64 `json:"totalPower"`

	// 套餐模板信息
	TemplateID     uint   `json:"templateID"`
	CPUModel       string `json:"cpuModel"`
	MemoryCapacity int    `json:"memoryCapacity"`
	GPUModel       string `json:"gpuModel"`
	DiskType       string `json:"diskType"`
}

// 判断角色是否有效
func IsValidNetworkRole(role string) bool {
	validRoles := []string{
		NetworkRoleS0,
		NetworkRoleS1,
		NetworkRoleGS0,
		NetworkRoleGS1,
		NetworkRoleRGW,
		NetworkRoleRSW,
		NetworkRoleDAS,
		NetworkRoleMS0,
		NetworkRoleMS1,
		NetworkRoleBS0,
		NetworkRoleBS1,
		NetworkRoleDCI,
	}

	for _, r := range validRoles {
		if r == role {
			return true
		}
	}
	return false
}

// 根据角色获取对应的套餐模板名称
func GetPackageNameByRole(role string) string {
	if packageName, exists := NetworkRoleToPackageMap[role]; exists {
		return packageName
	}
	return ""
}
