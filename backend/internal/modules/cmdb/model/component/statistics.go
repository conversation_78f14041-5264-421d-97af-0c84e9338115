package component

// ComponentStatistics 组件统计信息
type ComponentStatistics struct {
	TotalComponents int                 `json:"total_components"`
	ByType          map[string]int      `json:"by_type"`
	ByStatus        map[string]int      `json:"by_status"`
	ByServer        map[string]int      `json:"by_server"`
	Inventory       InventoryStatistics `json:"inventory"`
}

// InventoryStatistics 库存统计信息
type InventoryStatistics struct {
	TotalStock     int `json:"total_stock"`
	InUseCount     int `json:"in_use_count"`
	IdleCount      int `json:"idle_count"`
	GoodCount      int `json:"good_count"`
	DefectCount    int `json:"defect_count"`
	AllocatedStock int `json:"allocated_stock"` // 已分配库存
	AllocatedCount int `json:"allocated_count"` // 已分配数量
}

// ServerComponentWithDetails 服务器组件详情，包含关联信息
type ServerComponentWithDetails struct {
	ServerComponent
	ServerInfo      ServerInfo      `json:"server_info,omitempty"`
	InventoryDetail InventoryDetail `json:"inventory_detail"`
}

// ServerInfo 服务器信息
type ServerInfo struct {
	SN    string `json:"sn"`
	Brand string `json:"brand"`
	Model string `json:"model"`
}

// InventoryDetail 库存详情
type InventoryDetail struct {
	TotalStock  int `json:"total_stock"`
	InUseCount  int `json:"in_use_count"`
	IdleCount   int `json:"idle_count"`
	GoodCount   int `json:"good_count"`
	DefectCount int `json:"defect_count"`
}
