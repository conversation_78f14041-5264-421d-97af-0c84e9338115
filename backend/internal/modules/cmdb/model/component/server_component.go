package component

import (
	"backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/cmdb/model/product"
	"backend/pkg/utils"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// ServerComponent 服务器组件模型
type ServerComponent struct {
	ID                  uint            `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt           time.Time       `json:"created_at" example:"2023-01-01T00:00:00Z"`
	UpdatedAt           time.Time       `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	DeletedAt           gorm.DeletedAt  `json:"-" gorm:"index"`
	ServerID            uint            `json:"server_id" gorm:"not null;comment:服务器ID" example:"1"`
	Server              asset.Device    `json:"server" gorm:"foreignKey:ServerID"`
	ProductID           uint            `json:"product_id" gorm:"comment:产品ID" example:"1"`
	Product             product.Product `json:"product" gorm:"foreignKey:ProductID"`
	ComponentType       string          `json:"component_type" gorm:"type:varchar(50);not null;comment:组件类型" example:"CPU"`
	SN                  string          `json:"sn" gorm:"type:varchar(150);uniqueIndex;comment:序列号" example:"SN123456789"`
	Model               string          `json:"model" gorm:"type:varchar(100);comment:型号" example:"Intel Xeon 8280"`
	PN                  string          `json:"pn" gorm:"type:varchar(100);comment:部件号" example:"PN987654321"`
	FirmwareVersion     string          `json:"firmware_version" gorm:"type:varchar(50);comment:固件版本" example:"1.2.3"`
	SlotPosition        string          `json:"slot_position" gorm:"type:varchar(50);comment:安装槽位" example:"CPU1"`
	InstallDate         utils.Date      `json:"install_date" gorm:"comment:安装日期" example:"2023-01-01T00:00:00Z"`
	Status              string          `json:"status" gorm:"type:varchar(20);default:normal;comment:状态" example:"normal"`
	Description         string          `json:"description" gorm:"type:text;comment:描述" example:"主处理器"`
	ExtraInfo           datatypes.JSON  `json:"extra_info" gorm:"type:json;comment:额外信息" example:"{\"cores\":64,\"threads\":128}"`
	IsActive            bool            `json:"is_active" gorm:"default:true;comment:是否当前使用中" example:"true"`
	PreviousComponentID uint            `json:"previous_component_id" gorm:"default:0;comment:前一个组件ID" example:"0"`
	SpareID             uint            `json:"spare_id" gorm:"default:0;comment:备件ID" example:"0"`
	ReplacedAt          *time.Time      `json:"replaced_at" gorm:"comment:替换时间" example:"2023-06-01T00:00:00Z"`
}

// TableName 指定表名
func (ServerComponent) TableName() string {
	return "server_components"
}

// SlotInfo 槽位信息结构
type SlotInfo struct {
	Slot       string `json:"slot"`        // 槽位标识
	Position   string `json:"position"`    // 物理位置
	Status     string `json:"status"`      // 槽位状态
	IsOccupied bool   `json:"is_occupied"` // 是否被占用
}
