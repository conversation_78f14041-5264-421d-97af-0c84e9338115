package product

import (
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// Product 产品模型
type Product struct {
	ID              uint           `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt       time.Time      `json:"created_at" example:"2023-01-01T00:00:00Z"`
	UpdatedAt       time.Time      `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	DeletedAt       gorm.DeletedAt `json:"-" gorm:"index"`
	MaterialType    string         `json:"material_type" gorm:"type:varchar(50);comment:物料类型" example:"CPU"`
	Brand           string         `json:"brand" gorm:"type:varchar(50);comment:品牌" example:"Intel"`
	Model           string         `json:"model" gorm:"type:varchar(100);comment:型号" example:"Xeon 8280"`
	Spec            string         `json:"spec" gorm:"type:varchar(200);comment:规格" example:"28核56线程"`
	PN              string         `json:"pn" gorm:"type:varchar(100);comment:原厂PN" example:"BX806958280"`
	ProductCategory string         `json:"product_category" gorm:"type:varchar(50);comment:产品类别" example:"processor"`
	Unit            string         `json:"unit" gorm:"type:varchar(20);comment:单位" example:"台"`
	ReferencePrice  float64        `json:"reference_price" gorm:"type:decimal(10,2);comment:参考单价" example:"5000.00"`
	WarrantyPeriod  int            `json:"warranty_period" gorm:"comment:质保期(月)" example:"36"`
	SupplierIDs     string         `json:"supplier_ids" gorm:"type:text;comment:建议供应商ID列表(JSON格式)" example:"[1,2,3]"`
	Status          int8           `json:"status" gorm:"type:tinyint(1);default:1;comment:状态(1:启用,0:禁用)" example:"1"`
	Description     string         `json:"description" gorm:"type:text;comment:产品描述" example:"高性能服务器处理器"`
	CreatedBy       uint           `json:"created_by" gorm:"comment:创建人ID" example:"1"`
	UpdatedBy       uint           `json:"updated_by" gorm:"comment:更新人ID" example:"1"`
}

// TableName 指定表名
func (Product) TableName() string {
	return "products"
}

// CreateProductDTO 创建产品DTO
type CreateProductDTO struct {
	MaterialType    string  `json:"material_type" binding:"required" example:"CPU"`
	Brand           string  `json:"brand" binding:"required" example:"Intel"`
	Model           string  `json:"model" binding:"required" example:"Xeon 8280"`
	Spec            string  `json:"spec" binding:"required" example:"28核56线程"`
	PN              string  `json:"pn" binding:"required" example:"BX806958280"`
	ProductCategory string  `json:"product_category" binding:"required" example:"processor"`
	Unit            string  `json:"unit" example:"台"`
	ReferencePrice  float64 `json:"reference_price" example:"5000.00"`
	WarrantyPeriod  int     `json:"warranty_period" example:"36"`
	SupplierIDs     []uint  `json:"supplier_ids" example:"[1,2,3]"`
	Status          int8    `json:"status" example:"1"`
	Description     string  `json:"description" example:"高性能服务器处理器"`
}

// UpdateProductDTO 更新产品DTO
type UpdateProductDTO struct {
	ID              uint    `json:"id" binding:"required" example:"1"`
	MaterialType    string  `json:"material_type" binding:"required" example:"CPU"`
	Brand           string  `json:"brand" binding:"required" example:"Intel"`
	Model           string  `json:"model" binding:"required" example:"Xeon 8280"`
	Spec            string  `json:"spec" binding:"required" example:"28核56线程"`
	PN              string  `json:"pn" binding:"required" example:"BX806958280"`
	ProductCategory string  `json:"product_category" binding:"required" example:"processor"`
	Unit            string  `json:"unit" example:"台"`
	ReferencePrice  float64 `json:"reference_price" example:"5000.00"`
	WarrantyPeriod  int     `json:"warranty_period" example:"36"`
	SupplierIDs     []uint  `json:"supplier_ids" example:"[1,2,3]"`
	Status          int8    `json:"status" example:"1"`
	Description     string  `json:"description" example:"高性能服务器处理器"`
}

// ProductListQuery 产品列表查询参数
type ProductListQuery struct {
	PN              string `form:"pn" json:"pn"`
	MaterialType    string `form:"material_type" json:"material_type"`
	Brand           string `form:"brand" json:"brand"`
	Model           string `form:"model" json:"model"`
	Spec            string `form:"spec" json:"spec"`
	ProductCategory string `form:"product_category" json:"product_category"`
	Status          int8   `form:"status" json:"status"`
	Page            int    `form:"page" json:"page" binding:"required,min=1"`
	PageSize        int    `form:"pageSize" json:"pageSize" binding:"required,min=1,max=100"`
}

// ProductListResult 产品列表结果
type ProductListResult struct {
	Total int64      `json:"total"`
	Items []*Product `json:"items"`
}

// ToProduct 将DTO转换为Product模型
func (dto *CreateProductDTO) ToProduct() (*Product, error) {
	supplierIDs, err := json.Marshal(dto.SupplierIDs)
	if err != nil {
		return nil, err
	}

	return &Product{
		MaterialType:    dto.MaterialType,
		Brand:           dto.Brand,
		Model:           dto.Model,
		Spec:            dto.Spec,
		PN:              dto.PN,
		ProductCategory: dto.ProductCategory,
		Unit:            dto.Unit,
		ReferencePrice:  dto.ReferencePrice,
		WarrantyPeriod:  dto.WarrantyPeriod,
		SupplierIDs:     string(supplierIDs),
		Status:          dto.Status,
		Description:     dto.Description,
	}, nil
}

// UpdateProduct 更新Product模型
func (dto *UpdateProductDTO) UpdateProduct(p *Product) error {
	supplierIDs, err := json.Marshal(dto.SupplierIDs)
	if err != nil {
		return err
	}

	p.MaterialType = dto.MaterialType
	p.Brand = dto.Brand
	p.Model = dto.Model
	p.Spec = dto.Spec
	p.PN = dto.PN
	p.ProductCategory = dto.ProductCategory
	p.Unit = dto.Unit
	p.ReferencePrice = dto.ReferencePrice
	p.WarrantyPeriod = dto.WarrantyPeriod
	p.SupplierIDs = string(supplierIDs)
	p.Status = dto.Status
	p.Description = dto.Description

	return nil
}

// GetSupplierIDs 获取供应商ID列表
func (p *Product) GetSupplierIDs() ([]uint, error) {
	if p.SupplierIDs == "" {
		return []uint{}, nil
	}

	var supplierIDs []uint
	err := json.Unmarshal([]byte(p.SupplierIDs), &supplierIDs)
	if err != nil {
		return nil, err
	}

	return supplierIDs, nil
}

// ErrProductNotFound 产品不存在错误
var ErrProductNotFound = fmt.Errorf("产品不存在")

// ErrInvalidProductData 无效的产品数据错误
var ErrInvalidProductData = fmt.Errorf("无效的产品数据")
