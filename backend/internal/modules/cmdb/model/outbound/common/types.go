package common

import "backend/internal/common/constants"

type WorkflowInput interface {
	GetWorkflowInputType() string
}
type WorkflowSignal interface {
	GetWorkflowSignalName() string
}

type DeviceOutboundWorkflowInput struct {
	TicketID       uint   `json:"ticket_id"`
	TicketNo       string `json:"ticket_no"`
	OperatorID     uint   `json:"operator_id"`
	OperatorName   string `json:"operator_name"`
	OutboundType   string `json:"outbound_type"`
	OutboundReason string `json:"outbound_reason"`

	LaunchTicketID uint `json:"launch_ticket_id"` // 关联上架
}

func (DeviceOutboundWorkflowInput) GetWorkflowInputType() string {
	return WorkflowInputTypeDevice
}

type PartOutboundWorkflowInput struct {
	TicketID       uint   `json:"ticket_id"`
	TicketNo       string `json:"ticket_no"`
	OperatorID     uint   `json:"operator_id"`
	OperatorName   string `json:"operator_name"`
	OutboundReason string `json:"outbound_reason"`
}

func (PartOutboundWorkflowInput) GetWorkflowInputType() string {
	return WorkflowInputTypePart
}

type UpdateTicketInput struct {
	TicketID      uint   `json:"ticket_id"`
	TicketNo      string `json:"ticket_no"`
	OperatorID    uint   `json:"operator_id"`
	OperatorName  string `json:"operator_name"`
	NextStatus    string `json:"next_status"`
	CurrentStatus string `json:"current_status"`
	CurrentStage  string `json:"current_stage"`
	Stage         string `json:"stage"`
	Comments      string `json:"comments"`
}

type PartOutboundSignal struct {
	Status       string
	OperatorID   uint
	OperatorName string
	Comments     string
}

func (PartOutboundSignal) GetWorkflowSignalName() string {
	return WorkflowSignalPart
}

type DeviceOutboundSignal struct {
	Status       string
	OperatorID   uint
	OperatorName string
	Comments     string
}

func (DeviceOutboundSignal) GetWorkflowSignalName() string {
	return WorkflowSignalDevice
}

// OutboundTrigger 入库触发器
type OutboundTrigger struct {
	Stage        string                 `json:"stage"`
	Status       string                 `json:"status" binding:"required"`
	OperatorID   uint                   `json:"operator"`
	OperatorName string                 `json:"operator_name"`
	Comments     string                 `json:"comments"`
	Data         map[string]interface{} `json:"data"`
}

// MapMaterialTypeToAssetType 映射物料类型到资产类型
var MapMaterialTypeToAssetType = map[string]string{
	"GPU服务器": constants.AssetTypeGPUServer,
	"服务器":    constants.AssetTypeServer,
	"存储设备":   constants.AssetTypeStorage,
	"交换机":    constants.AssetTypeSwitch,
	"负载均衡":   constants.AssetTypeLoadbalancer,
	"防火墙":    constants.AssetTypeFirewall,
	"路由器":    constants.AssetTypeRouter,
	"其他":     constants.AssetTypeOther,
}
