package outbound

import (
	"backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/cmdb/model/product"
	"backend/internal/modules/cmdb/model/template"
	ticketModel "backend/internal/modules/ticket/model"
	"mime/multipart"
	"time"

	"gorm.io/gorm"
)

// SpareOutboundTicket 出库单模型
type SpareOutboundTicket struct {
	ID            uint           `json:"id" gorm:"primarykey"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"-" gorm:"index"`
	OutboundTitle string         `json:"outbound_title" gorm:"comment:出库标题"`
	Project       string         `json:"project" gorm:"comment:项目" binding:"required"`
	TicketNo      string         `json:"ticketNo" gorm:"unique;type:varchar(50);not null;comment:工单号"`
	AssetType     string         `json:"asset_type" gorm:"type:varchar(50);comment:资产类型"`

	// 共用 选择PN和Number  资产管理员录入spareIds
	SpareIds string `json:"spareIds" gorm:"type:varchar(255);comment:出库的备件列表"`
	//PN       string `json:"pn" gorm:"type:varchar(100);comment:原厂PN"`
	//Number   *int   `json:"number" gorm:"default:1;comment:备件数量"`
	Spares string `json:"spares" gorm:"type:varchar(2000);comment:出库的备件信息"`

	Info           []OutboundInfo   `json:"info" gorm:"comment:出库信息"`
	Details        []OutboundDetail `json:"details" gorm:"comment:出库详情"`
	OutboundType   string           `json:"outbound_type" gorm:"type:varchar(30);comment:出库类型"`
	OutboundReason string           `json:"outbound_reason" gorm:"type:text;comment:出库原因"`

	// 维修出库--配件（一个机器上可能会更换多个配件）
	RepairTicketId uint                      `json:"repair_ticket_id" gorm:"comment:关联的维修工单ID;default:null"`
	DeviceSN       string                    `json:"device_sn" gorm:"type:varchar(255);comment:服务器SN"`
	RepairTicket   *ticketModel.RepairTicket `json:"repair_ticket" gorm:"foreignKey:RepairTicketId"`

	// 改配出库--配件（多个机器改配多个配件）
	Machines string `json:"machines" gorm:"type:varchar(100);comment:改配的机器信息"`

	// 售卖出库--服务器、配件
	OrderNumber  string     `json:"order_number" gorm:"type:varchar(30);comment:合同编号"`
	BuyerInfo    string     `json:"buyer_info" gorm:"type:varchar(100);comment:买方信息"`
	ShipmentTime *time.Time `json:"shipmentTime" gorm:"comment:出货时间"`

	// 调拨出库--服务器、配件
	SourceWarehouseID uint   `json:"source_warehouse_id" gorm:"comment:原仓库ID"`
	SourceLocation    string `json:"source_location" gorm:"type:varchar(100);comment:原仓库位置"`
	DestWarehouseID   uint   `json:"dest_warehouse_id" gorm:"comment:目的位置仓库ID;default:null"`
	DestLocation      string `json:"dest_location" gorm:"type:varchar(100);comment:目的仓库具体位置"`

	OutboundTime *time.Time `json:"outbound_time" gorm:"comment:出库时间"`
	Stage        string     `json:"stage" gorm:"type:varchar(30);comment:阶段"`
	Status       string     `json:"status" gorm:"type:varchar(50);comment:状态"`
	ApprovalTime *time.Time `json:"approval_time" gorm:"comment:审批时间"`
	Remarks      string     `json:"remarks" gorm:"type:text;comment:备注"`

	ReporterID   uint   `json:"reporterID" gorm:"comment:申请人ID"`
	ReporterName string `json:"reporterName" gorm:"type:varchar(100);comment:申请人姓名"`

	CreationTime time.Time `json:"creationTime" gorm:"comment:申请时间"`

	TrackingInfo string     `json:"tracking_info" gorm:"type:text;comment:物流跟踪信息"`
	CloseTime    *time.Time `json:"closeTime" gorm:"comment:关闭时间"`
	//History      []OutboundTicketStatusHistory `json:"history" gorm:"comment:出库单历史"`

	// 工作流相关字段
	NeedsWorkflowRetry    bool       `json:"needsWorkflowRetry" gorm:"default:false;comment:是否需要重试工作流"`
	LastWorkflowRetryTime *time.Time `json:"lastWorkflowRetryTime" gorm:"comment:最后工作流重试时间"`
	WorkflowRetryCount    int        `json:"workflow_retry_count" gorm:"default:0;comment:工作流重试次数"`
	// 手动触发相关字段
	WaitingManualTrigger bool       `json:"waitingManualTrigger" gorm:"default:false;comment:是否等待手动触发"`
	CurrentWaitingStage  string     `json:"currentWaitingStage" gorm:"type:varchar(32);comment:当前等待的阶段"`
	LastWaitingTime      *time.Time `json:"lastWaitingTime" gorm:"comment:上次等待的时间"`
}

// TableName 指定表名
func (SpareOutboundTicket) TableName() string {
	return "outbound_tickets"
}

// OutboundTicketStatusHistory 出库单状态历史
type OutboundTicketStatusHistory struct {
	ID               uint           `json:"id" gorm:"primarykey"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `json:"-" gorm:"index"`
	OutboundTicketID uint           `json:"outbound_ticket_id" gorm:"comment:出库单ID"`
	Stage            string         `json:"stage" gorm:"type:varchar(30);comment:当前阶段"`
	PreviousStatus   string         `json:"previous_status" gorm:"type:varchar(50);comment:先前状态"`
	NewStatus        string         `json:"new_status" gorm:"type:varchar(50);comment:新状态"`
	OperatorID       uint           `json:"operator_id" gorm:"comment:操作人ID"`
	OperatorName     string         `json:"operator_name" gorm:"type:varchar(50);comment:操作人姓名"`
	OperationTime    time.Time      `json:"operation_time" gorm:"comment:操作时间"`
	Duration         int            `json:"duration" gorm:"comment:状态持续时间(分钟)"`
	ActivityCategory string         `json:"activity_category" gorm:"type:varchar(50);comment:活动类别"`
	Remarks          string         `json:"remarks" gorm:"type:text;comment:备注"`
}

// TableName 指定表名
func (OutboundTicketStatusHistory) TableName() string {
	return "outbound_ticket_status_histories"
}

// OutboundInfo 出库申请信息表
type OutboundInfo struct {
	ID                    uint           `json:"id" gorm:"primarykey"`
	CreatedAt             time.Time      `json:"created_at"`
	UpdatedAt             time.Time      `json:"updated_at"`
	DeletedAt             gorm.DeletedAt `json:"-" gorm:"index"`
	SpareOutboundTicketID uint           `json:"spare_outbound_ticket_id" gorm:"comment:出库单ID"`
	OutboundType          string         `json:"outbound_type" gorm:"type:varchar(50);comment:出库类型" example:"配件 ｜ 整机"`
	DeviceSN              string         `json:"device_sn" gorm:"type:varchar(50);comment:主设备SN"`
	ComponentSNs          string         `json:"component_sns" gorm:"type:varchar(50);comment:配件SN"`
	Amount                int            `json:"amount" gorm:"comment:数量"`

	// 备件信息
	ProductID    uint             `json:"product_id" gorm:"comment:规格ID;default:null"`
	Product      *product.Product `json:"product" gorm:"foreignKey:ProductID"`
	SpareAssetID uint             `json:"spare_asset_id" gorm:"comment:备件ID;default:null"`
	SpareAsset   asset.AssetSpare `json:"spare_asset" gorm:"foreignKey:SpareAssetID"`

	// 整机设备信息
	TemplateID uint                      `json:"template_id" gorm:"comment:模板ID;default:null"`
	Template   *template.MachineTemplate `json:"template" gorm:"foreignKey:TemplateID"`
	DeviceID   uint                      `json:"device_id" gorm:"comment:设备ID;default:null"`
	Device     *asset.Device             `json:"device" gorm:"foreignKey:DeviceID"`
	AssetType  string                    ` json:"asset_type" gorm:"type:varchar(50);comment:资产类型" example:"server ｜ gpu_server ｜ network_device"`
}

func (OutboundInfo) TableName() string {
	return "outbound_infos"
}

// OutboundDetail 出库申请详情
type OutboundDetail struct {
	ID                    uint             `json:"id" gorm:"primarykey"`
	CreatedAt             time.Time        `json:"created_at"`
	UpdatedAt             time.Time        `json:"updated_at"`
	DeletedAt             gorm.DeletedAt   `json:"-" gorm:"index"`
	SpareOutboundTicketID uint             `json:"spare_outbound_ticket_id" gorm:"comment:出库单ID"`
	OutboundType          string           `json:"outbound_type" gorm:"type:varchar(50);comment:出库类型" example:"配件 ｜ 服务器、网络设备"`
	ProductID             uint             `json:"product_id" gorm:"comment:规格ID;default:null"`
	Product               *product.Product `json:"product" gorm:"foreignKey:ProductID"`

	// SN
	DeviceSN    string `json:"device_sn" gorm:"type:varchar(50);comment:主设备SN"`
	ComponentSN string `json:"component_sn" gorm:"type:varchar(50);comment:配件SN"`

	// 改配
	PreComponentSN string `json:"pre_component_sn" gorm:"type:varchar(50);comment:上一个设备的SN;default:null"`

	// 已弃用
	TemplateID   uint                      `json:"template_id" gorm:"comment:模板ID;default:null"`
	Template     *template.MachineTemplate `json:"template" gorm:"foreignKey:TemplateID"`
	SpareAssetID uint                      `json:"spare_asset_id" gorm:"comment:备件ID;default:null"`
	SpareAsset   *asset.AssetSpare         `json:"spare_asset" gorm:"foreignKey:SpareAssetID"`
	DeviceID     uint                      `json:"device_id" gorm:"comment:设备ID;default:null"`
	Device       *asset.Device             `json:"device" gorm:"foreignKey:DeviceID"`
}

func (OutboundDetail) TableName() string {
	return "outbound_details"
}

// OutboundDetailImport 出库详情导入表单
type OutboundDetailImport struct {
	TemplateName string                `form:"template_name" binding:"required"`
	File         *multipart.FileHeader `form:"file" binding:"required"`
}
