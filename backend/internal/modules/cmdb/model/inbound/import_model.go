package inbound

import (
	"mime/multipart"
	"time"
)

// ProductCSVData 定义产品CSV数据结构
// 用于临时存储从CSV文件中读取的产品数据
type ProductCSVData struct {
	MaterialType    string // 物料类型
	Brand           string // 品牌
	Model           string // 型号
	Spec            string // 规格
	PN              string // 产品编号
	ProductCategory string // 产品类别
	LineNumber      int    // CSV文件中的行号（用于错误提示）
}

// SpareCSVData 定义备件CSV数据结构
// 用于临时存储从CSV文件中读取的备件数据
type SpareCSVData struct {
	SN              string    // 序列号
	PN              string    // 产品编号
	Warehouse       string    // 仓库
	SourceType      string    // 来源类型
	AssetStatus     string    // 资产状态
	HardwareStatus  string    // 硬件状态
	Price           float64   // 价格
	Code            string    // 仓库编码
	Location        string    // 存放位置
	FirmwareVersion string    // 固件版本
	PurchaseDate    time.Time // 购买时间
	WarrantyExpire  time.Time // 过保时间
	RelatedAssetSN  string    // 关联资产序列号
	Remark          string    // 备注
	LineNumber      int       // CSV文件中的行号（用于错误提示）
}

// ImportResult 定义导入结果结构
// 用于返回导入处理的详细结果
type ImportResult struct {
	Total            int                      `json:"total"`             // 总处理记录数
	Success          int                      `json:"success"`           // 成功导入数量
	Failed           int                      `json:"failed"`            // 失败数量
	InvalidPNs       []map[string]interface{} `json:"invalid_pns"`       // 无效的产品编号列表
	ErrorDetails     []map[string]interface{} `json:"error_details"`     // 错误详情
	ProductsImported int                      `json:"products_imported"` // 新导入的产品数量
	ProductsExisted  int                      `json:"products_existed"`  // 已存在的产品数量
}

type NewPartImport struct {
	Project         string                `json:"project" form:"project" binding:"required"`
	InboundTitle    string                `json:"inbound_title" form:"inbound_title" binding:"required"`
	PurchaseOrderNo string                `json:"purchase_order_no" form:"purchase_order_no" binding:"required"` //采购合同编号
	SupplierName    string                `json:"supplier_name" form:"supplier_name" binding:"required"`         // 供应商名称
	WarehouseName   string                `json:"warehouse_name" form:"warehouse_name" binding:"required"`
	WarehouseID     uint                  `json:"warehouse_id" form:"warehouse_id"`
	NewInboundInfo  []NewInboundInfo      `json:"new_info" form:"new_info"`
	TrackingInfo    string                `json:"tracking_info" form:"tracking_info"`
	MayArriveAt     string                `json:"may_arrive_at" form:"may_arrive_at"  binding:"required"`
	File            *multipart.FileHeader `form:"file" binding:"required"`
}

type NewPartDetailImport struct {
	File *multipart.FileHeader `form:"file" binding:"required"`
}
