package template

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// JSONMap 用于处理 JSON 数据，支持从 map 类型扫描
// 这个类型用于解决数据库驱动返回 map 时无法扫描到 datatypes.JSON 的问题
// 实际上，JSONMap 是 datatypes.JSON 的基础上增加了自定义 Scan 方法

type JSONMap datatypes.JSON

// Scan 实现 sql.Scanner 接口
func (j *JSONMap) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	switch v := value.(type) {
	case []byte:
		// 直接使用字节数组，不进行 base64 解码
		*j = JSONMap(v)
		return nil
	case string:
		// 直接转换字符串为字节数组，不进行 base64 解码
		*j = JSONMap([]byte(v))
		return nil
	case map[string]interface{}:
		// 将 map 转换为 JSON 字节数组
		b, err := json.Marshal(v)
		if err != nil {
			return err
		}
		*j = JSONMap(b)
		return nil
	default:
		// 尝试将其他类型转换为 JSON 字符串
		jsonData, err := json.Marshal(v)
		if err != nil {
			return errors.New("unsupported type for JSONMap: " + err.Error())
		}
		*j = JSONMap(jsonData)
		return nil
	}
}

// MarshalJSON 实现 json.Marshaler 接口
func (j JSONMap) MarshalJSON() ([]byte, error) {
	// 如果是空的 JSONMap，返回 null
	if len(j) == 0 {
		return []byte("null"), nil
	}

	// 确保数据是有效的 JSON
	var temp interface{}
	if err := json.Unmarshal([]byte(j), &temp); err != nil {
		// 如果无法解析为 JSON，则将其作为字符串返回
		return json.Marshal(string(j))
	}

	// 直接返回原始 JSON 字节
	return []byte(j), nil
}

// UnmarshalJSON 实现 json.Unmarshaler 接口
func (j *JSONMap) UnmarshalJSON(data []byte) error {
	if len(data) == 0 || string(data) == "null" {
		*j = nil
		return nil
	}

	// 验证输入是否为有效的 JSON
	var temp interface{}
	if err := json.Unmarshal(data, &temp); err != nil {
		return err
	}

	*j = JSONMap(data)
	return nil
}

// Value 实现 driver.Valuer 接口
func (j JSONMap) Value() (driver.Value, error) {
	return []byte(j), nil
}

// MachineTemplate 服务器套餐模板
type MachineTemplate struct {
	ID               uint                `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt        time.Time           `json:"created_at" example:"2023-01-01T00:00:00Z"`
	UpdatedAt        time.Time           `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	DeletedAt        gorm.DeletedAt      `gorm:"index" json:"-"`
	TemplateName     string              `json:"templateName" gorm:"type:varchar(100);not null;comment:模板名称"`
	CPUModel         string              `json:"cpuModel" gorm:"type:varchar(100);comment:CPU型号"`
	MemoryCapacity   int                 `json:"memoryCapacity" gorm:"comment:内存容量(GB)"`
	GPUModel         string              `json:"gpuModel" gorm:"type:varchar(100);comment:GPU型号"`
	DiskType         string              `json:"diskType" gorm:"type:varchar(50);comment:存储类型"`
	ComponentList    JSONMap             `json:"componentList" gorm:"type:json;comment:配件列表（冗余字段）"`
	TemplateCategory string              `json:"templateCategory" gorm:"type:varchar(50);comment:模板类别"`
	Components       []TemplateComponent `json:"components" gorm:"foreignKey:TemplateID;constraint:OnDelete:CASCADE" swaggerignore:"true"`
}

// TableName 返回表名
func (MachineTemplate) TableName() string {
	return "machine_templates"
}
