package inventory

import (
	"time"

	"gorm.io/gorm"
)

// StockHistory 库存变更历史记录
type StockHistory struct {
	gorm.Model
	DetailID     uint      `json:"detail_id" gorm:"comment:库存明细ID"`
	ProductID    uint      `json:"product_id" gorm:"comment:产品ID"`
	OldQuantity  int       `json:"old_quantity" gorm:"comment:原数量"`
	NewQuantity  int       `json:"new_quantity" gorm:"comment:新数量"`
	ChangeAmount int       `json:"change_amount" gorm:"comment:变更数量"`
	ChangeType   string    `json:"change_type" gorm:"type:varchar(20);comment:变更类型(inbound/outbound/adjust)"`
	ChangeTime   time.Time `json:"change_time" gorm:"comment:变更时间"`
	OperatorID   uint      `json:"operator_id" gorm:"comment:操作人ID"`
	Operator     string    `json:"operator" gorm:"type:varchar(50);comment:操作人"`
	Reason       string    `json:"reason" gorm:"type:text;comment:变更原因"`
	BatchNumber  string    `json:"batch_number" gorm:"type:varchar(50);comment:批次号"`
	InboundID    uint      `json:"inbound_id" gorm:"default:0;comment:新设备入库单ID"`
	OutboundID   uint      `json:"outbound_id" gorm:"default:0;comment:出库单ID"`
	WarehouseID  uint      `json:"warehouse_id" gorm:"comment:仓库ID"`
}

// TableName 指定表名
func (StockHistory) TableName() string {
	return "inventory_stock_history"
}

// StockChangeHistory 库存变更历史（兼容旧版结构，可在迁移后移除）
type StockChangeHistory struct {
	gorm.Model
	DetailID     uint      `json:"detail_id" gorm:"comment:库存明细ID"`
	ProductID    uint      `json:"product_id" gorm:"comment:产品ID"`
	OldQuantity  int       `json:"old_quantity" gorm:"comment:原数量"`
	NewQuantity  int       `json:"new_quantity" gorm:"comment:新数量"`
	ChangeAmount int       `json:"change_amount" gorm:"comment:变更数量"`
	ChangeType   string    `json:"change_type" gorm:"type:varchar(20);comment:变更类型"`
	ChangeTime   time.Time `json:"change_time" gorm:"comment:变更时间"`
	OperatorID   uint      `json:"operator_id" gorm:"comment:操作人ID"`
	Reason       string    `json:"reason" gorm:"type:text;comment:变更原因"`
}

type AssetAdjustID struct {
	OutboundID uint
	InboundID  uint
}
