// models/location/az.go
package location

import (
	"time"

	"gorm.io/gorm"
)

// AZ 可用区模型
type AZ struct {
	ID          uint           `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt   time.Time      `gorm:"column:created_at;not null" json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
	Name        string         `json:"name" gorm:"type:varchar(100);not null;comment:可用区名称"`
	RegionID    uint           `json:"regionId" gorm:"not null;comment:区域ID"`
	Region      Region         `json:"region" gorm:"foreignKey:RegionID"`
	Description string         `json:"description,omitempty" gorm:"type:text;comment:描述"`
	Status      string         `json:"status" gorm:"type:varchar(20);default:active;comment:状态(active:启用,disabled:禁用)"`
	DataCenters []DataCenter   `gorm:"foreignKey:AZID;constraint:OnDelete:CASCADE" json:"-" swaggerignore:"true"`
}

// TableName 指定表名
func (AZ) TableName() string {
	return "azs"
}
