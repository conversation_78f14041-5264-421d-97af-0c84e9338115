package location

import (
	"time"

	"gorm.io/gorm"
)

// Cabinet 机柜模型
type Cabinet struct {
	ID                 uint           `json:"id" gorm:"primarykey" example:"1"`
	CreatedAt          time.Time      `gorm:"column:created_at;not null" json:"created_at"`
	UpdatedAt          time.Time      `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	DeletedAt          gorm.DeletedAt `gorm:"index" json:"-"`
	Name               string         `json:"name" gorm:"type:varchar(100);not null;comment:机柜名称"`
	RoomID             uint           `json:"roomID" gorm:"not null;comment:房间ID"`
	Room               Room           `json:"room" gorm:"foreignKey:RoomID"`
	CapacityUnits      int            `json:"capacityUnits" gorm:"not null;default:42;comment:机柜容量(U)"`
	Row                string         `json:"row" gorm:"type:varchar(20);comment:机柜行"`
	Column             string         `json:"column" gorm:"type:varchar(20);comment:机柜列"`
	CabinetType        string         `json:"cabinetType" gorm:"type:varchar(50);comment:机柜类型"`
	NetworkEnvironment string         `json:"networkEnvironment" gorm:"type:varchar(50);comment:网络环境"`
	BondType           string         `json:"bondType" gorm:"type:varchar(50);comment:Bond类型"`
	TotalPower         float64        `json:"totalPower" gorm:"comment:总功耗(kW)"`
	Description        string         `json:"description,omitempty" gorm:"type:text;comment:描述"`
	Status             string         `json:"status" gorm:"type:varchar(20);default:active;comment:状态(active:启用,disabled:禁用)"`
}

// TableName 指定表名
func (Cabinet) TableName() string {
	return "cabinets"
}
