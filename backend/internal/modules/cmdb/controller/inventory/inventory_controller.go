package inventory

import (
	"backend/internal/common/constants"
	inventoryModel "backend/internal/modules/cmdb/model/inventory"
	inventorySvc "backend/internal/modules/cmdb/service/inventory"
	"backend/pkg/response"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// InventoryController 库存控制器
type InventoryController struct {
	service inventorySvc.InventoryService
}

// NewInventoryController 创建库存控制器
func NewInventoryController(service inventorySvc.InventoryService) *InventoryController {
	return &InventoryController{service: service}
}

// Register 注册路由
func (c *InventoryController) Register(router *gin.RouterGroup) {
	inventoryRouter := router.Group("/inventory")
	{
		// 基础CRUD
		//inventoryRouter.POST("", c.CreateInventory)
		//inventoryRouter.PUT("/:id", c.UpdateInventory)
		//inventoryRouter.DELETE("/:id", c.DeleteInventory)
		inventoryRouter.GET("/:id", c.GetInventoryByID)
		inventoryRouter.GET("", c.ListInventory)

		// 库存操作
		//inventoryRouter.POST("/:id/adjust", c.AdjustStock)
		//inventoryRouter.POST("/:id/allocate", c.AllocateStock)
		//inventoryRouter.POST("/:id/release", c.ReleaseAllocatedStock)

		// 统计和报表
		inventoryRouter.GET("/summary/:productID", c.GetProductInventorySummary)
		inventoryRouter.GET("/low-stock", c.GetLowStockProducts)
		inventoryRouter.GET("/expiring-warranty", c.GetExpiringWarrantyItems)

		// 按仓库查询
		inventoryRouter.GET("/warehouse/:warehouseID", c.ListByWarehouse)

		// 根据产品ID和仓库ID查询
		inventoryRouter.GET("/product/:productID/warehouse/:warehouseID", c.GetByProductAndWarehouse)

		// 历史记录
		inventoryRouter.GET("/history/:detailID", c.GetStockHistory)
		// 根据产品ID和仓库ID查询库存变更历史
		inventoryRouter.GET("/history/product/:productID/warehouse/:warehouseID", c.GetStockHistoryByProductAndWarehouse)
	}
}

// CreateInventory 创建库存
// @Summary 创建库存明细
// @Description 创建一个新的库存明细记录
// @Tags 库存管理
// @Accept json
// @Produce json
// @Param inventory body inventoryModel.InventoryDetail true "库存明细信息"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /inventory [post]
func (c *InventoryController) CreateInventory(ctx *gin.Context) {
	var detail inventoryModel.InventoryDetail
	if err := ctx.ShouldBindJSON(&detail); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	if err := c.service.CreateInventory(ctx, &detail); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	response.Success(ctx, detail)
}

// UpdateInventory 更新库存
// @Summary 更新库存明细
// @Description 根据ID更新库存明细信息
// @Tags 库存管理
// @Accept json
// @Produce json
// @Param id path int true "库存明细ID"
// @Param inventory body inventoryModel.InventoryDetail true "库存明细信息"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /inventory/{id} [put]
func (c *InventoryController) UpdateInventory(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var detail inventoryModel.InventoryDetail
	if err := ctx.ShouldBindJSON(&detail); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	detail.ID = uint(id)

	if err := c.service.UpdateInventory(ctx, &detail); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// DeleteInventory 删除库存
// @Summary 删除库存明细
// @Description 根据ID删除库存明细记录
// @Tags 库存管理
// @Produce json
// @Param id path int true "库存明细ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /inventory/{id} [delete]
func (c *InventoryController) DeleteInventory(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	if err := c.service.DeleteInventory(ctx, uint(id)); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// GetInventoryByID 根据ID获取库存
// @Summary 获取库存明细详情
// @Description 根据ID获取库存明细记录详情
// @Tags 库存管理
// @Produce json
// @Param id path int true "库存明细ID"
// @Success 200 {object} response.Response{data=inventoryModel.InventoryDetail}
// @Failure 400 {object} response.Response
// @Router /inventory/{id} [get]
func (c *InventoryController) GetInventoryByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	detail, err := c.service.GetInventoryByID(ctx, uint(id))
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	response.Success(ctx, detail)
}

// ListInventory 查询库存列表
// @Summary 获取库存明细列表
// @Description 分页查询库存明细列表
// @Tags 库存管理
// @Produce json
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Param productID query int false "产品ID"
// @Param warehouse query string false "仓库名称"
// @Param materialType query string false "物料类型"
// @Param pn query string false "PN号码"
// @Param spec query string false "规格"
// @Param warehouseId query int false "仓库ID"
// @Param hasAvailable query bool false "是否有可用库存"
// @Param brand query string false "产品品牌"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /inventory [get]
func (c *InventoryController) ListInventory(ctx *gin.Context) {
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}

	// 获取查询参数
	productIDStr := ctx.Query("productID")
	warehouse := ctx.Query("warehouse")
	materialType := ctx.Query("materialType")
	pn := ctx.Query("pn")
	spec := ctx.Query("spec")
	warehouseIdStr := ctx.Query("warehouseId")
	hasAvailableStr := ctx.Query("hasAvailable")
	brand := ctx.Query("brand")
	showAllStr := ctx.DefaultQuery("showAll", "false")
	showAll, err := strconv.ParseBool(showAllStr)
	if err != nil {
		fmt.Println("无效的showAll参数:", err)
		showAll = false // 或根据需要返回错误
	}
	// part | device 用于控制单独展示 配件 ｜ 整机设备
	viewMode := ctx.Query("viewMode")

	var productID uint = 0
	if productIDStr != "" {
		pid, err := strconv.ParseUint(productIDStr, 10, 64)
		if err == nil {
			productID = uint(pid)
		}
	}

	var warehouseId uint = 0
	if warehouseIdStr != "" {
		wid, err := strconv.ParseUint(warehouseIdStr, 10, 64)
		if err == nil {
			warehouseId = uint(wid)
		}
	}

	// 处理hasAvailable参数
	var hasAvailable *bool
	if hasAvailableStr != "" {
		hasAvailableBool, err := strconv.ParseBool(hasAvailableStr)
		if err == nil {
			hasAvailable = &hasAvailableBool
		}
	}

	// 构建查询参数
	params := map[string]interface{}{
		"productID": productID,
		"warehouse": warehouse,
		"showAll":   showAll,
	}

	// 添加新的查询参数
	if materialType != "" {
		params["materialType"] = materialType
	}

	if pn != "" {
		params["pn"] = pn
	}

	if spec != "" {
		params["spec"] = spec
	}

	if warehouseId > 0 {
		params["warehouseId"] = warehouseId
	}

	if hasAvailable != nil {
		params["hasAvailable"] = *hasAvailable
	}

	if brand != "" {
		params["brand"] = brand
	}

	if viewMode != "" {
		params["viewMode"] = viewMode
	}

	details, total, err := c.service.ListInventory(ctx, page, pageSize, productID, warehouse, params)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	// 创建包含分页信息的响应
	result := map[string]interface{}{
		"list":  details,
		"total": total,
	}
	response.Success(ctx, result)
}

// AdjustStock 调整库存数量
// @Summary 调整库存数量
// @Description 增加或减少库存数量
// @Tags 库存管理
// @Accept json
// @Produce json
// @Param id path int true "库存明细ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /inventory/{id}/adjust [post]
func (c *InventoryController) AdjustStock(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var req struct {
		Quantity int    `json:"quantity" binding:"required"`
		Reason   string `json:"reason" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	if err := c.service.AdjustStock(ctx, uint(id), req.Quantity, constants.ChangeTypeAdjust, req.Reason, 0, 0); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// AllocateStock 分配库存
// @Summary 分配库存
// @Description 分配库存给特定用途
// @Tags 库存管理
// @Accept json
// @Produce json
// @Param id path int true "库存明细ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /inventory/{id}/allocate [post]
func (c *InventoryController) AllocateStock(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var req struct {
		Quantity int    `json:"quantity" binding:"required"`
		Purpose  string `json:"purpose" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	if err := c.service.AllocateStock(ctx, uint(id), req.Quantity, req.Purpose); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// ReleaseAllocatedStock 释放已分配库存
// @Summary 释放已分配库存
// @Description 释放之前分配的库存
// @Tags 库存管理
// @Accept json
// @Produce json
// @Param id path int true "库存明细ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /inventory/{id}/release [post]
func (c *InventoryController) ReleaseAllocatedStock(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var req struct {
		Quantity int `json:"quantity" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	if err := c.service.ReleaseAllocatedStock(ctx, uint(id), req.Quantity); err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// GetProductInventorySummary 获取产品库存汇总
// @Summary 获取产品库存汇总
// @Description 获取指定产品的库存汇总信息
// @Tags 库存管理
// @Produce json
// @Param productID path int true "产品ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /inventory/summary/{productID} [get]
func (c *InventoryController) GetProductInventorySummary(ctx *gin.Context) {
	productID, err := strconv.ParseUint(ctx.Param("productID"), 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的产品ID")
		return
	}

	summary, err := c.service.GetProductInventorySummary(ctx, uint(productID))
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	response.Success(ctx, summary)
}

// GetLowStockProducts 获取低库存产品列表
// @Summary 获取低库存产品列表
// @Description 获取库存数量低于阈值的产品列表
// @Tags 库存管理
// @Produce json
// @Param threshold query int false "库存阈值"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /inventory/low-stock [get]
func (c *InventoryController) GetLowStockProducts(ctx *gin.Context) {
	threshold, err := strconv.Atoi(ctx.DefaultQuery("threshold", "10"))
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的阈值参数: "+err.Error())
		return
	}

	products, err := c.service.GetLowStockProducts(ctx, threshold)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	response.Success(ctx, products)
}

// GetExpiringWarrantyItems 获取即将过保的库存项目
// @Summary 获取即将过保的库存项目
// @Description 获取在指定天数内即将过保的库存项目
// @Tags 库存管理
// @Produce json
// @Param days query int false "天数"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /inventory/expiring-warranty [get]
func (c *InventoryController) GetExpiringWarrantyItems(ctx *gin.Context) {
	days, err := strconv.Atoi(ctx.DefaultQuery("days", "30"))
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的天数参数: "+err.Error())
		return
	}

	items, err := c.service.GetExpiringWarrantyItems(ctx, days)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	response.Success(ctx, items)
}

// ListByWarehouse 根据仓库查询库存
// @Summary 查询仓库内的库存明细
// @Description 根据仓库ID查询库存明细列表
// @Tags 库存管理
// @Produce json
// @Param warehouseID path int true "仓库ID"
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /inventory/warehouse/{warehouseID} [get]
func (c *InventoryController) ListByWarehouse(ctx *gin.Context) {
	warehouseID, err := strconv.ParseUint(ctx.Param("warehouseID"), 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的仓库ID")
		return
	}

	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}

	details, total, err := c.service.ListByWarehouse(ctx, uint(warehouseID), page, pageSize)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	// 创建包含分页信息的响应
	result := map[string]interface{}{
		"list":  details,
		"total": total,
	}
	response.Success(ctx, result)
}

// GetStockHistory 获取库存变更历史
// @Summary 获取库存变更历史
// @Description 获取指定库存明细的变更历史记录
// @Tags 库存管理
// @Produce json
// @Param detailID path int true "库存明细ID"
// @Param startTime query string false "开始时间"
// @Param endTime query string false "结束时间"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /inventory/history/{detailID} [get]
func (c *InventoryController) GetStockHistory(ctx *gin.Context) {
	detailID, err := strconv.ParseUint(ctx.Param("detailID"), 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的库存明细ID")
		return
	}

	var startTime, endTime time.Time

	startTimeStr := ctx.Query("startTime")
	if startTimeStr != "" {
		startTime, err = time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			response.Error(ctx, http.StatusBadRequest, "无效的开始时间格式")
			return
		}
	}

	endTimeStr := ctx.Query("endTime")
	if endTimeStr != "" {
		endTime, err = time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			response.Error(ctx, http.StatusBadRequest, "无效的结束时间格式")
			return
		}
	}

	histories, err := c.service.GetStockHistory(ctx, uint(detailID), 0, 0, startTime, endTime)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	response.Success(ctx, histories)
}

// GetByProductAndWarehouse 根据产品ID和仓库ID查询库存
// @Summary 根据产品ID和仓库ID查询库存
// @Description 根据产品ID和仓库ID查询库存明细
// @Tags 库存管理
// @Accept json
// @Produce json
// @Param productID path int true "产品ID"
// @Param warehouseID path int true "仓库ID"
// @Success 200 {object} response.Response{data=inventoryModel.InventoryDetail}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /cmdb/inventory/product/{productID}/warehouse/{warehouseID} [get]
func (c *InventoryController) GetByProductAndWarehouse(ctx *gin.Context) {
	// 解析路径参数
	productIDStr := ctx.Param("productID")
	warehouseIDStr := ctx.Param("warehouseID")

	productID, err := strconv.ParseUint(productIDStr, 10, 32)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的产品ID")
		return
	}

	warehouseID, err := strconv.ParseUint(warehouseIDStr, 10, 32)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的仓库ID")
		return
	}

	// 调用服务层方法
	inventory, err := c.service.GetByProductAndWarehouse(ctx, uint(productID), uint(warehouseID))
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "查询库存失败: "+err.Error())
		return
	}

	// 返回数据
	response.Success(ctx, inventory)
}

// GetStockHistoryByProductAndWarehouse 根据产品ID和仓库ID获取库存变更历史
// @Summary 根据产品ID和仓库ID获取库存变更历史
// @Description 根据产品ID和仓库ID获取库存变更历史记录
// @Tags 库存管理
// @Produce json
// @Param productID path int true "产品ID"
// @Param warehouseID path int true "仓库ID"
// @Param startTime query string false "开始时间"
// @Param endTime query string false "结束时间"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /inventory/history/product/{productID}/warehouse/{warehouseID} [get]
func (c *InventoryController) GetStockHistoryByProductAndWarehouse(ctx *gin.Context) {
	productID, err := strconv.ParseUint(ctx.Param("productID"), 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的产品ID")
		return
	}

	warehouseID, err := strconv.ParseUint(ctx.Param("warehouseID"), 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的仓库ID")
		return
	}

	var startTime, endTime time.Time

	startTimeStr := ctx.Query("startTime")
	if startTimeStr != "" {
		startTime, err = time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			response.Error(ctx, http.StatusBadRequest, "无效的开始时间格式")
			return
		}
	}

	endTimeStr := ctx.Query("endTime")
	if endTimeStr != "" {
		endTime, err = time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			response.Error(ctx, http.StatusBadRequest, "无效的结束时间格式")
			return
		}
	}

	// 直接通过产品ID和仓库ID获取历史记录
	histories, err := c.service.GetStockHistory(ctx, 0, uint(productID), uint(warehouseID), startTime, endTime)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, err.Error())
		return
	}

	response.Success(ctx, histories)
}
