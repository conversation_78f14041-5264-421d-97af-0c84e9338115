package location

import (
	"backend/internal/modules/cmdb/model/location"
	ser "backend/internal/modules/cmdb/service/location"
	"backend/response"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// RegionController 区域控制器
type RegionController struct {
	service ser.RegionService
}

// NewRegionController 创建区域控制器
func NewRegionController(service ser.RegionService) *RegionController {
	return &RegionController{service: service}
}

// Create 创建区域
// @Summary 创建区域
// @Description 创建新的区域
// @Tags 位置管理-区域
// @Accept json
// @Produce json
// @Param region body location.Region true "区域信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/regions [post]
func (c *RegionController) Create(ctx *gin.Context) {
	var region location.Region
	if err := ctx.ShouldBindJSON(&region); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := c.service.CreateRegion(ctx, &region); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建区域失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{"id": region.ID}, "创建区域成功")
}

// Update 更新区域
// @Summary 更新区域
// @Description 更新区域信息
// @Tags 位置管理-区域
// @Accept json
// @Produce json
// @Param id path int true "区域ID"
// @Param region body location.Region true "区域信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/regions/{id} [put]
func (c *RegionController) Update(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var region location.Region
	if err := ctx.ShouldBindJSON(&region); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	region.ID = uint(id)
	if err := c.service.UpdateRegion(ctx, &region); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新区域失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新区域成功")
}

// Delete 删除区域
// @Summary 删除区域
// @Description 删除指定ID的区域
// @Tags 位置管理-区域
// @Accept json
// @Produce json
// @Param id path int true "区域ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/regions/{id} [delete]
func (c *RegionController) Delete(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	if err := c.service.DeleteRegion(ctx, uint(id)); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "删除区域失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "删除区域成功")
}

// GetByID 根据ID获取区域
// @Summary 获取区域详情
// @Description 根据ID获取区域详情
// @Tags 位置管理-区域
// @Accept json
// @Produce json
// @Param id path int true "区域ID"
// @Success 200 {object} response.ResponseStruct{data=location.Region}
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/regions/{id} [get]
func (c *RegionController) GetByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	region, err := c.service.GetRegionByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取区域失败: "+err.Error())
		return
	}

	response.Success(ctx, region, "获取区域成功")
}

// List 获取区域列表
// @Summary 获取区域列表
// @Description 分页获取区域列表
// @Tags 位置管理-区域
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Param query query string false "搜索关键词"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/regions [get]
func (c *RegionController) List(ctx *gin.Context) {
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}
	query := ctx.DefaultQuery("name", "")

	regions, total, err := c.service.ListRegions(ctx, page, pageSize, query)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取区域列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  regions,
		"total": total,
	}, "获取区域列表成功")
}

func (c *RegionController) GetWarehousesByProject(ctx *gin.Context) {
	project := ctx.Param("name")
	if project == "" {
		response.Fail(ctx, http.StatusBadRequest, "项目名称不能为空")
		return
	}
	warehouses, err := c.service.GetWarehousesByProject(ctx, project)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取相关仓库失败: "+err.Error())
		return
	}
	warehouseRes := make(map[string]uint)
	for _, warehouse := range warehouses {
		warehouseRes[warehouse.Name] = warehouse.ID
	}
	response.Success(ctx, warehouseRes, "获取所有仓库成功")
}
