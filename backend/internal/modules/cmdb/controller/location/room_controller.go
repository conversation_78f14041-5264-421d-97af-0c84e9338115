package location

import (
	"backend/internal/modules/cmdb/model/location"
	ser "backend/internal/modules/cmdb/service/location"
	"backend/response"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// RoomController 房间控制器
type RoomController struct {
	service ser.RoomService
}

// NewRoomController 创建房间控制器
func NewRoomController(service ser.RoomService) *RoomController {
	return &RoomController{service: service}
}

// Create 创建房间
// @Summary 创建房间
// @Description 创建新的房间
// @Tags 位置管理-房间
// @Accept json
// @Produce json
// @Param room body location.Room true "房间信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/rooms [post]
func (c *RoomController) Create(ctx *gin.Context) {
	var room location.Room
	if err := ctx.ShouldBindJSON(&room); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := c.service.CreateRoom(ctx, &room); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建房间失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{"id": room.ID}, "创建房间成功")
}

// Update 更新房间
// @Summary 更新房间
// @Description 更新房间信息
// @Tags 位置管理-房间
// @Accept json
// @Produce json
// @Param id path int true "房间ID"
// @Param room body location.Room true "房间信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/rooms/{id} [put]
func (c *RoomController) Update(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var room location.Room
	if err := ctx.ShouldBindJSON(&room); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	room.ID = uint(id)
	if err := c.service.UpdateRoom(ctx, &room); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新房间失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新房间成功")
}

// Delete 删除房间
// @Summary 删除房间
// @Description 删除指定ID的房间
// @Tags 位置管理-房间
// @Accept json
// @Produce json
// @Param id path int true "房间ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/rooms/{id} [delete]
func (c *RoomController) Delete(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	if err := c.service.DeleteRoom(ctx, uint(id)); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "删除房间失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "删除房间成功")
}

// GetByID 根据ID获取房间
// @Summary 获取房间详情
// @Description 根据ID获取房间详情
// @Tags 位置管理-房间
// @Accept json
// @Produce json
// @Param id path int true "房间ID"
// @Success 200 {object} response.ResponseStruct{data=location.Room}
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/rooms/{id} [get]
func (c *RoomController) GetByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	room, err := c.service.GetRoomByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取房间失败: "+err.Error())
		return
	}

	response.Success(ctx, room, "获取房间成功")
}

// GetByDataCenterID 根据机房ID获取房间列表
// @Summary 获取机房下的房间列表
// @Description 根据机房ID获取房间列表
// @Tags 位置管理-房间
// @Accept json
// @Produce json
// @Param id path int true "机房ID"
// @Success 200 {object} response.ResponseStruct{data=[]location.Room}
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/datacenters/{id}/rooms [get]
func (c *RoomController) GetByDataCenterID(ctx *gin.Context) {
	dataCenterID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的机房ID")
		return
	}

	rooms, err := c.service.GetRoomsByDataCenterID(ctx, uint(dataCenterID))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取房间列表失败: "+err.Error())
		return
	}

	response.Success(ctx, rooms, "获取房间列表成功")
}

// List 获取房间列表
// @Summary 获取房间列表
// @Description 分页获取房间列表
// @Tags 位置管理-房间
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Param query query string false "搜索关键词"
// @Param dataCenterId query int false "机房ID，按机房筛选"
// @Success 200 {object} response.ResponseStruct{data=[]location.Room, total=int}
// @Failure 400 {object} response.ResponseStruct
// @Security BearerAuth
// @Router /cmdb/rooms [get]
func (c *RoomController) List(ctx *gin.Context) {
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}
	query := ctx.DefaultQuery("query", "")

	// 获取机房ID参数
	var dataCenterId uint
	if dcID := ctx.Query("dataCenterId"); dcID != "" {
		id, err := strconv.ParseUint(dcID, 10, 32)
		if err == nil {
			dataCenterId = uint(id)
		}
	}

	rooms, total, err := c.service.ListRooms(ctx, page, pageSize, query, dataCenterId)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取房间列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  rooms,
		"total": total,
	}, "获取房间列表成功")
}
