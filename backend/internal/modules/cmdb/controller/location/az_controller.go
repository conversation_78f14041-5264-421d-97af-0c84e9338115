package location

import (
	"backend/internal/modules/cmdb/model/location"
	ser "backend/internal/modules/cmdb/service/location"
	"backend/response"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// AZController 可用区控制器
type AZController struct {
	service ser.AZService
}

// NewAZController 创建可用区控制器
func NewAZController(service ser.AZService) *AZController {
	return &AZController{service: service}
}

// Create 创建可用区
// @Summary 创建可用区
// @Description 创建新的可用区
// @Tags 位置管理-可用区
// @Accept json
// @Produce json
// @Param az body location.AZ true "可用区信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/azs [post]
// @Security BearerAuth
func (c *AZController) Create(ctx *gin.Context) {
	var az location.AZ
	if err := ctx.ShouldBindJSON(&az); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := c.service.CreateAZ(ctx, &az); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建可用区失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{"id": az.ID}, "创建可用区成功")
}

// Update 更新可用区
// @Summary 更新可用区
// @Description 更新可用区信息
// @Tags 位置管理-可用区
// @Accept json
// @Produce json
// @Param id path int true "可用区ID"
// @Param az body location.AZ true "可用区信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/azs/{id} [put]
// @Security BearerAuth
func (c *AZController) Update(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var az location.AZ
	if err := ctx.ShouldBindJSON(&az); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	az.ID = uint(id)
	if err := c.service.UpdateAZ(ctx, &az); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新可用区失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新可用区成功")
}

// Delete 删除可用区
// @Summary 删除可用区
// @Description 删除指定ID的可用区
// @Tags 位置管理-可用区
// @Accept json
// @Produce json
// @Param id path int true "可用区ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /azs/cmdb/{id} [delete]
// @Security BearerAuth
func (c *AZController) Delete(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	if err := c.service.DeleteAZ(ctx, uint(id)); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "删除可用区失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "删除可用区成功")
}

// GetByID 根据ID获取可用区
// @Summary 获取可用区详情
// @Description 根据ID获取可用区详情
// @Tags 位置管理-可用区
// @Accept json
// @Produce json
// @Param id path int true "可用区ID"
// @Success 200 {object} response.ResponseStruct{data=location.AZ}
// @Failure 400 {object} response.ResponseStruct
// @Router /azs/cmdb/{id} [get]
// @Security BearerAuth
func (c *AZController) GetByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	az, err := c.service.GetAZByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取可用区失败: "+err.Error())
		return
	}

	response.Success(ctx, az, "获取可用区成功")
}

// GetByRegionID 根据区域ID获取可用区列表
// @Summary 获取区域下的可用区列表
// @Description 根据区域ID获取可用区列表
// @Tags 位置管理-可用区
// @Accept json
// @Produce json
// @Param id path int true "区域ID"
// @Success 200 {object} response.ResponseStruct{data=[]location.AZ}
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/regions/{id}/azs [get]
// @Security BearerAuth
func (c *AZController) GetByRegionID(ctx *gin.Context) {
	regionID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的区域ID")
		return
	}

	azs, err := c.service.GetAZsByRegionID(ctx, uint(regionID))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取可用区列表失败: "+err.Error())
		return
	}

	response.Success(ctx, azs, "获取可用区列表成功")
}

// List 获取可用区列表
// @Summary 获取可用区列表
// @Description 分页获取可用区列表
// @Tags 位置管理-可用区
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Param query query string false "搜索关键词"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /cmdb/azs [get]
// @Security BearerAuth
func (c *AZController) List(ctx *gin.Context) {
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}
	query := ctx.DefaultQuery("query", "")

	regionID, err := strconv.Atoi(ctx.DefaultQuery("regionId", "0"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的区域ID: "+err.Error())
		return
	}

	azs, total, err := c.service.ListAZs(ctx, page, pageSize, query, regionID)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取可用区列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  azs,
		"total": total,
	}, "获取可用区列表成功")
}
