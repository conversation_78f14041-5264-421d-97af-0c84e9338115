package product

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"

	"backend/internal/modules/cmdb/service/product"
	"backend/response"
)

// ProductHandler 产品处理器
type ProductHandler struct {
	service product.ProductService
}

// NewProductHandler 创建产品处理器
func NewProductHandler(service product.ProductService) *ProductHandler {
	return &ProductHandler{service: service}
}

// GetByID 根据ID获取产品
// @Summary 获取产品详情
// @Description 根据ID获取产品详情
// @Tags 产品管理
// @Accept json
// @Produce json
// @Param id path int true "产品ID"
// @Success 200 {object} response.Response{data=product.Product} "成功"
// @Failure 400 {object} response.Response "请求错误"
// @Failure 404 {object} response.Response "产品不存在"
// @Router /cmdb/products/{id} [get]
func (h *ProductHandler) GetByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(c, http.StatusBadRequest, "无效的ID")
		return
	}

	p, err := h.service.GetByID(c, uint(id))
	if err != nil {
		if err == product.ErrProductNotFound {
			response.Fail(c, http.StatusNotFound, "产品不存在")
			return
		}
		response.Fail(c, http.StatusInternalServerError, "获取产品失败")
		return
	}

	response.Success(c, p, "获取产品成功")
}

// List 获取产品列表
// @Summary 获取产品列表
// @Description 获取产品列表，支持分页和筛选
// @Tags 产品管理
// @Accept json
// @Produce json
// @Param pn query string false "PN号码"
// @Param material_type query string false "物料类型"
// @Param brand query string false "品牌"
// @Param model query string false "型号"
// @Param spec query string false "规格"
// @Param product_category query string false "产品类别"
// @Param status query integer false "状态(1:启用,0:禁用)"
// @Param page query int true "页码"
// @Param pageSize query int true "每页数量"
// @Success 200 {object} response.Response{data=product.ProductListResult} "成功"
// @Failure 400 {object} response.Response "请求错误"
// @Router /cmdb/products [get]
func (h *ProductHandler) List(c *gin.Context) {
	var query product.ProductListQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		response.Fail(c, http.StatusBadRequest, "无效的查询参数")
		return
	}

	result, err := h.service.List(c, query)
	if err != nil {
		response.Fail(c, http.StatusInternalServerError, "获取产品列表失败")
		return
	}

	response.Success(c, result, "获取产品列表成功")
}

// Create 创建产品
// @Summary 创建产品
// @Description 创建新产品
// @Tags 产品管理
// @Accept json
// @Produce json
// @Param product body product.CreateProductDTO true "产品信息"
// @Success 201 {object} response.Response{data=product.Product} "成功"
// @Failure 400 {object} response.Response "请求错误"
// @Router /cmdb/products [post]
func (h *ProductHandler) Create(c *gin.Context) {
	var dto product.CreateProductDTO
	if err := c.ShouldBindJSON(&dto); err != nil {
		response.Fail(c, http.StatusBadRequest, "无效的请求数据")
		return
	}

	// 去除PN前后的空白字符，再录入数据库
	dto.PN = strings.TrimSpace(dto.PN)

	p, err := h.service.Create(c, dto)
	if err != nil {
		if err == product.ErrInvalidProductData {
			response.Fail(c, http.StatusBadRequest, err.Error())
			return
		}
		response.Fail(c, http.StatusInternalServerError, "创建产品失败")
		return
	}

	response.Success(c, p, "创建产品成功")
}

// Update 更新产品
// @Summary 更新产品
// @Description 更新产品信息
// @Tags 产品管理
// @Accept json
// @Produce json
// @Param id path int true "产品ID"
// @Param product body product.UpdateProductDTO true "产品信息"
// @Success 200 {object} response.Response{data=product.Product} "成功"
// @Failure 400 {object} response.Response "请求错误"
// @Failure 404 {object} response.Response "产品不存在"
// @Router /cmdb/products/{id} [put]
func (h *ProductHandler) Update(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(c, http.StatusBadRequest, "无效的ID")
		return
	}

	var dto product.UpdateProductDTO
	if err := c.ShouldBindJSON(&dto); err != nil {
		response.Fail(c, http.StatusBadRequest, "无效的请求数据")
		return
	}

	// 确保路径参数ID和请求体中的ID一致
	dto.ID = uint(id)

	p, err := h.service.Update(c, dto)
	if err != nil {
		if err == product.ErrProductNotFound {
			response.Fail(c, http.StatusNotFound, "产品不存在")
			return
		}
		if err == product.ErrInvalidProductData {
			response.Fail(c, http.StatusBadRequest, err.Error())
			return
		}
		response.Fail(c, http.StatusInternalServerError, "更新产品失败")
		return
	}

	response.Success(c, p, "更新产品成功")
}

// Delete 删除产品
// @Summary 删除产品
// @Description 删除产品
// @Tags 产品管理
// @Accept json
// @Produce json
// @Param id path int true "产品ID"
// @Success 200 {object} response.Response "成功"
// @Failure 400 {object} response.Response "请求错误"
// @Failure 404 {object} response.Response "产品不存在"
// @Router /cmdb/products/{id} [delete]
func (h *ProductHandler) Delete(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(c, http.StatusBadRequest, "无效的ID")
		return
	}

	err = h.service.Delete(c, uint(id))
	if err != nil {
		if err == product.ErrProductNotFound {
			response.Fail(c, http.StatusNotFound, "产品不存在")
			return
		}
		response.Fail(c, http.StatusInternalServerError, "删除产品失败")
		return
	}

	response.Success(c, nil, "删除产品成功")
}

// GetMaterialTypes 获取所有物料类型
// @Summary 获取所有物料类型
// @Description 获取所有物料类型
// @Tags 产品管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]string} "成功"
// @Router /cmdb/dict/material-types [get]
func (h *ProductHandler) GetMaterialTypes(c *gin.Context) {
	types, err := h.service.GetMaterialTypes(c)
	if err != nil {
		response.Fail(c, http.StatusInternalServerError, "获取物料类型失败")
		return
	}

	response.Success(c, types, "获取物料类型成功")
}

// GetProductCategories 获取所有产品类别
// @Summary 获取所有产品类别
// @Description 获取所有产品类别
// @Tags 产品管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]string} "成功"
// @Router /cmdb/dict/product-categories [get]
func (h *ProductHandler) GetProductCategories(c *gin.Context) {
	categories, err := h.service.GetProductCategories(c)
	if err != nil {
		response.Fail(c, http.StatusInternalServerError, "获取产品类别失败")
		return
	}

	response.Success(c, categories, "获取产品类别成功")
}

// GetBrands 获取所有品牌
// @Summary 获取所有品牌
// @Description 获取所有品牌
// @Tags 产品管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]string} "成功"
// @Router /cmdb/dict/brands [get]
func (h *ProductHandler) GetBrands(c *gin.Context) {
	brands, err := h.service.GetBrands(c)
	if err != nil {
		response.Fail(c, http.StatusInternalServerError, "获取品牌失败")
		return
	}

	response.Success(c, brands, "获取品牌成功")
}

// GetAllSpecs 获取所有规格列表
// @Summary 获取所有规格列表
// @Description 获取所有规格列表
// @Tags 产品管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]string} "成功"
// @Router /cmdb/dict/specs [get]
func (h *ProductHandler) GetAllSpecs(c *gin.Context) {
	specs, err := h.service.GetAllSpecs(c)
	if err != nil {
		response.Fail(c, http.StatusInternalServerError, "获取规格列表失败")
		return
	}

	response.Success(c, specs, "获取规格列表成功")
}

// GetSpecsByMaterialType 获取物料类型对应的规格列表
// @Summary 获取物料类型对应的规格列表
// @Description 获取物料类型对应的规格列表
// @Tags 产品管理
// @Accept json
// @Produce json
// @Param material_type query string true "物料类型"
// @Success 200 {object} response.Response{data=[]string} "成功"
// @Failure 400 {object} response.Response "请求错误"
// @Router /cmdb/dict/specs-by-material-type [get]
func (h *ProductHandler) GetSpecsByMaterialType(c *gin.Context) {
	materialType := c.Query("material_type")
	if materialType == "" {
		response.Fail(c, http.StatusBadRequest, "物料类型不能为空")
		return
	}

	specs, err := h.service.GetSpecsByMaterialType(c, materialType)
	if err != nil {
		response.Fail(c, http.StatusInternalServerError, "获取规格列表失败")
		return
	}

	response.Success(c, specs, "获取规格列表成功")
}

// Register 注册路由
func (h *ProductHandler) Register(router *gin.RouterGroup) {
	productGroup := router.Group("/products")
	{
		productGroup.GET("", h.List)
		productGroup.GET("/:id", h.GetByID)
		productGroup.POST("", h.Create)
		productGroup.PUT("/:id", h.Update)
		productGroup.DELETE("/:id", h.Delete)
	}

	// 字典接口
	dictGroup := router.Group("/dict")
	{
		dictGroup.GET("/material-types", h.GetMaterialTypes)
		dictGroup.GET("/product-categories", h.GetProductCategories)
		dictGroup.GET("/brands", h.GetBrands)
		dictGroup.GET("/specs", h.GetAllSpecs) // Added this line
		dictGroup.GET("/specs-by-material-type", h.GetSpecsByMaterialType)
	}
}
