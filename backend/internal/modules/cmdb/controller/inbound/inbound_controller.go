package inbound

import (
	"backend/internal/modules/cmdb/model/inbound"
	inboundSvc "backend/internal/modules/cmdb/service/inbound"
	fileSvc "backend/internal/modules/file/service"
	"backend/internal/modules/ticket/common"
	ticketModel "backend/internal/modules/ticket/model"
	"backend/response"
	"context"
	"fmt"
	"mime/multipart"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// 入库控制器
type InboundController struct {
	InboundSvc       inboundSvc.InboundService
	InboundImportSvc inboundSvc.InboundImportService
	file             fileSvc.FileService
}

// 创建入库控制器
func NewInboundController(service inboundSvc.InboundService, importSvc inboundSvc.InboundImportService, file fileSvc.FileService) *InboundController {
	return &InboundController{InboundSvc: service, InboundImportSvc: importSvc, file: file}
}

func (c *InboundController) RegisterRoutes(r *gin.RouterGroup) {
	inboundDevice := r.Group("/inbound")
	{
		// 新购配件入库
		inboundDevice.POST("/new", c.CreateNewInbound)
		inboundDevice.POST("/new-import", c.CreateNewInboundByFile)
		inboundDevice.POST("/new-input", c.CreateNewInboundByInput)
		inboundDevice.PUT("/new/update-details-input", c.UpdateNewInboundDetailsByInput)
		inboundDevice.POST("/new/update-details-import", c.UpdateNewInboundDetailsByImport)

		//inboundDevice.POST("/new/:id/upload", c.UploadCSV)
		//inboundDevice.GET("/new/:id", c.GetNewInboundDetailsByID)
		inboundDevice.GET("/new/:inboundNo", c.GetNewInboundByNo)
		inboundDevice.GET("/new/submittersList", c.GetNewInboundSubmitterList)

		// 拆机配件入库
		inboundDevice.POST("/dismantled-input", c.CreateDismantledInboundByInput)
		inboundDevice.POST("/trans/dismantled-input", c.CreateDismantledInboundByInputV1)
		inboundDevice.POST("/trans/dismantled-import", c.CreateDismantledInboundByImportV1)

		// 设备入库 ｜ 服务器、交换机
		inboundDevice.PUT("/device/update-details-input", c.UpdateDeviceInboundDetailsByInput)

		//维修件入库单（废弃）
		inboundDevice.POST("/part", c.CreatePartInbound)       //创建入库单
		inboundDevice.PUT("/part/:id", c.UpdatePartInbound)    //更新入库单
		inboundDevice.DELETE("/part/:id", c.DeletePartInbound) //删除入库单
		inboundDevice.GET("/part/:id", c.GetPartInboundByID)   //获取入库单详情

		// 返修配件入库
		inboundDevice.POST("/repair-input", c.CreateRepairInboundByInput) // 创建返修配件入库单+返修配件入库工单+入库列表
		inboundDevice.POST("/repair-import", c.CreateRepairInboundByImport)

		// 统一入口
		// TODO 统一上传接口
		//inboundDevice.POST("")
		// TODO 统一创建接口
		//inboundDevice.POST("")
		// TODO 统一查询接口
		//inboundDevice.POST("")
		//inbound.GET("/detail/:id", c.GetDetailByID)
		//inbound.GET("/device/:deviceID", c.GetByDeviceID)

		// 根据工单号查询信息
		inboundDevice.GET(":inboundNo", c.GetByNo)
		// 获取工单列表
		inboundDevice.GET("", c.List)
	}
}

// CreateNewInbound 创建新入库单
// @Summary 创建新入库单
// @Description 创建一个新的入库单，包含采购合同ID、供应商ID和数量信息
// @Tags 入库管理
// @Accept json
// @Produce json
// @Param request body inbound.NewInbound true "新入库单请求参数"
// @Success 200 {object} response.Response "创建新入库单成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /cmdb/inbound/new [post]
func (c *InboundController) CreateNewInbound(ctx *gin.Context) {
	var newInboundReq inbound.NewInbound
	if err := ctx.ShouldBindJSON(&newInboundReq); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	// 获取当前用户信息
	userID, userName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取用户信息失败: "+err.Error())
		return
	}

	// 设置创建人信息
	newInboundReq.CreateBy = userName
	newInboundReq.CreateID = userID

	// 调用服务层创建入库单
	err = c.InboundSvc.CreateNewInbound(context.Background(), &newInboundReq)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建入库单失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"id": newInboundReq.ID,
	}, "创建新入库单成功")
}

type CreateNewInboundResponse struct {
	NewInboundID  uint      `json:"new_inbound_id"`
	NewInboundNo  string    `json:"new_inbound_no"`
	SubmitterTime time.Time `json:"submitter_time"`
}

type inboundDTO struct {
	Project         string                   `gorm:"column:project;comment:项目" json:"project" binding:"required"`
	InboundTitle    string                   ` json:"inbound_title" binding:"required"`
	PurchaseOrderNo string                   `json:"purchase_order_no" binding:"required"` //采购合同编号
	SupplierName    string                   `json:"supplier_name" binding:"required"`     // 供应商名称
	WarehouseName   string                   `json:"warehouse_name"`
	WarehouseID     uint                     `json:"warehouse_id"`
	NewInboundInfo  []inbound.NewInboundInfo `json:"new_info" binding:"required"`
	TrackingInfo    string                   `json:"tracking_info"`
	MayArriveAt     string                   `json:"may_arrive_at" binding:"required"`
}

// CreateNewInboundByInput 通过输入的形式创建新入库单、入库信息单、入库详情
// @Summary 创建新入库单及详情
// @Description 通过输入的形式创建新入库单，包含入库信息单和入库详情
// @Tags 入库管理
// @Accept json
// @Produce json
// @Param request body inboundDTO true "入库单创建请求参数"
// @Success 200 {object} response.Response{data=CreateNewInboundResponse} "创建成功"
// @Failure 400 {object} response.Response{msg=string} "请求参数错误"
// @Failure 500 {object} response.Response{msg=string} "服务器内部错误"
// @Router /cmdb/inbound/new-input [post]
func (c *InboundController) CreateNewInboundByInput(ctx *gin.Context) {
	var (
		InboundDTO       inboundDTO
		newInbound       inbound.NewInbound
		newInboudDetails []inbound.NewInboundDetail
	)
	if err := ctx.ShouldBindJSON(&InboundDTO); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "缺少相应字段: "+err.Error())
		return
	}

	// 获取当前用户信息
	userID, userName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取用户信息失败: "+err.Error())
		return
	}

	InboundList := inbound.InboundList{
		Project:       InboundDTO.Project,
		InboundType:   inbound.TypePartInbound,
		InboundTitle:  InboundDTO.InboundTitle,
		InboundReason: inbound.SourceTypeNewPurchase,
		CreateBy:      userName,
		CreaterID:     userID,
		Stage:         common.StageAssetApproval,
	}
	// 更新入库单
	newInbound.InboundTitle = InboundDTO.InboundTitle
	newInbound.Project = InboundDTO.Project
	newInbound.TrackingInfo = InboundDTO.TrackingInfo
	mayArriveAt, err := time.Parse(time.RFC3339, InboundDTO.MayArriveAt)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "日期转换失败: "+err.Error())
		return
	}
	newInbound.MayArriveAt = &mayArriveAt
	newInbound.PurchaseOrderNo = InboundDTO.PurchaseOrderNo
	newInbound.SupplierName = InboundDTO.SupplierName
	newInbound.CreateBy = userName
	newInbound.CreateID = userID
	newInbound.NewInfo = InboundDTO.NewInboundInfo

	err = c.InboundSvc.CreateNewInboundByInput(ctx, &InboundList, &newInbound)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建新购入库单失败："+err.Error())
		return
	}
	// 后续添加，给创建的配件统一添加仓库信息
	if InboundDTO.WarehouseName != "" && InboundDTO.WarehouseID != 0 {
		NewInbound, err := c.InboundSvc.GetNewInboundByID(context.Background(), newInbound.ID)
		if err != nil {
			response.Fail(ctx, http.StatusInternalServerError, "获取NewInbound失败"+err.Error())
			return
		}
		for _, detail := range NewInbound.NewDetails {
			detail.WarehouseName = InboundDTO.WarehouseName
			detail.WarehouseID = InboundDTO.WarehouseID
			newInboudDetails = append(newInboudDetails, detail)
		}
		err = c.InboundSvc.UpdateNewInboundWarehouse(ctx, newInboudDetails)
		if err != nil {
			response.Fail(ctx, http.StatusInternalServerError, "更新入库详情失败："+err.Error())
			return
		}
		fmt.Println(newInboudDetails)
	}
	data := CreateNewInboundResponse{
		NewInboundID:  newInbound.ID,
		NewInboundNo:  newInbound.InboundNo,
		SubmitterTime: newInbound.CreatedAt,
	}
	response.Success(ctx, data, "创建新购入库单成功")
}

// CreateNewInboundByFile 通过Excel文件创建新入库单
// @Summary 通过Excel文件创建新入库单
// @Description 上传Excel文件创建新入库单，文件格式要求：
// @Description 1. 必须包含以下列：project(项目)、inbound_title(入库标题)、purchase_order_no(采购合同编号)、supplier_name(供应商名称)
// @Description 2. 每行数据必须包含：product_id(产品ID)、amount(数量)
// @Description 3. 文件必须是.xlsx格式
// @Tags 入库管理
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "Excel文件(.xlsx)"
// @Success 200 {object} response.Response{data=string} "导入成功"
// @Failure 400 {object} response.Response{msg=string} "请求参数错误，包括：文件格式错误、必要列缺失等"
// @Failure 500 {object} response.Response{msg=string} "服务器内部错误，包括：文件处理错误、数据验证失败等"
// @Router /cmdb/inbound/new-import [post]
func (c *InboundController) CreateNewInboundByFile(ctx *gin.Context) {
	var (
		ImportForm       inbound.NewPartImport
		newInbound       inbound.NewInbound
		newInboudDetails []inbound.NewInboundDetail
	)
	// 获取要上传的信息
	if err := ctx.ShouldBind(&ImportForm); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取文件失败: "+err.Error())
		return
	}

	// 获取当前用户信息
	userID, userName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取用户信息失败: "+err.Error())
		return
	}

	InboundList := inbound.InboundList{
		Project:       ImportForm.Project,
		InboundType:   inbound.TypePartInbound,
		InboundTitle:  ImportForm.InboundTitle,
		InboundReason: inbound.SourceTypeNewPurchase,
		CreateBy:      userName,
		CreaterID:     userID,
		Stage:         common.StageAssetApproval,
	}

	// 调用service层处理Excel文件，获取到newInbound.NewInfo的信息
	info, err := c.InboundSvc.ImportNewInbound(ctx, ImportForm.File)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "导入失败: "+err.Error())
		return
	}

	// 更新入库单信息
	newInbound.InboundTitle = ImportForm.InboundTitle
	newInbound.Project = ImportForm.Project
	newInbound.TrackingInfo = ImportForm.TrackingInfo
	mayArriveAt, err := time.Parse(time.RFC3339, ImportForm.MayArriveAt)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "日期格式错误: "+err.Error())
		return
	}
	newInbound.MayArriveAt = &mayArriveAt
	newInbound.PurchaseOrderNo = ImportForm.PurchaseOrderNo
	newInbound.SupplierName = ImportForm.SupplierName
	newInbound.CreateBy = userName
	newInbound.CreateID = userID
	newInbound.NewInfo = info
	// 调用上面的Input方法，创建新购入库单
	err = c.InboundSvc.CreateNewInboundByInput(ctx, &InboundList, &newInbound)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}
	// 后续添加，给创建的配件统一添加仓库信息
	if ImportForm.WarehouseName != "" && ImportForm.WarehouseID != 0 {
		NewInbound, err := c.InboundSvc.GetNewInboundByID(context.Background(), newInbound.ID)
		if err != nil {
			response.Fail(ctx, http.StatusInternalServerError, "获取NewInbound失败"+err.Error())
			return
		}
		for _, detail := range NewInbound.NewDetails {
			detail.WarehouseName = ImportForm.WarehouseName
			detail.WarehouseID = ImportForm.WarehouseID
			newInboudDetails = append(newInboudDetails, detail)
		}
		err = c.InboundSvc.UpdateNewInboundWarehouse(ctx, newInboudDetails)
		if err != nil {
			response.Fail(ctx, http.StatusInternalServerError, "更新入库详情失败："+err.Error())
			return
		}
		fmt.Println(newInboudDetails)
	}
	data := CreateNewInboundResponse{
		NewInboundID:  newInbound.ID,
		NewInboundNo:  newInbound.InboundNo,
		SubmitterTime: newInbound.CreatedAt,
	}
	response.Success(ctx, data, "创建新购入库单成功")
}

// UpdateNewInboundDetailsByInput 通过输入的方式更新入库详情
// @Summary 更新入库详情
// @Description 通过输入的方式更新入库单的详细信息
// @Tags 入库管理
// @Accept json
// @Produce json
// @Param request body []inbound.NewInboundDetail true "入库详情更新参数"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Failure 400 {object} response.Response{msg=string} "请求参数错误"
// @Failure 500 {object} response.Response{msg=string} "服务器内部错误"
// @Router /cmdb/inbound/new/update-details-input [put]
func (c *InboundController) UpdateNewInboundDetailsByInput(ctx *gin.Context) {
	var DTO []inbound.NewInboundDetail
	err := ctx.ShouldBindJSON(&DTO)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "绑定DTO数据失败")
		return
	}

	err = c.InboundSvc.UpdateNewInboundDetailsByInput(ctx, DTO)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}
	response.Success(ctx, nil, "更新入库详情成功")
}

// UpdateNewInboundDetailsByImport 根据文件的形式导入入库详情
func (c *InboundController) UpdateNewInboundDetailsByImport(ctx *gin.Context) {
	var ImportForm inbound.NewPartDetailImport
	if err := ctx.ShouldBind(&ImportForm); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "绑定数据失败")
		return
	}
	err := c.InboundSvc.UpdateNewInboundDetailsByImport(ctx, &ImportForm)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "更新入库详情失败"+err.Error())
		return
	}
	response.Success(ctx, nil, "更新成功")
}

// Create 创建入库工单
// @Summary 创建入库工单
// @Description 根据请求参数创建一个新的入库工单
// @Tags 入库管理
// @Accept json
// @Produce json
// @Param request body inbound.PartInbound true "入库请求参数"
// @Success 200 {object} response.Response "创建入库工单成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /cmdb/inbound/part [post]
func (c *InboundController) CreatePartInbound(ctx *gin.Context) {
	var InboundReq inbound.PartInbound
	if err := ctx.ShouldBindJSON(&InboundReq); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	userID, userName, err := common.GetCurrentUserInfo(ctx)
	//InboundReq.Submitter = userName
	//InboundReq.SubmitterID = userID
	// 由硬件维修团队工程师发起
	InboundReq.EngineerName = userName
	InboundReq.EngineerID = userID
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取用户信息失败: "+err.Error())
	}
	inboundNo, id, err := c.InboundSvc.CreatePartInbound(context.Background(), InboundReq)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"id":        id,
		"inboundNo": inboundNo,
	}, "创建入库单成功")
}

// @Summary 获取我的工单列表
// @Description 根据用户ID、角色、状态、工单类别等条件获取工单列表
// @Tags 入库管理
// @Accept  json
// @Produce  json
// @Param role query string false "角色"
// @Param status query string false "状态"
// @Param orderCategory query string false "订单类别"
// @Param page query int false "页码" default(1)
// @Param pagesize query int false "每页数量" default(10)
// @Success 200 {object} interface{} "成功响应，返回票务列表"
// @Failure 400 {object} interface{} "请求参数错误"
// @Failure 500 {object} interface{} "服务器内部错误"
// @Router /cmdb/inbound [get]
//func (c *InboundController) List(ctx *gin.Context) {
//	var req inbound.GetListQuery
//
//	// 绑定请求参数
//	if err := ctx.ShouldBindQuery(&req); err != nil {
//		response.FailWithError(ctx, err)
//		return
//	}
//
//	// 从上下文中获取userID
//	userID, _, err := common.GetCurrentUserInfo(ctx)
//	if err != nil {
//		response.Fail(ctx, http.StatusUnauthorized, err.Error())
//		return
//	}
//
//	// 调用服务层方法
//	res, total, err := c.InboundSvc.List(context.Background(), userID, req)
//	if err != nil {
//		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
//		return
//	}
//
//	response.Success(ctx, gin.H{
//		"list":  res,
//		"total": total,
//	}, "获取维修入库工单成功")
//}

// DeletePartInbound 删除入库单
// @Summary 删除入库单
// @Description 根据ID删除指定的入库单
// @Tags 入库管理
// @Accept json
// @Produce json
// @Param id path int true "入库单ID"
// @Success 200 {object} response.Response "删除入库单成功"
// @Failure 400 {object} response.Response "无效的ID"
// @Failure 500 {object} response.Response "删除失败"
// @Router /cmdb/inbound/{id} [delete]
func (c *InboundController) DeletePartInbound(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	if err := c.InboundSvc.DeletePartInbound(context.Background(), uint(id)); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "删除失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "删除入库单成功")
}

// Update 更新入库单
// @Summary 更新入库单
// @Description 根据ID更新入库单信息
// @Tags 入库管理
// @Accept json
// @Produce json
// @Param id path int true "入库单ID"
// @Param request body inbound.PartInbound true "入库单更新数据"
// @Success 200 {object} response.Response "更新成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "更新失败"
// @Router /cmdb/inbound/Part/{id} [put]
func (c *InboundController) UpdatePartInbound(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var req inbound.PartInbound
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := c.InboundSvc.UpdatePartInbound(context.Background(), uint(id), req); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新成功")
}

// GetPartInboundByID 获取入库单详情
// @Summary 获取入库单详情
// @Description 根据ID获取入库单详细信息
// @Tags 入库管理
// @Accept json
// @Produce json
// @Param id path int true "入库单ID"
// @Success 200 {object} response.Response "成功获取详情"
// @Failure 400 {object} response.Response "无效的ID"
// @Failure 404 {object} response.Response "未找到数据"
// @Router /cmdb/inbound/{id} [get]
func (c *InboundController) GetPartInboundByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	result, err := c.InboundSvc.GetPartInboundByID(context.Background(), uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusNotFound, "未找到数据: "+err.Error())
		return
	}

	response.Success(ctx, result, "成功获取详情")
}

// GetNewInboundDetailsByID godoc
// @Summary 根据 ID 获取新购入库信息
// @Description 通过指定的 ID 获取新购入库的详细信息
// @Tags 新购入库
// @Accept  json
// @Produce  json
// @Param id path int true "新购入库的 ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {string} string "无效的 ID"
// @Router /cmdb/inbound/{id} [get]
func (c *InboundController) GetNewInboundDetailsByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}
	newInboundInfo, err := c.InboundSvc.GetNewInboundByID(context.Background(), uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusNotFound, "没有找到相应数据")
	}
	infoList, err := c.InboundSvc.GetNewInboundInfoByID(context.Background(), uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusNotFound, "没有找到相应数据")
	}
	var result struct {
		NewInbound *inbound.NewInbound
		Details    *[]inbound.NewInboundDetail
	}
	result.NewInbound = newInboundInfo
	result.Details = infoList
	response.Success(ctx, result, "成功获取详情")
}

// 创建入库工单
//func (c *InboundController) CreateTicket(ctx *gin.Context) {
//	var req inbound.InboundTicket
//	if err := ctx.ShouldBindJSON(&req); err != nil {
//		response.FailWithError(ctx, err)
//	}
//
//	// 获取userID 和 userName
//	userID, userName, err := common.GetCurrentUserInfo(ctx)
//	if err != nil {
//		response.Fail(ctx, http.StatusUnauthorized, err.Error())
//	}
//	req.Submitter = userName
//	req.SubmitterID = userID
//	// 调用服务层方法
//	if err := c.InboundSvc.CreatTicket(context.Background(), req); err != nil {
//		response.Fail(ctx, http.StatusInternalServerError, err.Error())
//		return
//	}
//	response.Success(ctx, nil, "创建成功")
//
//}

// UpdateTicket 更新入库工单
// @Summary 更新入库工单
// @Description 根据ID更新入库工单信息
// @Tags 入库管理
// @Accept json
// @Produce json
// @Param id path int true "工单ID"
// @Param request body inbound.PartInboundTicket true "工单更新数据"
// @Success 200 {object} response.Response "更新成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "更新失败"
// @Router /cmdb/inbound/tickets/{id} [put]
//func (c *InboundController) UpdateTicket(ctx *gin.Context) {
//	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
//	if err != nil {
//		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
//		return
//	}
//
//	var req inbound.InboundTicket
//	if err := ctx.ShouldBindJSON(&req); err != nil {
//		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
//		return
//	}
//
//	if err := c.InboundSvc.UpdateTicket(context.Background(), uint(id), req); err != nil {
//		response.Fail(ctx, http.StatusInternalServerError, "更新失败: "+err.Error())
//		return
//	}
//
//	response.Success(ctx, nil, "更新成功")
//}

// DeleteTicket 删除入库工单
// @Summary 删除入库工单
// @Description 根据ID删除指定的入库工单
// @Tags 入库管理
// @Accept json
// @Produce json
// @Param id path int true "工单ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.Response "无效的ID"
// @Failure 500 {object} response.Response "删除失败"
// @Router /cmdb/inbound/tickets/{id} [delete]
//func (c *InboundController) DeleteTicket(ctx *gin.Context) {
//	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
//	if err != nil {
//		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
//		return
//	}
//
//	if err := c.InboundSvc.DeleteTicket(context.Background(), uint(id)); err != nil {
//		response.Fail(ctx, http.StatusInternalServerError, "删除失败: "+err.Error())
//		return
//	}
//
//	response.Success(ctx, nil, "删除成功")
//}

// GetTicketByID 获取入库工单详情
// @Summary 获取入库工单详情
// @Description 根据ID获取入库工单详细信息
// @Tags 入库管理
// @Accept json
// @Produce json
// @Param id path int true "工单ID"
// @Success 200 {object} response.Response "成功获取详情"
// @Failure 400 {object} response.Response "无效的ID"
// @Failure 404 {object} response.Response "未找到数据"
// @Router /cmdb/inbound/tickets/{id} [get]
//func (c *InboundController) GetTicketByID(ctx *gin.Context) {
//	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
//	if err != nil {
//		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
//		return
//	}
//
//	ticket, err := c.InboundSvc.GetTicketByID(context.Background(), uint(id))
//	if err != nil {
//		response.Fail(ctx, http.StatusNotFound, "未找到数据: "+err.Error())
//		return
//	}
//
//	response.Success(ctx, ticket, "成功获取详情")
//}

// UploadCSV 上传CSV文件并导入数据
// @Summary 上传CSV文件并导入数据
// @Description 上传产品和备件的CSV文件，解析并导入到系统中
// @Tags 入库管理
// @Accept multipart/form-data
// @Produce json
// @Param product formData file true "产品CSV文件"
// @Param spare formData file true "备件CSV文件"
// @Success 200 {object} response.Response "导入成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /cmdb/inbound/new/{id}/upload [post]
func (c *InboundController) UploadCSV(ctx *gin.Context) {
	newInboundID, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请输入对应入库单ID："+err.Error())
	}

	// 防止重复上传文件
	newInbound, err := c.InboundSvc.GetNewInboundByID(ctx, uint(newInboundID))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}
	if newInbound.Lock {
		response.Fail(ctx, http.StatusInternalServerError, "该工单导入文件已录入")
		return
	}

	// 1. 获取上传的文件
	//productFile, err := ctx.FormFile("product")
	//if err != nil {
	//	response.Fail(ctx, http.StatusBadRequest, "获取产品CSV文件失败: "+err.Error())
	//	return
	//}

	spareFile, err := ctx.FormFile("spare")
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取备件CSV文件失败: "+err.Error())
		return
	}

	// 2. 打开产品文件
	//productReader, err := productFile.Open()
	//if err != nil {
	//	response.Fail(ctx, http.StatusInternalServerError, "打开产品CSV文件失败: "+err.Error())
	//	return
	//}
	//defer productReader.Close()

	// 3. 打开备件文件
	spareReader, err := spareFile.Open()
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "打开备件CSV文件失败: "+err.Error())
		return
	}
	defer func(spareReader multipart.File) {
		err := spareReader.Close()
		if err != nil {
			return
		}
	}(spareReader)

	// 4. 调用服务层处理导入
	//result, err := c.InboundImportSvc.ImportProductAndSpareData(context.Background(), productReader, spareReader, uint(newInboundID))

	result, err := c.InboundImportSvc.ImportSpareData(context.Background(), spareReader, uint(newInboundID))
	if err != nil {
		response.InboundFail(ctx, http.StatusInternalServerError, result, err.Error())
		return
	}
	file, err := c.file.UploadFile(ctx, spareFile, "NewInbound", uint(newInboundID), "新购入库文件")
	if err != nil {
		response.InboundFail(ctx, http.StatusInternalServerError, result, err.Error())
	}

	// 5. 返回导入结果
	response.Success(ctx, gin.H{
		"total":   result.Total,
		"success": result.Success,
		"failed":  result.Failed,
		//"products_imported": result.ProductsImported,
		//"products_existed":  result.ProductsExisted,
		"invalid_pns":   result.InvalidPNs,
		"error_details": result.ErrorDetails,
		"file_id":       file.ID,
	}, "导入成功")
}

// GetNewInboundSubmitterList 获取新维修入库单提交人列表
// @Summary 获取提交人列表
// @Description 获取所有新维修入库单的提交人列表
// @Tags 入库管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=map[string]uint} "获取成功"
// @Failure 500 {object} response.Response{msg=string} "服务器内部错误"
// @Router /cmdb/inbound/new/submittersList [get]
func (c *InboundController) GetNewInboundSubmitterList(ctx *gin.Context) {
	list, err := c.InboundSvc.GetNewInboundSubmitterList(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}
	response.Success(ctx, list, "获取成功")
}

// GetNewInboundByNo 根据入库单号获取新购入库单信息
// @Summary 获取新购入库单信息
// @Description 根据入库单号获取新购入库单的详细信息
// @Tags 入库管理
// @Accept json
// @Produce json
// @Param inboundNo path string true "入库单号"
// @Success 200 {object} response.Response{data=inbound.NewInbound} "获取成功"
// @Failure 400 {object} response.Response{msg=string} "请求参数错误"
// @Failure 500 {object} response.Response{msg=string} "服务器内部错误"
// @Router /cmdb/inbound/new/{inboundNo} [get]
func (c *InboundController) GetNewInboundByNo(ctx *gin.Context) {
	inboundNo := ctx.Param("inboundNo")
	if inboundNo == "" {
		response.Fail(ctx, http.StatusBadRequest, "入库单号不能为空")
		return
	}

	result, err := c.InboundSvc.GetNewInboundByNo(ctx, inboundNo)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取入库单信息失败: "+err.Error())
		return
	}

	response.Success(ctx, result, "获取入库单信息成功")
}

type InboudWholeInfo struct {
	InboundType string
	InboundInfo inbound.InboundInterface           `json:"inbound_info"`
	TicketInfo  ticketModel.InboundTicketInterface `json:"ticket_info"`
}

// GetByNo 根据工单号获取入库信息
// @Summary 获取入库信息
// @Description 根据工单号获取入库信息，支持新购入库、返修入库、拆机入库等类型
// @Tags 入库管理
// @Accept json
// @Produce json
// @Param inboundNo path string true "工单号"
// @Success 200 {object} response.Response{data=InboudWholeInfo} "获取成功"
// @Failure 400 {object} response.Response{msg=string} "请求参数错误"
// @Failure 404 {object} response.Response{msg=string} "数据不存在"
// @Router /cmdb/inbound/{inboundNo} [get]
func (c *InboundController) GetByNo(ctx *gin.Context) {
	inboundNo := ctx.Param("inboundNo")
	var inboundType string
	if inboundNo == "" {
		response.Fail(ctx, http.StatusBadRequest, "工单号不能为空")
		return
	}

	inboundInfo, ticketInfo, err := c.InboundSvc.GetByNo(ctx, inboundNo)
	if err != nil {
		response.Fail(ctx, http.StatusNotFound, err.Error())
		return
	}
	switch t := inboundInfo.(type) {
	case *inbound.NewInbound:
		inboundType = t.InboundType()
	case *inbound.RepairInbound:
		inboundType = t.InboundType()
	case *inbound.DismantledInbound:
		inboundType = t.InboundType()
	case *inbound.DeviceInbound:
		inboundType = t.InboundType()
	}

	result := InboudWholeInfo{
		InboundType: inboundType,
		InboundInfo: inboundInfo,
		TicketInfo:  ticketInfo,
	}

	response.Success(ctx, result, "获取入库信息成功")
}

// List 获取入库单列表
// @Summary 获取入库单列表
// @Description 获取入库单列表，支持分页和条件查询
// @Tags 入库管理
// @Accept json
// @Produce json
// @Param inboundNo query string false "入库单号"
// @Param inboundType query string false "入库类型"
// @Param createID query integer false "创建人ID"
// @Param stage query string false "状态"
// @Param page query integer true "页码"
// @Param pageSize query integer true "每页数量"
// @Success 200 {object} response.Response{data=object{total=int64,list=[]inbound.InboundList}} "获取成功"
// @Failure 400 {object} response.Response{msg=string} "请求参数错误"
// @Failure 500 {object} response.Response{msg=string} "服务器内部错误"
// @Router /cmdb/inbound [get]
func (c *InboundController) List(ctx *gin.Context) {
	var inboundList inbound.ListDTO
	if err := ctx.ShouldBindQuery(&inboundList); err != nil {
		response.Fail(ctx, http.StatusBadRequest, err.Error())
		return
	}

	total, list, err := c.InboundSvc.List(ctx, &inboundList)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取入库单列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"total": total,
		"list":  list,
	}, "获取入库单列表成功")
}

type DismantledInboundDTO struct {
	DismantledInbounds inbound.DismantledInbound `json:"dismantled_inbounds" binding:"required"`
	InboundTitle       string                    `json:"inbound_title" binding:"required"`
	InboundReason      string                    `json:"inbound_reason"`
	Project            string                    `json:"project" binding:"required"`
}

// CreateDismantledInboundByInput 通过输入的方式创建拆机入库单
// @Summary 创建拆机入库单
// @Description 通过输入的方式创建拆机入库单及相关信息
// @Tags 入库管理
// @Accept json
// @Produce json
// @Param request body DismantledInboundDTO true "拆机入库单创建参数"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Failure 400 {object} response.Response{msg=string} "请求参数错误"
// @Failure 500 {object} response.Response{msg=string} "服务器内部错误"
// @Router /cmdb/inbound/dismantled-input [post]
func (c *InboundController) CreateDismantledInboundByInput(ctx *gin.Context) {
	var DTO DismantledInboundDTO
	err := ctx.ShouldBindJSON(&DTO)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	// 获取当前用户信息
	userID, userName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取用户信息失败: "+err.Error())
		return
	}

	DTO.DismantledInbounds.CreateBy = userName
	DTO.DismantledInbounds.CreateID = userID

	// 调用服务层创建拆机入库单
	err = c.InboundSvc.CreateDismantledInboundByInput(ctx, DTO.DismantledInbounds, DTO.InboundTitle)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建拆机入库单失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "创建拆机入库单成功")
}

type DismantledInboundDTOV1 struct {
	DismantledInbound inbound.DismantledInbound `json:"dismantled_inbounds" binding:"required"`
	InboundTitle      string                    `json:"inbound_title" binding:"required"`
	Project           string                    `json:"project" binding:"required"`
}

// CreateDismantledInboundByInputV1 通过输入的方式创建拆机入库单(V1版本)
// @Summary 创建拆机入库单(V1)
// @Description 通过输入的方式创建拆机入库单及相关信息的V1版本
// @Tags 入库管理
// @Accept json
// @Produce json
// @Param request body DismantledInboundDTOV1 true "拆机入库单创建参数V1"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Failure 400 {object} response.Response{msg=string} "请求参数错误"
// @Failure 500 {object} response.Response{msg=string} "服务器内部错误"
// @Router /cmdb/inbound/trans/dismantled-input [post]
func (c *InboundController) CreateDismantledInboundByInputV1(ctx *gin.Context) {
	var DTO DismantledInboundDTOV1
	err := ctx.ShouldBindJSON(&DTO)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	// 获取当前用户信息
	userID, userName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取用户信息失败: "+err.Error())
		return
	}
	DTO.DismantledInbound.CreateBy = userName
	DTO.DismantledInbound.CreateID = userID

	// 调用服务层创建拆机入库单
	inboundNo, err := c.InboundSvc.CreateDismantledInboundByInputV1(ctx, DTO.DismantledInbound, DTO.InboundTitle, DTO.Project)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建拆机入库单失败: "+err.Error())
		return
	}

	response.Success(ctx, inboundNo, "创建拆机入库单成功")
}

type RepairInboundDTO struct {
	InboundTitle  string                `json:"inbound_title" binding:"required"`
	Project       string                `json:"project" binding:"required"`
	RepairInbound inbound.RepairInbound `json:"repair_inbound" binding:"required"`
}
type RepairInboundResult struct {
	InboundNo string `json:"inbound_no"`
}

// CreateRepairInboundByInput 通过输入的方式创建返修入库单
// @Summary 创建返修入库单
// @Description 通过输入的方式创建返修入库单、返修入库工单和入库列表
// @Tags 入库管理
// @Accept json
// @Produce json
// @Param request body RepairInboundDTO true "返修入库单创建参数"
// @Success 200 {object} response.Response{data=RepairInboundResult} "创建成功"
// @Failure 400 {object} response.Response{msg=string} "请求参数错误"
// @Failure 500 {object} response.Response{msg=string} "服务器内部错误"
// @Router /cmdb/inbound/repair-input [post]
func (c *InboundController) CreateRepairInboundByInput(ctx *gin.Context) {
	var (
		dto    RepairInboundDTO
		result RepairInboundResult
	)
	if err := ctx.ShouldBindJSON(&dto); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 获取当前用户信息
	userID, userName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取用户信息失败: "+err.Error())
		return
	}

	// 设置创建人信息
	dto.RepairInbound.CreateBy = userName
	dto.RepairInbound.CreateID = userID

	// 调用service层
	inboundNo, err := c.InboundSvc.CreateRepairInboundByInput(ctx, &dto.RepairInbound, dto.InboundTitle, dto.Project)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}
	result.InboundNo = inboundNo

	response.Success(ctx, result, "创建返修入库单成功")
}

// CreateRepairInboundByImport 通过Excel文件导入返修入库单
// @Summary 通过Excel文件导入返修入库单
// @Description 通过Excel文件导入返修入库单及相关信息
// @Tags 入库管理
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "Excel文件"
// @Param inbound_title formData string true "入库标题"
// @Param project formData string true "项目"
// @Param need_return formData bool false "是否需要返厂"
// @Success 200 {object} response.Response{data=RepairInboundResult} "导入成功"
// @Failure 400 {object} response.Response{msg=string} "请求参数错误"
// @Failure 500 {object} response.Response{msg=string} "服务器内部错误"
// @Router /cmdb/inbound/repair-import [put]
func (c *InboundController) CreateRepairInboundByImport(ctx *gin.Context) {
	var (
		ImportForm struct {
			File         *multipart.FileHeader `form:"file" binding:"required"`
			InboundTitle string                `form:"inbound_title" binding:"required"`
			Project      string                `form:"project" binding:"required"`
			//NeedReturn   bool                  `form:"need_return"`
		}
		result RepairInboundResult
	)

	if err := ctx.ShouldBind(&ImportForm); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	// 获取当前用户信息
	userID, userName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取用户信息失败: "+err.Error())
		return
	}

	// 调用service层处理Excel文件
	repairInbound, err := c.InboundSvc.ImportRepairInbound(ctx, ImportForm.File)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "导入失败: "+err.Error())
		return
	}

	// 设置创建人信息
	repairInbound.CreateBy = userName
	repairInbound.CreateID = userID

	// 调用service层创建返修入库单
	inboundNo, err := c.InboundSvc.CreateRepairInboundByInput(ctx, repairInbound, ImportForm.InboundTitle, ImportForm.Project)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	result.InboundNo = inboundNo
	response.Success(ctx, result, "导入返修入库单成功")
}

// CreateDismantledInboundByImportV1 通过Excel文件导入拆机入库单(V1版本)
// @Summary 通过Excel文件导入拆机入库单
// @Description 通过Excel文件导入拆机入库单及相关信息的V1版本
// @Tags 入库管理
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "Excel文件"
// @Param inbound_title formData string true "入库标题"
// @Param project formData string true "项目"
// @Success 200 {object} response.Response{msg=string} "导入成功"
// @Failure 400 {object} response.Response{msg=string} "请求参数错误"
// @Failure 500 {object} response.Response{msg=string} "服务器内部错误"
// @Router /cmdb/inbound/trans/dismantled-import [post]
func (c *InboundController) CreateDismantledInboundByImportV1(ctx *gin.Context) {
	var ImportForm struct {
		File         *multipart.FileHeader `form:"file" binding:"required"`
		InboundTitle string                `form:"inbound_title" binding:"required"`
		Project      string                `form:"project" binding:"required"`
		NeedReturn   bool                  `form:"need_return"`
	}

	if err := ctx.ShouldBind(&ImportForm); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	// 获取当前用户信息
	userID, userName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "获取用户信息失败: "+err.Error())
		return
	}

	// 调用service层处理Excel文件
	dismantledInbound, err := c.InboundSvc.ImportDismantledInbound(ctx, ImportForm.File)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "导入失败: "+err.Error())
		return
	}

	// 设置创建人信息
	dismantledInbound.CreateBy = userName
	dismantledInbound.CreateID = userID
	for _, detail := range dismantledInbound.Details {
		detail.NeedReturn = ImportForm.NeedReturn
	}

	// 创建拆机入库单
	inboundNo, err := c.InboundSvc.CreateDismantledInboundByInputV1(ctx, *dismantledInbound, ImportForm.InboundTitle, ImportForm.Project)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建拆机入库单失败: "+err.Error())
		return
	}

	response.Success(ctx, inboundNo, "导入拆机入库单成功")
}

type DeviceInboundDTO struct {
}

// UpdateDeviceInboundDetailsByInput 通过输入的方式更新整机入库详情
// @Summary 更新整机入库详情
// @Description 通过输入的方式更新整机入库单的详细信息
// @Tags 入库管理
// @Accept json
// @Produce json
// @Param request body []inbound.DeviceInboundDetail true "整机入库详情更新参数"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Failure 400 {object} response.Response{msg=string} "请求参数错误"
// @Failure 500 {object} response.Response{msg=string} "服务器内部错误"
// @Router /cmdb/inbound/device-input [put]
func (c *InboundController) UpdateDeviceInboundDetailsByInput(ctx *gin.Context) {
	var details []inbound.DeviceInboundDetail
	err := ctx.ShouldBindJSON(&details)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "绑定数据失败: "+err.Error())
		return
	}

	// 更新整机入库详情
	err = c.InboundSvc.UpdateDeviceInboundDetailsByInput(ctx, details)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新整机入库详情失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新整机入库详情成功")
}
