package asset

import (
	"backend/internal/modules/cmdb/model/asset"
	assetservice "backend/internal/modules/cmdb/service/asset"
	"backend/pkg/utils"
	"backend/response"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// NetworkDeviceController 网络设备控制器
type NetworkDeviceController struct {
	networkDeviceService assetservice.NetworkDeviceService
}

// NewNetworkDeviceController 创建网络设备控制器
func NewNetworkDeviceController(networkDeviceService assetservice.NetworkDeviceService) *NetworkDeviceController {
	return &NetworkDeviceController{
		networkDeviceService: networkDeviceService,
	}
}

// RegisterRoutes 注册路由
func (c *NetworkDeviceController) RegisterRoutes(r *gin.RouterGroup) {
	networkDevice := r.Group("/network-devices")
	{
		networkDevice.POST("", c.Create)
		networkDevice.POST("/with-device", c.CreateNetworkDeviceWithDevice) // 添加一站式创建路由
		networkDevice.PUT("/:id", c.Update)
		networkDevice.PUT("/with-device/:id", c.UpdateNetworkDeviceWithDevice) // 添加一站式更新路由
		networkDevice.DELETE("/:id", c.Delete)
		networkDevice.GET("/:id", c.GetByID)
		networkDevice.GET("/detail/:id", c.GetDetailByID)
		networkDevice.GET("/device/:deviceID", c.GetByDeviceID)
		networkDevice.GET("", c.List)
	}
}

// Create 创建网络设备
// @Summary 创建网络设备
// @Description 创建网络设备
// @Tags 网络设备
// @Accept json
// @Produce json
// @Param networkDevice body asset.NetworkDevice true "网络设备信息"
// @Success 200 {object} response.ResponseStruct
// @Router /cmdb/network-devices [post]
func (c *NetworkDeviceController) Create(ctx *gin.Context) {
	var networkDevice asset.NetworkDevice
	if err := ctx.ShouldBindJSON(&networkDevice); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	err := c.networkDeviceService.Create(ctx, &networkDevice)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建网络设备失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{"id": networkDevice.ID}, "创建网络设备成功")
}

// Update 更新网络设备
// @Summary 更新网络设备
// @Description 更新网络设备
// @Tags 网络设备
// @Accept json
// @Produce json
// @Param id path int true "网络设备ID"
// @Param networkDevice body asset.NetworkDevice true "网络设备信息"
// @Success 200 {object} response.ResponseStruct
// @Router /cmdb/network-devices/{id} [put]
func (c *NetworkDeviceController) Update(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "ID参数错误")
		return
	}

	var networkDevice asset.NetworkDevice
	if err := ctx.ShouldBindJSON(&networkDevice); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	networkDevice.ID = uint(id)
	err = c.networkDeviceService.Update(ctx, &networkDevice)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新网络设备失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新网络设备成功")
}

// Delete 删除网络设备
// @Summary 删除网络设备
// @Description 删除网络设备
// @Tags 网络设备
// @Accept json
// @Produce json
// @Param id path int true "网络设备ID"
// @Success 200 {object} response.ResponseStruct
// @Router /cmdb/network-devices/{id} [delete]
func (c *NetworkDeviceController) Delete(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "ID参数错误")
		return
	}

	err = c.networkDeviceService.Delete(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "删除网络设备失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "删除网络设备成功")
}

// GetByID 根据ID获取网络设备
// @Summary 根据ID获取网络设备
// @Description 根据ID获取网络设备
// @Tags 网络设备
// @Accept json
// @Produce json
// @Param id path int true "网络设备ID"
// @Success 200 {object} response.ResponseStruct{data=asset.NetworkDevice}
// @Router /cmdb/network-devices/{id} [get]
func (c *NetworkDeviceController) GetByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "ID参数错误")
		return
	}

	networkDevice, err := c.networkDeviceService.GetByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取网络设备失败: "+err.Error())
		return
	}

	response.Success(ctx, networkDevice, "获取网络设备成功")
}

// GetDetailByID 获取网络设备详情
// @Summary 获取网络设备详情（包含设备和资源信息）
// @Description 获取网络设备详情（包含设备和资源信息）
// @Tags 网络设备
// @Accept json
// @Produce json
// @Param id path int true "网络设备ID"
// @Success 200 {object} response.ResponseStruct{data=asset.NetworkDeviceWithDeviceInfo}
// @Router /cmdb/network-devices/detail/{id} [get]
func (c *NetworkDeviceController) GetDetailByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "ID参数错误")
		return
	}

	networkDevice, err := c.networkDeviceService.GetDetailByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取网络设备详情失败: "+err.Error())
		return
	}

	response.Success(ctx, networkDevice, "获取网络设备详情成功")
}

// GetByDeviceID 根据设备ID获取网络设备
// @Summary 根据设备ID获取网络设备
// @Description 根据设备ID获取网络设备
// @Tags 网络设备
// @Accept json
// @Produce json
// @Param deviceID path int true "设备ID"
// @Success 200 {object} response.ResponseStruct{data=asset.NetworkDevice}
// @Router /cmdb/network-devices/device/{deviceID} [get]
func (c *NetworkDeviceController) GetByDeviceID(ctx *gin.Context) {
	deviceID, err := strconv.ParseUint(ctx.Param("deviceID"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "设备ID参数错误")
		return
	}

	networkDevice, err := c.networkDeviceService.GetByDeviceID(ctx, uint(deviceID))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取网络设备失败: "+err.Error())
		return
	}

	response.Success(ctx, networkDevice, "获取网络设备成功")
}

// List 获取网络设备列表
// @Summary 获取网络设备列表
// @Description 获取网络设备列表
// @Tags 网络设备
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认20"
// @Param query query string false "查询关键字"
// @Param role query string false "网络设备角色"
// @Success 200 {object} response.ResponseStruct{data=[]asset.NetworkDeviceWithDeviceInfo}
// @Router /cmdb/network-devices [get]
func (c *NetworkDeviceController) List(ctx *gin.Context) {
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "20"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}
	query := ctx.Query("query")
	role := ctx.Query("role")
	// 解析其他查询参数
	params := make(map[string]interface{})
	assetType := ctx.Query("assetType")
	if assetType != "" {
		params["assetType"] = assetType
	}

	networkDevices, total, err := c.networkDeviceService.List(ctx, page, pageSize, query, role, params)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取网络设备列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  networkDevices,
		"total": total,
	}, "获取网络设备列表成功")
}

// CreateNetworkDeviceWithDevice 创建网络设备(包含设备基本信息)
// @Summary 一站式创建网络设备
// @Description 一次请求同时创建设备和网络设备信息
// @Tags 网络设备
// @Accept json
// @Produce json
// @Param data body object true "网络设备信息"
// @Success 200 {object} response.ResponseStruct
// @Router /cmdb/network-devices/with-device [post]
func (c *NetworkDeviceController) CreateNetworkDeviceWithDevice(ctx *gin.Context) {
	var req struct {
		// 设备基本信息
		SN             string  `json:"sn" binding:"required"`
		Brand          string  `json:"brand"`
		Model          string  `json:"model"`
		AssetType      string  `json:"assetType"`
		AssetStatus    string  `json:"assetStatus" binding:"required"`
		HardwareStatus string  `json:"hardwareStatus"`
		PurchaseOrder  string  `json:"purchaseOrder"`
		PurchaseDate   string  `json:"purchaseDate"`
		WarrantyExpire string  `json:"warrantyExpire"`
		Price          float64 `json:"price"`
		ResidualValue  float64 `json:"residualValue"`
		Remark         string  `json:"remark"`

		// 网络设备特有信息
		Role              string `json:"role" binding:"required"`
		FirmwareVersion   string `json:"firmwareVersion"`
		LoopbackAddress   string `json:"loopbackAddress"`
		ManagementAddress string `json:"managementAddress"`
		Ports             int    `json:"ports"`
		PortSpeed         string `json:"portSpeed"`
		StackSupport      bool   `json:"stackSupport"`
		StackID           int    `json:"stackID"`
		StackRole         string `json:"stackRole"`
		Layer             int    `json:"layer"`
		RoutingProtocols  string `json:"routingProtocols"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	// 验证网络设备角色是否有效
	if !asset.IsValidNetworkRole(req.Role) {
		response.Fail(ctx, http.StatusBadRequest, "无效的网络设备角色")
		return
	}

	// 验证资产状态是否有效
	if req.AssetStatus != "" && !asset.IsValidAssetStatus(req.AssetStatus) {
		response.Fail(ctx, http.StatusBadRequest, "无效的资产状态")
		return
	}

	// 验证资产类型是否有效
	if req.AssetType != "" && !asset.IsValidAssetType(req.AssetType) {
		response.Fail(ctx, http.StatusBadRequest, "无效的资产类型")
		return
	}

	// 如果资产类型为空，默认设置为network
	if req.AssetType == "" {
		req.AssetType = asset.AssetTypeNetwork
	}

	// 解析日期
	var purchaseDate, warrantyExpire utils.Date
	if req.PurchaseDate != "" {
		t, err := time.Parse("2006-01-02", req.PurchaseDate)
		if err != nil {
			response.Fail(ctx, http.StatusBadRequest, "购买日期格式错误，请使用YYYY-MM-DD格式")
			return
		}
		purchaseDate = utils.Date(t)
	}

	if req.WarrantyExpire != "" {
		t, err := time.Parse("2006-01-02", req.WarrantyExpire)
		if err != nil {
			response.Fail(ctx, http.StatusBadRequest, "过保日期格式错误，请使用YYYY-MM-DD格式")
			return
		}
		warrantyExpire = utils.Date(t)
	}

	// 1. 创建设备
	device := &asset.Device{
		SN:             req.SN,
		Brand:          req.Brand,
		Model:          req.Model,
		AssetType:      req.AssetType,
		AssetStatus:    req.AssetStatus,
		HardwareStatus: req.HardwareStatus,
		PurchaseOrder:  req.PurchaseOrder,
		PurchaseDate:   purchaseDate,
		WarrantyExpire: warrantyExpire,
		Price:          req.Price,
		ResidualValue:  req.ResidualValue,
		Remark:         req.Remark,
	}

	// 2. 创建网络设备
	networkDevice := &asset.NetworkDevice{
		Role:              req.Role,
		FirmwareVersion:   req.FirmwareVersion,
		LoopbackAddress:   req.LoopbackAddress,
		ManagementAddress: req.ManagementAddress,
		Ports:             req.Ports,
		PortSpeed:         req.PortSpeed,
		StackSupport:      req.StackSupport,
		StackID:           req.StackID,
		StackRole:         req.StackRole,
		Layer:             req.Layer,
		RoutingProtocols:  req.RoutingProtocols,
	}

	// 调用服务层方法创建记录
	err := c.networkDeviceService.CreateWithDevice(ctx, device, networkDevice)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建网络设备失败: "+err.Error())
		return
	}

	// 获取模板名称用于返回
	templateName := asset.GetPackageNameByRole(req.Role)

	response.Success(ctx, gin.H{
		"id":           networkDevice.ID,
		"deviceID":     device.ID,
		"role":         req.Role,
		"templateName": templateName,
	}, "创建网络设备成功")
}

// UpdateNetworkDeviceWithDevice 更新网络设备(包含设备基本信息)
// @Summary 一站式更新网络设备
// @Description 一次请求同时更新设备和网络设备信息
// @Tags 网络设备
// @Accept json
// @Produce json
// @Param id path int true "网络设备ID"
// @Param data body object true "网络设备信息"
// @Success 200 {object} response.ResponseStruct
// @Router /cmdb/network-devices/with-device/{id} [put]
func (c *NetworkDeviceController) UpdateNetworkDeviceWithDevice(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "ID参数错误")
		return
	}

	var req struct {
		// 设备基本信息
		SN             string  `json:"sn"`
		Brand          string  `json:"brand"`
		Model          string  `json:"model"`
		AssetType      string  `json:"assetType"`
		AssetStatus    string  `json:"assetStatus"`
		HardwareStatus string  `json:"hardwareStatus"`
		PurchaseOrder  string  `json:"purchaseOrder"`
		PurchaseDate   string  `json:"purchaseDate"`
		WarrantyExpire string  `json:"warrantyExpire"`
		Price          float64 `json:"price"`
		ResidualValue  float64 `json:"residualValue"`
		Remark         string  `json:"remark"`

		// 网络设备特有信息
		Role              string `json:"role" binding:"required"`
		FirmwareVersion   string `json:"firmwareVersion"`
		LoopbackAddress   string `json:"loopbackAddress"`
		ManagementAddress string `json:"managementAddress"`
		Ports             int    `json:"ports"`
		PortSpeed         string `json:"portSpeed"`
		StackSupport      bool   `json:"stackSupport"`
		StackID           int    `json:"stackID"`
		StackRole         string `json:"stackRole"`
		Layer             int    `json:"layer"`
		RoutingProtocols  string `json:"routingProtocols"`

		// 资源信息
		Project        string `json:"project"`
		CabinetID      uint   `json:"cabinetID"`
		RackPosition   int    `json:"rackPosition"`
		UnitHeight     int    `json:"unitHeight"`
		BizStatus      string `json:"bizStatus"`
		ResStatus      string `json:"resStatus"`
		RackingTime    string `json:"rackingTime"`
		DeliveryTime   string `json:"deliveryTime"`
		BmcIP          string `json:"bmcIP"`
		VpcIP          string `json:"vpcIP"`
		TenantIP       string `json:"tenantIP"`
		Cluster        string `json:"cluster"`
		IsBackup       bool   `json:"isBackup"`
		ResourceRemark string `json:"resourceRemark"`

		// 模板信息
		TemplateID   uint   `json:"templateID"`
		TemplateName string `json:"templateName"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	// 验证网络设备角色是否有效
	if !asset.IsValidNetworkRole(req.Role) {
		response.Fail(ctx, http.StatusBadRequest, "无效的网络设备角色")
		return
	}

	// 验证资产状态是否有效
	if req.AssetStatus != "" && !asset.IsValidAssetStatus(req.AssetStatus) {
		response.Fail(ctx, http.StatusBadRequest, "无效的资产状态")
		return
	}

	// 验证资产类型是否有效
	if req.AssetType != "" && !asset.IsValidAssetType(req.AssetType) {
		response.Fail(ctx, http.StatusBadRequest, "无效的资产类型")
		return
	}

	// 如果资产类型为空，默认设置为network
	if req.AssetType == "" {
		req.AssetType = asset.AssetTypeNetwork
	}

	// 解析日期
	var purchaseDate, warrantyExpire, rackingTime, deliveryTime utils.Date
	if req.PurchaseDate != "" {
		t, err := time.Parse("2006-01-02", req.PurchaseDate)
		if err != nil {
			response.Fail(ctx, http.StatusBadRequest, "购买日期格式错误，请使用YYYY-MM-DD格式")
			return
		}
		purchaseDate = utils.Date(t)
	}

	if req.WarrantyExpire != "" {
		t, err := time.Parse("2006-01-02", req.WarrantyExpire)
		if err != nil {
			response.Fail(ctx, http.StatusBadRequest, "过保日期格式错误，请使用YYYY-MM-DD格式")
			return
		}
		warrantyExpire = utils.Date(t)
	}

	// 解析资源相关日期
	if req.RackingTime != "" {
		t, err := time.Parse("2006-01-02", req.RackingTime)
		if err != nil {
			response.Fail(ctx, http.StatusBadRequest, "上架日期格式错误，请使用YYYY-MM-DD格式")
			return
		}
		rackingTime = utils.Date(t)
	}

	if req.DeliveryTime != "" {
		t, err := time.Parse("2006-01-02", req.DeliveryTime)
		if err != nil {
			response.Fail(ctx, http.StatusBadRequest, "交付日期格式错误，请使用YYYY-MM-DD格式")
			return
		}
		deliveryTime = utils.Date(t)
	}

	// 获取模板ID
	var templateID uint

	// 如果提供了templateName，优先使用templateName查找模板
	if req.TemplateName != "" {
		// 调用服务获取指定名称的模板
		machineTemplate, err := c.networkDeviceService.GetTemplateByName(ctx, req.TemplateName)
		if err != nil {
			response.Fail(ctx, http.StatusBadRequest, "找不到指定名称的模板: "+req.TemplateName)
			return
		}
		templateID = machineTemplate.ID
	} else if req.TemplateID > 0 {
		// 如果没有提供templateName但提供了templateID，则使用templateID
		templateID = req.TemplateID
	} else if req.Role != "" {
		// 如果只提供了角色，尝试通过角色获取默认模板
		packageName := asset.GetPackageNameByRole(req.Role)
		if packageName != "" {
			machineTemplate, err := c.networkDeviceService.GetTemplateByName(ctx, packageName)
			if err == nil && machineTemplate != nil {
				templateID = machineTemplate.ID
			}
		}
	}

	// 1. 设备信息
	device := &asset.Device{
		SN:             req.SN,
		Brand:          req.Brand,
		Model:          req.Model,
		AssetType:      req.AssetType,
		AssetStatus:    req.AssetStatus,
		HardwareStatus: req.HardwareStatus,
		PurchaseOrder:  req.PurchaseOrder,
		PurchaseDate:   purchaseDate,
		WarrantyExpire: warrantyExpire,
		Price:          req.Price,
		ResidualValue:  req.ResidualValue,
		Remark:         req.Remark,
		TemplateID:     templateID,
	}

	// 2. 网络设备信息
	networkDevice := &asset.NetworkDevice{
		Role:              req.Role,
		FirmwareVersion:   req.FirmwareVersion,
		LoopbackAddress:   req.LoopbackAddress,
		ManagementAddress: req.ManagementAddress,
		Ports:             req.Ports,
		PortSpeed:         req.PortSpeed,
		StackSupport:      req.StackSupport,
		StackID:           req.StackID,
		StackRole:         req.StackRole,
		Layer:             req.Layer,
		RoutingProtocols:  req.RoutingProtocols,
	}

	// 3. 资源信息
	var resource *asset.Resource = nil
	if req.BizStatus != "" || req.Project != "" || req.CabinetID > 0 || req.RackPosition > 0 {
		resource = &asset.Resource{
			Project:      req.Project,
			CabinetID:    req.CabinetID,
			RackPosition: req.RackPosition,
			Height:       req.UnitHeight,
			BizStatus:    req.BizStatus,
			ResStatus:    req.ResStatus,
			RackingTime:  rackingTime,
			DeliveryTime: deliveryTime,
			BmcIP:        req.BmcIP,
			VpcIP:        req.VpcIP,
			TenantIP:     req.TenantIP,
			Cluster:      req.Cluster,
			IsBackup:     req.IsBackup,
			Remark:       req.ResourceRemark,
		}
	}

	// 调用服务层方法更新记录
	err = c.networkDeviceService.UpdateWithDevice(ctx, uint(id), device, networkDevice, resource)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新网络设备失败: "+err.Error())
		return
	}

	// 获取模板名称用于返回
	templateName := asset.GetPackageNameByRole(req.Role)

	response.Success(ctx, gin.H{
		"id":           uint(id),
		"role":         req.Role,
		"templateName": templateName,
	}, "更新网络设备成功")
}
