package workflow

// 工单状态常量
const (
	StatusRejected = "rejected" //审核不通过
	// 备件出库阶段常量
	StatusWaitingApproval       = "waiting_approval"        // 待审批
	StatusWaitingSecondApproval = "waiting_second_approval" //待二次审批
	StatusOutbounding           = "outbounding"             // 出库中
	StatusCompleteOutbound      = "complete_outbound"       // 出库完成
	StatusCompleted             = "completed"
	StatusCancelled             = "cancelled" // 已取消

	// 备件出库常量（new）
	StatusWaitingEngineerApproval  = "waiting_engineer_approval"   // 等待专业工程师审核
	StatusEngineerApprovalPass     = "engineer_approval_pass"      // 专业工程师审核通过
	StatusEngineerApprovalFail     = "engineer_approval_fail"      // 专业工程师审核不通过
	StatusWaitingAssetApproval     = "waiting_asset_approval"      // 等待仓库管理员审核
	StatusAssetApprovalPass        = "asset_approval_pass"         // 仓库管理员审核通过
	StatusAssetApprovalFail        = "asset_approval_fail"         // 仓库管理员审核不通过
	StatusWaitingReplaceOperate    = "waiting_replace_operate"     // 等待改配负责人操作
	StatusReplaceApprovalPass      = "replace_approval_pass"       // 改配负责人通过
	StatusReplaceApprovalFail      = "replace_approval_fail"       // 改配负责人不通过
	StatusWaitingAssetDestApproval = "waiting_asset_dest_approval" // 等待收货方仓库管理员审核
	StatusWaitingBuyerApproval     = "waiting_buyer_approval"
	StatusBuyerApprovalPass        = "buyer_approval_pass"
	StatusBuyerApprovalFail        = "buyer_approval_fail"
	// #nosec G101
	StatusAssetDestApprovalPass = "asset_dest_approval_pass" // 收货方仓库管理员审核通过
	StatusAssetDestApprovalFail = "asset_dest_approval_fail" // 收货方仓库管理员审核拒绝

)

// 工作流阶段常量
const (
	// 出库
	StageCustomerApproval = "customer_approval" // 审批
	StageSecondApproval   = "second_approval"   // 二次审批
	StageStartOutbound    = "start_outbound"    // 开始出库
	StageCompleteOutbound = "complete_outbound" // 完成出库
	StageCancelled        = "cancelled"         // 取消工单

	StageCompleteTicket = "complete_ticket" // 完成工单

	StageRejected = "rejected" //审批驳回

	// 出库（new）
	StageAssetApproval    = "asset_approval"    // 仓库管理员审核
	StageEngineerApproval = "engineer_approval" // 专业工程师审核
	StageRefitApproval    = "refit_approval"    // 改配负责人审核

)

// 出库类型
const (
	OutboundTypePart   = "part"   // 配件
	OutboundTypeDevice = "device" // 整机
)

// 出库原因
const (
	Repair      = "repair"
	Sell        = "sell"
	Replacement = "replacement"
	Allocate    = "allocate"

	OutboundReasonRepair       = "repair"        // 维修
	OutboundReasonSell         = "sell"          // 售卖
	OutboundReasonReplace      = "replacement"   // 改配
	OutboundReasonAllocate     = "allocate"      // 调拨
	OutboundReasonReturnRepair = "return_repair" // 返修
	OutboundReasonRack         = "rack"          // 上架
)
