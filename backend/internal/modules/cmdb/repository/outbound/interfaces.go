package outbound

import (
	"backend/internal/modules/cmdb/model/outbound"
	"context"
)

// OutboundApprovalRepository 出库审批仓库接口
type OutboundApprovalRepository interface {
	// Create 创建客户审批记录
	Create(ctx context.Context, customerApproval *outbound.OutboundApproval) error
	// GetByTicketID 根据工单ID获取客户审批记录
	GetByTicketID(ctx context.Context, ticketID uint) (*outbound.OutboundApproval, error)

	GetByTicketNo(ctx context.Context, ticketNo string) (*outbound.OutboundApproval, error)
	// Update 更新客户审批记录
	Update(ctx context.Context, customerApproval *outbound.OutboundApproval) error
	// Delete 删除客户审批记录
	Delete(ctx context.Context, id uint) error
}
