package mysql

import (
	"backend/internal/modules/cmdb/model/outbound"
	outbound2 "backend/internal/modules/cmdb/repository/outbound"
	"context"

	"gorm.io/gorm"
)

type outboundApprovalRepository struct {
	db *gorm.DB
}

// NewOutboundApprovalRepository 创建新的客户审批仓库
func NewOutboundApprovalRepository(db *gorm.DB) outbound2.OutboundApprovalRepository {
	return &outboundApprovalRepository{db: db}
}

// Create 创建客户审批记录
func (r *outboundApprovalRepository) Create(ctx context.Context, customerApproval *outbound.OutboundApproval) error {
	return r.db.WithContext(ctx).Create(customerApproval).Error
}

// GetByTicketID 根据工单ID获取审批记录
func (r *outboundApprovalRepository) GetByTicketID(ctx context.Context, ticketID uint) (*outbound.OutboundApproval, error) {
	var approval outbound.OutboundApproval
	err := r.db.WithContext(ctx).Where("ticket_id = ?", ticketID).First(&approval).Error
	if err != nil {
		return nil, err
	}
	return &approval, nil
}

// GetByTicketID 根据工单ID获取审批记录
func (r *outboundApprovalRepository) GetByTicketNo(ctx context.Context, ticketNo string) (*outbound.OutboundApproval, error) {
	var approval outbound.OutboundApproval
	err := r.db.WithContext(ctx).Where("ticket_no = ?", ticketNo).First(&approval).Error
	if err != nil {
		return nil, err
	}
	return &approval, nil
}

// Update 更新审批记录
func (r *outboundApprovalRepository) Update(ctx context.Context, customerApproval *outbound.OutboundApproval) error {
	return r.db.WithContext(ctx).Save(customerApproval).Error
}

// Delete 删除审批记录
func (r *outboundApprovalRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&outbound.OutboundApproval{}, id).Error
}
