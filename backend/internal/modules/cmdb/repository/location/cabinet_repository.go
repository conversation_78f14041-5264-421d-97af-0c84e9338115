package location

import (
	"backend/internal/modules/cmdb/model/location"
	"context"
	"errors"

	"gorm.io/gorm"
)

// CabinetRepository 机柜仓库接口
type CabinetRepository interface {
	Create(ctx context.Context, cabinet *location.Cabinet) error
	Update(ctx context.Context, cabinet *location.Cabinet) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*location.Cabinet, error)
	GetByName(ctx context.Context, name string) (*location.Cabinet, error)
	GetByRoomID(ctx context.Context, roomID uint) ([]*location.Cabinet, error)
	List(ctx context.Context, page, pageSize int, query string, roomID uint) ([]*location.Cabinet, int64, error)
}

// cabinetRepository 机柜仓库实现
type cabinetRepository struct {
	db *gorm.DB
}

// NewCabinetRepository 创建机柜仓库
func NewCabinetRepository(db *gorm.DB) CabinetRepository {
	return &cabinetRepository{db: db}
}

// Create 创建机柜
func (r *cabinetRepository) Create(ctx context.Context, cabinet *location.Cabinet) error {
	return r.db.WithContext(ctx).Create(cabinet).Error
}

// Update 更新机柜
func (r *cabinetRepository) Update(ctx context.Context, cabinet *location.Cabinet) error {
	var original location.Cabinet
	if err := r.db.WithContext(ctx).First(&original, cabinet.ID).Error; err != nil {
		return err
	}
	cabinet.CreatedAt = original.CreatedAt
	return r.db.WithContext(ctx).Save(cabinet).Error
}

// Delete 删除机柜
func (r *cabinetRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&location.Cabinet{}, id).Error
}

// GetByID 根据ID获取机柜
func (r *cabinetRepository) GetByID(ctx context.Context, id uint) (*location.Cabinet, error) {
	var cabinet location.Cabinet
	if err := r.db.WithContext(ctx).Preload("Room.DataCenter.AZ.Region").First(&cabinet, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("机柜不存在")
		}
		return nil, err
	}
	return &cabinet, nil
}

// GetByName 根据名称获取机柜
func (r *cabinetRepository) GetByName(ctx context.Context, name string) (*location.Cabinet, error) {
	var cabinet location.Cabinet
	if err := r.db.WithContext(ctx).Preload("Room.DataCenter.AZ.Region").Where("name = ?", name).First(&cabinet).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("机柜不存在")
		}
		return nil, err
	}
	return &cabinet, nil
}

// GetByRoomID 根据房间ID获取机柜列表
func (r *cabinetRepository) GetByRoomID(ctx context.Context, roomID uint) ([]*location.Cabinet, error) {
	var cabinets []*location.Cabinet
	if err := r.db.WithContext(ctx).Where("room_id = ?", roomID).Find(&cabinets).Error; err != nil {
		return nil, err
	}
	return cabinets, nil
}

// List 分页查询机柜列表
func (r *cabinetRepository) List(ctx context.Context, page, pageSize int, query string, roomID uint) ([]*location.Cabinet, int64, error) {
	var cabinets []*location.Cabinet
	var total int64

	db := r.db.WithContext(ctx).Model(&location.Cabinet{}).Preload("Room.DataCenter.AZ.Region")

	if query != "" {
		db = db.Where("name LIKE ? OR description LIKE ?", "%"+query+"%", "%"+query+"%")
	}

	if roomID > 0 {
		db = db.Where("room_id = ?", roomID)
	}

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		db = db.Offset(offset).Limit(pageSize)
	}

	if err := db.Find(&cabinets).Error; err != nil {
		return nil, 0, err
	}

	return cabinets, total, nil
}
