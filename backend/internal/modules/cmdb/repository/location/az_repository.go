// repository/location/az_repository.go
package location

import (
	"backend/internal/modules/cmdb/model/location"
	"context"
	"errors"

	"gorm.io/gorm"
)

// AZRepository 可用区仓库接口
type AZRepository interface {
	Create(ctx context.Context, az *location.AZ) error
	Update(ctx context.Context, az *location.AZ) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*location.AZ, error)
	GetByName(ctx context.Context, name string) (*location.AZ, error)
	GetByRegionID(ctx context.Context, regionID uint) ([]*location.AZ, error)
	List(ctx context.Context, page, pageSize int, query string, regionID int) ([]*location.AZ, int64, error)
}

// azRepository 可用区仓库实现
type azRepository struct {
	db *gorm.DB
}

// NewAZRepository 创建可用区仓库
func NewAZRepository(db *gorm.DB) AZRepository {
	return &azRepository{db: db}
}

// Create 创建可用区
func (r *azRepository) Create(ctx context.Context, az *location.AZ) error {
	return r.db.WithContext(ctx).Create(az).Error
}

// Update 更新可用区
func (r *azRepository) Update(ctx context.Context, az *location.AZ) error {
	// 获取原始记录
	var original location.AZ
	if err := r.db.WithContext(ctx).First(&original, az.ID).Error; err != nil {
		return err
	}

	// 保留原始的创建时间
	az.CreatedAt = original.CreatedAt

	// 使用 Select 指定要更新的字段，排除 created_at
	return r.db.WithContext(ctx).Model(az).Select("*").Omit("created_at").Updates(az).Error
}

// Delete 删除可用区
func (r *azRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&location.AZ{}, id).Error
}

// GetByID 根据ID获取可用区
func (r *azRepository) GetByID(ctx context.Context, id uint) (*location.AZ, error) {
	var az location.AZ
	if err := r.db.WithContext(ctx).Preload("Region").First(&az, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("可用区不存在")
		}
		return nil, err
	}
	return &az, nil
}

// GetByName 根据名称获取可用区
func (r *azRepository) GetByName(ctx context.Context, name string) (*location.AZ, error) {
	var az location.AZ
	if err := r.db.WithContext(ctx).Preload("Region").Where("name = ?", name).First(&az).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("可用区不存在")
		}
		return nil, err
	}
	return &az, nil
}

// GetByRegionID 根据区域ID获取可用区列表
func (r *azRepository) GetByRegionID(ctx context.Context, regionID uint) ([]*location.AZ, error) {
	var azs []*location.AZ
	if err := r.db.WithContext(ctx).Where("region_id = ?", regionID).Find(&azs).Error; err != nil {
		return nil, err
	}
	return azs, nil
}

// List 分页查询可用区列表
func (r *azRepository) List(ctx context.Context, page, pageSize int, query string, regionID int) ([]*location.AZ, int64, error) {
	var azs []*location.AZ
	var total int64

	db := r.db.WithContext(ctx).Model(&location.AZ{}).Preload("Region")

	if query != "" {
		db = db.Where("name LIKE ? OR description LIKE ?", "%"+query+"%", "%"+query+"%")
	}

	if regionID > 0 {
		db = db.Where("region_id = ?", regionID)
	}

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		db = db.Offset(offset).Limit(pageSize)
	}

	if err := db.Find(&azs).Error; err != nil {
		return nil, 0, err
	}

	return azs, total, nil
}
