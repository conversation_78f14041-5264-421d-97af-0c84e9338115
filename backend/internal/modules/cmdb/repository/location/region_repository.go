// repository/location/region_repository.go
package location

import (
	"backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/cmdb/model/location"
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"
)

// RegionRepository 区域仓库接口
type RegionRepository interface {
	Create(ctx context.Context, region *location.Region) error
	Update(ctx context.Context, region *location.Region) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*location.Region, error)
	GetByName(ctx context.Context, name string) (*location.Region, error)
	List(ctx context.Context, page, pageSize int, query string) ([]*location.Region, int64, error)
	GetWarehousesByProject(ctx context.Context, project string) ([]asset.Warehouse, error)
}

// regionRepository 区域仓库实现
type regionRepository struct {
	db *gorm.DB
}

// NewRegionRepository 创建区域仓库
func NewRegionRepository(db *gorm.DB) RegionRepository {
	return &regionRepository{db: db}
}

// Create 创建区域
func (r *regionRepository) Create(ctx context.Context, region *location.Region) error {
	return r.db.WithContext(ctx).Create(region).Error
}

// Update 更新区域
func (r *regionRepository) Update(ctx context.Context, region *location.Region) error {
	// 只更新特定字段，避免更新 created_at 和其他不需要更新的字段
	return r.db.WithContext(ctx).Model(region).
		Select("name", "status", "description", "updated_at").
		Updates(map[string]interface{}{
			"name":        region.Name,
			"status":      region.Status,
			"description": region.Description,
			"updated_at":  gorm.Expr("NOW()"),
		}).Error
}

// Delete 删除区域
func (r *regionRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&location.Region{}, id).Error
}

// GetByID 根据ID获取区域
func (r *regionRepository) GetByID(ctx context.Context, id uint) (*location.Region, error) {
	var region location.Region
	if err := r.db.WithContext(ctx).First(&region, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("区域不存在")
		}
		return nil, err
	}
	return &region, nil
}

// GetByName 根据名称获取区域
func (r *regionRepository) GetByName(ctx context.Context, name string) (*location.Region, error) {
	var region location.Region
	if err := r.db.WithContext(ctx).Where("name = ?", name).First(&region).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("区域不存在")
		}
		return nil, err
	}
	return &region, nil
}

// List 分页查询区域列表
func (r *regionRepository) List(ctx context.Context, page, pageSize int, query string) ([]*location.Region, int64, error) {
	var regions []*location.Region
	var total int64

	db := r.db.WithContext(ctx).Model(&location.Region{})

	if query != "" {
		db = db.Where("name LIKE ? OR description LIKE ?", "%"+query+"%", "%"+query+"%")
	}

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		db = db.Offset(offset).Limit(pageSize)
	}

	if err := db.Find(&regions).Error; err != nil {
		return nil, 0, err
	}

	return regions, total, nil
}

func (r *regionRepository) GetWarehousesByProject(ctx context.Context, project string) ([]asset.Warehouse, error) {
	var warehouses []asset.Warehouse
	err := r.db.Model(&asset.Warehouse{}).
		Joins("JOIN rooms ON warehouses.room_id = rooms.id").
		Joins("JOIN data_centers ON rooms.data_center_id = data_centers.id").
		Joins("JOIN azs ON data_centers.az_id = azs.id").
		Joins("JOIN regions ON azs.region_id = regions.id").
		Where("regions.name = ?", project).
		Find(&warehouses).Error
	if err != nil {
		return nil, fmt.Errorf("获取项目关联仓库失败:%w", err)
	}
	return warehouses, nil
}
