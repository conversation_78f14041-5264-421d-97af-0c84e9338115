package location

import (
	"backend/internal/modules/cmdb/model/location"
	"context"
	"errors"

	"gorm.io/gorm"
)

// DataCenterRepository 机房仓库接口
type DataCenterRepository interface {
	Create(ctx context.Context, dataCenter *location.DataCenter) error
	Update(ctx context.Context, dataCenter *location.DataCenter) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*location.DataCenter, error)
	GetByName(ctx context.Context, name string) (*location.DataCenter, error)
	GetByAZID(ctx context.Context, azID uint) ([]*location.DataCenter, error)
	List(ctx context.Context, page, pageSize int, query string) ([]*location.DataCenter, int64, error)
}

// dataCenterRepository 机房仓库实现
type dataCenterRepository struct {
	db *gorm.DB
}

// NewDataCenterRepository 创建机房仓库
func NewDataCenterRepository(db *gorm.DB) DataCenterRepository {
	return &dataCenterRepository{db: db}
}

// Create 创建机房
func (r *dataCenterRepository) Create(ctx context.Context, dataCenter *location.DataCenter) error {
	return r.db.WithContext(ctx).Create(dataCenter).Error
}

// Update 更新机房
func (r *dataCenterRepository) Update(ctx context.Context, dataCenter *location.DataCenter) error {
	var original location.DataCenter
	if err := r.db.WithContext(ctx).First(&original, dataCenter.ID).Error; err != nil {
		return err
	}
	dataCenter.CreatedAt = original.CreatedAt
	return r.db.WithContext(ctx).Save(dataCenter).Error
}

// Delete 删除机房
func (r *dataCenterRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&location.DataCenter{}, id).Error
}

// GetByID 根据ID获取机房
func (r *dataCenterRepository) GetByID(ctx context.Context, id uint) (*location.DataCenter, error) {
	var dataCenter location.DataCenter
	if err := r.db.WithContext(ctx).Preload("AZ.Region").First(&dataCenter, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("机房不存在")
		}
		return nil, err
	}
	return &dataCenter, nil
}

// GetByName 根据名称获取机房
func (r *dataCenterRepository) GetByName(ctx context.Context, name string) (*location.DataCenter, error) {
	var dataCenter location.DataCenter
	if err := r.db.WithContext(ctx).Preload("AZ.Region").Where("name = ?", name).First(&dataCenter).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("机房不存在")
		}
		return nil, err
	}
	return &dataCenter, nil
}

// GetByAZID 根据可用区ID获取机房列表
func (r *dataCenterRepository) GetByAZID(ctx context.Context, azID uint) ([]*location.DataCenter, error) {
	var dataCenters []*location.DataCenter
	if err := r.db.WithContext(ctx).Where("az_id = ?", azID).Find(&dataCenters).Error; err != nil {
		return nil, err
	}
	return dataCenters, nil
}

// List 分页查询机房列表
func (r *dataCenterRepository) List(ctx context.Context, page, pageSize int, query string) ([]*location.DataCenter, int64, error) {
	var dataCenters []*location.DataCenter
	var total int64

	db := r.db.WithContext(ctx).Model(&location.DataCenter{}).Preload("AZ.Region")

	if query != "" {
		db = db.Where("name LIKE ? OR address LIKE ? OR description LIKE ?", "%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		db = db.Offset(offset).Limit(pageSize)
	}

	if err := db.Find(&dataCenters).Error; err != nil {
		return nil, 0, err
	}

	return dataCenters, total, nil
}
