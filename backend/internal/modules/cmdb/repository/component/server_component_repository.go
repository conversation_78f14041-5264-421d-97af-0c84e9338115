package component

import (
	"backend/internal/modules/cmdb/model/asset"
	componentModel "backend/internal/modules/cmdb/model/component"
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm/clause"

	"gorm.io/gorm"
)

// ServerComponentRepository 服务器组件仓库接口
type ServerComponentRepository interface {
	Create(ctx context.Context, component *componentModel.ServerComponent) error
	Update(ctx context.Context, component *componentModel.ServerComponent) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*componentModel.ServerComponent, error)
	GetBySN(ctx context.Context, sn string) (*componentModel.ServerComponent, error)
	ListByServerID(ctx context.Context, serverID uint) ([]*componentModel.ServerComponent, error)
	List(ctx context.Context, page, pageSize int, query string, componentType string) ([]*componentModel.ServerComponent, int64, error)

	// 新增方法
	GetComponentWithDetails(ctx context.Context, id uint) (*componentModel.ServerComponentWithDetails, error)
	GetComponentStatistics(ctx context.Context, componentType string) (*componentModel.ComponentStatistics, error)
	ListWithDetails(ctx context.Context, page, pageSize int, query string, componentType string) ([]*componentModel.ServerComponentWithDetails, int64, error)
	ListBySNs(ctx context.Context, sns []string) ([]*componentModel.ServerComponent, error)
	// 批量方法
	BatchInsert(ctx context.Context, components []*componentModel.ServerComponent) error
	// 事务支持
	GetDB() *gorm.DB
	WithTx(tx *gorm.DB) ServerComponentRepository
}

// serverComponentRepository 服务器组件仓库实现
type serverComponentRepository struct {
	db *gorm.DB
}

// NewServerComponentRepository 创建服务器组件仓库
func NewServerComponentRepository(db *gorm.DB) ServerComponentRepository {
	return &serverComponentRepository{db: db}
}

// GetDB 获取数据库实例
func (r *serverComponentRepository) GetDB() *gorm.DB {
	return r.db
}

// WithTx 使用事务
func (r *serverComponentRepository) WithTx(tx *gorm.DB) ServerComponentRepository {
	if tx == nil {
		return r
	}
	return &serverComponentRepository{db: tx}
}

// Create 创建服务器组件
func (r *serverComponentRepository) Create(ctx context.Context, component *componentModel.ServerComponent) error {
	return r.db.WithContext(ctx).Create(component).Error
}

// Update 更新服务器组件
func (r *serverComponentRepository) Update(ctx context.Context, component *componentModel.ServerComponent) error {
	var original componentModel.ServerComponent
	if err := r.db.WithContext(ctx).First(&original, component.ID).Error; err != nil {
		return err
	}
	component.CreatedAt = original.CreatedAt
	return r.db.WithContext(ctx).Save(component).Error
}

// Delete 删除服务器组件
func (r *serverComponentRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&componentModel.ServerComponent{}, id).Error
}

// GetByID 根据ID获取服务器组件
func (r *serverComponentRepository) GetByID(ctx context.Context, id uint) (*componentModel.ServerComponent, error) {
	var component componentModel.ServerComponent
	if err := r.db.WithContext(ctx).Preload("Server").Preload("Product").First(&component, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("服务器组件不存在")
		}
		return nil, err
	}
	return &component, nil
}

// GetBySN 根据SN获取服务器组件
func (r *serverComponentRepository) GetBySN(ctx context.Context, sn string) (*componentModel.ServerComponent, error) {
	var component componentModel.ServerComponent
	if err := r.db.WithContext(ctx).Preload("Server").Preload("Product").Where("sn = ?", sn).First(&component).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("服务器组件不存在")
		}
		return nil, err
	}
	return &component, nil
}

// ListByServerID 根据服务器ID获取组件列表
func (r *serverComponentRepository) ListByServerID(ctx context.Context, serverID uint) ([]*componentModel.ServerComponent, error) {
	var components []*componentModel.ServerComponent
	if err := r.db.WithContext(ctx).Preload("Product").Where("server_id = ?", serverID).Find(&components).Error; err != nil {
		return nil, err
	}
	return components, nil
}

// List 分页查询服务器组件列表
func (r *serverComponentRepository) List(ctx context.Context, page, pageSize int, query string, componentType string) ([]*componentModel.ServerComponent, int64, error) {
	var components []*componentModel.ServerComponent
	var total int64

	db := r.db.WithContext(ctx).Model(&componentModel.ServerComponent{}).Preload("Server").Preload("Product")

	if query != "" {
		db = db.Where("sn LIKE ? OR model LIKE ? OR pn LIKE ? OR firmware_version LIKE ? OR description LIKE ?",
			"%"+query+"%", "%"+query+"%", "%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	if componentType != "" {
		db = db.Where("component_type = ?", componentType)
	}

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		db = db.Offset(offset).Limit(pageSize)
	}

	if err := db.Find(&components).Error; err != nil {
		return nil, 0, err
	}

	return components, total, nil
}

// ListBySNs 根据SN列表列出服务器组件信息
func (r *serverComponentRepository) ListBySNs(ctx context.Context, sns []string) ([]*componentModel.ServerComponent, error) {
	var components []*componentModel.ServerComponent
	result := r.db.WithContext(ctx).
		Where("sn IN ?", sns).
		Find(&components)

	// 处理查询错误
	if result.Error != nil {
		return nil, fmt.Errorf("查询服务器组件失败: %w", result.Error)
	}

	return components, nil
}

// GetComponentWithDetails 获取组件详情，包含关联信息
func (r *serverComponentRepository) GetComponentWithDetails(ctx context.Context, id uint) (*componentModel.ServerComponentWithDetails, error) {
	comp, err := r.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	result := &componentModel.ServerComponentWithDetails{
		ServerComponent: *comp,
	}

	// 设置服务器信息
	if comp.ServerID > 0 && comp.Server.ID > 0 {
		result.ServerInfo = componentModel.ServerInfo{
			SN:    comp.Server.SN,
			Brand: comp.Server.Brand,
			Model: comp.Server.Model,
		}
	}

	// 库存信息将由服务层注入，这里只提供一个空的结构
	result.InventoryDetail = componentModel.InventoryDetail{
		TotalStock:  0,
		InUseCount:  0,
		IdleCount:   0,
		GoodCount:   0,
		DefectCount: 0,
	}

	return result, nil
}

// ListWithDetails 获取带详情的组件列表
func (r *serverComponentRepository) ListWithDetails(ctx context.Context, page, pageSize int, query string, componentType string) ([]*componentModel.ServerComponentWithDetails, int64, error) {
	components, total, err := r.List(ctx, page, pageSize, query, componentType)
	if err != nil {
		return nil, 0, err
	}

	// 创建结果切片
	result := make([]*componentModel.ServerComponentWithDetails, 0, len(components))

	// 组装详细信息，库存信息将由服务层注入
	for _, comp := range components {
		detailedComp := &componentModel.ServerComponentWithDetails{
			ServerComponent: *comp,
		}

		// 添加服务器信息
		if comp.ServerID > 0 && comp.Server.ID > 0 {
			detailedComp.ServerInfo = componentModel.ServerInfo{
				SN:    comp.Server.SN,
				Brand: comp.Server.Brand,
				Model: comp.Server.Model,
			}
		}

		// 初始化空的库存详情
		detailedComp.InventoryDetail = componentModel.InventoryDetail{
			TotalStock:  0,
			InUseCount:  0,
			IdleCount:   0,
			GoodCount:   0,
			DefectCount: 0,
		}

		result = append(result, detailedComp)
	}

	return result, total, nil
}

// GetComponentStatistics 获取组件统计信息
func (r *serverComponentRepository) GetComponentStatistics(ctx context.Context, componentType string) (*componentModel.ComponentStatistics, error) {
	result := &componentModel.ComponentStatistics{
		ByType:   make(map[string]int),
		ByStatus: make(map[string]int),
		ByServer: make(map[string]int),
	}

	// 构建查询条件
	query := r.db.WithContext(ctx).Model(&componentModel.ServerComponent{})
	if componentType != "" {
		query = query.Where("component_type = ?", componentType)
	}

	// 获取总数
	var totalCount int64
	query.Count(&totalCount)
	result.TotalComponents = int(totalCount)

	// 按类型统计
	var typeStats []struct {
		ComponentType string
		Count         int
	}
	r.db.WithContext(ctx).Model(&componentModel.ServerComponent{}).
		Select("component_type, count(*) as count").
		Group("component_type").
		Find(&typeStats)

	for _, stat := range typeStats {
		result.ByType[stat.ComponentType] = stat.Count
	}

	// 按状态统计
	var statusStats []struct {
		Status string
		Count  int
	}
	r.db.WithContext(ctx).Model(&componentModel.ServerComponent{}).
		Select("status, count(*) as count").
		Group("status").
		Find(&statusStats)

	for _, stat := range statusStats {
		result.ByStatus[stat.Status] = stat.Count
	}

	// 按服务器统计
	var serverStats []struct {
		ServerID uint
		SN       string
		Count    int
	}
	r.db.WithContext(ctx).Model(&componentModel.ServerComponent{}).
		Select("server_components.server_id, asset_devices.sn, count(*) as count").
		Joins("left join asset_devices on server_components.server_id = asset_devices.id").
		Where("server_components.server_id > 0").
		Group("server_components.server_id, asset_devices.sn").
		Find(&serverStats)

	for _, stat := range serverStats {
		result.ByServer[stat.SN] = stat.Count
	}

	// 获取库存统计，不再使用GetInventoryStatistics方法
	var stats componentModel.InventoryStatistics

	if componentType != "" {
		// 如果指定了组件类型，进行特定组件类型的统计
		var productID uint
		r.db.WithContext(ctx).Model(&componentModel.ServerComponent{}).
			Where("component_type = ?", componentType).
			Select("product_id").
			First(&productID)

		if productID > 0 {
			// 直接从inventory_details表查询总库存
			r.db.WithContext(ctx).Table("inventory_details").
				Where("product_id = ? AND status = 'active'", productID).
				Select("SUM(current_stock) as total_stock, SUM(allocated_stock) as allocated_stock").
				Scan(&stats)

			// 获取特定产品的在用组件数量
			var inUseCount int64
			r.db.WithContext(ctx).Model(&componentModel.ServerComponent{}).
				Where("product_id = ? AND server_id > 0", productID).
				Count(&inUseCount)
			stats.InUseCount = int(inUseCount)

			// 获取好件数量
			var goodCount int64
			r.db.WithContext(ctx).Model(&componentModel.ServerComponent{}).
				Where("product_id = ? AND status = ?", productID, asset.HardwareStatusNormal).
				Count(&goodCount)
			stats.GoodCount = int(goodCount)

			// 获取坏件数量
			var defectCount int64
			r.db.WithContext(ctx).Model(&componentModel.ServerComponent{}).
				Where("product_id = ? AND status = ?", productID, asset.HardwareStatusFaulty).
				Count(&defectCount)
			stats.DefectCount = int(defectCount)

			// 闲置数量 = 总库存 - 在用数量
			stats.IdleCount = stats.TotalStock - stats.InUseCount
			if stats.IdleCount < 0 {
				stats.IdleCount = 0
			}

			// 已分配数量
			stats.AllocatedCount = stats.AllocatedStock
		}
	} else {
		// 如果没有指定组件类型，获取总库存统计
		// 获取总库存 (假设从产品库存表获取)
		r.db.WithContext(ctx).Table("products").
			Joins("inner join inventory_details on products.id = inventory_details.product_id").
			Select("sum(inventory_details.current_stock) as total_stock, sum(inventory_details.allocated_stock) as allocated_stock").
			Scan(&stats)

		// 获取在用数量
		var inUseCount int64
		r.db.WithContext(ctx).Model(&componentModel.ServerComponent{}).
			Where("server_id > 0").
			Count(&inUseCount)
		stats.InUseCount = int(inUseCount)

		// 获取闲置数量
		stats.IdleCount = stats.TotalStock - stats.InUseCount
		if stats.IdleCount < 0 {
			stats.IdleCount = 0
		}

		// 获取好件数量
		var goodCount int64
		r.db.WithContext(ctx).Model(&componentModel.ServerComponent{}).
			Where("status = ?", asset.HardwareStatusNormal).
			Count(&goodCount)
		stats.GoodCount = int(goodCount)

		// 获取坏件数量
		var defectCount int64
		r.db.WithContext(ctx).Model(&componentModel.ServerComponent{}).
			Where("status = ?", asset.HardwareStatusFaulty).
			Count(&defectCount)
		stats.DefectCount = int(defectCount)

		// 已分配数量
		stats.AllocatedCount = stats.AllocatedStock
	}

	result.Inventory = stats

	return result, nil
}

// BatchInsert 批量插入（存在重复sn则更新其他信息）
func (r *serverComponentRepository) BatchInsert(ctx context.Context, components []*componentModel.ServerComponent) error {
	if len(components) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).Clauses(
		clause.OnConflict{
			Columns: []clause.Column{{Name: "sn"}}, // 冲突检测列
			DoUpdates: clause.AssignmentColumns([]string{"server_id", "product_id", "component_type",
				"model", "pn", "firmware_version", "status", "description", "is_active"}), // 更新字段
		},
	).Create(&components).Error
}
