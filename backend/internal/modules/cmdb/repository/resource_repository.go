package repository

import (
	"context"

	"gorm.io/gorm"
)

// ResourceRepository 资源仓库接口
type ResourceRepository interface {
	// 获取项目列表
	GetProjectList(ctx context.Context) ([]string, error)
}

// resourceRepository 资源仓库实现
type resourceRepository struct {
	db *gorm.DB
}

// NewResourceRepository 创建资源仓库
func NewResourceRepository(db *gorm.DB) ResourceRepository {
	return &resourceRepository{db: db}
}

// GetProjectList 获取项目列表
func (r *resourceRepository) GetProjectList(ctx context.Context) ([]string, error) {
	var projects []string

	// 查询所有不为空的唯一项目名称
	err := r.db.WithContext(ctx).
		Table("resources").
		Select("DISTINCT project").
		Where("project != ''").
		Pluck("project", &projects).
		Error

	if err != nil {
		return nil, err
	}

	return projects, nil
}
