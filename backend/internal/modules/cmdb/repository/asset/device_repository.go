package asset

import (
	"backend/internal/common/utils"
	"backend/internal/modules/cmdb/model/asset"
	"context"
	"errors"
	"fmt"
	"strings"

	"gorm.io/gorm"
)

// DeviceRepository 资产设备仓库接口
type DeviceRepository interface {
	// 基本设备操作
	Create(ctx context.Context, device *asset.Device) error
	Update(ctx context.Context, device *asset.Device) error
	UpdateDevices(ctx context.Context, devices []asset.Device) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*asset.Device, error)
	GetBySN(ctx context.Context, sn string) (*asset.Device, error)
	GetIdleBySN(ctx context.Context, sn string) (*asset.Device, error)
	GetDeviceSpares(ctx context.Context, sns []string) (map[string][]asset.AssetSpare, error)
	List(ctx context.Context, page, pageSize int, query, assetStatus, assetType string) ([]*asset.Device, int64, error)

	// 高级设备资源操作
	CreateWithResource(ctx context.Context, device *asset.Device) (*asset.Device, error)
	UpdateWithResource(ctx context.Context, device *asset.Device) (*asset.Device, error)
	DeleteWithResource(ctx context.Context, id uint) error
	GetByIDWithResource(ctx context.Context, id uint) (*asset.Device, error)
	ListWithResource(ctx context.Context, page, pageSize int, query, assetStatus, assetType, bizStatus, project, ip string) ([]*asset.Device, int64, error)
	GetDeviceTemplate(ctx context.Context, deviceID uint, withComponents bool) (interface{}, error)
	GetDeviceWarehouse(ctx context.Context, deviceID uint) (*asset.Warehouse, error)

	// 扩展查询功能
	ListDeviceResourcesExtended(
		ctx context.Context,
		page, pageSize int,
		query, hostname, brand, model string,
		assetStatus, assetType, bizStatus, project, cluster string,
		vpcIP, bmcIP, tenantIP string,
		isBackup *bool,
	) ([]*asset.Device, int64, error)

	GetDB() *gorm.DB
	WithTx(tx *gorm.DB) DeviceRepository

	// ListBySns 根据Sns列表查询 不preload深层结果
	ListByIds(ctx context.Context, ids []uint) ([]*asset.Device, int64, error)
	ListBySns(ctx context.Context, sns []string) ([]*asset.Device, int64, error)
	ListBySnsV2(ctx context.Context, sns []string, deep uint) ([]asset.Device, int64, error)
	GetDeviceAmount(ctx context.Context, req []asset.DeviceAmountReq) ([]asset.DeviceAmountRes, error)
}

// deviceRepository 资产设备仓库实现
type deviceRepository struct {
	db *gorm.DB
}

// GetDeviceAmount 获取设备数量
func (r *deviceRepository) GetDeviceAmount(ctx context.Context, reqs []asset.DeviceAmountReq) ([]asset.DeviceAmountRes, error) {
	var (
		res    []asset.DeviceAmountRes
		roomID uint
	)
	if err := r.db.Model(&asset.Warehouse{}).Select("room_id").Where("id = ?", reqs[0].WarehouseID).First(&roomID).Error; err != nil {
		return nil, fmt.Errorf("获取仓库房间ID失败: %w", err)
	}
	for _, req := range reqs {
		db := r.db.WithContext(ctx).Model(&asset.Device{}).Joins("left join resources on resources.asset_id = asset_devices.id")
		db = db.Where("resources.room_id = ?", roomID)
		db = db.Where("template_id = ?", req.TemplateID)
		if req.AssetStatus != "" {
			db = db.Where("asset_status = ?", req.AssetStatus)
		}
		if req.AssetType != "" {
			db = db.Where("asset_type = ?", req.AssetType)
		}
		if req.Project != "" {
			db = db.Where("resources.project = ?", req.Project)
		}

		var count int64
		err := db.Count(&count).Error
		if err != nil {
			return nil, fmt.Errorf("获取设备数量失败: %w", err)
		}
		res = append(res, asset.DeviceAmountRes{
			TemplateID: req.TemplateID,
			Amount:     int(count),
			Project:    req.Project,
		})
	}

	return res, nil
}

// NewDeviceRepository 创建资产设备仓库
func NewDeviceRepository(db *gorm.DB) DeviceRepository {
	return &deviceRepository{db: db}
}

// GetDB 获取数据库连接
func (r *deviceRepository) GetDB() *gorm.DB {
	return r.db
}

// ==================== 基本设备操作 ====================

// Create 创建资产设备
func (r *deviceRepository) Create(ctx context.Context, device *asset.Device) error {
	return r.db.WithContext(ctx).Create(device).Error
}

// Update 更新资产设备
func (r *deviceRepository) Update(ctx context.Context, device *asset.Device) error {
	var original asset.Device
	if err := r.db.WithContext(ctx).First(&original, device.ID).Error; err != nil {
		return err
	}
	device.CreatedAt = original.CreatedAt
	return r.db.WithContext(ctx).Save(device).Error
}

func (r *deviceRepository) UpdateDevices(ctx context.Context, devices []asset.Device) error {
	db := utils.GetDB(ctx, r.db)
	if db == r.db {
		db = r.db // 使用原始数据库连接
		return db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
			for _, device := range devices {
				err := tx.Model(asset.Device{}).Where("id = ?", device.ID).Updates(map[string]interface{}{
					"asset_status":    device.AssetStatus,
					"hardware_status": device.HardwareStatus,
				}).Error
				if err != nil {
					return fmt.Errorf("批量更新备件信息失败: %w", err)
				}
			}
			return nil
		})
	} else {
		for _, device := range devices {
			err := db.Model(asset.Device{}).Where("id = ?", device.ID).Updates(map[string]interface{}{
				"asset_status":    device.AssetStatus,
				"hardware_status": device.HardwareStatus,
			}).Error
			if err != nil {
				return fmt.Errorf("批量更新备件信息失败: %w", err)
			}
		}
		return nil
	}
}

// Delete 删除资产设备
func (r *deviceRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&asset.Device{}, id).Error
}

// GetByID 根据ID获取资产设备
func (r *deviceRepository) GetByID(ctx context.Context, id uint) (*asset.Device, error) {
	var device asset.Device
	err := r.db.WithContext(ctx).First(&device, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("资产设备不存在")
		}
		return nil, err
	}
	return &device, nil
}

// GetBySN 根据SN获取资产设备
func (r *deviceRepository) GetBySN(ctx context.Context, sn string) (*asset.Device, error) {
	var device asset.Device
	if err := r.db.WithContext(ctx).
		Preload("Template").
		Preload("Resource").
		Where("sn = ?", sn).
		First(&device).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("资产设备不存在")
		}
		return nil, err
	}
	return &device, nil
}

// GetIdleBySN 根据SN获取状态为闲置中的资产设备
func (r *deviceRepository) GetIdleBySN(ctx context.Context, sn string) (*asset.Device, error) {
	var device asset.Device
	if err := r.db.WithContext(ctx).
		Preload("Template").
		Where("sn = ? AND asset_status = ?", sn, "idle").
		First(&device).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("闲置中的资产设备不存在")
		}
		return nil, err
	}
	return &device, nil
}

// GetDevicesSpare 获取设备相关联的备件
func (r *deviceRepository) GetDeviceSpares(ctx context.Context, sns []string) (map[string][]asset.AssetSpare, error) {
	var spares []asset.AssetSpare
	sparesMap := make(map[string][]asset.AssetSpare)
	for _, sn := range sns {
		err := r.db.WithContext(ctx).Model(&asset.AssetSpare{}).Where("related_asset_sn = ?", sn).Find(&spares).Error
		if err != nil {
			return nil, fmt.Errorf("获取设备配件失败")
		}
		sparesMap[sn] = spares
	}

	return sparesMap, nil
}

// List 分页查询资产设备列表
func (r *deviceRepository) List(ctx context.Context, page, pageSize int, query, assetStatus, assetType string) ([]*asset.Device, int64, error) {
	var devices []*asset.Device
	var total int64

	db := r.db.WithContext(ctx).Model(&asset.Device{})

	if query != "" {
		db = db.Where("sn LIKE ? OR brand LIKE ? OR model LIKE ? OR purchase_order LIKE ?",
			"%"+query+"%", "%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	if assetType != "" {
		db = db.Where("asset_type = ?", assetType)
	}

	if assetStatus != "" {
		db = db.Where("status = ?", assetStatus)
	}

	db = db.Preload("Resource").
		Preload("Resource.Cabinet").
		Preload("Resource.Cabinet.Room").
		Preload("Resource.Cabinet.Room.DataCenter").
		Preload("Resource.Cabinet.Room.DataCenter.AZ").
		Preload("Resource.Cabinet.Room.DataCenter.AZ.Region")

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		db = db.Offset(offset).Limit(pageSize)
	}

	if err := db.Find(&devices).Error; err != nil {
		return nil, 0, err
	}

	return devices, total, nil
}

// ==================== 高级设备资源操作 ====================

// CreateWithResource 创建资产设备及其资源信息
func (r *deviceRepository) CreateWithResource(ctx context.Context, device *asset.Device) (*asset.Device, error) {
	tx := r.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 先创建设备记录
	if err := tx.Create(device).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("创建设备失败: %w", err)
	}

	fmt.Printf("创建设备成功: ID=%d, SN=%s\n", device.ID, device.SN)

	// 2. 如果有资源信息，创建资源记录
	if device.Resource != nil {
		// 强制删除可能存在的旧资源记录（包括软删除的记录）
		if err := tx.Unscoped().Where("asset_id = ?", device.ID).Delete(&asset.Resource{}).Error; err != nil {
			fmt.Printf("清理旧资源记录失败: %v\n", err)
			tx.Rollback()
			return nil, fmt.Errorf("清理旧资源记录失败: %w", err)
		}

		// 重置 ID，让数据库自动生成
		device.Resource.ID = 0
		// 设置资源记录的 AssetID 为刚创建的设备ID
		device.Resource.AssetID = device.ID
		// 如果没有设置SN，使用设备的SN
		if device.Resource.SN == "" {
			device.Resource.SN = device.SN
		}

		fmt.Printf("准备创建资源记录: AssetID=%d, SN=%s\n", device.Resource.AssetID, device.Resource.SN)

		// 直接创建资源记录
		if err := tx.Create(device.Resource).Error; err != nil {
			fmt.Printf("创建资源记录失败: %v\n", err)
			tx.Rollback()
			return nil, fmt.Errorf("创建资源记录失败: %w", err)
		}

		fmt.Printf("创建资源记录成功: ID=%d, AssetID=%d, SN=%s\n",
			device.Resource.ID, device.Resource.AssetID, device.Resource.SN)
	}

	// 3. 提交事务
	if err := tx.Commit().Error; err != nil {
		fmt.Printf("提交事务失败: %v\n", err)
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	fmt.Printf("事务提交成功，准备查询完整设备信息: ID=%d\n", device.ID)

	// 4. 使用新的数据库连接重新查询完整的设备信息
	return r.GetByIDWithResource(ctx, device.ID)
}

// UpdateWithResource 更新资产设备及其资源信息
func (r *deviceRepository) UpdateWithResource(ctx context.Context, device *asset.Device) (*asset.Device, error) {
	tx := r.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 检查设备是否存在
	var existingDevice asset.Device
	if err := tx.Where("id = ?", device.ID).First(&existingDevice).Error; err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("设备不存在")
		}
		return nil, err
	}

	// 更新设备记录
	if err := tx.Model(&asset.Device{}).Where("id = ?", device.ID).Updates(device).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// 如果设备的SN发生了变化，需要同步更新资源表的SN
	if existingDevice.SN != device.SN {
		// 查找关联的资源记录并更新SN
		if err := tx.Model(&asset.Resource{}).Where("asset_id = ?", device.ID).Update("sn", device.SN).Error; err != nil {
			// 如果更新失败但不是因为记录不存在，则回滚事务
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				tx.Rollback()
				return nil, err
			}
			// 如果是记录不存在，继续执行（可能还没有创建资源记录）
		}
	}

	// 如果有资源信息，更新资源记录
	if device.Resource != nil {
		// 检查资源是否存在
		var existingResource asset.Resource
		result := tx.Where("asset_id = ?", device.ID).First(&existingResource)

		if result.Error != nil {
			// 如果资源不存在，创建新资源
			if errors.Is(result.Error, gorm.ErrRecordNotFound) {
				device.Resource.AssetID = device.ID
				// 确保资源的SN与设备的SN保持一致
				device.Resource.SN = device.SN
				if err := tx.Create(device.Resource).Error; err != nil {
					tx.Rollback()
					return nil, err
				}
			} else {
				tx.Rollback()
				return nil, result.Error
			}
		} else {
			// 如果资源存在，更新资源
			device.Resource.ID = existingResource.ID
			device.Resource.AssetID = device.ID
			// 确保资源的SN与设备的SN保持一致
			device.Resource.SN = device.SN
			if err := tx.Model(&asset.Resource{}).Where("id = ?", existingResource.ID).Updates(device.Resource).Error; err != nil {

				tx.Rollback()
				return nil, err
			}
		}
	}

	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// 重新查询完整的设备信息
	return r.GetByIDWithResource(ctx, device.ID)
}

// DeleteWithResource 删除资产设备及其资源信息
func (r *deviceRepository) DeleteWithResource(ctx context.Context, id uint) error {
	tx := r.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 先删除关联的资源记录
	if err := tx.Where("asset_id = ?", id).Delete(&asset.Resource{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 再删除设备记录
	if err := tx.Where("id = ?", id).Delete(&asset.Device{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// GetByIDWithResource 根据ID获取资产设备及其资源信息
func (r *deviceRepository) GetByIDWithResource(ctx context.Context, id uint) (*asset.Device, error) {
	var device asset.Device

	if err := r.db.WithContext(ctx).Model(&asset.Device{}).
		Where("id = ?", id).
		Preload("Resource", func(db *gorm.DB) *gorm.DB {
			return db.Preload("Cabinet.Room.DataCenter.AZ.Region")
		}).
		First(&device).Error; err != nil {
		return nil, err
	}

	return &device, nil
}

// ListWithResource 查询资产设备列表，包含资源信息及高级筛选条件
func (r *deviceRepository) ListWithResource(ctx context.Context, page, pageSize int, query, assetStatus, assetType, bizStatus, project, ip string) ([]*asset.Device, int64, error) {
	var devices []*asset.Device
	var total int64

	// 通过资源表查询符合条件的资产ID
	subQuery := r.db.WithContext(ctx).Model(&asset.Resource{})

	if bizStatus != "" {
		subQuery = subQuery.Where("biz_status = ?", bizStatus)
	}

	if project != "" {
		subQuery = subQuery.Where("project LIKE ?", "%"+project+"%")
	}

	if ip != "" {
		subQuery = subQuery.Where("vpc_ip LIKE ? OR bmc_ip LIKE ?", "%"+ip+"%", "%"+ip+"%")
	}

	var assetIDs []uint
	if err := subQuery.Distinct("asset_id").Pluck("asset_id", &assetIDs).Error; err != nil {
		return nil, 0, err
	}

	// 如果没有符合条件的资源，直接返回空结果
	if len(assetIDs) == 0 && (bizStatus != "" || project != "" || ip != "") {
		return devices, 0, nil
	}

	// 构建主查询
	mainQuery := r.db.WithContext(ctx).Model(&asset.Device{})

	// 只有当有资源条件并且找到了符合条件的资产ID时，才添加ID条件
	if len(assetIDs) > 0 && (bizStatus != "" || project != "" || ip != "") {
		mainQuery = mainQuery.Where("id IN ?", assetIDs)
	}

	// 添加设备表过滤条件
	if query != "" {
		mainQuery = mainQuery.Where("sn LIKE ? OR brand LIKE ? OR model LIKE ?",
			"%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	if assetType != "" {
		mainQuery = mainQuery.Where("asset_type = ?", assetType)
	}

	if assetStatus != "" {
		mainQuery = mainQuery.Where("asset_status = ?", assetStatus)
	}

	// 计算总记录数
	if err := mainQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	if page > 0 && pageSize > 0 {
		mainQuery = mainQuery.Offset((page - 1) * pageSize).Limit(pageSize)
	}

	// 预加载关联数据
	mainQuery = mainQuery.Preload("Resource", func(db *gorm.DB) *gorm.DB {
		return db.Preload("Cabinet.Room.DataCenter.AZ.Region")
	})

	// 执行查询
	if err := mainQuery.Find(&devices).Error; err != nil {
		return nil, 0, err
	}

	return devices, total, nil
}

// GetDeviceTemplate 获取设备模板信息
func (r *deviceRepository) GetDeviceTemplate(ctx context.Context, deviceID uint, withComponents bool) (interface{}, error) {
	// 修改为直接从asset_devices表获取templateID
	var device asset.Device
	if err := r.db.WithContext(ctx).Table("asset_devices").
		Select("template_id").
		Where("id = ?", deviceID).
		First(&device).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("未找到设备关联的模板信息")
		}
		return nil, err
	}

	if device.TemplateID == 0 {
		return nil, errors.New("设备未关联任何模板")
	}

	// 查询machine_templates表获取模板信息
	var templateData map[string]interface{}
	if err := r.db.WithContext(ctx).Raw("SELECT * FROM machine_templates WHERE id = ?", device.TemplateID).
		Scan(&templateData).Error; err != nil {
		return nil, err
	}

	// 如果需要组件信息
	if withComponents {
		// 使用原生SQL查询所有组件
		var componentsData []map[string]interface{}
		if err := r.db.WithContext(ctx).Raw("SELECT * FROM template_components WHERE template_id = ?", device.TemplateID).
			Scan(&componentsData).Error; err != nil {
			// 如果获取组件失败，不中断流程，只返回模板信息
			return templateData, nil
		}

		// 处理每个组件的产品信息
		for i := range componentsData {
			productID, ok := componentsData[i]["product_id"]
			if !ok || productID == nil {
				// 如果没有product_id，初始化为空对象
				componentsData[i]["product"] = map[string]interface{}{}
				continue
			}

			// 查询产品信息
			var productData []map[string]interface{}
			if err := r.db.WithContext(ctx).Raw("SELECT * FROM products WHERE id = ?", productID).
				Scan(&productData).Error; err != nil || len(productData) == 0 {
				// 查询失败或无结果，初始化为空对象
				componentsData[i]["product"] = map[string]interface{}{}
			} else {
				// 成功获取产品信息
				componentsData[i]["product"] = productData[0]
			}
		}

		// 添加组件信息到返回结果
		templateData["components"] = componentsData
	}

	return templateData, nil
}

// ListDeviceResourcesExtended 扩展的设备资源查询功能，支持更多筛选条件
func (r *deviceRepository) ListDeviceResourcesExtended(
	ctx context.Context,
	page, pageSize int,
	query, hostname, brand, model string,
	assetStatus, assetType, bizStatus, project, cluster string,
	vpcIP, bmcIP, tenantIP string,
	isBackup *bool,
) ([]*asset.Device, int64, error) {
	var devices []*asset.Device
	var total int64

	// 处理查询关键词，支持多个关键词（以空格分隔）
	var keywords []string
	if query != "" {
		keywords = strings.Fields(query) // 自动处理连续的空格
	}

	// 快速检查：如果query参数存在，先快速检查是否有匹配的记录
	if len(keywords) > 0 {
		var count int64

		// 构建快速检查查询
		quickCheckQuery := r.db.WithContext(ctx).Model(&asset.Resource{})

		// 构建OR条件查询
		orConditions := r.db.WithContext(ctx)
		for _, keyword := range keywords {
			orConditions = orConditions.Or("sn = ? OR hostname = ?", keyword, keyword)
		}

		// 应用OR条件
		quickCheckQuery = quickCheckQuery.Where(orConditions)

		// 执行快速检查查询
		if err := quickCheckQuery.Count(&count).Error; err != nil {
			return nil, 0, err
		}

		// 如果资源表中没有匹配记录，再检查设备表
		if count == 0 {
			deviceCheckQuery := r.db.WithContext(ctx).Model(&asset.Device{})

			// 构建设备表的OR条件查询
			deviceOrConditions := r.db.WithContext(ctx)
			for _, keyword := range keywords {
				deviceOrConditions = deviceOrConditions.Or("sn = ?", keyword)
			}

			// 应用OR条件
			deviceCheckQuery = deviceCheckQuery.Where(deviceOrConditions)

			// 执行快速检查查询
			if err := deviceCheckQuery.Count(&count).Error; err != nil {
				return nil, 0, err
			}

			// 如果两个表都没有匹配记录，立即返回空结果
			if count == 0 {
				return devices, 0, nil
			}
		}
	}

	// 通过资源表查询符合条件的资产ID
	subQuery := r.db.WithContext(ctx).Model(&asset.Resource{})

	// 资源相关条件筛选
	if bizStatus != "" {
		subQuery = subQuery.Where("biz_status = ?", bizStatus)
	}

	if project != "" {
		subQuery = subQuery.Where("project = ?", project)
	}

	// 处理多个查询关键词
	if len(keywords) > 0 {
		// 构建OR条件
		orConditions := r.db.WithContext(ctx)
		for _, keyword := range keywords {
			orConditions = orConditions.Or("sn = ? OR hostname = ?", keyword, keyword)
		}
		subQuery = subQuery.Where(orConditions)
	}

	if hostname != "" {
		subQuery = subQuery.Where("hostname = ?", hostname)
	}

	if cluster != "" {
		subQuery = subQuery.Where("cluster = ?", cluster)
	}

	if vpcIP != "" {
		subQuery = subQuery.Where("vpc_ip = ?", vpcIP)
	}

	if bmcIP != "" {
		subQuery = subQuery.Where("bmc_ip = ?", bmcIP)
	}

	if tenantIP != "" {
		subQuery = subQuery.Where("tenant_ip = ?", tenantIP)
	}

	if isBackup != nil {
		subQuery = subQuery.Where("is_backup = ?", *isBackup)
	}

	// 只有当有资源相关条件时才执行子查询
	hasResourceConditions := bizStatus != "" || project != "" || len(keywords) > 0 ||
		vpcIP != "" || bmcIP != "" || tenantIP != "" || isBackup != nil ||
		hostname != "" || cluster != ""

	var assetIDs []uint
	if hasResourceConditions {
		if err := subQuery.Distinct("asset_id").Pluck("asset_id", &assetIDs).Error; err != nil {
			return nil, 0, err
		}

		// 如果没找到符合条件的资源，直接返回空结果
		if len(assetIDs) == 0 {
			return devices, 0, nil
		}
	}

	// 构建主查询
	mainQuery := r.db.WithContext(ctx).Model(&asset.Device{})

	// 添加资产ID条件
	if hasResourceConditions && len(assetIDs) > 0 {
		mainQuery = mainQuery.Where("id IN ?", assetIDs)
	} else if len(keywords) > 0 && !hasResourceConditions {
		// 如果只有query参数且没有其他资源条件，构建设备表的OR条件
		orConditions := r.db.WithContext(ctx)
		for _, keyword := range keywords {
			orConditions = orConditions.Or("sn = ?", keyword)
		}
		mainQuery = mainQuery.Where(orConditions)
	}

	// 添加设备表过滤条件
	if brand != "" {
		mainQuery = mainQuery.Where("brand = ?", brand)
	}

	if model != "" {
		mainQuery = mainQuery.Where("model = ?", model)
	}

	// 限制只返回四种资产类型
	allowedAssetTypes := []string{"server", "gpu_server", "storage", "other"}

	if assetType != "" {
		// 如果用户指定了资产类型，检查是否在允许列表中
		isAllowed := false
		for _, allowedType := range allowedAssetTypes {
			if assetType == allowedType {
				isAllowed = true
				break
			}
		}

		if isAllowed {
			mainQuery = mainQuery.Where("asset_type = ?", assetType)
		} else {
			// 如果用户指定了不允许的资产类型，强制只返回允许的资产类型
			mainQuery = mainQuery.Where("asset_type IN ?", allowedAssetTypes)
		}
	} else {
		// 如果用户没有指定资产类型，只返回允许的资产类型
		mainQuery = mainQuery.Where("asset_type IN ?", allowedAssetTypes)
	}

	if assetStatus != "" {
		mainQuery = mainQuery.Where("asset_status = ?", assetStatus)
	}

	// 计算总记录数
	if err := mainQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 如果总数为0，直接返回空结果
	if total == 0 {
		return devices, 0, nil
	}

	// 分页查询
	if page > 0 && pageSize > 0 {
		mainQuery = mainQuery.Offset((page - 1) * pageSize).Limit(pageSize)
	}

	// 预加载关联数据
	mainQuery = mainQuery.Preload("Resource", func(db *gorm.DB) *gorm.DB {
		return db.Preload("Cabinet.Room.DataCenter.AZ.Region").Preload("Room.DataCenter")
	})

	// 执行查询
	if err := mainQuery.Find(&devices).Error; err != nil {
		return nil, 0, err
	}

	return devices, total, nil
}

func (r *deviceRepository) WithTx(tx *gorm.DB) DeviceRepository {
	if tx == nil {
		return r
	}
	return &deviceRepository{db: tx}
}

func (r *deviceRepository) ListByIds(ctx context.Context, ids []uint) ([]*asset.Device, int64, error) {
	if len(ids) == 0 {
		return []*asset.Device{}, 0, nil
	}
	var devices []*asset.Device
	var count int64

	// 执行查询并预加载关联数据
	err := r.db.WithContext(ctx).
		Model(&asset.Device{}).
		Where("id IN (?)", ids).
		Find(&devices).
		Count(&count).Error

	if err != nil {
		return nil, 0, err
	}

	return devices, count, nil
}

// ListBySns 根据Sns列表查询 不preload深层结果
func (r *deviceRepository) ListBySns(ctx context.Context, sns []string) ([]*asset.Device, int64, error) {
	if len(sns) == 0 {
		return []*asset.Device{}, 0, nil
	}

	var devices []*asset.Device
	var count int64

	// 执行查询并预加载关联数据
	err := r.db.WithContext(ctx).
		Model(&asset.Device{}).
		Where("sn IN (?)", sns).
		Find(&devices).
		Count(&count).Error

	if err != nil {
		return nil, 0, err
	}

	return devices, count, nil
}

func (r *deviceRepository) ListBySnsV2(ctx context.Context, sns []string, deep uint) ([]asset.Device, int64, error) {
	if len(sns) == 0 {
		return []asset.Device{}, 0, nil
	}

	var devices []asset.Device
	var count int64

	// 基础查询
	query := r.db.WithContext(ctx).
		Model(&asset.Device{}).
		Where("sn IN (?)", sns)

	// 根据deep级别动态添加预加载
	switch deep {
	case 1:
		// 不预加载任何关联
	case 2:
		query = query.Preload("Resource")
	case 3:
		query = query.
			Preload("Resource").
			Preload("Resource.Cabinet").
			Preload("Resource.Room")
	default:
		// 完整深度预加载
		query = query.
			Preload("Resource").
			Preload("Resource.Cabinet").
			Preload("Resource.Cabinet.Room").
			Preload("Resource.Cabinet.Room.DataCenter").
			Preload("Resource.Cabinet.Room.DataCenter.AZ").
			Preload("Resource.Cabinet.Room.DataCenter.AZ.Region").
			Preload("Resource.Room")
	}

	// 执行查询并获取结果和计数
	err := query.Find(&devices).Count(&count).Error
	if err != nil {
		return nil, 0, err
	}

	return devices, count, nil
}

func (r *deviceRepository) GetDeviceWarehouse(ctx context.Context, deviceID uint) (*asset.Warehouse, error) {
	warehouse := asset.Warehouse{}
	d, err := r.GetByID(ctx, deviceID)
	if err != nil {
		return nil, fmt.Errorf("获取设备信息失败: %w", err)
	}
	devices, _, err := r.List(ctx, 1, 1, d.SN, "", "")
	if err != nil {
		return nil, fmt.Errorf("获取设备信息失败: %w", err)
	}
	device := devices[0]

	if device.Resource == nil {
		return nil, fmt.Errorf("设备没有关联资源信息: %d", device.ID)
	}
	if device.Resource.RoomID == 0 {
		return nil, fmt.Errorf("设备没有关联包间信息: %w", err)
	}
	err = r.db.WithContext(ctx).
		Model(&asset.Warehouse{}).
		Where("room_id = ?", device.Resource.RoomID).
		First(&warehouse).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("查询仓库信息失败: %w", err)
	}
	return &warehouse, nil
}
