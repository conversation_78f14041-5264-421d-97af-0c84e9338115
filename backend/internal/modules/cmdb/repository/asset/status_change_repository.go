package asset

import (
	"backend/internal/modules/cmdb/model/asset"
	"backend/pkg/utils"
	"context"
	"errors"
	"time"

	"gorm.io/gorm"
)

// StatusChangeRepository 资产状态变更仓库接口
type StatusChangeRepository interface {
	// 记录状态变更
	LogStatusChange(ctx context.Context, log *asset.StatusChangeLog) error

	// 获取资产状态变更历史
	GetAssetStatusHistory(ctx context.Context, assetID uint, params asset.ListParams) ([]*asset.StatusChangeLog, int64, error)

	// 获取设备在指定工单前的原始状态
	GetDeviceOriginalStatusByWorkflowID(ctx context.Context, workflowID string) (*asset.StatusChangeLog, error)

	// 通过设备SN获取指定工单的原始状态
	GetDeviceOriginalStatusBySN(ctx context.Context, deviceSN string, workflowID string) (*asset.StatusChangeLog, error)

	// 变更资产状态
	ChangeAssetStatus(ctx context.Context, assetID uint, newStatus string, reason string, operatorID uint, operatorName string) error

	// 变更业务状态
	ChangeBizStatus(ctx context.Context, resourceID uint, newStatus string, reason string, operatorID uint, operatorName string) error

	// 变更硬件状态
	ChangeHardwareStatus(ctx context.Context, assetID uint, newStatus string, reason string, operatorID uint, operatorName string, source string) error

	// 记录完整状态变更（同时包含资产状态、业务状态和硬件状态）
	LogFullStatusChange(ctx context.Context, assetID uint,
		newAssetStatus, oldAssetStatus string,
		newBizStatus, oldBizStatus string,
		newHardwareStatus, oldHardwareStatus string,
		reason string, source string, operatorID uint, operatorName string, workflowID string) error

	WithTx(tx *gorm.DB) StatusChangeRepository
}

// statusChangeRepository 资产状态变更仓库实现
type statusChangeRepository struct {
	db *gorm.DB
}

// NewStatusChangeRepository 创建资产状态变更仓库
func NewStatusChangeRepository(db *gorm.DB) StatusChangeRepository {
	return &statusChangeRepository{db: db}
}

// LogStatusChange 记录状态变更
func (r *statusChangeRepository) LogStatusChange(ctx context.Context, log *asset.StatusChangeLog) error {
	return r.db.WithContext(ctx).Create(log).Error
}

// GetAssetStatusHistory 获取资产状态变更历史
func (r *statusChangeRepository) GetAssetStatusHistory(ctx context.Context, assetID uint, params asset.ListParams) ([]*asset.StatusChangeLog, int64, error) {
	var logs []*asset.StatusChangeLog
	var total int64

	db := r.db.WithContext(ctx).Model(&asset.StatusChangeLog{}).Where("asset_id = ?", assetID)
	// 根据时间范围过滤
	if !params.StartDate.IsZero() && !params.EndDate.IsZero() {
		db = db.Where("created_at BETWEEN ? AND ?", params.StartDate, params.EndDate)
	}

	// 根据操作人ID过滤
	if params.OperatorID > 0 {
		db = db.Where("operator_id = ?", params.OperatorID)
	}

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if params.Page > 0 && params.PageSize > 0 {
		offset := (params.Page - 1) * params.PageSize
		db = db.Offset(offset).Limit(params.PageSize)
	}

	if err := db.Order("created_at DESC").Find(&logs).Error; err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetDeviceOriginalStatusByWorkflowID 获取设备在指定工单前的原始状态
func (r *statusChangeRepository) GetDeviceOriginalStatusByWorkflowID(ctx context.Context, workflowID string) (*asset.StatusChangeLog, error) {
	// 查找该工作流最早的状态变更日志，这条记录包含了设备最初的状态
	var log asset.StatusChangeLog

	err := r.db.WithContext(ctx).
		Where("workflow_id = ?", workflowID).
		Order("created_at ASC"). // 按时间升序，获取最早的记录
		First(&log).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("未找到指定工作流ID的状态变更记录")
		}
		return nil, err
	}

	return &log, nil
}

// GetDeviceOriginalStatusBySN 通过设备SN获取指定工单的原始状态
func (r *statusChangeRepository) GetDeviceOriginalStatusBySN(ctx context.Context, deviceSN string, workflowID string) (*asset.StatusChangeLog, error) {
	// 先根据SN获取设备ID
	var device asset.Device
	err := r.db.WithContext(ctx).
		Where("sn = ?", deviceSN).
		First(&device).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("未找到指定SN的设备")
		}
		return nil, err
	}

	// 查找该工作流最早的状态变更日志
	var log asset.StatusChangeLog

	err = r.db.WithContext(ctx).
		Where("asset_id = ? AND workflow_id = ?", device.ID, workflowID).
		Order("created_at ASC"). // 按时间升序，获取最早的记录
		First(&log).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("未找到设备状态变更记录")
		}
		return nil, err
	}

	return &log, nil
}

// ChangeAssetStatus 变更资产状态
func (r *statusChangeRepository) ChangeAssetStatus(ctx context.Context, assetID uint, newStatus string, reason string, operatorID uint, operatorName string) error {
	if !asset.IsValidAssetStatus(newStatus) {
		return errors.New("无效的资产状态")
	}

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取当前资产信息
		var device asset.Device
		if err := tx.First(&device, assetID).Error; err != nil {
			return err
		}

		oldStatus := device.AssetStatus

		// 记录状态变更日志
		log := &asset.StatusChangeLog{
			AssetID:        assetID,
			OldAssetStatus: oldStatus,
			NewAssetStatus: newStatus,
			ChangeReason:   reason,
			OperatorID:     operatorID,
			OperatorName:   operatorName,
		}

		if err := tx.Create(log).Error; err != nil {
			return err
		}

		// 更新资产状态
		device.AssetStatus = newStatus
		device.LastStatusChange = utils.Date(time.Now())

		return tx.Save(&device).Error
	})
}

// ChangeBizStatus 变更业务状态
func (r *statusChangeRepository) ChangeBizStatus(ctx context.Context, resourceID uint, newStatus string, reason string, operatorID uint, operatorName string) error {
	if !asset.IsValidBizStatus(newStatus) {
		return errors.New("无效的业务状态")
	}

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取当前资源信息
		var resource asset.Resource
		if err := tx.First(&resource, resourceID).Error; err != nil {
			return err
		}

		oldStatus := resource.BizStatus

		// 获取关联的资产信息
		var device asset.Device
		if err := tx.First(&device, resource.AssetID).Error; err != nil {
			return err
		}

		// 记录状态变更日志
		log := &asset.StatusChangeLog{
			AssetID:      device.ID,
			OldBizStatus: oldStatus,
			NewBizStatus: newStatus,
			ChangeReason: reason,
			OperatorID:   operatorID,
			OperatorName: operatorName,
		}

		if err := tx.Create(log).Error; err != nil {
			return err
		}

		// 更新资源业务状态
		resource.BizStatus = newStatus
		resource.LastBizStatusChange = utils.Date(time.Now())

		return tx.Save(&resource).Error
	})
}

// ChangeHardwareStatus 变更硬件状态
func (r *statusChangeRepository) ChangeHardwareStatus(ctx context.Context, assetID uint, newStatus string, reason string, operatorID uint, operatorName string, source string) error {
	if !asset.IsValidHardwareStatus(newStatus) {
		return errors.New("无效的硬件状态")
	}

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取当前资产信息
		var device asset.Device
		if err := tx.First(&device, assetID).Error; err != nil {
			return err
		}

		oldStatus := device.HardwareStatus

		// 记录状态变更日志
		log := &asset.StatusChangeLog{
			AssetID:           assetID,
			OldHardwareStatus: oldStatus,
			NewHardwareStatus: newStatus,
			ChangeReason:      reason,
			OperatorID:        operatorID,
			OperatorName:      operatorName,
			Source:            source,
		}

		if err := tx.Create(log).Error; err != nil {
			return err
		}

		// 更新资产硬件状态
		device.HardwareStatus = newStatus
		device.LastHardwareStatusChange = utils.Date(time.Now())

		return tx.Save(&device).Error
	})
}

// LogFullStatusChange 记录完整状态变更（同时包含资产状态、业务状态和硬件状态）
func (r *statusChangeRepository) LogFullStatusChange(ctx context.Context, assetID uint,
	newAssetStatus, oldAssetStatus string,
	newBizStatus, oldBizStatus string,
	newHardwareStatus, oldHardwareStatus string,
	reason string, source string, operatorID uint, operatorName string, workflowID string) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取当前资产信息
		var device asset.Device
		if err := tx.First(&device, assetID).Error; err != nil {
			return err
		}

		// 获取资源信息
		var resource *asset.Resource
		if err := tx.Where("asset_id = ?", assetID).First(&resource).Error; err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return err
			}
			// 如果资源不存在，置为nil
			resource = nil
		}

		// 记录状态变更日志
		log := &asset.StatusChangeLog{
			AssetID:           assetID,
			OldAssetStatus:    oldAssetStatus,
			NewAssetStatus:    newAssetStatus,
			OldHardwareStatus: oldHardwareStatus,
			NewHardwareStatus: newHardwareStatus,
			ChangeReason:      reason,
			Source:            source,
			OperatorID:        operatorID,
			OperatorName:      operatorName,
			WorkflowID:        workflowID,
		}

		// 如果资源存在，记录业务状态
		if resource != nil && oldBizStatus != "" && newBizStatus != "" {
			log.OldBizStatus = oldBizStatus
			log.NewBizStatus = newBizStatus
		}

		if err := tx.Create(log).Error; err != nil {
			return err
		}

		// 更新资产状态
		if newAssetStatus != "" {
			device.AssetStatus = newAssetStatus
			device.LastStatusChange = utils.Date(time.Now())
		}

		// 更新硬件状态
		if newHardwareStatus != "" {
			device.HardwareStatus = newHardwareStatus
			device.LastHardwareStatusChange = utils.Date(time.Now())
		}

		// 保存设备更新
		if err := tx.Save(&device).Error; err != nil {
			return err
		}

		// 如果资源存在且需要更新业务状态
		if resource != nil && newBizStatus != "" {
			resource.BizStatus = newBizStatus
			resource.LastBizStatusChange = utils.Date(time.Now())
			if err := tx.Save(resource).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

func (r *statusChangeRepository) WithTx(tx *gorm.DB) StatusChangeRepository {
	if tx == nil {
		return r
	}
	return &statusChangeRepository{
		db: tx,
	}
}
