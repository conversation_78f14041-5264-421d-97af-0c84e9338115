package asset

import (
	"backend/internal/modules/cmdb/model/asset"
	"context"
	"errors"

	"gorm.io/gorm"
)

// ResourceRepository 资源仓库接口
type ResourceRepository interface {
	Create(ctx context.Context, resource *asset.Resource) error
	Update(ctx context.Context, resource *asset.Resource) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*asset.Resource, error)
	GetBySN(ctx context.Context, sn string) (*asset.Resource, error)
	GetByAssetID(ctx context.Context, assetID uint) (*asset.Resource, error)
	List(ctx context.Context, page, pageSize int, query string, bizStatus, resStatus string) ([]*asset.Resource, int64, error)
	GetAllProjects(ctx context.Context) ([]string, error)
	GetAllClusters(ctx context.Context) ([]string, error)
	GetAvailableBackups(ctx context.Context, project, cluster, hardwareStatus, bizStatus string) ([]*asset.Resource, error)

	ListByAssetIDsWithDevice(ctx context.Context, assetIDs []uint) ([]*asset.Resource, error)
	WithTx(db *gorm.DB) ResourceRepository
}

// resourceRepository 资源仓库实现
type resourceRepository struct {
	db *gorm.DB
}

// NewResourceRepository 创建资源仓库
func NewResourceRepository(db *gorm.DB) ResourceRepository {
	return &resourceRepository{db: db}
}

// Create 创建资源
func (r *resourceRepository) Create(ctx context.Context, resource *asset.Resource) error {
	return r.db.WithContext(ctx).Create(resource).Error
}

// Update 更新资源
func (r *resourceRepository) Update(ctx context.Context, resource *asset.Resource) error {
	var original asset.Resource
	if err := r.db.WithContext(ctx).First(&original, resource.ID).Error; err != nil {
		return err
	}
	resource.CreatedAt = original.CreatedAt
	return r.db.WithContext(ctx).Save(resource).Error
}

// Delete 删除资源
func (r *resourceRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&asset.Resource{}, id).Error
}

// GetByID 根据ID获取资源
func (r *resourceRepository) GetByID(ctx context.Context, id uint) (*asset.Resource, error) {
	var resource asset.Resource
	if err := r.db.WithContext(ctx).Preload("Device").Preload("Cabinet").Preload("Cabinet.Room").First(&resource, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("资源不存在")
		}
		return nil, err
	}
	return &resource, nil
}

// GetBySN 根据SN获取资源
func (r *resourceRepository) GetBySN(ctx context.Context, sn string) (*asset.Resource, error) {
	var resource asset.Resource
	if err := r.db.WithContext(ctx).Preload("Device").Preload("Cabinet").Preload("Cabinet.Room").Where("sn = ?", sn).First(&resource).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("资源不存在")
		}
		return nil, err
	}
	return &resource, nil
}

// GetByAssetID 根据资产ID获取资源
func (r *resourceRepository) GetByAssetID(ctx context.Context, assetID uint) (*asset.Resource, error) {
	var resource asset.Resource
	if err := r.db.WithContext(ctx).Preload("Device").Preload("Cabinet").Preload("Cabinet.Room").Where("asset_id = ?", assetID).First(&resource).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("资源不存在")
		}
		return nil, err
	}
	return &resource, nil
}

// List 分页查询资源列表
func (r *resourceRepository) List(ctx context.Context, page, pageSize int, query string, bizStatus, resStatus string) ([]*asset.Resource, int64, error) {
	var resources []*asset.Resource
	var total int64

	db := r.db.WithContext(ctx).Model(&asset.Resource{}).Preload("Device").Preload("Cabinet").Preload("Cabinet.Room")

	if query != "" {
		db = db.Where("sn LIKE ? OR vpc_ip LIKE ? OR bmc_ip LIKE ? OR cluster LIKE ? OR project LIKE ?",
			"%"+query+"%", "%"+query+"%", "%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	if bizStatus != "" {
		db = db.Where("biz_status = ?", bizStatus)
	}

	if resStatus != "" {
		db = db.Where("res_status = ?", resStatus)
	}

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		db = db.Offset(offset).Limit(pageSize)
	}

	if err := db.Find(&resources).Error; err != nil {
		return nil, 0, err
	}

	return resources, total, nil
}

// GetAllProjects 获取所有唯一的项目列表
func (r *resourceRepository) GetAllProjects(ctx context.Context) ([]string, error) {
	var projects []string

	// 使用DISTINCT查询所有唯一的项目名称
	err := r.db.WithContext(ctx).Model(&asset.Resource{}).
		Select("DISTINCT project").
		Where("project <> ''").
		Pluck("project", &projects).Error

	return projects, err
}

// GetAllClusters 获取所有唯一的集群列表
func (r *resourceRepository) GetAllClusters(ctx context.Context) ([]string, error) {
	var clusters []string

	// 使用DISTINCT查询所有唯一的集群名称
	err := r.db.WithContext(ctx).Model(&asset.Resource{}).
		Select("DISTINCT cluster").
		Where("cluster <> ''").
		Pluck("cluster", &clusters).Error

	return clusters, err
}

// GetAvailableBackups 获取可用的备机列表
func (r *resourceRepository) GetAvailableBackups(ctx context.Context, project, cluster, hardwareStatus, bizStatus string) ([]*asset.Resource, error) {
	var resources []*asset.Resource

	// 构建查询
	db := r.db.WithContext(ctx).Model(&asset.Resource{})

	// 添加项目条件
	if project != "" {
		db = db.Where("project = ?", project)
	}

	// 添加集群条件
	if cluster != "" {
		db = db.Where("cluster = ?", cluster)
	}

	// 添加业务状态条件
	if bizStatus != "" {
		db = db.Where("biz_status = ?", bizStatus)
	}

	// 添加硬件状态条件 - 这需要关联设备表
	if hardwareStatus != "" {
		db = db.Joins("JOIN asset_devices ON resources.asset_id = asset_devices.id").
			Where("asset_devices.hardware_status = ?", hardwareStatus)
	}

	// 查询设置为备机的资源
	db = db.Where("is_backup = ?", true)

	// 预加载关联对象
	db = db.Preload("Device").Preload("Cabinet").Preload("Cabinet.Room")

	// 执行查询
	if err := db.Find(&resources).Error; err != nil {
		return nil, err
	}

	return resources, nil
}

// ListBySNs 根据SN批量查询
func (r *resourceRepository) ListByAssetIDsWithDevice(ctx context.Context, assetIDs []uint) ([]*asset.Resource, error) {
	if len(assetIDs) == 0 {
		return []*asset.Resource{}, nil
	}

	var resources []*asset.Resource
	err := r.db.WithContext(ctx).Preload("Device").Preload("Cabinet").Preload("Cabinet.Room").
		Where("asset_id IN ?", assetIDs).
		Find(&resources).Error

	return resources, err
}
func (r *resourceRepository) WithTx(db *gorm.DB) ResourceRepository {
	if db == nil {
		return r
	}
	return &resourceRepository{db: db}
}
