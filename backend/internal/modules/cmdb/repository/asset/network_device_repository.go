package asset

import (
	"backend/internal/modules/cmdb/model/asset"
	"context"
	"errors"
	"time"

	"gorm.io/gorm"
)

// NetworkDeviceRepository 网络设备仓库接口
type NetworkDeviceRepository interface {
	// 创建网络设备
	Create(ctx context.Context, networkDevice *asset.NetworkDevice) error
	// 更新网络设备
	Update(ctx context.Context, networkDevice *asset.NetworkDevice) error
	// 删除网络设备
	Delete(ctx context.Context, id uint) error
	// 根据ID获取网络设备
	GetByID(ctx context.Context, id uint) (*asset.NetworkDevice, error)
	// 根据设备ID获取网络设备
	GetByDeviceID(ctx context.Context, deviceID uint) (*asset.NetworkDevice, error)
	// 获取网络设备列表
	List(ctx context.Context, page, pageSize int, query, role string, params map[string]interface{}) ([]*asset.NetworkDeviceWithDeviceInfo, int64, error)
	// 获取网络设备详情（包含设备和资源信息）
	GetDetailByID(ctx context.Context, id uint) (*asset.NetworkDeviceWithDeviceInfo, error)
}

// networkDeviceRepository 网络设备仓库实现
type networkDeviceRepository struct {
	db *gorm.DB
}

// NewNetworkDeviceRepository 创建网络设备仓库
func NewNetworkDeviceRepository(db *gorm.DB) NetworkDeviceRepository {
	return &networkDeviceRepository{db: db}
}

// Create 创建网络设备
func (r *networkDeviceRepository) Create(ctx context.Context, networkDevice *asset.NetworkDevice) error {
	return r.db.WithContext(ctx).Create(networkDevice).Error
}

// Update 更新网络设备
func (r *networkDeviceRepository) Update(ctx context.Context, networkDevice *asset.NetworkDevice) error {
	if networkDevice.ID == 0 {
		return errors.New("网络设备ID不能为空")
	}

	// 不更新created_at和updated_at字段
	return r.db.WithContext(ctx).Model(networkDevice).
		Omit("created_at").
		Updates(networkDevice).Error
}

// Delete 删除网络设备
func (r *networkDeviceRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&asset.NetworkDevice{}, id).Error
}

// GetByID 根据ID获取网络设备
func (r *networkDeviceRepository) GetByID(ctx context.Context, id uint) (*asset.NetworkDevice, error) {
	var networkDevice asset.NetworkDevice
	err := r.db.WithContext(ctx).First(&networkDevice, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("网络设备不存在")
		}
		return nil, err
	}
	return &networkDevice, nil
}

// GetByDeviceID 根据设备ID获取网络设备
func (r *networkDeviceRepository) GetByDeviceID(ctx context.Context, deviceID uint) (*asset.NetworkDevice, error) {
	var networkDevice asset.NetworkDevice
	err := r.db.WithContext(ctx).Where("device_id = ?", deviceID).First(&networkDevice).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("网络设备不存在")
		}
		return nil, err
	}
	return &networkDevice, nil
}

// List 获取网络设备列表
func (r *networkDeviceRepository) List(ctx context.Context, page, pageSize int, query, role string, params map[string]interface{}) ([]*asset.NetworkDeviceWithDeviceInfo, int64, error) {
	var networkDevices []*asset.NetworkDevice
	var total int64

	db := r.db.WithContext(ctx).Model(&asset.NetworkDevice{})

	// 条件查询
	if query != "" {
		// 关联查询设备表中的SN、品牌、型号等信息
		db = db.Joins("Device").Where("Device.sn LIKE ? OR Device.brand LIKE ? OR Device.model LIKE ?",
			"%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	if role != "" {
		db = db.Where("role = ?", role)
	}

	if len(params) > 0 {
		if assetType, ok := params["assetType"]; ok && assetType != "" {
			db = db.Joins("Device").Where("asset_type = ?", assetType)
		}
	}
	// 查询总数
	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		db = db.Offset(offset).Limit(pageSize)
	}

	// 加载关联的设备信息，包括资源、机柜、机房等
	err = db.Preload("Device.Resource.Cabinet.Room.DataCenter.AZ.Region").
		Preload("Device.Template").
		Preload("Device.Resource.Room.DataCenter").Find(&networkDevices).Error
	if err != nil {
		return nil, 0, err
	}

	// 将NetworkDevice转换为NetworkDeviceWithDeviceInfo
	result := make([]*asset.NetworkDeviceWithDeviceInfo, 0, len(networkDevices))
	for _, networkDevice := range networkDevices {
		if networkDevice.Device == nil {
			continue
		}

		deviceInfo := &asset.NetworkDeviceWithDeviceInfo{
			// 网络设备信息
			ID:                networkDevice.ID,
			Role:              networkDevice.Role,
			FirmwareVersion:   networkDevice.FirmwareVersion,
			LoopbackAddress:   networkDevice.LoopbackAddress,
			ManagementAddress: networkDevice.ManagementAddress,
			Ports:             networkDevice.Ports,
			PortSpeed:         networkDevice.PortSpeed,
			StackSupport:      networkDevice.StackSupport,
			StackID:           networkDevice.StackID,
			StackRole:         networkDevice.StackRole,
			Layer:             networkDevice.Layer,
			RoutingProtocols:  networkDevice.RoutingProtocols,

			// 基本设备信息
			DeviceID:       networkDevice.DeviceID,
			SN:             networkDevice.Device.SN,
			Brand:          networkDevice.Device.Brand,
			Model:          networkDevice.Device.Model,
			AssetType:      networkDevice.Device.AssetType,
			AssetStatus:    networkDevice.Device.AssetStatus,
			HardwareStatus: networkDevice.Device.HardwareStatus,
			Remark:         networkDevice.Device.Remark,
		}

		// 添加套餐模板名称
		if networkDevice.Device.Template != nil {
			template := networkDevice.Device.Template
			deviceInfo.TemplateName = template.TemplateName
			deviceInfo.TemplateID = template.ID
			deviceInfo.CPUModel = template.CPUModel
			deviceInfo.MemoryCapacity = template.MemoryCapacity
			deviceInfo.GPUModel = template.GPUModel
			deviceInfo.DiskType = template.DiskType
		} else {
			// 如果没有关联模板，使用角色映射的模板名称
			deviceInfo.TemplateName = asset.GetPackageNameByRole(networkDevice.Role)
		}

		// 添加资源信息（项目、机柜、机房）
		if networkDevice.Device.Resource != nil {
			resource := networkDevice.Device.Resource
			deviceInfo.ResourceID = resource.ID
			deviceInfo.Project = resource.Project
			deviceInfo.BizStatus = resource.BizStatus
			deviceInfo.ResStatus = resource.ResStatus
			deviceInfo.Cluster = resource.Cluster
			deviceInfo.VpcIP = resource.VpcIP
			deviceInfo.BmcIP = resource.BmcIP
			deviceInfo.IsBackup = resource.IsBackup
			deviceInfo.ResourceRemark = resource.Remark

			// 财务信息
			deviceInfo.PurchaseOrder = networkDevice.Device.PurchaseOrder
			if !time.Time(networkDevice.Device.PurchaseDate).IsZero() {
				deviceInfo.PurchaseDate = time.Time(networkDevice.Device.PurchaseDate).Format("2006-01-02")
			}
			if !time.Time(networkDevice.Device.WarrantyExpire).IsZero() {
				deviceInfo.WarrantyExpire = time.Time(networkDevice.Device.WarrantyExpire).Format("2006-01-02")
			}
			deviceInfo.Price = networkDevice.Device.Price
			deviceInfo.ResidualValue = networkDevice.Device.ResidualValue

			// 转换日期
			if !time.Time(resource.RackingTime).IsZero() {
				deviceInfo.RackingTime = time.Time(resource.RackingTime).Format("2006-01-02")
			}
			if !time.Time(resource.DeliveryTime).IsZero() {
				deviceInfo.DeliveryTime = time.Time(resource.DeliveryTime).Format("2006-01-02")
			}

			// 机柜信息
			if resource.CabinetID > 0 && resource.Cabinet.ID > 0 {
				cabinet := resource.Cabinet
				deviceInfo.CabinetID = cabinet.ID
				deviceInfo.CabinetName = cabinet.Name
				deviceInfo.RackPosition = resource.RackPosition
				deviceInfo.UnitHeight = resource.Height

				// 机柜详情
				deviceInfo.CabinetRow = cabinet.Row
				deviceInfo.CabinetColumn = cabinet.Column
				deviceInfo.CabinetType = cabinet.CabinetType
				deviceInfo.NetworkEnvironment = cabinet.NetworkEnvironment
				deviceInfo.BondType = cabinet.BondType
				deviceInfo.TotalPower = cabinet.TotalPower

				// 位置信息
				if cabinet.Room.ID > 0 {
					room := cabinet.Room
					deviceInfo.RoomID = room.ID
					deviceInfo.RoomName = room.Name

					if room.DataCenter.ID > 0 {
						dataCenter := room.DataCenter
						//deviceInfo.DataCenterID = dataCenter.ID
						//deviceInfo.DataCenterName = dataCenter.Name
						//deviceInfo.DataCenterAddress = dataCenter.Address

						if dataCenter.AZ.ID > 0 {
							az := dataCenter.AZ
							deviceInfo.AZID = az.ID
							deviceInfo.AZName = az.Name

							if az.Region.ID > 0 {
								region := az.Region
								deviceInfo.RegionID = region.ID
								deviceInfo.RegionName = region.Name
							}
						}
					}
				}
			}

			// 仓库信息
			if resource.Room != nil && resource.Room.DataCenter != nil {
				dataCenter := resource.Room.DataCenter
				deviceInfo.DataCenterID = dataCenter.ID
				deviceInfo.DataCenterName = dataCenter.Name
				deviceInfo.DataCenterAddress = dataCenter.Address
			}
		}

		result = append(result, deviceInfo)
	}

	return result, total, nil
}

// GetDetailByID 获取网络设备详情（包含设备和资源信息）
func (r *networkDeviceRepository) GetDetailByID(ctx context.Context, id uint) (*asset.NetworkDeviceWithDeviceInfo, error) {
	var networkDevice asset.NetworkDevice
	if err := r.db.WithContext(ctx).
		Preload("Device", func(db *gorm.DB) *gorm.DB {
			return db.Preload("Resource.Cabinet.Room.DataCenter.AZ.Region").Preload("Template")
		}).
		First(&networkDevice, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("网络设备不存在")
		}
		return nil, err
	}

	if networkDevice.Device == nil {
		return nil, errors.New("设备信息不存在")
	}

	deviceInfo := &asset.NetworkDeviceWithDeviceInfo{
		// 网络设备信息
		ID:                networkDevice.ID,
		Role:              networkDevice.Role,
		FirmwareVersion:   networkDevice.FirmwareVersion,
		LoopbackAddress:   networkDevice.LoopbackAddress,
		ManagementAddress: networkDevice.ManagementAddress,

		Ports:            networkDevice.Ports,            // 添加
		PortSpeed:        networkDevice.PortSpeed,        // 添加
		StackSupport:     networkDevice.StackSupport,     // 添加
		StackID:          networkDevice.StackID,          // 添加
		StackRole:        networkDevice.StackRole,        // 添加
		Layer:            networkDevice.Layer,            // 添加
		RoutingProtocols: networkDevice.RoutingProtocols, // 添加

		// 基本设备信息
		DeviceID:       networkDevice.DeviceID,
		SN:             networkDevice.Device.SN,
		Brand:          networkDevice.Device.Brand,
		Model:          networkDevice.Device.Model,
		AssetType:      networkDevice.Device.AssetType,
		AssetStatus:    networkDevice.Device.AssetStatus,
		HardwareStatus: networkDevice.Device.HardwareStatus,
		CreatedAt:      networkDevice.CreatedAt,
		UpdatedAt:      networkDevice.UpdatedAt,
		Remark:         networkDevice.Device.Remark, // 添加
	}

	// 添加套餐模板名称
	if networkDevice.Device.Template != nil {
		template := networkDevice.Device.Template
		deviceInfo.TemplateName = template.TemplateName
		deviceInfo.TemplateID = template.ID
		deviceInfo.CPUModel = template.CPUModel
		deviceInfo.MemoryCapacity = template.MemoryCapacity
		deviceInfo.GPUModel = template.GPUModel
		deviceInfo.DiskType = template.DiskType
	} else {
		// 如果没有关联模板，使用角色映射的模板名称
		deviceInfo.TemplateName = asset.GetPackageNameByRole(networkDevice.Role)
	}

	// 资源信息
	if networkDevice.Device.Resource != nil {
		resource := networkDevice.Device.Resource
		deviceInfo.ResourceID = resource.ID
		deviceInfo.Project = resource.Project
		deviceInfo.BizStatus = resource.BizStatus
		deviceInfo.ResStatus = resource.ResStatus
		deviceInfo.Cluster = resource.Cluster
		deviceInfo.VpcIP = resource.VpcIP
		deviceInfo.BmcIP = resource.BmcIP
		deviceInfo.IsBackup = resource.IsBackup
		deviceInfo.ResourceRemark = resource.Remark

		// 转换日期
		if !time.Time(resource.RackingTime).IsZero() {
			deviceInfo.RackingTime = time.Time(resource.RackingTime).Format("2006-01-02")
		}
		if !time.Time(resource.DeliveryTime).IsZero() {
			deviceInfo.DeliveryTime = time.Time(resource.DeliveryTime).Format("2006-01-02")
		}

		// 机柜信息
		if resource.CabinetID > 0 && resource.Cabinet.ID > 0 {
			cabinet := resource.Cabinet
			deviceInfo.CabinetID = cabinet.ID
			deviceInfo.CabinetName = cabinet.Name
			deviceInfo.RackPosition = resource.RackPosition
			deviceInfo.UnitHeight = resource.Height

			// 机柜详情
			deviceInfo.CabinetRow = cabinet.Row
			deviceInfo.CabinetColumn = cabinet.Column
			deviceInfo.CabinetType = cabinet.CabinetType
			deviceInfo.NetworkEnvironment = cabinet.NetworkEnvironment
			deviceInfo.BondType = cabinet.BondType
			deviceInfo.TotalPower = cabinet.TotalPower

			// 位置信息
			if cabinet.Room.ID > 0 {
				room := cabinet.Room
				deviceInfo.RoomID = room.ID
				deviceInfo.RoomName = room.Name

				if room.DataCenter.ID > 0 {
					dataCenter := room.DataCenter
					deviceInfo.DataCenterID = dataCenter.ID
					deviceInfo.DataCenterName = dataCenter.Name
					deviceInfo.DataCenterAddress = dataCenter.Address

					if dataCenter.AZ.ID > 0 {
						az := dataCenter.AZ
						deviceInfo.AZID = az.ID
						deviceInfo.AZName = az.Name

						if az.Region.ID > 0 {
							region := az.Region
							deviceInfo.RegionID = region.ID
							deviceInfo.RegionName = region.Name
						}
					}
				}
			}
		}
	}

	// 财务信息
	deviceInfo.PurchaseOrder = networkDevice.Device.PurchaseOrder
	if !time.Time(networkDevice.Device.PurchaseDate).IsZero() {
		deviceInfo.PurchaseDate = time.Time(networkDevice.Device.PurchaseDate).Format("2006-01-02")
	}
	if !time.Time(networkDevice.Device.WarrantyExpire).IsZero() {
		deviceInfo.WarrantyExpire = time.Time(networkDevice.Device.WarrantyExpire).Format("2006-01-02")
	}
	deviceInfo.Price = networkDevice.Device.Price
	deviceInfo.ResidualValue = networkDevice.Device.ResidualValue

	return deviceInfo, nil
}
