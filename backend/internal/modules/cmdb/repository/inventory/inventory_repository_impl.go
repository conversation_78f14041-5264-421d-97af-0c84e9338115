package inventory

import (
	"backend/internal/common/constants"
	"backend/internal/common/utils"
	"backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/cmdb/model/inventory"
	"backend/internal/modules/cmdb/model/outbound"
	ticketmodel "backend/internal/modules/ticket/model"
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

// inventoryRepository 库存仓库实现
type inventoryRepository struct {
	db *gorm.DB
}

// NewInventoryRepository 创建库存仓库
func NewInventoryRepository(db *gorm.DB) InventoryRepository {
	return &inventoryRepository{db: db}
}

// Create 创建库存明细
func (r *inventoryRepository) Create(ctx context.Context, detail *inventory.InventoryDetail) error {
	// 确保可用库存数值正确
	detail.AvailableStock = detail.CurrentStock - detail.AllocatedStock

	if detail.AvailableStock < 0 {
		return errors.New("已分配库存不能大于当前库存")
	}

	return r.db.WithContext(ctx).Create(detail).Error
}

// Update 更新库存明细
func (r *inventoryRepository) Update(ctx context.Context, detail *inventory.InventoryDetail) error {
	var original inventory.InventoryDetail
	if err := r.db.WithContext(ctx).First(&original, detail.ID).Error; err != nil {
		return err
	}

	// 确保可用库存数值正确
	detail.AvailableStock = detail.CurrentStock - detail.AllocatedStock

	if detail.AvailableStock < 0 {
		return errors.New("已分配库存不能大于当前库存")
	}

	// 保留创建时间
	detail.CreatedAt = original.CreatedAt
	return r.db.WithContext(ctx).Save(detail).Error
}

// Delete 删除库存明细
func (r *inventoryRepository) Delete(ctx context.Context, id uint) error {
	// 检查是否有分配的库存
	var detail inventory.InventoryDetail
	if err := r.db.WithContext(ctx).First(&detail, id).Error; err != nil {
		return err
	}

	if detail.AllocatedStock > 0 {
		return errors.New("该库存有已分配的物品，无法删除")
	}

	return r.db.WithContext(ctx).Delete(&inventory.InventoryDetail{}, id).Error
}

// GetByID 根据ID获取库存明细
func (r *inventoryRepository) GetByID(ctx context.Context, id uint) (*inventory.InventoryDetail, error) {
	var detail inventory.InventoryDetail
	if err := r.db.WithContext(ctx).Preload("Product").First(&detail, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("库存明细不存在")
		}
		return nil, err
	}
	return &detail, nil
}

// List 分页查询库存明细列表
func (r *inventoryRepository) List(ctx context.Context, page, pageSize int, productID uint, warehouse string) ([]*inventory.InventoryDetail, int64, error) {
	var details []*inventory.InventoryDetail
	var total int64

	db := r.db.WithContext(ctx).Model(&inventory.InventoryDetail{}).Preload("Product")

	// 查询条件
	if productID > 0 {
		db = db.Where("product_id = ?", productID)
	}

	if warehouse != "" {
		db = db.Where("warehouse = ? OR warehouse_id IN (SELECT id FROM warehouses WHERE name LIKE ?)",
			warehouse, "%"+warehouse+"%")
	}

	// 统计总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		db = db.Offset(offset).Limit(pageSize)
	}

	// 获取结果
	if err := db.Find(&details).Error; err != nil {
		return nil, 0, err
	}

	// 更新每条记录的仓库名称
	for _, detail := range details {
		if detail.WarehouseID > 0 {
			var warehouseName string
			if err := r.db.WithContext(ctx).Table("warehouses").
				Select("name").
				Where("id = ?", detail.WarehouseID).
				Row().Scan(&warehouseName); err == nil && warehouseName != "" {
				detail.Warehouse = warehouseName
			}
		}
	}

	return details, total, nil
}

// 定义结果结构体，包含来自多个模型的字段
type CombinedResult struct {
	Inbound struct {
		CreateID uint   `json:"create_id"`
		CreateBy string `json:"create_by"`
	}

	Outbound struct {
		ReporterID   uint   `json:"reporter_id"`
		ReporterName string `json:"reporter_name"`
	} `json:"outbound"`
}

// AdjustStock 调整库存数量
func (r *inventoryRepository) AdjustStock(ctx context.Context, id uint, quantity int, changeType, reason string, inboundID, outboundID uint) error {
	var result CombinedResult
	// 验证库存明细是否存在
	detail, err := r.GetByID(ctx, id)
	if err != nil {
		return err
	}

	// 获取操作用户
	var operatorID uint
	var operator = "system"

	// 使用正确的键名获取用户ID
	if userID, ok := ctx.Value(constants.ContextKeyUserID).(uint); ok {
		operatorID = userID
	}

	// 从userInfo中获取用户真实姓名
	if userInfo, ok := ctx.Value(constants.ContextKeyUserInfo).(map[string]interface{}); ok {
		if realName, ok := userInfo["realName"].(string); ok && realName != "" {
			operator = realName
		}
	}
	switch changeType {
	case constants.ChangeTypeInbound, constants.ChangeTypeOutbound:
		if inboundID == 0 {
			r.db.WithContext(ctx).Model(&outbound.SpareOutboundTicket{}).Select("reporter_id, reporter_name").Where("id = ?", outboundID).First(&result.Outbound)
			operatorID = result.Outbound.ReporterID
			operator = result.Outbound.ReporterName
		} else {
			r.db.WithContext(ctx).Model(&ticketmodel.InboundTicket{}).Select("create_id, create_by").Where("id = ?", inboundID).First(&result.Inbound)
			operatorID = result.Inbound.CreateID
			operator = result.Inbound.CreateBy
		}

	}
	// 开始事务
	//tx := r.db.WithContext(ctx).Begin()
	tx := utils.GetDB(ctx, r.db)
	if tx.Error != nil {
		return tx.Error
	}

	// 记录原库存数量
	oldStock := detail.CurrentStock

	// 计算新库存
	newStock := oldStock

	// 计算新的可用库存 - 考虑状态变化和库存调整类型
	var newAvailableStock int

	// 根据库存变更类型和原因进行不同处理
	switch changeType {
	case constants.ChangeTypeInbound:
		// 入库操作应增加可用库存（除非是特殊情况）
		newAvailableStock = detail.AvailableStock + quantity
		newStock = newStock + quantity
		//if reason == constants.ChangeReasonNewPurchase || reason == constants.ChangeReasonAllocate {
		//	// 新购和调拨会增加库存数量
		//	newStock = newStock + quantity
		//	if newStock < 0 {
		//		tx.Rollback()
		//		return errors.New("库存不足，无法调整")
		//	}
		//}
	case constants.ChangeTypeOutbound:
		// 出库操作应减少可用库存
		newAvailableStock = detail.AvailableStock - quantity
		// 安全检查，防止可用库存小于零
		if newAvailableStock < 0 {
			//tx.Rollback()
			return errors.New("可用库存不足，无法出库")
		}
		newStock = newStock - quantity
		if newStock < 0 {
			//tx.Rollback()
			return errors.New("库存不足，无法调整")
		}
		//if reason == constants.ChangeReasonSell || reason == constants.ChangeReasonAllocate {
		//	newStock = newStock - quantity
		//	if newStock < 0 {
		//		tx.Rollback()
		//		return errors.New("库存不足，无法调整")
		//	}
		//}
	case constants.ChangeTypeAdjust:
		// 调整类型，根据reason判断
		if strings.Contains(reason, "报废") {
			// 报废相关操作，只修改总库存，不影响可用库存计算方式
			newAvailableStock = detail.AvailableStock + quantity
		} else {
			// 默认情况：可用库存 = 总库存 - 已分配库存
			newAvailableStock = newStock - detail.AllocatedStock
		}
	case constants.ChangeTypeSync:
		// 系统同步，直接调整
		newAvailableStock = newStock - detail.AllocatedStock
	default:
		// 其他未知类型，使用安全的计算方式
		newAvailableStock = newStock - detail.AllocatedStock
	}

	// 安全检查，确保可用库存非负
	if newAvailableStock < 0 {
		//tx.Rollback()
		return errors.New("调整后的可用库存将小于零，请检查库存状态")
	}

	// 更新库存数量
	if err = tx.Model(&inventory.InventoryDetail{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"current_stock":   newStock,
			"available_stock": newAvailableStock,
			"updated_at":      time.Now(),
		}).Error; err != nil {
		//tx.Rollback()
		return err
	}

	// 记录库存变更历史
	stockHistory := inventory.StockHistory{
		DetailID:     id,
		ProductID:    detail.ProductID,
		OldQuantity:  oldStock,
		NewQuantity:  newStock,
		ChangeAmount: quantity,
		ChangeType:   changeType,
		ChangeTime:   time.Now(),
		OperatorID:   operatorID,
		Operator:     operator,
		Reason:       reason,
		WarehouseID:  detail.WarehouseID,
		InboundID:    inboundID,
		OutboundID:   outboundID,
	}

	if err = tx.Create(&stockHistory).Error; err != nil {
		//tx.Rollback()
		return err
	}

	// 提交事务
	//return tx.Commit().Error
	return nil
}

// AllocateStock 分配库存
func (r *inventoryRepository) AllocateStock(ctx context.Context, id uint, quantity int, purpose string) error {
	if quantity <= 0 {
		return errors.New("分配数量必须大于零")
	}

	// 验证库存明细是否存在
	detail, err := r.GetByID(ctx, id)
	if err != nil {
		return err
	}

	// 检查可用库存是否足够
	if detail.AvailableStock < quantity {
		return errors.New("可用库存不足")
	}

	// 获取操作用户
	var operatorID uint
	var operator = "system"

	// 使用正确的键名获取用户ID
	if userID, ok := ctx.Value(constants.ContextKeyUserID).(uint); ok {
		operatorID = userID
	}

	// 从userInfo中获取用户真实姓名
	if userInfo, ok := ctx.Value(constants.ContextKeyUserInfo).(map[string]interface{}); ok {
		if realName, ok := userInfo["realName"].(string); ok && realName != "" {
			operator = realName
		}
	}

	// 开始事务
	//tx := r.db.WithContext(ctx).Begin()
	tx := utils.GetDB(ctx, r.db)
	if tx.Error != nil {
		return tx.Error
	}

	// 记录原分配库存数量
	oldAllocated := detail.AllocatedStock
	newAllocated := oldAllocated + quantity

	// 直接从可用库存中减去分配的数量 - 这样更符合业务语义，而不是重新计算
	newAvailable := detail.AvailableStock - quantity

	// 再次确保可用库存非负（防止并发问题）
	if newAvailable < 0 {
		//tx.Rollback()
		return errors.New("可用库存不足，无法完成分配")
	}

	// 更新库存分配
	if err := tx.Model(&inventory.InventoryDetail{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"allocated_stock": newAllocated,
			"available_stock": newAvailable,
			"updated_at":      time.Now(),
		}).Error; err != nil {
		//tx.Rollback()
		return err
	}

	// 记录库存分配历史
	stockHistory := inventory.StockHistory{
		DetailID:     id,
		ProductID:    detail.ProductID,
		OldQuantity:  oldAllocated,
		NewQuantity:  newAllocated,
		ChangeAmount: quantity,
		ChangeType:   constants.ChangeTypeAllocate,
		ChangeTime:   time.Now(),
		OperatorID:   operatorID,
		Operator:     operator,
		Reason:       purpose,
		WarehouseID:  detail.WarehouseID,
	}

	if err := tx.Create(&stockHistory).Error; err != nil {
		//tx.Rollback()
		return err
	}

	// 提交事务
	//return tx.Commit().Error
	return nil
}

// ReleaseAllocatedStock 释放已分配库存
func (r *inventoryRepository) ReleaseAllocatedStock(ctx context.Context, id uint, quantity int) error {
	if quantity <= 0 {
		return errors.New("释放数量必须大于零")
	}

	// 验证库存明细是否存在
	detail, err := r.GetByID(ctx, id)
	if err != nil {
		return err
	}

	// 检查已分配库存是否足够
	if detail.AllocatedStock < quantity {
		return fmt.Errorf("规格: %s, %s, 已分配库存：%d,需要转换的数量: %d", detail.Product.PN, detail.Product.MaterialType, detail.AllocatedStock, quantity)
	}

	// 获取操作用户
	var (
		operatorID uint
		operator   = "system"
		reason     = "释放已分配库存"
	)

	// 使用正确的键名获取用户ID
	if userID, ok := ctx.Value(constants.ContextKeyUserID).(uint); ok {
		operatorID = userID
	}

	// 从userInfo中获取用户真实姓名
	if userInfo, ok := ctx.Value(constants.ContextKeyUserInfo).(map[string]interface{}); ok {
		if realName, ok := userInfo["realName"].(string); ok && realName != "" {
			operator = realName
		}
	}

	if rea, ok := ctx.Value(constants.ContextKeyChangeReason).(string); ok && rea != "" {
		reason = rea
	}

	// 开始事务
	//tx := r.db.WithContext(ctx).Begin()
	tx := utils.GetDB(ctx, r.db)
	if tx.Error != nil {
		return tx.Error
	}

	// 记录原分配库存数量
	oldAllocated := detail.AllocatedStock
	newAllocated := oldAllocated - quantity

	// 直接增加可用库存数量 - 这样更符合业务语义，而不是重新计算
	newAvailable := detail.AvailableStock + quantity

	// 确保可用库存不超过总库存（防止并发问题）
	if newAvailable > detail.CurrentStock {
		newAvailable = detail.CurrentStock
	}

	// 更新库存分配
	if err := tx.Model(&inventory.InventoryDetail{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"allocated_stock": newAllocated,
			"available_stock": newAvailable,
			"updated_at":      time.Now(),
		}).Error; err != nil {
		//tx.Rollback()
		return err
	}

	// 记录库存释放历史
	stockHistory := inventory.StockHistory{
		DetailID:     id,
		ProductID:    detail.ProductID,
		OldQuantity:  oldAllocated,
		NewQuantity:  newAllocated,
		ChangeAmount: -quantity, // 负值表示释放
		ChangeType:   constants.ChangeTypeRelease,
		ChangeTime:   time.Now(),
		OperatorID:   operatorID,
		Operator:     operator,
		Reason:       reason,
		WarehouseID:  detail.WarehouseID,
	}

	if err := tx.Create(&stockHistory).Error; err != nil {
		//tx.Rollback()
		return err
	}

	// 提交事务
	//return tx.Commit().Error
	return nil
}

// RemoveAllocatedStock 减少当前仓库库存和已分配库存
func (r *inventoryRepository) RemoveAllocatedStock(ctx context.Context, id uint, quantity int, purpose string) error {

	if quantity <= 0 {
		return errors.New("数量必须大于零")
	}

	// 验证库存明细是否存在
	detail, err := r.GetByID(ctx, id)
	if err != nil {
		return err
	}

	// 检查可用库存是否足够
	if detail.AllocatedStock < quantity || detail.CurrentStock < quantity {
		return errors.New("可用库存不足")
	}

	// 获取操作用户
	var (
		operatorID uint
		operator   = "system"
		outboundID uint
	)

	// 使用正确的键名获取用户ID
	if userID, ok := ctx.Value(constants.ContextKeyUserID).(uint); ok {
		operatorID = userID
	}

	// 从userInfo中获取用户真实姓名
	if userInfo, ok := ctx.Value(constants.ContextKeyUserInfo).(map[string]interface{}); ok {
		if realName, ok := userInfo["realName"].(string); ok && realName != "" {
			operator = realName
		}
	}

	if outID, ok := ctx.Value("outboundID").(uint); ok {
		outboundID = outID
	}

	tx := utils.GetDB(ctx, r.db)
	if tx.Error != nil {
		return tx.Error
	}

	// 记录原分配库存数量
	oldAllocated := detail.AllocatedStock
	newAllocated := oldAllocated - quantity

	// 记录库存数量
	newCurrentStock := detail.CurrentStock - quantity

	// 再次确保库存非负（防止并发问题）
	if newCurrentStock < 0 || newAllocated < 0 {
		return errors.New("库存不足，无法完成分配")
	}

	// 更新库存分配
	if err = tx.Model(&inventory.InventoryDetail{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"allocated_stock": newAllocated,
			"current_stock":   newCurrentStock,
			"updated_at":      time.Now(),
		}).Error; err != nil {
		return err
	}

	// 记录库存分配历史
	stockHistory := inventory.StockHistory{
		DetailID:     id,
		ProductID:    detail.ProductID,
		OldQuantity:  oldAllocated,
		NewQuantity:  newAllocated,
		ChangeAmount: quantity,
		ChangeType:   constants.ChangeTypeOutbound,
		ChangeTime:   time.Now(),
		OperatorID:   operatorID,
		Operator:     operator,
		Reason:       purpose,
		OutboundID:   outboundID,
		WarehouseID:  detail.WarehouseID,
	}

	if err = tx.Create(&stockHistory).Error; err != nil {
		return err
	}

	return nil
}

// GetProductInventorySummary 获取产品库存汇总
func (r *inventoryRepository) GetProductInventorySummary(ctx context.Context, productID uint) (*inventory.InventorySummary, error) {
	// 验证产品是否存在
	var count int64
	if err := r.db.WithContext(ctx).Model(&inventory.InventoryDetail{}).Where("product_id = ?", productID).Count(&count).Error; err != nil {
		return nil, err
	}

	// 查询产品基本信息
	var product struct {
		Model string
		PN    string
	}
	if err := r.db.WithContext(ctx).Table("products").Select("model, pn").Where("id = ?", productID).First(&product).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
	}

	// 查询库存汇总信息
	var summary inventory.InventorySummary
	summary.ProductID = productID
	summary.ProductModel = product.Model
	summary.ProductPN = product.PN

	// 获取库存总量
	var stockSum struct {
		TotalStock     int
		AllocatedStock int
		AvailableStock int
	}
	if err := r.db.WithContext(ctx).
		Model(&inventory.InventoryDetail{}).
		Select("SUM(current_stock) AS total_stock, SUM(allocated_stock) AS allocated_stock, SUM(available_stock) AS available_stock").
		Where("product_id = ?", productID).
		First(&stockSum).Error; err != nil {
		return nil, err
	}

	// 获取实际备件数量
	var spareCount int64
	if err := r.db.WithContext(ctx).
		Model(&asset.AssetSpare{}).
		Where("product_id = ? AND asset_status = 'idle'", productID).
		Count(&spareCount).Error; err != nil {
		return nil, err
	}

	// 如果备件数量与库存数量不一致，使用备件数量作为实际库存
	if int(spareCount) != stockSum.TotalStock {
		summary.TotalStock = int(spareCount)
		summary.AvailableStock = int(spareCount) - stockSum.AllocatedStock
		summary.AllocatedStock = stockSum.AllocatedStock
	} else {
		summary.TotalStock = stockSum.TotalStock
		summary.AllocatedStock = stockSum.AllocatedStock
		summary.AvailableStock = stockSum.AvailableStock
	}

	// 统计入库记录数
	if err := r.db.WithContext(ctx).
		Model(&inventory.StockHistory{}).
		Where("product_id = ? AND change_type = 'inbound'", productID).
		Count(&count).Error; err != nil {
		return nil, err
	}
	summary.InboundItems = int(count)

	// 统计出库记录数
	if err := r.db.WithContext(ctx).
		Model(&inventory.StockHistory{}).
		Where("product_id = ? AND change_type = 'outbound'", productID).
		Count(&count).Error; err != nil {
		return nil, err
	}
	summary.OutboundItems = int(count)

	return &summary, nil
}

// GetLowStockProducts 获取低库存产品列表
func (r *inventoryRepository) GetLowStockProducts(ctx context.Context, threshold int) ([]*inventory.LowStockProduct, error) {
	var products []*inventory.LowStockProduct

	// 子查询：获取每个产品的库存总量
	subQuery := r.db.WithContext(ctx).
		Table("inventory_details").
		Select("product_id, SUM(current_stock) as current_stock").
		Group("product_id").
		Having("SUM(current_stock) <= ?", threshold)

	// 主查询：获取低库存产品信息
	err := r.db.WithContext(ctx).
		Table("(?) as sub", subQuery).
		Select("sub.product_id, products.model as product_model, sub.current_stock, ? as threshold, products.product_category as category",
			threshold).
		Joins("JOIN products ON sub.product_id = products.id").
		Order("sub.current_stock").
		Find(&products).Error

	if err != nil {
		return nil, err
	}

	return products, nil
}

// GetExpiringWarrantyItems 获取即将过保的库存项目
func (r *inventoryRepository) GetExpiringWarrantyItems(ctx context.Context, days int) ([]*inventory.ExpiringWarrantyItem, error) {
	var items []*inventory.ExpiringWarrantyItem

	// 计算即将过保的日期范围
	now := time.Now()
	futureDate := now.AddDate(0, 0, days)

	// 查询即将过保的库存项目
	err := r.db.WithContext(ctx).
		Model(&inventory.InventoryDetail{}).
		Select(`
			inventory_details.id,
			inventory_details.product_id,
			products.model as product_model,
			inventory_details.batch_number,
			inventory_details.warranty_end,
			DATEDIFF(inventory_details.warranty_end, NOW()) as remaining_days,
			inventory_details.current_stock
		`).
		Joins("JOIN products ON inventory_details.product_id = products.id").
		Where("inventory_details.warranty_end BETWEEN ? AND ? AND inventory_details.current_stock > 0 ",
			now, futureDate).
		Order("inventory_details.warranty_end").
		Find(&items).Error

	if err != nil {
		return nil, err
	}

	return items, nil
}

// ListByWarehouse 根据仓库ID获取库存明细
func (r *inventoryRepository) ListByWarehouse(ctx context.Context, warehouseID uint, page, pageSize int) ([]*inventory.InventoryDetail, int64, error) {
	var details []*inventory.InventoryDetail
	var total int64

	// 验证仓库是否存在
	var count int64
	if err := r.db.WithContext(ctx).Table("warehouses").Where("id = ?", warehouseID).Count(&count).Error; err != nil {
		return nil, 0, err
	}
	if count == 0 {
		return nil, 0, errors.New("仓库不存在")
	}

	// 获取仓库名称
	var warehouseName string
	if err := r.db.WithContext(ctx).Table("warehouses").
		Select("name").
		Where("id = ?", warehouseID).
		Row().Scan(&warehouseName); err != nil {
		warehouseName = "未知仓库"
	}

	// 基础查询
	db := r.db.WithContext(ctx).Model(&inventory.InventoryDetail{}).
		Preload("Product").
		Where("warehouse_id = ?", warehouseID)

	// 统计总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		db = db.Offset(offset).Limit(pageSize)
	}

	// 获取结果
	if err := db.Find(&details).Error; err != nil {
		return nil, 0, err
	}

	// 更新所有记录的仓库名称
	for _, detail := range details {
		detail.Warehouse = warehouseName
	}

	return details, total, nil
}

// GetStockHistory 获取库存变更历史
func (r *inventoryRepository) GetStockHistory(ctx context.Context, detailID, productID, warehouseID uint, startTime, endTime time.Time) ([]*inventory.StockHistory, error) {
	var histories []*inventory.StockHistory

	// 验证库存明细是否存在
	if detailID > 0 {
		var count int64
		if err := r.db.WithContext(ctx).Model(&inventory.InventoryDetail{}).Where("id = ?", detailID).Count(&count).Error; err != nil {
			return nil, err
		}
		if count == 0 {
			return nil, fmt.Errorf("库存明细ID %d 不存在", detailID)
		}
	}

	// 构建查询
	query := r.db.WithContext(ctx).Model(&inventory.StockHistory{})

	// 按库存明细ID筛选
	if detailID > 0 {
		query = query.Where("detail_id = ?", detailID)
	} else if productID > 0 && warehouseID > 0 {
		// 如果没有detailID但有productID和warehouseID，则按productID和warehouseID筛选
		query = query.Where("product_id = ? AND warehouse_id = ?", productID, warehouseID)
	}

	// 时间范围筛选
	if !startTime.IsZero() {
		query = query.Where("change_time >= ?", startTime)
	}

	if !endTime.IsZero() {
		query = query.Where("change_time <= ?", endTime)
	}

	// 执行查询
	err := query.Order("change_time DESC").Find(&histories).Error
	if err != nil {
		return nil, err
	}

	return histories, nil
}

// GetByProductIDAndWarehouseID 根据产品ID和仓库ID获取库存明细
func (r *inventoryRepository) GetByProductIDAndWarehouseID(ctx context.Context, productID, warehouseID uint) (*inventory.InventoryDetail, error) {
	var detail inventory.InventoryDetail
	if err := r.db.WithContext(ctx).
		Preload("Product").
		Where("product_id = ? AND warehouse_id = ?", productID, warehouseID).
		FirstOrCreate(&detail).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("库存明细不存在")
		}
		return nil, err
	}
	return &detail, nil
}

// 获取特定仓库中某一种规格的最新信息
func (r *inventoryRepository) GetLatestInventoryDetail(ctx context.Context, productID, warehouseID uint) (*inventory.InventoryDetail, error) {
	var inventoryDetail inventory.InventoryDetail

	// 使用 GORM 构建查询条件
	err := r.db.WithContext(ctx). // 传入上下文
					Where("product_id = ? AND warehouse_id = ?", productID, warehouseID). // 筛选条件
					Order("created_at DESC").                                             // 按创建时间倒序，取最新记录
					First(&inventoryDetail).                                              // 查询单条记录
					Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			detail := &inventory.InventoryDetail{
				ProductID:   productID,
				WarehouseID: warehouseID,
			}
			r.db.WithContext(ctx).Create(&detail)
			return detail, nil
		} else {
			return nil, fmt.Errorf("获取详情信息失败：%v", err.Error())
		}
	}

	return &inventoryDetail, nil // 返回查询到的库存记录
}

// ListWithParams 根据扩展参数查询库存列表
func (r *inventoryRepository) ListWithParams(ctx context.Context, page, pageSize int, params map[string]interface{}) ([]*inventory.InventoryDetail, int64, error) {
	var (
		details []*inventory.InventoryDetail
		total   int64
	)

	// 创建基础查询
	query := r.db.WithContext(ctx).Model(&inventory.InventoryDetail{})

	// 创建预加载查询
	query = query.Preload("Product")

	// 处理基础参数
	if productID, ok := params["productID"].(uint); ok && productID > 0 {
		query = query.Where("product_id = ?", productID)
	}

	if warehouse, ok := params["warehouse"].(string); ok && warehouse != "" {
		query = query.Where("warehouse LIKE ?", "%"+warehouse+"%")
	}

	joinedProduct := false

	// 处理新增查询参数
	if materialType, ok := params["materialType"].(string); ok && materialType != "" {
		// 物料类型通过关联查询Product表
		query = query.Joins("JOIN products p ON inventory_details.product_id = p.id")
		joinedProduct = true
		query = query.Where("p.material_type = ?", materialType)
	}

	if pn, ok := params["pn"].(string); ok && pn != "" {
		// PN号码通过关联查询Product表
		if !joinedProduct {
			query = query.Joins("JOIN products p ON inventory_details.product_id = p.id")
			joinedProduct = true
		}
		query = query.Where("p.pn = ?", pn)
	}

	if spec, ok := params["spec"].(string); ok && spec != "" {
		// 规格通过关联查询Product表
		if !joinedProduct {
			query = query.Joins("JOIN products p ON inventory_details.product_id = p.id")
			joinedProduct = true
		}
		query = query.Where("p.spec = ?", spec)
	}

	if brand, ok := params["brand"].(string); ok && brand != "" {
		// 品牌通过关联查询Product表
		if !joinedProduct {
			query = query.Joins("JOIN products ON inventory_details.product_id = products.id").
				Where("products.brand = ?", brand)
		}
		query = query.Where("products.brand = ?", brand)
	}

	if warehouseId, ok := params["warehouseId"].(uint); ok && warehouseId > 0 {
		query = query.Where("warehouse_id = ?", warehouseId)
	}

	// 处理可用库存查询
	if hasAvailable, ok := params["hasAvailable"].(bool); ok {
		if hasAvailable {
			// 查询有可用库存的记录
			query = query.Where("available_stock > 0")
		} else {
			// 查询无可用库存的记录
			query = query.Where("available_stock <= 0")
		}
	}

	// 如果没有设置 showAll 或 showAll 为 false，则排除库存都为 0 的记录
	if showAll, _ := params["showAll"].(bool); !showAll {
		query = query.Where("NOT (current_stock = 0 AND allocated_stock = 0 AND available_stock = 0)")
	}

	if viewMode, ok := params["viewMode"].(string); ok && viewMode != "" {
		var deviceTypes = []string{
			"服务器", "GPU服务器", "网络设备", "存储设备",
			"交换机", "路由器", "防火墙", "负载均衡器", "其他",
		}
		// 根据视图模式进行不同的查询
		switch viewMode {
		case "part":
			query = query.Joins("JOIN products ON inventory_details.product_id = products.id").Where("products.material_type NOT IN ?", deviceTypes)
		case "device":
			query = query.Joins("JOIN products ON inventory_details.product_id = products.id").Where("products.material_type IN ?", deviceTypes)
		default:
			return nil, 0, fmt.Errorf("未知视图模式: %s", viewMode)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	query = query.Offset(offset).Limit(pageSize).Order("id DESC")

	// 执行查询
	if err := query.Find(&details).Error; err != nil {
		return nil, 0, err
	}

	// 更新仓库名称
	for _, detail := range details {
		if detail.WarehouseID > 0 {
			var warehouseName string
			if err := r.db.WithContext(ctx).Table("warehouses").
				Select("name").
				Where("id = ?", detail.WarehouseID).
				Row().Scan(&warehouseName); err == nil && warehouseName != "" {
				detail.Warehouse = warehouseName
			}
		}
	}

	return details, total, nil
}
