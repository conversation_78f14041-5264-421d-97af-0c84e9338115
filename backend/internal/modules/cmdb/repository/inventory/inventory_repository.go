package inventory

import (
	"backend/internal/modules/cmdb/model/inventory"
	"context"
	"time"
)

// InventoryRepository 库存仓库接口
type InventoryRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, detail *inventory.InventoryDetail) error
	Update(ctx context.Context, detail *inventory.InventoryDetail) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*inventory.InventoryDetail, error)
	List(ctx context.Context, page, pageSize int, productID uint, warehouse string) ([]*inventory.InventoryDetail, int64, error)
	// 支持扩展查询参数的列表方法
	ListWithParams(ctx context.Context, page, pageSize int, params map[string]interface{}) ([]*inventory.InventoryDetail, int64, error)
	GetByProductIDAndWarehouseID(ctx context.Context, productID, warehouseID uint) (*inventory.InventoryDetail, error)
	// 库存操作
	AdjustStock(ctx context.Context, id uint, quantity int, changeType, reason string, inboundID, outboundID uint) error
	AllocateStock(ctx context.Context, id uint, quantity int, purpose string) error
	ReleaseAllocatedStock(ctx context.Context, id uint, quantity int) error
	RemoveAllocatedStock(ctx context.Context, id uint, quantity int, purpose string)error

	// 统计和报表
	GetProductInventorySummary(ctx context.Context, productID uint) (*inventory.InventorySummary, error)
	GetLowStockProducts(ctx context.Context, threshold int) ([]*inventory.LowStockProduct, error)
	GetExpiringWarrantyItems(ctx context.Context, days int) ([]*inventory.ExpiringWarrantyItem, error)

	// 按仓库查询
	ListByWarehouse(ctx context.Context, warehouseID uint, page, pageSize int) ([]*inventory.InventoryDetail, int64, error)

	// 历史记录
	GetStockHistory(ctx context.Context, detailID, productID, warehouseID uint, startTime, endTime time.Time) ([]*inventory.StockHistory, error)

	// 获取特定仓库中某一种规格的最新信息,提供给配件新购入库使用
	GetLatestInventoryDetail(ctx context.Context, productID, warehouseID uint) (*inventory.InventoryDetail, error)
}
