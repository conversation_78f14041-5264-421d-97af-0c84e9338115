package template

import (
	"context"

	"backend/internal/modules/cmdb/model/template"

	"gorm.io/gorm"
)

// TemplateComponentRepository 模板组件仓库接口
type TemplateComponentRepository interface {
	// ListByTemplateID 根据模板ID获取组件列表
	ListByTemplateID(ctx context.Context, templateID uint) ([]*template.TemplateComponent, error)
	// Create 创建模板组件
	Create(ctx context.Context, component *template.TemplateComponent) (*template.TemplateComponent, error)
	// Update 更新模板组件
	Update(ctx context.Context, component *template.TemplateComponent) (*template.TemplateComponent, error)
	// Delete 删除模板组件
	Delete(ctx context.Context, id uint) error
	// DeleteByTemplateID 根据模板ID删除所有组件
	DeleteByTemplateID(ctx context.Context, templateID uint) error
	// BatchCreate 批量创建模板组件
	BatchCreate(ctx context.Context, components []*template.TemplateComponent) error
}

// templateComponentRepository 模板组件仓库实现
type templateComponentRepository struct {
	db *gorm.DB
}

// NewTemplateComponentRepository 创建模板组件仓库
func NewTemplateComponentRepository(db *gorm.DB) TemplateComponentRepository {
	return &templateComponentRepository{db: db}
}

// ListByTemplateID 根据模板ID获取组件列表
func (r *templateComponentRepository) ListByTemplateID(ctx context.Context, templateID uint) ([]*template.TemplateComponent, error) {
	var components []*template.TemplateComponent
	if err := r.db.WithContext(ctx).Preload("Product").
		Where("template_id = ?", templateID).
		Find(&components).Error; err != nil {
		return nil, err
	}
	return components, nil
}

// Create 创建模板组件
func (r *templateComponentRepository) Create(ctx context.Context, component *template.TemplateComponent) (*template.TemplateComponent, error) {
	if err := r.db.WithContext(ctx).Create(component).Error; err != nil {
		return nil, err
	}
	return component, nil
}

// Update 更新模板组件
func (r *templateComponentRepository) Update(ctx context.Context, component *template.TemplateComponent) (*template.TemplateComponent, error) {
	if err := r.db.WithContext(ctx).Model(component).Updates(map[string]interface{}{
		"product_id": component.ProductID,
		"quantity":   component.Quantity,
		"slot":       component.Slot,
	}).Error; err != nil {
		return nil, err
	}
	return component, nil
}

// Delete 删除模板组件
func (r *templateComponentRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&template.TemplateComponent{}, id).Error
}

// DeleteByTemplateID 根据模板ID删除所有组件
func (r *templateComponentRepository) DeleteByTemplateID(ctx context.Context, templateID uint) error {
	return r.db.WithContext(ctx).Where("template_id = ?", templateID).Delete(&template.TemplateComponent{}).Error
}

// BatchCreate 批量创建模板组件
func (r *templateComponentRepository) BatchCreate(ctx context.Context, components []*template.TemplateComponent) error {
	return r.db.WithContext(ctx).Create(&components).Error
}
