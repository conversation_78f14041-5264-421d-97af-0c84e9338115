package outbound

import (
	"backend/internal/common/constants"
	"backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/cmdb/model/inventory"
	locationModel "backend/internal/modules/cmdb/model/location"
	"backend/internal/modules/cmdb/model/outbound"
	cmdbCommon "backend/internal/modules/cmdb/model/outbound/common"
	outboundRep "backend/internal/modules/cmdb/repository/outbound"
	assetService "backend/internal/modules/cmdb/service/asset"
	inventoryService "backend/internal/modules/cmdb/service/inventory"
	"backend/internal/modules/cmdb/service/product"
	cmdbWorkflow "backend/internal/modules/cmdb/workflow"
	exportModel "backend/internal/modules/export/model"
	"backend/internal/modules/ticket/common"
	"context" // 使用别名避免冲突
	cryptorand "crypto/rand"
	"database/sql"
	"encoding/binary"
	"errors"
	"fmt"
	"io"
	"math"
	"mime/multipart"
	"os"
	"path/filepath"
	"regexp"
	"slices"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"

	"github.com/xuri/excelize/v2"
	"go.temporal.io/sdk/client"
	"go.uber.org/zap"
)

// outboundTicketService 出库单服务接口
type OutboundTicketService interface {
	CreateOutboundTicket(ctx context.Context, ticket *outbound.SpareOutboundTicket) error
	GetOutboundTicketByID(ctx context.Context, id uint) (*outbound.SpareOutboundTicket, error)
	GetOutboundTicketByTicketNo(ctx context.Context, ticketNo string) (*outbound.SpareOutboundTicket, error)
	UpdateOutboundTicket(ctx context.Context, ticket *outbound.SpareOutboundTicket) error
	UpdateOutboundTicketStatus(ctx context.Context, id uint, status string, operatorID uint, operatorName string) error
	//AssignOutboundTicket(ctx context.Context, id uint, engineerID uint) error
	CloseOutboundTicket(ctx context.Context, id uint) error
	GetOutboundTicketStatusHistory(ctx context.Context, ticketID uint) ([]*outbound.OutboundTicketStatusHistory, error)
	//StartOutboundTicketWorkflow(ctx context.Context, ticketID uint) error
	RecoverInconsistentWorkflows(ctx context.Context) error
	TriggerWorkflowStage(ctx context.Context, ticketID uint, stage string, operatorID uint, operatorName string, comments string, data map[string]interface{}) error
	//CreateRepairSelection(ctx context.Context, selection *model.RepairSelection) error
	CreateApproval(ctx context.Context, approval *outbound.OutboundApproval) error
	GetApprovalById(ctx context.Context, id uint) (*outbound.OutboundApproval, error)
	UpdateApproval(ctx context.Context, approval *outbound.OutboundApproval) error
	//CreateVerification(ctx context.Context, verification *model.Verification) error

	/** 重构后的接口 */
	// 创建出库单
	CreateOutboundTicketV2(ctx context.Context, ticket *outbound.SpareOutboundTicket) error

	// 工作流相关
	StartWorkflow(ctx context.Context, ticketID, userID uint, userName string) error
	TriggerWorkflowStageV2(ctx context.Context, ticket *outbound.SpareOutboundTicket, triggers cmdbCommon.OutboundTrigger) error

	// GET
	GetTicketInfoByID(ctx context.Context, ticketID uint) ([]outbound.OutboundInfo, error)
	GetTicketDetailsByID(ctx context.Context, ticketID uint) ([]outbound.OutboundDetail, error)
	GetTicketDetailsListByID(ctx context.Context, ticketID uint, page, pageSize int) ([]outbound.OutboundDetail, int64, error)
	GetDetailByID(ctx context.Context, detailID uint) (*outbound.OutboundDetail, error)
	GetDetailsLen(ctx context.Context, ticketID uint) (uint, error)
	ListOutboundTickets(ctx context.Context, page, pageSize int, query, stage, outboundType, outboundReason, project string) ([]*outbound.SpareOutboundTicket, int64, error)

	// Update
	UpdateOutboundTicketDetails(ctx context.Context, detail []outbound.OutboundDetail) error
	UpdateOutboundTicketDetailsByImport(ctx context.Context, importForm *outbound.OutboundDetailImport) error
	UpdateTicketAndHistory(ctx context.Context, ticket *outbound.SpareOutboundTicket, history *outbound.OutboundTicketStatusHistory) error

	// Inventory 库存相关
	UpdateCMDB(ctx context.Context, outboundType, outboundReason string, ticketID uint) error
	TransitionToMiddleStatus(ctx context.Context, outboundType, outboundReason, MiddleStatus string, TicketID uint) error
	RevertStatus(ctx context.Context, outboundType, outboundReason, Status string, TicketID uint) error

	// 生命周期相关
	CreateDeviceLifeCycleLog(ctx context.Context, ticket *outbound.SpareOutboundTicket, devices []asset.Device, oldAssetStatus string) error
}

// outboundTicketService 报障单服务实现
type outboundTicketService struct {
	repo outboundRep.OutboundTicketRepository
	//repairSelectionRepo  repository.RepairSelectionRepository
	outboundApprovalRepo outboundRep.OutboundApprovalRepository
	//verificationRepo     repository.VerificationRepository
	deviceSvc      assetService.DeviceService
	productSvc     product.ProductService
	spareSvc       assetService.SpareService
	temporalClient client.Client
	inventorySvc   inventoryService.InventoryService
	logger         *zap.Logger
}

// NewoutboundTicketService 创建报障单服务
func NewOutboundTicketService(
	repo outboundRep.OutboundTicketRepository,
	//repo repository.FaultTicketRepository,
	//repairSelectionRepo repository.RepairSelectionRepository,
	outboundApprovalRepo outboundRep.OutboundApprovalRepository,
	//verificationRepo repository.VerificationRepository,
	deviceSvc assetService.DeviceService,
	productSvc product.ProductService,
	inventorySvc inventoryService.InventoryService,
	spareSvc assetService.SpareService,
	temporalClient client.Client,
	logger *zap.Logger,
) OutboundTicketService {
	return &outboundTicketService{
		repo: repo,
		//repairSelectionRepo:  repairSelectionRepo,
		outboundApprovalRepo: outboundApprovalRepo,
		//verificationRepo:     verificationRepo,
		deviceSvc:      deviceSvc,
		productSvc:     productSvc,
		inventorySvc:   inventorySvc,
		spareSvc:       spareSvc,
		temporalClient: temporalClient,
		logger:         logger,
	}
}

// CreateOutboundTicket 创建出库单
func (s *outboundTicketService) CreateOutboundTicket(ctx context.Context, ticket *outbound.SpareOutboundTicket) error {
	// 生成工单号
	ticket.TicketNo = s.generateTicketNo(ticket.OutboundType, ticket.OutboundReason)

	// 设置初始状态
	ticket.Status = cmdbWorkflow.StatusWaitingApproval

	// 创建出库单
	if err := s.repo.Create(ctx, ticket); err != nil {
		return fmt.Errorf("创建出库单失败: %w", err)
	}

	// 记录状态历史
	history := &outbound.OutboundTicketStatusHistory{
		OutboundTicketID: ticket.ID,
		PreviousStatus:   "",
		NewStatus:        ticket.Status,
		OperatorID:       ticket.ReporterID,
		OperatorName:     ticket.ReporterName,
		OperationTime:    time.Now(),
		Remarks:          "创建出库单",
	}
	if err := s.repo.CreateStatusHistory(ctx, history); err != nil {
		// 仅记录日志，不影响主流程
		fmt.Printf("记录状态历史失败: %v\n", err)
	}

	// 启动工作流
	if s.temporalClient != nil {
		workflowID := fmt.Sprintf("outbound_ticket_%d", ticket.ID)
		_, err := s.temporalClient.ExecuteWorkflow(ctx, client.StartWorkflowOptions{
			ID:        workflowID,
			TaskQueue: cmdbWorkflow.OutboundTicketTaskQueue,
		}, cmdbWorkflow.OutboundTicketWorkflow, cmdbWorkflow.OutboundTicketWorkflowInput{
			TicketID:       ticket.ID,
			OutboundType:   ticket.OutboundType,
			OutboundReason: ticket.OutboundReason,
			ReporterID:     ticket.ReporterID,
			ReporterName:   ticket.ReporterName,
			Status:         ticket.Status,
			TicketNo:       ticket.TicketNo,
		})
		if err != nil {
			// 仅记录日志，不影响主流程
			fmt.Printf("启动工作流失败: %v\n", err)
		}
	}

	return nil
}

// 创建出库单
func (s *outboundTicketService) CreateOutboundTicketV2(ctx context.Context, ticket *outbound.SpareOutboundTicket) error {
	var (
		details []outbound.OutboundDetail
	)
	// 根据出库类型进行数据绑定判断
	if err := s.validateOutboundTypeFields(ctx, ticket); err != nil {
		return err
	}
	// 校验出库设备仓库中是否存在
	err := s.validAssetExist(ctx, ticket)
	if ticket.OutboundReason == constants.SourceTypeAllocate {
		if ticket.DestWarehouseID == ticket.SourceWarehouseID {
			return fmt.Errorf("调拨出库的出库位置和目标位置不能相同")
		}
	}
	if err != nil {
		return err
	}
	ticket.TicketNo = s.generateTicketNo(ticket.OutboundType, ticket.OutboundReason)
	history := &outbound.OutboundTicketStatusHistory{
		Stage:          cmdbCommon.StageSubmitApply,
		NewStatus:      cmdbCommon.StatusSubmitingApply,
		PreviousStatus: "",
		OperatorID:     ticket.ReporterID,
		OperatorName:   ticket.ReporterName,
		OperationTime:  time.Now(),
	}

	for _, info := range ticket.Info {
		if info.Amount > math.MaxInt {
			return fmt.Errorf("商品数量超过系统限制: %d", info.Amount)
		}
		amount := info.Amount
		for i := 0; i < amount; i++ {
			detail := outbound.OutboundDetail{
				OutboundType: ticket.OutboundType,
				ProductID:    info.ProductID,
				//TemplateID:   info.TemplateID,
				//DeviceSN:     info.DeviceSN,
				//ComponentSN:  info.ComponentSNs,
			}
			details = append(details, detail)
		}
	}
	ticket.Details = details
	err = s.repo.CreateV2(ctx, ticket, history)
	if err != nil {
		return err
	}

	return nil
}

// GetOutboundTicketByID 根据ID获取出库单
func (s *outboundTicketService) GetOutboundTicketByID(ctx context.Context, id uint) (*outbound.SpareOutboundTicket, error) {
	return s.repo.GetByID(ctx, id)
}

// GetOutboundTicketByTicketNo 根据工单号获取出库单
func (s *outboundTicketService) GetOutboundTicketByTicketNo(ctx context.Context, ticketNo string) (*outbound.SpareOutboundTicket, error) {
	return s.repo.GetByTicketNo(ctx, ticketNo)
}

// UpdateOutboundTicket 更新出库单
func (s *outboundTicketService) UpdateOutboundTicket(ctx context.Context, ticket *outbound.SpareOutboundTicket) error {
	return s.repo.Update(ctx, ticket)
}

// UpdateOutboundTicketDetails 更新出库单
func (s *outboundTicketService) UpdateOutboundTicketDetails(ctx context.Context, details []outbound.OutboundDetail) error {
	var (
		deviceSNs           []string
		componentSNs        []string
		exitSNs             []string
		unExitSNs           []string
		assetStatusNotMatch []string // 用于存储状态不匹配的SN
		snUnfitPn           []string // 用于存储SN与产品PN不匹配的SN
		projectNoMatch      []string // 用于存储项目不匹配的SN
	)
	snMap := make(map[string]bool)   // 用于去重
	tempMap := make(map[string]bool) // 临时变量
	detailTrans, err := s.GetDetailByID(ctx, details[0].ID)
	if err != nil {
		return fmt.Errorf("获取详情信息失败: %w", err)
	}
	ticket, err := s.GetOutboundTicketByID(ctx, detailTrans.SpareOutboundTicketID)
	if err != nil {
		return fmt.Errorf("获取工单信息失败: %w", err)
	}
	if len(details) == 0 {
		return fmt.Errorf("更新数据不能为空")
	}

	// 检查是否填写完整、重复的SN
	for i, detail := range details {
		if detail.ID == 0 {
			return fmt.Errorf("第%d行详情ID不能为空：", i+1)
		}
		if detail.ProductID == 0 {
			return fmt.Errorf("第%d行的规格ID不能为空：", i+1)
		}
		switch ticket.OutboundType {
		case cmdbWorkflow.OutboundTypePart:
			if detail.ComponentSN == "" {
				return fmt.Errorf("第 %d 行的备件SN不能为空", i+1)
			}
			if snMap[detail.ComponentSN] {
				return fmt.Errorf("第 %d 行的备件SN重复: %s", i+1, detail.ComponentSN)
			}
			snMap[detail.ComponentSN] = true
			componentSNs = append(componentSNs, detail.ComponentSN)
			// 如果为改配出库，则需要验证设备SN
			if ticket.OutboundReason == cmdbCommon.OutboundReasonReplacement && ticket.Stage == cmdbCommon.StageReplaceApproval {
				if detail.DeviceSN == "" {
					return fmt.Errorf("第 %d 行的设备SN不能为空", i+1)
				}
				if tempMap[detail.DeviceSN] {
					continue
				}
				tempMap[detail.DeviceSN] = true
				deviceSNs = append(deviceSNs, detail.DeviceSN)
			}
		case cmdbWorkflow.OutboundTypeDevice:
			if detail.DeviceSN == "" {
				return fmt.Errorf("第 %d 行的设备SN不能为空", i+1)
			}
			if snMap[detail.DeviceSN] {
				return fmt.Errorf("第 %d 行的设备SN重复: %s", i+1, detail.DeviceSN)
			}
			snMap[detail.DeviceSN] = true
			deviceSNs = append(deviceSNs, detail.DeviceSN)
		}
	}

	switch ticket.OutboundType {
	case cmdbWorkflow.OutboundTypePart:
		spares, err := s.spareSvc.GetSpareBySNs(ctx, componentSNs)
		if err != nil {
			return err
		}
		for i, spare := range spares {
			if spare.AssetStatus != constants.AssetStatusIdle {
				assetStatusNotMatch = append(assetStatusNotMatch, spare.SN)
			}

			if spare.ProductID != details[i].ProductID {
				snUnfitPn = append(snUnfitPn, spare.SN)
			}
			if spare.WarehouseID != ticket.SourceWarehouseID {
				projectNoMatch = append(projectNoMatch, spare.SN)
			}
			exitSNs = append(exitSNs, spare.SN)
		}

		for _, componentSN := range componentSNs {
			if !slices.Contains(exitSNs, componentSN) {
				unExitSNs = append(unExitSNs, componentSN)
			}
		}

		if len(unExitSNs) > 0 {
			return fmt.Errorf("以下配件SN不存在: %v", unExitSNs)
		}

		if len(assetStatusNotMatch) > 0 && ticket.Stage != cmdbCommon.StageReplaceApproval {
			return fmt.Errorf("以下配件的状态不为“闲置中”，无法出库：%v", assetStatusNotMatch)
		}
		if len(snUnfitPn) > 0 {
			return fmt.Errorf("以下配件SN与入库明细中的产品PN不匹配: %v", snUnfitPn)
		}
		if len(projectNoMatch) > 0 {
			return fmt.Errorf("无法跨项目出库以下配件SN: %v", projectNoMatch)
		}
		// 如果是改配出库，校验服务器与配件的位置是否统一
		if ticket.OutboundReason == cmdbCommon.OutboundReasonReplacement && ticket.Stage == cmdbCommon.StageReplaceApproval {
			devices, _, err := s.deviceSvc.ListBySNsV2(ctx, deviceSNs, 2)
			if err != nil {
				return err
			}
			for _, device := range devices {
				if device.Resource.Project != ticket.Project {
					projectNoMatch = append(projectNoMatch, device.SN)
				}
			}
			if len(projectNoMatch) > 0 {
				return fmt.Errorf("以下设备不属于%s项目: %v", ticket.Project, projectNoMatch)
			}
		}

	case cmdbWorkflow.OutboundTypeDevice:
		devices, _, err := s.deviceSvc.ListBySNsV2(ctx, deviceSNs, 2)
		if err != nil {
			return err
		}
		for i, device := range devices {
			if device.AssetStatus != constants.AssetStatusIdle {
				assetStatusNotMatch = append(assetStatusNotMatch, device.SN)
			}
			if device.ProductID != details[i].ProductID {
				snUnfitPn = append(snUnfitPn, device.SN)
			}
			if !strings.EqualFold(device.Resource.Project, ticket.Project) {
				projectNoMatch = append(projectNoMatch, device.SN)
			}
			exitSNs = append(exitSNs, device.SN)
		}

		for _, deviceSN := range deviceSNs {
			if !slices.Contains(exitSNs, deviceSN) {
				unExitSNs = append(unExitSNs, deviceSN)
			}
		}

		if len(unExitSNs) > 0 {
			return fmt.Errorf("以下设备SN不存在：%v", unExitSNs)
		}

		if len(assetStatusNotMatch) > 0 {
			return fmt.Errorf("以下设备的状态不为“闲置中”，无法出库：%v", assetStatusNotMatch)
		}
		if len(snUnfitPn) > 0 {
			return fmt.Errorf("以下设备SN与入库明细中的产品PN不匹配: %v", snUnfitPn)
		}
		if len(projectNoMatch) > 0 {
			return fmt.Errorf("无法跨项目出库设备，SN: %v", projectNoMatch)
		}
	}

	return s.repo.UpdateDetails(ctx, details)
}

// validateSNs 校验设备SN和组件SN的有效性
//func (s *outboundTicketService) validateSNs(ctx context.Context, deviceSNs, componentSNs []string, outboundType string) error {
//	var (
//		invalidDeviceSNs     []string
//		invalidComponentSNs  []string
//		existingDeviceSNs    []string
//		existingComponentSNs []string
//	)
//
//	// 校验设备SN
//	if len(deviceSNs) > 0 {
//		// 查询设备SN是否存在
//		devices, _, err := s.deviceSvc.ListBySNs(ctx, deviceSNs)
//		if err != nil {
//			return err
//		}
//		for _, device := range devices {
//			existingDeviceSNs = append(existingDeviceSNs, device.SN)
//		}
//
//		// 找出不存在的SN
//		existMap := make(map[string]bool)
//		for _, sn := range existingDeviceSNs {
//			existMap[sn] = true
//		}
//		for _, sn := range deviceSNs {
//			if !existMap[sn] {
//				invalidDeviceSNs = append(invalidDeviceSNs, sn)
//			}
//		}
//		if len(invalidDeviceSNs) > 0 {
//			return fmt.Errorf("以下设备SN不存在: %v; ", invalidDeviceSNs)
//		}
//	}
//
//	// 校验组件SN
//	if len(componentSNs) > 0 {
//		// 查询组件SN是否存在
//		spares, err := s.spareSvc.GetSpareBySNs(ctx, componentSNs)
//		if err != nil {
//			return err
//		}
//		for _, spare := range spares {
//			existingComponentSNs = append(existingComponentSNs, spare.SN)
//		}
//
//		// 找出不存在的SN
//		existMap := make(map[string]bool)
//		for _, sn := range existingComponentSNs {
//			existMap[sn] = true
//		}
//		for _, sn := range componentSNs {
//			if !existMap[sn] {
//				invalidComponentSNs = append(invalidComponentSNs, sn)
//			}
//		}
//		if len(invalidComponentSNs) > 0 {
//			return fmt.Errorf("以下组件SN不存在: %v", invalidComponentSNs)
//		}
//	}
//	return nil
//}

// UpdateOutboundTicketStatus 更新出库单状态
func (s *outboundTicketService) UpdateOutboundTicketStatus(ctx context.Context, id uint, status string, operatorID uint, operatorName string) error {
	// 使用事务处理状态更新
	return s.repo.WithTransaction(ctx, func(txCtx context.Context, repo outboundRep.OutboundTicketRepository) error {
		// 获取当前出库单
		ticket, err := repo.GetByID(txCtx, id)
		if err != nil {
			s.logger.Error("获取出库单失败",
				zap.Error(err),
				zap.Uint("ticketID", id))
			return fmt.Errorf("获取出库单失败: %w", err)
		}

		// 如果状态没有变化，则直接返回
		if ticket.Status == status {
			s.logger.Info("出库单状态未变化，无需更新",
				zap.Uint("ticketID", id),
				zap.String("status", status))
			return nil
		}

		previousStatus := ticket.Status

		// 更新工单状态相关字段
		ticket.Status = status
		ticket.WaitingManualTrigger = false // 更新状态时清除等待标记
		ticket.CurrentWaitingStage = ""     // 清除当前等待阶段

		// 根据新状态更新工单时间字段
		now := time.Now()
		switch status {
		case common.StatusCompleted:
			if ticket.CloseTime == nil {
				ticket.CloseTime = &now
			}
		}

		// 更新出库单状态
		if err := repo.Update(txCtx, ticket); err != nil {
			s.logger.Error("更新出库单状态失败",
				zap.Error(err),
				zap.Uint("ticketID", id),
				zap.String("oldStatus", previousStatus),
				zap.String("newStatus", status))
			return fmt.Errorf("更新出库单状态失败: %w", err)
		}

		// 记录状态历史
		history := &outbound.OutboundTicketStatusHistory{
			OutboundTicketID: id,
			PreviousStatus:   previousStatus,
			NewStatus:        status,
			OperatorID:       operatorID,
			OperatorName:     operatorName,
			OperationTime:    now,
			Remarks:          fmt.Sprintf("状态从 %s 变更为 %s", previousStatus, status),
			ActivityCategory: getOutboundActivityCategory(status),
		}

		// 如果是系统操作，添加标记
		if operatorID == 0 {
			history.OperatorName = "系统"
			history.Remarks = fmt.Sprintf("系统自动将状态从 %s 变更为 %s", previousStatus, status)
		}

		if err := repo.CreateStatusHistory(txCtx, history); err != nil {
			s.logger.Error("记录状态历史失败",
				zap.Error(err),
				zap.Uint("ticketID", id),
				zap.String("oldStatus", previousStatus),
				zap.String("newStatus", status))
			// 仅记录日志，不影响主流程
			return nil
		}

		s.logger.Info("出库单状态更新成功",
			zap.Uint("ticketID", id),
			zap.String("oldStatus", previousStatus),
			zap.String("newStatus", status),
			zap.String("operator", operatorName))

		return nil
	})
}

// getOutboundActivityCategory 根据状态获取活动类别
func getOutboundActivityCategory(status string) string {
	switch status {
	case cmdbWorkflow.StatusWaitingApproval, cmdbWorkflow.StatusWaitingSecondApproval:
		return "approval"
	case common.StatusCompleted:
		return "completion"
	case common.StatusCancelled:
		return "cancellation"
	default:
		return "status_change"
	}
}

// isSLAPauseStatus 判断是否为SLA暂停状态
//func isSLAPauseStatus(status string) bool {
//	switch status {
//	case common.StatusWaitingApproval, common.StatusApprovedWaiting:
//		return true
//	default:
//		return false
//	}
//}

// getPauseReason 获取暂停原因
//func getPauseReason(status string) string {
//	switch status {
//	case common.StatusWaitingApproval:
//		return "等待客户审批"
//	case common.StatusApprovedWaiting:
//		return "等待开始维修"
//	default:
//		return ""
//	}
//}

// CloseOutboundTicket 关闭出库单
func (s *outboundTicketService) CloseOutboundTicket(ctx context.Context, id uint) error {
	// 获取当前出库单
	ticket, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("获取出库单失败: %w", err)
	}

	// 更新状态为已完成
	ticket.Status = common.StatusCompleted
	now := time.Now()
	ticket.CloseTime = &now

	if err := s.repo.Update(ctx, ticket); err != nil {
		return fmt.Errorf("更新出库单状态失败: %w", err)
	}

	// 记录状态历史
	history := &outbound.OutboundTicketStatusHistory{
		OutboundTicketID: id,
		PreviousStatus:   ticket.Status,
		NewStatus:        common.StatusCompleted,
		OperatorID:       ticket.ReporterID,
		OperatorName:     ticket.ReporterName,
		OperationTime:    now,
		Remarks:          "关闭出库单",
	}
	if err := s.repo.CreateStatusHistory(ctx, history); err != nil {
		// 仅记录日志，不影响主流程
		fmt.Printf("记录状态历史失败: %v\n", err)
	}

	return nil
}

// ListOutboundTickets 获取出库单列表
func (s *outboundTicketService) ListOutboundTickets(ctx context.Context, page, pageSize int, query, stage, outboundType, outboundReason, project string) ([]*outbound.SpareOutboundTicket, int64, error) {
	return s.repo.List(ctx, page, pageSize, query, stage, outboundType, outboundReason, project)
}

// GetOutboundTicketStatusHistory 获取出库单状态历史
func (s *outboundTicketService) GetOutboundTicketStatusHistory(ctx context.Context, id uint) ([]*outbound.OutboundTicketStatusHistory, error) {
	return s.repo.GetStatusHistory(ctx, id)
}

// isTemporalConnectionError 判断是否为Temporal连接错误
//func isTemporalConnectionError(err error) bool {
//	if err == nil {
//		return false
//	}
//
//	errMsg := err.Error()
//	return strings.Contains(errMsg, "connection refused") ||
//		strings.Contains(errMsg, "deadline exceeded") ||
//		strings.Contains(errMsg, "connection reset") ||
//		strings.Contains(errMsg, "broken pipe") ||
//		strings.Contains(errMsg, "connect: connection timed out")
//}

// generateTicketNo 生成工单号
func (s *outboundTicketService) generateTicketNo(outboundType, outboundReason string) string {
	// 使用时间戳和安全随机数生成工单号
	timestamp := time.Now().Format("20060102150405")

	// 生成安全的随机数
	var random int
	randomBytes := make([]byte, 4)

	// 尝试使用crypto/rand包获取随机字节
	if _, err := cryptorand.Read(randomBytes); err != nil {
		// 如果安全随机数生成失败，使用时间纳秒作为备选
		s.logger.Warn("使用加密随机数失败，使用时间纳秒代替", zap.Error(err))
		random = int(time.Now().UnixNano() % 1000)
	} else {
		// 将随机字节转换为 0-999 范围内的随机数
		random = int(binary.BigEndian.Uint32(randomBytes) % 1000)
	}

	// 生成工单号
	switch outboundType {
	case cmdbWorkflow.OutboundTypePart:
		switch outboundReason {
		case cmdbWorkflow.OutboundReasonRepair:
			return fmt.Sprintf("RPOT%s%03d", timestamp, random)
		case cmdbWorkflow.OutboundReasonReplace:
			return fmt.Sprintf("RPOT%s%03d", timestamp, random)
		case cmdbWorkflow.OutboundReasonSell:
			return fmt.Sprintf("SPOT%s%03d", timestamp, random)
		case cmdbWorkflow.OutboundReasonAllocate:
			return fmt.Sprintf("APOT%s%03d", timestamp, random)
		case cmdbWorkflow.OutboundReasonReturnRepair:
			return fmt.Sprintf("RRPOT%s%03d", timestamp, random)
		}
	case cmdbWorkflow.OutboundTypeDevice:
		switch outboundReason {
		case cmdbWorkflow.OutboundReasonRack:
			return fmt.Sprintf("RDOT%s%03d", timestamp, random)
		case cmdbWorkflow.OutboundReasonAllocate:
			return fmt.Sprintf("ADOT%s%03d", timestamp, random)
		}
	}

	return fmt.Sprintf("tmp%s%03d", timestamp, random)
}

// UintPtr 辅助函数，返回指向uint的指针
func UintPtr(v uint) *uint {
	return &v
}

// RecoverInconsistentWorkflows 恢复不一致的工作流状态
func (s *outboundTicketService) RecoverInconsistentWorkflows(ctx context.Context) error {
	if s.temporalClient == nil {
		return fmt.Errorf("工作流客户端未初始化")
	}

	// 获取所有未完成的出库单
	tickets, _, err := s.repo.List(ctx, 1, 1000, "", "", "", "", "")
	if err != nil {
		return fmt.Errorf("获取出库单列表失败: %w", err)
	}

	for _, ticket := range tickets {
		if ticket.Status == cmdbWorkflow.StatusCompleted || ticket.Status == cmdbWorkflow.StatusCancelled || ticket.Status == cmdbWorkflow.StatusRejected {
			//fmt.Printf("工单ID=%d已处于终态(%s)，不重新启动工作流\n", ticket.ID, ticket.Status)
			continue
		}
		workflowID := fmt.Sprintf("outbound_ticket_%d", ticket.ID)
		// 检查工作流是否存在
		_, err := s.temporalClient.DescribeWorkflowExecution(ctx, workflowID, "")
		if err != nil {
			// 工作流不存在，重新启动
			_, err := s.temporalClient.ExecuteWorkflow(ctx, client.StartWorkflowOptions{
				ID:        workflowID,
				TaskQueue: cmdbWorkflow.OutboundTicketTaskQueue,
			}, cmdbWorkflow.OutboundTicketWorkflow, cmdbWorkflow.OutboundTicketWorkflowInput{
				TicketID:     ticket.ID,
				Status:       ticket.Status,
				TicketNo:     ticket.TicketNo,
				OutboundType: ticket.OutboundType,
			})
			if err != nil {
				fmt.Printf("恢复工作流失败 [%d]: %v\n", ticket.ID, err)
			}
		}
	}

	return nil
}

// TriggerWorkflowStage 触发工作流阶段
func (s *outboundTicketService) TriggerWorkflowStage(
	ctx context.Context,
	ticketID uint,
	stage string,
	operatorID uint,
	operatorName string,
	comments string,
	data map[string]interface{},
) error {
	// 验证工作流阶段参数
	if stage == "" {
		return fmt.Errorf("工作流阶段不能为空")
	}

	// 获取当前工单，但不更新其状态
	ticket, err := s.repo.GetByID(ctx, ticketID)
	if err != nil {
		return fmt.Errorf("获取工单失败: %w", err)
	}

	// 验证阶段名是否有效
	isValid, message := cmdbWorkflow.ValidateStageTransition(ticket.Status, stage)
	if !isValid {
		return fmt.Errorf("无效的工作流阶段: %s - %s", stage, message)
	}

	// 构造工作流ID
	workflowID := fmt.Sprintf("outbound_ticket_%d", ticketID)

	// 构造工作流控制信号
	signal := common.WorkflowControlSignal{
		Stage:        stage,
		OperatorID:   operatorID,
		OperatorName: operatorName,
		Comments:     comments,
		Data:         data,
	}

	s.logger.Info("准备触发工作流信号",
		zap.String("workflowID", workflowID),
		zap.String("stage", stage),
		zap.Uint("ticketID", ticketID))

	// 触发工作流信号
	if err := s.temporalClient.SignalWorkflow(ctx, workflowID, "", cmdbWorkflow.WorkflowControlSignalName, signal); err != nil {
		s.logger.Error("触发工作流信号失败",
			zap.Error(err),
			zap.String("workflowID", workflowID),
			zap.String("stage", stage),
			zap.Uint("ticketID", ticketID))
		return fmt.Errorf("触发工作流失败: %w", err)
	}

	s.logger.Info("成功触发工作流信号",
		zap.String("workflowID", workflowID),
		zap.String("stage", stage),
		zap.Uint("ticketID", ticketID))

	return nil
}

func (s *outboundTicketService) TriggerWorkflowStageV2(ctx context.Context, ticket *outbound.SpareOutboundTicket, triggers cmdbCommon.OutboundTrigger) error {
	// 验证工作流阶段参数
	if triggers.Status == "" {
		return fmt.Errorf("工作流状态不能为空")
	}
	var (
		updateName string
		signal     cmdbCommon.WorkflowSignal
	)

	// 选择更新的工作流
	switch ticket.OutboundType {
	case cmdbWorkflow.OutboundTypePart:
		updateName = cmdbCommon.PartOutboundUpdateSignal
		signal = cmdbCommon.PartOutboundSignal{
			Status:       triggers.Status,
			OperatorID:   triggers.OperatorID,
			OperatorName: triggers.OperatorName,
			Comments:     triggers.Comments,
		}
	case cmdbWorkflow.OutboundTypeDevice:
		updateName = cmdbCommon.DeviceOutboundUpdateSignal
		signal = cmdbCommon.DeviceOutboundSignal{
			Status:       triggers.Status,
			OperatorID:   triggers.OperatorID,
			OperatorName: triggers.OperatorName,
			Comments:     triggers.Comments,
		}
	default:
		return fmt.Errorf("目前不支持的出库类型: %s", ticket.OutboundType)
	}
	// 构造工作流ID
	workflowID := fmt.Sprintf("outbound_ticket_%d", ticket.ID)

	s.logger.Info("准备更新工作流状态",
		zap.String("workflowID", workflowID),
		zap.String("stage", triggers.Stage),
		zap.String("status", triggers.Status),
		zap.String("operatorName", triggers.OperatorName),
		zap.Uint("ticketID", ticket.ID))
	// 更新工作流状态
	updateOptions := client.UpdateWorkflowOptions{
		WorkflowID:   workflowID,
		UpdateName:   updateName,
		WaitForStage: client.WorkflowUpdateStageCompleted,
		Args:         []interface{}{signal},
	}
	updateHandle, err := s.temporalClient.UpdateWorkflow(ctx, updateOptions)
	if err != nil {
		s.logger.Error("更新工作流状态失败",
			zap.Error(err),
			zap.String("workflowID", workflowID),
			zap.String("stage", triggers.Stage),
			zap.String("status", triggers.Status),
			zap.String("operatorName", triggers.OperatorName),
			zap.Uint("ticketID", ticket.ID))
		return fmt.Errorf("更新工作流状态失败: %w", err)
	}

	err = updateHandle.Get(ctx, nil)
	if err != nil {
		s.logger.Error("Unable to get update result", zap.Error(err))
		return err
	}

	s.logger.Info("成功更新出库工作流",
		zap.String("workflowID", workflowID),
		zap.String("stage", triggers.Stage),
		zap.String("status", triggers.Status),
		zap.Uint("ticketID", ticket.ID))

	return nil
}

// // CreateRepairSelection 创建维修选择记录
//
//	func (s *outboundTicketService) CreateRepairSelection(ctx context.Context, selection *model.RepairSelection) error {
//		return s.repairSelectionRepo.Create(ctx, selection)
//	}
//
// CreateCustomerApproval 创建客户审批记录
func (s *outboundTicketService) CreateApproval(ctx context.Context, approval *outbound.OutboundApproval) error {
	return s.outboundApprovalRepo.Create(ctx, approval)
}

func (s *outboundTicketService) GetApprovalById(ctx context.Context, id uint) (*outbound.OutboundApproval, error) {
	return s.outboundApprovalRepo.GetByTicketID(ctx, id)
}

func (s *outboundTicketService) UpdateApproval(ctx context.Context, approval *outbound.OutboundApproval) error {
	return s.outboundApprovalRepo.Update(ctx, approval)
}

//// CreateVerification 创建验证记录
//func (s *outboundTicketService) CreateVerification(ctx context.Context, verification *model.Verification) error {
//	return s.verificationRepo.Create(ctx, verification)
//}

// StartWorkflow 启动工作流
func (s *outboundTicketService) StartWorkflow(ctx context.Context, ticketID, userID uint, userName string) error {
	ticket, err := s.GetOutboundTicketByID(ctx, ticketID)
	if err != nil {
		return fmt.Errorf("获取工单失败: %w", err)
	}

	// 启动工作流
	if s.temporalClient != nil {
		var (
			workflowFunc  interface{}
			workflowInput cmdbCommon.WorkflowInput
			taskQueue     string
		)

		switch ticket.OutboundType {
		case cmdbWorkflow.OutboundTypePart:
			workflowFunc = cmdbWorkflow.PartOutboundWorkflow
			workflowInput = cmdbCommon.PartOutboundWorkflowInput{
				TicketID:       ticket.ID,
				TicketNo:       ticket.TicketNo,
				OperatorName:   userName,
				OperatorID:     userID,
				OutboundReason: ticket.OutboundReason,
			}
			taskQueue = common.PartOutboundTaskQueue

		case cmdbWorkflow.OutboundTypeDevice:
			workflowFunc = cmdbWorkflow.DeviceOutboundWorkflow
			workflowInput = cmdbCommon.DeviceOutboundWorkflowInput{
				TicketID:       ticket.ID,
				TicketNo:       ticket.TicketNo,
				OperatorName:   userName,
				OperatorID:     userID,
				OutboundReason: ticket.OutboundReason,
			}
			taskQueue = common.DeviceOutboundTaskQueue
		}
		options := client.StartWorkflowOptions{
			ID:        fmt.Sprintf("outbound_ticket_%d", ticketID),
			TaskQueue: taskQueue,
		}
		if workflowFunc != nil {
			_, err := s.temporalClient.ExecuteWorkflow(ctx, options, workflowFunc, workflowInput)
			if err != nil {
				return fmt.Errorf("启动工作流失败: %v", err)
			}
		}
	}
	return nil
}

// 获取出库单 Info
func (s *outboundTicketService) GetTicketInfoByID(ctx context.Context, ticketID uint) ([]outbound.OutboundInfo, error) {
	return s.repo.GetInfoByTicketID(ctx, ticketID)
}

// 获取出库单 Detail
func (s *outboundTicketService) GetTicketDetailsByID(ctx context.Context, ticketID uint) ([]outbound.OutboundDetail, error) {
	return s.repo.GetDetailsByTicketID(ctx, ticketID)
}

// GetTicketDetailsListByID 获取出库单详情列表（分页）
func (s *outboundTicketService) GetTicketDetailsListByID(ctx context.Context, ticketID uint, page, pageSize int) ([]outbound.OutboundDetail, int64, error) {
	// 获取全部详情
	allDetails, err := s.repo.GetDetailsByTicketID(ctx, ticketID)
	if err != nil {
		return nil, 0, fmt.Errorf("获取出库单详情失败: %w", err)
	}

	// 计算总数
	total := int64(len(allDetails))

	// 计算分页参数
	offset := (page - 1) * pageSize
	end := offset + pageSize

	// 边界检查
	if offset >= len(allDetails) {
		return []outbound.OutboundDetail{}, total, nil
	}

	if end > len(allDetails) {
		end = len(allDetails)
	}

	// 返回分页后的数据
	return allDetails[offset:end], total, nil
}

func (s *outboundTicketService) UpdateTicketAndHistory(ctx context.Context, ticket *outbound.SpareOutboundTicket, history *outbound.OutboundTicketStatusHistory) error {
	if ticket == nil {
		return fmt.Errorf("出库单不能为空")
	}
	if history == nil {
		return fmt.Errorf("状态历史记录不能为空")
	}

	// 使用事务处理更新
	return s.repo.WithTransaction(ctx, func(txCtx context.Context, repo outboundRep.OutboundTicketRepository) error {
		// 更新出库单
		if err := repo.Update(txCtx, ticket); err != nil {
			s.logger.Error("更新出库单失败",
				zap.Error(err),
				zap.Uint("ticketID", ticket.ID))
			return fmt.Errorf("更新出库单失败: %w", err)
		}

		// 记录状态历史
		if err := repo.CreateStatusHistory(txCtx, history); err != nil {
			s.logger.Error("记录状态历史失败",
				zap.Error(err),
				zap.Uint("ticketID", ticket.ID))
			return fmt.Errorf("记录状态历史失败: %w", err)
		}

		s.logger.Info("成功更新出库单和状态历史",
			zap.Uint("ticketID", ticket.ID),
			zap.String("oldStatus", history.PreviousStatus),
			zap.String("newStatus", history.NewStatus),
			zap.String("operator", history.OperatorName))

		return nil
	})
}

// GetDetailsLen 获取出库数量
func (s *outboundTicketService) GetDetailsLen(ctx context.Context, ticketID uint) (uint, error) {
	return s.repo.GetDetailsLen(ctx, ticketID)
}

// ValidAssetExist 校验要出库的设备是否有相应数量
func (s *outboundTicketService) validAssetExist(ctx context.Context, ticket *outbound.SpareOutboundTicket) error {
	switch ticket.OutboundType {
	case cmdbWorkflow.OutboundTypePart: // 配件
		for i, info := range ticket.Info {
			detail, err := s.inventorySvc.GetInventoryByProductIDandWarehouseID(ctx, info.ProductID, ticket.SourceWarehouseID)
			if err != nil {
				return err
			}
			if detail.AvailableStock < info.Amount {
				return fmt.Errorf("第 %d 行配件库存不足，%v 库存数量:%d，申请数量：%d,请联系管理员", i+1, ticket.SourceLocation, detail.AvailableStock, info.Amount)
			}
		}
		//switch ticket.OutboundReason {
		//case cmdbCommon.OutboundReasonAllocate, cmdbWorkflow.OutboundReasonReturnRepair, cmdbWorkflow.Repair, cmdbCommon.OutboundReasonReplacement: // 调拨和返修出库
		//	for i, info := range ticket.Info {
		//		detail, err := s.inventorySvc.GetInventoryByProductIDandWarehouseID(ctx, info.ProductID, ticket.SourceWarehouseID)
		//		if err != nil {
		//			return err
		//		}
		//		if detail.CurrentStock < info.Amount {
		//			return fmt.Errorf("第 %d 行配件库存不足，%v 库存数量:%d，申请数量：%d,请联系管理员", i+1, ticket.SourceLocation, detail.CurrentStock, info.Amount)
		//		}
		//	}
		//case cmdbCommon.OutboundReasonReplacement: // 改配
		//	for i, info := range ticket.Info {
		//		detail, err := s.inventorySvc.GetInventoryByProductIDandWarehouseID(ctx, info.ProductID, ticket.SourceWarehouseID)
		//		if err != nil {
		//			return err
		//		}
		//		if detail.CurrentStock < info.Amount {
		//			return fmt.Errorf("第 %d 行配件库存不足，%v 库存数量:%d，申请数量：%d,请联系管理员进行调拨", i+1, ticket.DestLocation, detail.CurrentStock, info.Amount)
		//		}
		//	}
		//}
	case cmdbWorkflow.OutboundTypeDevice: // 设备
		for i, info := range ticket.Info {
			detail, err := s.inventorySvc.GetInventoryByProductIDandWarehouseID(ctx, info.ProductID, ticket.SourceWarehouseID)
			if err != nil {
				return err
			}
			if detail.AvailableStock < info.Amount {
				return fmt.Errorf("第 %d 行配件库存不足，%v 库存数量:%d，申请数量：%d,请联系管理员", i+1, ticket.SourceLocation, detail.AvailableStock, info.Amount)
			}
		}
	}
	return nil
}

func (s *outboundTicketService) UpdateOutboundTicketDetailsByImport(ctx context.Context, importForm *outbound.OutboundDetailImport) error {
	// 打开文件
	src, err := importForm.File.Open()
	if err != nil {
		return fmt.Errorf("打开文件失败: %w", err)
	}
	defer func(src multipart.File) {
		err := src.Close()
		if err != nil {
			fmt.Printf("关闭文件失败%v", err)
		}
	}(src)

	// 构建临时文件路径
	timestamp := time.Now().UnixNano()
	filename := fmt.Sprintf("%s_%d.%s", "OutboundDetail", timestamp, filepath.Ext(importForm.File.Filename))

	// 安全地构建文件路径
	basePath := "storage/imports"
	if err := os.MkdirAll(basePath, 0750); err != nil {
		return fmt.Errorf("创建目录失败: %w", err)
	}

	// 验证文件名安全性
	//if !isFileNameSafe(filename) {
	//	return fmt.Errorf("文件名不安全: %s", filename)
	//}
	//
	filePath := filepath.Join(basePath, filename)

	// 确保路径没有超出基础目录
	absPath, err := filepath.Abs(filePath)
	if err != nil {
		return fmt.Errorf("获取绝对路径失败: %w", err)
	}
	absBasePath, err := filepath.Abs(basePath)
	if err != nil {
		return fmt.Errorf("获取基础目录绝对路径失败: %w", err)
	}
	if !strings.HasPrefix(absPath, absBasePath) {
		return fmt.Errorf("文件路径超出允许范围")
	}

	// 使用安全的文件操作
	// #nosec G304 -- 文件路径已经过验证，确保在安全的目录中
	dst, err := os.OpenFile(filePath, os.O_WRONLY|os.O_CREATE|os.O_EXCL, 0600)
	if err != nil {
		return fmt.Errorf("创建文件失败: %w", err)
	}
	defer func(dst *os.File) {
		err := dst.Close()
		if err != nil {
			fmt.Printf("关闭文件失败%v", err)
		}
	}(dst)

	// 将文件保存到本地
	if _, err := io.Copy(dst, src); err != nil {
		return fmt.Errorf("保存文件失败: %w", err)
	}

	// 根据模板类型读取Excel文件
	var details []outbound.OutboundDetail
	switch importForm.TemplateName {
	case exportModel.ReplacePartTemplate:
		details, err = s.ReadReplacePartTemplate(filePath)
	case exportModel.AllocatePartTemplate:
		details, err = s.ReadAllocatePartTemplate(filePath)
	case exportModel.ReturnRepairOutPartTemplate:
		details, err = s.ReadReturnRepairOutPartTemplate(filePath)
		if err != nil {
			return err
		}
	case exportModel.RepairOutPartTemplate:
		details, err = s.ReadRepairOutPartTemplate(filePath)
		if err != nil {
			return err
		}
	case exportModel.SellPartTemplate:
		details, err = s.ReadSellPartTemplate(filePath)
		if err != nil {
			return err
		}
	case exportModel.RackDeviceTemplate:
		details, err = s.ReadRackDeviceTemplate(filePath)
		if err != nil {
			return err
		}
	case exportModel.AllocateDeviceTemplate:
		details, err = s.ReadAllocateDeviceTemplate(filePath)
		if err != nil {
			return err
		}
	default:
		return fmt.Errorf("不支持的模板类型: %s", importForm.TemplateName)
	}

	if err != nil {
		return fmt.Errorf("读取Excel文件失败: %w", err)
	}

	// 调用已有的UpdateOutboundTicketDetails方法更新数据
	return s.UpdateOutboundTicketDetails(ctx, details)
}

// isFileNameSafe 验证文件名是否安全
//
//nolint:unused
func isFileNameSafe(filename string) bool {
	// 文件名中只允许字母、数字、下划线、连字符、点
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9_\-\.]+$`, filename)
	return matched
}

// ReadReplacePartTemplate 读取配件改配模板
func (s *outboundTicketService) ReadReplacePartTemplate(filePath string) ([]outbound.OutboundDetail, error) {
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开Excel文件失败: %w", err)
	}
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println("关闭Excel文件失败:", err)
		}
	}()

	// 读取工作表
	sheetName := exportModel.ReplacePartSheet
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, fmt.Errorf("读取工作表失败: %w", err)
	}

	if len(rows) <= 1 {
		return nil, fmt.Errorf("Excel文件中没有数据")
	}

	var details []outbound.OutboundDetail
	// 跳过表头，从第二行开始读取
	for i, row := range rows {
		if i == 0 {
			continue // 跳过表头
		}

		// 确保行数据至少包含所需的列
		if len(row) < 10 {
			return nil, fmt.Errorf("第%d行数据格式不正确", i+1)
		}

		detailID, err := strconv.ParseUint(row[9], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("第%d行出库详情ID格式错误: %s", i+1, err.Error())
		}
		productID, err := strconv.ParseUint(row[8], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("第%d行规格ID格式错误", i)
		}
		// 构建出库详情对象
		detail := outbound.OutboundDetail{
			ID:          uint(detailID),
			ProductID:   uint(productID),
			ComponentSN: row[0], // 配件SN
			DeviceSN:    row[1], // 服务器SN
		}

		// 添加到详情列表
		details = append(details, detail)
	}

	return details, nil
}

// ReadAllocatePartTemplate 读取配件调拨模板
func (s *outboundTicketService) ReadAllocatePartTemplate(filePath string) ([]outbound.OutboundDetail, error) {
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开Excel文件失败: %w", err)
	}
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println("关闭Excel文件失败:", err)
		}
	}()

	// 读取工作表
	sheetName := exportModel.AllocatePartSheet
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, fmt.Errorf("读取工作表失败: %w", err)
	}

	if len(rows) <= 1 {
		return nil, fmt.Errorf("Excel文件中没有数据")
	}

	var details []outbound.OutboundDetail
	// 跳过表头，从第二行开始读取
	for i, row := range rows {
		if i == 0 {
			continue // 跳过表头
		}

		// 确保行数据至少包含所需的列
		if len(row) < 9 {
			return nil, fmt.Errorf("第%d行数据格式不正确", i+1)
		}

		detailID, err := strconv.ParseUint(row[8], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("第%d行出库详情ID格式错误: %s", i+1, err.Error())
		}
		productID, err := strconv.ParseUint(row[7], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("第%d行规格ID格式错误", i)
		}

		// 构建出库详情对象
		detail := outbound.OutboundDetail{
			ID:          uint(detailID),
			ProductID:   uint(productID),
			ComponentSN: row[0], // 配件SN
		}

		// 添加到详情列表
		details = append(details, detail)
	}
	return details, nil
}

// ReadReturnRepairOutPartTemplate 读取配件返修模板
func (s *outboundTicketService) ReadReturnRepairOutPartTemplate(filePath string) ([]outbound.OutboundDetail, error) {
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开Excel文件失败: %w", err)
	}
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println("关闭Excel文件失败:", err)
		}
	}()

	// 读取工作表
	sheetName := exportModel.ReturnRepairOutPartSheet
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, fmt.Errorf("读取工作表失败: %w", err)
	}

	if len(rows) <= 1 {
		return nil, fmt.Errorf("Excel文件中没有数据")
	}

	var details []outbound.OutboundDetail
	// 跳过表头，从第二行开始读取
	for i, row := range rows {
		if i == 0 {
			continue // 跳过表头
		}

		// 确保行数据至少包含所需的列
		if len(row) < 9 {
			return nil, fmt.Errorf("第%d行数据格式不正确", i+1)
		}

		detailID, err := strconv.ParseUint(row[8], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("第%d行出库详情ID格式错误: %s", i+1, err.Error())
		}
		productID, err := strconv.ParseUint(row[7], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("第%d行规格ID格式错误", i)
		}

		// 构建出库详情对象
		detail := outbound.OutboundDetail{
			ID:          uint(detailID),
			ProductID:   uint(productID),
			ComponentSN: row[0], // 配件SN
		}

		// 添加到详情列表
		details = append(details, detail)
	}
	return details, nil
}

// ReadRepairOutPartTemplate 读取配件维修模板
func (s *outboundTicketService) ReadRepairOutPartTemplate(filePath string) ([]outbound.OutboundDetail, error) {
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开Excel文件失败: %w", err)
	}
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println("关闭Excel文件失败:", err)
		}
	}()

	// 读取工作表
	sheetName := exportModel.RepairOutPartSheet
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, fmt.Errorf("读取工作表失败: %w", err)
	}

	if len(rows) <= 1 {
		return nil, fmt.Errorf("Excel文件中没有数据")
	}

	var details []outbound.OutboundDetail
	// 跳过表头，从第二行开始读取
	for i, row := range rows {
		if i == 0 {
			continue // 跳过表头
		}

		// 确保行数据至少包含所需的列
		if len(row) < 9 {
			return nil, fmt.Errorf("第%d行数据格式不正确", i+1)
		}

		detailID, err := strconv.ParseUint(row[8], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("第%d行出库详情ID格式错误: %s", i+1, err.Error())
		}
		productID, err := strconv.ParseUint(row[7], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("第%d行规格ID格式错误", i)
		}

		// 构建出库详情对象
		detail := outbound.OutboundDetail{
			ID:          uint(detailID),
			ProductID:   uint(productID),
			ComponentSN: row[0], // 配件SN
		}

		// 添加到详情列表
		details = append(details, detail)
	}
	return details, nil
}

// ReadSellPartTemplate 读取配件售卖出库Excel模板
func (s *outboundTicketService) ReadSellPartTemplate(filePath string) ([]outbound.OutboundDetail, error) {
	// 打开Excel文件
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开Excel文件失败: %w", err)
	}
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Printf("关闭Excel文件失败: %v\n", err)
		}
	}()

	// 获取售卖出库工作表中的所有行
	rows, err := f.GetRows(exportModel.SellPartSheet)
	if err != nil {
		return nil, fmt.Errorf("读取%s工作表失败: %w", exportModel.SellPartSheet, err)
	}

	// 如果行数小于2（没有数据，只有表头），则返回空结果
	if len(rows) < 2 {
		return []outbound.OutboundDetail{}, nil
	}

	// 解析每行数据并构建OutboundDetail数组
	var details []outbound.OutboundDetail
	for i := 1; i < len(rows); i++ { // 从第二行开始（跳过表头）
		row := rows[i]
		// 确保行有足够的列
		if len(row) < 9 { // 需要至少9列数据
			continue
		}
		detailID, err := strconv.ParseUint(row[8], 10, 32)
		if err == nil {
			return nil, fmt.Errorf("第%d行详情ID格式错误", i)
		}
		productID, err := strconv.ParseUint(row[7], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("第%d行规格ID格式错误", i)
		}

		// 解析数据，构建OutboundDetail对象
		detail := outbound.OutboundDetail{
			ID:          uint(detailID),
			ProductID:   uint(productID),
			ComponentSN: row[0], // 配件SN列
		}

		// 添加到结果数组
		details = append(details, detail)
	}

	return details, nil
}

// ReadRackDeviceTemplate 读取设备上架导入模板
func (s *outboundTicketService) ReadRackDeviceTemplate(filePath string) ([]outbound.OutboundDetail, error) {
	// 打开Excel文件
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %w", err)
	}
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println("关闭Excel文件失败:", err)
		}
	}()

	// 获取所有行
	rows, err := f.GetRows(exportModel.RackDeviceSheet)
	if err != nil {
		return nil, fmt.Errorf("获取工作表数据失败: %w", err)
	}

	// 检查数据行是否存在
	if len(rows) <= 1 {
		return nil, fmt.Errorf("导入文件无数据")
	}

	// 验证表头
	if !validHeaders(rows[0], exportModel.DeviceInboundHeader) {
		return nil, fmt.Errorf("表头格式错误")
	}

	var details []outbound.OutboundDetail
	// 从第二行开始处理数据（跳过表头）
	for i := 1; i < len(rows); i++ {
		row := rows[i]
		// 跳过空行
		if len(row) == 0 || (len(row) > 1 && row[1] == "") {
			continue
		}

		// 确保行有足够的列
		if len(row) < 10 {
			return nil, fmt.Errorf("第%d行数据不完整", i+1)
		}

		// 检查设备SN是否为空
		deviceSN := strings.TrimSpace(row[1])
		if deviceSN == "" {
			return nil, fmt.Errorf("第%d行设备SN不能为空", i+1)
		}

		// 检查模板ID是否有效
		productIDStr := row[8]
		if productIDStr == "" {
			return nil, fmt.Errorf("第%d行规格ID不能为空", i+1)
		}

		productID, err := strconv.ParseUint(productIDStr, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("第%d行模板ID格式错误: %w", i+1, err)
		}

		// 检查出库详情ID是否有效
		detailIDStr := row[9]
		if detailIDStr == "" {
			return nil, fmt.Errorf("第%d行出库详情ID不能为空", i+1)
		}
		detailID, err := strconv.ParseUint(detailIDStr, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("第%d行出库详情ID格式错误: %w", i+1, err)
		}

		// 构建出库明细
		detail := outbound.OutboundDetail{
			DeviceSN:  row[1], // 设备SN
			ProductID: uint(productID),
			ID:        uint(detailID), // 出库详情ID
		}

		details = append(details, detail)
	}

	if len(details) == 0 {
		return nil, fmt.Errorf("导入文件无有效数据")
	}

	return details, nil
}

// ReadAllocateDeviceTemplate 读取设备调拨导入模板
func (s *outboundTicketService) ReadAllocateDeviceTemplate(filePath string) ([]outbound.OutboundDetail, error) {
	// 打开Excel文件
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %w", err)
	}
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println("关闭Excel文件失败:", err)
		}
	}()

	// 获取所有行
	rows, err := f.GetRows(exportModel.AllocateDeviceSheet)
	if err != nil {
		return nil, fmt.Errorf("获取工作表数据失败: %w", err)
	}

	// 检查数据行是否存在
	if len(rows) <= 1 {
		return nil, fmt.Errorf("导入文件无数据")
	}

	// 验证表头
	if !validHeaders(rows[0], exportModel.RackDeviceHeader) {
		return nil, fmt.Errorf("表头格式错误")
	}

	var details []outbound.OutboundDetail
	// 从第二行开始处理数据（跳过表头）
	for i := 1; i < len(rows); i++ {
		row := rows[i]
		// 跳过空行
		if len(row) == 0 || (len(row) > 1 && row[1] == "") {
			continue
		}

		// 确保行有足够的列
		if len(row) < 10 {
			return nil, fmt.Errorf("第%d行数据不完整", i+1)
		}

		// 检查设备SN是否为空
		deviceSN := strings.TrimSpace(row[1])
		if deviceSN == "" {
			return nil, fmt.Errorf("第%d行设备SN不能为空", i+1)
		}

		// 检查模板ID是否有效
		productIDStr := row[8]
		if productIDStr == "" {
			return nil, fmt.Errorf("第%d行规格ID不能为空", i+1)
		}

		productID, err := strconv.ParseUint(productIDStr, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("第%d行模板ID格式错误: %w", i+1, err)
		}

		// 检查出库详情ID是否有效
		detailIDStr := row[9]
		if detailIDStr == "" {
			return nil, fmt.Errorf("第%d行出库详情ID不能为空", i+1)
		}
		detailID, err := strconv.ParseUint(detailIDStr, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("第%d行出库详情ID格式错误: %w", i+1, err)
		}

		// 构建出库明细
		detail := outbound.OutboundDetail{
			DeviceSN:  row[1], // 设备SN
			ProductID: uint(productID),
			ID:        uint(detailID), // 出库详情ID
		}

		details = append(details, detail)
	}

	if len(details) == 0 {
		return nil, fmt.Errorf("导入文件无有效数据")
	}

	return details, nil
}

// validHeaders 验证表头是否匹配预期
func validHeaders(headers []string, expectedHeaders []string) bool {
	if len(headers) < len(expectedHeaders) {
		return false
	}
	for i, expected := range expectedHeaders {
		if i >= len(headers) || headers[i] != expected {
			return false
		}
	}
	return true
}

func (s *outboundTicketService) GetDetailByID(ctx context.Context, detailID uint) (*outbound.OutboundDetail, error) {
	return s.repo.GetDetailByID(ctx, detailID)
}

// validateOutboundTypeFields 验证不同出库类型的特定字段
func (s *outboundTicketService) validateOutboundTypeFields(ctx context.Context, ticket *outbound.SpareOutboundTicket) error {
	switch ticket.OutboundReason { // 根据出库原因
	case cmdbWorkflow.OutboundReasonSell: // 售卖
		// 销售出库需要订单号和购买者信息
		if ticket.OrderNumber == "" {
			return errors.New("售卖出库必须提供订单号")
		}
		if ticket.BuyerInfo == "" {
			return errors.New("售卖出库必须提供购买者信息")
		}
		if ticket.SourceWarehouseID == 0 {
			return errors.New("出库仓库不能为空")
		}
	case cmdbWorkflow.OutboundReasonRepair: // 维修
		fmt.Println(ticket.Project)
		switch strings.ToLower(ticket.Project) {
		case "cloud11", "cloud23":
			if ticket.DeviceSN == "" {
				return errors.New("Clound11的设备SN不能为空")
			}
		default:
			if ticket.RepairTicketId == 0 {
				return errors.New("维修出库需关联维修单")
			}
		}
		if ticket.SourceWarehouseID == 0 {
			return errors.New("出库位置不能为空")
		}
	//case cmdbWorkflow.OutboundReasonReplace: // 改配
	//	if outbound.DestWarehouseID == 0 {
	//		return errors.New("申请改配的仓库不能为空")
	//	}
	case cmdbWorkflow.OutboundReasonReturnRepair, cmdbWorkflow.OutboundReasonRack, cmdbWorkflow.OutboundReasonReplace: // 返修、上架、改配
		if ticket.SourceWarehouseID == 0 {
			return errors.New("出库位置不能为空")
		}
	case cmdbWorkflow.OutboundReasonAllocate: // 调拨
		if ticket.SourceWarehouseID == 0 {
			return errors.New("原位置不能为空")
		}
		if ticket.DestWarehouseID == 0 {
			return errors.New("目的位置不能为空")
		}
	default:
		return fmt.Errorf("不支持的出库原因：%v", ticket.OutboundReason)
	}
	switch ticket.OutboundType { // 根据出库类型
	case cmdbWorkflow.OutboundTypePart, cmdbWorkflow.OutboundTypeDevice:
		// 验证出库信息项
		if len(ticket.Info) == 0 {
			return errors.New("出库信息项不能为空")
		}

		for i, info := range ticket.Info {
			if info.ProductID == 0 {
				return fmt.Errorf("出库信息项[%d]缺少产品ID", i)
			}
			if info.Amount <= 0 {
				return fmt.Errorf("出库信息项[%d]数量必须大于0", i)
			}
			Product, err := s.productSvc.GetByID(ctx, info.ProductID)
			if err != nil {
				return err
			}
			assetType := cmdbCommon.MapMaterialTypeToAssetType[Product.MaterialType]
			if ticket.OutboundType == cmdbCommon.OutboundTypeDevice {
				if assetType == "" {
					return fmt.Errorf("入库信息第 %d 项: 物料类型不符合规范，设备入库不能选择 %s ", i+1, Product.MaterialType)
				}
			} else {
				if assetType != "" {
					return fmt.Errorf("入库信息第 %d 项: 物料类型不符合规范，配件入库不能选择 %s", i+1, Product.MaterialType)
				}
			}
		}
	default:
		return errors.New("无效的出库类型")
	}

	return nil
}

var trans = map[string]string{
	cmdbCommon.OutboundReasonReplacement:  "改配出库",
	cmdbCommon.OutboundReasonRepair:       "维修出库",
	cmdbCommon.OutboundReasonReturnRepair: "返修出库",
	cmdbCommon.OutboundReasonSell:         "售卖出库",
	cmdbCommon.OutboundReasonAllocate:     "调拨出库",
}

func (s *outboundTicketService) UpdateCMDB(ctx context.Context, outboundType, outboundReason string, ticketID uint) error {
	var (
		componentSns    []string
		deviceSns       []string
		inventoryDetail *inventory.InventoryDetail
		purpose         string
		spares          []asset.AssetSpare
		devices         []asset.Device
		oldAssetStatus  string
	)
	ticket, err := s.GetOutboundTicketByID(ctx, ticketID)
	if err != nil {
		return err
	}
	details, err := s.GetTicketDetailsByID(ctx, ticketID)
	if err != nil {
		return fmt.Errorf("获取出库详情失败: %w", err)
	}
	for _, detail := range details {
		componentSns = append(componentSns, detail.ComponentSN)
		deviceSns = append(deviceSns, detail.DeviceSN)

	}
	infos, err := s.GetTicketInfoByID(ctx, ticketID)
	if err != nil {
		return fmt.Errorf("获取出库信息失败: %w", err)
	}
	spareInfos := make(map[uint]int) // 用于调拨
	switch outboundType {
	case cmdbWorkflow.OutboundTypePart:
		spares, err = s.spareSvc.GetSpareBySNs(ctx, componentSns)
		if err != nil {
			return err
		}
	case cmdbWorkflow.OutboundTypeDevice:
		devices, _, err = s.deviceSvc.ListBySNsV2(ctx, deviceSns, 2)
		if err != nil {
			return err
		}
	}

	ctx = context.WithValue(ctx, constants.ContextKeyUserID, ticket.ReporterID)
	ctx = context.WithValue(ctx, constants.ContextKeyUserInfo, map[string]interface{}{"realName": ticket.ReporterName})

	db, err := s.repo.GetDB()
	if err != nil {
		return err
	}
	// 启动事务操作
	err = db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		ctx = context.WithValue(ctx, constants.ContextKeyTX, tx)
		switch outboundReason {
		case cmdbCommon.OutboundReasonReplacement: // 改配
			for i := range spares {
				spares[i].RelatedAssetSN = details[i].DeviceSN
				spares[i].AssetStatus = constants.AssetStatusInUse
			}
			// 更新库存历史(仓库管理员那边已经更新了，这里只需要更新状态为使用中）
			//for _, info := range infos {
			//	// 目标仓库数量减少
			//	inventoryDetail, err = s.inventorySvc.GetInventoryByProductIDandWarehouseID(ctx, info.ProductID, ticket.SourceWarehouseID)
			//	if err != nil {
			//		return err
			//	}
			//	purpose = fmt.Sprintf("%s：%s，数量：%d", trans[outboundReason], ticket.TicketNo, info.Amount)
			//	err = s.inventorySvc.AllocateStock(ctx, inventoryDetail.ID, info.Amount, purpose)
			//	if err != nil {
			//		return err
			//	}
			//}

		case cmdbCommon.OutboundReasonAllocate: // 调拨
			switch outboundType {
			case cmdbWorkflow.OutboundTypePart:
				for i := range spares {
					spares[i].WarehouseID = ticket.DestWarehouseID
					spares[i].Location = ""
					spares[i].AssetStatus = constants.AssetStatusIdle
					spares[i].SourceType = constants.SourceTypeAllocate
				}
			case cmdbWorkflow.OutboundTypeDevice:
				spareMap, err := s.deviceSvc.GetDevicesSpare(ctx, deviceSns)
				if err != nil {
					return err
				}
				for _, deviceSN := range deviceSns {
					spares = append(spares, spareMap[deviceSN]...)
				}
				for i := range spares {
					spares[i].WarehouseID = ticket.DestWarehouseID
					spares[i].Location = ""
					spares[i].AssetStatus = constants.AssetStatusInUse
					spares[i].SourceType = constants.SourceTypeAllocate
					spareInfos[spares[i].ProductID]++
				}
				// 将服务器状态设置为闲置中
				oldAssetStatus = devices[0].AssetStatus
				for i := range devices {
					devices[i].AssetStatus = constants.AssetStatusIdle
				}
			}

			for _, info := range infos {
				// 原仓库设备库存减少
				inventoryDetail, err = s.inventorySvc.GetInventoryByProductIDandWarehouseID(ctx, info.ProductID, ticket.SourceWarehouseID)
				if err != nil {
					return err
				}
				purpose = fmt.Sprintf("%s：%s , 数量：%d", trans[outboundReason], ticket.TicketNo, info.Amount)
				err = s.inventorySvc.RemoveAllocatedStock(ctx, inventoryDetail.ID, info.Amount, purpose)

				// 目标仓库设备库存增加
				err = db.Model(&inventory.InventoryDetail{}).Where("product_id = ? AND warehouse_id = ?", info.ProductID, ticket.DestWarehouseID).First(&inventoryDetail).Error
				if err != nil {
					return fmt.Errorf("获取库存详情失败: %w", err)
				}
				purpose = fmt.Sprintf("调拨入库：%s , 数量：%d", ticket.TicketNo, info.Amount)
				err = s.inventorySvc.AdjustStock(ctx, inventoryDetail.ID, info.Amount, constants.ChangeTypeInbound, purpose, 0, ticket.ID)
				if err != nil {
					return err
				}
			}
		case cmdbCommon.OutboundReasonReturnRepair: // 返修出库
			for i := range spares {
				spares[i].RelatedAssetSN = ""
				spares[i].RelatedAssetID = 0
				spares[i].Location = ""
				spares[i].AssetStatus = constants.AssetStatusRepairing
				spares[i].HardwareStatus = constants.HardwareStatusFaulty
			}
			// 更新库存历史
			for _, info := range infos {
				// 原仓库库存减少
				inventoryDetail, err = s.inventorySvc.GetInventoryByProductIDandWarehouseID(ctx, info.ProductID, ticket.SourceWarehouseID)
				if err != nil {
					return err
				}
				purpose = fmt.Sprintf("%s：%s , 数量：%d", trans[outboundReason], ticket.TicketNo, info.Amount)
				err = s.inventorySvc.AllocateStock(ctx, inventoryDetail.ID, info.Amount, purpose)
				if err != nil {
					return err
				}
			}
		case cmdbCommon.OutboundReasonRepair: // 维修
			for i := range spares {
				spares[i].RelatedAssetSN = ticket.DeviceSN
				spares[i].AssetStatus = constants.AssetStatusInUse
				spares[i].HardwareStatus = constants.HardwareStatusNormal
			}
			// 更新库存历史
			for _, info := range infos {
				// 原仓库库存减少
				inventoryDetail, err = s.inventorySvc.GetInventoryByProductIDandWarehouseID(ctx, info.ProductID, ticket.SourceWarehouseID)
				if err != nil {
					return err
				}
				purpose = fmt.Sprintf("%s：%s , 数量：%d", trans[outboundReason], ticket.TicketNo, info.Amount)
				err = s.inventorySvc.AllocateStock(ctx, inventoryDetail.ID, info.Amount, purpose)
				if err != nil {
					return err
				}
			}

		case cmdbCommon.OutboundReasonSell: // 售卖
			for i := range spares {
				spares[i].RelatedAssetSN = ""
				spares[i].RelatedAssetID = 0
				spares[i].AssetStatus = constants.AssetStatusSoldOut
			}
			// 更新库存历史
			for _, info := range infos {
				// 原仓库库存减少
				inventoryDetail, err = s.inventorySvc.GetInventoryByProductIDandWarehouseID(ctx, info.ProductID, ticket.SourceWarehouseID)
				if err != nil {
					return err
				}
				purpose = fmt.Sprintf("%s：%s , 数量：%d", trans[outboundReason], ticket.TicketNo, info.Amount)
				err = s.inventorySvc.RemoveAllocatedStock(ctx, inventoryDetail.ID, info.Amount, purpose)
			}
		case cmdbCommon.OutboundReasonRack:
			oldAssetStatus = devices[0].AssetStatus
			for i := range devices {
				devices[i].AssetStatus = constants.AssetStatusOutStock
			}
			// 更新设备状态
			err = s.deviceSvc.UpdateDevices(ctx, devices)
			if err != nil {
				return err
			}
			// 更新库存
			for _, info := range infos {
				purpose = fmt.Sprintf("设备上架出库: %s, 数量: %d", ticket.TicketNo, info.Amount)
				inventoryDetail, err = s.inventorySvc.GetByProductAndWarehouse(ctx, info.ProductID, ticket.SourceWarehouseID)
				if err != nil {
					return err
				}
				err = s.inventorySvc.AllocateStock(ctx, inventoryDetail.ID, info.Amount, purpose)
				if err != nil {
					return err
				}
			}
		default:
			return fmt.Errorf("不支持的出库原因")
		}

		// 操作备件、设备状态
		switch outboundType {
		case cmdbWorkflow.OutboundTypePart:
			// 更新备件状态
			err = s.spareSvc.UpdateSpares(ctx, spares)
			if err != nil {
				return err
			}
		case cmdbWorkflow.OutboundTypeDevice:
			// 创建生命周期记录
			err = s.CreateDeviceLifeCycleLog(ctx, ticket, devices, oldAssetStatus)
			if err != nil {
				return err
			}
			if outboundReason == cmdbCommon.OutboundReasonAllocate {
				var (
					roomID  uint
					project string
				)
				if err = tx.Model(&asset.Warehouse{}).Select("room_id").Where("id = ?", ticket.DestWarehouseID).First(&roomID).Error; err != nil {
					return fmt.Errorf("获取仓库房间ID失败: %w", err)
				}
				err = tx.Model(&locationModel.Room{}).
					Select("regions.name").
					Joins("JOIN data_centers ON rooms.data_center_id = data_centers.id").
					Joins("JOIN azs ON data_centers.az_id = azs.id").
					Joins("JOIN regions ON azs.region_id = regions.id").
					Where("rooms.id = ?", roomID).
					Scan(&project).Error
				if err != nil {
					return fmt.Errorf("获取调拨目的地所属项目失败: %w", err)
				}

				// 调拨服务器
				err = tx.Model(&asset.Resource{}).Joins("left join asset_devices on asset_devices.id=resources.asset_id").Where("sn IN ?", deviceSns).
					Updates(map[string]interface{}{
						"cabinet_id": sql.NullInt64{
							Int64: 0,
							Valid: false, // 关键：告诉 GORM 这是 NULL
						},
						"room_id": roomID,
						"project": project,
					}).Error
				if err != nil {
					return fmt.Errorf("更新设备位置失败: %w", err)
				}

				// 把配件也一起调拨了
				err = s.spareSvc.UpdateSpares(ctx, spares)
				if err != nil {
					return err
				}

				// 服务器的库存在上面已更新

				// 更新备件库存
				for productID, amount := range spareInfos {
					// 从原仓库从删去
					inventoryDetail, err = s.inventorySvc.GetByProductAndWarehouse(ctx, productID, ticket.SourceWarehouseID)
					if err != nil {
						return err
					}
					purpose = fmt.Sprintf("配件更随服务器调拨出库：%s，数量：%d", ticket.TicketNo, amount)
					err = s.inventorySvc.RemoveAllocatedStock(ctx, inventoryDetail.ID, amount, purpose)
					if err != nil {
						return fmt.Errorf("配件随服务器调拨出现错误：%v", err)
					}

					// 放入调拨后的仓库
					inventoryDetail, err = s.inventorySvc.GetByProductAndWarehouse(ctx, productID, ticket.DestWarehouseID)
					if err != nil {
						return err
					}
					purpose = fmt.Sprintf("配件更随服务器调拨入库：%s，数量：%d", ticket.TicketNo, amount)
					err = s.inventorySvc.AdjustStock(ctx, inventoryDetail.ID, amount, constants.ChangeTypeInbound, purpose, 0, ticket.ID)
					if err != nil {
						return err
					}
				}

			} else {
				err = s.deviceSvc.UpdateDevices(ctx, devices)
				if err != nil {
					return err
				}
			}

		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

// CreateDeviceLifeCycleLog 创建生命周期记录
func (s *outboundTicketService) CreateDeviceLifeCycleLog(ctx context.Context, ticket *outbound.SpareOutboundTicket, devices []asset.Device, oldAssetStatus string) error {
	return s.repo.CreateDeviceLifeCycleLog(ctx, ticket, devices, oldAssetStatus)
}

// TransitionToMiddleStatus 转换到中间状态
func (s *outboundTicketService) TransitionToMiddleStatus(ctx context.Context, outboundType, outboundReason, MiddleStatus string, TicketID uint) error {
	var (
		componentSns    []string
		deviceSns       []string
		inventoryDetail inventory.InventoryDetail
		spares          []asset.AssetSpare
		devices         []asset.Device
	)
	ticket, err := s.GetOutboundTicketByID(ctx, TicketID)
	if err != nil {
		return err
	}
	details, err := s.GetTicketDetailsByID(ctx, TicketID)
	if err != nil {
		return fmt.Errorf("获取出库详情失败: %w", err)
	}
	for _, detail := range details {
		componentSns = append(componentSns, detail.ComponentSN)
		deviceSns = append(deviceSns, detail.DeviceSN)
	}
	infos, err := s.GetTicketInfoByID(ctx, TicketID)
	if err != nil {
		return fmt.Errorf("获取出库信息失败: %w", err)
	}
	switch outboundType {
	case cmdbWorkflow.OutboundTypePart:
		spares, err = s.spareSvc.GetSpareBySNs(ctx, componentSns)
		if err != nil {
			return fmt.Errorf("获取备件信息失败: %w", err)
		}
		for i := range spares {
			spares[i].AssetStatus = MiddleStatus
		}
	case cmdbWorkflow.OutboundTypeDevice:
		devices, _, err = s.deviceSvc.ListBySNsV2(ctx, deviceSns, 1)
		if err != nil {
			return fmt.Errorf("获取设备信息失败")
		}
		for i := range devices {
			devices[i].AssetStatus = MiddleStatus
		}
	}

	db, err := s.repo.GetDB()
	if err != nil {
		return err
	}
	err = db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		ctx = context.WithValue(ctx, constants.ContextKeyUserID, ticket.ReporterID)
		ctx = context.WithValue(ctx, constants.ContextKeyUserInfo, map[string]interface{}{"realName": ticket.ReporterName})
		ctx = context.WithValue(ctx, constants.ContextKeyTX, tx)
		switch outboundReason {
		case constants.ChangeReasonAllocate:
			for _, info := range infos {
				err = db.Model(&inventory.InventoryDetail{}).Where("product_id = ? AND warehouse_id = ?", info.ProductID, ticket.SourceWarehouseID).First(&inventoryDetail).Error
				if err != nil {
					return fmt.Errorf("获取库存详情失败: %w", err)
				}
				purpose := fmt.Sprintf("%s出库：%s，出库数量：%d", trans[outboundReason], ticket.TicketNo, info.Amount)
				// 可用库存减少，已分配库存增加
				err = s.inventorySvc.AllocateStock(ctx, inventoryDetail.ID, info.Amount, purpose)
				if err != nil {
					return err
				}
			}
		case constants.ChangeReasonReplace:
			for _, info := range infos {
				err = db.Model(&inventory.InventoryDetail{}).Where("product_id = ? AND warehouse_id = ?", info.ProductID, ticket.SourceWarehouseID).First(&inventoryDetail).Error
				if err != nil {
					return fmt.Errorf("获取库存详情失败: %w", err)
				}
				purpose := fmt.Sprintf("%s：%s，出库数量：%d，可用库存减少，已分配库存增加", trans[outboundReason], ticket.TicketNo, info.Amount)
				// 可用库存减少，已分配库存增加
				err = s.inventorySvc.AllocateStock(ctx, inventoryDetail.ID, info.Amount, purpose)
				if err != nil {
					return err
				}
			}
		}
		switch outboundType {
		case cmdbWorkflow.OutboundTypePart:
			// 更新备件状态
			err = s.spareSvc.UpdateSpares(ctx, spares)
			if err != nil {
				return err
			}
		case cmdbWorkflow.OutboundTypeDevice:
			// 更新设备状态
			err = s.deviceSvc.UpdateDevices(ctx, devices)
			if err != nil {
				return nil
			}
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

// RevertStatus 从中间状态回退
func (s *outboundTicketService) RevertStatus(ctx context.Context, outboundType, outboundReason, Status string, TicketID uint) error {
	var (
		componentSns    []string
		deviceSns       []string
		inventoryDetail inventory.InventoryDetail
		spares          []asset.AssetSpare
		devices         []asset.Device
	)
	ticket, err := s.GetOutboundTicketByID(ctx, TicketID)
	if err != nil {
		return err
	}
	details, err := s.GetTicketDetailsByID(ctx, TicketID)
	if err != nil {
		return fmt.Errorf("获取出库详情失败: %w", err)
	}
	for _, detail := range details {
		componentSns = append(componentSns, detail.ComponentSN)
		deviceSns = append(deviceSns, detail.DeviceSN)
	}
	infos, err := s.GetTicketInfoByID(ctx, TicketID)
	if err != nil {
		return fmt.Errorf("获取出库信息失败: %w", err)
	}
	switch outboundType {
	case cmdbWorkflow.OutboundTypePart:
		spares, err = s.spareSvc.GetSpareBySNs(ctx, componentSns)
		if err != nil {
			return fmt.Errorf("获取备件信息失败: %w", err)
		}
		for i := range spares {
			spares[i].AssetStatus = Status
		}
	case cmdbWorkflow.OutboundTypeDevice:
		devices, _, err = s.deviceSvc.ListBySNsV2(ctx, deviceSns, 1)
		if err != nil {
			return fmt.Errorf("获取设备信息失败")
		}
		for i := range devices {
			devices[i].AssetStatus = Status
		}
	}

	db, err := s.repo.GetDB()
	if err != nil {
		return err
	}

	// 更新库存历史
	err = db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		ctx = context.WithValue(ctx, constants.ContextKeyUserID, ticket.ReporterID)
		ctx = context.WithValue(ctx, constants.ContextKeyUserInfo, map[string]interface{}{"realName": ticket.ReporterName})
		ctx = context.WithValue(ctx, constants.ContextKeyTX, tx)
		switch outboundReason {
		case constants.ChangeReasonAllocate, constants.ChangeReasonSell, constants.ChangeReasonReplace:
			for _, info := range infos {
				err = db.Model(&inventory.InventoryDetail{}).Where("product_id = ? AND warehouse_id = ?", info.ProductID, ticket.SourceWarehouseID).First(&inventoryDetail).Error
				if err != nil {
					return fmt.Errorf("获取库存详情失败: %w", err)
				}
				changeReason := fmt.Sprintf("%s：%s，回退库存数量：%d，已分配库存减少，可用库存增加", trans[outboundReason], ticket.TicketNo, info.Amount)
				ctx = context.WithValue(ctx, constants.ContextKeyChangeReason, changeReason)
				// 原仓库可用的数量增加
				err = s.inventorySvc.ReleaseAllocatedStock(ctx, inventoryDetail.ID, info.Amount)
				if err != nil {
					return err
				}
			}

		}
		switch outboundType {
		case cmdbWorkflow.OutboundTypePart:
			// 更新备件状态
			err = s.spareSvc.UpdateSpares(ctx, spares)
			if err != nil {
				return err
			}
		case cmdbWorkflow.OutboundTypeDevice:
			// 更新设备状态
			err = s.deviceSvc.UpdateDevices(ctx, devices)
			if err != nil {
				return nil
			}
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}
