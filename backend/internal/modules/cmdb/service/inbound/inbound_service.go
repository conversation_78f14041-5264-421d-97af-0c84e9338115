package inbound

import (
	"backend/internal/common/constants"
	assetModel "backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/cmdb/model/inbound"
	inboundRepo "backend/internal/modules/cmdb/repository/inbound"
	"backend/internal/modules/cmdb/repository/inventory"
	inventorySvc "backend/internal/modules/cmdb/service/inventory"
	exportModel "backend/internal/modules/export/model"
	purchaseSvc "backend/internal/modules/purchase_old/service"
	serverService "backend/internal/modules/server/service"
	"backend/internal/modules/ticket/common"
	ticketModel "backend/internal/modules/ticket/model"
	"context"
	"crypto/rand"
	"errors"
	"fmt"
	"io"
	"math/big"
	"mime/multipart"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"
)

type InboundService interface {

	// 创建入库单（旧件入库）
	CreatePartInbound(ctx context.Context, req inbound.PartInbound) (string, uint, error)
	UpdatePartInbound(ctx context.Context, id uint, req inbound.PartInbound) error
	DeletePartInbound(ctx context.Context, id uint) error
	GetPartInboundByID(ctx context.Context, id uint) (*inbound.PartInbound, error)

	/* 新购入库 */
	CreateNewInbound(ctx context.Context, req *inbound.NewInbound) error
	GetNewInboundByID(ctx context.Context, id uint) (*inbound.NewInbound, error)
	GetNewInboundInfoByID(ctx context.Context, id uint) (*[]inbound.NewInboundDetail, error)
	GetNewInboundSubmitterList(ctx context.Context) (map[string]uint, error)
	CreateNewInboundByInput(ctx context.Context, InboundList *inbound.InboundList, NewInbound *inbound.NewInbound) error
	ImportToCMDB(ctx context.Context, newInbound *inbound.NewInbound) error

	// 创建新购入库单 及 入库大致信息
	CreateNewInboundAndInfo(ctx context.Context, inboundList *inbound.InboundList, newInbound *inbound.NewInbound, newInboundInfo []inbound.NewInboundInfo) error

	// 初始化新购入库详情
	InitNewInboundDetails(ctx context.Context, newInboundInfo []inbound.NewInboundInfo) error
	// 更新入库单详情
	UpdateNewInboundDetailsByInput(ctx context.Context, dto []inbound.NewInboundDetail) error
	UpdateNewInboundDetail(ctx context.Context, details []inbound.NewInboundDetail) error
	ValidNewPartInboundSN(ctx context.Context, SNs []string) error
	UpdateNewInboundWarehouse(ctx context.Context, details []inbound.NewInboundDetail) error

	// generateTicketNo 生成工单号
	GenerateTicketNoByInboundType(inboundType, inboundReason string) (string, error)

	// GetNewInboundByNo 根据入库单号获取新购入库单信息
	GetNewInboundByNo(ctx context.Context, inboundNo string) (*inbound.NewInbound, error)

	/*
		拆机配件入库
	*/
	CreateDismantledInboundByInput(ctx context.Context, DismantledInbounds inbound.DismantledInbound, InboundTitle string) error
	CreateDismantledInboundByInputV1(ctx context.Context, DismantledInbounds inbound.DismantledInbound, InboundTitle, Project string) (string, error)
	checkMainDeviceSNsExist(ctx context.Context, sns []string) (map[string]bool, error)

	/*
		返修配件入库
	*/
	CreateRepairInboundByInput(ctx context.Context, repairInbounds *inbound.RepairInbound, inboundTitle, project string) (string, error)
	/*
		公共接口
	*/
	// GetByNo 根据工单号获取入库信息
	GetByNo(ctx context.Context, inboundNo string) (inbound.InboundInterface, ticketModel.InboundTicketInterface, error)

	// List 获取入库单列表
	List(ctx context.Context, dto *inbound.ListDTO) (int64, []inbound.InboundList, error)

	// ImportNewInbound 通过Excel文件导入新入库单
	ImportNewInbound(ctx context.Context, file *multipart.FileHeader) ([]inbound.NewInboundInfo, error)
	UpdateNewInboundDetailsByImport(ctx context.Context, ImportForm *inbound.NewPartDetailImport) error

	// ImportDismantledInbound 通过Excel文件导入拆机入库单
	ImportDismantledInbound(ctx context.Context, file *multipart.FileHeader) (*inbound.DismantledInbound, error)

	// ImportRepairInbound 通过Excel文件导入返修入库单
	ImportRepairInbound(ctx context.Context, file *multipart.FileHeader) (*inbound.RepairInbound, error)

	/* 整机入库相关方法 */
	UpdateDeviceInboundDetailsByInput(ctx context.Context, details []inbound.DeviceInboundDetail) error
	UpdateDeviceInbound(ctx context.Context, details []inbound.DeviceInboundDetail) error
	ValidDeviceInboundSN(ctx context.Context, SNs []string) error
}

type inboundService struct {
	InboundRepo inboundRepo.Repository
	//InboundTicketSvc service.InboundTicketService
	purchaseSvc   purchaseSvc.PurchaseService
	inventorySvc  inventorySvc.InventoryService
	inventoryRepo inventory.InventoryRepository
	serverSvc     serverService.ServerService
}

// InitInboundService 创建入库服务实例
func InitInboundService(inboundRepo inboundRepo.Repository, purchaseSvc purchaseSvc.PurchaseService, inventorySvc inventorySvc.InventoryService, inventoryRepo inventory.InventoryRepository, serverSvc serverService.ServerService) InboundService {
	return &inboundService{
		InboundRepo:   inboundRepo,
		purchaseSvc:   purchaseSvc,
		inventorySvc:  inventorySvc,
		inventoryRepo: inventoryRepo,
		serverSvc:     serverSvc,
	}
}

// generateTicketNoByInboundType 根据入库类型和入库原因生成工单号
func (i *inboundService) GenerateTicketNoByInboundType(inboundType, inboundReason string) (string, error) {
	var inboundNo string
	now := time.Now()
	millisPart := now.Format("150405.000")
	// 移除小数点，保留毫秒部分
	millisStr := millisPart[:6] + millisPart[7:]

	// 生成安全的随机数
	getSecureRandom := func() (int, error) {
		max := big.NewInt(100)
		n, err := rand.Int(rand.Reader, max)
		if err != nil {
			return 0, err
		}
		return int(n.Int64()), nil
	}

	// 生成工单号
	generateNo := func(prefix string) (string, error) {
		randNum, err := getSecureRandom()
		if err != nil {
			return "", err
		}
		return fmt.Sprintf("%s%s%s%d", prefix, now.Format("20060102"), millisStr, randNum), nil
	}

	var err error
	switch inboundType {
	case inbound.TypePartInbound: // 配件入库
		switch inboundReason { // 新购
		case inbound.SourceTypeNewPurchase:
			inboundNo, err = generateNo("NPIT")
		case inbound.SourceTypeRepaired:
			inboundNo, err = generateNo("RPIT")
		case inbound.SourceTypeDismantled:
			inboundNo, err = generateNo("DPIT")
		}
	case inbound.TypeDeviceInbound: //整机入库
		switch inboundReason {
		case inbound.TypeServerInbound: // 服务器
			inboundNo, err = generateNo("SVIT")
		case inbound.TypeGPUServerInbound: // GPU服务器
			inboundNo, err = generateNo("GSIT")
		case inbound.TypeSwitchInbound: //交换机
			inboundNo, err = generateNo("SWIT")
		default:
			return "", fmt.Errorf("不支持的入库原因：%s", inboundReason)
		}
		/* 废弃 */
	case inbound.TypeServerInbound: // 服务器入库
	case inbound.TypeSwitchInbound: // 交换机入库
	case inbound.TypeNewPartInbound:
		inboundNo, err = generateNo("NPIT")
	case inbound.TypeRepairedPartInbound:
		inboundNo, err = generateNo("RPIT")
	case inbound.TypeDismantledPartInbound:
		inboundNo, err = generateNo("DPIT")
	default:
		return "", errors.New("生成工单编号失败，请检查传入的工单类型及原因")
	}

	if err != nil {
		return "", fmt.Errorf("生成工单号失败: %w", err)
	}

	return inboundNo, nil
}

// generateTicketNo 生成工单号
func (i *inboundService) generateTicketNo() string {
	// 使用时间戳和随机数生成工单号
	now := time.Now()
	return fmt.Sprintf("PT%s%d", now.Format("20060102"), now.UnixNano()%10000)
}

// 获取我的入库维修工单
//func (i inboundService) List(ctx context.Context, userId uint, req inbound.GetListQuery) ([]inbound.PartInboundTicket, int64, error) {
//	return i.InboundRepo.List(ctx, userId, req)
//}

// 创建入库单（旧件入库）
func (i inboundService) CreatePartInbound(ctx context.Context, req inbound.PartInbound) (string, uint, error) {
	req.InboundNo = i.generateTicketNo()
	id, err := i.InboundRepo.CreatePartInbound(ctx, &req)
	if err != nil {
		return "", 0, err
	}
	return req.InboundNo, id, err
}

// 更新入库单
func (i *inboundService) UpdatePartInbound(ctx context.Context, id uint, req inbound.PartInbound) error {
	return i.InboundRepo.UpdatePartInbound(ctx, id, &req)
}

// 删除入库单
func (i *inboundService) DeletePartInbound(ctx context.Context, id uint) error {
	return i.InboundRepo.DeletePartInbound(ctx, id)
}

// 获取入库单详情
func (i *inboundService) GetPartInboundByID(ctx context.Context, id uint) (*inbound.PartInbound, error) {
	return i.InboundRepo.GetPartInboundByID(ctx, id)
}

// 创建入库工单
//func (i *inboundService) CreatTicket(ctx context.Context, req inbound.PartInboundTicket) error {
//	// 启用工作流日志
//	logger := activity.GetLogger(ctx)
//	logger.Info("开始入库工单的工作流")
//
//	return i.InboundRepo.CreateTicket(ctx, req)
//}

// 更新入库工单
//func (i *inboundService) UpdateTicket(ctx context.Context, id uint, req inbound.PartInboundTicket) error {
//	return i.InboundRepo.UpdateTicket(ctx, id, req)
//}

// 删除入库工单
//func (i *inboundService) DeleteTicket(ctx context.Context, id uint) error {
//	return i.InboundRepo.DeleteTicket(ctx, id)
//}

// 获取工单详情
//func (i *inboundService) GetTicketByID(ctx context.Context, id uint) (*inbound.PartInboundTicket, error) {
//	return i.InboundRepo.GetTicketByID(ctx, id)
//}

// CreateNewInbound 创建新购入库单
// 参数:
//   - ctx: 上下文
//   - req: 新购入库单请求参数
//
// 返回:
//   - error: 错误信息
func (i *inboundService) CreateNewInbound(ctx context.Context, req *inbound.NewInbound) error {
	// 创建新购入库单
	if err := i.InboundRepo.CreateNewInbound(ctx, req); err != nil {
		return fmt.Errorf("创建新购入库单失败: %w", err)
	}

	return nil
}

func (i *inboundService) GetNewInboundByID(ctx context.Context, id uint) (*inbound.NewInbound, error) {
	return i.InboundRepo.GetNewInboundByID(ctx, id)
}

func (i *inboundService) GetNewInboundInfoByID(ctx context.Context, id uint) (*[]inbound.NewInboundDetail, error) {
	return i.InboundRepo.GetNewInboundInfoByID(ctx, id)
}

func (i *inboundService) GetNewInboundSubmitterList(ctx context.Context) (map[string]uint, error) {
	list, err := i.InboundRepo.GetNewInboundSubmitterList(ctx)
	if err != nil {
		return nil, err
	}
	// 构造 map
	result := make(map[string]uint)
	for _, item := range list {
		result[item.Name] = item.ID
	}
	return result, nil
}

// CreateNewInboundAndInfo 创建新购入库单、入库详情单、入库粗略单
func (i *inboundService) CreateNewInboundAndInfo(ctx context.Context, inboundList *inbound.InboundList, newInbound *inbound.NewInbound, newInboundInfos []inbound.NewInboundInfo) error {
	inboundNo, err := i.GenerateTicketNoByInboundType(inboundList.InboundType, inboundList.InboundReason)
	if err != nil {
		return err
	}
	// 更新入库列表
	inboundList.InboundNo = inboundNo
	// 更新入库单
	newInbound.InboundNo = inboundNo

	return i.InboundRepo.CreateNewInboundWithInfo(ctx, inboundList, newInbound, newInboundInfos)
}

// CreateNewInboundByInput 创建入库单列表、新购入库单、入库工单、入库历史、启动工作流
// TODO 后续考虑把创建入库单、入库历史、启动工作流分离出去
func (i *inboundService) CreateNewInboundByInput(ctx context.Context, InboundList *inbound.InboundList, newInbound *inbound.NewInbound) error {
	if newInbound.NewInfo == nil {
		return fmt.Errorf("请携带此次要入库的配件信息")
	}
	order, err := i.purchaseSvc.GetPurchaseOrderByPurchaseOrderNo(ctx, newInbound.PurchaseOrderNo)
	if err != nil {
		return err
	}

	// 更新入库单
	newInbound.PurchaseOrderID = order.ID

	// 获取粗略的信息
	newInboundInfo := newInbound.NewInfo

	// 创建入库单及入库配件信息
	err = i.CreateNewInboundAndInfo(context.Background(), InboundList, newInbound, newInboundInfo)
	if err != nil {
		return fmt.Errorf("创建入库单及入库配件信息失败：%w", err)
	}

	return nil
}

// ImportToCMDB 将数据导入 CMDB 并更新库存历史及备件表
func (i *inboundService) ImportToCMDB(ctx context.Context, newInbound *inbound.NewInbound) error {
	var (
		//inventoryStockHistories []inventoryModel.StockHistory
		assetSpares []assetModel.AssetSpare
	)
	// 按 ProductID+WarehouseID 分组统计每个组合的数量
	groupedDetails := make(map[string][]inbound.NewInboundDetail)
	for _, detail := range newInbound.NewDetails {
		key := fmt.Sprintf("%d-%d", detail.ProductID, detail.WarehouseID)
		groupedDetails[key] = append(groupedDetails[key], detail)
	}

	// 处理每个分组
	for key, details := range groupedDetails {
		parts := strings.Split(key, "-")
		productID, err := strconv.Atoi(parts[0])
		if err != nil {
			return fmt.Errorf("解析产品ID失败: %w", err)
		}
		warehouseID, err := strconv.Atoi(parts[1])
		if err != nil {
			return fmt.Errorf("解析仓库ID失败: %w", err)
		}

		// 查询当前库存信息
		if productID < 0 {
			return fmt.Errorf("产品ID不能为负数: %d", productID)
		}
		if warehouseID < 0 {
			return fmt.Errorf("仓库ID不能为负数: %d", warehouseID)
		}

		// 仅用于解决新建规格时导入失败的Bug 2025年6月20日
		_, err = i.inventoryRepo.GetLatestInventoryDetail(ctx, uint(productID), uint(warehouseID))
		if err != nil {
			return fmt.Errorf("获取库存信息失败: %w", err)
		}

		// 计算当前分组的数量
		quantity := len(details)

		//// 创建相关的库存历史
		//inventoryStockHistory := inventoryModel.StockHistory{
		//	DetailID:    inventoryDetail.ID,
		//	ProductID:   uint(productID),
		//	InboundID:   details[0].NewInboundID,
		//	WarehouseID: uint(warehouseID),
		//	OperatorID:  newInbound.CreateID,
		//	Operator:    newInbound.CreateBy,
		//	// 记录变化数量
		//	OldQuantity:  inventoryDetail.CurrentStock,
		//	NewQuantity:  inventoryDetail.CurrentStock + quantity,
		//	ChangeType:   inbound.SourceTypeNewPurchase,
		//	ChangeAmount: quantity,
		//	ChangeTime:   time.Now(),
		//}
		//
		//inventoryStockHistories = append(inventoryStockHistories, inventoryStockHistory)
		err = i.updateInventory(ctx, newInbound.ID, uint(productID), uint(warehouseID), quantity, constants.ChangeReasonNewPurchase)
		if err != nil {
			return err
		}

		// 创建备件信息
		for _, detail := range details {
			// 备件
			assetSpare := assetModel.AssetSpare{
				SN:             detail.SN,
				ProductID:      uint(productID),
				SourceType:     inbound.SourceTypeNewPurchase,
				WarehouseID:    uint(warehouseID),
				PurchaseDate:   detail.PurchaseDate,
				AssetStatus:    inbound.AssetStatusIdle,
				HardwareStatus: inbound.HardwareStatusNormal,
				Location:       detail.Location,
				WarrantyExpire: detail.WarrantyExpire,
			}
			assetSpares = append(assetSpares, assetSpare)
		}
	}

	//err := i.InboundRepo.ImportToCMDB(ctx, assetSpares, inventoryStockHistories)
	err := i.InboundRepo.ImportToCMDB(ctx, assetSpares)
	if err != nil {
		return err
	}
	return nil
}

// GetNewInboundByNo 根据入库单号获取新购入库单信息
func (i *inboundService) GetNewInboundByNo(ctx context.Context, inboundNo string) (*inbound.NewInbound, error) {
	newInbound, _, err := i.InboundRepo.GetNewInboundByNo(ctx, inboundNo)
	if err != nil {
		return nil, err
	}
	return newInbound, nil
}

// GetByNo 根据工单号获取入库信息、工单信息
func (i *inboundService) GetByNo(ctx context.Context, inboundNo string) (inbound.InboundInterface, ticketModel.InboundTicketInterface, error) {
	// 查找匹配的前缀
	var matchedPrefix string
	for prefix := range inbound.InboundTypePrefixes {
		if strings.HasPrefix(inboundNo, prefix) {
			matchedPrefix = prefix
			break
		}
	}

	if matchedPrefix == "" {
		return nil, nil, fmt.Errorf("不支持的工单类型: %s", inboundNo)
	}

	// 根据匹配的前缀执行对应的处理逻辑
	switch matchedPrefix {
	case "NPIT":
		return i.InboundRepo.GetNewInboundByNo(ctx, inboundNo)
	case "RPIT":
		return i.InboundRepo.GetRepairedInboundByNo(ctx, inboundNo)
	case "DPIT":
		//dismantleds, ticket, err := i.InboundRepo.GetDismantledInboundByNo(ctx, inboundNo)
		//adapter := inbound.InboundAdapter{
		//	DismantledInbounds: dismantleds,
		//}
		return i.InboundRepo.GetDismantledInboundByNo(ctx, inboundNo)
	case "GSIT", "SVIT", "SWIT":
		return i.InboundRepo.GetDeviceInboundByNo(ctx, inboundNo)
	default:
		return nil, nil, fmt.Errorf("未知的工单前缀: %s", matchedPrefix)
	}
}

// InitNewInboundDetails 初始化入库详情
func (i *inboundService) InitNewInboundDetails(ctx context.Context, newInboundInfo []inbound.NewInboundInfo) error {
	var newInboundDetails []inbound.NewInboundDetail
	for _, info := range newInboundInfo {
		for i := uint(0); i < info.Amount; i++ {
			detail := inbound.NewInboundDetail{
				ProductID:    info.ProductID,
				NewInboundID: info.NewInboundID,
			}
			newInboundDetails = append(newInboundDetails, detail)
		}
	}
	err := i.InboundRepo.InitNewInboundDetails(ctx, newInboundDetails)
	if err != nil {
		return err
	}
	return nil
}

// NewInboundDetailDTO 新购入库详情 DTO
type NewInboundDetailDTO struct {
	NewInboundDetails []inbound.NewInboundDetail `json:"new_inbound_details"`
}

// UpdateNewInboundDetailDTO 用于更新入库详情 SN
type UpdateNewInboundDetailDTO struct {
	NewInboundDetailID uint   `json:"new_inbound_detail_id" binding:"required"`
	SN                 string `json:"sn" binding:"required"`
	WarehouseID        uint   `json:"warehouse_id" binding:"required"`
}

func (i *inboundService) UpdateNewInboundDetailsByInput(ctx context.Context, dto []inbound.NewInboundDetail) error {
	return i.UpdateNewInboundDetail(ctx, dto)
}

// UpdateNewInboundDetailsByImport 通过Excel文件更新新购入库单详情
func (i *inboundService) UpdateNewInboundDetailsByImport(ctx context.Context, ImportForm *inbound.NewPartDetailImport) error {
	// 打开文件
	src, err := ImportForm.File.Open()
	if err != nil {
		return fmt.Errorf("打开文件失败: %w", err)
	}
	defer func(src multipart.File) {
		err := src.Close()
		if err != nil {
			fmt.Printf("关闭文件失败%v", err)
		}
	}(src)

	// 构建临时文件路径
	timestamp := time.Now().UnixNano()
	filename := fmt.Sprintf("%s_%d.%s", "NewPartInboundDetail", timestamp, filepath.Ext(ImportForm.File.Filename))

	// 安全地构建文件路径
	basePath := "storage/imports"
	if err := os.MkdirAll(basePath, 0750); err != nil {
		return fmt.Errorf("创建目录失败: %w", err)
	}

	// 验证文件名安全性
	//if !isFileNameSafe(filename) {
	//	return fmt.Errorf("文件名不安全: %s", filename)
	//}

	filePath := filepath.Join(basePath, filename)

	// 确保路径没有超出基础目录
	absPath, err := filepath.Abs(filePath)
	if err != nil {
		return fmt.Errorf("获取绝对路径失败: %w", err)
	}
	absBasePath, err := filepath.Abs(basePath)
	if err != nil {
		return fmt.Errorf("获取基础目录绝对路径失败: %w", err)
	}
	if !strings.HasPrefix(absPath, absBasePath) {
		return fmt.Errorf("文件路径超出允许范围")
	}

	// 使用安全的文件操作
	// #nosec G304 -- 文件路径已经过验证，确保在安全的目录中
	dst, err := os.OpenFile(filePath, os.O_WRONLY|os.O_CREATE|os.O_EXCL, 0600)
	if err != nil {
		return fmt.Errorf("创建文件失败: %w", err)
	}
	defer func(dst *os.File) {
		err := dst.Close()
		if err != nil {
			fmt.Printf("关闭文件失败%v", err)
		}
	}(dst)

	// 将文件保存到本地
	if _, err := io.Copy(dst, src); err != nil {
		return fmt.Errorf("保存文件失败: %w", err)
	}

	// 读取文件
	details, err := i.ReadNewPartInboundDetail(filePath)
	if err != nil {
		return err
	}
	return i.UpdateNewInboundDetail(ctx, details)
}

// isFileNameSafe 检查文件名是否安全
//
//nolint:unused
func isFileNameSafe(fileName string) bool {
	// 检查文件名是否包含可能导致路径遍历的模式
	if strings.Contains(fileName, "..") || strings.Contains(fileName, "/") || strings.Contains(fileName, "\\") {
		return false
	}
	// 仅允许字母、数字、下划线、连字符和点
	safePattern := regexp.MustCompile(`^[a-zA-Z0-9_\-\.]+$`)
	return safePattern.MatchString(fileName)
}

// 更新用户详情
func (i *inboundService) UpdateNewInboundDetail(ctx context.Context, details []inbound.NewInboundDetail) error {
	// 验证新购配件入库SN
	var Sns []string
	for index, detail := range details {
		if detail.SN == "" {
			return fmt.Errorf("第 %d 行的SN为空", index+1)
		}
		Sns = append(Sns, detail.SN)
	}
	if err := i.ValidNewPartInboundSN(ctx, Sns); err != nil {
		return err
	}

	// 更新新购配件入库信息
	err := i.InboundRepo.UpdateNewInboundDetail(ctx, details)
	if err != nil {
		return err
	}
	return nil
}

// 更新用户详情
func (i *inboundService) UpdateNewInboundWarehouse(ctx context.Context, details []inbound.NewInboundDetail) error {
	// 更新新购配件入库信息
	err := i.InboundRepo.UpdateNewInboundDetail(ctx, details)
	if err != nil {
		return err
	}
	return nil
}

// List 获取入库单列表
func (i *inboundService) List(ctx context.Context, dto *inbound.ListDTO) (int64, []inbound.InboundList, error) {
	return i.InboundRepo.List(ctx, dto)
}

// CreateDismantledInbound 创建拆机入库单+入库列表
func (i *inboundService) CreateDismantledInboundByInput(ctx context.Context, DismantledInbounds inbound.DismantledInbound, InboundTitle string) error {
	// 收集所有需的主设备SN
	mainSNs := make([]string, 0, len(DismantledInbounds.Details))

	// 用于记录已检查过的SN，避免重复查询
	checkedMainSNs := make(map[string]struct{})

	// 提取所有需要验证的SN（去重）
	for _, DisInbound := range DismantledInbounds.Details {
		// 验证mainSN
		if _, exists := checkedMainSNs[DisInbound.MainDeviceSN]; !exists {
			mainSNs = append(mainSNs, DisInbound.MainDeviceSN)
			checkedMainSNs[DisInbound.MainDeviceSN] = struct{}{}
		}
	}
	// 批量验证主设备SN
	if len(mainSNs) > 0 {
		exists, err := i.checkMainDeviceSNsExist(ctx, mainSNs)
		if err != nil {
			return fmt.Errorf("验证主设备SN失败: %w", err)
		}

		for _, sn := range mainSNs {
			if !exists[sn] {
				return fmt.Errorf("主设备SN不存在: %s", sn)
			}
		}
	}

	// 获取product.id

	// 更新CMDB
	err := i.InboundRepo.UpdateDismantledData(ctx, DismantledInbounds)
	if err != nil {
		return err
	}

	// 生成入库单号
	inboundNo, err := i.GenerateTicketNoByInboundType(inbound.TypePartInbound, inbound.SourceTypeDismantled)
	if err != nil {
		return err
	}

	//TODO 创建 inboundList 创建DismantledInbound
	inboundList := &inbound.InboundList{
		InboundTitle:  InboundTitle,
		InboundNo:     inboundNo,
		InboundReason: inbound.SourceTypeDismantled,
		InboundType:   inbound.TypePartInbound,
		CreateBy:      DismantledInbounds.CreateBy,
		CreaterID:     DismantledInbounds.CreateID,
		Stage:         common.StageCompleteInbound,
	}
	err = i.InboundRepo.CreateDismantledInbound(ctx, inboundList, &DismantledInbounds)
	if err != nil {
		return err
	}

	for _, dismantledInbound := range DismantledInbounds.Details {
		err := i.updateInventory(ctx, DismantledInbounds.ID, dismantledInbound.ProductID, dismantledInbound.WarehouseID, 1, constants.ChangeReasonDismantled)
		if err != nil {
			return err
		}
	}
	//TODO 飞书通知

	return nil
}

// CreateDismantledInboundByInputV1 临时过渡方案
func (i *inboundService) CreateDismantledInboundByInputV1(ctx context.Context, DismantledInbounds inbound.DismantledInbound, InboundTitle, Project string) (string, error) {
	// 收集所有需的主设备SN
	mainSNs := make([]string, 0, len(DismantledInbounds.Details))

	// 用于记录已检查过的SN，避免重复查询
	checkedMainSNs := make(map[string]struct{})

	// 提取所有需要验证的SN（去重）
	for _, DisInbound := range DismantledInbounds.Details {
		// 验证mainSN
		if _, exists := checkedMainSNs[DisInbound.MainDeviceSN]; !exists {
			mainSNs = append(mainSNs, DisInbound.MainDeviceSN)
			checkedMainSNs[DisInbound.MainDeviceSN] = struct{}{}
		}
	}
	// 批量验证主设备SN
	if len(mainSNs) > 0 {
		exists, err := i.checkMainDeviceSNsExist(ctx, mainSNs)
		if err != nil {
			return "", fmt.Errorf("验证主设备SN失败: %w", err)
		}

		for _, sn := range mainSNs {
			if !exists[sn] {
				return "", fmt.Errorf("主设备SN不存在: %s", sn)
			}
		}
	}

	// 更新CMDB
	err := i.InboundRepo.UpdateDismantledDataV1(ctx, DismantledInbounds)
	if err != nil {
		return "", err
	}

	// 生成入库单号
	inboundNo, err := i.GenerateTicketNoByInboundType(inbound.TypePartInbound, inbound.SourceTypeDismantled)
	if err != nil {
		return "", err
	}

	//创建 inboundList 创建DismantledInbound
	inboundList := &inbound.InboundList{
		Project:       Project,
		InboundTitle:  InboundTitle,
		InboundNo:     inboundNo,
		InboundReason: inbound.SourceTypeDismantled,
		InboundType:   inbound.TypePartInbound,
		CreateBy:      DismantledInbounds.CreateBy,
		CreaterID:     DismantledInbounds.CreateID,
		Stage:         common.StageCompleteInbound,
	}
	DismantledInbounds.Project = Project
	DismantledInbounds.InboundTitle = InboundTitle
	err = i.InboundRepo.CreateDismantledInbound(ctx, inboundList, &DismantledInbounds)
	if err != nil {
		return "", err
	}

	for _, dismantledInbound := range DismantledInbounds.Details {
		err := i.updateInventory(ctx, DismantledInbounds.ID, dismantledInbound.ProductID, dismantledInbound.WarehouseID, 1, constants.ChangeReasonDismantled)
		if err != nil {
			return "", err
		}
	}

	return inboundNo, nil
}

// checkMainDeviceSNsExist 批量检查主设备SN是否存在
func (i *inboundService) checkMainDeviceSNsExist(ctx context.Context, sns []string) (map[string]bool, error) {
	existsMap, err := i.serverSvc.ValidateServerSNs(sns)
	if err != nil {
		return nil, err
	}
	return existsMap, nil
}

// CreateRepairInboundByInput 通过输入的方式创建列表+返修入库+返修入库历史
func (i *inboundService) CreateRepairInboundByInput(ctx context.Context, repairInbound *inbound.RepairInbound, inboundTitle, project string) (string, error) {
	// 生成入库单号
	inboundNo, err := i.GenerateTicketNoByInboundType(inbound.TypePartInbound, inbound.SourceTypeRepaired)
	if err != nil {
		return "", err
	}

	// 进行SN校验
	if err := i.ValidRepairPartInboundSN(ctx, repairInbound.RepairDetails); err != nil {
		return "", err
	}
	// 创建入库单列表
	inboundList := &inbound.InboundList{
		Project:       project,
		InboundNo:     inboundNo,
		InboundTitle:  inboundTitle,
		InboundReason: inbound.SourceTypeReturnRepaired,
		InboundType:   inbound.TypePartInbound,
		CreateBy:      repairInbound.CreateBy,
		CreaterID:     repairInbound.CreateID,
		Stage:         common.StageAssetApproval,
	}
	repairInbound.InboundNo = inboundNo
	repairInbound.Project = project
	repairInbound.InboundTitle = inboundTitle

	// 调用repository层
	err = i.InboundRepo.CreateRepairInbound(ctx, inboundList, repairInbound)
	if err != nil {
		return "", err
	}
	return inboundNo, nil
}

// ValidRepairPartInboundSN 校验返修入库SN，如果不存在于数据库中，则报错
func (i *inboundService) ValidRepairPartInboundSN(ctx context.Context, details []inbound.RepairInboundDetails) error {
	var (
		repairsSNs []string
		replaceSns []string
	)
	fmt.Println(details)
	for _, detail := range details {
		switch detail.RepairType {
		case "renew":
			replaceSns = append(replaceSns, detail.ReplaceSN)
		case "repaired":
			repairsSNs = append(repairsSNs, detail.SN)
		default:
			return fmt.Errorf("返修入库类型错误，不能为%s", detail.RepairType)
		}
	}
	fmt.Println("repairsSNs:", repairsSNs, ", replaceSns:", replaceSns)
	if err := i.InboundRepo.ValidRepairPartInboundSN(ctx, repairsSNs, replaceSns); err != nil {
		return err
	}

	return nil
}

// ValidNewPartInboundSN 检查新购配件入库的SN是否存在CMDB当中，如果存在于CMDB当中，报错并抛出相对应的SN
func (i *inboundService) ValidNewPartInboundSN(ctx context.Context, SNs []string) error {
	err := i.InboundRepo.ValidNewPartInboundSN(ctx, SNs)
	if err != nil {
		return err
	}
	return nil
}

// ImportNewInbound 通过Excel文件导入新入库单
func (i *inboundService) ImportNewInbound(ctx context.Context, file *multipart.FileHeader) ([]inbound.NewInboundInfo, error) {
	// 打开文件
	src, err := file.Open()
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %w", err)
	}
	defer func(src multipart.File) {
		err := src.Close()
		if err != nil {
			fmt.Printf("关闭文件失败：%v", err)
		}
	}(src)

	// 构建临时文件路径
	timestamp := time.Now().UnixNano()
	filename := fmt.Sprintf("%s_%d.%s", "NewPartInbound", timestamp, filepath.Ext(file.Filename))

	// 安全地构建文件路径
	basePath := "storage/imports"
	if err := os.MkdirAll(basePath, 0750); err != nil {
		return nil, fmt.Errorf("创建目录失败: %w", err)
	}

	// 验证文件名安全性
	//if !isFileNameSafe(filename) {
	//	return nil, fmt.Errorf("文件名不安全: %s", filename)
	//}
	//
	filePath := filepath.Join(basePath, filename)

	// 确保路径没有超出基础目录
	absPath, err := filepath.Abs(filePath)
	if err != nil {
		return nil, fmt.Errorf("获取绝对路径失败: %w", err)
	}
	absBasePath, err := filepath.Abs(basePath)
	if err != nil {
		return nil, fmt.Errorf("获取基础目录绝对路径失败: %w", err)
	}
	if !strings.HasPrefix(absPath, absBasePath) {
		return nil, fmt.Errorf("文件路径超出允许范围")
	}

	// 使用安全的文件操作
	// #nosec G304 -- 文件路径已经过验证，确保在安全的目录中
	dst, err := os.OpenFile(filePath, os.O_WRONLY|os.O_CREATE|os.O_EXCL, 0600)
	if err != nil {
		return nil, fmt.Errorf("创建临时文件失败: %w", err)
	}
	defer func(dst *os.File) {
		err := dst.Close()
		if err != nil {
			fmt.Printf("关闭文件失败: %s\n", err)
		}
		// 操作完成后删除临时文件
		err = os.Remove(filePath)
		if err != nil {
			fmt.Printf("删除临时文件失败: %s\n", err)
		}
	}(dst)

	// 将文件保存到本地
	if _, err = io.Copy(dst, src); err != nil {
		return nil, fmt.Errorf("保存文件失败: %w", err)
	}
	info, err := i.ReadNewPartInboundExcel(filePath)
	if err != nil {
		return nil, err
	}
	return info, nil
}

// ReadNewPartInboundExcel 读取新购入库Excel文件
func (i *inboundService) ReadNewPartInboundExcel(filePath string) ([]inbound.NewInboundInfo, error) {
	// 打开Excel文件
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开Excel文件失败: %w", err)
	}
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Printf("关闭Excel文件失败: %v\n", err)
		}
	}()

	// 获取所有行
	rows, err := f.GetRows(exportModel.PurchaseInboundSheet)
	if err != nil {
		return nil, fmt.Errorf("读取工作表失败: %w", err)
	}
	fmt.Println(len(rows))
	// 检查是否有数据
	if len(rows) < 2 { // 至少要有表头和一行数据
		return nil, fmt.Errorf("Excel文件中没有数据")
	}

	// 存储结果
	var results []inbound.NewInboundInfo

	// 从第二行开始读取数据（跳过表头）
	for index := 1; index < len(rows); index++ {
		row := rows[index]
		// 检查行是否有足够的列
		if len(row) < 9 { // 需要至少9列数据
			continue // 跳过数据不完整的行
		}

		// 检查PN号码是否为空
		if row[0] == "" {
			continue // 如果PN为空，跳过该行
		}

		productID, err := strconv.ParseUint(row[8], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("规格ID转换格式失败: %w", err)
		}
		amount, err := strconv.ParseFloat(row[2], 64)
		if err != nil {
			return nil, fmt.Errorf("数量转换格式失败: %w", err)
		}

		// 创建记录
		item := inbound.NewInboundInfo{
			ProductID: uint(productID),
			Amount:    uint(amount),
		}

		// 添加到结果集
		results = append(results, item)
	}

	return results, nil
}

// ReadNewPartInboundDetail 获取新购配件入库详情
func (i *inboundService) ReadNewPartInboundDetail(filePath string) ([]inbound.NewInboundDetail, error) {
	// 打开文件
	file, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取入库详情发生错误，打开文件失败: %w", err)
	}
	defer func(file *excelize.File) {
		err := file.Close()
		if err != nil {
			fmt.Printf("关闭文件失败: %v\n", err)
		}
	}(file)

	// 获取每一行数据
	rows, err := file.GetRows(exportModel.NewPartSheet)
	if err != nil {
		return nil, fmt.Errorf("获取%v信息失败: %w", exportModel.NewPartSheet, err)
	}

	if len(rows) < 2 {
		return nil, fmt.Errorf("%v中没有数据", exportModel.NewPartSheet)
	}

	var results []inbound.NewInboundDetail
	for index := 1; index < len(rows); index++ {
		row := rows[index]
		fmt.Println(row)
		// 判定表格是否齐全
		if len(row) < 10 {
			continue
		}
		if row[0] == "" {
			continue
		}

		detailID, err := strconv.ParseUint(row[9], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("入库详情ID转换失败: %w", err)
		}
		warehouseID, err := strconv.ParseUint(row[10], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("仓库ID转换失败: %w", err)
		}

		item := inbound.NewInboundDetail{
			SN:            row[0],
			WarehouseName: row[1],
			WarehouseID:   uint(warehouseID),
		}
		item.ID = uint(detailID)
		results = append(results, item)
	}
	return results, nil
}

// ImportDismantledInbound 通过Excel文件导入拆机入库单
func (i *inboundService) ImportDismantledInbound(ctx context.Context, file *multipart.FileHeader) (*inbound.DismantledInbound, error) {
	// 打开文件
	src, err := file.Open()
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %w", err)
	}
	defer func(src multipart.File) {
		err := src.Close()
		if err != nil {
			fmt.Printf("关闭文件失败：%v", err)
		}
	}(src)

	// 构建临时文件路径
	timestamp := time.Now().UnixNano()
	fileName := fmt.Sprintf("%s_%d.%s", "DismantledInbound", timestamp, filepath.Ext(file.Filename))

	// 安全地构建文件路径
	basePath := "storage/imports"
	if err := os.MkdirAll(basePath, 0750); err != nil {
		return nil, fmt.Errorf("创建目录失败: %w", err)
	}

	// 验证文件名安全性
	//if !isFileNameSafe(fileName) {
	//	return nil, fmt.Errorf("文件名不安全: %s", fileName)
	//}

	filePath := filepath.Join(basePath, fileName)

	// 确保路径没有超出基础目录
	absPath, err := filepath.Abs(filePath)
	if err != nil {
		return nil, fmt.Errorf("获取绝对路径失败: %w", err)
	}
	absBasePath, err := filepath.Abs(basePath)
	if err != nil {
		return nil, fmt.Errorf("获取基础目录绝对路径失败: %w", err)
	}
	if !strings.HasPrefix(absPath, absBasePath) {
		return nil, fmt.Errorf("文件路径超出允许范围")
	}

	// 使用安全的文件操作
	// #nosec G304 -- 文件路径已经过验证，确保在安全的目录中
	dst, err := os.OpenFile(filePath, os.O_WRONLY|os.O_CREATE|os.O_EXCL, 0600)
	if err != nil {
		return nil, fmt.Errorf("创建临时文件失败: %w", err)
	}
	defer func(dst *os.File) {
		err := dst.Close()
		if err != nil {
			fmt.Printf("关闭文件失败：%v", err)
		}
		// 删除临时文件
		err = os.Remove(filePath)
		if err != nil {
			fmt.Printf("删除临时文件失败：%v", err)
		}
	}(dst)

	// 保存文件到临时目录
	if _, err := io.Copy(dst, src); err != nil {
		return nil, fmt.Errorf("保存文件失败: %w", err)
	}

	// 读取Excel文件
	dismantledInbound, err := i.readDismantledInboundExcel(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取Excel文件失败: %w", err)
	}

	return dismantledInbound, nil
}

// readDismantledInboundExcel 读取拆机入库Excel文件
func (i *inboundService) readDismantledInboundExcel(filePath string) (*inbound.DismantledInbound, error) {
	// 打开Excel文件
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开Excel文件失败: %w", err)
	}
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Printf("关闭Excel文件失败: %v\n", err)
		}
	}()

	// 获取所有行
	rows, err := f.GetRows(exportModel.DismantlePartdSheet)
	if err != nil {
		return nil, fmt.Errorf("读取工作表失败: %w", err)
	}

	// 检查是否有数据
	if len(rows) < 2 { // 至少要有表头和一行数据
		return nil, fmt.Errorf("Excel文件中没有数据")
	}

	dismantledInbound := &inbound.DismantledInbound{
		Details: make([]inbound.DismantledInboundDetails, 0),
	}

	// 从第二行开始读取数据（跳过表头）
	for i := 1; i < len(rows); i++ {
		row := rows[i]
		// 检查行是否有足够的列
		if len(row) < 8 { // 需要至少8列数据：主设备SN、配件SN、配件状态、配件PN、配件名称、规格ID、仓库ID、仓库名称
			continue
		}

		// 检查必要字段是否为空
		if row[0] == "" || row[1] == "" {
			continue
		}

		// 转换规格ID和仓库ID
		productID, err := strconv.ParseUint(row[11], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("第%d行规格ID格式错误: %w", i+1, err)
		}

		warehouseID, err := strconv.ParseUint(row[12], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("第%d行仓库ID格式错误: %w", i+1, err)
		}

		detail := inbound.DismantledInboundDetails{
			MainDeviceSN:   row[1],
			ComponentSN:    row[0],
			ComponentState: row[13],
			ProductID:      uint(productID),
			WarehouseID:    uint(warehouseID),
			WarehouseName:  row[3],
			NeedReturn:     false, // 默认不需要返厂
		}

		dismantledInbound.Details = append(dismantledInbound.Details, detail)
	}

	if len(dismantledInbound.Details) == 0 {
		return nil, fmt.Errorf("Excel文件中没有有效数据")
	}

	return dismantledInbound, nil
}

// ImportRepairInbound 通过Excel文件导入返修入库单
func (i *inboundService) ImportRepairInbound(ctx context.Context, file *multipart.FileHeader) (*inbound.RepairInbound, error) {
	// 打开文件
	src, err := file.Open()
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %w", err)
	}
	defer func(src multipart.File) {
		err := src.Close()
		if err != nil {
			fmt.Printf("关闭文件失败：%v", err)
		}
	}(src)

	// 构建临时文件路径
	timestamp := time.Now().UnixNano()
	fileName := fmt.Sprintf("%s_%d.%s", "RepairInbound", timestamp, filepath.Ext(file.Filename))

	// 安全地构建文件路径
	basePath := "storage/imports"
	if err := os.MkdirAll(basePath, 0750); err != nil {
		return nil, fmt.Errorf("创建目录失败: %w", err)
	}

	// 验证文件名安全性
	//if !isFileNameSafe(fileName) {
	//	return nil, fmt.Errorf("文件名不安全: %s", fileName)
	//}

	filePath := filepath.Join(basePath, fileName)

	// 确保路径没有超出基础目录
	absPath, err := filepath.Abs(filePath)
	if err != nil {
		return nil, fmt.Errorf("获取绝对路径失败: %w", err)
	}
	absBasePath, err := filepath.Abs(basePath)
	if err != nil {
		return nil, fmt.Errorf("获取基础目录绝对路径失败: %w", err)
	}
	if !strings.HasPrefix(absPath, absBasePath) {
		return nil, fmt.Errorf("文件路径超出允许范围")
	}

	// 使用安全的文件操作
	// #nosec G304 -- 文件路径已经过验证，确保在安全的目录中
	dst, err := os.OpenFile(filePath, os.O_WRONLY|os.O_CREATE|os.O_EXCL, 0600)
	if err != nil {
		return nil, fmt.Errorf("创建临时文件失败: %w", err)
	}
	defer func(dst *os.File) {
		err := dst.Close()
		if err != nil {
			fmt.Printf("关闭文件失败：%v", err)
		}
		// 删除临时文件
		err = os.Remove(filePath)
		if err != nil {
			fmt.Printf("删除临时文件失败：%v", err)
		}
	}(dst)

	// 保存文件到临时目录
	if _, err := io.Copy(dst, src); err != nil {
		return nil, fmt.Errorf("保存文件失败: %w", err)
	}

	// 读取Excel文件
	repairInbound, err := i.readRepairInboundExcel(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取Excel文件失败: %w", err)
	}

	return repairInbound, nil
}

// readRepairInboundExcel 读取返修入库Excel文件
func (i *inboundService) readRepairInboundExcel(filePath string) (*inbound.RepairInbound, error) {
	// 打开Excel文件
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开Excel文件失败: %w", err)
	}
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Printf("关闭Excel文件失败: %v\n", err)
		}
	}()

	// 获取所有行
	rows, err := f.GetRows(exportModel.ReturnRepairPartSheet)
	if err != nil {
		return nil, fmt.Errorf("读取工作表失败: %w", err)
	}

	// 检查是否有数据
	if len(rows) < 2 { // 至少要有表头和一行数据
		return nil, fmt.Errorf("Excel文件中没有数据")
	}

	repairInbound := &inbound.RepairInbound{
		RepairDetails: make([]inbound.RepairInboundDetails, 0),
	}

	// 从第二行开始读取数据（跳过表头）
	for i := 1; i < len(rows); i++ {
		row := rows[i]
		// 检查行是否有足够的列
		if len(row) < 6 { // 需要至少6列数据：SN、维修类型、原SN、入库位置、维修类型映射、仓库ID
			continue
		}

		// 检查必要字段是否为空
		if row[0] == "" || row[1] == "" {
			continue
		}

		// 转换仓库ID
		warehouseID, err := strconv.ParseUint(row[5], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("第%d行仓库ID格式错误: %w", i+1, err)
		}

		detail := inbound.RepairInboundDetails{
			SN:            row[0],
			RepairType:    row[4], // 使用映射后的维修类型
			ReplaceSN:     row[2], // 原SN
			WarehouseID:   uint(warehouseID),
			WarehouseName: row[3],
		}

		repairInbound.RepairDetails = append(repairInbound.RepairDetails, detail)
	}

	if len(repairInbound.RepairDetails) == 0 {
		return nil, fmt.Errorf("Excel文件中没有有效数据")
	}

	return repairInbound, nil
}

// updateInventory 更新库存
func (i *inboundService) updateInventory(ctx context.Context, ticketID, productID, warehouseID uint, delta int, reason string) error {
	detail, err := i.inventorySvc.GetInventoryByProductIDandWarehouseID(ctx, productID, warehouseID)
	if err != nil {
		return err
	}
	fmt.Println("detailID", detail.ID)
	err = i.inventorySvc.AdjustStock(ctx, detail.ID, delta, constants.ChangeTypeInbound, reason, ticketID, 0)
	if err != nil {
		return err
	}
	return nil
}

// UpdateDeviceInboundDetailsByInput 更新整机入库详情
func (i *inboundService) UpdateDeviceInboundDetailsByInput(ctx context.Context, details []inbound.DeviceInboundDetail) error {
	return i.UpdateDeviceInbound(ctx, details)
}

func (i *inboundService) UpdateDeviceInbound(ctx context.Context, details []inbound.DeviceInboundDetail) error {
	if len(details) == 0 {
		return fmt.Errorf("详情数据为空")
	}
	// 校验数据库中是否存在相同的SN
	// 验证整机入库SN
	var Sns []string
	for _, detail := range details {
		Sns = append(Sns, detail.SN)
	}
	if err := i.ValidNewPartInboundSN(ctx, Sns); err != nil {
		return err
	}

	return i.InboundRepo.UpdateDeviceInboundDetailsByInput(ctx, details)
}

// ValidDeviceInboundSN 检查设备入库的SN是否存在CMDB当中，如果存在于CMDB当中，报错并抛出相对应的SN
func (i *inboundService) ValidDeviceInboundSN(ctx context.Context, SNs []string) error {
	err := i.InboundRepo.ValidDeviceInboundSN(ctx, SNs)
	if err != nil {
		return err
	}
	return nil
}
