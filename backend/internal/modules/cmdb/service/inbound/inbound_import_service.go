package inbound

import (
	inboundModel "backend/internal/modules/cmdb/model/inbound"
	productModel "backend/internal/modules/cmdb/model/product"
	"backend/internal/modules/cmdb/repository/asset"
	"backend/internal/modules/cmdb/repository/inbound"
	productRepo "backend/internal/modules/cmdb/repository/product"
	importhandler "backend/internal/modules/import/handle"
	"backend/pkg/utils"
	"bufio"
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

type InboundImportService interface {
	// CSV导入
	ImportProductAndSpareData(ctx context.Context, productReader, spareReader io.Reader, newInboundID uint) (*inboundModel.ImportResult, error)
	ImportSpareData(ctx context.Context, spareReader io.Reader, newInboundID uint) (*inboundModel.ImportResult, error)

	// 读取CSV
	readProductCSV(reader io.Reader) ([]inboundModel.ProductCSVData, map[string]bool, error)
	readSpareCSV(reader io.Reader) ([]inboundModel.SpareCSVData, error)

	// 数据验证
	validateSparePN(spares []inboundModel.SpareCSVData, productPNMap map[string]bool) map[string][]int

	// 数据处理
	//processProductData(ctx context.Context, tx *gorm.DB, products []inboundModel.ProductCSVData, productRepo ProductRepository) (map[string]int, error)
	//processSpareData(ctx context.Context, tx *gorm.DB, spares []inboundModel.SpareCSVData, productRepo ProductRepository, warehouseRepo WarehouseRepository, result *inboundModel.ImportResult) (int, error)
}

// inboundImportService 入库导入服务
type inboundImportService struct {
	inboundRepo    inbound.Repository
	productRepo    productRepo.ProductRepository
	spareHandler   *importhandler.SpareHandler
	productHandler *importhandler.ProductHandler
	warehouseRepo  asset.WarehouseRepository
}

// NewInboundImportService 创建新的入库导入服务实例
func NewInboundImportService(inboundRepo inbound.Repository, spareHandler *importhandler.SpareHandler, productHandler *importhandler.ProductHandler, productRepo productRepo.ProductRepository, warehouseRepo asset.WarehouseRepository) InboundImportService {
	return &inboundImportService{
		inboundRepo:    inboundRepo,
		spareHandler:   spareHandler,
		productHandler: productHandler,
		productRepo:    productRepo,
		warehouseRepo:  warehouseRepo,
	}
}

// ProductRepository 定义产品仓库接口
// 提供产品相关的数据库操作方法
type ProductRepository interface {
	// GetByPN 根据PN获取产品
	GetByPN(ctx context.Context, pn string) (*productModel.Product, error)
	// ExistsByPN 检查产品是否存在
	ExistsByPN(ctx context.Context, pn string) (bool, error)
	// Create 创建产品
	Create(ctx context.Context, product *productModel.Product) error
}

// WarehouseRepository 定义仓库管理接口
// 提供仓库相关的数据库操作方法
type WarehouseRepository interface {
	// GetByNameOrCode 根据名称或编码获取仓库
	GetByNameOrCode(ctx context.Context, nameOrCode string) (*struct {
		ID   uint
		Name string
	}, error)
}

// RepositoryExtension 定义Repository扩展接口
// 提供获取数据库连接的方法
type RepositoryExtension interface {
	GetDB() *gorm.DB
}

// ImportProductAndSpareData 导入产品和备件数据
// 处理产品CSV和备件CSV的导入，包括数据验证和组装
// 参数:
//   - ctx: 上下文
//   - productReader: 产品CSV文件读取器
//   - spareReader: 备件CSV文件读取器
//
// 返回:
//   - *ImportResult: 导入结果
//   - error: 错误信息
func (s *inboundImportService) ImportProductAndSpareData(ctx context.Context, productReader, spareReader io.Reader, newInboundID uint) (*inboundModel.ImportResult, error) {
	// 初始化结果
	result := &inboundModel.ImportResult{
		InvalidPNs:   []map[string]interface{}{},
		ErrorDetails: []map[string]interface{}{},
	}

	// 1. 读取产品CSV数据
	productData, productPNMap, err := s.readProductCSV(productReader)
	if err != nil {
		return result, fmt.Errorf("读取产品CSV失败: %w", err)
	}

	// 2. 读取备件CSV数据
	spareData, err := s.readSpareCSV(spareReader)
	if err != nil {
		return result, fmt.Errorf("读取备件CSV失败: %w", err)
	}

	// 3. 校验备件中的PN是否在产品中存在
	invalidPNs := s.validateSparePN(spareData, productPNMap)

	// 如果有无效的PN号，添加到结果并返回
	if len(invalidPNs) > 0 {
		for pn, lines := range invalidPNs {
			for _, line := range lines {
				result.InvalidPNs = append(result.InvalidPNs, map[string]interface{}{
					"pn":   pn,
					"line": line,
				})
			}
		}
		result.Total = len(spareData)
		result.Failed = len(spareData)
		result.Success = 0
		return result, errors.New("备件CSV中包含产品CSV未定义的PN号")
	}

	// 4. 组装NewInboundInfo数组
	var newInboundInfos []*inboundModel.NewInboundDetail
	productMap := make(map[string]inboundModel.ProductCSVData)
	for _, product := range productData {
		productMap[product.PN] = product
	}

	for _, spare := range spareData {
		_, exists := productMap[spare.PN]
		if !exists {
			continue // 已在前面的验证中处理过
		}

		// 转换状态
		assetStatus, err := inboundModel.ConvertAssetStatus(spare.AssetStatus)
		if err != nil {
			result.ErrorDetails = append(result.ErrorDetails, map[string]interface{}{
				"line":  spare.LineNumber,
				"error": err.Error(),
			})
			continue
		}

		hardwareStatus, err := inboundModel.ConvertHardwareStatus(spare.HardwareStatus)
		if err != nil {
			result.ErrorDetails = append(result.ErrorDetails, map[string]interface{}{
				"line":  spare.LineNumber,
				"error": err.Error(),
			})
			continue
		}

		sourceType, err := inboundModel.ConvertSourceType(spare.SourceType)
		if err != nil {
			result.ErrorDetails = append(result.ErrorDetails, map[string]interface{}{
				"line":  spare.LineNumber,
				"error": err.Error(),
			})
			continue
		}

		// 创建NewInboundInfo
		newInboundInfo := &inboundModel.NewInboundDetail{
			NewInboundID: newInboundID,

			// 规格相关
			//MaterialType: product.MaterialType,
			//Brand:        product.Brand,
			//ProductModel: product.Model,
			//Spec:         product.Spec,
			//PN:           product.PN,
			// 备件相关
			SN:              spare.SN,
			SourceType:      sourceType,
			AssetStatus:     assetStatus,
			HardwareStatus:  hardwareStatus,
			Price:           spare.Price,
			Location:        spare.Location,
			FirmwareVersion: spare.FirmwareVersion,
			RelatedAssetSN:  spare.RelatedAssetSN,
			Remark:          spare.Remark,
			WarehouseName:   spare.Warehouse, // repo层会处理仓库ID的映射
		}

		// 设置日期字段
		if !spare.PurchaseDate.IsZero() {
			newInboundInfo.PurchaseDate = utils.Date(spare.PurchaseDate)
		}
		if !spare.WarrantyExpire.IsZero() {
			newInboundInfo.WarrantyExpire = utils.Date(spare.WarrantyExpire)
		}

		newInboundInfos = append(newInboundInfos, newInboundInfo)
	}

	// 5. 调用repo层进行数据插入
	if len(result.ErrorDetails) == 0 {
		err = s.inboundRepo.CreateNewInboundInfo(ctx, newInboundInfos)
		if err != nil {
			return result, fmt.Errorf("批量创建入库记录失败: %w", err)
		}
	}

	// 6. 更新结果
	result.Total = len(spareData)
	result.Success = len(newInboundInfos)
	result.Failed = len(result.ErrorDetails)

	return result, nil
}

// 导入 Spare 数据， 进行数据校验并拼接
func (s *inboundImportService) ImportSpareData(ctx context.Context, spareReader io.Reader, newInboundID uint) (*inboundModel.ImportResult, error) {
	// 初始化结果
	result := &inboundModel.ImportResult{
		InvalidPNs:   []map[string]interface{}{},
		ErrorDetails: []map[string]interface{}{},
	}

	//  1.读取备件CSV数据
	spareData, err := s.readSpareCSV(spareReader)
	if err != nil {
		return result, fmt.Errorf("读取备件CSV失败: %w", err)
	}

	// 2.获取数据库中所有的product
	filter := productRepo.ProductFilter{}
	pagination := productRepo.PaginationOptions{
		PageSize: 500,
		Page:     1,
	}
	products, _, err := s.productRepo.List(ctx, filter, pagination)
	if err != nil {
		return nil, fmt.Errorf("获取系统规格信息失败：%w", err)
	}
	// 创建PN映射，优化匹配速度
	productPNMap := make(map[string]bool)
	for _, product := range products {
		productPNMap[product.PN] = true
	}

	// 3. 校验备件中的PN是否在产品中存在
	invalidPNs := s.validateSparePN(spareData, productPNMap)
	// 如果有无效的PN号，添加到结果并返回
	if len(invalidPNs) > 0 {
		for pn, lines := range invalidPNs {
			for _, line := range lines {
				result.InvalidPNs = append(result.InvalidPNs, map[string]interface{}{
					"pn":   pn,
					"line": line,
				})
			}
		}
		result.Total = len(spareData)
		result.Failed = len(spareData)
		result.Success = 0
		return result, errors.New("备件CSV中包含未定义的PN号")
	}

	// 4. 组装NewInboundInfo数组
	var newInboundInfos []*inboundModel.NewInboundDetail
	productMap := make(map[string]*productModel.Product)

	for _, product := range products {
		productMap[product.PN] = product
	}
	for _, spare := range spareData {
		//product, exists := productMap[spare.PN]
		_, exists := productMap[spare.PN]
		if !exists {
			continue // 已在前面的验证中处理过
		}

		// 转换状态
		assetStatus, err := inboundModel.ConvertAssetStatus(spare.AssetStatus)
		if err != nil {
			result.ErrorDetails = append(result.ErrorDetails, map[string]interface{}{
				"line":  spare.LineNumber,
				"error": err.Error(),
			})
			continue
		}

		hardwareStatus, err := inboundModel.ConvertHardwareStatus(spare.HardwareStatus)
		if err != nil {
			result.ErrorDetails = append(result.ErrorDetails, map[string]interface{}{
				"line":  spare.LineNumber,
				"error": err.Error(),
			})
			continue
		}

		sourceType, err := inboundModel.ConvertSourceType(spare.SourceType)
		if err != nil {
			result.ErrorDetails = append(result.ErrorDetails, map[string]interface{}{
				"line":  spare.LineNumber,
				"error": err.Error(),
			})
			continue
		}

		warehouse, err := s.warehouseRepo.GetByCodeOrName(ctx, spare.Warehouse)
		if err != nil {
			return nil, fmt.Errorf("获取仓库信息失败：%w", err)
		}
		// 创建NewInboundInfo
		newInboundInfo := &inboundModel.NewInboundDetail{
			NewInboundID: newInboundID,
			// 规格相关
			//MaterialType: product.MaterialType,
			//Brand:        product.Brand,
			//ProductModel: product.Model,
			//Spec:         product.Spec,
			//PN:           product.PN,
			// 备件相关
			SN:              spare.SN,
			SourceType:      sourceType,
			AssetStatus:     assetStatus,
			HardwareStatus:  hardwareStatus,
			Price:           spare.Price,
			Location:        spare.Location,
			FirmwareVersion: spare.FirmwareVersion,
			RelatedAssetSN:  spare.RelatedAssetSN,
			Remark:          spare.Remark,
			WarehouseID:     warehouse.ID,
			WarehouseName:   warehouse.Name,
		}

		// 设置日期字段
		if !spare.PurchaseDate.IsZero() {
			newInboundInfo.PurchaseDate = utils.Date(spare.PurchaseDate)
		}
		if !spare.WarrantyExpire.IsZero() {
			newInboundInfo.WarrantyExpire = utils.Date(spare.WarrantyExpire)
		}

		newInboundInfos = append(newInboundInfos, newInboundInfo)
	}

	// 5. 调用repo层进行数据插入
	if len(result.ErrorDetails) == 0 {
		err = s.inboundRepo.CreateNewInboundInfo(ctx, newInboundInfos)
		if err != nil {
			return result, fmt.Errorf("批量创建入库记录失败: %w", err)
		}
		newInbound := &inboundModel.NewInbound{
			Amount: uint(len(newInboundInfos)),
			Lock:   true,
		}
		err = s.inboundRepo.UpdateNewInbound(ctx, newInboundID, newInbound)
		if err != nil {
			// 记录错误但继续执行，因为更新失败不应该影响整个导入流程
			fmt.Printf("更新新入库单失败: %v，入库单ID: %d\n", err, newInboundID)
		}
	}

	// 6. 更新结果
	result.Total = len(spareData)
	result.Success = len(newInboundInfos)
	result.Failed = len(result.ErrorDetails)

	return result, nil
}

// readProductCSV 读取产品CSV数据
// 从CSV文件中读取产品数据，并创建产品PN映射表
// 参数:
//   - reader: CSV文件读取器
//
// 返回:
//   - []ProductCSVData: 产品数据列表
//   - map[string]bool: 产品PN映射表
//   - error: 错误信息
func (s *inboundImportService) readProductCSV(reader io.Reader) ([]inboundModel.ProductCSVData, map[string]bool, error) {
	csvReader := csv.NewReader(reader)
	csvReader.LazyQuotes = true
	csvReader.Comment = '#' // 设置#为注释符号

	// 读取头行
	headers, err := csvReader.Read()
	if err != nil {
		return nil, nil, fmt.Errorf("读取产品CSV头行失败: %w", err)
	}

	// 标准化头行 移除 UTF - 8 BOM，移除开头*号，移除首尾空格
	for i := range headers {
		headers[i] = strings.TrimSpace(strings.TrimPrefix(strings.TrimPrefix(headers[i], "\uFEFF"), "*"))
	}

	// 校验CSV表头
	err = s.productHandler.ValidateHeaders(headers)
	if err != nil {
		return nil, nil, err
	}

	// 查找必要列的索引
	materialTypeIndex := findColumnIndex(headers, "物料类型")
	brandIndex := findColumnIndex(headers, "品牌")
	modelIndex := findColumnIndex(headers, "型号")
	specIndex := findColumnIndex(headers, "规格")
	pnIndex := findColumnIndex(headers, "PN号码")
	productCategoryIndex := findColumnIndex(headers, "产品类别")

	// 读取所有产品数据
	var products []inboundModel.ProductCSVData
	pnMap := make(map[string]bool)
	lineNum := 1 // 头行是第1行
	formatError := &CSVFormatError{
		CommentLines: make([]int, 0),
		EmptyLines:   make([]int, 0),
		InvalidLines: make([]string, 0),
	}
	for {
		lineNum++
		record, err := csvReader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			formatError.InvalidLines = append(formatError.InvalidLines,
				fmt.Sprintf("读取第%d行产品数据失败: %s", lineNum, err.Error()))
			continue
		}

		// 提取产品数据
		pn := ""
		if pnIndex < len(record) {
			pn = strings.TrimSpace(record[pnIndex])
		}

		// PN号不能为空
		if pn == "" {
			formatError.InvalidLines = append(formatError.InvalidLines, fmt.Sprintf("%d行:PN号码不能为空", lineNum))
			continue
		}

		// 记录PN号
		pnMap[pn] = true

		// 创建产品数据对象
		productData := inboundModel.ProductCSVData{
			PN:         pn,
			LineNumber: lineNum,
		}

		// 设置其他字段
		if materialTypeIndex != -1 && materialTypeIndex < len(record) {
			productData.MaterialType = strings.TrimSpace(record[materialTypeIndex])
			if productData.MaterialType == "" {
				formatError.InvalidLines = append(formatError.InvalidLines, fmt.Sprintf("%d行:物料类型不能为空", lineNum))
			}
		}

		if brandIndex != -1 && brandIndex < len(record) {
			productData.Brand = strings.TrimSpace(record[brandIndex])
		}

		if modelIndex != -1 && modelIndex < len(record) {
			productData.Model = strings.TrimSpace(record[modelIndex])
		}

		if specIndex != -1 && specIndex < len(record) {
			productData.Spec = strings.TrimSpace(record[specIndex])
			if productData.Spec == "" {
				formatError.InvalidLines = append(formatError.InvalidLines, fmt.Sprintf("%d行:规格不能为空", lineNum))
			}
		}

		if productCategoryIndex != -1 && productCategoryIndex < len(record) {
			productData.ProductCategory = strings.TrimSpace(record[productCategoryIndex])
			if productData.ProductCategory == "" {
				formatError.InvalidLines = append(formatError.InvalidLines, fmt.Sprintf("%d行:产品类别不能为空", lineNum))
			}
		}

		if formatError.HasError() {
			return nil, nil, formatError
		}

		products = append(products, productData)
	}

	return products, pnMap, nil
}

// readSpareCSV 读取备件CSV数据
// 从CSV文件中读取备件数据，支持注释行和空行处理
// 参数:
//   - reader: CSV文件读取器
//
// 返回:
//   - []SpareCSVData: 备件数据列表
//   - error: 错误信息
func (s *inboundImportService) readSpareCSV(reader io.Reader) ([]inboundModel.SpareCSVData, error) {
	// 创建格式错误收集器
	formatError := &CSVFormatError{
		CommentLines: make([]int, 0),
		EmptyLines:   make([]int, 0),
		InvalidLines: make([]string, 0),
	}

	// 创建一个bufio.Reader以便处理注释行
	bufReader := bufio.NewReader(reader)

	// 首先读取整个文件进行格式检查
	var lines []string
	lineNum := 0
	for {
		line, err := bufReader.ReadString('\n')
		if err != nil && err != io.EOF {
			return nil, fmt.Errorf("读取第%d行失败: %w", lineNum+1, err)
		}

		lineNum++
		trimmedLine := strings.TrimSpace(line)

		// 检查注释行
		if strings.HasPrefix(trimmedLine, "#") {
			formatError.CommentLines = append(formatError.CommentLines, lineNum)
		}

		// 检查空行
		if trimmedLine == "" && err != io.EOF {
			formatError.EmptyLines = append(formatError.EmptyLines, lineNum)
		}

		lines = append(lines, line)

		if err == io.EOF {
			break
		}
	}

	// 如果存在格式错误，直接返回
	if formatError.HasError() {
		return nil, formatError
	}

	// 重新创建reader处理实际数据
	csvReader := csv.NewReader(strings.NewReader(strings.Join(lines, "")))
	csvReader.LazyQuotes = true

	// 读取头行
	headers, err := csvReader.Read()
	if err != nil {
		return nil, fmt.Errorf("读取备件CSV头行失败: %w", err)
	}

	// 标准化头行
	for i := range headers {
		headers[i] = strings.TrimSpace(strings.TrimPrefix(strings.TrimPrefix(headers[i], "\uFEFF"), "*"))
	}

	// 校验表头字段
	err = s.spareHandler.ValidateHeaders(headers)
	if err != nil {
		return nil, err
	}

	// 查找相关列
	columnIndexes := map[string]int{
		"SN":     -1,
		"产品PN号码": -1,
		"仓库":     -1,
		"来源类型":   -1,
		"资产状态":   -1,
		"硬件状态":   -1,
		"金额":     -1,
		"存放位置":   -1,
		"固件版本":   -1,
		"购买时间":   -1,
		"过保时间":   -1,
		"关联资产SN": -1,
		"备注":     -1,
	}

	// 查找所有列的索引
	for name := range columnIndexes {
		columnIndexes[name] = findColumnIndex(headers, name)
	}

	// 读取所有备件数据
	var spares []inboundModel.SpareCSVData
	lineNum = 1 // 重置行号（头行是第1行）

	for {
		lineNum++
		record, err := csvReader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			formatError.InvalidLines = append(formatError.InvalidLines,
				fmt.Sprintf("读取第%d行备件数据失败: %s", lineNum, err.Error()))
			continue
		}

		// 提取备件数据
		pn := ""
		if columnIndexes["产品PN号码"] < len(record) {
			pn = strings.TrimSpace(record[columnIndexes["产品PN号码"]])
		}

		// PN号不能为空
		if pn == "" {
			formatError.InvalidLines = append(formatError.InvalidLines,
				fmt.Sprintf("%d:PN号码不能为空", lineNum))
			continue
		}

		// SN 不能为空
		sn := ""
		if idx := columnIndexes["SN"]; idx != -1 && idx < len(record) {
			sn = strings.TrimSpace(record[idx])
		}
		if sn == "" {
			formatError.InvalidLines = append(formatError.InvalidLines,
				fmt.Sprintf("%d:SN不能为空", lineNum))
			continue
		}

		// 仓库不能为空
		warehouse := ""
		if idx := columnIndexes["仓库"]; idx != -1 && idx < len(record) {
			warehouse = strings.TrimSpace(record[idx])
		}
		if warehouse == "" {
			formatError.InvalidLines = append(formatError.InvalidLines,
				fmt.Sprintf("%d:仓库不能为空", lineNum))
			continue
		}

		// 来源类型不能为空
		sourceType := ""
		if idx := columnIndexes["来源类型"]; idx != -1 && idx < len(record) {
			sourceType = strings.TrimSpace(record[idx])
		}
		if sourceType == "" {
			formatError.InvalidLines = append(formatError.InvalidLines,
				fmt.Sprintf("%d:来源类型不能为空", lineNum))
			continue
		}

		// 资产状态不能为空
		assetStatus := ""
		if idx := columnIndexes["资产状态"]; idx != -1 && idx < len(record) {
			assetStatus = strings.TrimSpace(record[idx])
		}
		if assetStatus == "" {
			formatError.InvalidLines = append(formatError.InvalidLines,
				fmt.Sprintf("%d:资产状态不能为空", lineNum))
			continue
		}

		// 硬件状态不能为空
		hardwareStatus := ""
		if idx := columnIndexes["硬件状态"]; idx != -1 && idx < len(record) {
			hardwareStatus = strings.TrimSpace(record[idx])
		}
		if hardwareStatus == "" {
			formatError.InvalidLines = append(formatError.InvalidLines,
				fmt.Sprintf("%d:硬件状态不能为空", lineNum))
			continue
		}

		// 创建备件数据对象
		spareData := inboundModel.SpareCSVData{
			PN:             pn,
			SN:             sn,
			Warehouse:      warehouse,
			SourceType:     sourceType,
			AssetStatus:    assetStatus,
			HardwareStatus: hardwareStatus,
			LineNumber:     lineNum,
		}

		// 设置其他字段
		if idx := columnIndexes["金额"]; idx != -1 && idx < len(record) && record[idx] != "" {
			price, err := strconv.ParseFloat(strings.TrimSpace(record[idx]), 64)
			if err == nil {
				spareData.Price = price
			}
		}

		if idx := columnIndexes["存放位置"]; idx != -1 && idx < len(record) {
			spareData.Location = strings.TrimSpace(record[idx])
		}

		if idx := columnIndexes["固件版本"]; idx != -1 && idx < len(record) {
			spareData.FirmwareVersion = strings.TrimSpace(record[idx])
		}

		if idx := columnIndexes["购买时间"]; idx != -1 && idx < len(record) && record[idx] != "" {
			purchaseDate, err := parseDate(strings.TrimSpace(record[idx]))
			if err == nil {
				spareData.PurchaseDate = purchaseDate
			}
		}

		if idx := columnIndexes["过保时间"]; idx != -1 && idx < len(record) && record[idx] != "" {
			warrantyExpire, err := parseDate(strings.TrimSpace(record[idx]))
			if err == nil {
				spareData.WarrantyExpire = warrantyExpire
			}
		}

		if idx := columnIndexes["关联资产SN"]; idx != -1 && idx < len(record) {
			spareData.RelatedAssetSN = strings.TrimSpace(record[idx])
		}

		if idx := columnIndexes["备注"]; idx != -1 && idx < len(record) {
			spareData.Remark = strings.TrimSpace(record[idx])
		}

		spares = append(spares, spareData)
	}

	// 最后检查是否收集到任何错误
	if formatError.HasError() {
		return nil, formatError
	}

	return spares, nil
}

// validateSparePN 校验备件中的PN是否在产品中存在
// 检查备件CSV中的产品编号是否都存在于产品CSV中
// 参数:
//   - spares: 备件数据列表
//   - productPNMap: 产品PN映射表
//
// 返回:
//   - map[string][]int: 无效PN及其所在行号的映射
func (s *inboundImportService) validateSparePN(spares []inboundModel.SpareCSVData, productPNMap map[string]bool) map[string][]int {
	invalidPNs := make(map[string][]int)

	for _, spare := range spares {
		if _, exists := productPNMap[spare.PN]; !exists {
			invalidPNs[spare.PN] = append(invalidPNs[spare.PN], spare.LineNumber)
		}
	}

	return invalidPNs
}

// processProductData 处理产品数据
// 检查产品是否存在，不存在则创建新产品
// 参数:
//   - ctx: 上下文
//   - tx: 数据库事务
//   - products: 产品数据列表
//   - productRepo: 产品仓库接口
//
// 返回:
//   - map[string]int: 处理结果统计
//   - error: 错误信息
//
// nolint:unused
func (s *inboundImportService) processProductData(ctx context.Context, tx *gorm.DB, products []inboundModel.ProductCSVData, productRepo ProductRepository) (map[string]int, error) {
	result := map[string]int{
		"imported": 0,
		"existed":  0,
	}

	for _, productData := range products {
		// 检查产品是否已存在
		exists, err := productRepo.ExistsByPN(ctx, productData.PN)
		if err != nil {
			return nil, fmt.Errorf("检查产品PN=%s是否存在失败: %w", productData.PN, err)
		}

		if exists {
			// 产品已存在，跳过
			result["existed"]++
			continue
		}

		// 创建新产品
		newProduct := &productModel.Product{
			PN:              productData.PN,
			MaterialType:    productData.MaterialType,
			Brand:           productData.Brand,
			Model:           productData.Model,
			Spec:            productData.Spec,
			ProductCategory: productData.ProductCategory,
		}

		if err := productRepo.Create(ctx, newProduct); err != nil {
			return nil, fmt.Errorf("创建产品PN=%s失败: %w", productData.PN, err)
		}

		result["imported"]++
	}

	return result, nil
}

// processSpareData 处理备件数据
// 创建备件入库记录
// 参数:
//   - ctx: 上下文
//   - tx: 数据库事务
//   - spares: 备件数据列表
//   - productRepo: 产品仓库接口
//   - warehouseRepo: 仓库仓库接口
//   - result: 导入结果
//
// 返回:
//   - int: 成功处理的记录数
//   - error: 错误信息
//
// nolint:unused
func (s *inboundImportService) processSpareData(ctx context.Context, tx *gorm.DB, spares []inboundModel.SpareCSVData, productRepo ProductRepository, warehouseRepo WarehouseRepository, result *inboundModel.ImportResult) (int, error) {
	successCount := 0

	for _, spareData := range spares {
		// 获取产品ID
		_, err := productRepo.GetByPN(ctx, spareData.PN)
		if err != nil {
			result.ErrorDetails = append(result.ErrorDetails, map[string]interface{}{
				"line":  spareData.LineNumber,
				"error": fmt.Sprintf("获取产品PN=%s失败: %s", spareData.PN, err.Error()),
			})
			continue
		}

		// 获取仓库ID
		var warehouseID uint
		warehouse, err := warehouseRepo.GetByNameOrCode(ctx, spareData.Warehouse)
		if err != nil {
			result.ErrorDetails = append(result.ErrorDetails, map[string]interface{}{
				"line":  spareData.LineNumber,
				"error": fmt.Sprintf("获取仓库名称=%s失败: %s", spareData.Warehouse, err.Error()),
			})
			continue
		}
		warehouseID = warehouse.ID

		// 转换状态
		assetStatus, err := inboundModel.ConvertAssetStatus(spareData.AssetStatus)
		if err != nil {
			result.ErrorDetails = append(result.ErrorDetails, map[string]interface{}{
				"line":  spareData.LineNumber,
				"error": err.Error(),
			})
			continue
		}

		hardwareStatus, err := inboundModel.ConvertHardwareStatus(spareData.HardwareStatus)
		if err != nil {
			result.ErrorDetails = append(result.ErrorDetails, map[string]interface{}{
				"line":  spareData.LineNumber,
				"error": err.Error(),
			})
			continue
		}

		sourceType, err := inboundModel.ConvertSourceType(spareData.SourceType)
		if err != nil {
			result.ErrorDetails = append(result.ErrorDetails, map[string]interface{}{
				"line":  spareData.LineNumber,
				"error": err.Error(),
			})
			continue
		}

		// 创建入库信息
		newInboundInfo := &inboundModel.NewInboundDetail{
			NewInboundID:    1,
			WarehouseID:     warehouseID,
			WarehouseName:   spareData.Warehouse,
			SN:              spareData.SN,
			SourceType:      sourceType,     // 使用转换后的英文状态
			AssetStatus:     assetStatus,    // 使用转换后的英文状态
			HardwareStatus:  hardwareStatus, // 使用转换后的英文状态
			Price:           spareData.Price,
			Location:        spareData.Location,
			FirmwareVersion: spareData.FirmwareVersion,
			RelatedAssetSN:  spareData.RelatedAssetSN,
			Remark:          spareData.Remark,
		}

		// 设置日期字段
		if !spareData.PurchaseDate.IsZero() {
			newInboundInfo.PurchaseDate = utils.Date(spareData.PurchaseDate)
		}

		if !spareData.WarrantyExpire.IsZero() {
			newInboundInfo.WarrantyExpire = utils.Date(spareData.WarrantyExpire)
		}

		// 保存记录
		if err := tx.Create(newInboundInfo).Error; err != nil {
			result.ErrorDetails = append(result.ErrorDetails, map[string]interface{}{
				"line":  spareData.LineNumber,
				"error": fmt.Sprintf("保存入库信息失败: %s", err.Error()),
			})
			continue
		}

		successCount++
	}

	return successCount, nil
}

// findColumnIndex 查找列索引
// 在CSV头行中查找指定列名的索引
// 参数:
//   - headers: CSV头行
//   - columnName: 要查找的列名
//
// 返回:
//   - int: 列索引，如果未找到返回-1
func findColumnIndex(headers []string, columnName string) int {
	for i, header := range headers {
		if strings.TrimSpace(header) == columnName {
			return i
		}
	}
	return -1
}

// parseDate 解析日期字符串
// 支持多种日期格式的解析
// 参数:
//   - dateStr: 日期字符串
//
// 返回:
//   - time.Time: 解析后的时间
//   - error: 错误信息
func parseDate(dateStr string) (time.Time, error) {
	formats := []string{
		"2006-01-02",
		"2006/01/02",
		"02-01-2006",
		"02/01/2006",
	}

	for _, format := range formats {
		if t, err := time.Parse(format, dateStr); err == nil {
			return t, nil
		}
	}

	return time.Time{}, errors.New("无效的日期格式")
}

// productRepositoryImpl 实现ProductRepository接口
type productRepositoryImpl struct {
	db *gorm.DB
}

// NewProductRepository 创建产品仓库实例
func NewProductRepository(db *gorm.DB) ProductRepository {
	return &productRepositoryImpl{db: db}
}

// GetByPN 根据PN获取产品
func (r *productRepositoryImpl) GetByPN(ctx context.Context, pn string) (*productModel.Product, error) {
	var prod productModel.Product
	err := r.db.WithContext(ctx).Where("pn = ?", pn).First(&prod).Error
	if err != nil {
		return nil, err
	}
	return &prod, nil
}

// ExistsByPN 检查产品是否存在
func (r *productRepositoryImpl) ExistsByPN(ctx context.Context, pn string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&productModel.Product{}).Where("pn = ?", pn).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// Create 创建产品
func (r *productRepositoryImpl) Create(ctx context.Context, product *productModel.Product) error {
	return r.db.WithContext(ctx).Create(product).Error
}

// warehouseRepositoryImpl 实现WarehouseRepository接口
type warehouseRepositoryImpl struct {
	db *gorm.DB
}

// NewWarehouseRepository 创建仓库仓库实例
func NewWarehouseRepository(db *gorm.DB) WarehouseRepository {
	return &warehouseRepositoryImpl{db: db}
}

// GetByNameOrCode 根据名称或编码获取仓库
func (r *warehouseRepositoryImpl) GetByNameOrCode(ctx context.Context, nameOrCode string) (*struct {
	ID   uint
	Name string
}, error) {
	var result struct {
		ID   uint
		Name string
	}

	err := r.db.WithContext(ctx).
		Table("warehouses").
		Select("id, name").
		Where("name = ? OR code = ?", nameOrCode, nameOrCode).
		First(&result).Error

	if err != nil {
		return nil, err
	}

	return &result, nil
}

// CSVFormatError CSV格式错误
type CSVFormatError struct {
	CommentLines []int    // 包含注释的行号
	EmptyLines   []int    // 空行的行号
	InvalidLines []string // 其他格式错误，格式为"行号:错误描述"
}

// Error 实现error接口
func (e *CSVFormatError) Error() string {
	var errMsgs []string

	if len(e.CommentLines) > 0 {
		errMsgs = append(errMsgs, fmt.Sprintf("发现注释行（行号：%v），请删除所有注释", e.CommentLines))
	}
	if len(e.EmptyLines) > 0 {
		errMsgs = append(errMsgs, fmt.Sprintf("发现空行（行号：%v），请删除所有空行", e.EmptyLines))
	}
	if len(e.InvalidLines) > 0 {
		errMsgs = append(errMsgs, fmt.Sprintf("发现格式错误：\n%s", strings.Join(e.InvalidLines, "\n")))
	}

	return strings.Join(errMsgs, "\n")
}

// HasError 检查是否存在错误
func (e *CSVFormatError) HasError() bool {
	return len(e.CommentLines) > 0 || len(e.EmptyLines) > 0 || len(e.InvalidLines) > 0
}
