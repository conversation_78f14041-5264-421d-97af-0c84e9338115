package location

import (
	"backend/internal/modules/cmdb/model/location"
	repo "backend/internal/modules/cmdb/repository/location"
	"context"
)

// RoomService 房间服务接口
type RoomService interface {
	CreateRoom(ctx context.Context, room *location.Room) error
	UpdateRoom(ctx context.Context, room *location.Room) error
	DeleteRoom(ctx context.Context, id uint) error
	GetRoomByID(ctx context.Context, id uint) (*location.Room, error)
	GetRoomByName(ctx context.Context, name string) (*location.Room, error)
	GetRoomsByDataCenterID(ctx context.Context, dataCenterID uint) ([]*location.Room, error)
	ListRooms(ctx context.Context, page, pageSize int, query string, dataCenterID uint) ([]*location.Room, int64, error)
}

// roomService 房间服务实现
type roomService struct {
	repo repo.RoomRepository
}

// NewRoomService 创建房间服务
func NewRoomService(repo repo.RoomRepository) RoomService {
	return &roomService{repo: repo}
}

// CreateRoom 创建房间
func (s *roomService) CreateRoom(ctx context.Context, room *location.Room) error {
	return s.repo.Create(ctx, room)
}

// UpdateRoom 更新房间
func (s *roomService) UpdateRoom(ctx context.Context, room *location.Room) error {
	return s.repo.Update(ctx, room)
}

// DeleteRoom 删除房间
func (s *roomService) DeleteRoom(ctx context.Context, id uint) error {
	return s.repo.Delete(ctx, id)
}

// GetRoomByID 根据ID获取房间
func (s *roomService) GetRoomByID(ctx context.Context, id uint) (*location.Room, error) {
	return s.repo.GetByID(ctx, id)
}

// GetRoomByName 根据名称获取房间
func (s *roomService) GetRoomByName(ctx context.Context, name string) (*location.Room, error) {
	return s.repo.GetByName(ctx, name)
}

// GetRoomsByDataCenterID 根据机房ID获取房间列表
func (s *roomService) GetRoomsByDataCenterID(ctx context.Context, dataCenterID uint) ([]*location.Room, error) {
	return s.repo.GetByDataCenterID(ctx, dataCenterID)
}

// ListRooms 分页查询房间列表
func (s *roomService) ListRooms(ctx context.Context, page, pageSize int, query string, dataCenterID uint) ([]*location.Room, int64, error) {
	return s.repo.List(ctx, page, pageSize, query, dataCenterID)
}
