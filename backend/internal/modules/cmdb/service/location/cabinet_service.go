// service/location/cabinet_service.go
package location

import (
	"backend/internal/modules/cmdb/model/location"
	repo "backend/internal/modules/cmdb/repository/location"
	"context"
)

// CabinetService 机柜服务接口
type CabinetService interface {
	CreateCabinet(ctx context.Context, cabinet *location.Cabinet) error
	UpdateCabinet(ctx context.Context, cabinet *location.Cabinet) error
	DeleteCabinet(ctx context.Context, id uint) error
	GetCabinetByID(ctx context.Context, id uint) (*location.Cabinet, error)
	GetCabinetByName(ctx context.Context, name string) (*location.Cabinet, error)
	GetCabinetsByRoomID(ctx context.Context, roomID uint) ([]*location.Cabinet, error)
	ListCabinets(ctx context.Context, page, pageSize int, query string, roomID uint) ([]*location.Cabinet, int64, error)
}

// cabinetService 机柜服务实现
type cabinetService struct {
	repo repo.CabinetRepository
}

// NewCabinetService 创建机柜服务
func NewCabinetService(repo repo.CabinetRepository) CabinetService {
	return &cabinetService{repo: repo}
}

// CreateCabinet 创建机柜
func (s *cabinetService) CreateCabinet(ctx context.Context, cabinet *location.Cabinet) error {
	return s.repo.Create(ctx, cabinet)
}

// UpdateCabinet 更新机柜
func (s *cabinetService) UpdateCabinet(ctx context.Context, cabinet *location.Cabinet) error {
	return s.repo.Update(ctx, cabinet)
}

// DeleteCabinet 删除机柜
func (s *cabinetService) DeleteCabinet(ctx context.Context, id uint) error {
	return s.repo.Delete(ctx, id)
}

// GetCabinetByID 根据ID获取机柜
func (s *cabinetService) GetCabinetByID(ctx context.Context, id uint) (*location.Cabinet, error) {
	return s.repo.GetByID(ctx, id)
}

// GetCabinetByName 根据名称获取机柜
func (s *cabinetService) GetCabinetByName(ctx context.Context, name string) (*location.Cabinet, error) {
	return s.repo.GetByName(ctx, name)
}

// GetCabinetsByRoomID 根据房间ID获取机柜列表
func (s *cabinetService) GetCabinetsByRoomID(ctx context.Context, roomID uint) ([]*location.Cabinet, error) {
	return s.repo.GetByRoomID(ctx, roomID)
}

// ListCabinets 分页查询机柜列表
func (s *cabinetService) ListCabinets(ctx context.Context, page, pageSize int, query string, roomID uint) ([]*location.Cabinet, int64, error) {
	return s.repo.List(ctx, page, pageSize, query, roomID)
}
