package asset

import (
	"backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/cmdb/model/template"
	assetrepo "backend/internal/modules/cmdb/repository/asset"
	templaterepo "backend/internal/modules/cmdb/repository/template"
	"context"
	"errors"
	"fmt"
)

// NetworkDeviceService 网络设备服务接口
type NetworkDeviceService interface {
	// 创建网络设备
	Create(ctx context.Context, networkDevice *asset.NetworkDevice) error
	// 更新网络设备
	Update(ctx context.Context, networkDevice *asset.NetworkDevice) error
	// 删除网络设备
	Delete(ctx context.Context, id uint) error
	// 根据ID获取网络设备
	GetByID(ctx context.Context, id uint) (*asset.NetworkDevice, error)
	// 根据设备ID获取网络设备
	GetByDeviceID(ctx context.Context, deviceID uint) (*asset.NetworkDevice, error)
	// 获取网络设备列表
	List(ctx context.Context, page, pageSize int, query, role string, params map[string]interface{}) ([]*asset.NetworkDeviceWithDeviceInfo, int64, error)
	// 获取网络设备详情（包含设备和资源信息）
	GetDetailByID(ctx context.Context, id uint) (*asset.NetworkDeviceWithDeviceInfo, error)
	// 创建网络设备(包含设备基本信息)
	CreateWithDevice(ctx context.Context, device *asset.Device, networkDevice *asset.NetworkDevice) error
	// 更新网络设备(包含设备基本信息和资源信息)
	UpdateWithDevice(ctx context.Context, id uint, device *asset.Device, networkDevice *asset.NetworkDevice, resource *asset.Resource) error
	// 根据模板名称获取模板
	GetTemplateByName(ctx context.Context, name string) (*template.MachineTemplate, error)
}

// networkDeviceService 网络设备服务实现
type networkDeviceService struct {
	networkDeviceRepo   assetrepo.NetworkDeviceRepository
	deviceRepo          assetrepo.DeviceRepository
	machineTemplateRepo templaterepo.MachineTemplateRepository
	resourceRepo        assetrepo.ResourceRepository
}

// NewNetworkDeviceService 创建网络设备服务
func NewNetworkDeviceService(
	networkDeviceRepo assetrepo.NetworkDeviceRepository,
	deviceRepo assetrepo.DeviceRepository,
	machineTemplateRepo templaterepo.MachineTemplateRepository,
	resourceRepo assetrepo.ResourceRepository,
) NetworkDeviceService {
	return &networkDeviceService{
		networkDeviceRepo:   networkDeviceRepo,
		deviceRepo:          deviceRepo,
		machineTemplateRepo: machineTemplateRepo,
		resourceRepo:        resourceRepo,
	}
}

// Create 创建网络设备
func (s *networkDeviceService) Create(ctx context.Context, networkDevice *asset.NetworkDevice) error {
	// 验证设备ID是否存在
	device, err := s.deviceRepo.GetByID(ctx, networkDevice.DeviceID)
	if err != nil {
		return fmt.Errorf("获取设备信息失败: %w", err)
	}

	// 验证设备类型是否为网络设备
	if device.AssetType != asset.AssetTypeNetwork {
		return errors.New("设备类型不是网络设备")
	}

	// 验证网络设备角色是否有效
	if !asset.IsValidNetworkRole(networkDevice.Role) {
		return errors.New("无效的网络设备角色")
	}

	// 创建网络设备
	return s.networkDeviceRepo.Create(ctx, networkDevice)
}

// Update 更新网络设备
func (s *networkDeviceService) Update(ctx context.Context, networkDevice *asset.NetworkDevice) error {
	// 验证网络设备是否存在
	existingDevice, err := s.networkDeviceRepo.GetByID(ctx, networkDevice.ID)
	if err != nil {
		return fmt.Errorf("获取网络设备信息失败: %w", err)
	}

	// 验证网络设备角色是否有效
	if !asset.IsValidNetworkRole(networkDevice.Role) {
		return errors.New("无效的网络设备角色")
	}

	// 如果角色改变，需要更新对应的套餐模板
	if existingDevice.Role != networkDevice.Role {
		// 获取设备信息
		device, err := s.deviceRepo.GetByID(ctx, networkDevice.DeviceID)
		if err != nil {
			return fmt.Errorf("获取设备信息失败: %w", err)
		}

		// 根据角色获取套餐名称
		packageName := asset.GetPackageNameByRole(networkDevice.Role)
		if packageName != "" {
			// 查找对应的模板
			templateObj, err := s.machineTemplateRepo.GetByName(ctx, packageName)
			if err == nil && templateObj != nil {
				// 更新设备的模板ID
				device.TemplateID = templateObj.ID
				if err := s.deviceRepo.Update(ctx, device); err != nil {
					return fmt.Errorf("更新设备套餐模板失败: %w", err)
				}
			}
		}
	}

	// 更新网络设备
	return s.networkDeviceRepo.Update(ctx, networkDevice)
}

// Delete 删除网络设备
func (s *networkDeviceService) Delete(ctx context.Context, id uint) error {
	// 验证网络设备是否存在
	_, err := s.networkDeviceRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("获取网络设备信息失败: %w", err)
	}

	// 删除网络设备
	return s.networkDeviceRepo.Delete(ctx, id)
}

// GetByID 根据ID获取网络设备
func (s *networkDeviceService) GetByID(ctx context.Context, id uint) (*asset.NetworkDevice, error) {
	return s.networkDeviceRepo.GetByID(ctx, id)
}

// GetByDeviceID 根据设备ID获取网络设备
func (s *networkDeviceService) GetByDeviceID(ctx context.Context, deviceID uint) (*asset.NetworkDevice, error) {
	return s.networkDeviceRepo.GetByDeviceID(ctx, deviceID)
}

// List 获取网络设备列表
func (s *networkDeviceService) List(ctx context.Context, page, pageSize int, query, role string, params map[string]interface{}) ([]*asset.NetworkDeviceWithDeviceInfo, int64, error) {
	return s.networkDeviceRepo.List(ctx, page, pageSize, query, role, params)
}

// GetDetailByID 获取网络设备详情（包含设备和资源信息）
func (s *networkDeviceService) GetDetailByID(ctx context.Context, id uint) (*asset.NetworkDeviceWithDeviceInfo, error) {
	return s.networkDeviceRepo.GetDetailByID(ctx, id)
}

// CreateWithDevice 创建网络设备(包含设备信息)
func (s *networkDeviceService) CreateWithDevice(ctx context.Context, device *asset.Device, networkDevice *asset.NetworkDevice) error {
	// 验证设备类型
	if device.AssetType != asset.AssetTypeNetwork {
		return errors.New("设备类型必须为网络设备")
	}

	// 验证SN唯一性
	existingDevice, err := s.deviceRepo.GetBySN(ctx, device.SN)
	if err == nil && existingDevice != nil {
		return errors.New("设备SN已存在")
	}

	// 验证网络设备角色是否有效
	if !asset.IsValidNetworkRole(networkDevice.Role) {
		return errors.New("无效的网络设备角色")
	}

	// 根据角色获取套餐模板
	packageName := asset.GetPackageNameByRole(networkDevice.Role)
	if packageName != "" {
		// 查找对应的模板
		templateObj, err := s.machineTemplateRepo.GetByName(ctx, packageName)
		if err == nil && templateObj != nil {
			// 设置设备的模板ID
			device.TemplateID = templateObj.ID
		} else {
			// 如果找不到模板，可以考虑创建一个新模板
			newTemplate := &template.MachineTemplate{
				TemplateName: packageName,
				// 其他必填字段可以根据需要设置
				TemplateCategory: "network", // 网络设备类别
			}
			createdTemplate, err := s.machineTemplateRepo.Create(ctx, newTemplate)
			if err == nil && createdTemplate != nil {
				device.TemplateID = createdTemplate.ID
			}
		}
	}

	// 开启事务
	tx := s.deviceRepo.GetDB().Begin()

	// 创建设备
	if err := tx.Create(device).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("创建设备失败: %w", err)
	}

	// 设置网络设备的DeviceID并创建
	networkDevice.DeviceID = device.ID
	if err := tx.Create(networkDevice).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("创建网络设备失败: %w", err)
	}

	// 提交事务
	return tx.Commit().Error
}

// UpdateWithDevice 更新网络设备(包含设备基本信息和资源信息)
func (s *networkDeviceService) UpdateWithDevice(ctx context.Context, id uint, device *asset.Device, networkDevice *asset.NetworkDevice, resource *asset.Resource) error {
	// 验证网络设备是否存在
	existingNetworkDevice, err := s.networkDeviceRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("获取网络设备信息失败: %w", err)
	}
	networkDevice.ID = id
	networkDevice.DeviceID = existingNetworkDevice.DeviceID

	// 验证设备是否存在
	existingDevice, err := s.deviceRepo.GetByID(ctx, existingNetworkDevice.DeviceID)
	if err != nil {
		return fmt.Errorf("获取设备信息失败: %w", err)
	}

	// 设置设备ID
	device.ID = existingDevice.ID

	// 验证网络设备角色是否有效
	if !asset.IsValidNetworkRole(networkDevice.Role) {
		return errors.New("无效的网络设备角色")
	}

	// 用于记录需要在创建资源时忽略的字段
	var omitFields []string

	// 检查资源是否存在
	if resource != nil {
		// 处理 cabinetID 和 roomID 为 0 的情况，避免外键错误
		if resource.CabinetID == 0 {
			// 标记需要忽略的字段
			omitFields = append(omitFields, "cabinet_id")
		}

		if resource.RoomID == 0 {
			// 标记需要忽略的字段
			omitFields = append(omitFields, "room_id")
		}

		existingResource, err := s.resourceRepo.GetByAssetID(ctx, device.ID)
		if err != nil {
			// 如果资源不存在且提供了资源信息，则创建新资源
			if resource.BizStatus != "" {
				resource.AssetID = device.ID
				resource.SN = device.SN // 确保SN一致
			} else {
				// 没有提供有效的业务状态，忽略资源更新
				resource = nil
			}
		} else {
			// 资源存在，设置ID以便更新
			resource.ID = existingResource.ID
			resource.AssetID = device.ID
		}

		// 验证业务状态是否有效
		if resource != nil && resource.BizStatus != "" && !asset.IsValidBizStatus(resource.BizStatus) {
			return errors.New("无效的业务状态")
		}
	}

	// 开启事务
	tx := s.deviceRepo.GetDB().Begin()

	// 确定要更新的设备字段
	updateFields := []string{
		"sn", "brand", "model", "asset_type", "asset_status",
		"hardware_status", "purchase_order", "purchase_date",
		"warranty_expire", "price", "residual_value", "remark",
	}

	// 模板ID处理 - 只有模板ID大于0时才更新该字段
	// 如果传递了无效模板ID，避免进行更新
	if device.TemplateID > 0 {
		// 检查模板是否存在
		templateObj, err := s.machineTemplateRepo.GetByID(ctx, device.TemplateID)
		if err == nil && templateObj != nil {
			// 模板存在，可以更新
			updateFields = append(updateFields, "template_id")
		} else {
			// 模板不存在，记录信息但不中断
			fmt.Printf("不更新模板ID %d，因为找不到对应的模板\n", device.TemplateID)
		}
	} else {
		// 模板ID为0，不更新该字段
		fmt.Println("模板ID为0，不更新模板ID字段")
	}

	// 更新设备信息 - 使用安全的字段列表
	if err := tx.Model(existingDevice).Select(updateFields).Updates(device).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("更新设备信息失败: %w", err)
	}

	// 更新网络设备信息 - 明确字段列表
	networkFields := []string{
		"role", "firmware_version", "loopback_address", "management_address",
		"ports", "port_speed", "stack_support", "stack_id", "stack_role",
		"layer", "routing_protocols",
	}
	if err := tx.Model(existingNetworkDevice).Select(networkFields).Updates(networkDevice).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("更新网络设备信息失败: %w", err)
	}

	// 更新资源信息(如果有)
	if resource != nil {
		existingResource, err := s.resourceRepo.GetByAssetID(ctx, device.ID)
		if err == nil && existingResource != nil {
			// 资源存在，更新资源信息
			resourceFields := []string{
				"project", "rack_position", "height",
				"biz_status", "res_status", "bmc_ip", "vpc_ip",
				"tenant_ip", "cluster", "is_backup", "remark",
				"racking_time", "delivery_time",
			}

			// 只有当 cabinetID > 0 时才更新这个字段
			if resource.CabinetID > 0 {
				resourceFields = append(resourceFields, "cabinet_id")
			}

			// 只有当 roomID > 0 时才更新这个字段
			if resource.RoomID > 0 {
				resourceFields = append(resourceFields, "room_id")
			}

			if err := tx.Model(existingResource).Select(resourceFields).Updates(resource).Error; err != nil {
				tx.Rollback()
				return fmt.Errorf("更新资源信息失败: %w", err)
			}
		} else {
			// 如果资源不存在，创建新资源

			// 使用之前构建的omitFields来排除需要忽略的字段
			if len(omitFields) > 0 {
				if err := tx.Omit(omitFields...).Create(resource).Error; err != nil {
					tx.Rollback()
					return fmt.Errorf("创建资源信息失败: %w", err)
				}
			} else {
				if err := tx.Create(resource).Error; err != nil {
					tx.Rollback()
					return fmt.Errorf("创建资源信息失败: %w", err)
				}
			}
		}
	}

	// 提交事务
	return tx.Commit().Error
}

// GetTemplateByName 根据模板名称获取模板
func (s *networkDeviceService) GetTemplateByName(ctx context.Context, name string) (*template.MachineTemplate, error) {
	return s.machineTemplateRepo.GetByName(ctx, name)
}
