package asset

import (
	"backend/internal/common/constants"
	"backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/cmdb/model/inventory"
	assetRepo "backend/internal/modules/cmdb/repository/asset"
	productRepo "backend/internal/modules/cmdb/repository/product"
	inventorySvc "backend/internal/modules/cmdb/service/inventory"
	"context"
	"fmt"
)

// SpareService 备件服务接口
type SpareService interface {
	// 基本CRUD操作
	CreateSpare(ctx context.Context, spare *asset.AssetSpare) error
	UpdateSpare(ctx context.Context, spare *asset.AssetSpare) error
	UpdateSpares(ctx context.Context, spares []asset.AssetSpare) error
	DeleteSpare(ctx context.Context, id uint) error
	GetSpareByID(ctx context.Context, id uint) (*asset.AssetSpare, error)
	GetSpareBySN(ctx context.Context, sn string) (*asset.AssetSpare, error)
	GetSpareBySNs(ctx context.Context, SNs []string) ([]asset.AssetSpare, error)
	ListSpares(ctx context.Context, page, pageSize int, params map[string]interface{}) ([]*asset.AssetSpare, int64, error)

	// 特定业务接口
	ChangeSpareStatus(ctx context.Context, id uint, status, reason string) error
	GetSpareWithDetails(ctx context.Context, id uint) (*asset.AssetSpareWithDetails, error)
	GetSpareStatistics(ctx context.Context, spareType string) (*asset.SpareStatistics, error)

	// 备件与设备关联/分离
	AssignSpareToDevice(ctx context.Context, spareID, deviceID uint, position string) error
	RemoveSpareFromDevice(ctx context.Context, spareID uint) error

	// 备件库存相关
	TransferWarehouse(ctx context.Context, spareID, warehouseID uint, newLocation string) error
	ListSparesByWarehouse(ctx context.Context, warehouseID uint, page, pageSize int) ([]*asset.AssetSpare, int64, error)
	ListSparesByProduct(ctx context.Context, productID uint, page, pageSize int) ([]*asset.AssetSpare, int64, error)

	// 服务器组件相关备件查询
	ListSparesByComponentType(ctx context.Context, componentType string, page, pageSize int) ([]*asset.AssetSpareWithDetails, int64, error)
	ListSparesByProductID(ctx context.Context, productID uint, page, pageSize int) ([]*asset.AssetSpareWithDetails, int64, error)

	// 校验库存与备件状态并同步
	SyncInventoryWithSpareStatus(ctx context.Context) error
}

// spareService 备件服务实现
type spareService struct {
	repo             assetRepo.SpareRepository
	inventoryService inventorySvc.InventoryService
	deviceSvc        DeviceService
}

// NewSpareService 创建备件服务
func NewSpareService(repo assetRepo.SpareRepository, inventoryService inventorySvc.InventoryService, deviceSvc DeviceService) SpareService {
	return &spareService{
		repo:             repo,
		inventoryService: inventoryService,
		deviceSvc:        deviceSvc,
	}
}

// CreateSpare 创建备件
func (s *spareService) CreateSpare(ctx context.Context, spare *asset.AssetSpare) error {
	// 如果备件状态为可用（闲置或已入库），并且有产品和仓库信息，调整对应的库存数量
	if isAvailableStatus(spare.AssetStatus) && spare.ProductID > 0 && spare.WarehouseID > 0 {
		// 获取状态的中文名称
		statusCN := getStatusChineseName(spare.AssetStatus)

		// 查找产品和仓库匹配的库存记录
		inventoryDetail, err := s.inventoryService.GetByProductAndWarehouse(ctx, spare.ProductID, spare.WarehouseID)
		if err == nil && inventoryDetail != nil {
			// 找到匹配的库存记录，增加库存数量，记录SN
			reason := fmt.Sprintf("新备件[SN:%s]加入库存，状态为[%s]", spare.SN, statusCN)
			if err := s.inventoryService.AdjustStock(ctx, inventoryDetail.ID, 1, constants.ChangeTypeAdjust, reason, 0, 0); err != nil {
				// 记录错误但继续执行，因为这不应该阻止备件创建
				fmt.Printf("调整库存失败: %v，备件SN: %s, 库存ID: %d\n", err, spare.SN, inventoryDetail.ID)
			}
			// 保存批次号信息
			spare.BatchNumber = inventoryDetail.BatchNumber
		} else {
			// 如果找不到匹配的库存记录，但有产品信息，尝试使用产品对应的库存
			details, _, err := s.inventoryService.ListInventory(ctx, 1, 1, spare.ProductID, "", nil)
			if err == nil && len(details) > 0 {
				// 找到产品对应的库存记录
				reason := fmt.Sprintf("新备件[SN:%s]加入库存(产品匹配)，状态为[%s]", spare.SN, statusCN)
				if err := s.inventoryService.AdjustStock(ctx, details[0].ID, 1, constants.ChangeTypeAdjust, reason, 0, 0); err != nil {
					// 记录错误但继续执行
					fmt.Printf("调整库存失败: %v，备件SN: %s, 库存ID: %d\n", err, spare.SN, details[0].ID)
				}
				spare.BatchNumber = details[0].BatchNumber
			}
		}
	}

	// 执行创建操作
	return s.repo.Create(ctx, spare)
}

// UpdateSpare 更新备件
func (s *spareService) UpdateSpare(ctx context.Context, spare *asset.AssetSpare) error {
	// 获取原始备件信息
	oldSpare, err := s.GetSpareByID(ctx, spare.ID)
	if err != nil {
		return err
	}

	// 获取操作人信息（仅记录日志用）
	_, operatorName := getOperatorInfo(ctx)
	fmt.Printf("备件[ID:%d, SN:%s]更新操作，操作人: %s\n", spare.ID, spare.SN, operatorName)

	ctx = context.WithValue(ctx, constants.ContextKeyUserID, ctx.Value("userID").(uint))
	ctx = context.WithValue(ctx, constants.ContextKeyUserInfo, ctx.Value("userInfo").(map[string]interface{}))
	// 检测状态变更
	if oldSpare.AssetStatus != spare.AssetStatus {
		var oldInventory, newInventory *inventory.InventoryDetail

		// 获取原备件对应的库存
		if oldSpare.ProductID > 0 && oldSpare.WarehouseID > 0 {
			oldInventory, err = s.inventoryService.GetByProductAndWarehouse(ctx, oldSpare.ProductID, oldSpare.WarehouseID)
			if err != nil {
				return err
			}
		}

		// 获取新备件对应的库存
		if spare.ProductID > 0 && spare.WarehouseID > 0 {
			newInventory, err = s.inventoryService.GetByProductAndWarehouse(ctx, spare.ProductID, spare.WarehouseID)
			if err != nil {
				return err
			}
		}

		// 获取状态的中文名称
		oldStatusCN := getStatusChineseName(oldSpare.AssetStatus)
		newStatusCN := getStatusChineseName(spare.AssetStatus)

		// 处理库存变更 - 根据状态变更的类型进行不同处理，避免重复调整库存

		// 1. 处理报废相关状态变更（优先处理，避免与其他状态变更逻辑重叠）
		if isScrapStatus(oldSpare.AssetStatus) && !isScrapStatus(spare.AssetStatus) {
			// 从报废状态恢复为正常状态：增加总库存
			if newInventory != nil {
				reason := fmt.Sprintf("备件[SN:%s]状态从[%s]变更为[%s]，加入总库存",
					spare.SN, oldStatusCN, newStatusCN)
				err := s.inventoryService.AdjustStock(ctx, newInventory.ID, 1, constants.ChangeTypeAdjust, reason, 0, 0)
				if err != nil {
					return err
				}

				// 如果新状态是已分配状态，需要同时增加已分配库存
				if isAllocatedStatus(spare.AssetStatus) {
					allocateReason := fmt.Sprintf("备件[SN:%s]状态从[%s]变更为[%s]，增加已分配库存",
						spare.SN, oldStatusCN, newStatusCN)
					err := s.inventoryService.AllocateStock(ctx, newInventory.ID, 1, allocateReason)
					if err != nil {
						fmt.Printf("%s - 失败: %v\n", allocateReason, err)
					}
				}
			}
		} else if !isScrapStatus(oldSpare.AssetStatus) && isScrapStatus(spare.AssetStatus) {
			// 从正常状态变为报废相关状态：从总库存中减少
			if oldInventory != nil {
				reason := fmt.Sprintf("备件[SN:%s]状态从[%s]变更为[%s]，移出总库存",
					spare.SN, oldStatusCN, newStatusCN)
				err := s.inventoryService.AdjustStock(ctx, oldInventory.ID, -1, constants.ChangeTypeAdjust, reason, 0, 0)
				if err != nil {
					return err
				}

				// 如果原状态是已分配状态，需要同时减少已分配库存
				if isAllocatedStatus(oldSpare.AssetStatus) {
					releaseReason := fmt.Sprintf("备件[SN:%s]状态从[%s]变更为[%s]，减少已分配库存",
						spare.SN, oldStatusCN, newStatusCN)
					err := s.inventoryService.ReleaseAllocatedStock(ctx, oldInventory.ID, 1)
					if err != nil {
						fmt.Printf("%s - 失败: %v\n", releaseReason, err)
					}
				}
			}
		} else {
			// 2. 处理非报废状态之间的变更

			// 2.1 处理可用状态变更
			if !isAvailableStatus(oldSpare.AssetStatus) && isAvailableStatus(spare.AssetStatus) {
				// 从非可用状态变为可用状态：增加新库存
				if newInventory != nil {
					reason := fmt.Sprintf("备件[SN:%s]状态从[%s]变更为[%s]，库存增加",
						spare.SN, oldStatusCN, newStatusCN)
					err := s.inventoryService.AdjustStock(ctx, newInventory.ID, 1, constants.ChangeTypeAdjust, reason, 0, 0)
					if err != nil {
						return err
					}
				}
			} else if isAvailableStatus(oldSpare.AssetStatus) && !isAvailableStatus(spare.AssetStatus) {
				// 从可用状态变为非可用状态：减少原库存
				if oldInventory != nil {
					reason := fmt.Sprintf("备件[SN:%s]状态从[%s]变更为[%s]，库存减少",
						spare.SN, oldStatusCN, newStatusCN)
					err := s.inventoryService.AdjustStock(ctx, oldInventory.ID, -1, constants.ChangeTypeAdjust, reason, 0, 0)
					if err != nil {
						return err
					}
				}
			}

			// 2.2 处理已分配状态变更
			if !isAllocatedStatus(oldSpare.AssetStatus) && isAllocatedStatus(spare.AssetStatus) {
				// 从非已分配状态变为已分配状态：增加已分配库存
				if newInventory != nil {
					reason := fmt.Sprintf("备件[SN:%s]状态从[%s]变更为[%s]，已分配库存增加",
						spare.SN, oldStatusCN, newStatusCN)
					err := s.inventoryService.AllocateStock(ctx, newInventory.ID, 1, reason)
					if err != nil {
						return err
					}
				}
			} else if isAllocatedStatus(oldSpare.AssetStatus) && !isAllocatedStatus(spare.AssetStatus) {
				// 从已分配状态变为非已分配状态：减少已分配库存
				if oldInventory != nil {
					releaseReason := fmt.Sprintf("备件[SN:%s]状态从[%s]变更为[%s]，已分配库存减少",
						spare.SN, oldStatusCN, newStatusCN)
					err := s.inventoryService.ReleaseAllocatedStock(ctx, oldInventory.ID, 1)
					if err != nil {
						fmt.Printf("%s - 失败: %v\n", releaseReason, err)
					}
				}
			} else if isAllocatedStatus(oldSpare.AssetStatus) && isAllocatedStatus(spare.AssetStatus) && oldSpare.AssetStatus != spare.AssetStatus {
				// 已分配状态之间的变更（已分配库存数量不变，只需记录变更）
				if oldInventory != nil {
					fmt.Printf("备件[SN:%s]状态从[%s]变更为[%s]，已分配库存状态变更\n",
						spare.SN, oldStatusCN, newStatusCN)
				}
			}
		}
	}

	// 检测仓库或产品变更（当备件为可用状态时）
	if isAvailableStatus(spare.AssetStatus) &&
		(oldSpare.ProductID != spare.ProductID || oldSpare.WarehouseID != spare.WarehouseID) {
		// 获取原库存
		oldInventory, err := s.inventoryService.GetByProductAndWarehouse(ctx, oldSpare.ProductID, oldSpare.WarehouseID)
		if err != nil {
			return err
		}
		// 获取新库存
		newInventory, err := s.inventoryService.GetByProductAndWarehouse(ctx, spare.ProductID, spare.WarehouseID)
		if err != nil {
			return err
		}

		// 获取原仓库信息
		var oldWarehouseName, newWarehouseName string
		if oldSpare.WarehouseID > 0 {
			oldWarehouse, err := assetRepo.NewWarehouseRepository(s.repo.GetDB()).GetByID(ctx, oldSpare.WarehouseID)
			if err == nil && oldWarehouse != nil {
				oldWarehouseName = oldWarehouse.Name
			} else {
				oldWarehouseName = fmt.Sprintf("ID:%d", oldSpare.WarehouseID)
			}
		} else {
			oldWarehouseName = "未知仓库"
		}

		// 获取新仓库信息
		if spare.WarehouseID > 0 {
			newWarehouse, err := assetRepo.NewWarehouseRepository(s.repo.GetDB()).GetByID(ctx, spare.WarehouseID)
			if err == nil && newWarehouse != nil {
				newWarehouseName = newWarehouse.Name
			} else {
				newWarehouseName = fmt.Sprintf("ID:%d", spare.WarehouseID)
			}
		} else {
			newWarehouseName = "未知仓库"
		}

		// 从原库存减少
		if oldInventory != nil {
			reason := fmt.Sprintf("备件[SN:%s]从仓库[%s]迁移到仓库[%s]",
				spare.SN, oldWarehouseName, newWarehouseName)
			err := s.inventoryService.AdjustStock(ctx, oldInventory.ID, -1, constants.ChangeTypeAdjust, reason, 0, 0)
			if err != nil {
				return err
			}
		}

		// 增加新库存
		if newInventory != nil {
			reason := fmt.Sprintf("备件[SN:%s]从仓库[%s]迁入仓库[%s]",
				spare.SN, oldWarehouseName, newWarehouseName)
			err := s.inventoryService.AdjustStock(ctx, newInventory.ID, 1, constants.ChangeTypeAdjust, reason, 0, 0)
			if err != nil {
				return err
			}
			// 同步批次号
			spare.BatchNumber = newInventory.BatchNumber
		}
	}

	// 执行更新操作
	return s.repo.Update(ctx, spare)
}

// UpdateSpares 更新备件
func (s *spareService) UpdateSpares(ctx context.Context, spare []asset.AssetSpare) error {
	return s.repo.UpdateSpares(ctx, spare)
}

// DeleteSpare 删除备件
func (s *spareService) DeleteSpare(ctx context.Context, id uint) error {
	// 获取备件信息
	spare, err := s.GetSpareByID(ctx, id)
	if err != nil {
		return err
	}

	// 如果备件状态为可用且有产品和仓库信息，减少对应的库存
	if isAvailableStatus(spare.AssetStatus) && spare.ProductID > 0 && spare.WarehouseID > 0 {
		// 查找对应的库存记录
		inventoryDetail, err := s.inventoryService.GetByProductAndWarehouse(ctx, spare.ProductID, spare.WarehouseID)
		if err == nil && inventoryDetail != nil {
			// 调整库存
			reason := fmt.Sprintf("备件[SN:%s]被删除", spare.SN)
			err := s.inventoryService.AdjustStock(ctx, inventoryDetail.ID, -1, constants.ChangeTypeAdjust, reason, 0, 0)
			if err != nil {
				// 记录错误但继续执行，因为备件已经被删除，不希望库存调整失败影响整个流程
				fmt.Printf("调整库存失败: %v，备件ID: %d, 库存ID: %d\n", err, id, inventoryDetail.ID)
			}
		}
	}

	// 执行删除操作
	return s.repo.Delete(ctx, id)
}

// GetSpareByID 根据ID获取备件
func (s *spareService) GetSpareByID(ctx context.Context, id uint) (*asset.AssetSpare, error) {
	return s.repo.GetByID(ctx, id)
}

// GetSpareBySN 根据SN获取备件
func (s *spareService) GetSpareBySN(ctx context.Context, sn string) (*asset.AssetSpare, error) {
	return s.repo.GetBySN(ctx, sn)
}

func (s *spareService) GetSpareBySNs(ctx context.Context, SNs []string) ([]asset.AssetSpare, error) {
	return s.repo.GetBySNs(ctx, SNs)
}

// ListSpares 获取备件列表
func (s *spareService) ListSpares(ctx context.Context, page, pageSize int, params map[string]interface{}) ([]*asset.AssetSpare, int64, error) {
	return s.repo.List(ctx, page, pageSize, params)
}

// ChangeSpareStatus 更改备件状态
func (s *spareService) ChangeSpareStatus(ctx context.Context, id uint, status, reason string) error {
	// 先获取备件信息
	spare, err := s.GetSpareByID(ctx, id)
	if err != nil {
		return err
	}

	// 获取操作人信息（仅记录日志用）
	_, operatorName := getOperatorInfo(ctx)
	fmt.Printf("备件[ID:%d, SN:%s]状态变更，操作人: %s\n", id, spare.SN, operatorName)

	// 记录原始状态
	oldStatus := spare.AssetStatus

	// 获取状态的中文名称
	oldStatusCN := getStatusChineseName(oldStatus)
	newStatusCN := getStatusChineseName(status)

	// 调用仓库层修改状态
	if err := s.repo.ChangeStatus(ctx, id, status, reason); err != nil {
		return err
	}

	// 根据状态变更调整库存（如果有产品和仓库信息）
	if spare.ProductID > 0 && spare.WarehouseID > 0 {
		// 查找对应的库存记录
		inventoryDetail, err := s.inventoryService.GetByProductAndWarehouse(ctx, spare.ProductID, spare.WarehouseID)
		if err == nil && inventoryDetail != nil {
			// 处理库存变更 - 根据状态变更的类型进行不同处理，避免重复调整库存

			// 1. 处理报废相关状态变更（优先处理，避免与其他状态变更逻辑重叠）
			if isScrapStatus(oldStatus) && !isScrapStatus(status) {
				// 从报废状态恢复为正常状态：增加总库存
				unscrapReason := fmt.Sprintf("备件[SN:%s]状态从[%s]变更为[%s]，加入总库存。原因: %s",
					spare.SN, oldStatusCN, newStatusCN, reason)
				err := s.inventoryService.AdjustStock(ctx, inventoryDetail.ID, 1, constants.ChangeTypeAdjust, unscrapReason, 0, 0)
				if err != nil {
					fmt.Printf("调整库存失败: %v，备件ID: %d, 库存ID: %d\n", err, id, inventoryDetail.ID)
				}

				// 如果新状态是已分配状态，需要同时增加已分配库存
				if isAllocatedStatus(status) {
					allocateReason := fmt.Sprintf("备件[SN:%s]状态从[%s]变更为[%s]，增加已分配库存。原因: %s",
						spare.SN, oldStatusCN, newStatusCN, reason)
					if err := s.inventoryService.AllocateStock(ctx, inventoryDetail.ID, 1, allocateReason); err != nil {
						fmt.Printf("调整已分配库存失败: %v，备件ID: %d, 库存ID: %d\n", err, id, inventoryDetail.ID)
					}
				}
			} else if !isScrapStatus(oldStatus) && isScrapStatus(status) {
				// 从正常状态变为报废状态：减少总库存
				scrapReason := fmt.Sprintf("备件[SN:%s]状态从[%s]变更为[%s]，移出总库存。原因: %s",
					spare.SN, oldStatusCN, newStatusCN, reason)
				err := s.inventoryService.AdjustStock(ctx, inventoryDetail.ID, -1, constants.ChangeTypeAdjust, scrapReason, 0, 0)
				if err != nil {
					fmt.Printf("调整库存失败: %v，备件ID: %d, 库存ID: %d\n", err, id, inventoryDetail.ID)
				}

				// 如果原状态是已分配状态，需要同时减少已分配库存
				if isAllocatedStatus(oldStatus) {
					releaseReason := fmt.Sprintf("备件[SN:%s]状态从[%s]变更为[%s]，减少已分配库存。原因: %s",
						spare.SN, oldStatusCN, newStatusCN, reason)
					if err := s.inventoryService.ReleaseAllocatedStock(ctx, inventoryDetail.ID, 1); err != nil {
						fmt.Printf("%s - 失败: %v，备件ID: %d, 库存ID: %d\n", releaseReason, err, id, inventoryDetail.ID)
					}
				}
			} else {
				// 2. 处理非报废状态之间的变更

				// 2.1 处理可用状态变更
				var changeAmount = 0

				// 根据状态变化决定库存变更类型和数量
				if !isAvailableStatus(oldStatus) && isAvailableStatus(status) {
					// 从非可用状态变为可用状态
					changeAmount = 1
				} else if isAvailableStatus(oldStatus) && !isAvailableStatus(status) {
					// 从可用状态变为非可用状态
					changeAmount = -1
				}

				// 只有在需要调整库存时才调用接口
				if changeAmount != 0 {
					adjustReason := fmt.Sprintf("备件[SN:%s]状态从[%s]变更为[%s]。原因: %s",
						spare.SN, oldStatusCN, newStatusCN, reason)
					err := s.inventoryService.AdjustStock(ctx, inventoryDetail.ID, changeAmount, constants.ChangeTypeAdjust, adjustReason, 0, 0)
					if err != nil {
						fmt.Printf("调整库存失败: %v，备件ID: %d, 库存ID: %d\n", err, id, inventoryDetail.ID)
					}
				}

				// 2.2 处理已分配状态变更
				if !isAllocatedStatus(oldStatus) && isAllocatedStatus(status) {
					// 从非已分配状态变为已分配状态
					allocateReason := fmt.Sprintf("备件[SN:%s]状态从[%s]变更为[%s]，已分配库存增加。原因: %s",
						spare.SN, oldStatusCN, newStatusCN, reason)
					if err := s.inventoryService.AllocateStock(ctx, inventoryDetail.ID, 1, allocateReason); err != nil {
						fmt.Printf("调整已分配库存失败: %v，备件ID: %d, 库存ID: %d\n", err, id, inventoryDetail.ID)
					}
				} else if isAllocatedStatus(oldStatus) && !isAllocatedStatus(status) {
					// 从已分配状态变为非已分配状态
					releaseReason := fmt.Sprintf("备件[SN:%s]状态从[%s]变更为[%s]，已分配库存减少。原因: %s",
						spare.SN, oldStatusCN, newStatusCN, reason)
					if err := s.inventoryService.ReleaseAllocatedStock(ctx, inventoryDetail.ID, 1); err != nil {
						fmt.Printf("%s - 失败: %v，备件ID: %d, 库存ID: %d\n", releaseReason, err, id, inventoryDetail.ID)
					}
				} else if isAllocatedStatus(oldStatus) && isAllocatedStatus(status) && oldStatus != status {
					// 已分配状态之间的变更
					statusChangeReason := fmt.Sprintf("备件[SN:%s]状态从[%s]变更为[%s]，已分配库存状态变更。原因: %s",
						spare.SN, oldStatusCN, newStatusCN, reason)
					fmt.Printf("%s\n", statusChangeReason)
				}
			}
		}
	}

	return nil
}

// GetSpareWithDetails 获取备件详情
func (s *spareService) GetSpareWithDetails(ctx context.Context, id uint) (*asset.AssetSpareWithDetails, error) {
	spareWithDetails, err := s.repo.GetSpareWithDetails(ctx, id)
	if err != nil {
		return nil, err
	}

	// 如果有产品ID和仓库ID，通过它们获取库存信息
	if spareWithDetails.ProductID > 0 && spareWithDetails.WarehouseID > 0 {
		// 尝试直接通过产品ID和仓库ID获取对应的库存明细
		inventoryDetail, err := s.inventoryService.GetByProductAndWarehouse(ctx, spareWithDetails.ProductID, spareWithDetails.WarehouseID)
		if err == nil && inventoryDetail != nil {
			// 找到了对应的库存明细，直接使用
			spareWithDetails.InventoryDetail = *inventoryDetail
		} else {
			// 如果找不到直接对应的库存明细，则使用产品级别的库存汇总信息
			inventorySummary, err := s.inventoryService.GetProductInventorySummary(ctx, spareWithDetails.ProductID)
			if err == nil && inventorySummary != nil {
				spareWithDetails.InventoryDetail = inventory.InventoryDetail{
					ProductID:      spareWithDetails.ProductID,
					WarehouseID:    spareWithDetails.WarehouseID,
					CurrentStock:   inventorySummary.TotalStock,
					AllocatedStock: inventorySummary.AllocatedStock,
					AvailableStock: inventorySummary.AvailableStock,
				}
			}
		}
	}

	return spareWithDetails, nil
}

// GetSpareStatistics 获取备件统计信息
func (s *spareService) GetSpareStatistics(ctx context.Context, spareType string) (*asset.SpareStatistics, error) {
	return s.repo.GetSpareStatistics(ctx, spareType)
}

// AssignSpareToDevice 分配备件到设备
func (s *spareService) AssignSpareToDevice(ctx context.Context, spareID, deviceID uint, position string) error {
	return s.repo.AssignToDevice(ctx, spareID, deviceID, position)
}

// RemoveSpareFromDevice 从设备移除备件
func (s *spareService) RemoveSpareFromDevice(ctx context.Context, spareID uint) error {
	return s.repo.RemoveFromDevice(ctx, spareID)
}

// TransferWarehouse 移动备件到其他仓库
func (s *spareService) TransferWarehouse(ctx context.Context, spareID, warehouseID uint, newLocation string) error {
	return s.repo.TransferWarehouse(ctx, spareID, warehouseID, newLocation)
}

// ListSparesByWarehouse 根据仓库ID获取备件列表
func (s *spareService) ListSparesByWarehouse(ctx context.Context, warehouseID uint, page, pageSize int) ([]*asset.AssetSpare, int64, error) {
	return s.repo.ListByWarehouse(ctx, warehouseID, page, pageSize)
}

// ListSparesByProduct 根据产品ID获取备件列表
func (s *spareService) ListSparesByProduct(ctx context.Context, productID uint, page, pageSize int) ([]*asset.AssetSpare, int64, error) {
	return s.repo.ListByProduct(ctx, productID, page, pageSize)
}

// ListSparesByComponentType 根据组件类型获取可用备件列表
func (s *spareService) ListSparesByComponentType(ctx context.Context, componentType string, page, pageSize int) ([]*asset.AssetSpareWithDetails, int64, error) {
	// 查询指定类型的备件
	params := map[string]interface{}{
		"type":         componentType,
		"asset_status": "idle",
	}
	spares, total, err := s.repo.List(ctx, page, pageSize, params)
	if err != nil {
		return nil, 0, err
	}

	// 转换为带详细信息的备件列表
	sparesWithDetails := make([]*asset.AssetSpareWithDetails, 0, len(spares))
	for _, spare := range spares {
		spareWithDetails, err := s.GetSpareWithDetails(ctx, spare.ID)
		if err != nil {
			continue // 如果某个详情获取失败，继续处理下一个
		}
		sparesWithDetails = append(sparesWithDetails, spareWithDetails)
	}

	return sparesWithDetails, total, nil
}

// ListSparesByProductID 根据产品ID获取详细备件列表
func (s *spareService) ListSparesByProductID(ctx context.Context, productID uint, page, pageSize int) ([]*asset.AssetSpareWithDetails, int64, error) {
	// 查询指定产品ID的备件
	params := map[string]interface{}{
		"product_id": productID,
	}
	spares, total, err := s.repo.List(ctx, page, pageSize, params)
	if err != nil {
		return nil, 0, err
	}

	// 转换为带详细信息的备件列表
	sparesWithDetails := make([]*asset.AssetSpareWithDetails, 0, len(spares))
	for _, spare := range spares {
		spareWithDetails, err := s.GetSpareWithDetails(ctx, spare.ID)
		if err != nil {
			continue // 如果某个详情获取失败，继续处理下一个
		}
		sparesWithDetails = append(sparesWithDetails, spareWithDetails)
	}

	return sparesWithDetails, total, nil
}

// SyncInventoryWithSpareStatus 校验库存与备件状态并同步
func (s *spareService) SyncInventoryWithSpareStatus(ctx context.Context) error {
	// 创建可返回的结果结构
	result := map[string]interface{}{
		"total_spares":    0,
		"processed":       0,
		"fixed":           0,
		"inconsistencies": []map[string]interface{}{},
	}

	inconsistencies := []map[string]interface{}{}
	fixedCount := 0

	// 获取数据库连接
	db := s.repo.GetDB()

	// 获取所有产品
	prodRepo := productRepo.NewProductRepository(db)
	prodFilter := productRepo.ProductFilter{}
	prodPagination := productRepo.PaginationOptions{Page: 1, PageSize: 1000}
	allProducts, _, err := prodRepo.List(ctx, prodFilter, prodPagination)
	if err != nil {
		return fmt.Errorf("获取所有产品失败: %w", err)
	}

	// 获取所有仓库
	warehouseRepo := assetRepo.NewWarehouseRepository(db)
	allWarehouses, _, err := warehouseRepo.List(ctx, 1, 1000, "", "")
	if err != nil {
		return fmt.Errorf("获取所有仓库失败: %w", err)
	}

	// 创建产品-仓库映射来跟踪已处理的组合
	processedPairs := make(map[string]bool)

	// 分页处理所有备件
	page := 1
	pageSize := 100
	var total int64

	for {
		// 查询一页备件
		params := map[string]interface{}{}
		spares, totalSpares, err := s.repo.List(ctx, page, pageSize, params)
		if err != nil {
			return fmt.Errorf("查询备件失败: %w", err)
		}

		devices, totalDevices, err := s.deviceSvc.List(ctx, page, pageSize, "", "", "")
		if err != nil {
			return err
		}

		total = totalSpares + totalDevices

		if len(spares) == 0 && len(devices) == 0 {
			break
		}

		// 处理当前页的备件
		for _, spare := range spares {
			// 只处理有产品ID和仓库ID的备件
			if spare.ProductID > 0 && spare.WarehouseID > 0 {
				// 创建唯一键以跟踪已同步的产品-仓库组合
				key := fmt.Sprintf("%d-%d", spare.ProductID, spare.WarehouseID)

				// 如果这个组合已经处理过，跳过
				if processedPairs[key] {
					continue
				}

				// 标记为已处理
				processedPairs[key] = true

				// 使用库存服务同步这个产品-仓库组合的库存
				err := s.inventoryService.SyncInventoryWithSpareStatus(ctx, spare.ProductID, spare.WarehouseID)
				if err != nil {
					// 记录错误但继续处理下一个组合
					inconsistency := map[string]interface{}{
						"product_id":    spare.ProductID,
						"warehouse_id":  spare.WarehouseID,
						"spare_id":      spare.ID,
						"error_message": err.Error(),
						"sync_status":   "失败",
					}
					inconsistencies = append(inconsistencies, inconsistency)
				} else {
					fixedCount++
				}
			}
		}

		for _, device := range devices {
			// 只处理有产品ID的设备
			if device.ProductID > 0 && device.Resource.RoomID > 0 {
				warehouse, err := s.deviceSvc.GetDeviceWarehouse(ctx, device.ID)
				if err != nil {
					return err
				}
				if warehouse == nil {
					break
				}
				// 创建唯一键以跟踪已同步的产品-仓库组合
				key := fmt.Sprintf("%d-%d", device.ProductID, warehouse.ID)

				// 如果这个组合已经处理过，跳过
				if processedPairs[key] {
					continue
				}

				// 标记为已处理
				processedPairs[key] = true
				// 使用库存服务同步这个产品-仓库组合的库存
				err = s.inventoryService.SyncInventoryWithDeviceStatus(ctx, device.ProductID, warehouse.ID)
				if err != nil {
					// 记录错误但继续处理下一个组合
					inconsistency := map[string]interface{}{
						"product_id":    device.ProductID,
						"warehouse_id":  warehouse.ID,
						"device_id":     device.ID,
						"error_message": err.Error(),
						"sync_status":   "失败",
					}
					inconsistencies = append(inconsistencies, inconsistency)
				} else {
					fixedCount++
				}
			}
		}
		// 处理下一页
		page++
		if len(spares) < pageSize && len(devices) < pageSize {
			break
		}
	}

	// 确保每个仓库的每个产品都有库存记录，即使没有备件
	for _, prod := range allProducts {
		for _, warehouse := range allWarehouses {
			// 创建唯一键
			key := fmt.Sprintf("%d-%d", prod.ID, warehouse.ID)

			// 如果已经处理过，跳过
			if processedPairs[key] {
				continue
			}

			// 对于尚未处理的产品-仓库组合，同步库存
			err := s.inventoryService.SyncInventoryWithSpareStatus(ctx, prod.ID, warehouse.ID)
			if err != nil {
				inconsistency := map[string]interface{}{
					"product_id":     prod.ID,
					"warehouse_id":   warehouse.ID,
					"product_model":  prod.Model,
					"warehouse_name": warehouse.Name,
					"error_message":  err.Error(),
					"sync_status":    "失败",
				}
				inconsistencies = append(inconsistencies, inconsistency)
			} else {
				fixedCount++

				// 如果是新创建的库存记录（没有备件的情况），记录这个信息
				inventoryDetail, _ := s.inventoryService.GetByProductAndWarehouse(ctx, prod.ID, warehouse.ID)
				if inventoryDetail != nil && inventoryDetail.CurrentStock == 0 {
					inconsistency := map[string]interface{}{
						"product_id":      prod.ID,
						"product_model":   prod.Model,
						"warehouse_id":    warehouse.ID,
						"warehouse_name":  warehouse.Name,
						"inventory_issue": "缺少库存记录",
						"action":          "已创建空库存记录",
					}
					inconsistencies = append(inconsistencies, inconsistency)
				}
			}
		}
	}

	result["total_spares"] = total
	result["processed"] = len(processedPairs)
	result["fixed"] = fixedCount
	result["inconsistencies"] = inconsistencies

	return nil
}

// 判断备件是否为可用状态的辅助函数
func isAvailableStatus(status string) bool {
	return status == constants.AssetStatusIdle || status == constants.AssetStatusInStock
}

// 判断状态是否为已分配状态的辅助函数
func isAllocatedStatus(status string) bool {
	return status == constants.AssetStatusInUse || status == constants.AssetStatusRepairing || status == constants.AssetStatusPendingOut
}

// 判断状态是否为报废相关状态的辅助函数
func isScrapStatus(status string) bool {
	return status == constants.AssetStatusPendingScrap || status == constants.AssetStatusScrapped
}

// 获取状态的中文名称
func getStatusChineseName(status string) string {
	statusMap := map[string]string{
		constants.AssetStatusPendingIn:    "待入库",
		constants.AssetStatusInStock:      "已入库",
		constants.AssetStatusPendingOut:   "待出库",
		constants.AssetStatusOutStock:     "已出库",
		constants.AssetStatusIdle:         "闲置中",
		constants.AssetStatusInUse:        "使用中",
		constants.AssetStatusRepairing:    "维修中",
		constants.AssetStatusPendingScrap: "待报废",
		constants.AssetStatusScrapped:     "已报废",
	}

	if name, ok := statusMap[status]; ok {
		return name
	}
	return status // 如果没有映射，返回原始状态
}

// 从上下文获取操作人信息
func getOperatorInfo(ctx context.Context) (uint, string) {
	var operatorID uint
	var operatorName string

	// 尝试从上下文获取用户ID - 使用正确的键名"userID"
	if userID, ok := ctx.Value("userID").(uint); ok {
		operatorID = userID
	}

	// 尝试从上下文获取用户信息
	if userInfo, ok := ctx.Value("userInfo").(map[string]interface{}); ok {
		if realName, ok := userInfo["realName"].(string); ok {
			operatorName = realName
		}
	}

	// 如果没有获取到操作人姓名，使用默认值
	if operatorName == "" {
		operatorName = "system"
	}

	return operatorID, operatorName
}
