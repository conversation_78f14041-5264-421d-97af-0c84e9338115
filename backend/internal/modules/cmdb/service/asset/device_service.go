package asset

import (
	"context"
	"fmt"

	"backend/internal/modules/cmdb/model/asset"
	repo "backend/internal/modules/cmdb/repository/asset"
)

// DeviceService 资产设备服务接口
type DeviceService interface {
	// 基本设备操作
	Create(ctx context.Context, device *asset.Device) error
	Update(ctx context.Context, device *asset.Device) error
	UpdateDevices(ctx context.Context, devices []asset.Device) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*asset.Device, error)
	GetBySN(ctx context.Context, sn string) (*asset.Device, error)
	GetIdleBySN(ctx context.Context, sn string) (*asset.Device, error)
	GetDevicesSpare(ctx context.Context, sns []string) (map[string][]asset.AssetSpare, error)
	List(ctx context.Context, page, pageSize int, query, assetStatus, assetType string) ([]*asset.Device, int64, error)

	// 高级设备资源操作
	CreateWithResource(ctx context.Context, device *asset.Device) (*asset.Device, error)
	UpdateWithResource(ctx context.Context, device *asset.Device) (*asset.Device, error)
	DeleteWithResource(ctx context.Context, id uint) error
	GetByIDWithResource(ctx context.Context, id uint) (*asset.Device, error)
	ListWithResource(ctx context.Context, page, pageSize int, query, assetStatus, assetType, bizStatus, project, ip string) ([]*asset.Device, int64, error)
	GetDeviceTemplate(ctx context.Context, deviceID uint, withComponents bool) (interface{}, error)
	GetDeviceWarehouse(ctx context.Context, deviceID uint) (*asset.Warehouse, error)

	// 扩展查询功能
	ListDeviceResourcesExtended(
		ctx context.Context,
		page, pageSize int,
		query, hostname, brand, model string,
		assetStatus, assetType, bizStatus, project, cluster string,
		vpcIP, bmcIP, tenantIP string,
		isBackup *bool,
	) ([]*asset.Device, int64, error)

	// ListBySNs 通过设备sn批量查询，不进行preload
	ListBySNs(ctx context.Context, sns []string) ([]*asset.Device, int64, error)
	ListBySNsV2(ctx context.Context, sns []string, deep uint) ([]asset.Device, int64, error)
	GetDeviceAmount(ctx context.Context, req []asset.DeviceAmountReq) ([]asset.DeviceAmountRes, error)
	ListByIDs(ctx context.Context, ids []uint) ([]*asset.Device, int64, error)
}

// deviceService 设备服务实现
type deviceService struct {
	repo repo.DeviceRepository
}

// NewDeviceService 创建设备服务
func NewDeviceService(repo repo.DeviceRepository) DeviceService {
	return &deviceService{repo: repo}
}

// ==================== 基本设备操作 ====================

// Create 创建资产设备
func (s *deviceService) Create(ctx context.Context, device *asset.Device) error {
	return s.repo.Create(ctx, device)
}

// Update 更新资产设备
func (s *deviceService) Update(ctx context.Context, device *asset.Device) error {
	return s.repo.Update(ctx, device)
}

func (s *deviceService) UpdateDevices(ctx context.Context, devices []asset.Device) error {
	return s.repo.UpdateDevices(ctx, devices)
}

// Delete 删除资产设备
func (s *deviceService) Delete(ctx context.Context, id uint) error {
	return s.repo.Delete(ctx, id)
}

// GetByID 根据ID获取资产设备
func (s *deviceService) GetByID(ctx context.Context, id uint) (*asset.Device, error) {
	return s.repo.GetByID(ctx, id)
}

// GetBySN 根据SN获取资产设备
func (s *deviceService) GetBySN(ctx context.Context, sn string) (*asset.Device, error) {
	return s.repo.GetBySN(ctx, sn)
}

// GetIdleBySN 根据SN获取状态为闲置中的资产设备
func (s *deviceService) GetIdleBySN(ctx context.Context, sn string) (*asset.Device, error) {
	return s.repo.GetIdleBySN(ctx, sn)
}

// List 分页查询资产设备列表
func (s *deviceService) List(ctx context.Context, page, pageSize int, query, assetStatus, assetType string) ([]*asset.Device, int64, error) {
	return s.repo.List(ctx, page, pageSize, query, assetStatus, assetType)
}

// ==================== 高级设备资源操作 ====================

// CreateWithResource 创建资产设备及其资源信息
func (s *deviceService) CreateWithResource(ctx context.Context, device *asset.Device) (*asset.Device, error) {
	return s.repo.CreateWithResource(ctx, device)
}

// UpdateWithResource 更新资产设备及其资源信息
func (s *deviceService) UpdateWithResource(ctx context.Context, device *asset.Device) (*asset.Device, error) {
	return s.repo.UpdateWithResource(ctx, device)
}

// DeleteWithResource 删除资产设备及其资源信息
func (s *deviceService) DeleteWithResource(ctx context.Context, id uint) error {
	return s.repo.DeleteWithResource(ctx, id)
}

// GetByIDWithResource 根据ID获取资产设备及其资源信息
func (s *deviceService) GetByIDWithResource(ctx context.Context, id uint) (*asset.Device, error) {
	return s.repo.GetByIDWithResource(ctx, id)
}

// ListWithResource 查询资产设备列表，包含资源信息及高级筛选条件
func (s *deviceService) ListWithResource(ctx context.Context, page, pageSize int, query, assetStatus, assetType, bizStatus, project, ip string) ([]*asset.Device, int64, error) {
	return s.repo.ListWithResource(ctx, page, pageSize, query, assetStatus, assetType, bizStatus, project, ip)
}

// GetDeviceTemplate 获取设备模板信息
func (s *deviceService) GetDeviceTemplate(ctx context.Context, deviceID uint, withComponents bool) (interface{}, error) {
	return s.repo.GetDeviceTemplate(ctx, deviceID, withComponents)
}

// ListDeviceResourcesExtended 扩展查询功能，支持更多筛选条件
func (s *deviceService) ListDeviceResourcesExtended(
	ctx context.Context,
	page, pageSize int,
	query, hostname, brand, model string,
	assetStatus, assetType, bizStatus, project, cluster string,
	vpcIP, bmcIP, tenantIP string,
	isBackup *bool,
) ([]*asset.Device, int64, error) {
	return s.repo.ListDeviceResourcesExtended(
		ctx,
		page, pageSize,
		query, hostname, brand, model,
		assetStatus, assetType, bizStatus, project, cluster,
		vpcIP, bmcIP, tenantIP,
		isBackup,
	)
}

func (s *deviceService) GetDeviceWarehouse(ctx context.Context, deviceID uint) (*asset.Warehouse, error) {
	return s.repo.GetDeviceWarehouse(ctx, deviceID)
}

// ListBySns 根据SN查询列表，不对内容进行preload
func (s *deviceService) ListBySNs(ctx context.Context, sns []string) ([]*asset.Device, int64, error) {
	return s.repo.ListBySns(ctx, sns)
}

func (s *deviceService) ListBySNsV2(ctx context.Context, sns []string, deep uint) ([]asset.Device, int64, error) {
	return s.repo.ListBySnsV2(ctx, sns, deep)
}

func (s *deviceService) GetDeviceAmount(ctx context.Context, req []asset.DeviceAmountReq) ([]asset.DeviceAmountRes, error) {
	for i, r := range req {
		if r.TemplateID == 0 {
			return nil, fmt.Errorf("第 %d 行模板ID不能为0", i+1)
		}
	}
	return s.repo.GetDeviceAmount(ctx, req)
}

// GetDevicesSpare 获取机器上的配件
func (s *deviceService) GetDevicesSpare(ctx context.Context, sns []string) (map[string][]asset.AssetSpare, error) {
	return s.repo.GetDeviceSpares(ctx, sns)
}

func (s *deviceService) ListByIDs(ctx context.Context, ids []uint) ([]*asset.Device, int64, error) {
	return s.repo.ListByIds(ctx, ids)
}
