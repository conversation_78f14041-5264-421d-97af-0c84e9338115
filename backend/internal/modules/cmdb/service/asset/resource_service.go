package asset

import (
	"backend/internal/modules/cmdb/model/asset"
	repo "backend/internal/modules/cmdb/repository/asset"
	"context"
)

// ResourceService 资源服务接口
type ResourceService interface {
	CreateResource(ctx context.Context, resource *asset.Resource) error
	UpdateResource(ctx context.Context, resource *asset.Resource) error
	DeleteResource(ctx context.Context, id uint) error
	GetResourceByID(ctx context.Context, id uint) (*asset.Resource, error)
	GetResourceBySN(ctx context.Context, sn string) (*asset.Resource, error)
	GetResourceByAssetID(ctx context.Context, assetID uint) (*asset.Resource, error)
	ListResources(ctx context.Context, page, pageSize int, query string, bizStatus, resStatus string) ([]*asset.Resource, int64, error)
	GetAllProjects(ctx context.Context) ([]string, error)
	GetAllClusters(ctx context.Context) ([]string, error)
	GetAvailableBackups(ctx context.Context, project, cluster, hardwareStatus, bizStatus string) ([]*asset.Resource, error)
}

// resourceService 资源服务实现
type resourceService struct {
	repo repo.ResourceRepository
}

// NewResourceService 创建资源服务
func NewResourceService(repo repo.ResourceRepository) ResourceService {
	return &resourceService{repo: repo}
}

// CreateResource 创建资源
func (s *resourceService) CreateResource(ctx context.Context, resource *asset.Resource) error {
	return s.repo.Create(ctx, resource)
}

// UpdateResource 更新资源
func (s *resourceService) UpdateResource(ctx context.Context, resource *asset.Resource) error {
	return s.repo.Update(ctx, resource)
}

// DeleteResource 删除资源
func (s *resourceService) DeleteResource(ctx context.Context, id uint) error {
	return s.repo.Delete(ctx, id)
}

// GetResourceByID 根据ID获取资源
func (s *resourceService) GetResourceByID(ctx context.Context, id uint) (*asset.Resource, error) {
	return s.repo.GetByID(ctx, id)
}

// GetResourceBySN 根据SN获取资源
func (s *resourceService) GetResourceBySN(ctx context.Context, sn string) (*asset.Resource, error) {
	return s.repo.GetBySN(ctx, sn)
}

// GetResourceByAssetID 根据资产ID获取资源
func (s *resourceService) GetResourceByAssetID(ctx context.Context, assetID uint) (*asset.Resource, error) {
	return s.repo.GetByAssetID(ctx, assetID)
}

// ListResources 分页查询资源列表
func (s *resourceService) ListResources(ctx context.Context, page, pageSize int, query string, bizStatus, resStatus string) ([]*asset.Resource, int64, error) {
	return s.repo.List(ctx, page, pageSize, query, bizStatus, resStatus)
}

// GetAllProjects 获取所有唯一的项目列表
func (s *resourceService) GetAllProjects(ctx context.Context) ([]string, error) {
	return s.repo.GetAllProjects(ctx)
}

// GetAllClusters 获取所有唯一的集群列表
func (s *resourceService) GetAllClusters(ctx context.Context) ([]string, error) {
	return s.repo.GetAllClusters(ctx)
}

// GetAvailableBackups 获取可用的备机列表
func (s *resourceService) GetAvailableBackups(ctx context.Context, project, cluster, hardwareStatus, bizStatus string) ([]*asset.Resource, error) {
	return s.repo.GetAvailableBackups(ctx, project, cluster, hardwareStatus, bizStatus)
}
