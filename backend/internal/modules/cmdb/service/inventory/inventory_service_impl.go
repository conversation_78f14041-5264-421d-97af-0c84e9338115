package inventory

import (
	"backend/internal/common/constants"
	"backend/internal/modules/cmdb/model/inventory"
	inventoryRepo "backend/internal/modules/cmdb/repository/inventory"
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// inventoryService 库存服务实现
type inventoryService struct {
	repo inventoryRepo.InventoryRepository
	db   *gorm.DB
}

// NewInventoryService 创建库存服务
func NewInventoryService(repo inventoryRepo.InventoryRepository, db *gorm.DB) InventoryService {
	return &inventoryService{repo: repo, db: db}
}

// CreateInventory 创建库存
func (s *inventoryService) CreateInventory(ctx context.Context, detail *inventory.InventoryDetail) error {
	// 业务逻辑验证
	if detail.ProductID == 0 {
		return errors.New("产品信息不能为空")
	}

	if detail.CurrentStock < 0 {
		return errors.New("库存数量不能小于0")
	}

	// 计算可用库存
	detail.AvailableStock = detail.CurrentStock - detail.AllocatedStock

	return s.repo.Create(ctx, detail)
}

// UpdateInventory 更新库存
func (s *inventoryService) UpdateInventory(ctx context.Context, detail *inventory.InventoryDetail) error {
	// 业务逻辑验证
	if detail.ID == 0 {
		return errors.New("库存ID不能为空")
	}

	// 获取原数据
	oldDetail, err := s.repo.GetByID(ctx, detail.ID)
	if err != nil {
		return err
	}

	// 某些字段不允许直接修改
	detail.AllocatedStock = oldDetail.AllocatedStock

	// 计算可用库存
	detail.AvailableStock = detail.CurrentStock - detail.AllocatedStock

	if detail.AvailableStock < 0 {
		return errors.New("库存调整后可用库存不足，请检查数量")
	}

	return s.repo.Update(ctx, detail)
}

// DeleteInventory 删除库存
func (s *inventoryService) DeleteInventory(ctx context.Context, id uint) error {
	// 验证是否可以删除
	detail, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return err
	}

	if detail.AllocatedStock > 0 {
		return errors.New("该库存有已分配数量，无法删除")
	}

	return s.repo.Delete(ctx, id)
}

// GetInventoryByID 根据ID获取库存
func (s *inventoryService) GetInventoryByID(ctx context.Context, id uint) (*inventory.InventoryDetail, error) {
	return s.repo.GetByID(ctx, id)
}

// ListInventory 查询库存列表
func (s *inventoryService) ListInventory(ctx context.Context, page, pageSize int, productID uint, warehouse string, params map[string]interface{}) ([]*inventory.InventoryDetail, int64, error) {
	// 参数处理
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 10
	}

	// 如果有新参数，则构建扩展查询条件
	if len(params) > 0 {
		// 这里需要扩展repository层实现以支持更多查询条件
		return s.repo.ListWithParams(ctx, page, pageSize, params)
	}

	// 向下兼容，使用原始查询方法
	return s.repo.List(ctx, page, pageSize, productID, warehouse)
}

// AdjustStock 调整库存数量
func (s *inventoryService) AdjustStock(ctx context.Context, id uint, quantity int, changeType, reason string, inboundID, outboundID uint) error {
	if reason == "" {
		return errors.New("必须提供调整原因")
	}

	return s.repo.AdjustStock(ctx, id, quantity, changeType, reason, inboundID, outboundID)
}

// AllocateStock 分配库存
func (s *inventoryService) AllocateStock(ctx context.Context, id uint, quantity int, purpose string) error {
	if quantity <= 0 {
		return errors.New("分配数量必须大于0")
	}

	if purpose == "" {
		return errors.New("必须提供分配用途")
	}

	return s.repo.AllocateStock(ctx, id, quantity, purpose)
}

// ReleaseAllocatedStock 释放已分配库存
func (s *inventoryService) ReleaseAllocatedStock(ctx context.Context, id uint, quantity int) error {
	if quantity <= 0 {
		return errors.New("释放数量必须大于0")
	}

	return s.repo.ReleaseAllocatedStock(ctx, id, quantity)
}

func (s *inventoryService) RemoveAllocatedStock(ctx context.Context, id uint, quantity int, purpose string) error {
	if quantity <= 0 {
		return errors.New("数量必须大于0")
	}
	return s.repo.RemoveAllocatedStock(ctx, id, quantity, purpose)
}

// GetProductInventorySummary 获取产品库存汇总
func (s *inventoryService) GetProductInventorySummary(ctx context.Context, productID uint) (*inventory.InventorySummary, error) {
	if productID == 0 {
		return nil, errors.New("产品ID不能为空")
	}

	return s.repo.GetProductInventorySummary(ctx, productID)
}

// GetLowStockProducts 获取低库存产品列表
func (s *inventoryService) GetLowStockProducts(ctx context.Context, threshold int) ([]*inventory.LowStockProduct, error) {
	if threshold <= 0 {
		threshold = 10 // 默认阈值
	}

	return s.repo.GetLowStockProducts(ctx, threshold)
}

// GetExpiringWarrantyItems 获取即将过保的库存项目
func (s *inventoryService) GetExpiringWarrantyItems(ctx context.Context, days int) ([]*inventory.ExpiringWarrantyItem, error) {
	if days <= 0 {
		days = 30 // 默认30天
	}

	return s.repo.GetExpiringWarrantyItems(ctx, days)
}

// ListByWarehouse 根据仓库查询库存
func (s *inventoryService) ListByWarehouse(ctx context.Context, warehouseID uint, page, pageSize int) ([]*inventory.InventoryDetail, int64, error) {
	if warehouseID == 0 {
		return nil, 0, errors.New("仓库ID不能为空")
	}

	// 参数处理
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 10
	}

	return s.repo.ListByWarehouse(ctx, warehouseID, page, pageSize)
}

// GetStockHistory 获取库存变更历史
func (s *inventoryService) GetStockHistory(ctx context.Context, detailID, productID, warehouseID uint, startTime, endTime time.Time) ([]*inventory.StockHistory, error) {
	// 只有在指定了开始时间但未指定结束时间时，才使用当前时间作为结束时间
	if !startTime.IsZero() && endTime.IsZero() {
		endTime = time.Now()
	}

	// 注意：如果startTime和endTime都为零值，则查询所有历史记录
	// 不再默认限制为最近一个月

	return s.repo.GetStockHistory(ctx, detailID, productID, warehouseID, startTime, endTime)
}

// GetByProductAndWarehouse 根据产品ID和仓库ID查询库存
func (s *inventoryService) GetByProductAndWarehouse(ctx context.Context, productID, warehouseID uint) (*inventory.InventoryDetail, error) {
	var detail inventory.InventoryDetail

	// 查询条件：产品ID和仓库ID匹配
	err := s.db.WithContext(ctx).
		Where("product_id = ? AND warehouse_id = ?", productID, warehouseID).
		Preload("Product").
		First(&detail).Error

	// 如果找不到记录，自动创建一个新的记录
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 获取仓库名称
			var warehouseName string
			if err := s.db.WithContext(ctx).Table("warehouses").
				Select("name").
				Where("id = ?", warehouseID).
				Row().Scan(&warehouseName); err != nil {
				return nil, fmt.Errorf("查询仓库信息失败: %w", err)
			}

			if warehouseName == "" {
				warehouseName = "未知仓库"
			}

			// 自动创建库存记录
			newDetail := &inventory.InventoryDetail{
				ProductID:      productID,
				WarehouseID:    warehouseID,
				Warehouse:      warehouseName,
				CurrentStock:   0,
				AllocatedStock: 0,
				AvailableStock: 0,
			}

			// 保存新记录
			if createErr := s.db.WithContext(ctx).Create(newDetail).Error; createErr != nil {
				return nil, fmt.Errorf("自动创建库存记录失败: %w", createErr)
			}

			// 重新加载记录（包括关联的产品信息）
			if loadErr := s.db.WithContext(ctx).
				Where("product_id = ? AND warehouse_id = ?", productID, warehouseID).
				Preload("Product").
				First(&detail).Error; loadErr != nil {
				return nil, fmt.Errorf("加载新创建的库存记录失败: %w", loadErr)
			}

			return &detail, nil
		}
		return nil, err
	}

	return &detail, nil
}

// CreateSimpleInventory 使用基本参数创建库存记录
func (s *inventoryService) CreateSimpleInventory(ctx context.Context, productID, warehouseID uint, stock int, warehouseName, status string) error {
	// 业务逻辑验证
	if productID == 0 {
		return errors.New("产品信息不能为空")
	}

	if stock < 0 {
		return errors.New("库存数量不能小于0")
	}

	if warehouseName == "" {
		// 获取仓库名称
		if err := s.db.WithContext(ctx).Table("warehouses").
			Select("name").
			Where("id = ?", warehouseID).
			Row().Scan(&warehouseName); err != nil {
			warehouseName = "未知仓库" // 如果查询失败设置默认值
		}
	}

	// 创建库存明细对象
	detail := &inventory.InventoryDetail{
		ProductID:      productID,
		WarehouseID:    warehouseID,
		Warehouse:      warehouseName,
		CurrentStock:   stock,
		AllocatedStock: 0,
		AvailableStock: stock,
	}

	// 调用仓库层方法创建
	return s.repo.Create(ctx, detail)
}

func (s *inventoryService) GetInventoryByProductIDandWarehouseID(ctx context.Context, productID, warehouseID uint) (*inventory.InventoryDetail, error) {
	return s.repo.GetByProductIDAndWarehouseID(ctx, productID, warehouseID)
}

// SyncInventoryWithSpareStatus 根据备件状态同步库存数据
func (s *inventoryService) SyncInventoryWithSpareStatus(ctx context.Context, productID, warehouseID uint) error {

	// 获取库存记录（如不存在则自动创建）
	inventoryDetail, err := s.GetByProductAndWarehouse(ctx, productID, warehouseID)
	if err != nil {
		return fmt.Errorf("获取库存记录失败: %w", err)
	}

	// 记录原始库存数量，用于生成变更历史
	oldCurrentStock := inventoryDetail.CurrentStock
	oldAllocatedStock := inventoryDetail.AllocatedStock
	oldAvailableStock := inventoryDetail.AvailableStock

	// 计算各种状态的资产数量
	var (
		totalCount     int64 // 总数量
		availableCount int64 // 可用数量（闲置中+已入库）
		allocatedCount int64 // 已分配数量（使用中+维修中+待出库）
	)

	// 使用正确的表名查询总数量（不包括已报废、待报废、已售卖的资产）
	if err := s.db.WithContext(ctx).Table("asset_spares").
		Where("product_id = ? AND warehouse_id = ? AND asset_status NOT IN (?, ?, ?) AND deleted_at IS NULL",
			productID, warehouseID, constants.AssetStatusScrapped, constants.AssetStatusPendingScrap, constants.AssetStatusSoldOut).
		Count(&totalCount).Error; err != nil {
		return fmt.Errorf("统计资产总数失败: %w", err)
	}

	// 查询可用数量（闲置中 + 已入库，这些资产可以直接使用）
	if err := s.db.WithContext(ctx).Table("asset_spares").
		Where("product_id = ? AND warehouse_id = ? AND asset_status IN (?, ?) AND deleted_at IS NULL",
			productID, warehouseID, constants.AssetStatusIdle, constants.AssetStatusInStock).
		Count(&availableCount).Error; err != nil {
		return fmt.Errorf("统计可用资产数量失败: %w", err)
	}

	// 查询已分配数量（使用中 + 维修中 + 待出库 + 已出库，这些资产已被占用）
	if err := s.db.WithContext(ctx).Table("asset_spares").
		Where("product_id = ? AND warehouse_id = ? AND asset_status IN (?, ?, ?, ?) AND deleted_at IS NULL",
			productID, warehouseID, constants.AssetStatusInUse, constants.AssetStatusRepairing, constants.AssetStatusPendingOut, constants.AssetStatusOutStock).
		Count(&allocatedCount).Error; err != nil {
		return fmt.Errorf("统计已分配资产数量失败: %w", err)
	}

	// 更新库存数量
	inventoryDetail.CurrentStock = int(totalCount)
	inventoryDetail.AllocatedStock = int(allocatedCount)
	inventoryDetail.AvailableStock = int(availableCount)

	// 计算变更量
	changeAmount := inventoryDetail.CurrentStock - oldCurrentStock

	// 如果数量发生变化，记录库存变更历史
	if inventoryDetail.CurrentStock != oldCurrentStock ||
		inventoryDetail.AllocatedStock != oldAllocatedStock ||
		inventoryDetail.AvailableStock != oldAvailableStock {

		// 获取操作用户信息
		var operatorID uint
		var operator = "system"
		if userID, ok := ctx.Value("user_id").(uint); ok {
			operatorID = userID
		}
		if userName, ok := ctx.Value("user_name").(string); ok {
			operator = userName
		}

		// 生成变更原因
		var productName string
		if inventoryDetail.Product.ID > 0 {
			productName = inventoryDetail.Product.Model
		} else {
			// 尝试获取产品名称
			if err := s.db.WithContext(ctx).Table("products").
				Select("model").
				Where("id = ?", productID).
				Row().Scan(&productName); err != nil {
				productName = fmt.Sprintf("产品ID:%d", productID)
			}
		}

		// 生成变更历史记录
		reason := fmt.Sprintf("系统同步库存: %s, 库存数量从 %d 调整为 %d, 可用库存从 %d 调整为 %d",
			productName, oldCurrentStock, inventoryDetail.CurrentStock,
			oldAvailableStock, inventoryDetail.AvailableStock)

		// 开始事务
		tx := s.db.WithContext(ctx).Begin()
		if tx.Error != nil {
			return tx.Error
		}

		// 记录库存变更历史
		stockHistory := inventory.StockHistory{
			DetailID:     inventoryDetail.ID,
			ProductID:    inventoryDetail.ProductID,
			OldQuantity:  oldCurrentStock,
			NewQuantity:  inventoryDetail.CurrentStock,
			ChangeAmount: changeAmount,
			ChangeType:   "sync",
			ChangeTime:   time.Now(),
			OperatorID:   operatorID,
			Operator:     operator,
			Reason:       reason,
			WarehouseID:  inventoryDetail.WarehouseID,
		}

		if err := tx.Create(&stockHistory).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("创建库存变更历史失败: %w", err)
		}

		// 更新库存记录
		if err := tx.Model(&inventory.InventoryDetail{}).
			Where("id = ?", inventoryDetail.ID).
			Updates(map[string]interface{}{
				"current_stock":   inventoryDetail.CurrentStock,
				"allocated_stock": inventoryDetail.AllocatedStock,
				"available_stock": inventoryDetail.AvailableStock,
				"updated_at":      time.Now(),
			}).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("更新库存记录失败: %w", err)
		}

		// 提交事务
		if err := tx.Commit().Error; err != nil {
			return fmt.Errorf("提交事务失败: %w", err)
		}

		return nil
	}

	return nil
}

// SyncInventoryWithDeviceStatus 根据设备状态同步库存数据
func (s *inventoryService) SyncInventoryWithDeviceStatus(ctx context.Context, productID, warehouseID uint) error {

	// 获取库存记录（如不存在则自动创建）
	inventoryDetail, err := s.GetByProductAndWarehouse(ctx, productID, warehouseID)
	if err != nil {
		return fmt.Errorf("获取库存记录失败: %w", err)
	}

	// 记录原始库存数量，用于生成变更历史
	oldCurrentStock := inventoryDetail.CurrentStock
	oldAllocatedStock := inventoryDetail.AllocatedStock
	oldAvailableStock := inventoryDetail.AvailableStock

	// 计算各种状态的资产数量
	var (
		totalCount     int64 // 总数量
		availableCount int64 // 可用数量（闲置中+已入库）
		allocatedCount int64 // 已分配数量（使用中+维修中+待出库）
	)

	// 使用正确的表名查询总数量（不包括已报废、待报废、已售卖的资产）
	if err := s.db.WithContext(ctx).
		Table("asset_devices").
		Joins("JOIN resources ON asset_devices.id = resources.asset_id").
		Joins("JOIN warehouses ON resources.room_id = warehouses.room_id").
		Where(`asset_devices.product_id = ? AND warehouses.id = ? AND asset_devices.asset_status NOT IN (?, ?, ?) AND asset_devices.deleted_at IS NULL`,
			productID, warehouseID, constants.AssetStatusScrapped, constants.AssetStatusPendingScrap, constants.AssetStatusSoldOut).
		Count(&totalCount).Error; err != nil {
		return fmt.Errorf("统计资产总数失败: %w", err)
	}

	// 查询可用数量（闲置中 + 已入库，这些资产可以直接使用）
	if err := s.db.WithContext(ctx).Table("asset_devices").
		Joins("JOIN resources ON asset_devices.id = resources.asset_id").
		Joins("JOIN warehouses ON resources.room_id = warehouses.room_id").
		Where("asset_devices.product_id = ? AND warehouses.id = ? AND asset_devices.asset_status IN (?, ?) AND asset_devices.deleted_at IS NULL",
			productID, warehouseID, constants.AssetStatusIdle, constants.AssetStatusInStock).
		Count(&availableCount).Error; err != nil {
		return fmt.Errorf("统计可用资产数量失败: %w", err)
	}

	// 查询已分配数量（使用中 + 维修中 + 已出库，已验收，已上架，这些资产已被占用）
	if err := s.db.WithContext(ctx).Table("asset_devices").
		Joins("JOIN resources ON asset_devices.id = resources.asset_id").
		Joins("JOIN warehouses ON resources.room_id = warehouses.room_id").
		Where("asset_devices.product_id = ? AND warehouses.id = ? AND asset_devices.asset_status IN (?, ?, ?, ?, ?) AND asset_devices.deleted_at IS NULL",
			productID, warehouseID, constants.AssetStatusInUse, constants.AssetStatusRepairing, constants.AssetStatusOutStock, constants.AssetStatusOnRack, constants.AssetStatusVerify).
		Count(&allocatedCount).Error; err != nil {
		return fmt.Errorf("统计已分配资产数量失败: %w", err)
	}

	// 更新库存数量
	inventoryDetail.CurrentStock = int(totalCount)
	inventoryDetail.AllocatedStock = int(allocatedCount)
	inventoryDetail.AvailableStock = int(availableCount)

	// 计算变更量
	changeAmount := inventoryDetail.CurrentStock - oldCurrentStock

	// 如果数量发生变化，记录库存变更历史
	if inventoryDetail.CurrentStock != oldCurrentStock ||
		inventoryDetail.AllocatedStock != oldAllocatedStock ||
		inventoryDetail.AvailableStock != oldAvailableStock {

		// 获取操作用户信息
		var operatorID uint
		var operator = "system"
		if userID, ok := ctx.Value("user_id").(uint); ok {
			operatorID = userID
		}
		if userName, ok := ctx.Value("user_name").(string); ok {
			operator = userName
		}

		// 生成变更原因
		var productName string
		if inventoryDetail.Product.ID > 0 {
			productName = inventoryDetail.Product.Model
		} else {
			// 尝试获取产品名称
			if err := s.db.WithContext(ctx).Table("products").
				Select("model").
				Where("id = ?", productID).
				Row().Scan(&productName); err != nil {
				productName = fmt.Sprintf("产品ID:%d", productID)
			}
		}

		// 生成变更历史记录
		reason := fmt.Sprintf("系统同步库存: %s, 库存数量从 %d 调整为 %d, 可用库存从 %d 调整为 %d",
			productName, oldCurrentStock, inventoryDetail.CurrentStock,
			oldAvailableStock, inventoryDetail.AvailableStock)

		// 开始事务
		tx := s.db.WithContext(ctx).Begin()
		if tx.Error != nil {
			return tx.Error
		}

		// 记录库存变更历史
		stockHistory := inventory.StockHistory{
			DetailID:     inventoryDetail.ID,
			ProductID:    inventoryDetail.ProductID,
			OldQuantity:  oldCurrentStock,
			NewQuantity:  inventoryDetail.CurrentStock,
			ChangeAmount: changeAmount,
			ChangeType:   "sync",
			ChangeTime:   time.Now(),
			OperatorID:   operatorID,
			Operator:     operator,
			Reason:       reason,
			WarehouseID:  inventoryDetail.WarehouseID,
		}

		if err := tx.Create(&stockHistory).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("创建库存变更历史失败: %w", err)
		}

		// 更新库存记录
		if err := tx.Model(&inventory.InventoryDetail{}).
			Where("id = ?", inventoryDetail.ID).
			Updates(map[string]interface{}{
				"current_stock":   inventoryDetail.CurrentStock,
				"allocated_stock": inventoryDetail.AllocatedStock,
				"available_stock": inventoryDetail.AvailableStock,
				"updated_at":      time.Now(),
			}).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("更新库存记录失败: %w", err)
		}

		// 提交事务
		if err := tx.Commit().Error; err != nil {
			return fmt.Errorf("提交事务失败: %w", err)
		}

		return nil
	}

	return nil
}

// GetLatestInventoryDetail 获取特定仓库中某一种规格的最新信息,给配件新购入库使用
func (s *inventoryService) GetLatestInventoryDetail(ctx context.Context, productID, warehouseID uint) (*inventory.InventoryDetail, error) {
	return s.repo.GetLatestInventoryDetail(ctx, productID, warehouseID)
}
