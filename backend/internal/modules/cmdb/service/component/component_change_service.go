package component

import (
	"context"
	"errors"
	"fmt"
	"time"

	"backend/internal/modules/cmdb/model/component"
	assetRepo "backend/internal/modules/cmdb/repository/asset"
	componentRepo "backend/internal/modules/cmdb/repository/component"
)

// ComponentChangeInfo 组件变更信息
type ComponentChangeInfo struct {
	Reason     string `json:"reason"`     // 变更原因
	OperatorID uint   `json:"operatorId"` // 操作人ID
	Remarks    string `json:"remarks"`    // 备注
}

// ComponentInstallInfo 组件安装信息
type ComponentInstallInfo struct {
	SlotPosition  string `json:"slotPosition"`  // 安装位置
	ComponentType string `json:"componentType"` // 组件类型
	Reason        string `json:"reason"`        // 安装原因
	OperatorID    uint   `json:"operatorId"`    // 操作人ID
	Remarks       string `json:"remarks"`       // 备注
}

// ComponentRemoveInfo 组件移除信息
type ComponentRemoveInfo struct {
	Reason     string `json:"reason"`     // 移除原因
	OperatorID uint   `json:"operatorId"` // 操作人ID
	Remarks    string `json:"remarks"`    // 备注
}

// ComponentChangeService 组件变更服务接口
type ComponentChangeService interface {
	// GetChangeHistory 获取组件变更历史
	GetChangeHistory(ctx context.Context, componentID uint) ([]*component.ComponentChangeLog, error)
	// GetServerChangeHistory 获取服务器组件变更历史
	GetServerChangeHistory(ctx context.Context, serverID uint, page, pageSize int) ([]*component.ComponentChangeLog, int64, error)
	// ReplaceComponent 更换组件
	ReplaceComponent(ctx context.Context, oldComponentID, spareID uint, changeInfo *ComponentChangeInfo) (*component.ServerComponent, error)
	// InstallComponent 安装组件
	InstallComponent(ctx context.Context, serverID, productID uint, spareID uint, installInfo *ComponentInstallInfo) (*component.ServerComponent, error)
	// RemoveComponent 移除组件
	RemoveComponent(ctx context.Context, componentID uint, removeInfo *ComponentRemoveInfo) error
}

// componentChangeService 组件变更服务实现
type componentChangeService struct {
	componentRepo componentRepo.ServerComponentRepository
	changeLogRepo componentRepo.ComponentChangeLogRepository
	spareRepo     assetRepo.SpareRepository
}

// NewComponentChangeService 创建组件变更服务
func NewComponentChangeService(
	componentRepo componentRepo.ServerComponentRepository,
	changeLogRepo componentRepo.ComponentChangeLogRepository,
	spareRepo assetRepo.SpareRepository,
) ComponentChangeService {
	return &componentChangeService{
		componentRepo: componentRepo,
		changeLogRepo: changeLogRepo,
		spareRepo:     spareRepo,
	}
}

// GetChangeHistory 获取组件变更历史
func (s *componentChangeService) GetChangeHistory(ctx context.Context, componentID uint) ([]*component.ComponentChangeLog, error) {
	return s.changeLogRepo.ListByComponentID(ctx, componentID)
}

// GetServerChangeHistory 获取服务器组件变更历史
func (s *componentChangeService) GetServerChangeHistory(ctx context.Context, serverID uint, page, pageSize int) ([]*component.ComponentChangeLog, int64, error) {
	return s.changeLogRepo.ListByServerID(ctx, serverID, page, pageSize)
}

// ReplaceComponent 更换组件
func (s *componentChangeService) ReplaceComponent(ctx context.Context, oldComponentID, spareID uint, changeInfo *ComponentChangeInfo) (*component.ServerComponent, error) {
	// 参数验证
	if oldComponentID == 0 {
		return nil, errors.New("旧组件ID不能为空")
	}
	if spareID == 0 {
		return nil, errors.New("备件ID不能为空")
	}
	if changeInfo == nil {
		return nil, errors.New("变更信息不能为空")
	}
	if changeInfo.Reason == "" {
		return nil, errors.New("变更原因不能为空")
	}

	// 查询旧组件
	oldComponent, err := s.componentRepo.GetByID(ctx, oldComponentID)
	if err != nil {
		return nil, fmt.Errorf("获取旧组件失败: %w", err)
	}
	if oldComponent.ServerID == 0 {
		return nil, errors.New("旧组件未关联服务器")
	}

	// 查询备件
	spare, err := s.spareRepo.GetByID(ctx, spareID)
	if err != nil {
		return nil, fmt.Errorf("获取备件失败: %w", err)
	}
	if spare.AssetStatus != "idle" {
		return nil, errors.New("备件状态不是空闲状态")
	}

	// 开始事务
	tx := s.componentRepo.GetDB().Begin()
	if tx.Error != nil {
		return nil, tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 使用事务创建仓库实例
	txComponentRepo := s.componentRepo.WithTx(tx)
	txChangeLogRepo := s.changeLogRepo.WithTx(tx)
	txSpareRepo := s.spareRepo.WithTx(tx)

	// 1. 创建新组件
	newComponent := &component.ServerComponent{
		ServerID:        oldComponent.ServerID,
		ProductID:       spare.ProductID,
		SN:              spare.SN,
		ComponentType:   oldComponent.ComponentType,
		SlotPosition:    oldComponent.SlotPosition,
		Status:          "normal",
		FirmwareVersion: spare.FirmwareVersion,
		Description:     spare.Remark,
	}

	if err := txComponentRepo.Create(ctx, newComponent); err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("创建新组件失败: %w", err)
	}

	// 2. 创建变更日志
	now := time.Now()
	changeLog := &component.ComponentChangeLog{
		ServerID:            oldComponent.ServerID,
		ComponentID:         newComponent.ID,
		PreviousComponentID: oldComponent.ID,
		SpareID:             spareID,
		ChangeType:          "replace",
		ChangeTime:          now,
		Reason:              changeInfo.Reason,
		OperatorID:          changeInfo.OperatorID,
		Remark:              changeInfo.Remarks,
	}

	if _, err := txChangeLogRepo.Create(ctx, changeLog); err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("创建变更日志失败: %w", err)
	}

	// 3. 更新旧组件状态
	oldComponent.Status = "removed"
	oldComponent.ServerID = 0 // 解除与服务器的关联
	if err := txComponentRepo.Update(ctx, oldComponent); err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("更新旧组件状态失败: %w", err)
	}

	// 4. 更新备件状态
	if err := txSpareRepo.ChangeStatus(ctx, spareID, "in_use", "已安装到服务器"); err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("更新备件状态失败: %w", err)
	}

	// 5. 关联备件与设备
	if err := txSpareRepo.AssignToDevice(ctx, spareID, oldComponent.ServerID, oldComponent.SlotPosition); err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("关联备件与设备失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	return newComponent, nil
}

// InstallComponent 安装组件
func (s *componentChangeService) InstallComponent(ctx context.Context, serverID, productID uint, spareID uint, installInfo *ComponentInstallInfo) (*component.ServerComponent, error) {
	// 参数验证
	if serverID == 0 {
		return nil, errors.New("服务器ID不能为空")
	}
	if productID == 0 {
		return nil, errors.New("产品ID不能为空")
	}
	if spareID == 0 {
		return nil, errors.New("备件ID不能为空")
	}
	if installInfo == nil {
		return nil, errors.New("安装信息不能为空")
	}
	if installInfo.ComponentType == "" {
		return nil, errors.New("组件类型不能为空")
	}
	if installInfo.Reason == "" {
		return nil, errors.New("安装原因不能为空")
	}

	// 查询备件
	spare, err := s.spareRepo.GetByID(ctx, spareID)
	if err != nil {
		return nil, fmt.Errorf("获取备件失败: %w", err)
	}
	if spare.AssetStatus != "idle" {
		return nil, errors.New("备件状态不是空闲状态")
	}

	// 开始事务
	tx := s.componentRepo.GetDB().Begin()
	if tx.Error != nil {
		return nil, tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 使用事务创建仓库实例
	txComponentRepo := s.componentRepo.WithTx(tx)
	txChangeLogRepo := s.changeLogRepo.WithTx(tx)
	txSpareRepo := s.spareRepo.WithTx(tx)

	// 1. 创建新组件
	newComponent := &component.ServerComponent{
		ServerID:        serverID,
		ProductID:       productID,
		SN:              spare.SN,
		ComponentType:   installInfo.ComponentType,
		SlotPosition:    installInfo.SlotPosition,
		Status:          "normal",
		FirmwareVersion: spare.FirmwareVersion,
		Description:     spare.Remark,
	}

	if err := txComponentRepo.Create(ctx, newComponent); err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("创建新组件失败: %w", err)
	}

	// 2. 创建变更日志
	now := time.Now()
	changeLog := &component.ComponentChangeLog{
		ServerID:            serverID,
		ComponentID:         newComponent.ID,
		PreviousComponentID: 0, // 新安装没有前置组件
		SpareID:             spareID,
		ChangeType:          "install",
		ChangeTime:          now,
		Reason:              installInfo.Reason,
		OperatorID:          installInfo.OperatorID,
		Remark:              installInfo.Remarks,
	}

	if _, err := txChangeLogRepo.Create(ctx, changeLog); err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("创建变更日志失败: %w", err)
	}

	// 3. 更新备件状态
	if err := txSpareRepo.ChangeStatus(ctx, spareID, "in_use", "已安装到服务器"); err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("更新备件状态失败: %w", err)
	}

	// 4. 关联备件与设备
	if err := txSpareRepo.AssignToDevice(ctx, spareID, serverID, installInfo.SlotPosition); err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("关联备件与设备失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	return newComponent, nil
}

// RemoveComponent 移除组件
func (s *componentChangeService) RemoveComponent(ctx context.Context, componentID uint, removeInfo *ComponentRemoveInfo) error {
	// 参数验证
	if componentID == 0 {
		return errors.New("组件ID不能为空")
	}
	if removeInfo == nil {
		return errors.New("移除信息不能为空")
	}
	if removeInfo.Reason == "" {
		return errors.New("移除原因不能为空")
	}

	// 查询组件
	comp, err := s.componentRepo.GetByID(ctx, componentID)
	if err != nil {
		return fmt.Errorf("获取组件失败: %w", err)
	}
	if comp.ServerID == 0 {
		return errors.New("组件未关联服务器")
	}

	// 开始事务
	tx := s.componentRepo.GetDB().Begin()
	if tx.Error != nil {
		return tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 使用事务创建仓库实例
	txComponentRepo := s.componentRepo.WithTx(tx)
	txChangeLogRepo := s.changeLogRepo.WithTx(tx)

	// 1. 创建变更日志
	now := time.Now()
	changeLog := &component.ComponentChangeLog{
		ServerID:            comp.ServerID,
		ComponentID:         0, // 移除操作没有新组件
		PreviousComponentID: componentID,
		SpareID:             0, // 移除操作可能没有关联备件
		ChangeType:          "remove",
		ChangeTime:          now,
		Reason:              removeInfo.Reason,
		OperatorID:          removeInfo.OperatorID,
		Remark:              removeInfo.Remarks,
	}

	if _, err := txChangeLogRepo.Create(ctx, changeLog); err != nil {
		tx.Rollback()
		return fmt.Errorf("创建变更日志失败: %w", err)
	}

	// 2. 更新组件状态
	comp.Status = "removed"
	comp.ServerID = 0 // 解除与服务器的关联
	if err := txComponentRepo.Update(ctx, comp); err != nil {
		tx.Rollback()
		return fmt.Errorf("更新组件状态失败: %w", err)
	}

	// 3. 如果组件关联了备件，需要更新备件状态并解除关联
	// 这里需要查询备件是否与该组件关联，如果有关联则更新状态
	// 由于当前模型中没有直接的关联字段，可能需要通过SN等信息查询

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	return nil
}
