package model

import (
	"backend/internal/modules/cmdb/model/asset"
	"time"

	"gorm.io/gorm"
)

// FaultTicket 报障单模型
type FaultTicket struct {
	ID                     uint           `json:"id" gorm:"primarykey"`
	CreatedAt              time.Time      `json:"created_at"`
	UpdatedAt              time.Time      `json:"updated_at"`
	DeletedAt              gorm.DeletedAt `json:"-" gorm:"index"`
	TicketNo               string         `json:"ticketNo" gorm:"unique;type:varchar(50);not null;comment:工单号"`
	Source                 string         `json:"source" gorm:"type:enum('manual','customer','alarm');comment:触发来源"`
	Title                  string         `json:"title" gorm:"type:varchar(200);comment:故障标题"`
	Priority               string         `json:"priority" gorm:"type:enum('low','medium','high','critical');comment:优先级"`
	Status                 string         `json:"status" gorm:"type:enum('waiting_accept','investigating','waiting_approval','approved_waiting_action','repairing','restarting','migrating','software_fixing','waiting_verification','summarizing','completed','cancelled');default:waiting_accept;comment:状态"`
	DeviceSN               string         `json:"deviceSN" gorm:"column:device_sn;type:varchar(100);comment:设备序列号"`
	ComponentSN            string         `json:"componentSN,omitempty" gorm:"column:component_sn;type:varchar(100);comment:组件序列号"`
	ComponentType          string         `json:"componentType,omitempty" gorm:"column:component_type;type:varchar(100);comment:组件类型"`
	Resource               asset.Resource `json:"resource" gorm:"foreignKey:device_sn;references:SN"`
	ResourceIdentifier     string         `json:"resource_identifier" gorm:"type:varchar(100);index;comment:资源标识符(租户IP)"`
	FaultType              string         `json:"faultType" gorm:"type:varchar(30);comment:故障类型(hardware:硬件故障,software:软件故障,network:网络故障,system:系统故障,operation:操作故障,other:其他)"`
	FaultDetailType        string         `json:"fault_detail_type" gorm:"type:varchar(100);comment:故障具体类型"`
	RepairTicketID         uint           `json:"repair_ticket_id" gorm:"column:repair_ticket_id"` // 关联的维修单ID
	FaultDescription       string         `json:"faultDescription" gorm:"type:text;comment:故障描述"`
	Symptom                string         `json:"symptom,omitempty" gorm:"type:varchar(255);comment:故障现象"`
	SlotPosition           string         `json:"slotPosition" gorm:"type:varchar(255);comment:插槽位置，多个位置用逗号分隔，如:1,2,3"`
	ReporterID             uint           `json:"reporterID" gorm:"comment:报障人ID"`
	ReporterName           string         `json:"reporterName" gorm:"type:varchar(100);comment:报障人姓名"`
	AssignedTo             string         `json:"assignedTo" gorm:"type:varchar(100);comment:分配给(用户真实姓名)"`
	CreationTime           time.Time      `json:"creationTime" gorm:"not null;comment:创建时间"`
	AcknowledgeTime        *time.Time     `json:"acknowledgeTime" gorm:"comment:确认时间"`
	TriageCompleteTime     *time.Time     `json:"triageCompleteTime" gorm:"comment:分诊完成时间"`
	AssignmentTime         *time.Time     `json:"assignmentTime" gorm:"comment:分配时间"`
	CustomerApprovalTime   *time.Time     `json:"customerApprovalTime" gorm:"comment:客户审批时间"`
	ExpectedFixTime        *time.Time     `json:"expectedFixTime" gorm:"comment:预计修复时间"`
	ActualFixTime          *time.Time     `json:"actualFixTime" gorm:"comment:实际修复时间"`
	VerificationStartTime  *time.Time     `json:"verificationStartTime" gorm:"comment:验证开始时间"`
	VerificationEndTime    *time.Time     `json:"verificationEndTime" gorm:"comment:验证结束时间"`
	CloseTime              *time.Time     `json:"closeTime" gorm:"comment:关闭时间"`
	DiagnosisDuration      int            `json:"diagnosis_duration,omitempty" gorm:"comment:诊断时长(分钟)"`
	ResponseDuration       int            `json:"response_duration" gorm:"comment:响应时长(分钟)"`
	HardwareRepairDuration int            `json:"hardware_repair_duration" gorm:"comment:硬件维修时长(分钟)"`
	SoftwareFixDuration    int            `json:"software_fix_duration" gorm:"comment:软件处理时长(分钟)"`
	TotalDowntime          int            `json:"totalDowntime" gorm:"comment:总停机时间(分钟)"`
	BusinessImpactTime     int            `json:"business_impact_time" gorm:"comment:业务影响时长(分钟)"`
	IsFrequentFault        bool           `json:"is_frequent_fault" gorm:"comment:是否高频故障"`
	IsFalseAlarm           bool           `json:"is_false_alarm" gorm:"default:false;comment:是否误报"`
	IsDuplicateFault       bool           `json:"is_duplicate_fault" gorm:"default:false;comment:是否重复故障"`
	RelatedTicketID        uint           `json:"related_ticket_id" gorm:"default:0;comment:关联报障单ID"`
	RequireApproval        bool           `json:"require_approval" gorm:"comment:是否需要客户审批"`
	ImmediateRepair        bool           `json:"immediate_repair,omitempty" gorm:"comment:是否立即维修"`
	CountInSLA             bool           `json:"count_in_sla" gorm:"default:true;comment:是否计入SLA"`
	FaultSummary           string         `json:"faultSummary" gorm:"type:text;comment:故障总结"`
	RepairMethod           string         `json:"repairMethod" gorm:"type:varchar(255);comment:维修方法"`
	PreventionMeasures     string         `json:"preventionMeasures,omitempty" gorm:"type:text;comment:预防措施"`
	BusinessImpact         string         `json:"business_impact,omitempty" gorm:"type:text;comment:业务影响"`
	SLAStatus              string         `json:"slaStatus" gorm:"type:enum('in_compliance','violated','exempted');default:in_compliance;comment:SLA状态"`
	SLAViolationReason     string         `json:"sla_violation_reason,omitempty" gorm:"type:text;comment:SLA违规原因"`
	Remarks                string         `json:"remarks" gorm:"type:text;comment:备注"`
	// 工作流相关字段
	NeedsWorkflowRetry    bool       `json:"needsWorkflowRetry" gorm:"default:false;comment:是否需要重试工作流"`
	LastWorkflowRetryTime *time.Time `json:"lastWorkflowRetryTime" gorm:"comment:最后工作流重试时间"`
	WorkflowRetryCount    int        `json:"workflow_retry_count" gorm:"default:0;comment:工作流重试次数"`
	// 手动触发相关字段
	WaitingManualTrigger bool       `json:"waiting_manual_trigger" gorm:"column:waiting_manual_trigger"`
	CurrentWaitingStage  string     `json:"current_waiting_stage" gorm:"column:current_waiting_stage"`
	LastWaitingTime      *time.Time `json:"lastWaitingTime" gorm:"comment:上次等待的时间"`
}

// TableName 指定表名
func (FaultTicket) TableName() string {
	return "fault_tickets"
}

// FaultTicketStatusHistory 报障单状态历史
type FaultTicketStatusHistory struct {
	ID               uint           `json:"id" gorm:"primarykey"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `json:"-" gorm:"index"`
	FaultTicketID    uint           `json:"fault_ticket_id" gorm:"comment:报障单ID"`
	PreviousStatus   string         `json:"previous_status" gorm:"type:varchar(50);comment:先前状态"`
	NewStatus        string         `json:"new_status" gorm:"type:varchar(50);comment:新状态"`
	OperatorID       uint           `json:"operator_id" gorm:"comment:操作人ID"`
	OperatorName     string         `json:"operator_name" gorm:"type:varchar(50);comment:操作人姓名"`
	OperationTime    time.Time      `json:"operation_time" gorm:"comment:操作时间"`
	Duration         int            `json:"duration" gorm:"comment:状态持续时间(分钟)"`
	ActivityCategory string         `json:"activity_category" gorm:"type:varchar(50);comment:活动类别"`
	IsSLAPause       bool           `json:"is_sla_pause" gorm:"comment:是否SLA暂停时间"`
	PauseReason      string         `json:"pause_reason" gorm:"type:text;comment:暂停原因"`
	Remarks          string         `json:"remarks" gorm:"type:text;comment:备注"`
}

// TableName 指定表名
func (FaultTicketStatusHistory) TableName() string {
	return "fault_ticket_status_histories"
}

// 工单状态常量
const (
	// ... existing code ...

	// StatusDiagnosisCompleted 诊断完成
	StatusDiagnosisCompleted = "diagnosis_completed"

	// StatusWaitingApproval 等待审批
	StatusWaitingApproval = "waiting_approval"

	// StatusVerificationPending 待验证
	StatusVerificationPending = "verification_pending"

	// ... existing code ...
)

// 工单阶段常量
const (
	// ... existing code ...

	// StageRepairSelection 维修选择阶段
	StageRepairSelection = "repair_selection"

	// StageCustomerApproval 客户审批阶段
	StageCustomerApproval = "customer_approval"

	// StageVerification 验证阶段
	StageVerification = "verification"

	// ... existing code ...
)

// RepairSelectionInput 维修选择输入
type RepairSelectionInput struct {
	TicketID        uint   `json:"ticket_id"`
	RepairType      string `json:"repair_type"`
	DiagnosisResult string `json:"diagnosis_result"`
}

// UpdateFaultTicketStatusInput 更新故障工单状态输入
type UpdateFaultTicketStatusInput struct {
	TicketID uint   `json:"ticket_id"`
	Status   string `json:"status"`
}

// MarkTicketForManualTriggerInput 标记工单为手动触发输入
type MarkTicketForManualTriggerInput struct {
	TicketID     uint   `json:"ticket_id"`
	WaitingStage string `json:"waiting_stage"`
}
