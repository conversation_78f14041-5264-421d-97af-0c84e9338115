package model

import (
	"time"
)

// RepairSelection 维修选择记录
type RepairSelection struct {
	ID              uint      `json:"id" gorm:"primaryKey"`
	TicketID        uint      `json:"ticket_id" gorm:"index;comment:关联的报障单ID"`
	RepairType      string    `json:"repair_type" gorm:"type:varchar(50);comment:维修类型(restart/hardware/software)"`
	FaultDetailType string    `json:"fault_detail_type" gorm:"type:varchar(100);comment:故障具体类型"`
	SlotPosition    string    `json:"slot_position" gorm:"type:varchar(255);comment:插槽位置，多个位置用逗号分隔，如:1,2,3"`
	Diagnosis       string    `json:"diagnosis" gorm:"type:text;comment:诊断结果"`
	Comments        string    `json:"comments" gorm:"type:text;comment:备注"`
	OperatorID      uint      `json:"operator_id" gorm:"comment:操作人ID"`
	OperatorName    string    `json:"operator_name" gorm:"type:varchar(50);comment:操作人姓名"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// CustomerApproval 客户审批记录
type CustomerApproval struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	TicketID     uint      `json:"ticket_id" gorm:"index;comment:关联的报障单ID"`
	Status       string    `json:"status" gorm:"type:varchar(20);comment:审批状态(approved/rejected)"`
	ResponseTime time.Time `json:"response_time" gorm:"comment:响应时间"`
	Comments     string    `json:"comments" gorm:"type:text;comment:审批意见"`
	CustomerID   uint      `json:"customer_id" gorm:"comment:客户ID"`
	CustomerName string    `json:"customer_name" gorm:"type:varchar(50);comment:客户姓名"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// Verification 验证结果记录
type Verification struct {
	ID               uint      `json:"id" gorm:"primaryKey"`
	TicketID         uint      `json:"ticket_id" gorm:"index;comment:关联的报障单ID"`
	Success          bool      `json:"success" gorm:"comment:是否验证通过"`
	VerificationTime time.Time `json:"verification_time" gorm:"comment:验证时间"`
	Comments         string    `json:"comments" gorm:"type:text;comment:验证意见"`
	OperatorID       uint      `json:"operator_id" gorm:"comment:操作人ID"`
	OperatorName     string    `json:"operator_name" gorm:"type:varchar(50);comment:操作人姓名"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// ColdMigration 冷迁移记录
type ColdMigration struct {
	ID             uint      `json:"id" gorm:"primaryKey"`
	TicketID       uint      `json:"ticket_id" gorm:"index;comment:关联的报障单ID"`
	FaultDeviceSN  string    `json:"fault_device_sn" gorm:"type:varchar(50);index;comment:故障机SN"`
	BackupDeviceSN string    `json:"backup_device_sn" gorm:"type:varchar(50);index;comment:备机SN"`
	TenantIP       string    `json:"tenant_ip" gorm:"type:varchar(50);comment:迁移的租户IP地址"`
	Status         string    `json:"status" gorm:"type:varchar(20);comment:迁移状态(success/failed)"`
	ExecutionTime  time.Time `json:"execution_time" gorm:"comment:执行时间"`
	Duration       int       `json:"duration" gorm:"comment:迁移耗时(秒)"`
	OperatorID     uint      `json:"operator_id" gorm:"comment:操作人ID"`
	OperatorName   string    `json:"operator_name" gorm:"type:varchar(50);comment:操作人姓名"`
	Comments       string    `json:"comments" gorm:"type:text;comment:备注信息"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// EntryPerson 入室人员记录
type EntryPerson struct {
	ID                   uint       `json:"id" gorm:"primaryKey"`
	TicketID             uint       `json:"ticket_id" gorm:"index;comment:关联的入室单ID"`
	Name                 string     `json:"name" gorm:"column:name;size:50;comment:姓名"`
	IdentificationNumber string     `json:"identification_number" gorm:"type:varchar(18);comment:人员身份证号码"`
	PersonType           string     `json:"person_type" gorm:"type:varchar(20);comment:人员类型"`
	Company              string     `json:"company" gorm:"type:varchar(50);comment:公司信息"`
	Telephone            string     `json:"telephone" gorm:"type:varchar(11);default:'';comment:联系方式"`
	EntryStartTime       *time.Time `json:"entry_start_time" gorm:"comment:入室开始时间"`
	EntryEndTime         *time.Time `json:"entry_end_time" gorm:"comment:入室结束时间"`
	Reason               string     `json:"reason" gorm:"type:varchar(50);comment:入室原因"`
	Status               string     `json:"status" gorm:"type:varchar(20);not null;default:'enabled';comment:人员状态"`
	CreatedAt            time.Time  `json:"created_at"`
	UpdatedAt            time.Time  `json:"updated_at"`
}

// TableName 指定表名
func (EntryPerson) TableName() string {
	return "entry_person"
}

// EntryApproval 入室单审批记录
type EntryApproval struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	TicketID     uint      `json:"ticket_id" gorm:"index;comment:关联的入室单ID"`
	TicketNo     string    `json:"ticketNo" gorm:"unique;type:varchar(50);not null;comment:工单号"`
	Status       string    `json:"status" gorm:"type:varchar(20);comment:审批状态(approved/rejected)"`
	ResponseTime time.Time `json:"response_time" gorm:"comment:响应时间"`
	Comments     string    `json:"comments" gorm:"type:text;comment:审批意见"`
	CustomerID   uint      `json:"customer_id" gorm:"comment:客户ID"`
	CustomerName string    `json:"customer_name" gorm:"type:varchar(50);comment:客户姓名"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// TableName 指定表名
func (EntryApproval) TableName() string {
	return "entry_approval"
}
