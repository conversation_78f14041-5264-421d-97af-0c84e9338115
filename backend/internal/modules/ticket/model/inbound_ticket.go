package model

import (
	"backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/cmdb/model/component"
	"backend/internal/modules/cmdb/model/inbound"
	"backend/internal/modules/cmdb/model/product"
	"mime/multipart"
	"time"

	"gorm.io/gorm"
)

type InboundTicketInterface interface {
	GetTicketType() string
}
type InboundTicketHistoryInterface interface {
	GetHistoryType() string
}

// 工单模板
type InboundTicketTemplate struct {
	gorm.Model
	InboundNo      string     `json:"inbound_no" gorm:"type:varchar(50);index;unique;commit:入库单号"`
	CompletedAt    *time.Time `json:"completed_at" gorm:"comment:工单完成时间"`
	Stage          string     `json:"stage" gorm:"type:varchar(50);comment:目前处在的阶段"`
	Status         string     `json:"status" gorm:"type:varchar(50);comment:工单状态"`
	PreviousStatus string     `json:"previous_status" gorm:"type:varchar(50);comment:工单状态"`
	TryCount       uint       `json:"try_count" gorm:"comment: 尝试次数"`
	Submitter      string     `json:"submitter" gorm:"varchar(50);not null;comment:提交人"`
	SubmitterID    uint       `json:"submitter_id" gorm:"comment:提交人 ID"`
}

// 工单历史模板
type InboundHistoryTemplate struct {
	gorm.Model
	InboundNo      string    `json:"inbound_no" gorm:"type:varchar(50);index;comment:订单号"`
	PreviousStatus string    `json:"previous_status" gorm:"type:varchar(50);comment:先前状态"`
	NewStatus      string    `json:"new_status" gorm:"type:varchar(50);comment:新状态"`
	OperatorID     uint      `json:"operator_id" gorm:"comment:操作人ID"`
	OperatorName   string    `json:"operator_name" gorm:"type:varchar(50);comment:操作人姓名"`
	OperationTime  time.Time `json:"operation_time" gorm:"comment:操作时间"`
	Comment        string    `json:"comment" gorm:"comment:操作备注"`
	Stage          string    `json:"stage" gorm:"type:varchar(50);comment:工单阶段"`
	TryCount       uint      `json:"try_count" gorm:"comment: 尝试次数"`
}

// InboundTicket 入库工单
type PartInboundTicket struct {
	gorm.Model
	InboundNo         string                     `json:"inbound_no" gorm:"type:varchar(50);commit:入库单号"`
	CompletedAt       *time.Time                 `json:"completed_at" gorm:"comment:工单完成时间"`
	PartInboundID     uint                       `json:"part_inbound_id" gorm:"commit:维修件入库单ID"`
	PartInbound       *inbound.PartInbound       `json:"part_inbound" gorm:"foreignKey:PartInboundID"`
	ComponentID       string                     `json:"component_id" gorm:"type:text;comment:组件ID（JSON数组）"`
	ServerComponent   *component.ServerComponent `json:"server_component" gorm:"-"`
	RepairTicketID    uint                       `json:"repair_ticket_id"`
	RepairTicket      *RepairTicket              `json:"repair_ticket" gorm:"foreignKey:RepairTicketID"`
	AssetID           string                     `json:"asset_id" gorm:"type:text;comment:资产ID（JSON数组）"`
	AssetSpare        *asset.AssetSpare          `json:"asset_spare" gorm:"-"`
	Submitter         string                     `json:"submitter" gorm:"varchar(50);not null;comment:提交人"`
	SubmitterID       uint                       `json:"submitter_id" gorm:"comment:提交人 ID"`
	Approver_before   string                     `json:"approver_before" gorm:"type:varchar(50);comment:审批人"` //使用map[string]bool
	ApproverID_before uint                       `json:"approver_id_before" gorm:"comment:审批人 ID"`            //使用map[string]string
	ApproverDescribe  string                     `json:"approver_describe" gorm:"comment:审批人批注"`
	Counter           string                     `json:"counter" gorm:"comment:清点人"`
	CounterID         uint                       `json:"counter_id" gorm:"comment:清点人ID"`
	Operator          string                     `json:"operator" gorm:"type:varchar(50);comment:入库操作人姓名"`
	OperatorID        uint                       `json:"operator_id" gorm:"comment:入库操作人ID"`
	Approver_after    string                     `json:"approver_after" gorm:"type:varchar(50);comment:审批人"`
	ApproverID_after  uint                       `json:"approver_id_after" gorm:"comment:审批人 ID"`
	Stage             string                     `json:"stage" gorm:"comment:目前处在的阶段"`
	Status            string                     `json:"status" gorm:"type:varchar(50);comment:工单状态"`
	PreviousStatus    string                     `json:"previous_status" gorm:"type:varchar(50);comment:工单状态"`
	TryCount          uint                       `json:"try_count" gorm:"comment:工单打回次数"`
	Lock              bool                       `json:"lock" gorm:"comment:工单锁定状态"`
}

func (t *PartInboundTicket) GetTicketType() string {
	return "part"
}

func (PartInboundTicket) TableName() string { return "part_inbound_ticket" }

// NewInboundTicket 新购入库工单
type NewInboundTicket struct {
	// 明确用户流程需求后字段
	gorm.Model
	InboundNo   string     `gorm:"type:varchar(50);index;unique;comment:入库单号"`
	CompletedAt *time.Time `json:"completed_at" gorm:"comment:工单完成时间"`
	//NewInboundID       uint                   `json:"new_inbound_id" gorm:"commit:新件入库单ID"`
	//NewInbound         inbound.NewInbound     `json:"new_inbound" gorm:"foreignKey:NewInboundID"`
	Submitter          string                 `json:"submitter" gorm:"varchar(50);not null;comment:提交人"`
	SubmitterID        uint                   `json:"submitter_id" gorm:"comment:提交人 ID"`
	AssetApprover      string                 `json:"asset_approver" gorm:"comment:资产管理员"`
	AssetApproverID    uint                   `json:"asset_approver_id" gorm:"comment:资产管理员ID"`
	EngineerApprover   string                 `json:"engineer_approver" gorm:"comment:工程师"`
	EngineerApproverID uint                   `json:"engineer_approver_id" gorm:"comment: 工程师ID"`
	Stage              string                 `json:"stage" gorm:"type:varchar(50);comment:目前处在的阶段"`
	Status             string                 `json:"status" gorm:"type:varchar(50);comment:工单状态"`
	PreviousStatus     string                 `json:"previous_status" gorm:"type:varchar(50);comment:工单状态"`
	History            []InboundTicketHistory `gorm:"foreignKey:InboundNo;references:InboundNo"`
	TryCount           uint                   `json:"try_count" gorm:"comment: 尝试次数，为后续重新编辑留拓展空间"`
	FileID             uint                   `gorm:"-:query;comment:关联的文件ID,查询时忽略这个字段"`
	// 下方字段为假设用户开发需求时设计的字段
	Counter           string `json:"counter" gorm:"comment:清点人"`
	CounterID         uint   `json:"counter_id" gorm:"comment:清点人ID"`
	Operator          string `json:"operator" gorm:"type:varchar(50);comment:入库操作人姓名"`
	OperatorID        uint   `json:"operator_id" gorm:"comment:入库操作人ID"`
	Approver_before   string `json:"approver_before" gorm:"type:varchar(50);comment:审批人"` //使用map[string]bool
	ApproverID_before uint   `json:"approver_id_before" gorm:"comment:审批人 ID"`            //使用map[string]string
	Approver_after    string `json:"approver_after" gorm:"type:varchar(50);comment:审批人"`
	ApproverID_after  uint   `json:"approver_id_after" gorm:"comment:审批人 ID"`
}

func (NewInboundTicket) TableName() string { return "new_inbound_ticket" }

func (t *NewInboundTicket) GetTicketType() string {
	return inbound.TypeNewPartInbound
}

// RepairPartInboundTicket 返修入库工单
type RepairPartInboundTicket struct {
	gorm.Model
	InboundNo   string     `gorm:"type:varchar(50);index;unique;comment:入库单号"`
	CompletedAt *time.Time `json:"completed_at" gorm:"comment:工单完成时间"`
	//RepairInboundID    uint                   `json:"repair_inbound_id" gorm:"commit:新件入库单ID"`
	//RepairInbound      inbound.RepairInbound  `json:"repair_inbound" gorm:"foreignKey:RepairInboundID"`
	Submitter          string                    `json:"submitter" gorm:"varchar(50);not null;comment:提交人"`
	SubmitterID        uint                      `json:"submitter_id" gorm:"comment:提交人 ID"`
	AssetApprover      string                    `json:"asset_approver" gorm:"comment:资产管理员"`
	AssetApproverID    uint                      `json:"asset_approver_id" gorm:"comment:资产管理员ID"`
	EngineerApprover   string                    `json:"engineer_approver" gorm:"comment:工程师"`
	EngineerApproverID uint                      `json:"engineer_approver_id" gorm:"comment: 工程师ID"`
	Stage              string                    `json:"stage" gorm:"type:varchar(50);comment:目前处在的阶段"`
	Status             string                    `json:"status" gorm:"type:varchar(50);comment:工单状态"`
	PreviousStatus     string                    `json:"previous_status" gorm:"type:varchar(50);comment:工单状态"`
	History            []RepairPartTicketHistory `gorm:"foreignKey:InboundNo;references:InboundNo"`
	TryCount           uint                      `json:"try_count" gorm:"comment: 尝试次数"`
}

func (RepairPartInboundTicket) TableName() string { return "repair_inbound_ticket" }

func (t *RepairPartInboundTicket) GetTicketType() string {
	return inbound.TypeRepairedPartInbound
}

// DismantledPartInboundTicket 拆机配件入库工单
type DismantledPartInboundTicket struct {
	// 导入标准模板
	InboundTicketTemplate
	History []DismantledPartTicketHistory `gorm:"foreignKey:InboundNo;references:InboundNo"`
}

func (DismantledPartInboundTicket) TableName() string { return "dismantled_part_ticket" }

func (d *DismantledPartInboundTicket) GetTicketType() string {
	return inbound.TypeDismantledPartInbound
}

// 拆机配件入库工单历史记录
type DismantledPartTicketHistory struct {
	// 套用入库历史模板
	InboundHistoryTemplate
}

func (DismantledPartTicketHistory) TableName() string { return "dismantled_part_ticket_history" }

func (d *DismantledPartTicketHistory) GetHistoryType() string {
	return inbound.TypeDismantledPartInbound
}

// InboundTicketHistory 入库工单历史记录
// BUG 发现不能混用，还是得分开
type InboundTicketHistory struct {
	gorm.Model
	InboundNo      string    `json:"inbound_no" gorm:"type:varchar(50);index;comment:订单号"`
	PreviousStatus string    `json:"previous_status" gorm:"type:varchar(50);comment:先前状态"`
	NewStatus      string    `json:"new_status" gorm:"type:varchar(50);comment:新状态"`
	OperatorID     uint      `json:"operator_id" gorm:"comment:操作人ID"`
	OperatorName   string    `json:"operator_name" gorm:"type:varchar(50);comment:操作人姓名"`
	OperationTime  time.Time `json:"operation_time" gorm:"comment:操作时间"`
	Comment        string    `json:"comment" gorm:"comment:操作备注"`
	Stage          string    `json:"stage" gorm:"type:varchar(50);comment:工单阶段"`
	TryCount       uint      `json:"try_count" gorm:"comment: 尝试次数"`
}

func (InboundTicketHistory) TableName() string { return "inbound_ticket_history" }

func (h *InboundTicketHistory) GetHistoryType() string {
	return inbound.TypeNewPartInbound
}

type RepairPartTicketHistory struct {
	gorm.Model
	InboundNo      string    `json:"inbound_no" gorm:"type:varchar(50);index;comment:订单号"`
	PreviousStatus string    `json:"previous_status" gorm:"type:varchar(50);comment:先前状态"`
	NewStatus      string    `json:"new_status" gorm:"type:varchar(50);comment:新状态"`
	OperatorID     uint      `json:"operator_id" gorm:"comment:操作人ID"`
	OperatorName   string    `json:"operator_name" gorm:"type:varchar(50);comment:操作人姓名"`
	OperationTime  time.Time `json:"operation_time" gorm:"comment:操作时间"`
	Comment        string    `json:"comment" gorm:"comment:操作备注"`
	Stage          string    `json:"stage" gorm:"type:varchar(50);comment:工单阶段"`
	TryCount       uint      `json:"try_count" gorm:"comment: 尝试次数"`
}

func (h *RepairPartTicketHistory) GetHistoryType() string {
	return inbound.TypeRepairedPartInbound
}

func (RepairPartTicketHistory) TableName() string { return "repair_part_ticket_history" }

// 设备入库工单
type DeviceInboundTicket struct {
	InboundTicketTemplate
	AssetApprover      string                       `json:"asset_approver" gorm:"comment:资产管理员"`
	AssetApproverID    uint                         `json:"asset_approver_id" gorm:"comment:资产管理员ID"`
	EngineerApprover   string                       `json:"engineer_approver" gorm:"comment:工程师"`
	EngineerApproverID uint                         `json:"engineer_approver_id" gorm:"comment: 工程师ID"`
	History            []DeviceInboundTicketHistory `gorm:"foreignKey:InboundNo;references:InboundNo"`
}

func (DeviceInboundTicket) TableName() string { return "device_inbound_ticket" }

func (d *DeviceInboundTicket) GetTicketType() string {
	return inbound.TypeDeviceInbound
}

type DeviceInboundTicketHistory struct {
	InboundHistoryTemplate
}

func (DeviceInboundTicketHistory) TableName() string { return "device_inbound_ticket_history" }

func (d *DeviceInboundTicketHistory) GetHistoryType() string {
	return inbound.TypeDeviceInbound
}

// 入库单
type InboundTicket struct {
	ID            uint             `json:"id" gorm:"primarykey"`
	CreatedAt     time.Time        `json:"created_at"`
	UpdatedAt     time.Time        `json:"updated_at"`
	DeletedAt     gorm.DeletedAt   `json:"-" gorm:"index"`
	Project       string           `json:"project" gorm:"comment:项目"`
	InboundTitle  string           `json:"inbound_title" gorm:"comment:入库标题"`
	InboundNo     string           `json:"inbound_no" gorm:"comment:入库单号"`
	InboundType   string           `json:"inbound_type" gorm:"comment:入库类型"`
	InboundReason string           `json:"inbound_reason" gorm:"comment:入库原因"`
	Info          []InboundInfo    `json:"info" gorm:"入库信息"`
	Detail        []InboundDetail  `json:"detail" gorm:"入库详情"`
	WarehouseID   uint             `json:"warehouse_id" gorm:"comment:关联仓库ID"`
	Warehouse     *asset.Warehouse `json:"warehouse" gorm:"foreignKey:WarehouseID"`
	CreateBy      string           `json:"create_by" gorm:"comment:创建人"`
	CreateID      uint             `json:"create_id" gorm:"comment:创建人ID"`
	Status        string           `json:"status" gorm:"comment:状态"`
	Stage         string           `json:"stage" gorm:"comment:阶段"`

	// 新购入库
	TrackingInfo string     `gorm:"type:text;comment:物流信息" json:"tracking_info"`
	MayArriveAt  *time.Time `gorm:"comment:预计到达时间;default:null" json:"may_arrive_at"`
	PurchaseNo   string     `gorm:"comment:采购入库单" json:"purchase_no"`

	// 拆机入库
	NeedReturn bool `json:"need_return" gorm:"comment:是否需要返厂"`

	Valid bool `json:"valid" gorm:"comment:是否需要工程师验证;default:false"`

	// 关联启动
	RepairTicketID   uint `json:"repair_ticket_id" gorm:"comment:关联的维修工单ID"`
	OutboundTicketID uint `json:"outbound_ticket_id" gorm:"comment:关联出库工单ID"`
}

func (InboundTicket) TableName() string {
	return "inbound_ticket"
}

type InboundInfo struct {
	ID              uint           `json:"id" gorm:"primarykey"`
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"-" gorm:"index"`
	InboundTicketID uint           `json:"inbound_ticket_id" gorm:"comment:入库单ID"`
	InboundNo       string         `json:"inbound_no" gorm:"comment:入库单号"`
	Amount          int            `json:"amount" gorm:"comment:数量"`

	// 备件信息
	ProductID    uint              `json:"product_id" gorm:"comment:规格ID;default:null"`
	Product      *product.Product  `json:"product" gorm:"foreignKey:ProductID"`
	//SpareAssetID uint              `json:"spare_asset_id" gorm:"comment:备件ID;default:null"`
	//SpareAsset   *asset.AssetSpare `json:"spare_asset" gorm:"foreignKey:SpareAssetID"`

	// 整机设备信息
	//TemplateID uint                      `json:"template_id" gorm:"comment:模板ID;default:null"`
	//Template   *template.MachineTemplate `json:"template" gorm:"foreignKey:TemplateID"`
	//Brand      string                    `json:"brand" gorm:"comment:厂商"`
	//Model      string                    `json:"model" gorm:"comment:型号"`
	//AssetType  string                    `json:"asset_type" gorm:"comment:资产类型"`
	//DeviceID   uint                      `json:"device_id" gorm:"comment:设备ID;default:null"`
	//Device     *asset.Device             `json:"device" gorm:"foreignKey:DeviceID"`
}

func (InboundInfo) TableName() string {
	return "inbound_info"
}

type InboundDetail struct {
	ID              uint           `json:"id" gorm:"primarykey"`
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"-" gorm:"index"`
	InboundTicketID uint           `json:"inbound_ticket_id" gorm:"comment:入库单ID"`
	InboundNo       string         `json:"inbound_no" gorm:"comment:入库单号"`

	// 信息
	ProductID    uint              `json:"product_id" gorm:"comment:规格ID;default:null"`
	Product      *product.Product  `json:"product" gorm:"foreignKey:ProductID"`
	//SpareAssetID uint              `json:"spare_asset_id" gorm:"comment:备件ID;default:null"`
	//SpareAsset   *asset.AssetSpare `json:"spare_asset" gorm:"foreignKey:SpareAssetID"`

	// 整机设备信息
	//TemplateID uint                      `json:"template_id" gorm:"comment:模板ID;default:null"`
	//Template   *template.MachineTemplate `json:"template" gorm:"foreignKey:TemplateID"`
	//Brand      string                    `json:"brand" gorm:"comment:厂商"`
	//Model      string                    `json:"model" gorm:"comment:型号"`
	//AssetType  string                    `json:"asset_type" gorm:"comment:资产类型"`
	//DeviceID   uint                      `json:"device_id" gorm:"comment:设备ID;default:null"`
	//Device     *asset.Device             `json:"device" gorm:"foreignKey:DeviceID"`

	// SN
	DeviceSN    string `json:"device_sn" gorm:"type:varchar(50);comment:主设备SN"`
	ComponentSN string `json:"component_sn" gorm:"type:varchar(50);comment:配件SN"`

	// 拆机
	ComponentState string `json:"component_state" gorm:"comment:资产状态" example:"正常 | 故障 | 维护 | 报废"`
	NeedReturn     bool   `json:"need_return" gorm:"comment:是否需要返厂"`

	// 返修
	ReturnRepairType string `json:"return_repair_type" gorm:"type:varchar(20);comment:返修类型" example:"维修 | 换新"`
	NewComponentSN   string `json:"new_component_sn" gorm:"type:varchar(50);comment:新配件的SN;default:null"`
}

func (InboundDetail) TableName() string {
	return "inbound_detail"
}

// 入库历史
type InboundHistory struct {
	ID              uint           `json:"id" gorm:"primarykey"`
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"-" gorm:"index"`
	InboundTicketID uint           `json:"inbound_ticket_id" gorm:"comment:工单ID"`
	InboundNo       string         `json:"inbound_no" gorm:"type:varchar(50);index;comment:订单号"`
	Stage           string         `json:"stage" gorm:"type:varchar(50);comment:工单阶段"`
	Status          string         `json:"status" gorm:"type:varchar(50);comment:新状态"`
	OperatorID      uint           `json:"operator_id" gorm:"comment:操作人ID"`
	OperatorName    string         `json:"operator_name" gorm:"type:varchar(50);comment:操作人姓名"`
	Comment         string         `json:"comment" gorm:"comment:操作备注"`
}

func (InboundHistory) TableName() string {
	return "inbound_history"
}

// GetListQuery 查询参数
type GetListQuery struct {
	Page          int    `json:"page"`
	PageSize      int    `json:"page_size"`
	Status        string `json:"status"`
	OrderCategory string `json:"order_category"`
}

// InboundTrigger 入库触发器
type InboundTrigger struct {
	Stage            string                 `json:"stage"`
	Status           string                 `json:"status" binding:"required"`
	OperatorID       uint                   `json:"operator"`
	OperatorName     string                 `json:"operator_name"`
	Comments         string                 `json:"comments"`
	RequiredApproval bool                   `json:"required_approval"`
	RequireVerified  bool                   `json:"require_verified"`
	Data             map[string]interface{} `json:"data"`
}

// ListParams 工单列表请求参数
type ListParams struct {
	Page            int      `json:"page" form:"page" validate:"required,min=1"`                                                   // 当前页码
	PageSize        int      `json:"pageSize" form:"pageSize" validate:"required,min=1,max=100"`                                   // 每页条数
	InboundNo       string   `json:"inboundNo,omitempty" form:"inboundNo"`                                                         // 入库单号
	SN              string   `json:"sn,omitempty" form:"sn"`                                                                       // SN号
	InboundType     string   `json:"inboundType" form:"inboundType" validate:"required,oneof=new part"`                            // 入库单类型
	InboundUser     string   `json:"inboundUser,omitempty" form:"inboundUser"`                                                     // 入库人员
	InboundStatus   string   `json:"inboundStatus,omitempty" form:"inboundStatus"`                                                 // 入库状态
	PurchaseOrderNo string   `json:"purchaseOrderNo,omitempty" form:"purchaseOrderNo"`                                             // 采购单号
	InboundTime     []string `json:"inboundTime,omitempty" form:"inboundTime" validate:"omitempty,len=2,dive,datetime=2006-01-02"` // 入库时间范围 [开始时间, 结束时间]
}

type WorkflowReq struct {
	InboundNo   string `json:"inbound_no" binding:"required"`
	InboundType string `json:"inbound_type" binding:"required"`
	Verify      bool   `json:"verify"`
}

// DetailImportReq 导入请求结构体
type DetailImportReq struct {
	File *multipart.FileHeader `json:"file" form:"file" binding:"required"` // 上传的文件
}
