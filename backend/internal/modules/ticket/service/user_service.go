package service

import (
	"backend/internal/modules/user/model"
	userService "backend/internal/modules/user/service"
	"context"
	"fmt"
)

// UserService 用户服务接口
type UserService interface {
	// GetUserInfoByID 根据用户ID获取用户信息
	GetUserInfoByID(ctx context.Context, userID uint) (*model.UserInfo, error)
	GetUserName(ctx context.Context, userID uint) (string, error)
}

// userServiceImpl 用户服务实现
type userServiceImpl struct {
	userService userService.IUserService
}

// NewUserService 创建用户服务
func NewUserService(userService userService.IUserService) UserService {
	return &userServiceImpl{
		userService: userService,
	}
}

// GetUserInfoByID 根据用户ID获取用户信息
func (s *userServiceImpl) GetUserInfoByID(ctx context.Context, userID uint) (*model.UserInfo, error) {
	// 增加空指针检查
	if s.userService == nil {
		return nil, fmt.Errorf("用户服务未初始化")
	}

	// 安全调用，防止panic
	var userInfo *model.UserInfo
	var err error

	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("获取用户信息时发生错误: %v", r)
			userInfo = nil
		}
	}()

	userInfo, err = s.userService.GetUserInfo(ctx, userID)
	return userInfo, err
}

// GetUserName 根据用户ID获取用户名
func (s *userServiceImpl) GetUserName(ctx context.Context, userID uint) (string, error) {
	// 先检查是否为nil
	if s == nil {
		return fmt.Sprintf("用户%d", userID), fmt.Errorf("userServiceImpl为nil")
	}

	userInfo, err := s.GetUserInfoByID(ctx, userID)
	if err != nil {
		return fmt.Sprintf("用户%d", userID), err
	}

	// 检查userInfo是否为nil
	if userInfo == nil {
		return fmt.Sprintf("用户%d", userID), fmt.Errorf("用户信息不存在")
	}

	// 检查RealName是否为空
	if userInfo.RealName == "" {
		return fmt.Sprintf("用户%d", userID), nil
	}

	return userInfo.RealName, nil
}
