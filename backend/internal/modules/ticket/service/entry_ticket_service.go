package service

import (
	"backend/internal/modules/ticket/common"
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	"backend/internal/modules/ticket/workflow"
	"context"
	"crypto/rand"
	"encoding/binary"
	"fmt"
	"strings"
	"time"

	"go.temporal.io/sdk/client"
	"go.uber.org/zap"
)

// EntryTicketService 入室单服务接口
type EntryTicketService interface {
	CreateEntryTicket(ctx context.Context, ticket *model.EntryTicket) error
	GetEntryTicketByID(ctx context.Context, id uint) (*model.EntryTicket, error)
	GetEntryTicketByTicketNo(ctx context.Context, ticketNo string) (*model.EntryTicket, error)
	UpdateEntryTicket(ctx context.Context, ticket *model.EntryTicket) error
	UpdateEntryTicketFields(ctx context.Context, id uint, fields map[string]interface{}) error
	UpdateEntryTicketStatus(ctx context.Context, id uint, status string, operatorID uint, operatorName string) error
	CloseEntryTicket(ctx context.Context, id uint, summary string) error
	ListEntryTickets(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*model.EntryTicket, int64, error)
	GetEntryTicketStatusHistory(ctx context.Context, ticketID uint) ([]*model.EntryTicketStatusHistory, error)
	StartEntryTicketWorkflow(ctx context.Context, ticketID uint) error
	RecoverInconsistentWorkflows(ctx context.Context) error
	TriggerWorkflowStage(ctx context.Context, ticketID uint, stage string, operatorID uint, operatorName string, comments string, data map[string]interface{}) error
	CreateEntryPerson(ctx context.Context, selection *model.EntryPerson) error
	CreateEntryApproval(ctx context.Context, approval *model.EntryApproval) error
	GetApprovalById(ctx context.Context, id uint) (*model.EntryApproval, error)
	UpdateApproval(ctx context.Context, approval *model.EntryApproval) error
}

// 注释掉UserService接口定义，避免重复声明冲突
// UserService 用户服务接口
// type UserService interface {
// 	GetUserByID(ctx context.Context, id uint) (*model.User, error)
// }

// 注释掉User结构体定义，避免未定义错误
// User 基本用户模型，针对Ticket服务需要的字段
// type User struct {
// 	ID       uint   `json:"id"`
// 	Username string `json:"username"`
// 	RealName string `json:"real_name"`
// }

// entryTicketService 入室单服务实现
type entryTicketService struct {
	repo              repository.EntryTicketRepository
	entryPersonRepo   repository.EntryPersonRepository
	entryApprovalRepo repository.EntryApprovalRepository
	temporalClient    client.Client
	logger            *zap.Logger
	// 注释掉userService字段，避免未使用的服务
	// userService          UserService
}

// NewEntryTicketService 创建入室单服务
func NewEntryTicketService(
	repo repository.EntryTicketRepository,
	entryPersonRepo repository.EntryPersonRepository,
	entryApprovalRepo repository.EntryApprovalRepository,
	temporalClient client.Client,
	logger *zap.Logger,
	// 注释掉userService参数，避免未使用的服务
	// userService UserService,
) EntryTicketService {
	return &entryTicketService{
		repo:              repo,
		entryPersonRepo:   entryPersonRepo,
		entryApprovalRepo: entryApprovalRepo,
		temporalClient:    temporalClient,
		logger:            logger,
		// 注释掉userService字段，避免未使用的服务
		// userService:          userService,
	}
}

// 工作流故障重试相关实现

// recordWorkflowFailure 记录工作流启动失败，以便后续重试
//
//nolint:unused
//func (s *entryTicketService) recordWorkflowFailure(ctx context.Context, ticketID uint, errMsg string) {
//	if ticketID == 0 {
//		fmt.Printf("警告: 尝试记录工作流失败，但工单ID为0，操作取消\n")
//		return
//	}
//
//	// 获取当前时间
//	now := time.Now()
//
//	fmt.Printf("记录工作流失败信息: 工单ID=%d, 错误=%s\n", ticketID, errMsg)
//
//	// 创建带有超时的上下文
//	dbCtx, cancel := context.WithTimeout(ctx, 3*time.Second)
//	defer cancel()
//
//	// 更新入室单的备注字段，添加工作流失败信息
//	ticket, err := s.repo.GetByID(dbCtx, ticketID)
//	if err != nil {
//		fmt.Printf("记录工作流失败信息时无法获取工单(ID=%d): %v\n", ticketID, err)
//		return
//	}
//
//	if ticket == nil {
//		fmt.Printf("记录工作流失败信息时工单不存在(ID=%d)\n", ticketID)
//		return
//	}
//
//	// 添加工作流失败信息到备注
//	newRemark := fmt.Sprintf("[%s] 工作流启动失败: %s", now.Format("2006-01-02 15:04:05"), errMsg)
//	if ticket.Remarks != "" {
//		ticket.Remarks = ticket.Remarks + "\n" + newRemark
//	} else {
//		ticket.Remarks = newRemark
//	}
//
//	// 添加重试标记
//	ticket.NeedsWorkflowRetry = true
//	ticket.LastWorkflowRetryTime = &now
//
//	// 确保不会修改其他关键字段
//	originalID := ticket.ID
//	originalTicketNo := ticket.TicketNo
//	originalStatus := ticket.Status
//
//	// 使用安全的更新方式，仅更新备注和重试相关字段
//	updateData := map[string]interface{}{
//		"remarks":                  ticket.Remarks,
//		"needs_workflow_retry":     ticket.NeedsWorkflowRetry,
//		"last_workflow_retry_time": ticket.LastWorkflowRetryTime,
//	}
//
//	// 使用直接更新特定字段的方式而不是整体更新
//	updateCtx, updateCancel := context.WithTimeout(ctx, 3*time.Second)
//	defer updateCancel()
//
//	err = s.repo.UpdateFields(updateCtx, ticketID, updateData)
//	if err != nil {
//		fmt.Printf("无法更新工单(ID=%d)的工作流失败信息: %v\n", ticketID, err)
//		return
//	}
//
//	// 验证更新结果
//	verifyCtx, verifyCancel := context.WithTimeout(ctx, 2*time.Second)
//	defer verifyCancel()
//
//	updatedTicket, err := s.repo.GetByID(verifyCtx, ticketID)
//	if err != nil {
//		fmt.Printf("验证更新后的工单失败(ID=%d): %v\n", ticketID, err)
//		return
//	}
//
//	if updatedTicket.ID != originalID || updatedTicket.TicketNo != originalTicketNo || updatedTicket.Status != originalStatus {
//		fmt.Printf("警告: 工单关键字段在更新后发生变化(ID=%d)！\n", ticketID)
//	} else {
//		fmt.Printf("成功更新工单工作流失败信息(ID=%d)\n", ticketID)
//	}
//}

// retryFailedWorkflows 重试失败的工作流
// 该方法可以在定时任务中调用
//
//nolint:unused
//func (s *entryTicketService) retryFailedWorkflows(ctx context.Context) {
//	logger := activity.GetLogger(ctx)
//	logger.Info("开始重试失败的工作流")
//
//	// 查询需要重试的工单
//	tickets, err := s.repo.FindTicketsForRetry(ctx)
//	if err != nil {
//		logger.Error("查询需要重试的工单失败", "error", err)
//		return
//	}
//
//	if len(tickets) == 0 {
//		logger.Info("没有需要重试的工单")
//		return
//	}
//
//	logger.Info("找到需要重试的工单", "count", len(tickets))
//
//	// 遍历需要重试的工单
//	for _, ticket := range tickets {
//		// 检查工单状态，如果是终态状态则不重试
//		if ticket.Status == common.StatusCompleted || ticket.Status == common.StatusCancelled {
//			logger.Info("工单已处于终态状态，不再重试",
//				"ticketID", ticket.ID,
//				"status", ticket.Status)
//
//			// 更新工单，清除重试标记
//			if err := s.repo.UpdateFields(ctx, ticket.ID, map[string]interface{}{
//				"needs_workflow_retry": false,
//			}); err != nil {
//				logger.Error("清除工单重试标记失败",
//					"ticketID", ticket.ID,
//					"error", err)
//			}
//			continue
//		}
//
//		// 检查重试次数是否超过限制
//		if ticket.WorkflowRetryCount >= 3 {
//			logger.Warn("工单重试次数超过限制，不再重试",
//				"ticketID", ticket.ID,
//				"ticketNo", ticket.TicketNo,
//				"retryCount", ticket.WorkflowRetryCount)
//			continue
//		}
//
//		// 更新重试相关字段
//		if err := s.repo.UpdateFields(ctx, ticket.ID, map[string]interface{}{
//			"needs_workflow_retry":     false,
//			"last_workflow_retry_time": time.Now(),
//			"workflow_retry_count":     ticket.WorkflowRetryCount + 1,
//		}); err != nil {
//			logger.Error("更新工单重试字段失败",
//				"ticketID", ticket.ID,
//				"error", err)
//			continue
//		}
//
//		// 重新启动工作流
//		input := workflow.FaultTicketWorkflowInput{
//			TicketID:         ticket.ID,
//			ReporterID:       ticket.ReporterID,
//			ReporterName:     ticket.ReporterName,
//			FaultType:        ticket.FaultType,
//			FaultDescription: ticket.FaultDescription,
//			Symptom:          ticket.Symptom,
//			SlotPosition:     ticket.SlotPosition,
//			Priority:         ticket.Priority,
//			Source:           ticket.Source,
//			DeviceSN:         ticket.DeviceSN,
//			ComponentSN:      ticket.ComponentSN,
//			ComponentType:    ticket.ComponentType,
//			Status:           ticket.Status,
//			TicketNo:         ticket.TicketNo,
//			Completed:        false, // 确保明确设置为false
//		}
//
//		workflowID := fmt.Sprintf("%s-%d-%d", ticket.TicketNo, ticket.ID, ticket.WorkflowRetryCount+1)
//		workflowOptions := client.StartWorkflowOptions{
//			ID:        workflowID,
//			TaskQueue: "fault_ticket_workflow",
//		}
//
//		_, err = s.temporalClient.ExecuteWorkflow(ctx, workflowOptions, workflow.FaultTicketWorkflow, input)
//		if err != nil {
//			logger.Error("重启工作流失败",
//				"ticketID", ticket.ID,
//				"workflowID", workflowID,
//				"error", err)
//
//			// 如果启动失败，重新标记为需要重试
//			if err := s.repo.UpdateFields(ctx, ticket.ID, map[string]interface{}{
//				"needs_workflow_retry": true,
//			}); err != nil {
//				logger.Error("更新工单重试标记失败",
//					"ticketID", ticket.ID,
//					"error", err)
//			}
//			continue
//		}
//
//		logger.Info("成功重启工作流",
//			"ticketID", ticket.ID,
//			"ticketNo", ticket.TicketNo,
//			"workflowID", workflowID,
//			"retryCount", ticket.WorkflowRetryCount+1)
//	}
//
//	logger.Info("完成工作流重试")
//}

// CreateEntryTicket 创建入室单
func (s *entryTicketService) CreateEntryTicket(ctx context.Context, ticket *model.EntryTicket) error {
	// 生成工单号
	ticket.TicketNo = s.generateTicketNo()

	// 设置初始状态
	ticket.Status = common.StatusWaitingApproval

	// 创建入室单
	if err := s.repo.Create(ctx, ticket); err != nil {
		return fmt.Errorf("创建入室单失败: %w", err)
	}

	// 记录状态历史
	history := &model.EntryTicketStatusHistory{
		EntryTicketID:  ticket.ID,
		PreviousStatus: "",
		NewStatus:      ticket.Status,
		OperatorID:     ticket.ApplicantID,
		OperatorName:   ticket.ApplicantName,
		OperationTime:  time.Now(),
		Remarks:        "创建入室单",
	}
	if err := s.repo.CreateStatusHistory(ctx, history); err != nil {
		// 仅记录日志，不影响主流程
		fmt.Printf("记录状态历史失败: %v\n", err)
	}

	// 启动工作流
	if s.temporalClient != nil {
		workflowID := fmt.Sprintf("entry_ticket_%d", ticket.ID)
		_, err := s.temporalClient.ExecuteWorkflow(ctx, client.StartWorkflowOptions{
			ID:        workflowID,
			TaskQueue: workflow.EntryTicketTaskQueue,
		}, workflow.EntryTicketWorkflow, workflow.EntryTicketWorkflowInput{
			TicketID:  ticket.ID,
			Status:    ticket.Status,
			TicketNo:  ticket.TicketNo,
			Completed: false, // 明确设置为false
		})
		if err != nil {
			// 仅记录日志，不影响主流程
			fmt.Printf("启动工作流失败: %v\n", err)
		}
	}

	return nil
}

// GetEntryTicketByID 根据ID获取入室单
func (s *entryTicketService) GetEntryTicketByID(ctx context.Context, id uint) (*model.EntryTicket, error) {
	return s.repo.GetByID(ctx, id)
}

// GetEntryTicketByTicketNo 根据工单号获取入室单
func (s *entryTicketService) GetEntryTicketByTicketNo(ctx context.Context, ticketNo string) (*model.EntryTicket, error) {
	return s.repo.GetByTicketNo(ctx, ticketNo)
}

// UpdateEntryTicket 更新入室单
func (s *entryTicketService) UpdateEntryTicket(ctx context.Context, ticket *model.EntryTicket) error {
	return s.repo.Update(ctx, ticket)
}

// UpdateEntryTicketFields 更新入室单指定字段
func (s *entryTicketService) UpdateEntryTicketFields(ctx context.Context, id uint, fields map[string]interface{}) error {
	// 使用Repository提供的UpdateFields方法
	return s.repo.UpdateFields(ctx, id, fields)
}

// UpdateEntryTicketStatus 更新入室单状态
func (s *entryTicketService) UpdateEntryTicketStatus(ctx context.Context, id uint, status string, operatorID uint, operatorName string) error {
	// 使用事务处理状态更新
	return s.repo.WithTransaction(ctx, func(txCtx context.Context, repo repository.EntryTicketRepository) error {
		// 获取当前入室单
		ticket, err := repo.GetByID(txCtx, id)
		if err != nil {
			s.logger.Error("获取入室单失败",
				zap.Error(err),
				zap.Uint("ticketID", id))
			return fmt.Errorf("获取入室单失败: %w", err)
		}

		// 如果状态没有变化，则直接返回
		if ticket.Status == status {
			s.logger.Info("入室单状态未变化，无需更新",
				zap.Uint("ticketID", id),
				zap.String("status", status))
			return nil
		}

		previousStatus := ticket.Status

		// 更新工单状态相关字段
		ticket.Status = status
		ticket.WaitingManualTrigger = false // 更新状态时清除等待标记
		ticket.CurrentWaitingStage = ""     // 清除当前等待阶段

		// 根据新状态更新工单时间字段
		now := time.Now()
		switch status {
		case common.StatusCompleted:
			if ticket.CloseTime == nil {
				ticket.CloseTime = &now
			}
		}

		// 更新入室单状态
		if err := repo.Update(txCtx, ticket); err != nil {
			s.logger.Error("更新入室单状态失败",
				zap.Error(err),
				zap.Uint("ticketID", id),
				zap.String("oldStatus", previousStatus),
				zap.String("newStatus", status))
			return fmt.Errorf("更新入室单状态失败: %w", err)
		}

		// 记录状态历史
		history := &model.EntryTicketStatusHistory{
			EntryTicketID:    id,
			PreviousStatus:   previousStatus,
			NewStatus:        status,
			OperatorID:       operatorID,
			OperatorName:     operatorName,
			OperationTime:    now,
			Remarks:          fmt.Sprintf("状态从 %s 变更为 %s", previousStatus, status),
			ActivityCategory: getEntryActivityCategory(status),
		}

		// 如果是系统操作，添加标记
		if operatorID == 0 {
			history.OperatorName = "系统"
			history.Remarks = fmt.Sprintf("系统自动将状态从 %s 变更为 %s", previousStatus, status)
		}

		if err := repo.CreateStatusHistory(txCtx, history); err != nil {
			s.logger.Error("记录状态历史失败",
				zap.Error(err),
				zap.Uint("ticketID", id),
				zap.String("oldStatus", previousStatus),
				zap.String("newStatus", status))
			// 仅记录日志，不影响主流程
			return nil
		}

		s.logger.Info("入室单状态更新成功",
			zap.Uint("ticketID", id),
			zap.String("oldStatus", previousStatus),
			zap.String("newStatus", status),
			zap.String("operator", operatorName))

		return nil
	})
}

// getEntryActivityCategory 根据状态获取活动类别
func getEntryActivityCategory(status string) string {
	switch status {
	case common.StatusWaitingApproval, common.StatusWaitingSecondApproval:
		return "approval"
	default:
		return "status_change"
	}
}

// isSLAPauseStatus 判断是否为SLA暂停状态
//func isSLAPauseStatus(status string) bool {
//	switch status {
//	case common.StatusWaitingApproval, common.StatusApprovedWaiting:
//		return true
//	default:
//		return false
//	}
//}

// getPauseReason 获取暂停原因
//func getPauseReason(status string) string {
//	switch status {
//	case common.StatusWaitingApproval:
//		return "等待客户审批"
//	case common.StatusApprovedWaiting:
//		return "等待开始维修"
//	default:
//		return ""
//	}
//}

// CloseEntryTicket 关闭入室单
func (s *entryTicketService) CloseEntryTicket(ctx context.Context, id uint, summary string) error {
	// 获取当前入室单
	ticket, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("获取入室单失败: %w", err)
	}

	// 更新状态为已完成
	ticket.Status = common.StatusCompleted
	now := time.Now()
	ticket.CloseTime = &now

	if err := s.repo.Update(ctx, ticket); err != nil {
		return fmt.Errorf("更新入室单状态失败: %w", err)
	}

	// 记录状态历史
	history := &model.EntryTicketStatusHistory{
		EntryTicketID:  id,
		PreviousStatus: ticket.Status,
		NewStatus:      common.StatusCompleted,
		OperatorID:     ticket.ApplicantID,
		OperatorName:   ticket.ApplicantName,
		OperationTime:  now,
		Remarks:        fmt.Sprintf("关闭入室单: %s", summary),
	}
	if err := s.repo.CreateStatusHistory(ctx, history); err != nil {
		// 仅记录日志，不影响主流程
		fmt.Printf("记录状态历史失败: %v\n", err)
	}

	return nil
}

// ListEntryTickets 分页获取入室单列表
func (s *entryTicketService) ListEntryTickets(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*model.EntryTicket, int64, error) {
	// 解析基本筛选条件
	var query string
	if queryVal, ok := filters["query"].(string); ok {
		query = queryVal
	}

	// 处理status参数，支持字符串或字符串数组
	var statusValues []string
	if statusArray, ok := filters["status"].([]string); ok && len(statusArray) > 0 {
		statusValues = statusArray
	} else if status, ok := filters["status"].(string); ok && status != "" {
		statusValues = []string{status}
	}

	startTime, ok := filters["startTime"].(*time.Time)
	if !ok {
		startTime = nil
	}
	endTime, ok := filters["endTime"].(*time.Time)
	if !ok {
		endTime = nil
	}

	// 构建查询条件
	conditions := make(map[string]interface{})

	// 处理原有的查询条件
	if query != "" {
		// 原有的query支持搜索设备SN和工单编号
		conditions["query"] = query
	}

	// 处理状态条件
	if len(statusValues) > 0 {
		conditions["status"] = statusValues
	}

	// 处理时间范围
	if startTime != nil {
		conditions["start_time"] = startTime
	}
	if endTime != nil {
		conditions["end_time"] = endTime
	}

	return s.repo.List(ctx, page, pageSize, conditions)
}

// GetEntryTicketStatusHistory 获取入室单状态历史
func (s *entryTicketService) GetEntryTicketStatusHistory(ctx context.Context, id uint) ([]*model.EntryTicketStatusHistory, error) {
	return s.repo.GetStatusHistory(ctx, id)
}

// StartEntryTicketWorkflow 启动入室单工作流
func (s *entryTicketService) StartEntryTicketWorkflow(ctx context.Context, ticketID uint) error {
	// 检查temporalClient是否为nil
	if s.temporalClient == nil {
		// 如果temporalClient为nil，记录一个警告并返回，但不视为错误
		fmt.Printf("Temporal客户端未初始化，跳过工作流启动\n")
		return nil
	}

	// 创建带超时的上下文
	dbCtx, cancel := context.WithTimeout(ctx, 2*time.Second)
	defer cancel()

	// 获取入室单前记录操作
	fmt.Printf("准备获取工单(ID: %d)用于启动工作流\n", ticketID)

	// 获取入室单
	ticket, err := s.repo.GetByID(dbCtx, ticketID)
	if err != nil {
		// 添加详细错误日志，但不执行数据库修改操作
		fmt.Printf("获取入室单失败(ID: %d): %v\n", ticketID, err)
		return fmt.Errorf("获取入室单失败: %w", err)
	}

	if ticket == nil {
		fmt.Printf("工单不存在(ID: %d)\n", ticketID)
		return fmt.Errorf("工单不存在，无法启动工作流")
	}

	// 检查工单状态，如果是终态则不启动工作流
	if ticket.Status == common.StatusCompleted || ticket.Status == common.StatusCancelled {
		fmt.Printf("工单ID=%d已处于终态(%s)，不启动工作流\n", ticket.ID, ticket.Status)
		return nil
	}

	fmt.Printf("成功获取工单(ID: %d, TicketNo: %s)准备启动工作流\n", ticket.ID, ticket.TicketNo)

	// 创建工作流输入 - 传递完整的入室单关键信息
	input := workflow.EntryTicketWorkflowInput{
		TicketID:      ticket.ID,
		ApplicantID:   ticket.ApplicantID,
		ApplicantName: ticket.ApplicantName,
		TicketNo:      ticket.TicketNo,
		Status:        ticket.Status,
		Completed:     false, // 明确设置为false
	}

	// 启动工作流，使用原始上下文以确保有足够时间
	workflowOptions := client.StartWorkflowOptions{
		ID:                  fmt.Sprintf("%s_%d", workflow.EntryTicketWorkflowIDPrefix, ticketID),
		TaskQueue:           workflow.EntryTicketTaskQueue,
		WorkflowRunTimeout:  24 * time.Hour,   // 更改为24小时，防止过早超时
		WorkflowTaskTimeout: 30 * time.Second, // 更改为30秒
	}

	// 记录将要启动的工作流信息
	fmt.Printf("准备启动工作流，ID: %s, 工单ID: %d\n",
		workflowOptions.ID, ticketID)

	// 创建更短超时的上下文，避免长时间等待
	workflowCtx, workflowCancel := context.WithTimeout(ctx, 5*time.Second)
	defer workflowCancel()

	// 使用通道和goroutine组合实现带超时的工作流启动
	errChan := make(chan error, 1)
	go func() {
		we, err := s.temporalClient.ExecuteWorkflow(workflowCtx, workflowOptions, workflow.EntryTicketWorkflow, input)
		if err != nil {
			// 区分连接错误和业务逻辑错误
			if isTemporalConnectionError(err) {
				errChan <- fmt.Errorf("temporal服务连接失败: %w", err)
			} else {
				errChan <- fmt.Errorf("启动工作流失败: %w", err)
			}
			return
		}
		// 不等待工作流执行完成，只记录工作流ID
		fmt.Printf("成功启动工作流，ID: %s, RunID: %s\n", we.GetID(), we.GetRunID())
		errChan <- nil
	}()

	// 等待启动结果或超时
	select {
	case err := <-errChan:
		if err != nil {
			fmt.Printf("工作流启动失败: %v，但不会影响工单数据\n", err)
		}
		return err
	case <-time.After(4500 * time.Millisecond): // 比上下文超时稍短，确保我们先处理超时
		// 记录超时但不返回错误，让主流程继续
		fmt.Printf("工作流启动操作超时，工单ID: %d，将在后台继续尝试\n", ticketID)
		return nil
	}
}

// generateTicketNo 生成工单号
func (s *entryTicketService) generateTicketNo() string {
	// 使用时间戳和随机数生成工单号
	timestamp := time.Now().Format("20060102150405")
	// 使用crypto/rand代替math/rand
	randomBytes := make([]byte, 4)
	_, err := rand.Read(randomBytes)
	var random int
	if err != nil {
		// 如果加密随机数生成失败，使用时间纳秒作为备选
		random = int(time.Now().UnixNano() % 1000)
	} else {
		// 使用加密随机数，取模确保在0-999范围内
		random = int(binary.BigEndian.Uint32(randomBytes) % 1000)
	}
	return fmt.Sprintf("ep%s%03d", timestamp, random)
}

// UintPtr 辅助函数，返回指向uint的指针
//func UintPtr(v uint) *uint {
//	return &v
//}

// RecoverInconsistentWorkflows 恢复不一致的工作流状态
func (s *entryTicketService) RecoverInconsistentWorkflows(ctx context.Context) error {
	if s.temporalClient == nil {
		return fmt.Errorf("工作流客户端未初始化")
	}

	// 获取所有未完成的入室单
	tickets, _, err := s.repo.List(ctx, 1, 1000, map[string]interface{}{})
	if err != nil {
		return fmt.Errorf("获取入室单列表失败: %w", err)
	}

	for _, ticket := range tickets {
		// 检查工单状态，如果是终态则不重新启动工作流
		if ticket.Status == common.StatusApproved || ticket.Status == common.StatusRejected {
			fmt.Printf("工单ID=%d已处于终态(%s)，不重新启动工作流\n", ticket.ID, ticket.Status)
			continue
		}

		workflowID := fmt.Sprintf("%s_%d", workflow.EntryTicketWorkflowIDPrefix, ticket.ID)
		// 检查工作流是否存在
		_, err := s.temporalClient.DescribeWorkflowExecution(ctx, workflowID, "")
		if err != nil {
			// 工作流不存在，重新启动
			_, err := s.temporalClient.ExecuteWorkflow(ctx, client.StartWorkflowOptions{
				ID:        workflowID,
				TaskQueue: workflow.EntryTicketTaskQueue,
			}, workflow.EntryTicketWorkflow, workflow.EntryTicketWorkflowInput{
				TicketID:      ticket.ID,
				ApplicantID:   ticket.ApplicantID,
				ApplicantName: ticket.ApplicantName,
				Status:        ticket.Status,
				TicketNo:      ticket.TicketNo,
				Completed:     false, // 确保明确设置为false
			})
			if err != nil {
				fmt.Printf("恢复工作流失败 [%d]: %v\n", ticket.ID, err)
			}
		}
	}

	return nil
}

// TriggerWorkflowStage 触发工作流阶段
func (s *entryTicketService) TriggerWorkflowStage(
	ctx context.Context,
	ticketID uint,
	stage string,
	operatorID uint,
	operatorName string,
	comments string,
	data map[string]interface{},
) error {
	// 验证工作流阶段参数
	if stage == "" {
		return fmt.Errorf("工作流阶段不能为空")
	}

	// 获取当前工单，但不更新其状态
	ticket, err := s.repo.GetByID(ctx, ticketID)
	if err != nil {
		return fmt.Errorf("获取工单失败: %w", err)
	}

	// 检查工单状态，如果是终态，则不允许触发工作流
	if ticket.Status == common.StatusCompleted || ticket.Status == common.StatusCancelled {
		return fmt.Errorf("工单已处于终态(%s)，不能触发工作流", ticket.Status)
	}

	// 验证阶段名是否有效
	isValid, message := common.ValidateEntryStageTransition(ticket.Status, stage)
	if !isValid {
		return fmt.Errorf("无效的工作流阶段: %s - %s", stage, message)
	}

	// 构造工作流ID
	workflowID := fmt.Sprintf("%s_%d", workflow.EntryTicketWorkflowIDPrefix, ticketID)

	// 构造工作流控制信号
	signal := common.WorkflowControlSignal{
		Stage:        stage,
		OperatorID:   operatorID,
		OperatorName: operatorName,
		Comments:     comments,
		Data:         data,
	}

	s.logger.Info("准备触发工作流信号",
		zap.String("workflowID", workflowID),
		zap.String("stage", stage),
		zap.Uint("operatorID", operatorID),
		zap.String("operatorName", operatorName))

	// 使用临时上下文以便控制超时
	signalCtx, signalCancel := context.WithTimeout(ctx, 3*time.Second)
	defer signalCancel()

	// 发送信号给工作流
	signalErr := s.temporalClient.SignalWorkflow(signalCtx, workflowID, "", common.WorkflowControlSignalName, signal)
	if signalErr != nil {
		// 工作流可能不存在，尝试重新启动
		if strings.Contains(signalErr.Error(), "workflow") && strings.Contains(signalErr.Error(), "not found") {
			s.logger.Warn("工作流不存在，将重新启动",
				zap.String("workflowID", workflowID),
				zap.Error(signalErr))

			// 重新启动工作流
			workflowOptions := client.StartWorkflowOptions{
				ID:        workflowID,
				TaskQueue: workflow.EntryTicketTaskQueue,
			}

			// 构造工作流输入
			input := workflow.EntryTicketWorkflowInput{
				TicketID:      ticket.ID,
				ApplicantID:   ticket.ApplicantID,
				ApplicantName: ticket.ApplicantName,
				Status:        ticket.Status,
				TicketNo:      ticket.TicketNo,
				Completed:     false, // 明确设置为false
			}

			// 创建工作流启动上下文
			startCtx, startCancel := context.WithTimeout(ctx, 5*time.Second)
			defer startCancel()

			// 启动工作流
			workflowExecution, startErr := s.temporalClient.ExecuteWorkflow(startCtx, workflowOptions, workflow.EntryTicketWorkflow, input)
			if startErr != nil {
				s.logger.Error("重新启动工作流失败",
					zap.Error(startErr),
					zap.String("workflowID", workflowID))
				return fmt.Errorf("重新启动工作流失败: %w", startErr)
			}

			s.logger.Info("成功重新启动工作流",
				zap.String("workflowID", workflowID),
				zap.String("runID", workflowExecution.GetRunID()))

			// 等待一段时间，确保工作流启动
			time.Sleep(500 * time.Millisecond)

			// 再次发送信号
			if resendErr := s.temporalClient.SignalWorkflow(ctx, workflowID, "", common.WorkflowControlSignalName, signal); resendErr != nil {
				s.logger.Error("向新启动的工作流发送信号失败",
					zap.Error(resendErr),
					zap.String("workflowID", workflowID))
				return fmt.Errorf("向新启动的工作流发送信号失败: %w", resendErr)
			}

			s.logger.Info("成功向新启动的工作流发送信号",
				zap.String("workflowID", workflowID),
				zap.String("stage", stage))
			return nil
		}

		s.logger.Error("发送工作流信号失败",
			zap.Error(signalErr),
			zap.String("workflowID", workflowID))
		return fmt.Errorf("发送工作流信号失败: %w", signalErr)
	}

	s.logger.Info("成功发送工作流信号",
		zap.String("workflowID", workflowID),
		zap.String("stage", stage))
	return nil
}

// CreateRepairSelection 创建维修选择记录
func (s *entryTicketService) CreateEntryPerson(ctx context.Context, selection *model.EntryPerson) error {
	return s.entryPersonRepo.Create(ctx, selection)
}

// CreateCustomerApproval 创建客户审批记录
func (s *entryTicketService) CreateEntryApproval(ctx context.Context, approval *model.EntryApproval) error {
	return s.entryApprovalRepo.Create(ctx, approval)
}

func (s *entryTicketService) GetApprovalById(ctx context.Context, id uint) (*model.EntryApproval, error) {
	return s.entryApprovalRepo.GetByTicketID(ctx, id)
}

func (s *entryTicketService) UpdateApproval(ctx context.Context, approval *model.EntryApproval) error {
	return s.entryApprovalRepo.Update(ctx, approval)
}

//// CreateRepairTicket 创建维修单
//func (s *entryTicketService) CreateRepairTicket(ctx context.Context, faultTicketID uint, repairType string) (*model.RepairTicket, error) {
//	// 检查故障单是否存在
//	_, err := s.repo.GetByID(ctx, faultTicketID)
//	if err != nil {
//		return nil, fmt.Errorf("获取故障单失败: %w", err)
//	}
//
//	// 创建维修单
//	repairTicket, err := s.repairTicketService.CreateRepairTicket(ctx, faultTicketID, repairType)
//	if err != nil {
//		return nil, fmt.Errorf("创建维修单失败: %w", err)
//	}
//
//	// 更新故障单的维修单ID字段
//	err = s.repo.UpdateFields(ctx, faultTicketID, map[string]interface{}{
//		"repair_ticket_id": repairTicket.ID,
//	})
//	if err != nil {
//		s.logger.Error("更新故障单维修单ID失败",
//			zap.Error(err),
//			zap.Uint("faultTicketID", faultTicketID),
//			zap.Uint("repairTicketID", repairTicket.ID))
//		return repairTicket, fmt.Errorf("更新故障单维修单ID失败: %w", err)
//	}
//
//	// 记录创建维修单的日志
//	s.logger.Info("成功创建维修单",
//		zap.Uint("faultTicketID", faultTicketID),
//		zap.Uint("repairTicketID", repairTicket.ID),
//		zap.String("status", repairTicket.Status),
//		zap.String("repairType", repairTicket.RepairType))
//
//	// 注意：移除了自动授权的逻辑，确保所有维修单创建后都处于waiting_authorization状态，
//	// 需要通过工作流显式授权后才能进入waiting_accept状态
//
//	return repairTicket, nil
//}
