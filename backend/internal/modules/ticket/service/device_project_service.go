package service

import (
	"backend/internal/modules/cmdb/service/asset"
	"context"
	"errors"
	"fmt"
)

// DeviceProjectService 设备项目查询服务接口
type DeviceProjectService interface {
	// GetProjectBySN 根据设备SN获取项目信息
	GetProjectBySN(ctx context.Context, sn string) (string, error)
}

// deviceProjectService 设备项目查询服务实现
type deviceProjectService struct {
	deviceSearchService asset.DeviceSearchService
}

// NewDeviceProjectService 创建设备项目查询服务
func NewDeviceProjectService(deviceSearchService asset.DeviceSearchService) DeviceProjectService {
	return &deviceProjectService{
		deviceSearchService: deviceSearchService,
	}
}

// GetProjectBySN 根据设备SN获取项目信息
func (s *deviceProjectService) GetProjectBySN(ctx context.Context, sn string) (string, error) {
	if sn == "" {
		return "", errors.New("设备SN不能为空")
	}

	fmt.Printf("[DEBUG] 开始查询设备项目信息 - SN: %s\n", sn)

	// 通过设备搜索服务查找设备信息
	searchResult, err := s.deviceSearchService.SearchBySN(ctx, sn)
	if err != nil {
		fmt.Printf("[DEBUG] 设备搜索失败 - SN: %s, 错误: %v\n", sn, err)
		return "", err
	}

	if searchResult == nil {
		fmt.Printf("[DEBUG] 设备搜索结果为空 - SN: %s\n", sn)
		return "", errors.New("未找到设备信息")
	}

	fmt.Printf("[DEBUG] 设备搜索结果 - SN: %s, Source: %s\n", sn, searchResult.Source)

	// 优先从资源信息中获取项目
	if searchResult.Resource != nil {
		fmt.Printf("[DEBUG] 资源信息存在 - SN: %s, Project: %s\n", sn, searchResult.Resource.Project)
		if searchResult.Resource.Project != "" {
			fmt.Printf("[DEBUG] 从资源信息获取项目 - SN: %s, Project: %s\n", sn, searchResult.Resource.Project)
			return searchResult.Resource.Project, nil
		}
	} else {
		fmt.Printf("[DEBUG] 资源信息不存在 - SN: %s\n", sn)
	}

	// 如果资源信息中没有项目，尝试从设备信息中获取
	if searchResult.Device != nil {
		fmt.Printf("[DEBUG] 设备信息存在 - SN: %s\n", sn)
		if searchResult.Device.Resource != nil {
			fmt.Printf("[DEBUG] 设备关联资源存在 - SN: %s, Project: %s\n", sn, searchResult.Device.Resource.Project)
			if searchResult.Device.Resource.Project != "" {
				fmt.Printf("[DEBUG] 从设备关联资源获取项目 - SN: %s, Project: %s\n", sn, searchResult.Device.Resource.Project)
				return searchResult.Device.Resource.Project, nil
			}
		} else {
			fmt.Printf("[DEBUG] 设备关联资源不存在 - SN: %s\n", sn)
		}
	} else {
		fmt.Printf("[DEBUG] 设备信息不存在 - SN: %s\n", sn)
	}

	// 如果都没有找到项目信息，返回空字符串（将使用默认webhook）
	fmt.Printf("[DEBUG] 未找到项目信息，使用默认配置 - SN: %s\n", sn)
	return "", nil
}
