package service

import (
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	"context"
	"errors"
	"fmt"
	"go.uber.org/zap"
	"time"
)

// RepairSelectionService 维修选择服务接口
type RepairSelectionService interface {
	// CreateRepairSelection 创建维修选择记录
	CreateRepairSelection(ctx context.Context, selection *model.RepairSelection) error
	// GetRepairSelectionByTicketID 根据工单ID获取维修选择记录
	GetRepairSelectionByTicketID(ctx context.Context, ticketID uint) (*model.RepairSelection, error)
	// UpdateRepairSelection 更新维修选择记录
	UpdateRepairSelection(ctx context.Context, selection *model.RepairSelection) error
}

// CustomerApprovalService 客户审批服务接口
type CustomerApprovalService interface {
	// CreateCustomerApproval 创建客户审批记录
	CreateCustomerApproval(ctx context.Context, approval *model.CustomerApproval) error
	// GetCustomerApprovalByTicketID 根据工单ID获取客户审批记录
	GetCustomerApprovalByTicketID(ctx context.Context, ticketID uint) (*model.CustomerApproval, error)
	// UpdateCustomerApproval 更新客户审批记录
	UpdateCustomerApproval(ctx context.Context, approval *model.CustomerApproval) error
}

// VerificationService 验证结果服务接口
type VerificationService interface {
	// CreateVerification 创建验证结果记录
	CreateVerification(ctx context.Context, verification *model.Verification) error
	// GetVerificationByTicketID 根据工单ID获取验证结果记录
	GetVerificationByTicketID(ctx context.Context, ticketID uint) (*model.Verification, error)
	// UpdateVerification 更新验证结果记录
	UpdateVerification(ctx context.Context, verification *model.Verification) error
}

// EntryPersonService 入室人员服务接口
type EntryPersonService interface {
	// CreateVerification 创建入室人员记录
	CreateEntryPerson(ctx context.Context, persons []*model.EntryPerson) error
	// Create 创建入室单
	Create(ctx context.Context, person *model.EntryPerson) error
	// GetVerificationByTicketID 根据工单ID获取入室人员记录
	GetEntryPersonByTicketID(ctx context.Context, ticketID uint) (*model.EntryPerson, error)
	// UpdateVerification 更新入室人员记录
	UpdateEntryPerson(ctx context.Context, entryPerson *model.EntryPerson) error
	// UpdateVerification 更新入室人员记录
	UpdateEntryPersonFields(ctx context.Context, id uint, fields map[string]interface{}) error
	// ListEntryPersons 查询入室人员记录
	ListEntryPersons(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*model.EntryPerson, int64, error)

	GetEntryPersonByID(ctx context.Context, id uint) (*model.EntryPerson, error)
}

// repairSelectionService 维修选择服务实现
type repairSelectionService struct {
	repo            repository.RepairSelectionRepository
	faultTicketRepo repository.FaultTicketRepository
}

// NewRepairSelectionService 创建维修选择服务
func NewRepairSelectionService(repo repository.RepairSelectionRepository, faultTicketRepo repository.FaultTicketRepository) RepairSelectionService {
	return &repairSelectionService{
		repo:            repo,
		faultTicketRepo: faultTicketRepo,
	}
}

// CreateRepairSelection 创建维修选择记录
func (s *repairSelectionService) CreateRepairSelection(ctx context.Context, selection *model.RepairSelection) error {
	// 检查工单是否存在
	ticket, err := s.faultTicketRepo.GetByID(ctx, selection.TicketID)
	if err != nil {
		return err
	}

	// 创建维修选择记录
	if err := s.repo.Create(ctx, selection); err != nil {
		return err
	}

	// 更新工单状态为客户待审批
	ticket.Status = "waiting_approval"
	ticket.RepairMethod = selection.RepairType

	// 无论维修类型如何，都更新具体故障类型（如果有）
	if selection.FaultDetailType != "" {
		// 更新故障单的具体故障类型
		ticket.FaultDetailType = selection.FaultDetailType
	}

	// 更新槽位位置信息，如果维修选择中有指定
	if selection.SlotPosition != "" {
		ticket.SlotPosition = selection.SlotPosition
	}

	// 如果不需要客户审批，设置count_in_sla为false
	if !ticket.RequireApproval {
		ticket.CountInSLA = false
	}

	// 更新到数据库
	return s.faultTicketRepo.Update(ctx, ticket)
}

// GetRepairSelectionByTicketID 根据工单ID获取维修选择记录
func (s *repairSelectionService) GetRepairSelectionByTicketID(ctx context.Context, ticketID uint) (*model.RepairSelection, error) {
	return s.repo.GetByTicketID(ctx, ticketID)
}

// UpdateRepairSelection 更新维修选择记录
func (s *repairSelectionService) UpdateRepairSelection(ctx context.Context, selection *model.RepairSelection) error {
	return s.repo.Update(ctx, selection)
}

// customerApprovalService 客户审批服务实现
type customerApprovalService struct {
	repo                repository.CustomerApprovalRepository
	faultTicketRepo     repository.FaultTicketRepository
	repairTicketService RepairTicketService
}

// NewCustomerApprovalService 创建客户审批服务
func NewCustomerApprovalService(
	repo repository.CustomerApprovalRepository,
	faultTicketRepo repository.FaultTicketRepository,
	repairTicketService RepairTicketService,
) CustomerApprovalService {
	return &customerApprovalService{
		repo:                repo,
		faultTicketRepo:     faultTicketRepo,
		repairTicketService: repairTicketService,
	}
}

// CreateCustomerApproval 创建客户审批记录
func (s *customerApprovalService) CreateCustomerApproval(ctx context.Context, approval *model.CustomerApproval) error {
	// 查询工单
	ticket, err := s.faultTicketRepo.GetByID(ctx, approval.TicketID)
	if err != nil {
		return err
	}

	// 检查工单状态
	if ticket.Status != "waiting_approval" {
		return errors.New("工单状态不是待审批")
	}

	// 设置响应时间
	approval.ResponseTime = time.Now()

	// 创建审批记录
	if err := s.repo.Create(ctx, approval); err != nil {
		return err
	}

	// 如果审批通过，更新工单状态
	switch approval.Status {
	case "approved":
		// 更新状态为客户已批准等待后续操作状态
		ticket.Status = "approved_waiting_action"
		ticket.RequireApproval = true
		fmt.Printf("客户审批通过: 更新工单状态为'approved_waiting_action', ticketID=%d\n", ticket.ID)

		// 注意：不再在此处创建维修单，而是在工作流中通过活动创建
		// 这确保了维修单的创建在正确的工作流状态下进行

		return s.faultTicketRepo.Update(ctx, ticket)
	case "rejected":
		// 如果审批拒绝，回到排查中状态
		ticket.Status = "investigating"
		return s.faultTicketRepo.Update(ctx, ticket)
	}

	return nil
}

// GetCustomerApprovalByTicketID 根据工单ID获取客户审批记录
func (s *customerApprovalService) GetCustomerApprovalByTicketID(ctx context.Context, ticketID uint) (*model.CustomerApproval, error) {
	return s.repo.GetByTicketID(ctx, ticketID)
}

// UpdateCustomerApproval 更新客户审批记录
func (s *customerApprovalService) UpdateCustomerApproval(ctx context.Context, approval *model.CustomerApproval) error {
	return s.repo.Update(ctx, approval)
}

// verificationService 验证结果服务实现
type verificationService struct {
	repo            repository.VerificationRepository
	faultTicketRepo repository.FaultTicketRepository
}

// NewVerificationService 创建验证结果服务
func NewVerificationService(repo repository.VerificationRepository, faultTicketRepo repository.FaultTicketRepository) VerificationService {
	return &verificationService{
		repo:            repo,
		faultTicketRepo: faultTicketRepo,
	}
}

// CreateVerification 创建验证结果记录
func (s *verificationService) CreateVerification(ctx context.Context, verification *model.Verification) error {
	// 查询工单
	ticket, err := s.faultTicketRepo.GetByID(ctx, verification.TicketID)
	if err != nil {
		return err
	}

	// 检查工单状态
	if ticket.Status != "waiting_verification" {
		return errors.New("工单状态不是待验证")
	}

	// 设置验证时间
	verification.VerificationTime = time.Now()

	// 先检查是否已存在验证记录
	existingVerification, err := s.repo.GetByTicketID(ctx, verification.TicketID)
	if err != nil && err.Error() != "record not found" {
		return fmt.Errorf("检查现有验证记录失败: %w", err)
	}

	// 如果已存在验证记录，则更新而不是创建
	if existingVerification != nil {
		// 记录这是第二次验证
		fmt.Printf("检测到第二次验证操作，ticketID=%d, 原success=%v, 新success=%v\n",
			verification.TicketID, existingVerification.Success, verification.Success)

		// 更新已有记录的字段
		existingVerification.Success = verification.Success
		existingVerification.Comments = verification.Comments
		existingVerification.VerificationTime = verification.VerificationTime
		existingVerification.OperatorID = verification.OperatorID
		existingVerification.OperatorName = verification.OperatorName

		// 更新记录
		if err := s.repo.Update(ctx, existingVerification); err != nil {
			return fmt.Errorf("更新验证记录失败: %w", err)
		}
	} else {
		// 不存在记录时创建新记录
		if err := s.repo.Create(ctx, verification); err != nil {
			return fmt.Errorf("创建验证记录失败: %w", err)
		}
	}

	// 如果验证通过，更新工单状态为总结阶段
	if verification.Success {
		ticket.Status = "summarizing"
		ticket.VerificationEndTime = &verification.VerificationTime
	} else {
		// 如果验证不通过，回到排查中状态
		ticket.Status = "investigating"
	}

	return s.faultTicketRepo.Update(ctx, ticket)
}

// GetVerificationByTicketID 根据工单ID获取验证结果记录
func (s *verificationService) GetVerificationByTicketID(ctx context.Context, ticketID uint) (*model.Verification, error) {
	verification, err := s.repo.GetByTicketID(ctx, ticketID)
	if err != nil {
		// 如果是记录不存在的错误，返回nil而不是错误
		if err.Error() == "record not found" {
			return nil, nil
		}
		return nil, err
	}
	return verification, nil
}

// UpdateVerification 更新验证结果记录
func (s *verificationService) UpdateVerification(ctx context.Context, verification *model.Verification) error {
	return s.repo.Update(ctx, verification)
}

// entryPersonService 维修选择服务实现
type entryPersonService struct {
	repo            repository.EntryPersonRepository
	entryTicketRepo repository.EntryTicketRepository
	logger          *zap.Logger
}

// NewEntryPersonService 创建维修选择服务
func NewEntryPersonService(repo repository.EntryPersonRepository, entryTicketRepo repository.EntryTicketRepository, logger *zap.Logger) EntryPersonService {
	return &entryPersonService{
		repo:            repo,
		entryTicketRepo: entryTicketRepo,
		logger:          logger,
	}
}

// CreateRepairSelection 创建维修选择记录
func (s *entryPersonService) CreateEntryPerson(ctx context.Context, persons []*model.EntryPerson) error {
	return s.repo.WithTransaction(ctx, func(txCtx context.Context, repo repository.EntryPersonRepository) error {
		for i := 0; i < len(persons); i++ {
			err := repo.Create(ctx, persons[i])
			if err != nil {
				return fmt.Errorf("创建入室人员失败: %w", err)
			}
		}
		return nil
	})
}

// Create 创建入室单
func (s *entryPersonService) Create(ctx context.Context, person *model.EntryPerson) error {
	// 验证必填字段
	if person.Name == "" {
		return fmt.Errorf("姓名不能为空")
	}
	if person.IdentificationNumber == "" {
		return fmt.Errorf("身份证号不能为空")
	}

	// 设置默认状态
	if person.Status == "" {
		person.Status = "enabled"
	}

	// 调用仓库层创建入室人员记录
	return s.repo.Create(ctx, person)
}

// GetRepairSelectionByTicketID 根据工单ID获取维修选择记录
func (s *entryPersonService) GetEntryPersonByTicketID(ctx context.Context, ticketID uint) (*model.EntryPerson, error) {
	return s.repo.GetByTicketID(ctx, ticketID)
}

// GetRepairSelectionByTicketID 根据工单ID获取维修选择记录
func (s *entryPersonService) GetEntryPersonByID(ctx context.Context, ticketID uint) (*model.EntryPerson, error) {
	return s.repo.GetByID(ctx, ticketID)
}

// UpdateRepairSelection 更新维修选择记录
func (s *entryPersonService) UpdateEntryPerson(ctx context.Context, selection *model.EntryPerson) error {
	return s.repo.Update(ctx, selection)
}

// UpdateFaultTicketFields 更新报障单指定字段
func (s *entryPersonService) UpdateEntryPersonFields(ctx context.Context, id uint, fields map[string]interface{}) error {
	// 使用Repository提供的UpdateFields方法
	return s.repo.UpdateFields(ctx, id, fields)
}

// ListEntryTickets 分页获取入室单列表
func (s *entryPersonService) ListEntryPersons(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*model.EntryPerson, int64, error) {
	// 解析基本筛选条件
	//query, _ := filters["query"].(string)
	//
	//// 处理status参数，支持字符串或字符串数组
	//var statusValues []string
	//if statusArray, ok := filters["status"].([]string); ok && len(statusArray) > 0 {
	//	statusValues = statusArray
	//} else if status, ok := filters["status"].(string); ok && status != "" {
	//	statusValues = []string{status}
	//}

	//startTime, _ := filters["startTime"].(*time.Time)
	//endTime, _ := filters["endTime"].(*time.Time)

	var timeValue *time.Time
	if timeVal, ok := filters["time"]; ok && timeVal != nil {
		if t, ok := timeVal.(*time.Time); ok {
			timeValue = t
		}
	}

	// 构建查询条件
	conditions := make(map[string]interface{})

	// 处理原有的查询条件
	//if query != "" {
	//	// 原有的query支持搜索设备SN和工单编号
	//	conditions["query"] = query
	//}

	// 处理状态条件
	//if len(statusValues) > 0 {
	//	conditions["status"] = statusValues
	//}

	// 处理时间范围
	//if startTime != nil {
	//	conditions["start_time"] = startTime
	//}
	//if endTime != nil {
	//	conditions["end_time"] = endTime
	//}

	if timeValue != nil {
		conditions["time"] = timeValue
	}

	return s.repo.List(ctx, page, pageSize, conditions)
}
