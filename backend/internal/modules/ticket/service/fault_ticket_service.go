package service

import (
	scheduleService "backend/internal/modules/schedule/service"
	"backend/internal/modules/ticket/common"
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	"backend/internal/modules/ticket/workflow"
	"context"
	cryptorand "crypto/rand" // 使用别名避免与math/rand冲突
	"encoding/binary"
	"fmt"
	"net"
	"regexp"
	"strings"
	"time"

	"go.temporal.io/sdk/activity"
	"go.temporal.io/sdk/client"
	"go.uber.org/zap"
)

// FaultTicketService 报障单服务接口
type FaultTicketService interface {
	CreateFaultTicket(ctx context.Context, ticket *model.FaultTicket) error
	GetFaultTicketByID(ctx context.Context, id uint) (*model.FaultTicket, error)
	GetFaultTicketByTicketNo(ctx context.Context, ticketNo string) (*model.FaultTicket, error)
	UpdateFaultTicket(ctx context.Context, ticket *model.FaultTicket) error
	UpdateFaultTicketFields(ctx context.Context, id uint, fields map[string]interface{}) error
	UpdateFaultTicketStatus(ctx context.Context, id uint, status string, operatorID uint, operatorName string) error
	AssignFaultTicket(ctx context.Context, id uint, engineerID uint) error
	CloseFaultTicket(ctx context.Context, id uint, summary string) error
	ListFaultTickets(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*model.FaultTicket, int64, error)
	GetFaultTicketStatusHistory(ctx context.Context, ticketID uint) ([]*model.FaultTicketStatusHistory, error)
	StartFaultTicketWorkflow(ctx context.Context, ticketID uint) error
	RecoverInconsistentWorkflows(ctx context.Context) error
	TriggerWorkflowStage(ctx context.Context, ticketID uint, stage string, operatorID uint, operatorName string, comments string, data map[string]interface{}) error
	CreateRepairSelection(ctx context.Context, selection *model.RepairSelection) error
	CreateCustomerApproval(ctx context.Context, approval *model.CustomerApproval) error
	CreateVerification(ctx context.Context, verification *model.Verification) error
	CreateRepairTicket(ctx context.Context, faultTicketID uint, repairType string) (*model.RepairTicket, error)
	// CountDeviceFaults 根据设备SN和查询条件统计故障次数
	CountDeviceFaults(ctx context.Context, filters map[string]interface{}) (int64, error)
}

// 注释掉UserService接口定义，避免重复声明冲突
// UserService 用户服务接口
// type UserService interface {
// 	GetUserByID(ctx context.Context, id uint) (*model.User, error)
// }

// 注释掉User结构体定义，避免未定义错误
// User 基本用户模型，针对Ticket服务需要的字段
// type User struct {
// 	ID       uint   `json:"id"`
// 	Username string `json:"username"`
// 	RealName string `json:"real_name"`
// }

// faultTicketService 报障单服务实现
type faultTicketService struct {
	repo                 repository.FaultTicketRepository
	repairSelectionRepo  repository.RepairSelectionRepository
	customerApprovalRepo repository.CustomerApprovalRepository
	verificationRepo     repository.VerificationRepository
	temporalClient       client.Client
	logger               *zap.Logger
	repairTicketService  RepairTicketService
	softScheduleService  scheduleService.SoftScheduleService
	// 注释掉userService字段，避免未使用的服务
	// userService          UserService
}

// NewFaultTicketService 创建报障单服务
func NewFaultTicketService(
	repo repository.FaultTicketRepository,
	repairSelectionRepo repository.RepairSelectionRepository,
	customerApprovalRepo repository.CustomerApprovalRepository,
	verificationRepo repository.VerificationRepository,
	temporalClient client.Client,
	logger *zap.Logger,
	repairTicketService RepairTicketService,
	softScheduleService scheduleService.SoftScheduleService,
	// 注释掉userService参数，避免未使用的服务
	// userService UserService,
) FaultTicketService {
	return &faultTicketService{
		repo:                 repo,
		repairSelectionRepo:  repairSelectionRepo,
		customerApprovalRepo: customerApprovalRepo,
		verificationRepo:     verificationRepo,
		temporalClient:       temporalClient,
		logger:               logger,
		repairTicketService:  repairTicketService,
		softScheduleService:  softScheduleService,
		// 注释掉userService字段，避免未使用的服务
		// userService:          userService,
	}
}

// 工作流故障重试相关实现

// recordWorkflowFailure 记录工作流启动失败，以便后续重试
//
//nolint:unused
func (s *faultTicketService) recordWorkflowFailure(ctx context.Context, ticketID uint, errMsg string) {
	if ticketID == 0 {
		fmt.Printf("警告: 尝试记录工作流失败，但工单ID为0，操作取消\n")
		return
	}

	// 获取当前时间
	now := time.Now()

	fmt.Printf("记录工作流失败信息: 工单ID=%d, 错误=%s\n", ticketID, errMsg)

	// 创建带有超时的上下文
	dbCtx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()

	// 更新报障单的备注字段，添加工作流失败信息
	ticket, err := s.repo.GetByID(dbCtx, ticketID)
	if err != nil {
		fmt.Printf("记录工作流失败信息时无法获取工单(ID=%d): %v\n", ticketID, err)
		return
	}

	if ticket == nil {
		fmt.Printf("记录工作流失败信息时工单不存在(ID=%d)\n", ticketID)
		return
	}

	// 添加工作流失败信息到备注
	newRemark := fmt.Sprintf("[%s] 工作流启动失败: %s", now.Format("2006-01-02 15:04:05"), errMsg)
	if ticket.Remarks != "" {
		ticket.Remarks = ticket.Remarks + "\n" + newRemark
	} else {
		ticket.Remarks = newRemark
	}

	// 添加重试标记
	ticket.NeedsWorkflowRetry = true
	ticket.LastWorkflowRetryTime = &now

	// 确保不会修改其他关键字段
	originalID := ticket.ID
	originalTicketNo := ticket.TicketNo
	originalStatus := ticket.Status

	// 使用安全的更新方式，仅更新备注和重试相关字段
	updateData := map[string]interface{}{
		"remarks":                  ticket.Remarks,
		"needs_workflow_retry":     ticket.NeedsWorkflowRetry,
		"last_workflow_retry_time": ticket.LastWorkflowRetryTime,
	}

	// 使用直接更新特定字段的方式而不是整体更新
	updateCtx, updateCancel := context.WithTimeout(ctx, 3*time.Second)
	defer updateCancel()

	err = s.repo.UpdateFields(updateCtx, ticketID, updateData)
	if err != nil {
		fmt.Printf("无法更新工单(ID=%d)的工作流失败信息: %v\n", ticketID, err)
		return
	}

	// 验证更新结果
	verifyCtx, verifyCancel := context.WithTimeout(ctx, 2*time.Second)
	defer verifyCancel()

	updatedTicket, err := s.repo.GetByID(verifyCtx, ticketID)
	if err != nil {
		fmt.Printf("验证更新后的工单失败(ID=%d): %v\n", ticketID, err)
		return
	}

	if updatedTicket.ID != originalID || updatedTicket.TicketNo != originalTicketNo || updatedTicket.Status != originalStatus {
		fmt.Printf("警告: 工单关键字段在更新后发生变化(ID=%d)！\n", ticketID)
	} else {
		fmt.Printf("成功更新工单工作流失败信息(ID=%d)\n", ticketID)
	}
}

// retryFailedWorkflows 重试失败的工作流
// 该方法可以在定时任务中调用
//
//nolint:unused
func (s *faultTicketService) retryFailedWorkflows(ctx context.Context) {
	logger := activity.GetLogger(ctx)
	logger.Info("开始重试失败的工作流")

	// 查询需要重试的工单
	tickets, err := s.repo.FindTicketsForRetry(ctx)
	if err != nil {
		logger.Error("查询需要重试的工单失败", "error", err)
		return
	}

	if len(tickets) == 0 {
		logger.Info("没有需要重试的工单")
		return
	}

	logger.Info("找到需要重试的工单", "count", len(tickets))

	// 遍历需要重试的工单
	for _, ticket := range tickets {
		// 检查工单状态，如果是终态状态则不重试
		if ticket.Status == common.StatusCompleted || ticket.Status == common.StatusCancelled {
			logger.Info("工单已处于终态状态，不再重试",
				"ticketID", ticket.ID,
				"status", ticket.Status)

			// 更新工单，清除重试标记
			if err := s.repo.UpdateFields(ctx, ticket.ID, map[string]interface{}{
				"needs_workflow_retry": false,
			}); err != nil {
				logger.Error("清除工单重试标记失败",
					"ticketID", ticket.ID,
					"error", err)
			}
			continue
		}

		// 检查重试次数是否超过限制
		if ticket.WorkflowRetryCount >= 3 {
			logger.Warn("工单重试次数超过限制，不再重试",
				"ticketID", ticket.ID,
				"ticketNo", ticket.TicketNo,
				"retryCount", ticket.WorkflowRetryCount)
			continue
		}

		// 更新重试相关字段
		if err := s.repo.UpdateFields(ctx, ticket.ID, map[string]interface{}{
			"needs_workflow_retry":     false,
			"last_workflow_retry_time": time.Now(),
			"workflow_retry_count":     ticket.WorkflowRetryCount + 1,
		}); err != nil {
			logger.Error("更新工单重试字段失败",
				"ticketID", ticket.ID,
				"error", err)
			continue
		}

		// 重新启动工作流
		input := workflow.FaultTicketWorkflowInput{
			TicketID:         ticket.ID,
			ReporterID:       ticket.ReporterID,
			ReporterName:     ticket.ReporterName,
			FaultType:        ticket.FaultType,
			FaultDescription: ticket.FaultDescription,
			Symptom:          ticket.Symptom,
			SlotPosition:     ticket.SlotPosition,
			Priority:         ticket.Priority,
			Source:           ticket.Source,
			DeviceSN:         ticket.DeviceSN,
			ComponentSN:      ticket.ComponentSN,
			ComponentType:    ticket.ComponentType,
			Status:           ticket.Status,
			TicketNo:         ticket.TicketNo,
			Completed:        false, // 确保明确设置为false
		}

		workflowID := fmt.Sprintf("%s-%d-%d", ticket.TicketNo, ticket.ID, ticket.WorkflowRetryCount+1)
		workflowOptions := client.StartWorkflowOptions{
			ID:        workflowID,
			TaskQueue: "fault_ticket_workflow",
		}

		_, err = s.temporalClient.ExecuteWorkflow(ctx, workflowOptions, workflow.FaultTicketWorkflow, input)
		if err != nil {
			logger.Error("重启工作流失败",
				"ticketID", ticket.ID,
				"workflowID", workflowID,
				"error", err)

			// 如果启动失败，重新标记为需要重试
			if err := s.repo.UpdateFields(ctx, ticket.ID, map[string]interface{}{
				"needs_workflow_retry": true,
			}); err != nil {
				logger.Error("更新工单重试标记失败",
					"ticketID", ticket.ID,
					"error", err)
			}
			continue
		}

		logger.Info("成功重启工作流",
			"ticketID", ticket.ID,
			"ticketNo", ticket.TicketNo,
			"workflowID", workflowID,
			"retryCount", ticket.WorkflowRetryCount+1)
	}

	logger.Info("完成工作流重试")
}

// CreateFaultTicket 创建报障单
func (s *faultTicketService) CreateFaultTicket(ctx context.Context, ticket *model.FaultTicket) error {
	// 验证ResourceIdentifier是否为有效的IP格式
	if ticket.ResourceIdentifier != "" {
		validIP, isValid := validateIPAddress(ticket.ResourceIdentifier)
		if !isValid {
			return fmt.Errorf("无效的IP格式: %s", ticket.ResourceIdentifier)
		}
		ticket.ResourceIdentifier = validIP
	}

	// 生成工单号
	ticket.TicketNo = s.generateTicketNo()

	// 设置初始状态
	ticket.Status = common.StatusWaitingAccept

	// 获取当前值班人员并设置AssignedTo
	if s.softScheduleService != nil {
		// 获取当前时间
		now := time.Now()

		// 判断当前时间是否在早上9点前
		currentHour := now.Hour()
		var targetDate string

		if currentHour < 9 {
			// 如果当前时间在早上9点前，则取前一天的值班信息
			yesterday := now.AddDate(0, 0, -1)
			targetDate = yesterday.Format("2006-01-02")
		} else {
			// 如果当前时间在早上9点后，则取当天的值班信息
			targetDate = now.Format("2006-01-02")
		}

		// 获取目标日期的值班信息
		schedule, err := s.softScheduleService.GetByDate(ctx, targetDate)
		if err == nil && schedule != nil && schedule.PrimaryUserName != "" {
			// 设置AssignedTo为主值班人员
			ticket.AssignedTo = schedule.PrimaryUserName
			s.logger.Info("自动设置报障单分配给当天主值班人员",
				zap.String("ticketNo", ticket.TicketNo),
				zap.String("assignedTo", ticket.AssignedTo),
				zap.String("date", targetDate))
		} else {
			s.logger.Warn("无法获取当天值班信息，未自动分配报障单",
				zap.String("ticketNo", ticket.TicketNo),
				zap.String("targetDate", targetDate),
				zap.Error(err))
		}
	}

	// 创建报障单
	if err := s.repo.Create(ctx, ticket); err != nil {
		return fmt.Errorf("创建报障单失败: %w", err)
	}

	// 记录状态历史
	history := &model.FaultTicketStatusHistory{
		FaultTicketID:  ticket.ID,
		PreviousStatus: "",
		NewStatus:      ticket.Status,
		OperatorID:     ticket.ReporterID,
		OperatorName:   ticket.ReporterName,
		OperationTime:  time.Now(),
		Remarks:        "创建报障单",
	}
	if err := s.repo.CreateStatusHistory(ctx, history); err != nil {
		// 仅记录日志，不影响主流程
		fmt.Printf("记录状态历史失败: %v\n", err)
	}

	// 启动工作流
	if s.temporalClient != nil {
		workflowID := fmt.Sprintf("fault_ticket_%d", ticket.ID)
		_, err := s.temporalClient.ExecuteWorkflow(ctx, client.StartWorkflowOptions{
			ID:        workflowID,
			TaskQueue: common.FaultTicketTaskQueue,
		}, workflow.FaultTicketWorkflow, workflow.FaultTicketWorkflowInput{
			TicketID:           ticket.ID,
			ReporterID:         ticket.ReporterID,
			ReporterName:       ticket.ReporterName,
			DeviceID:           nil, // 不使用设备ID
			ResourceID:         nil, // 不使用资源ID
			FaultType:          ticket.FaultType,
			FaultDescription:   ticket.FaultDescription,
			Symptom:            ticket.Symptom,
			SlotPosition:       ticket.SlotPosition,
			Priority:           ticket.Priority,
			Source:             ticket.Source,
			DeviceSN:           ticket.DeviceSN,
			ComponentSN:        ticket.ComponentSN,
			ComponentType:      ticket.ComponentType,
			Status:             ticket.Status,
			TicketNo:           ticket.TicketNo,
			Title:              ticket.Title,
			ResourceIdentifier: ticket.ResourceIdentifier,
			RequireApproval:    ticket.RequireApproval,
			Completed:          false, // 明确设置为false
		})
		if err != nil {
			// 仅记录日志，不影响主流程
			fmt.Printf("启动工作流失败: %v\n", err)
		}
	}

	return nil
}

// GetFaultTicketByID 根据ID获取报障单
func (s *faultTicketService) GetFaultTicketByID(ctx context.Context, id uint) (*model.FaultTicket, error) {
	return s.repo.GetByID(ctx, id)
}

// GetFaultTicketByTicketNo 根据工单号获取报障单
func (s *faultTicketService) GetFaultTicketByTicketNo(ctx context.Context, ticketNo string) (*model.FaultTicket, error) {
	return s.repo.GetByTicketNo(ctx, ticketNo)
}

// UpdateFaultTicket 更新报障单
func (s *faultTicketService) UpdateFaultTicket(ctx context.Context, ticket *model.FaultTicket) error {
	// 验证ResourceIdentifier是否为有效的IP格式
	if ticket.ResourceIdentifier != "" {
		validIP, isValid := validateIPAddress(ticket.ResourceIdentifier)
		if !isValid {
			return fmt.Errorf("无效的IP格式: %s", ticket.ResourceIdentifier)
		}
		ticket.ResourceIdentifier = validIP
	}

	return s.repo.Update(ctx, ticket)
}

// UpdateFaultTicketFields 更新报障单指定字段
func (s *faultTicketService) UpdateFaultTicketFields(ctx context.Context, id uint, fields map[string]interface{}) error {
	// 验证ResourceIdentifier是否为有效的IP格式
	if resourceIdentifier, ok := fields["resource_identifier"].(string); ok {
		validIP, isValid := validateIPAddress(resourceIdentifier)
		if !isValid {
			return fmt.Errorf("无效的IP格式: %s", resourceIdentifier)
		}
		fields["resource_identifier"] = validIP

		// 检查是否有相同的租户IP的未完成报障单
		if id != 0 { // 只在更新现有记录时检查
			existingTickets, err := s.repo.FindActiveTicketsByResourceIdentifier(ctx, validIP, id)
			if err != nil {
				s.logger.Error("检查重复IP报障单失败",
					zap.Error(err),
					zap.String("resourceIdentifier", validIP))
				return fmt.Errorf("检查重复IP报障单失败: %w", err)
			}

			if len(existingTickets) > 0 {
				// 找到了相同IP的未完成报障单，不允许更新
				return fmt.Errorf("存在使用相同IP (%s) 的未完成报障单（工单号：%s）",
					validIP, existingTickets[0].TicketNo)
			}
		}
	}

	// 处理驼峰命名的情况
	if resourceIdentifier, ok := fields["resourceIdentifier"].(string); ok {
		validIP, isValid := validateIPAddress(resourceIdentifier)
		if !isValid {
			return fmt.Errorf("无效的IP格式: %s", resourceIdentifier)
		}

		// 检查是否有相同的租户IP的未完成报障单
		if id != 0 { // 只在更新现有记录时检查
			existingTickets, err := s.repo.FindActiveTicketsByResourceIdentifier(ctx, validIP, id)
			if err != nil {
				s.logger.Error("检查重复IP报障单失败",
					zap.Error(err),
					zap.String("resourceIdentifier", validIP))
				return fmt.Errorf("检查重复IP报障单失败: %w", err)
			}

			if len(existingTickets) > 0 {
				// 找到了相同IP的未完成报障单，不允许更新
				return fmt.Errorf("存在使用相同IP (%s) 的未完成报障单（工单号：%s）",
					validIP, existingTickets[0].TicketNo)
			}
		}

		fields["resource_identifier"] = validIP
		delete(fields, "resourceIdentifier") // 移除驼峰命名的键，使用snake_case键
	}

	// 使用Repository提供的UpdateFields方法
	return s.repo.UpdateFields(ctx, id, fields)
}

// UpdateFaultTicketStatus 更新报障单状态
func (s *faultTicketService) UpdateFaultTicketStatus(ctx context.Context, id uint, status string, operatorID uint, operatorName string) error {
	// 使用事务处理状态更新
	return s.repo.WithTransaction(ctx, func(txCtx context.Context, repo repository.FaultTicketRepository) error {
		// 获取当前报障单
		ticket, err := repo.GetByID(txCtx, id)
		if err != nil {
			s.logger.Error("获取报障单失败",
				zap.Error(err),
				zap.Uint("ticketID", id))
			return fmt.Errorf("获取报障单失败: %w", err)
		}

		// 如果状态没有变化，则直接返回
		if ticket.Status == status {
			s.logger.Info("报障单状态未变化，无需更新",
				zap.Uint("ticketID", id),
				zap.String("status", status))
			return nil
		}

		previousStatus := ticket.Status

		// 如果当前状态是cancelled，不允许更改为其他状态
		if previousStatus == common.StatusCancelled && status != common.StatusCancelled {
			s.logger.Warn("工单已处于已取消状态，拒绝更改为其他状态",
				zap.Uint("ticketID", id),
				zap.String("当前状态", previousStatus),
				zap.String("目标状态", status))
			return nil
		}

		// 更新工单状态相关字段
		ticket.Status = status
		ticket.WaitingManualTrigger = false // 更新状态时清除等待标记
		ticket.CurrentWaitingStage = ""     // 清除当前等待阶段

		// 如果状态是cancelled，设置不计入SLA
		if status == common.StatusCancelled {
			ticket.CountInSLA = false
			s.logger.Info("工单状态设置为已取消，同时设置不计入SLA",
				zap.Uint("ticketID", id))

			// 检查取消原因是否包含误报或重复故障的关键词
			var cancelReason string
			if operatorName != "" {
				cancelReason = operatorName
			}

			// 检查是否包含"误报"字样
			if strings.Contains(operatorName, "误报") || (ticket.FaultSummary != "" && strings.Contains(ticket.FaultSummary, "误报")) {
				ticket.IsFalseAlarm = true
				s.logger.Info("根据取消原因自动设置is_false_alarm=true",
					zap.Uint("ticketID", id),
					zap.String("原因", cancelReason))
			}

			// 检查是否包含"重复"字样
			if strings.Contains(operatorName, "重复") || (ticket.FaultSummary != "" && strings.Contains(ticket.FaultSummary, "重复")) {
				ticket.IsDuplicateFault = true
				s.logger.Info("根据取消原因自动设置is_duplicate_fault=true",
					zap.Uint("ticketID", id),
					zap.String("原因", cancelReason))

				// 尝试从取消原因中提取关联工单ID
				if ticket.FaultSummary != "" {
					// 查找"ID:"或"ID："或"报障单ID"等关键词后面的数字
					parts := strings.Split(ticket.FaultSummary, "ID:")
					if len(parts) < 2 {
						parts = strings.Split(ticket.FaultSummary, "ID：") // 中文冒号
					}
					if len(parts) < 2 {
						parts = strings.Split(ticket.FaultSummary, "报障单ID")
					}

					if len(parts) >= 2 {
						// 从后半部分提取数字
						var relatedIDStr string
						for _, char := range parts[1] {
							if char >= '0' && char <= '9' {
								relatedIDStr += string(char)
							} else if relatedIDStr != "" {
								// 遇到非数字且已经收集了数字，则结束
								break
							}
						}

						// 尝试转换为uint
						if relatedIDStr != "" {
							var relatedID uint
							_, err := fmt.Sscanf(relatedIDStr, "%d", &relatedID)
							if err == nil && relatedID > 0 {
								ticket.RelatedTicketID = relatedID
								s.logger.Info("从取消原因中提取到关联工单ID",
									zap.Uint("ticketID", id),
									zap.Uint("relatedTicketID", relatedID),
									zap.String("提取源", ticket.FaultSummary))
							}
						}
					}
				}
			}
		}

		// 根据新状态更新工单时间字段
		now := time.Now()
		switch status {
		case common.StatusInvestigating:
			ticket.AcknowledgeTime = &now
			// 设置接单人信息，当状态转为investigating时
			if operatorID > 0 {
				ticket.AssignedTo = operatorName
				ticket.AssignmentTime = &now
				s.logger.Info("设置接单人",
					zap.Uint("ticketID", id),
					zap.String("assignedTo", operatorName))
			}

			// 检查是否为高频故障设备
			if ticket.DeviceSN != "" {
				// 检查最近7天内的故障数
				faults7Days, err := repo.CountRecentFaultsByDeviceSN(txCtx, ticket.DeviceSN, 7)
				if err != nil {
					s.logger.Warn("检查设备最近7天故障记录失败",
						zap.Error(err),
						zap.String("deviceSN", ticket.DeviceSN))
				} else if faults7Days >= 2 {
					ticket.IsFrequentFault = true
					s.logger.Info("设备为高频故障(最近7天内>=2次)",
						zap.String("deviceSN", ticket.DeviceSN),
						zap.Int64("故障次数", faults7Days))
				} else {
					// 检查最近30天内的故障数
					faults30Days, err := repo.CountRecentFaultsByDeviceSN(txCtx, ticket.DeviceSN, 30)
					if err != nil {
						s.logger.Warn("检查设备最近30天故障记录失败",
							zap.Error(err),
							zap.String("deviceSN", ticket.DeviceSN))
					} else if faults30Days >= 3 {
						ticket.IsFrequentFault = true
						s.logger.Info("设备为高频故障(最近30天内>=3次)",
							zap.String("deviceSN", ticket.DeviceSN),
							zap.Int64("故障次数", faults30Days))
					}
				}
			}

		case common.StatusRepairing, common.StatusRestarting, common.StatusMigrating, common.StatusSoftwareFixing:
			// 如果之前没有设置预计修复时间，则设置
			if ticket.ExpectedFixTime == nil {
				// 根据状态类型估算时间
				var estimatedDuration time.Duration
				switch status {
				case common.StatusRestarting:
					estimatedDuration = 30 * time.Minute
				case common.StatusMigrating:
					estimatedDuration = 2 * time.Hour
				case common.StatusSoftwareFixing:
					estimatedDuration = 1 * time.Hour
				case common.StatusRepairing:
					estimatedDuration = 4 * time.Hour
				}
				expectedTime := now.Add(estimatedDuration)
				ticket.ExpectedFixTime = &expectedTime
			}
		case common.StatusWaitingVerification:
			ticket.ActualFixTime = &now
			ticket.VerificationStartTime = &now
		case common.StatusCompleted:
			if ticket.CloseTime == nil {
				ticket.CloseTime = &now
			}
			if ticket.VerificationEndTime == nil && ticket.VerificationStartTime != nil {
				ticket.VerificationEndTime = &now
			}
		}

		// 更新报障单状态
		if err := repo.Update(txCtx, ticket); err != nil {
			s.logger.Error("更新报障单状态失败",
				zap.Error(err),
				zap.Uint("ticketID", id),
				zap.String("oldStatus", previousStatus),
				zap.String("newStatus", status))
			return fmt.Errorf("更新报障单状态失败: %w", err)
		}

		// 记录状态历史
		history := &model.FaultTicketStatusHistory{
			FaultTicketID:    id,
			PreviousStatus:   previousStatus,
			NewStatus:        status,
			OperatorID:       operatorID,
			OperatorName:     operatorName,
			OperationTime:    now,
			Remarks:          fmt.Sprintf("状态从 %s 变更为 %s", previousStatus, status),
			ActivityCategory: getActivityCategory(status),
			IsSLAPause:       isSLAPauseStatus(status),
			PauseReason:      getPauseReason(status),
		}

		// 如果是系统操作，添加标记
		if operatorID == 0 {
			history.OperatorName = "系统"
			history.Remarks = fmt.Sprintf("系统自动将状态从 %s 变更为 %s", previousStatus, status)
		}

		if err := repo.CreateStatusHistory(txCtx, history); err != nil {
			s.logger.Error("记录状态历史失败",
				zap.Error(err),
				zap.Uint("ticketID", id),
				zap.String("oldStatus", previousStatus),
				zap.String("newStatus", status))
			// 仅记录日志，不影响主流程
			return nil
		}

		s.logger.Info("报障单状态更新成功",
			zap.Uint("ticketID", id),
			zap.String("oldStatus", previousStatus),
			zap.String("newStatus", status),
			zap.String("operator", operatorName))

		return nil
	})
}

// getActivityCategory 根据状态获取活动类别
func getActivityCategory(status string) string {
	switch status {
	case common.StatusWaitingAccept:
		return "ticket_acceptance"
	case common.StatusInvestigating:
		return "diagnosis"
	case common.StatusWaitingApproval:
		return "customer_approval"
	case common.StatusApprovedWaiting:
		return "repair_preparation"
	case common.StatusRepairing, common.StatusRestarting, common.StatusMigrating, common.StatusSoftwareFixing:
		return "repair_execution"
	case common.StatusWaitingVerification:
		return "verification"
	case common.StatusSummarizing:
		return "summary"
	case common.StatusCompleted:
		return "completion"
	case common.StatusCancelled:
		return "cancellation"
	default:
		return "status_change"
	}
}

// isSLAPauseStatus 判断是否为SLA暂停状态
func isSLAPauseStatus(status string) bool {
	switch status {
	case common.StatusWaitingApproval, common.StatusApprovedWaiting, common.StatusCancelled:
		return true
	default:
		return false
	}
}

// getPauseReason 获取暂停原因
func getPauseReason(status string) string {
	switch status {
	case common.StatusWaitingApproval:
		return "等待客户审批"
	case common.StatusApprovedWaiting:
		return "等待开始维修"
	case common.StatusCancelled:
		return "工单已取消"
	default:
		return ""
	}
}

// AssignFaultTicket 分配报障单
func (s *faultTicketService) AssignFaultTicket(ctx context.Context, id uint, engineerID uint) error {
	// 直接更新字段，不尝试获取用户名
	// 无法从context.Context获取用户信息，只能使用提供的engineerID
	assignedTo := fmt.Sprintf("%d", engineerID) // 将ID转换为字符串

	updateFields := map[string]interface{}{
		"assigned_to":     assignedTo,
		"assignment_time": time.Now(),
	}

	return s.repo.UpdateFields(ctx, id, updateFields)
}

// CloseFaultTicket 关闭报障单
func (s *faultTicketService) CloseFaultTicket(ctx context.Context, id uint, summary string) error {
	// 获取当前报障单
	ticket, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("获取报障单失败: %w", err)
	}

	// 更新状态为已完成
	ticket.Status = common.StatusCompleted
	ticket.FaultSummary = summary
	now := time.Now()
	ticket.CloseTime = &now

	if err := s.repo.Update(ctx, ticket); err != nil {
		return fmt.Errorf("更新报障单状态失败: %w", err)
	}

	// 记录状态历史
	history := &model.FaultTicketStatusHistory{
		FaultTicketID:  id,
		PreviousStatus: ticket.Status,
		NewStatus:      common.StatusCompleted,
		OperatorID:     ticket.ReporterID,
		OperatorName:   ticket.ReporterName,
		OperationTime:  now,
		Remarks:        fmt.Sprintf("关闭报障单: %s", summary),
	}
	if err := s.repo.CreateStatusHistory(ctx, history); err != nil {
		// 仅记录日志，不影响主流程
		fmt.Printf("记录状态历史失败: %v\n", err)
	}

	return nil
}

// ListFaultTickets 分页获取报障单列表
func (s *faultTicketService) ListFaultTickets(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*model.FaultTicket, int64, error) {
	// 解析基本筛选条件
	query, ok := filters["query"].(string)
	if !ok {
		query = ""
	}

	// 处理status参数，支持字符串或字符串数组
	var statusValues []string
	if statusArray, ok := filters["status"].([]string); ok && len(statusArray) > 0 {
		statusValues = statusArray
	} else if status, ok := filters["status"].(string); ok && status != "" {
		statusValues = []string{status}
	}

	priority, ok := filters["priority"].(string)
	if !ok {
		priority = ""
	}

	// 解析新增的筛选条件
	title, ok := filters["title"].(string)
	if !ok {
		title = ""
	}

	resourceIdentifier, ok := filters["resource_identifier"].(string)
	if !ok {
		resourceIdentifier = ""
	}

	faultType, ok := filters["faultType"].(string)
	if !ok {
		faultType = ""
	}

	faultDetailType, ok := filters["fault_detail_type"].(string)
	if !ok {
		faultDetailType = ""
	}

	reporterName, ok := filters["reporterName"].(string)
	if !ok {
		reporterName = ""
	}

	assignedTo, ok := filters["assignedTo"].(string)
	if !ok {
		assignedTo = ""
	}

	startTime, ok := filters["startTime"].(*time.Time)
	if !ok {
		startTime = nil
	}

	endTime, ok := filters["endTime"].(*time.Time)
	if !ok {
		endTime = nil
	}

	// 构建查询条件
	conditions := make(map[string]interface{})

	// 处理原有的查询条件
	if query != "" {
		// 原有的query支持搜索设备SN和工单编号
		conditions["query"] = query
	}

	// 处理状态条件
	if len(statusValues) > 0 {
		conditions["status"] = statusValues
	}

	if priority != "" {
		conditions["priority"] = priority
	}

	// 处理新增的查询条件
	if title != "" {
		conditions["title"] = title
	}
	if resourceIdentifier != "" {
		conditions["resource_identifier"] = resourceIdentifier
	}
	if faultType != "" {
		conditions["fault_type"] = faultType
	}
	if faultDetailType != "" {
		conditions["fault_detail_type"] = faultDetailType
	}
	if reporterName != "" {
		conditions["reporter_name"] = reporterName
	}
	if assignedTo != "" {
		conditions["assigned_to"] = assignedTo
	}

	// 处理时间范围
	if startTime != nil {
		conditions["start_time"] = startTime
	}
	if endTime != nil {
		conditions["end_time"] = endTime
	}

	return s.repo.List(ctx, page, pageSize, conditions)
}

// GetFaultTicketStatusHistory 获取报障单状态历史
func (s *faultTicketService) GetFaultTicketStatusHistory(ctx context.Context, id uint) ([]*model.FaultTicketStatusHistory, error) {
	return s.repo.GetStatusHistory(ctx, id)
}

// StartFaultTicketWorkflow 启动报障单工作流
func (s *faultTicketService) StartFaultTicketWorkflow(ctx context.Context, ticketID uint) error {
	// 检查temporalClient是否为nil
	if s.temporalClient == nil {
		// 如果temporalClient为nil，记录一个警告并返回，但不视为错误
		fmt.Printf("Temporal客户端未初始化，跳过工作流启动\n")
		return nil
	}

	// 创建带超时的上下文
	dbCtx, cancel := context.WithTimeout(ctx, 2*time.Second)
	defer cancel()

	// 获取报障单前记录操作
	fmt.Printf("准备获取工单(ID: %d)用于启动工作流\n", ticketID)

	// 获取报障单
	ticket, err := s.repo.GetByID(dbCtx, ticketID)
	if err != nil {
		// 添加详细错误日志，但不执行数据库修改操作
		fmt.Printf("获取报障单失败(ID: %d): %v\n", ticketID, err)
		return fmt.Errorf("获取报障单失败: %w", err)
	}

	if ticket == nil {
		fmt.Printf("工单不存在(ID: %d)\n", ticketID)
		return fmt.Errorf("工单不存在，无法启动工作流")
	}

	// 检查工单状态，如果是终态则不启动工作流
	if ticket.Status == common.StatusCompleted || ticket.Status == common.StatusCancelled {
		fmt.Printf("工单ID=%d已处于终态(%s)，不启动工作流\n", ticket.ID, ticket.Status)
		return nil
	}

	fmt.Printf("成功获取工单(ID: %d, TicketNo: %s)准备启动工作流\n", ticket.ID, ticket.TicketNo)

	// 创建工作流输入 - 传递完整的报障单关键信息
	input := workflow.FaultTicketWorkflowInput{
		TicketID:         ticket.ID,
		ReporterID:       ticket.ReporterID,
		ReporterName:     ticket.ReporterName,
		DeviceID:         nil,        // 先初始化为nil
		ResourceID:       UintPtr(0), // 固定为0，不使用资源ID
		FaultType:        ticket.FaultType,
		FaultDescription: ticket.FaultDescription,
		Symptom:          ticket.Symptom,
		SlotPosition:     ticket.SlotPosition,
		Priority:         ticket.Priority,
		Source:           ticket.Source,
		// 补充更多关键字段
		DeviceSN:           ticket.DeviceSN,
		ComponentSN:        ticket.ComponentSN,
		ComponentType:      ticket.ComponentType,
		ResourceIdentifier: ticket.ResourceIdentifier,
		RepairMethod:       ticket.RepairMethod,
		Title:              ticket.Title,
		TicketNo:           ticket.TicketNo,
		Status:             ticket.Status,
		// 不传递AssignedTo字段，它现在是字符串类型
		// AssignedTo:         ticket.AssignedTo,
		RequireApproval: ticket.RequireApproval,
		ImmediateRepair: ticket.ImmediateRepair,
		Completed:       false, // 明确设置为false
	}

	// 启动工作流，使用原始上下文以确保有足够时间
	workflowOptions := client.StartWorkflowOptions{
		ID:                  fmt.Sprintf("%s%d", workflow.FaultTicketWorkflowIDPrefix, ticketID),
		TaskQueue:           workflow.FaultTicketTaskQueue,
		WorkflowRunTimeout:  24 * time.Hour,   // 更改为24小时，防止过早超时
		WorkflowTaskTimeout: 30 * time.Second, // 更改为30秒
	}

	// 记录将要启动的工作流信息
	fmt.Printf("准备启动工作流，ID: %s, 工单ID: %d\n",
		workflowOptions.ID, ticketID)

	// 创建更短超时的上下文，避免长时间等待
	workflowCtx, workflowCancel := context.WithTimeout(ctx, 5*time.Second)
	defer workflowCancel()

	// 使用通道和goroutine组合实现带超时的工作流启动
	errChan := make(chan error, 1)
	go func() {
		we, err := s.temporalClient.ExecuteWorkflow(workflowCtx, workflowOptions, workflow.FaultTicketWorkflow, input)
		if err != nil {
			// 区分连接错误和业务逻辑错误
			if isTemporalConnectionError(err) {
				errChan <- fmt.Errorf("temporal服务连接失败: %w", err)
			} else {
				errChan <- fmt.Errorf("启动工作流失败: %w", err)
			}
			return
		}
		// 不等待工作流执行完成，只记录工作流ID
		fmt.Printf("成功启动工作流，ID: %s, RunID: %s\n", we.GetID(), we.GetRunID())
		errChan <- nil
	}()

	// 等待启动结果或超时
	select {
	case err := <-errChan:
		if err != nil {
			fmt.Printf("工作流启动失败: %v，但不会影响工单数据\n", err)
		}
		return err
	case <-time.After(4500 * time.Millisecond): // 比上下文超时稍短，确保我们先处理超时
		// 记录超时但不返回错误，让主流程继续
		fmt.Printf("工作流启动操作超时，工单ID: %d，将在后台继续尝试\n", ticketID)
		return nil
	}
}

// isTemporalConnectionError 判断是否为Temporal连接错误
func isTemporalConnectionError(err error) bool {
	if err == nil {
		return false
	}

	errMsg := err.Error()
	return strings.Contains(errMsg, "connection refused") ||
		strings.Contains(errMsg, "deadline exceeded") ||
		strings.Contains(errMsg, "connection reset") ||
		strings.Contains(errMsg, "broken pipe") ||
		strings.Contains(errMsg, "connect: connection timed out")
}

// generateTicketNo 生成工单号
func (s *faultTicketService) generateTicketNo() string {
	// 使用时间戳和随机数生成工单号
	timestamp := time.Now().Format("20060102150405")

	// 使用crypto/rand代替math/rand
	randomBytes := make([]byte, 4)
	var random int
	if _, err := cryptorand.Read(randomBytes); err != nil {
		// 如果加密随机数生成失败，使用时间纳秒作为备选
		random = int(time.Now().UnixNano() % 1000)
		s.logger.Warn("使用加密随机数生成失败，使用时间纳秒替代", zap.Error(err))
	} else {
		// 使用加密随机数，取模确保在0-999范围内
		random = int(binary.BigEndian.Uint32(randomBytes) % 1000)
	}

	return fmt.Sprintf("i%s%03d", timestamp, random)
}

// UintPtr 辅助函数，返回指向uint的指针
func UintPtr(v uint) *uint {
	return &v
}

// RecoverInconsistentWorkflows 恢复不一致的工作流状态
func (s *faultTicketService) RecoverInconsistentWorkflows(ctx context.Context) error {
	if s.temporalClient == nil {
		return fmt.Errorf("工作流客户端未初始化")
	}

	// 获取所有未完成的报障单
	tickets, _, err := s.repo.List(ctx, 1, 1000, map[string]interface{}{})
	if err != nil {
		return fmt.Errorf("获取报障单列表失败: %w", err)
	}

	for _, ticket := range tickets {
		// 检查工单状态，如果是终态则不重新启动工作流
		if ticket.Status == common.StatusCompleted || ticket.Status == common.StatusCancelled {
			//fmt.Printf("工单ID=%d已处于终态(%s)，不重新启动工作流\n", ticket.ID, ticket.Status)
			continue
		}

		workflowID := fmt.Sprintf("fault_ticket_%d", ticket.ID)
		// 检查工作流是否存在
		_, err := s.temporalClient.DescribeWorkflowExecution(ctx, workflowID, "")
		if err != nil {
			// 工作流不存在，重新启动
			_, err := s.temporalClient.ExecuteWorkflow(ctx, client.StartWorkflowOptions{
				ID:        workflowID,
				TaskQueue: common.FaultTicketTaskQueue,
			}, workflow.FaultTicketWorkflow, workflow.FaultTicketWorkflowInput{
				TicketID:         ticket.ID,
				ReporterID:       ticket.ReporterID,
				ReporterName:     ticket.ReporterName,
				FaultType:        ticket.FaultType,
				FaultDescription: ticket.FaultDescription,
				Symptom:          ticket.Symptom,
				SlotPosition:     ticket.SlotPosition,
				Priority:         ticket.Priority,
				Source:           ticket.Source,
				DeviceSN:         ticket.DeviceSN,
				ComponentSN:      ticket.ComponentSN,
				ComponentType:    ticket.ComponentType,
				Status:           ticket.Status,
				TicketNo:         ticket.TicketNo,
				Completed:        false, // 确保明确设置为false
			})
			if err != nil {
				fmt.Printf("恢复工作流失败 [%d]: %v\n", ticket.ID, err)
			}
		}
	}

	return nil
}

// TriggerWorkflowStage 触发工作流阶段
func (s *faultTicketService) TriggerWorkflowStage(
	ctx context.Context,
	ticketID uint,
	stage string,
	operatorID uint,
	operatorName string,
	comments string,
	data map[string]interface{},
) error {
	// 验证工作流阶段参数
	if stage == "" {
		return fmt.Errorf("工作流阶段不能为空")
	}

	// 获取当前工单，但不更新其状态
	ticket, err := s.repo.GetByID(ctx, ticketID)
	if err != nil {
		return fmt.Errorf("获取工单失败: %w", err)
	}

	// 检查工单状态，如果是终态，则不允许触发工作流
	if ticket.Status == common.StatusCompleted || ticket.Status == common.StatusCancelled {
		return fmt.Errorf("工单已处于终态(%s)，不能触发工作流", ticket.Status)
	}

	// 验证阶段名是否有效
	isValid, message := common.ValidateStageTransition(ticket.Status, stage)
	if !isValid {
		return fmt.Errorf("无效的工作流阶段: %s - %s", stage, message)
	}

	// 特殊处理：对于summary阶段或cancel阶段的情况，直接更新数据库中的字段
	if (stage == "summary" || stage == "cancel") && data != nil {
		// 对于cancel阶段，或者summary阶段中status为cancelled的情况
		if stage == "cancel" || (stage == "summary" && data["status"] != nil) {
			statusVal, ok := data["status"].(string)
			if !ok {
				s.logger.Warn("状态类型断言失败", zap.Any("status", data["status"]))
			} else if statusVal == "cancelled" {
				// 先更新状态为cancelled，确保更新函数也能处理取消状态相关标志
				if updateErr := s.UpdateFaultTicketStatus(ctx, ticketID, "cancelled", operatorID, operatorName); updateErr != nil {
					s.logger.Warn("直接更新工单状态为已取消失败",
						zap.Error(updateErr),
						zap.Uint("ticketID", ticketID))
					// 继续处理，不中断流程
				}

				// 专门处理relatedTicketID - 尝试多种可能的键名和类型
				var relatedID float64
				var hasRelatedID bool

				// 从data中提取关联工单ID
				if val, ok := data["relatedTicketID"].(float64); ok && val > 0 {
					relatedID = val
					hasRelatedID = true
					s.logger.Info("从relatedTicketID(float64)获取关联ID",
						zap.Uint("ticketID", ticketID),
						zap.Float64("relatedTicketID", val))
				} else if val, ok := data["relatedTicketID"].(int); ok && val > 0 {
					relatedID = float64(val)
					hasRelatedID = true
					s.logger.Info("从relatedTicketID(int)获取关联ID",
						zap.Uint("ticketID", ticketID),
						zap.Int("relatedTicketID", val))
				} else if val, ok := data["relatedTicketID"].(string); ok && val != "" {
					// 尝试将字符串转换为数字
					var parsedVal int
					_, err := fmt.Sscanf(val, "%d", &parsedVal)
					if err == nil && parsedVal > 0 {
						relatedID = float64(parsedVal)
						hasRelatedID = true
						s.logger.Info("从relatedTicketID(string)获取关联ID",
							zap.Uint("ticketID", ticketID),
							zap.String("relatedTicketID", val),
							zap.Int("parsedVal", parsedVal))
					}
				} else if val, ok := data["related_ticket_id"].(float64); ok && val > 0 {
					// 尝试使用snake_case键名
					relatedID = val
					hasRelatedID = true
					s.logger.Info("从related_ticket_id获取关联ID",
						zap.Uint("ticketID", ticketID),
						zap.Float64("relatedTicketID", val))
				}

				// 如果找到了relatedID
				if hasRelatedID {
					// 直接明确更新关联工单ID
					relatedFields := map[string]interface{}{
						"related_ticket_id": uint(relatedID),
					}

					if err := s.repo.UpdateFields(ctx, ticketID, relatedFields); err != nil {
						s.logger.Error("更新关联工单ID失败",
							zap.Error(err),
							zap.Uint("ticketID", ticketID),
							zap.Float64("relatedTicketID", relatedID))
					} else {
						s.logger.Info("成功直接更新关联工单ID",
							zap.Uint("ticketID", ticketID),
							zap.Float64("relatedTicketID", relatedID),
							zap.Uint("convertedID", uint(relatedID)))
					}
				}

				// 从data中获取isFalseAlarm和isDuplicateFault字段
				fields := make(map[string]interface{})

				// 直接更新count_in_sla字段为false
				fields["count_in_sla"] = false
				fields["status"] = "cancelled"

				// 获取取消类型
				var cancellationType string
				if ct, ok := data["cancellation_type"].(string); ok && ct != "" {
					cancellationType = ct
					s.logger.Info("从取消类型字段获取类型",
						zap.Uint("ticketID", ticketID),
						zap.String("cancellationType", cancellationType))
				}

				// 检查取消类型以决定是否设置isFalseAlarm或isDuplicateFault
				switch cancellationType {
				case "false_alarm":
					fields["is_false_alarm"] = true
					s.logger.Info("根据cancellationType='false_alarm'直接设置is_false_alarm=true",
						zap.Uint("ticketID", ticketID))
				case "duplicate_fault":
					fields["is_duplicate_fault"] = true
					s.logger.Info("根据cancellationType='duplicate_fault'直接设置is_duplicate_fault=true",
						zap.Uint("ticketID", ticketID))
				}

				// 处理isFalseAlarm字段（优先级高于取消类型）
				if isFalseAlarm, ok := data["isFalseAlarm"].(bool); ok && isFalseAlarm {
					fields["is_false_alarm"] = true
					s.logger.Info("取消时直接更新is_false_alarm字段为true",
						zap.Uint("ticketID", ticketID))

					// 额外使用UpdateFields方法确保字段被更新
					if err := s.repo.UpdateFields(ctx, ticketID, map[string]interface{}{
						"is_false_alarm": true,
					}); err != nil {
						s.logger.Error("直接更新is_false_alarm字段失败",
							zap.Error(err),
							zap.Uint("ticketID", ticketID))
					} else {
						s.logger.Info("成功直接更新is_false_alarm=true",
							zap.Uint("ticketID", ticketID))
					}
				}

				// 处理isDuplicateFault字段（优先级高于取消类型）
				if isDuplicateFault, ok := data["isDuplicateFault"].(bool); ok && isDuplicateFault {
					fields["is_duplicate_fault"] = true
					s.logger.Info("取消时直接更新is_duplicate_fault字段为true",
						zap.Uint("ticketID", ticketID))

					// 额外使用UpdateFields方法确保字段被更新
					updateFields := map[string]interface{}{
						"is_duplicate_fault": true,
					}

					// 处理关联工单ID - 尝试多种可能的格式
					var relatedID uint
					var hasRelatedID bool

					if val, ok := data["relatedTicketID"].(float64); ok && val > 0 {
						relatedID = uint(val)
						hasRelatedID = true
						s.logger.Info("从isDuplicateFault处理中获取关联ID(float64)",
							zap.Uint("ticketID", ticketID),
							zap.Float64("relatedTicketID", val),
							zap.Uint("convertedID", uint(val)))
					} else if val, ok := data["relatedTicketID"].(int); ok && val > 0 {
						relatedID = uint(val)
						hasRelatedID = true
						s.logger.Info("从isDuplicateFault处理中获取关联ID(int)",
							zap.Uint("ticketID", ticketID),
							zap.Int("relatedTicketID", val))
					} else if val, ok := data["relatedTicketID"].(string); ok && val != "" {
						var numVal int
						_, err := fmt.Sscanf(val, "%d", &numVal)
						if err == nil && numVal > 0 {
							relatedID = uint(numVal)
							hasRelatedID = true
							s.logger.Info("从isDuplicateFault处理中获取关联ID(string)",
								zap.Uint("ticketID", ticketID),
								zap.String("relatedTicketID", val),
								zap.Int("parsedValue", numVal))
						}
					}

					if hasRelatedID {
						updateFields["related_ticket_id"] = relatedID

						// 添加额外的日志以确认值被正确设置
						s.logger.Info("向updateFields添加关联工单ID",
							zap.Uint("ticketID", ticketID),
							zap.Uint("relatedID", relatedID),
							zap.Any("updateFields", updateFields))

						// 单独更新关联工单ID以确保它被设置
						relatedOnlyFields := map[string]interface{}{
							"related_ticket_id": relatedID,
						}
						if err := s.repo.UpdateFields(ctx, ticketID, relatedOnlyFields); err != nil {
							s.logger.Error("单独更新关联工单ID失败",
								zap.Error(err),
								zap.Uint("ticketID", ticketID),
								zap.Uint("relatedID", relatedID))
						} else {
							s.logger.Info("成功单独更新关联工单ID",
								zap.Uint("ticketID", ticketID),
								zap.Uint("relatedID", relatedID))
						}
					}

					if err := s.repo.UpdateFields(ctx, ticketID, updateFields); err != nil {
						s.logger.Error("直接更新is_duplicate_fault字段失败",
							zap.Error(err),
							zap.Uint("ticketID", ticketID))
					} else {
						s.logger.Info("成功直接更新is_duplicate_fault=true",
							zap.Uint("ticketID", ticketID),
							zap.Any("updateFields", updateFields))
					}
				}

				// 处理取消原因
				var cancelReason string
				if reason, ok := data["cancel_reason"].(string); ok && reason != "" {
					cancelReason = reason
				} else if reason, ok := data["reason"].(string); ok && reason != "" {
					cancelReason = reason
				} else if comments != "" {
					cancelReason = comments
				}

				if cancelReason != "" {
					fields["fault_summary"] = fmt.Sprintf("取消原因: %s", cancelReason)
					s.logger.Info("取消时直接更新fault_summary字段",
						zap.Uint("ticketID", ticketID),
						zap.String("reason", cancelReason))

					// 如果没有明确设置isFalseAlarm或isDuplicateFault，通过文本内容自动判断
					if _, hasFalseAlarm := fields["is_false_alarm"]; !hasFalseAlarm {
						if strings.Contains(strings.ToLower(cancelReason), "误报") {
							fields["is_false_alarm"] = true
							s.logger.Info("根据取消原因文本内容自动设置is_false_alarm=true",
								zap.Uint("ticketID", ticketID),
								zap.String("reason", cancelReason))
						}
					}

					if _, hasDuplicateFault := fields["is_duplicate_fault"]; !hasDuplicateFault {
						if strings.Contains(strings.ToLower(cancelReason), "重复") {
							fields["is_duplicate_fault"] = true
							s.logger.Info("根据取消原因文本内容自动设置is_duplicate_fault=true",
								zap.Uint("ticketID", ticketID),
								zap.String("reason", cancelReason))
						}
					}
				}

				// 只有在有字段需要更新时才执行更新
				if len(fields) > 0 {
					s.logger.Info("在触发工作流前直接更新取消相关字段",
						zap.Uint("ticketID", ticketID),
						zap.Any("fields", fields))

					if err := s.repo.UpdateFields(ctx, ticketID, fields); err != nil {
						s.logger.Error("直接更新取消相关字段失败",
							zap.Error(err),
							zap.Uint("ticketID", ticketID))
						// 继续执行，不中断流程
					}
				}
			}
		}
	}

	// 构造工作流ID
	workflowID := fmt.Sprintf("fault_ticket_%d", ticketID)

	// 构造工作流控制信号
	signal := common.WorkflowControlSignal{
		Stage:        stage,
		OperatorID:   operatorID,
		OperatorName: operatorName,
		Comments:     comments,
		Data:         data,
	}

	s.logger.Info("准备触发工作流信号",
		zap.String("workflowID", workflowID),
		zap.String("stage", stage),
		zap.Uint("operatorID", operatorID),
		zap.String("operatorName", operatorName))

	// 使用临时上下文以便控制超时
	signalCtx, signalCancel := context.WithTimeout(ctx, 3*time.Second)
	defer signalCancel()

	// 发送信号给工作流
	signalErr := s.temporalClient.SignalWorkflow(signalCtx, workflowID, "", common.WorkflowControlSignalName, signal)
	if signalErr != nil {
		// 工作流可能不存在，尝试重新启动
		if strings.Contains(signalErr.Error(), "workflow") && strings.Contains(signalErr.Error(), "not found") {
			s.logger.Warn("工作流不存在，将重新启动",
				zap.String("workflowID", workflowID),
				zap.Error(signalErr))

			// 重新启动工作流
			workflowOptions := client.StartWorkflowOptions{
				ID:        workflowID,
				TaskQueue: common.FaultTicketTaskQueue,
			}

			// 构造工作流输入
			input := workflow.FaultTicketWorkflowInput{
				TicketID:         ticket.ID,
				ReporterID:       ticket.ReporterID,
				ReporterName:     ticket.ReporterName,
				FaultType:        ticket.FaultType,
				FaultDescription: ticket.FaultDescription,
				Symptom:          ticket.Symptom,
				SlotPosition:     ticket.SlotPosition,
				Priority:         ticket.Priority,
				Source:           ticket.Source,
				DeviceSN:         ticket.DeviceSN,
				ComponentSN:      ticket.ComponentSN,
				ComponentType:    ticket.ComponentType,
				Status:           ticket.Status,
				TicketNo:         ticket.TicketNo,
				Completed:        false, // 明确设置为false
			}

			// 创建工作流启动上下文
			startCtx, startCancel := context.WithTimeout(ctx, 5*time.Second)
			defer startCancel()

			// 启动工作流
			workflowExecution, startErr := s.temporalClient.ExecuteWorkflow(startCtx, workflowOptions, workflow.FaultTicketWorkflow, input)
			if startErr != nil {
				s.logger.Error("重新启动工作流失败",
					zap.Error(startErr),
					zap.String("workflowID", workflowID))
				return fmt.Errorf("重新启动工作流失败: %w", startErr)
			}

			s.logger.Info("成功重新启动工作流",
				zap.String("workflowID", workflowID),
				zap.String("runID", workflowExecution.GetRunID()))

			// 等待一段时间，确保工作流启动
			time.Sleep(500 * time.Millisecond)

			// 再次发送信号
			if resendErr := s.temporalClient.SignalWorkflow(ctx, workflowID, "", common.WorkflowControlSignalName, signal); resendErr != nil {
				s.logger.Error("向新启动的工作流发送信号失败",
					zap.Error(resendErr),
					zap.String("workflowID", workflowID))
				return fmt.Errorf("向新启动的工作流发送信号失败: %w", resendErr)
			}

			s.logger.Info("成功向新启动的工作流发送信号",
				zap.String("workflowID", workflowID),
				zap.String("stage", stage))
			return nil
		}

		s.logger.Error("发送工作流信号失败",
			zap.Error(signalErr),
			zap.String("workflowID", workflowID))
		return fmt.Errorf("发送工作流信号失败: %w", signalErr)
	}

	s.logger.Info("成功发送工作流信号",
		zap.String("workflowID", workflowID),
		zap.String("stage", stage))
	return nil
}

// CreateRepairSelection 创建维修选择记录
func (s *faultTicketService) CreateRepairSelection(ctx context.Context, selection *model.RepairSelection) error {
	return s.repairSelectionRepo.Create(ctx, selection)
}

// CreateCustomerApproval 创建客户审批记录
func (s *faultTicketService) CreateCustomerApproval(ctx context.Context, approval *model.CustomerApproval) error {
	return s.customerApprovalRepo.Create(ctx, approval)
}

// CreateVerification 创建验证记录
func (s *faultTicketService) CreateVerification(ctx context.Context, verification *model.Verification) error {
	return s.verificationRepo.Create(ctx, verification)
}

// CreateRepairTicket 创建维修单
func (s *faultTicketService) CreateRepairTicket(ctx context.Context, faultTicketID uint, repairType string) (*model.RepairTicket, error) {
	// 检查故障单是否存在
	_, err := s.repo.GetByID(ctx, faultTicketID)
	if err != nil {
		return nil, fmt.Errorf("获取故障单失败: %w", err)
	}

	// 创建维修单
	repairTicket, err := s.repairTicketService.CreateRepairTicket(ctx, faultTicketID, repairType)
	if err != nil {
		return nil, fmt.Errorf("创建维修单失败: %w", err)
	}

	// 更新故障单的维修单ID字段
	err = s.repo.UpdateFields(ctx, faultTicketID, map[string]interface{}{
		"repair_ticket_id": repairTicket.ID,
	})
	if err != nil {
		s.logger.Error("更新故障单维修单ID失败",
			zap.Error(err),
			zap.Uint("faultTicketID", faultTicketID),
			zap.Uint("repairTicketID", repairTicket.ID))
		return repairTicket, fmt.Errorf("更新故障单维修单ID失败: %w", err)
	}

	// 记录创建维修单的日志
	s.logger.Info("成功创建维修单",
		zap.Uint("faultTicketID", faultTicketID),
		zap.Uint("repairTicketID", repairTicket.ID),
		zap.String("status", repairTicket.Status),
		zap.String("repairType", repairTicket.RepairType))

	// 注意：移除了自动授权的逻辑，确保所有维修单创建后都处于waiting_authorization状态，
	// 需要通过工作流显式授权后才能进入waiting_accept状态

	return repairTicket, nil
}

// CountDeviceFaults 根据设备SN和查询条件统计故障次数
func (s *faultTicketService) CountDeviceFaults(ctx context.Context, filters map[string]interface{}) (int64, error) {
	// 解析查询条件
	deviceSN, ok := filters["deviceSN"].(string)
	if !ok {
		return 0, fmt.Errorf("设备SN格式错误或为空")
	}
	if deviceSN == "" {
		return 0, fmt.Errorf("设备SN不能为空")
	}

	// 获取开始和结束时间
	var startTime, endTime *time.Time
	startTimeVal, ok := filters["startTime"]
	if !ok || startTimeVal == nil {
		return 0, fmt.Errorf("开始时间不能为空")
	}
	startTime, ok = startTimeVal.(*time.Time)
	if !ok {
		return 0, fmt.Errorf("开始时间格式错误")
	}

	endTimeVal, ok := filters["endTime"]
	if !ok || endTimeVal == nil {
		return 0, fmt.Errorf("结束时间不能为空")
	}
	endTime, ok = endTimeVal.(*time.Time)
	if !ok {
		return 0, fmt.Errorf("结束时间格式错误")
	}

	// 如果时间为空，则返回错误
	if startTime == nil || endTime == nil {
		return 0, fmt.Errorf("开始时间和结束时间不能为空")
	}

	// 调用仓库层方法获取数据
	return s.repo.CountMonthlyFaultsByDeviceSN(ctx, deviceSN, *startTime, *endTime)
}

// validateIPAddress 验证IP地址格式并去除前后空格
func validateIPAddress(ipStr string) (string, bool) {
	// 去除前后空格
	ipStr = strings.TrimSpace(ipStr)

	// 如果是空字符串，返回空字符串和true（认为是有效的）
	if ipStr == "" {
		return "", true
	}

	// 使用标准库验证IP格式
	ip := net.ParseIP(ipStr)
	if ip != nil {
		return ipStr, true
	}

	// 如果不是标准IP，检查是否是CIDR格式
	_, _, err := net.ParseCIDR(ipStr)
	if err == nil {
		return ipStr, true
	}

	// 检查是否是IPv4地址范围（如***********-***********0）
	ipRangePattern := regexp.MustCompile(`^(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})-(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})$`)
	if ipRangePattern.MatchString(ipStr) {
		parts := ipRangePattern.FindStringSubmatch(ipStr)
		if len(parts) == 3 {
			startIP := net.ParseIP(parts[1])
			endIP := net.ParseIP(parts[2])
			if startIP != nil && endIP != nil {
				return ipStr, true
			}
		}
	}

	return ipStr, false
}
