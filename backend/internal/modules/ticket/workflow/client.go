package workflow

import (
	"go.temporal.io/sdk/client"
	"go.uber.org/zap"
)

// InitTemporalClient 初始化Temporal客户端
// not used
func InitTemporalClient(hostPort string, logger *zap.Logger) (client.Client, error) {
	// 创建客户端选项
	options := client.Options{
		HostPort: hostPort,
		Logger:   NewTemporalZapAdapter(logger),
	}

	// 创建客户端
	c, err := client.Dial(options)
	if err != nil {
		logger.Error("Failed to create Temporal client", zap.Error(err))
		return nil, err
	}

	// 简单记录连接信息
	logger.Info("Successfully created Temporal client", zap.String("hostPort", hostPort))
	return c, nil
}

// NewTemporalZapAdapter 创建适配Temporal的Zap日志适配器
func NewTemporalZapAdapter(logger *zap.Logger) *TemporalZapAdapter {
	return &TemporalZapAdapter{
		logger: logger.With(zap.String("component", "temporal")),
	}
}

// TemporalZapAdapter 适配Temporal的Zap日志适配器
type TemporalZapAdapter struct {
	logger *zap.Logger
}

// Debug 实现Temporal的Debug日志
func (l *TemporalZapAdapter) Debug(msg string, keyvals ...interface{}) {
	fields := parseKeyvals(keyvals)
	l.logger.Debug(msg, fields...)
}

// Info 实现Temporal的Info日志
func (l *TemporalZapAdapter) Info(msg string, keyvals ...interface{}) {
	fields := parseKeyvals(keyvals)
	l.logger.Info(msg, fields...)
}

// Warn 实现Temporal的Warn日志
func (l *TemporalZapAdapter) Warn(msg string, keyvals ...interface{}) {
	fields := parseKeyvals(keyvals)
	l.logger.Warn(msg, fields...)
}

// Error 实现Temporal的Error日志
func (l *TemporalZapAdapter) Error(msg string, keyvals ...interface{}) {
	fields := parseKeyvals(keyvals)
	l.logger.Error(msg, fields...)
}

// 将Temporal的日志键值对转换为Zap字段
func parseKeyvals(keyvals []interface{}) []zap.Field {
	if len(keyvals) == 0 {
		return nil
	}

	// 取2的倍数
	n := len(keyvals)
	if len(keyvals)%2 != 0 {
		n = len(keyvals) - 1
	}

	fields := make([]zap.Field, 0, n/2)
	for i := 0; i < n; i += 2 {
		key, ok := keyvals[i].(string)
		if !ok {
			key = "unknown"
		}

		fields = append(fields, zap.Any(key, keyvals[i+1]))
	}

	// 如果键值对不成对，添加最后一个值
	if len(keyvals)%2 != 0 {
		fields = append(fields, zap.Any("unpaired", keyvals[len(keyvals)-1]))
	}

	return fields
}
