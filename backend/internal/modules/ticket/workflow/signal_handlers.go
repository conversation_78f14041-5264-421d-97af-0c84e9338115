package workflow

import (
	"context"
	"fmt"
	"strings"
	"time"

	"backend/internal/modules/ticket/repository"

	"go.temporal.io/sdk/client"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
)

// 定义信号名称
const (
	ManualActionSignal = "ManualActionSignal"
)

// ManualActionRequest 手动操作请求
type ManualActionRequest struct {
	Stage string
}

// 全局变量
var (
	manualActionRequests = make(map[string]chan bool)
	faultTicketRepo      repository.FaultTicketRepository
	logger               *zap.Logger
)

// InitSignalHandlers 初始化信号处理器
func InitSignalHandlers(repo repository.FaultTicketRepository, log *zap.Logger) {
	faultTicketRepo = repo
	logger = log
}

// WaitForManualAction 等待手动触发工作流阶段的信号
func WaitForManualAction(ctx workflow.Context, logger *zap.Logger, ticketID uint, expectedStage string, timeout time.Duration) (bool, error) {
	// 创建信号通道
	signalChan := workflow.GetSignalChannel(ctx, ManualActionSignal)

	// 创建超时定时器
	timer := workflow.NewTimer(ctx, timeout)

	// 选择器，等待信号或超时
	selector := workflow.NewSelector(ctx)
	var stage string
	var timedOut bool

	// 添加信号处理
	selector.AddReceive(signalChan, func(c workflow.ReceiveChannel, more bool) {
		c.Receive(ctx, &stage)
		workflow.GetLogger(ctx).Info("收到手动触发信号",
			zap.String("stage", stage),
			zap.Uint("ticketID", ticketID),
		)
	})

	// 添加超时处理
	selector.AddFuture(timer, func(f workflow.Future) {
		timedOut = true
		workflow.GetLogger(ctx).Info("等待手动触发信号超时",
			zap.String("expectedStage", expectedStage),
			zap.Uint("ticketID", ticketID),
		)
	})

	// 等待信号或超时
	selector.Select(ctx)

	// 如果超时，返回错误
	if timedOut {
		return false, fmt.Errorf("等待手动触发信号[%s]超时", expectedStage)
	}

	// 检查是否是期望的阶段
	if stage != expectedStage {
		return false, fmt.Errorf("收到意外的阶段信号: 期望[%s]但收到[%s]", expectedStage, stage)
	}

	return true, nil
}

// SendManualActionSignal 发送手动触发工作流的信号
func SendManualActionSignal(ctx context.Context, temporalClient client.Client, workflowID string, stage string) error {
	// 发送信号
	return temporalClient.SignalWorkflow(ctx, workflowID, "", ManualActionSignal, stage)
}

// TriggerWorkflowStage 触发工作流阶段
func TriggerWorkflowStage(ctx context.Context, temporalClient client.Client, ticketID uint, stage string) error {
	// 构造工作流ID
	workflowID := fmt.Sprintf("%s%d", FaultTicketWorkflowIDPrefix, ticketID)

	// 发送信号
	return SendManualActionSignal(ctx, temporalClient, workflowID, stage)
}

// GetWaitingStages 获取工单当前等待的工作流阶段
func GetWaitingStages(ticketID uint) []string {
	// 从内存中获取等待阶段
	stages := GetWaitingStagesFromMemory(ticketID)

	// 如果内存中没有等待阶段，检查数据库
	if len(stages) == 0 {
		stages = GetWaitingStagesFromDB(ticketID)
	}

	return stages
}

// GetWaitingStagesFromMemory 从内存中获取等待阶段
func GetWaitingStagesFromMemory(ticketID uint) []string {
	prefix := fmt.Sprintf("%s%d_", FaultTicketWorkflowIDPrefix, ticketID)
	stages := []string{}

	// 遍历所有工作流ID，查找匹配的前缀
	for workflowID := range manualActionRequests {
		if strings.HasPrefix(workflowID, prefix) {
			// 提取阶段名称
			stage := strings.TrimPrefix(workflowID, prefix)
			if stage != "" {
				stages = append(stages, stage)
			}
		}
	}

	return stages
}

// GetWaitingStagesFromDB 从数据库中获取等待阶段
func GetWaitingStagesFromDB(ticketID uint) []string {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 获取工单信息
	ticket, err := faultTicketRepo.GetByID(ctx, ticketID)
	if err != nil {
		logger.Error("获取工单信息失败", zap.Error(err), zap.Uint("ticketID", ticketID))
		return []string{}
	}

	// 如果工单正在等待手动触发，返回当前等待阶段
	if ticket != nil && ticket.WaitingManualTrigger && ticket.CurrentWaitingStage != "" {
		return []string{ticket.CurrentWaitingStage}
	}

	return []string{}
}

// WaitForWorkflowControlSignal 等待工作流控制信号（新版）
func WaitForWorkflowControlSignal(ctx workflow.Context, signalChan workflow.ReceiveChannel, expectedStage string) (*WorkflowControlSignal, error) {
	logger := workflow.GetLogger(ctx)
	logger.Info("等待工作流控制信号", "expectedStage", expectedStage)

	var signal WorkflowControlSignal
	signalChan.Receive(ctx, &signal)

	logger.Info("收到工作流控制信号",
		"stage", signal.Stage,
		"operator", signal.OperatorName,
		"expected", expectedStage)

	// 检查是否是期望的阶段
	if signal.Stage != expectedStage {
		// 这里只记录日志，但仍然接受信号
		// 因为我们希望工作流可以灵活调整，跳过某些阶段或直接进入某些阶段
		logger.Warn("收到非期望的阶段信号",
			"expected", expectedStage,
			"received", signal.Stage)
	}

	return &signal, nil
}

// SendWorkflowControlSignal 发送工作流控制信号
func SendWorkflowControlSignal(ctx context.Context, temporalClient client.Client, ticketID uint, signal WorkflowControlSignal) error {
	// 构造工作流ID
	workflowID := fmt.Sprintf("%s%d", FaultTicketWorkflowIDPrefix, ticketID)

	// 发送信号
	return temporalClient.SignalWorkflow(ctx, workflowID, "", WorkflowControlSignalName, signal)
}

// TriggerWorkflowBySignal 通过信号触发工作流到指定阶段
func TriggerWorkflowBySignal(ctx context.Context, temporalClient client.Client, ticketID uint, stage string) error {
	// 构造信号
	signal := WorkflowControlSignal{
		Stage: stage,
	}

	// 发送信号
	return SendWorkflowControlSignal(ctx, temporalClient, ticketID, signal)
}
