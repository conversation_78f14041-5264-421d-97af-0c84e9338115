package workflow

import (
	"backend/internal/modules/ticket/common"
	"backend/internal/modules/ticket/model"
	"fmt"
	"strings"
	"time"

	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

// 维修工作流状态结构体，用于管理工作流内部状态
type workflowState struct {
	Stage               string
	Status              string
	EngineerID          uint
	SpareID             uint
	RepairResult        *RepairResult // 使用shared.go中定义的类型
	VerificationSuccess bool
	Completed           bool
	SignalData          map[string]interface{}
}

// RepairWorkflow 维修工单工作流
func RepairWorkflow(ctx workflow.Context, repairTicketID uint) error {
	// 添加版本处理，解决非确定性问题
	// 这会允许新老工作流版本共存
	v := workflow.GetVersion(ctx, "RepairWorkflowNotificationCompatibility", workflow.DefaultVersion, 1)

	logger := workflow.GetLogger(ctx)
	logger.Info("维修工单工作流版本", "version", v)

	// 设置工作流选项
	ao := workflow.ActivityOptions{
		StartToCloseTimeout: 48 * time.Hour,
		HeartbeatTimeout:    10 * time.Minute,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:        InitialInterval,
			BackoffCoefficient:     BackoffCoefficient,
			MaximumInterval:        MaximumInterval,
			MaximumAttempts:        MaxAttempts,
			NonRetryableErrorTypes: []string{"InvalidInputError"},
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)

	logger.Info("开始维修工单工作流",
		"repairTicketID", repairTicketID,
		"workflowID", workflow.GetInfo(ctx).WorkflowExecution.ID,
		"runID", workflow.GetInfo(ctx).WorkflowExecution.RunID,
		"taskQueue", workflow.GetInfo(ctx).TaskQueueName)

	// 工作流状态数据
	var state workflowState

	// 默认初始化状态
	state.Stage = "waiting_authorization"
	state.Status = "waiting_authorization"

	// 获取维修单信息
	var repairTicket struct {
		ID                 uint   `json:"id"`
		FaultTicketID      uint   `json:"fault_ticket_id"`
		Status             string `json:"status"`
		RepairType         string `json:"repair_type"`
		AssignedEngineerID uint   `json:"assigned_engineer_id"`
	}
	err := workflow.ExecuteActivity(ctx, "GetRepairTicketActivity", repairTicketID).Get(ctx, &repairTicket)
	if err != nil {
		logger.Error("获取维修单信息失败", "error", err)
		return err
	}

	logger.Info("获取到维修单信息",
		"repairTicketID", repairTicketID,
		"status", repairTicket.Status,
		"repairType", repairTicket.RepairType,
		"assignedEngineerID", repairTicket.AssignedEngineerID)

	// 同步工作流内部状态与数据库状态
	if repairTicket.Status != "waiting_authorization" {
		state.Status = repairTicket.Status
		switch repairTicket.Status {
		case "waiting_accept":
			state.Stage = "waiting_accept"
		case "assigned":
			state.Stage = "assigned"
		case "in_progress":
			state.Stage = "in_progress"
		case "replacing_hardware":
			state.Stage = "replacing_hardware"
		case "waiting_verification":
			state.Stage = "waiting_verification"
		case "completed", "failed", "cancelled":
			state.Stage = repairTicket.Status
			state.Completed = true
			// 检查如果状态已经是终态，立即结束工作流
			logger.Info("维修单处于终态状态，立即结束工作流",
				"repairTicketID", repairTicketID,
				"status", repairTicket.Status)
			return nil
		default:
			state.Stage = repairTicket.Status
		}
	}

	// 如果已有分配的工程师，记录在状态中
	if repairTicket.AssignedEngineerID > 0 {
		state.EngineerID = repairTicket.AssignedEngineerID
	}

	// 工作流主循环 - 完全基于信号驱动
	for {
		// 等待下一个信号
		logger.Info("等待工作流控制信号",
			"repairTicketID", repairTicketID,
			"currentStage", state.Stage,
			"currentStatus", state.Status)

		// 在每次循环开始时检查是否已完成，提前退出
		if state.Status == "completed" || state.Status == "failed" || state.Status == "cancelled" || state.Completed {
			logger.Info("工作流状态为终态，结束工作流",
				"repairTicketID", repairTicketID,
				"status", state.Status,
				"completed", state.Completed)

			// 获取维修单关联的故障单ID，只有当获取成功且存在关联故障单时才发送通知
			var ticketInfo struct {
				FaultTicketID uint `json:"fault_ticket_id"`
			}
			getErr := workflow.ExecuteActivity(ctx, "GetRepairTicketActivity", repairTicketID).Get(ctx, &ticketInfo)
			if getErr == nil && ticketInfo.FaultTicketID > 0 {
				sendFinalNotificationToFaultTicket(ctx, ticketInfo.FaultTicketID, repairTicketID, state)
			}

			return nil
		}

		// 无限期等待信号
		var signal common.WorkflowControlSignal

		// 添加超时机制，避免永久阻塞
		signalChan := workflow.GetSignalChannel(ctx, common.WorkflowControlSignalName)

		// 使用选择器添加超时检查
		selector := workflow.NewSelector(ctx)

		// 添加信号处理
		selector.AddReceive(signalChan, func(c workflow.ReceiveChannel, more bool) {
			c.Receive(ctx, &signal)
		})

		// 添加超时检查，每10分钟检查一次是否应该结束工作流
		//timerFuture := workflow.NewTimer(ctx, 10*time.Minute)
		//selector.AddFuture(timerFuture, func(f workflow.Future) {
		//	// 检查是否处于终态
		//	if state.Status == "completed" || state.Status == "failed" || state.Status == "cancelled" || state.Completed {
		//		// 什么都不做，将在下次循环开始时退出
		//		return
		//	}
		//})

		// 等待事件
		selector.Select(ctx)

		// 没有收到信号而是触发了超时检查，继续循环
		if signal.Stage == "" {
			continue
		}

		logger.Info("收到工作流控制信号",
			"repairTicketID", repairTicketID,
			"stage", signal.Stage,
			"operator", signal.OperatorName)

		// 处理信号
		switch signal.Stage {
		case common.StageAuthorize:
			if err := handleRepairWorkflowAuthorize(ctx, repairTicketID, signal, &state); err != nil {
				logger.Error("处理维修单授权失败", "error", err)
				continue
			}

		case common.StageEngineerTake:
			if err := handleRepairWorkflowEngineerTake(ctx, repairTicketID, signal, &state); err != nil {
				logger.Error("处理工程师接单失败", "error", err)
				continue
			}

		case common.StageStartRepair:
			if err := handleRepairWorkflowStartRepair(ctx, repairTicketID, signal, &state); err != nil {
				logger.Error("处理开始维修失败", "error", err)
				continue
			}

		case common.StageArriveOnSite:
			if err := handleRepairWorkflowArriveOnSite(ctx, repairTicketID, signal, &state); err != nil {
				logger.Error("处理到达现场失败", "error", err)
				continue
			}

		case common.StageStartHardwareReplace:
			if err := handleRepairWorkflowStartHardwareReplace(ctx, repairTicketID, signal, &state); err != nil {
				logger.Error("处理开始硬件更换失败", "error", err)
				continue
			}

		case common.StageCompleteHardwareReplace:
			if err := handleRepairWorkflowCompleteHardwareReplace(ctx, repairTicketID, signal, &state); err != nil {
				logger.Error("处理完成硬件更换失败", "error", err)
				continue
			}

		case common.StageCompleteRepair:
			err := handleRepairWorkflowCompleteRepair(ctx, repairTicketID, signal, &state)
			if err != nil {
				logger.Error("处理完成维修失败", "error", err)
				continue
			}

			// 如果是完成维修信号，不管处理结果如何都直接结束工作流
			logger.Info("完成维修信号已处理，工作流将结束",
				"repairTicketID", repairTicketID,
				"status", state.Status)

			// 直接返回nil结束工作流，不再发送通知给故障单
			return nil

		case common.StageVerify:
			logger.Info("处理验证信号前状态",
				"repairTicketID", repairTicketID,
				"currentStatus", state.Status,
				"currentStage", state.Stage,
				"completed", state.Completed)

			err := handleRepairWorkflowVerify(ctx, repairTicketID, signal, &state)
			if err != nil {
				logger.Error("处理验证失败", "error", err)
				continue
			}

			// 检查验证后的状态，决定是否结束工作流
			logger.Info("处理验证信号后状态",
				"repairTicketID", repairTicketID,
				"newStatus", state.Status,
				"newStage", state.Stage,
				"completed", state.Completed)

			if state.Completed {
				// 只有当明确标记为完成时才结束工作流
				logger.Info("验证通过或工作流已标记为完成，工作流将结束",
					"repairTicketID", repairTicketID,
					"status", state.Status,
					"verification_success", state.VerificationSuccess,
					"completed", state.Completed)

				// 如果关联了故障单，发送最终通知
				if repairTicket.FaultTicketID > 0 {
					sendFinalNotificationToFaultTicket(ctx, repairTicket.FaultTicketID, repairTicketID, state)
				}

				// 返回nil结束工作流
				return nil
			}

			// 验证失败或不需要结束工作流，继续等待信号
			logger.Info("验证未通过或不需要结束工作流，继续等待信号",
				"repairTicketID", repairTicketID,
				"status", state.Status,
				"verification_success", state.VerificationSuccess,
				"completed", state.Completed)

			continue

		default:
			logger.Error("未知的工作流阶段", "stage", signal.Stage)
		}
	}
}

// sendFinalNotificationToFaultTicket 向故障单发送最终通知
func sendFinalNotificationToFaultTicket(ctx workflow.Context, faultTicketID uint, repairTicketID uint, state workflowState) {
	logger := workflow.GetLogger(ctx)
	faultWorkflowID := fmt.Sprintf("%s%d", common.FaultTicketWorkflowIDPrefix, faultTicketID)

	var solution, repairSteps string
	if state.RepairResult != nil {
		solution = state.RepairResult.Solution
		repairSteps = state.RepairResult.RepairSteps
	}

	// 如果验证失败，发送需要继续维修的信号
	if state.Status == "failed" {
		signalErr := workflow.SignalExternalWorkflow(ctx, faultWorkflowID, "", common.WorkflowControlSignalName, common.WorkflowControlSignal{
			Stage:      common.StageCompleteVerification,
			OperatorID: 0, // 系统操作
			Comments:   "验证失败，需要继续维修",
			Data: map[string]interface{}{
				"repair_ticket_id": repairTicketID,
				"success":          false,
				"repair_result":    state.Status,
				"solution":         solution,
				"repair_steps":     repairSteps,
				"status":           "verification_failed",
			},
		}).Get(ctx, nil)

		if signalErr != nil {
			logger.Error("向故障单工作流发送验证失败信号失败", "error", signalErr)
		} else {
			logger.Info("成功向故障单工作流发送验证失败信号")
		}
		return
	}

	// 验证成功或其他终态，发送正常完成信号
	signalErr := workflow.SignalExternalWorkflow(ctx, faultWorkflowID, "", common.WorkflowControlSignalName, common.WorkflowControlSignal{
		Stage:      common.StageCompleteVerification,
		OperatorID: 0, // 系统操作
		Comments:   "验证完成，维修成功",
		Data: map[string]interface{}{
			"repair_ticket_id": repairTicketID,
			"success":          state.Status == "completed",
			"repair_result":    state.Status,
			"solution":         solution,
			"repair_steps":     repairSteps,
			"status":           "verification_passed",
		},
	}).Get(ctx, nil)

	if signalErr != nil {
		logger.Error("向故障单工作流发送最终信号失败", "error", signalErr)
	} else {
		logger.Info("成功向故障单工作流发送最终状态信号")
	}
}

// handleRepairWorkflowEngineerTake 处理工程师接单信号
func handleRepairWorkflowEngineerTake(ctx workflow.Context, repairTicketID uint, signal common.WorkflowControlSignal, state *workflowState) error {
	logger := workflow.GetLogger(ctx)

	// 先尝试获取当前维修单状态，确保工作流状态与数据库状态一致
	var currentTicket struct {
		Status string `json:"status"`
	}
	err := workflow.ExecuteActivity(ctx, "GetRepairTicketActivity", repairTicketID).Get(ctx, &currentTicket)
	if err == nil {
		logger.Info("获取当前维修单状态成功", "currentStatus", currentTicket.Status)
		if currentTicket.Status == "assigned" {
			// 数据库状态已经是 assigned，同步工作流状态，并进入下一阶段（开始维修）
			state.Status = "assigned"
			state.Stage = "engineer_take"
			state.EngineerID = signal.OperatorID

			// 记录工程师ID以便后续使用
			if engineerIDFloat, ok := signal.Data["engineer_id"].(float64); ok {
				state.EngineerID = uint(engineerIDFloat)
			}

			// 如果操作人ID是0，但指定了工程师ID，使用工程师ID
			if state.EngineerID == 0 && signal.OperatorID > 0 {
				state.EngineerID = signal.OperatorID
			}

			logger.Info("工作流状态已同步", "stage", state.Stage, "status", state.Status)
			return nil
		}
	}

	// 需要执行接单活动
	logger.Info("执行接单活动", "engineerID", signal.OperatorID)

	// 记录工程师ID以便后续使用
	if engineerIDFloat, ok := signal.Data["engineer_id"].(float64); ok {
		state.EngineerID = uint(engineerIDFloat)
	} else {
		state.EngineerID = signal.OperatorID
	}

	// 执行接单活动
	err = workflow.ExecuteActivity(ctx, "EngineerTakeRepairTicketActivity", repairTicketID, state.EngineerID).Get(ctx, nil)
	if err != nil {
		logger.Error("工程师接单失败", "error", err)
		// 活动执行失败，更新维修单状态为等待接单
		updateErr := workflow.ExecuteActivity(ctx, "UpdateRepairTicketStatusActivity", repairTicketID, "waiting_accept", signal.OperatorID, "接单失败: "+err.Error(), signal.OperatorName).Get(ctx, nil)
		if updateErr != nil {
			logger.Error("更新维修单状态失败", "error", updateErr)
		}
		return err
	}

	// 更新工作流状态
	state.Status = "assigned"
	state.Stage = "engineer_take"
	logger.Info("工程师接单成功", "engineerID", state.EngineerID)
	return nil
}

// handleRepairWorkflowStartRepair 处理开始维修信号
func handleRepairWorkflowStartRepair(ctx workflow.Context, repairTicketID uint, signal common.WorkflowControlSignal, state *workflowState) error {
	logger := workflow.GetLogger(ctx)
	logger.Info("处理开始维修信号", "repairTicketID", repairTicketID)

	// 先尝试获取维修单当前状态
	var currentTicket struct {
		Status string `json:"status"`
	}
	err := workflow.ExecuteActivity(ctx, "GetRepairTicketActivity", repairTicketID).Get(ctx, &currentTicket)
	if err == nil && currentTicket.Status == "in_progress" {
		// 已经是维修中状态，只更新工作流状态
		state.Status = "in_progress"
		state.Stage = "in_progress"
		logger.Info("维修单已经是维修中状态，只更新工作流状态")
		return nil
	}

	// 直接更新状态为 in_progress
	err = workflow.ExecuteActivity(ctx, "UpdateRepairTicketStatusActivity", repairTicketID, "in_progress", signal.OperatorID, "工程师开始维修", signal.OperatorName).Get(ctx, nil)
	if err != nil {
		logger.Error("更新维修单状态失败", "error", err)
		return err
	}

	// 更新工作流状态
	state.Status = "in_progress"
	state.Stage = "in_progress"
	logger.Info("开始维修处理完成", "status", state.Status)
	return nil
}

// handleRepairWorkflowArriveOnSite 处理到达现场信号
func handleRepairWorkflowArriveOnSite(ctx workflow.Context, repairTicketID uint, signal common.WorkflowControlSignal, state *workflowState) error {
	logger := workflow.GetLogger(ctx)

	// 只记录到达现场，不改变状态
	err := workflow.ExecuteActivity(ctx, "ArriveOnSiteActivity", repairTicketID).Get(ctx, nil)
	if err != nil {
		logger.Error("ArriveOnSiteActivity failed", "error", err)
		return err
	}

	return nil
}

// handleRepairWorkflowStartHardwareReplace 处理开始硬件更换信号
func handleRepairWorkflowStartHardwareReplace(ctx workflow.Context, repairTicketID uint, signal common.WorkflowControlSignal, state *workflowState) error {
	logger := workflow.GetLogger(ctx)
	logger.Info("处理开始硬件更换信号", "repairTicketID", repairTicketID)

	// 先尝试获取维修单当前状态
	var currentTicket struct {
		Status string `json:"status"`
	}
	err := workflow.ExecuteActivity(ctx, "GetRepairTicketActivity", repairTicketID).Get(ctx, &currentTicket)
	if err == nil && currentTicket.Status == "replacing_hardware" {
		// 已经是硬件更换状态，只更新工作流状态
		state.Status = "replacing_hardware"
		state.Stage = "replacing_hardware"
		logger.Info("维修单已经是硬件更换状态，只更新工作流状态")
		return nil
	}

	// 记录硬件更换开始
	err = workflow.ExecuteActivity(ctx, "RecordHardwareReplaceStartActivity", repairTicketID).Get(ctx, nil)
	if err != nil {
		logger.Error("记录硬件更换开始失败", "error", err)
		return err
	}

	// 直接更新状态为 replacing_hardware
	err = workflow.ExecuteActivity(ctx, "UpdateRepairTicketStatusActivity", repairTicketID, "replacing_hardware", signal.OperatorID, "开始更换硬件", signal.OperatorName).Get(ctx, nil)
	if err != nil {
		logger.Error("更新维修单状态失败", "error", err)
		return err
	}

	// 更新工作流状态
	state.Status = "replacing_hardware"
	state.Stage = "replacing_hardware"
	logger.Info("开始硬件更换处理完成", "status", state.Status)
	return nil
}

// handleRepairWorkflowCompleteHardwareReplace 处理完成硬件更换信号
func handleRepairWorkflowCompleteHardwareReplace(ctx workflow.Context, repairTicketID uint, signal common.WorkflowControlSignal, state *workflowState) error {
	logger := workflow.GetLogger(ctx)
	logger.Info("处理完成硬件更换信号", "repairTicketID", repairTicketID, "currentStatus", state.Status)

	// 从信号中获取备件ID和成功标志
	var spareID uint
	isSuccess := true
	details := ""
	originalStatus := signal.Data["status"]

	// 收集维修结果信息，包括来自信号的repair_result
	var repairResult RepairResult
	if signal.Data != nil {
		if id, ok := signal.Data["spare_id"].(float64); ok {
			spareID = uint(id)
			state.SpareID = spareID
		}

		if success, ok := signal.Data["success"].(bool); ok {
			isSuccess = success
		}

		if detailsStr, ok := signal.Data["details"].(string); ok {
			details = detailsStr
		}

		// 收集维修结果信息
		if result, ok := signal.Data["repair_result"].(string); ok && result != "" {
			repairResult.RepairResult = result
		} else {
			// 如果没有提供结果，使用信号中的status或默认值
			if status, ok := signal.Data["status"].(string); ok && status != "" {
				repairResult.RepairResult = status
			} else {
				repairResult.RepairResult = "hardware_replace_completed"
			}
		}

		if solution, ok := signal.Data["solution"].(string); ok && solution != "" {
			repairResult.Solution = solution
		}

		if steps, ok := signal.Data["repair_steps"].(string); ok && steps != "" {
			repairResult.RepairSteps = steps
		}

		// 保存新部件和旧部件SN到维修结果中
		if newPartSN, ok := signal.Data["new_part_sn"].(string); ok && newPartSN != "" {
			if repairResult.RepairSteps == "" {
				repairResult.RepairSteps = "更换部件: "
			} else {
				repairResult.RepairSteps += "\n更换部件: "
			}
			repairResult.RepairSteps += newPartSN
		}

		if oldPartSN, ok := signal.Data["old_part_sn"].(string); ok && oldPartSN != "" {
			if repairResult.RepairSteps == "" {
				repairResult.RepairSteps = "替换部件: "
			} else {
				repairResult.RepairSteps += "\n替换部件: "
			}
			repairResult.RepairSteps += oldPartSN
		}

		// 保存到工作流状态
		state.RepairResult = &repairResult
	}

	// 标记硬件更换完成
	err := workflow.ExecuteActivity(ctx, "CompleteHardwareReplaceActivity", repairTicketID, isSuccess, details).Get(ctx, nil)
	if err != nil {
		logger.Error("标记硬件更换完成失败", "error", err)
		return err
	}

	// 设置为等待验证状态，无论之前是什么状态
	newStatus := "waiting_verification"
	if !isSuccess {
		newStatus = "hardware_replace_failed"
	}

	// 更新维修单状态
	err = workflow.ExecuteActivity(ctx, "UpdateRepairTicketStatusActivity", repairTicketID, newStatus, signal.OperatorID, fmt.Sprintf("硬件更换%s，等待验证", details), signal.OperatorName).Get(ctx, nil)
	if err != nil {
		logger.Error("更新维修单状态失败", "error", err)
		return err
	}

	// 更新工作流状态
	state.Status = newStatus
	state.Stage = "waiting_verification"
	logger.Info("硬件更换完成，进入等待验证状态",
		"status", state.Status,
		"success", isSuccess,
		"originalStatus", originalStatus)

	// 获取维修单关联的故障单ID，并向报障单发送验证信号
	var ticketInfo struct {
		FaultTicketID uint `json:"fault_ticket_id"`
	}
	getErr := workflow.ExecuteActivity(ctx, "GetRepairTicketActivity", repairTicketID).Get(ctx, &ticketInfo)

	// 添加日志输出，便于问题诊断
	if getErr != nil {
		logger.Error("获取维修单关联的故障单ID失败", "error", getErr)
	} else {
		logger.Info("获取维修单关联的故障单ID", "repairTicketID", repairTicketID, "faultTicketID", ticketInfo.FaultTicketID)
	}

	if getErr == nil && ticketInfo.FaultTicketID > 0 {
		// 向故障单发送验证信号
		logger.Info("准备向故障单发送验证信号", "faultTicketID", ticketInfo.FaultTicketID)

		// 确保RepairResult不为空
		if state.RepairResult == nil {
			state.RepairResult = &repairResult
		}

		sendVerificationNotificationToFaultTicket(ctx, ticketInfo.FaultTicketID, repairTicketID, *state)
	} else {
		logger.Info("没有关联的故障单，跳过发送验证不通过通知", "repairTicketID", repairTicketID)
	}

	// 发送飞书通知 - 维修单待验证通知
	notifyErr := workflow.ExecuteActivity(ctx, "SendRepairTicketWaitingVerificationNotificationActivity", repairTicketID).Get(ctx, nil)
	if notifyErr != nil {
		logger.Warn("发送维修单待验证通知失败",
			"error", notifyErr,
			"repairTicketID", repairTicketID)
		// 通知失败不影响主工作流
	} else {
		logger.Info("成功发送维修单待验证通知",
			"repairTicketID", repairTicketID)
	}

	return nil
}

// sendVerificationNotificationToFaultTicket 向故障单发送验证通知
func sendVerificationNotificationToFaultTicket(ctx workflow.Context, faultTicketID uint, repairTicketID uint, state workflowState) {
	logger := workflow.GetLogger(ctx)
	faultWorkflowID := fmt.Sprintf("%s%d", common.FaultTicketWorkflowIDPrefix, faultTicketID)

	var solution, repairSteps, repairResult string
	if state.RepairResult != nil {
		solution = state.RepairResult.Solution
		repairSteps = state.RepairResult.RepairSteps
		repairResult = state.RepairResult.RepairResult

		// 如果RepairResult为空，使用默认值
		if repairResult == "" {
			repairResult = "hardware_replace_completed"
		}
	} else {
		repairResult = "hardware_replace_completed"
	}

	logger.Info("准备向故障单发送验证信号",
		"faultTicketID", faultTicketID,
		"repairTicketID", repairTicketID,
		"faultWorkflowID", faultWorkflowID,
		"solution", solution,
		"repairResult", repairResult)

	signalErr := workflow.SignalExternalWorkflow(ctx, faultWorkflowID, "", common.WorkflowControlSignalName, common.WorkflowControlSignal{
		Stage:      common.StageStartVerification,
		OperatorID: 0, // 系统操作
		Comments:   "维修完成，进入验证阶段",
		Data: map[string]interface{}{
			"repair_ticket_id": repairTicketID,
			"success":          true, // 硬件更换成功才会进入验证阶段
			"repair_result":    repairResult,
			"solution":         solution,
			"repair_steps":     repairSteps,
			"status":           "waiting_verification",
		},
	}).Get(ctx, nil)

	if signalErr != nil {
		logger.Error("向故障单工作流发送验证信号失败", "error", signalErr)
	} else {
		logger.Info("成功向故障单工作流发送验证信号")
	}
}

// handleRepairWorkflowCompleteRepair 处理完成维修信号
func handleRepairWorkflowCompleteRepair(ctx workflow.Context, repairTicketID uint, signal common.WorkflowControlSignal, state *workflowState) error {
	logger := workflow.GetLogger(ctx)
	logger.Info("处理完成维修信号", "repairTicketID", repairTicketID)

	// 从信号数据中获取维修结果信息
	var repairResult RepairResult
	if signal.Data != nil {
		if result, ok := signal.Data["repair_result"].(string); ok && result != "" {
			repairResult.RepairResult = result
		}
		if solution, ok := signal.Data["solution"].(string); ok && solution != "" {
			repairResult.Solution = solution
		}
		if steps, ok := signal.Data["repair_steps"].(string); ok && steps != "" {
			repairResult.RepairSteps = steps
		}

		// 保存到工作流状态
		state.RepairResult = &repairResult
	}

	// 标记维修完成
	success := true
	conclusion := "维修完成，等待验证"
	if signal.Comments != "" {
		conclusion = signal.Comments
	}

	// 设置状态为等待验证
	newStatus := "waiting_verification"

	// 更新维修单状态
	err := workflow.ExecuteActivity(ctx, "UpdateRepairTicketStatusActivity", repairTicketID, newStatus, signal.OperatorID, conclusion, signal.OperatorName).Get(ctx, nil)
	if err != nil {
		logger.Error("更新维修单状态失败", "error", err)
		return err
	}

	// 执行完成维修活动
	err = workflow.ExecuteActivity(ctx, "CompleteRepairActivity", repairTicketID, success, conclusion).Get(ctx, nil)
	if err != nil {
		logger.Error("标记维修完成失败", "error", err)
		return err
	}

	// 更新工作流状态
	state.Status = newStatus
	state.Stage = "waiting_verification"

	logger.Info("维修完成处理完成",
		"status", state.Status,
		"repair_result", repairResult.RepairResult,
		"solution", repairResult.Solution)

	// 检查工作流版本，确保新版本能发送通知，旧版本也能正常运行
	v := workflow.GetVersion(ctx, "RepairWorkflowNotificationCompatibility", workflow.DefaultVersion, 1)

	// 发送飞书通知 - 维修单待验证通知
	if v == 1 {
		// 新版本代码
		logger.Info("使用新版本飞书通知", "version", v)
		notifyErr := workflow.ExecuteActivity(ctx, "SendRepairTicketWaitingVerificationNotificationActivity", repairTicketID).Get(ctx, nil)
		if notifyErr != nil {
			logger.Warn("发送维修单待验证通知失败",
				"error", notifyErr,
				"repairTicketID", repairTicketID)
			// 通知失败不影响主工作流
		} else {
			logger.Info("成功发送维修单待验证通知",
				"repairTicketID", repairTicketID)
		}
	} else {
		// 旧版本代码 - 模拟之前行为的占位符
		logger.Info("使用旧版本工作流逻辑，跳过飞书通知", "version", v)
		// 旧版本可能有其他逻辑，也可能什么都不做
		// 为了保持兼容性，我们这里不做任何实际操作
	}

	return nil
}

// handleRepairWorkflowVerify 处理验证信号
func handleRepairWorkflowVerify(ctx workflow.Context, repairTicketID uint, signal common.WorkflowControlSignal, state *workflowState) error {
	logger := workflow.GetLogger(ctx)
	// 记录完整的信号信息，便于问题诊断
	logger.Info("收到验证信号，原始数据",
		"repairTicketID", repairTicketID,
		"signalStage", signal.Stage,
		"signalComments", signal.Comments,
		"signalData", signal.Data,
		"currentStatus", state.Status)

	// 从信号数据中获取验证结果
	success := true
	comments := "验证通过，维修成功"
	var targetStatus string
	var signalSuccess bool
	isSecondVerification := false
	var verificationResult string

	if signal.Data != nil {
		// 检查是否是第二次验证
		if second, ok := signal.Data["second_verification"].(bool); ok && second {
			isSecondVerification = true
			logger.Info("检测到第二次验证信号", "repairTicketID", repairTicketID)
		}

		// 记录关键字段原始值，方便排查问题
		if successValue, ok := signal.Data["success"].(bool); ok {
			signalSuccess = successValue
			success = successValue
			logger.Info("信号中的success字段", "success", signalSuccess)
		}

		// 记录status原始值
		if status, ok := signal.Data["status"].(string); ok && status != "" {
			logger.Info("信号中的status字段", "status", status)
			targetStatus = status
		}

		// 尝试从target_status获取更明确的目标状态
		if target, ok := signal.Data["target_status"].(string); ok && target != "" {
			targetStatus = target
			logger.Info("使用信号指定的target_status", "targetStatus", targetStatus)
		}

		if signalComments, ok := signal.Data["comments"].(string); ok && signalComments != "" {
			comments = signalComments
		}

		// 获取验证结果字段
		if vr, ok := signal.Data["verification_result"].(string); ok && vr != "" {
			verificationResult = vr
			logger.Info("从信号中获取验证结果", "verificationResult", verificationResult)
		}
	}

	// 如果没有明确的验证结果，根据success设置
	if verificationResult == "" {
		if success {
			verificationResult = "passed"
		} else {
			verificationResult = "failed"
		}
	}

	// 处理第二次验证 - 强制设置completed标志
	if isSecondVerification && success {
		logger.Info("处理第二次验证信号，且验证通过，强制设置completed=true",
			"repairTicketID", repairTicketID)

		newStatus := "completed"

		// 更新维修单状态和验证结果
		fields := map[string]interface{}{
			"status":              newStatus,
			"verification_result": verificationResult,
		}

		// 更新维修单状态
		err := workflow.ExecuteActivity(ctx, "UpdateRepairTicketFields", repairTicketID, fields).Get(ctx, nil)
		if err != nil {
			logger.Error("更新维修单状态和验证结果失败", "error", err)
			return err
		}

		// 记录验证结果
		state.VerificationSuccess = true
		state.Status = newStatus
		state.Completed = true

		logger.Info("第二次验证通过，工作流将结束",
			"status", newStatus,
			"completed", state.Completed,
			"verification_result", verificationResult)

		return nil
	}

	// 修正矛盾的信息
	if !success && (targetStatus == "verification_passed" || targetStatus == "completed") {
		logger.Warn("检测到矛盾的信号数据：success为false但status表示成功",
			"success", success,
			"targetStatus", targetStatus)
		// 强制修正为一致的状态
		targetStatus = "in_progress"
		verificationResult = "failed"
	}

	// 强制处理验证通过的情况，确保能正确结束工作流
	if success || targetStatus == "completed" {
		// 验证通过，进入完成状态
		newStatus := "completed"
		comments = "验证通过，维修成功：" + comments

		// 更新维修单状态和验证结果
		fields := map[string]interface{}{
			"status":                   newStatus,
			"verification_result":      verificationResult,
			"verification_description": comments, // 添加验证描述字段
		}

		// 更新维修单状态
		err := workflow.ExecuteActivity(ctx, "UpdateRepairTicketFields", repairTicketID, fields).Get(ctx, nil)
		if err != nil {
			logger.Error("更新维修单状态和验证结果失败", "error", err)
			return err
		}

		// 记录验证结果
		state.VerificationSuccess = true
		state.Status = newStatus
		state.Completed = true

		logger.Info("验证通过，工作流将结束",
			"status", newStatus,
			"completed", state.Completed,
			"verification_result", verificationResult,
			"comments", comments) // 添加日志记录

		return nil
	}

	// 验证失败的情况
	logger.Info("验证失败，需要继续维修", "repairTicketID", repairTicketID)

	// 根据验证结果设置最终状态
	newStatus := "in_progress"
	comments = "验证未通过，继续维修：" + comments

	// 输出详细日志
	logger.Info("验证失败处理结果",
		"repairTicketID", repairTicketID,
		"原始success", signalSuccess,
		"newStatus", newStatus,
		"comments", comments,
		"verification_result", verificationResult)

	// 获取维修单信息，用于发送通知
	var ticketInfo *model.RepairTicket
	err := workflow.ExecuteActivity(ctx, "GetRepairTicketActivity", repairTicketID).Get(ctx, &ticketInfo)
	if err != nil {
		logger.Error("获取维修单信息失败", "error", err)
		// 不阻塞工作流
	} else if ticketInfo != nil {
		// 发送验证不通过通知
		// 获取故障单信息（如果有）
		var faultTicketNo = ""
		var deviceSN = "未知设备"
		var faultTicketID string

		if ticketInfo.FaultTicketID > 0 {
			faultTicketID = fmt.Sprintf("%d", ticketInfo.FaultTicketID)

			// 获取故障单信息，以获取准确的设备SN和工单号
			var faultTicket *model.FaultTicket
			faultErr := workflow.ExecuteActivity(ctx, "GetFaultTicketByIDActivity", ticketInfo.FaultTicketID).Get(ctx, &faultTicket)
			if faultErr == nil && faultTicket != nil {
				deviceSN = faultTicket.DeviceSN
				faultTicketNo = faultTicket.TicketNo
				logger.Info("成功获取故障单信息",
					"deviceSN", deviceSN,
					"faultTicketNo", faultTicketNo)
			}

			// 发送飞书通知
			notifyErr := workflow.ExecuteActivity(ctx, "SendVerificationFailedNotification",
				faultTicketID,
				fmt.Sprintf("%d", repairTicketID),
				ticketInfo.TicketNo,
				deviceSN,
				faultTicketNo,
				ticketInfo.AssignedEngineerName,
				signal.Comments).Get(ctx, nil)

			if notifyErr != nil {
				logger.Warn("发送验证不通过通知失败",
					"error", notifyErr,
					"repairTicketID", repairTicketID)
				// 通知失败不阻塞工作流
			} else {
				logger.Info("成功发送验证不通过通知",
					"repairTicketID", repairTicketID,
					"engineerName", ticketInfo.AssignedEngineerName)
			}
		} else {
			// 如果没有关联故障单，跳过发送通知
			logger.Info("没有关联的故障单，跳过发送验证不通过通知",
				"repairTicketID", repairTicketID)
		}
	}

	// 更新维修单状态和验证结果
	fields := map[string]interface{}{
		"status":              newStatus,
		"verification_result": verificationResult,
	}

	// 更新维修单状态
	err = workflow.ExecuteActivity(ctx, "UpdateRepairTicketFields", repairTicketID, fields).Get(ctx, nil)
	if err != nil {
		logger.Error("更新维修单状态和验证结果失败", "error", err)
		return err
	}

	// 记录验证结果
	state.VerificationSuccess = false
	state.Status = newStatus
	state.Completed = false
	state.Stage = "in_progress"

	logger.Info("验证处理完成，维修单回到进行中状态，继续维修",
		"repairTicketID", repairTicketID,
		"status", state.Status,
		"completed", state.Completed,
		"verification_result", verificationResult)

	return nil
}

// handleRepairWorkflowAuthorize 处理维修单授权信号
func handleRepairWorkflowAuthorize(ctx workflow.Context, repairTicketID uint, signal common.WorkflowControlSignal, state *workflowState) error {
	logger := workflow.GetLogger(ctx)

	// 检查当前状态
	if state.Status != "waiting_authorization" {
		logger.Info("当前状态不是等待授权，忽略授权信号",
			"repairTicketID", repairTicketID,
			"currentStatus", state.Status)
		return nil
	}

	// 更新工作流内部状态
	state.Stage = "waiting_accept"
	state.Status = "waiting_accept"
	if signal.Data != nil {
		state.SignalData = signal.Data
	}

	// 准备操作人信息，确保有明确标识
	operatorName := signal.OperatorName
	operatorID := signal.OperatorID
	operatorComments := signal.Comments

	// 检查是否来自故障单的授权信号
	fromFaultTicket := false
	if signal.Data != nil {
		if val, ok := signal.Data["from_fault_ticket"].(bool); ok && val {
			fromFaultTicket = true
		}
	}

	// 如果来自故障单且名称为空或默认格式，添加特定标识
	if fromFaultTicket && (operatorName == "" || strings.HasPrefix(operatorName, "用户")) {
		operatorName = fmt.Sprintf("故障单授权人(ID:%d)", operatorID)

		// 如果信号数据中有故障单ID，添加到备注中增加追踪性
		if faultTicketID, ok := signal.Data["fault_ticket_id"].(float64); ok {
			operatorComments = fmt.Sprintf("故障单(ID:%d)客户已批准维修方案，自动授权维修单", uint(faultTicketID))
		}
	}

	// 更新数据库状态 - 确保传递完整的操作人信息
	err := workflow.ExecuteActivity(ctx, "UpdateRepairTicketStatusActivity",
		repairTicketID, "waiting_accept", operatorID, operatorComments, operatorName).Get(ctx, nil)
	if err != nil {
		logger.Error("UpdateRepairTicketStatusActivity failed",
			"error", err,
			"operatorID", operatorID,
			"operatorName", operatorName)
		return err
	}

	logger.Info("维修单授权完成，已转为等待接单状态",
		"repairTicketID", repairTicketID,
		"operatorName", operatorName)

	// 通知已移至故障单工作流中发送，这里不再发送
	// 故障单批准后会直接发送维修单待接单通知

	return nil
}
