package workflow

import (
	"backend/internal/modules/ticket/common"
	"backend/internal/modules/ticket/model"
	"errors"
	"fmt"

	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

// EntryTicketWorkflow 入室单工作流
func EntryTicketWorkflow(ctx workflow.Context, input EntryTicketWorkflowInput) error {
	// 如果工作流已完成，直接返回
	if input.Completed {
		logger := workflow.GetLogger(ctx)
		logger.Info("工作流已完成，直接返回", "ticketID", input.TicketID)
		return nil
	}

	// 设置工作流选项
	ao := workflow.ActivityOptions{
		StartToCloseTimeout: common.ActivityStartToCloseTimeout,
		HeartbeatTimeout:    common.ActivityHeartbeatTimeout,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:        common.InitialInterval,
			BackoffCoefficient:     common.BackoffCoefficient,
			MaximumInterval:        common.MaximumInterval,
			MaximumAttempts:        common.MaxAttempts,
			NonRetryableErrorTypes: []string{"InvalidInputError"},
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)

	logger := workflow.GetLogger(ctx)
	logger.Info("EntryTicket workflow started", "ticketID", input.TicketID)

	// 创建工作流状态
	workflowState := struct {
		TicketID              uint
		CurrentStatus         string
		CurrentStage          string
		RepairType            string
		RepairTicketID        uint
		NeedsCustomerApproval bool
		DiagnosisResult       string
		ChildWorkflowRunning  bool
		ChildWorkflowSuccess  *bool
	}{
		TicketID:      input.TicketID,
		CurrentStatus: input.Status, // 使用输入中的状态
		CurrentStage:  "",           // 初始没有阶段
	}

	// 创建信号通道 - 这将是我们接收所有信号的主要通道
	signalChan := workflow.GetSignalChannel(ctx, common.WorkflowControlSignalName)

	// 根据初始状态设置当前阶段
	switch workflowState.CurrentStatus {
	case common.StatusWaitingApproval:
		workflowState.CurrentStage = common.StageCustomerApproval
	case common.StatusWaitingSecondApproval:
		workflowState.CurrentStage = common.StageSecondApproval
	}

	// 工作流主循环 - 完全基于信号驱动
	for {
		// 等待下一个信号
		logger.Info("等待工作流控制信号",
			"ticketID", input.TicketID,
			"currentStage", workflowState.CurrentStage,
			"currentStatus", workflowState.CurrentStatus)

		// 无限期等待信号
		var signal common.WorkflowControlSignal
		signalChan.Receive(ctx, &signal)

		logger.Info("收到工作流控制信号",
			"ticketID", input.TicketID,
			"stage", signal.Stage,
			"operator", signal.OperatorName)

		// 处理信号
		switch signal.Stage {
		case common.StageCustomerApproval:
			// 处理客户审批信号
			err := handleEntryApproval(ctx, input.TicketID, input.PersonType, signal, &workflowState)
			if err != nil {
				logger.Error("处理客户审批失败", "error", err)
				continue
			}

		case common.StageSecondApproval:
			err := handleSecondApproval(ctx, input.TicketID, signal, &workflowState)
			if err != nil {
				logger.Error("处理二次审批失败", "error", err)
				continue
			}

		case common.StageCompleteTicket:
			// 处理完成工单信号
			err := handleCompleteEntryTicket(ctx, input.TicketID, signal, &workflowState)
			if err != nil {
				logger.Error("处理完成工单失败", "error", err)
				continue
			}
			// 工单已完成，结束工作流主循环
			logger.Info("工单已完成，结束工作流", "ticketID", input.TicketID)
			return nil

		default:
			logger.Error("未知的工作流阶段", "stage", signal.Stage)
		}

		// 检查工作流是否已完成，如果状态是completed或cancelled，则结束工作流
		if workflowState.CurrentStatus == common.StatusApproved ||
			workflowState.CurrentStatus == common.StatusRejected {
			logger.Info("工单状态已变更为已完成或已取消，结束工作流",
				"ticketID", input.TicketID,
				"status", workflowState.CurrentStatus)
			return nil
		}

	}

}

// handleEntryApproval 处理审批信号
func handleEntryApproval(ctx workflow.Context, ticketID uint, personType string, signal common.WorkflowControlSignal, state *struct {
	TicketID              uint
	CurrentStatus         string
	CurrentStage          string
	RepairType            string
	RepairTicketID        uint
	NeedsCustomerApproval bool
	DiagnosisResult       string
	ChildWorkflowRunning  bool
	ChildWorkflowSuccess  *bool
}) error {
	logger := workflow.GetLogger(ctx)

	// 解析审批结果
	var approvalResult common.CustomerApprovalResult
	err := workflow.ExecuteActivity(ctx, "GetApprovalActivity", ticketID).Get(ctx, &approvalResult)
	if err != nil {
		logger.Error("获取客户审批结果失败", "error", err)
		return fmt.Errorf("获取客户审批结果失败: %w", err)
	}

	// 检查审批状态
	switch approvalResult.Status {
	case "approved":
		// 获取入室单信息
		var entryTicket *model.EntryTicket
		if err := workflow.ExecuteActivity(ctx, "GetEntryTicketByIDActivity", ticketID).Get(ctx, &entryTicket); err != nil {
			logger.Error("获取入室单信息失败", "error", err)
			// 获取入室单失败不应阻塞工作流主流程
		}

		if *entryTicket.IsOutSource {
			state.CurrentStatus = common.StatusWaitingSecondApproval
			state.CurrentStage = common.StageSecondApproval
		} else {

			// 审批通过 插入入室人员信息  状态为完成completed
			if err := workflow.ExecuteActivity(ctx, "SaveEntryPersonActivity", ticketID).Get(ctx, nil); err != nil {
				logger.Error("保存入室人员信息失败", "error", err)
				return fmt.Errorf("保存入室人员信息失败: %w", err)
			}

			state.CurrentStage = common.StageCompleteTicket
			state.CurrentStatus = common.StatusApproved
		}

		// 日志记录此状态变更
		//logger.Info("客户已批准，工单状态变更为等待手动启动维修",
		//	"ticketID", ticketID,
		//	"newStatus", common.StatusApprovedWaiting)

		// 更新数据库状态
		err = workflow.ExecuteActivity(ctx, "UpdateEntryTicketStatusActivity",
			ticketID, state.CurrentStatus, signal.OperatorID, signal.OperatorName).Get(ctx, nil)
		if err != nil {
			return fmt.Errorf("更新工单状态失败: %w", err)
		}

		return nil
	case "rejected":
		// 审批拒绝
		logger.Info("审批未通过", "ticketID", ticketID)
		state.CurrentStage = common.StageCompleteTicket
		state.CurrentStatus = common.StatusRejected

		// 更新数据库状态
		err = workflow.ExecuteActivity(ctx, "UpdateEntryTicketStatusActivity",
			ticketID, state.CurrentStatus, signal.OperatorID, signal.OperatorName).Get(ctx, nil)
		if err != nil {
			return fmt.Errorf("更新工单状态失败: %w", err)
		}

		return nil
	default:
		// 审批状态未知，保持当前状态
		logger.Warn("未知的审批状态", "status", approvalResult.Status)
		return fmt.Errorf("未知的审批状态: %s", approvalResult.Status)
	}
}

// handleSecondApproval 处理二次审批信号
func handleSecondApproval(ctx workflow.Context, ticketID uint, signal common.WorkflowControlSignal, state *struct {
	TicketID              uint
	CurrentStatus         string
	CurrentStage          string
	RepairType            string
	RepairTicketID        uint
	NeedsCustomerApproval bool
	DiagnosisResult       string
	ChildWorkflowRunning  bool
	ChildWorkflowSuccess  *bool
}) error {
	logger := workflow.GetLogger(ctx)

	// 解析客户审批数据
	var approvalResult EntryApprovalResult
	if err := workflow.ExecuteActivity(ctx, "GetApprovalActivity", ticketID).Get(ctx, &approvalResult); err != nil {
		return fmt.Errorf("获取客户审批失败: %w", err)
	}

	if approvalResult.ApproverCount != 2 {
		return errors.New("未进行二次审批")
	}

	switch approvalResult.Status {
	case "approved":
		// 获取入室单信息
		var entryTicket *model.EntryTicket
		if err := workflow.ExecuteActivity(ctx, "GetEntryTicketByIDActivity", ticketID).Get(ctx, &entryTicket); err != nil {
			logger.Error("获取入室单信息失败", "error", err)
			// 获取入室单失败不应阻塞工作流主流程
		}

		// 审批通过 插入入室人员信息  状态为完成completed
		if err := workflow.ExecuteActivity(ctx, "SaveEntryPersonActivity", ticketID).Get(ctx, nil); err != nil {
			logger.Error("保存入室人员信息失败", "error", err)
			return fmt.Errorf("保存入室人员信息失败: %w", err)
		}

		state.CurrentStage = common.StageCompleteTicket
		state.CurrentStatus = common.StatusApproved

		// 日志记录此状态变更
		//logger.Info("客户已批准，工单状态变更为等待手动启动维修",
		//	"ticketID", ticketID,
		//	"newStatus", common.StatusApprovedWaiting)

		// 更新数据库状态
		err := workflow.ExecuteActivity(ctx, "UpdateEntryTicketStatusActivity",
			ticketID, state.CurrentStatus, signal.OperatorID, signal.OperatorName).Get(ctx, nil)
		if err != nil {
			return fmt.Errorf("更新工单状态失败: %w", err)
		}

		return nil
	case "rejected":
		// 审批拒绝
		logger.Info("审批未通过", "ticketID", ticketID)
		state.CurrentStage = common.StageCompleteTicket
		state.CurrentStatus = common.StatusRejected

		// 更新数据库状态
		err := workflow.ExecuteActivity(ctx, "UpdateEntryTicketStatusActivity",
			ticketID, state.CurrentStatus, signal.OperatorID, signal.OperatorName).Get(ctx, nil)
		if err != nil {
			return fmt.Errorf("更新工单状态失败: %w", err)
		}

		return nil
	default:
		// 审批状态未知，保持当前状态
		logger.Warn("未知的审批状态", "status", approvalResult.Status)
		return fmt.Errorf("未知的审批状态: %s", approvalResult.Status)
	}
}

// handleCompleteEntryTicket 处理完成工单信号
func handleCompleteEntryTicket(ctx workflow.Context, ticketID uint, signal common.WorkflowControlSignal, state *struct {
	TicketID              uint
	CurrentStatus         string
	CurrentStage          string
	RepairType            string
	RepairTicketID        uint
	NeedsCustomerApproval bool
	DiagnosisResult       string
	ChildWorkflowRunning  bool
	ChildWorkflowSuccess  *bool
}) error {
	// 先更新工作流内部状态
	state.CurrentStatus = common.StatusCompleted

	// 再更新数据库工单状态
	err := workflow.ExecuteActivity(ctx, "UpdateEntryTicketStatusActivity",
		ticketID, state.CurrentStatus, signal.OperatorID, signal.OperatorName).Get(ctx, nil)
	if err != nil {
		return fmt.Errorf("更新工单状态失败: %w", err)
	}

	return nil
}
