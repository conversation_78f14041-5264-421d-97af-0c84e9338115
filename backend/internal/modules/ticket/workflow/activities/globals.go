package activities

import (
	userModel "backend/internal/modules/user/model"
	"context"
)

// 增加函数来检查userService是否被正确初始化

// IsUserServiceInitialized 检查用户服务是否已初始化
func IsUserServiceInitialized() bool {
	if userService == nil {
		return false
	}

	// 使用反射检查userService具体实现是否为nil
	defer func() {
		r := recover() // 捕获任何可能的panic
		_ = r          // 显式检查返回值，避免errcheck警告
	}()

	// 尝试获取一个测试用户名，如果成功则认为服务已初始化
	_, err := userService.GetUserName(context.Background(), 1)
	return err == nil
}

// safeUserService 用户服务的安全实现
//
//nolint:unused
type safeUserService struct{}

// GetUserInfoByID 安全的获取用户信息方法
//
//nolint:unused
func (s *safeUserService) GetUserInfoByID(ctx context.Context, userID uint) (*userModel.UserInfo, error) {
	return &userModel.UserInfo{
		ID: userID,

		RealName: "未知用户",
	}, nil
}

// GetUserName 安全的获取用户名方法
//
//nolint:unused
func (s *safeUserService) GetUserName(ctx context.Context, userID uint) (string, error) {
	return "", nil
}
