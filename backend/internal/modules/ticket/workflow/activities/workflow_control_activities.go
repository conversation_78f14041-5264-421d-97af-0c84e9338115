package activities

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"

	"go.uber.org/zap"
)

// 工作流阶段常量 - 用于手动触发
const (
	StageAcceptTicket         = "accept_ticket"         // 接单
	StageRepairSelection      = "repair_selection"      // 选择维修方式
	StageCustomerApproval     = "customer_approval"     // 客户审批
	StageStartRepair          = "start_repair"          // 开始维修
	StageCompleteRepair       = "complete_repair"       // 完成维修
	StageStartVerification    = "start_verification"    // 开始验证
	StageCompleteVerification = "complete_verification" // 完成验证
	StageSummary              = "summary"               // 总结
	StageCompleteTicket       = "complete_ticket"       // 完成工单
)

// 手动触发请求的存储 - 支持不同的状态变更点
var (
	manualActionRequests = make(map[string]chan bool)
	manualActionRepo     repository.FaultTicketRepository
	activityLogger       *zap.Logger
	requestsMutex        sync.Mutex // 保护map的并发访问
)

// InitManualActionActivities 初始化手动触发活动
func InitManualActionActivities(repo repository.FaultTicketRepository, log *zap.Logger) {
	manualActionRepo = repo
	activityLogger = log
	activityLogger.Info("手动触发活动初始化完成")

	// 启动恢复机制，检测并恢复状态不一致的工单
	go recoverInconsistentTickets()
}

// 创建触发键
func createTriggerKey(ticketID uint, stage string) string {
	return fmt.Sprintf("%d_%s", ticketID, stage)
}

// WaitForManualActionActivity 等待手动API触发的活动
func WaitForManualActionActivity(ctx context.Context, ticketID uint, stage string) error {
	if activityLogger == nil {
		return errors.New("活动日志记录器未初始化")
	}

	// 验证输入参数
	if ticketID == 0 {
		return NewInvalidInputError("工单ID不能为空")
	}

	if stage == "" {
		return NewInvalidInputError("工作流阶段不能为空")
	}

	// 验证工作流阶段是否合法
	validStages := []string{
		StageAcceptTicket, StageRepairSelection, StageCustomerApproval,
		StageStartRepair, StageCompleteRepair, StageStartVerification,
		StageCompleteVerification, StageSummary, StageCompleteTicket,
	}

	isValidStage := false
	for _, validStage := range validStages {
		if stage == validStage {
			isValidStage = true
			break
		}
	}

	if !isValidStage {
		return NewInvalidInputError(fmt.Sprintf("无效的工作流阶段: %s", stage))
	}

	activityLogger.Info("等待手动触发下一步",
		zap.Uint("ticketID", ticketID),
		zap.String("stage", stage))

	// 创建唯一的触发键
	triggerKey := createTriggerKey(ticketID, stage)

	// 创建一个通道来接收触发信号
	signalChan := make(chan bool, 1)

	// 安全地添加到map
	requestsMutex.Lock()
	manualActionRequests[triggerKey] = signalChan
	requestsMutex.Unlock()

	// 记录当前状态到数据库
	if manualActionRepo != nil {
		ticket, err := manualActionRepo.GetByID(ctx, ticketID)
		if err != nil {
			activityLogger.Error("获取报障单失败", zap.Error(err), zap.Uint("ticketID", ticketID))
			return err
		}

		// 添加一个备注说明正在等待手动触发
		remark := fmt.Sprintf("[%s] 工作流阶段[%s]已暂停，等待手动触发下一步操作",
			time.Now().Format("2006-01-02 15:04:05"), stage)

		if ticket.Remarks != "" {
			ticket.Remarks = ticket.Remarks + "\n" + remark
		} else {
			ticket.Remarks = remark
		}

		// 更新等待手动触发的标记
		ticket.WaitingManualTrigger = true

		// 验证阶段名称，确保不含非法字符
		if strings.Contains(stage, "Time:") || strings.Contains(stage, ":") {
			activityLogger.Error("阶段名称包含非法字符",
				zap.Uint("ticketID", ticketID),
				zap.String("原始值", stage))

			// 尝试清理阶段名称
			cleanStage := strings.Split(stage, "Time:")[0]
			cleanStage = strings.Split(cleanStage, ":")[0]

			activityLogger.Info("已清理阶段名称",
				zap.Uint("ticketID", ticketID),
				zap.String("清理后", cleanStage))

			stage = cleanStage
		}

		// 确保只设置阶段名称，避免时间值混入
		ticket.CurrentWaitingStage = stage

		// 记录设置的具体值，便于调试
		activityLogger.Info("设置CurrentWaitingStage字段",
			zap.Uint("ticketID", ticketID),
			zap.String("值", ticket.CurrentWaitingStage))

		// 单独设置等待时间字段
		now := time.Now()
		if ticket.LastWaitingTime != nil {
			// 复制指针，避免操作同一个内存地址
			waitTime := now
			ticket.LastWaitingTime = &waitTime
		} else {
			waitTime := now
			ticket.LastWaitingTime = &waitTime
		}

		if err := manualActionRepo.Update(ctx, ticket); err != nil {
			activityLogger.Error("更新报障单失败", zap.Error(err), zap.Uint("ticketID", ticketID))
			// 继续执行，不返回错误
		}
	}

	// 等待信号或上下文取消
	select {
	case <-signalChan:
		activityLogger.Info("收到手动触发信号",
			zap.Uint("ticketID", ticketID),
			zap.String("stage", stage))

		// 安全地从map移除
		requestsMutex.Lock()
		delete(manualActionRequests, triggerKey)
		requestsMutex.Unlock()

		// 更新数据库，清除等待标记
		if manualActionRepo != nil {
			ticket, err := manualActionRepo.GetByID(ctx, ticketID)
			if err == nil {
				ticket.WaitingManualTrigger = false
				ticket.CurrentWaitingStage = ""
				if err := manualActionRepo.Update(ctx, ticket); err != nil {
					activityLogger.Warn("清除等待标记失败", zap.Error(err), zap.Uint("ticketID", ticketID))
				}
			}
		}

		return nil
	case <-ctx.Done():
		activityLogger.Warn("等待手动触发被取消",
			zap.Uint("ticketID", ticketID),
			zap.String("stage", stage),
			zap.Error(ctx.Err()))

		// 安全地从map移除
		requestsMutex.Lock()
		delete(manualActionRequests, triggerKey)
		requestsMutex.Unlock()

		return ctx.Err()
	}
}

// TriggerManualAction 触发手动操作 - 由API调用
func TriggerManualAction(ctx context.Context, ticketID uint, stage string) error {
	if activityLogger == nil {
		return errors.New("活动日志记录器未初始化")
	}

	triggerKey := createTriggerKey(ticketID, stage)

	// 安全地访问map
	requestsMutex.Lock()
	signalChan, exists := manualActionRequests[triggerKey]
	requestsMutex.Unlock()

	if !exists {
		// 尝试从数据库查询工单状态
		if manualActionRepo != nil {
			ticket, err := manualActionRepo.GetByID(ctx, ticketID)
			if err == nil && ticket.WaitingManualTrigger {
				// 检查存储的等待阶段是否被损坏
				currentStage := ticket.CurrentWaitingStage

				// 尝试修复损坏的阶段名称
				if strings.Contains(currentStage, "Time:") || strings.Contains(currentStage, ":") {
					// 可能的格式: "repair_selectionTime:2025-03-25 14:05:08..."
					// 或: "reaitingTime:2025..."
					cleanedStage := ""

					// 记录详细的原始值，便于分析
					activityLogger.Warn("发现损坏的阶段名称",
						zap.Uint("ticketID", ticketID),
						zap.String("原始值详情", fmt.Sprintf("%#v", currentStage)))

					// 先尝试根据特定模式提取
					if strings.Contains(currentStage, "Time:") {
						parts := strings.Split(currentStage, "Time:")
						if len(parts) > 0 {
							potentialStage := parts[0]
							activityLogger.Info("从Time分隔中提取的潜在阶段",
								zap.String("potentialStage", potentialStage))

							// 处理特殊情况：如果是"reaitingTime"，很可能是"repair_selection"的错误形式
							if potentialStage == "reait" || potentialStage == "reai" {
								potentialStage = "repair_selection"
								activityLogger.Info("特殊修复 reait/reai -> repair_selection")
							}

							cleanedStage = potentialStage
						}
					}

					// 如果上面的方法未提取出有效的阶段名称，尝试使用已知的有效阶段名称匹配
					if cleanedStage == "" {
						// 尝试提取有效的阶段名称
						for _, validStage := range []string{
							"accept_ticket", "repair_selection", "customer_approval",
							"start_repair", "complete_repair", "start_verification",
							"complete_verification", "summary", "complete_ticket",
						} {
							if strings.HasPrefix(currentStage, validStage) {
								cleanedStage = validStage
								activityLogger.Info("通过前缀匹配找到有效阶段", zap.String("stage", validStage))
								break
							}
						}
					}

					// 如果没找到匹配的前缀，可能是严重损坏的值，尝试用传入的stage替代
					if cleanedStage == "" {
						if currentStage == "reaitingTime" && stage == "repair_selection" {
							// 常见的特定错误模式
							cleanedStage = "repair_selection"
							activityLogger.Info("应用特定错误模式修复",
								zap.String("from", "reaitingTime"),
								zap.String("to", "repair_selection"))
						} else {
							// 使用传入的阶段作为回退
							cleanedStage = stage
							activityLogger.Info("未找到匹配，使用传入的阶段作为回退", zap.String("stage", stage))
						}
					}

					activityLogger.Warn("检测到损坏的阶段名称，已尝试修复",
						zap.Uint("ticketID", ticketID),
						zap.String("原始值", currentStage),
						zap.String("修复后", cleanedStage))

					// 如果修复的阶段与传入的阶段匹配，或者stage为空，则继续处理
					if cleanedStage == stage || stage == "" {
						// 清除等待标记
						ticket.WaitingManualTrigger = false
						ticket.CurrentWaitingStage = ""

						// 添加备注
						remark := fmt.Sprintf("[%s] 工作流阶段[%s]已手动触发(修复损坏的阶段名称)",
							time.Now().Format("2006-01-02 15:04:05"), cleanedStage)

						if ticket.Remarks != "" {
							ticket.Remarks = ticket.Remarks + "\n" + remark
						} else {
							ticket.Remarks = remark
						}

						if err := manualActionRepo.Update(ctx, ticket); err != nil {
							activityLogger.Error("更新工单状态失败",
								zap.Error(err),
								zap.Uint("ticketID", ticketID))
							return fmt.Errorf("更新工单状态失败: %w", err)
						}

						activityLogger.Info("已成功修复并触发工作流",
							zap.Uint("ticketID", ticketID),
							zap.String("stage", cleanedStage))

						return nil
					}
				} else if currentStage == stage {
					// 工单在数据库中标记为等待触发，但内存中没有对应的通道
					// 这可能是因为Worker重启或其他原因导致的状态不一致
					// 在这种情况下，我们直接更新数据库状态
					activityLogger.Info("直接更新数据库状态",
						zap.Uint("ticketID", ticketID),
						zap.String("stage", stage))

					// 清除等待标记
					ticket.WaitingManualTrigger = false
					ticket.CurrentWaitingStage = ""

					// 添加备注
					remark := fmt.Sprintf("[%s] 工作流阶段[%s]已手动触发(直接更新)",
						time.Now().Format("2006-01-02 15:04:05"), stage)

					if ticket.Remarks != "" {
						ticket.Remarks = ticket.Remarks + "\n" + remark
					} else {
						ticket.Remarks = remark
					}

					if err := manualActionRepo.Update(ctx, ticket); err != nil {
						activityLogger.Error("更新工单状态失败",
							zap.Error(err),
							zap.Uint("ticketID", ticketID))
						return fmt.Errorf("更新工单状态失败: %w", err)
					}

					return nil
				}
			}
		}

		activityLogger.Warn("没有找到等待中的工作流",
			zap.Uint("ticketID", ticketID),
			zap.String("stage", stage))
		return fmt.Errorf("没有找到工单ID:%d,阶段:%s的等待中的工作流", ticketID, stage)
	}

	// 记录触发事件
	activityLogger.Info("触发工作流继续执行",
		zap.Uint("ticketID", ticketID),
		zap.String("stage", stage))

	// 发送信号
	select {
	case signalChan <- true:
		return nil
	default:
		activityLogger.Warn("信号通道已满或已关闭",
			zap.Uint("ticketID", ticketID),
			zap.String("stage", stage))
		return fmt.Errorf("信号通道已满或已关闭")
	}
}

// GetWaitingStages 获取所有等待中的工作流阶段
func GetWaitingStages(ticketID uint) []string {
	requestsMutex.Lock()
	defer requestsMutex.Unlock()

	prefix := fmt.Sprintf("%d_", ticketID)
	stages := []string{}

	// 从内存中获取等待阶段
	for key := range manualActionRequests {
		if len(key) > len(prefix) && key[:len(prefix)] == prefix {
			stages = append(stages, key[len(prefix):])
		}
	}

	// 如果内存中没有等待阶段，检查数据库
	if len(stages) == 0 && manualActionRepo != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		ticket, err := manualActionRepo.GetByID(ctx, ticketID)
		if err == nil && ticket != nil && ticket.WaitingManualTrigger && ticket.CurrentWaitingStage != "" {
			stages = append(stages, ticket.CurrentWaitingStage)
		}
	}

	return stages
}

// GetWaitingStagesActivity 获取所有等待中的工作流阶段 - 活动版本
func GetWaitingStagesActivity(ctx context.Context, ticketID uint) ([]string, error) {
	if activityLogger == nil {
		return nil, errors.New("活动日志记录器未初始化")
	}

	// 验证输入参数
	if ticketID == 0 {
		return nil, NewInvalidInputError("工单ID不能为空")
	}

	activityLogger.Info("获取等待中的工作流阶段", zap.Uint("ticketID", ticketID))

	return GetWaitingStages(ticketID), nil
}

// CancelWaitingActions 取消工单的所有等待中的手动触发
func CancelWaitingActions(ticketID uint) {
	requestsMutex.Lock()
	defer requestsMutex.Unlock()

	prefix := fmt.Sprintf("%d_", ticketID)

	for key, ch := range manualActionRequests {
		if len(key) > len(prefix) && key[:len(prefix)] == prefix {
			close(ch)
			delete(manualActionRequests, key)
		}
	}
}

// recoverInconsistentTickets 恢复状态不一致的工单
func recoverInconsistentTickets() {
	if manualActionRepo == nil || activityLogger == nil {
		return
	}

	activityLogger.Info("开始检测状态不一致的工单...")

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 查询所有等待手动触发的工单
	tickets, err := findWaitingManualTriggerTickets(ctx)
	if err != nil {
		activityLogger.Error("查询等待手动触发的工单失败", zap.Error(err))
		return
	}

	activityLogger.Info("找到需要恢复的工单", zap.Int("数量", len(tickets)))

	// 恢复每个工单的等待状态
	for _, ticket := range tickets {
		if ticket.WaitingManualTrigger && ticket.CurrentWaitingStage != "" {
			// 创建触发键
			triggerKey := createTriggerKey(ticket.ID, ticket.CurrentWaitingStage)

			// 检查是否已存在同样的请求
			requestsMutex.Lock()
			_, exists := manualActionRequests[triggerKey]

			// 如果不存在，则创建一个新的通道
			if !exists {
				activityLogger.Info("恢复工单等待状态",
					zap.Uint("ticketID", ticket.ID),
					zap.String("stage", ticket.CurrentWaitingStage))

				signalChan := make(chan bool, 1)
				manualActionRequests[triggerKey] = signalChan

				// 为每个恢复的工单创建一个轻量级goroutine来等待手动触发
				go func(ticketID uint, stage string, ch chan bool) {
					// 记录恢复日志
					activityLogger.Info("工单已恢复等待手动触发状态",
						zap.Uint("ticketID", ticketID),
						zap.String("stage", stage))

					// 简单等待信号
					<-ch

					// 收到信号后清理
					requestsMutex.Lock()
					delete(manualActionRequests, createTriggerKey(ticketID, stage))
					requestsMutex.Unlock()

					// 更新数据库状态
					cleanupTicketAfterTrigger(ticketID, stage)
				}(ticket.ID, ticket.CurrentWaitingStage, signalChan)
			}
			requestsMutex.Unlock()
		}
	}
}

// findWaitingManualTriggerTickets 查询所有等待手动触发的工单
func findWaitingManualTriggerTickets(ctx context.Context) ([]struct {
	ID                   uint
	WaitingManualTrigger bool
	CurrentWaitingStage  string
}, error) {
	if manualActionRepo == nil {
		return nil, errors.New("仓库未初始化")
	}

	// 这里需要根据实际的repository接口来调整
	// 假设repository有一个方法可以查询所有等待手动触发的工单
	result := []struct {
		ID                   uint
		WaitingManualTrigger bool
		CurrentWaitingStage  string
	}{}

	// 使用原生SQL查询
	// 通过类型断言获取底层的db对象，但我们无法直接访问内部结构体的字段
	// 因此需要修改查询方式
	// 直接使用FindWaitingManualTickets方法查询
	waitingTickets, err := findWaitingTicketsWithFields(ctx)
	if err != nil {
		return nil, err
	}

	// 转换结果格式
	for _, ticket := range waitingTickets {
		result = append(result, struct {
			ID                   uint
			WaitingManualTrigger bool
			CurrentWaitingStage  string
		}{
			ID:                   ticket.ID,
			WaitingManualTrigger: ticket.WaitingManualTrigger,
			CurrentWaitingStage:  ticket.CurrentWaitingStage,
		})
	}

	return result, nil
}

// findWaitingTicketsWithFields 通过接口方法查询等待手动触发的工单
func findWaitingTicketsWithFields(ctx context.Context) ([]*model.FaultTicket, error) {
	// 通过List方法查询，使用status为空，但附加额外条件
	// 实际上我们应该扩展Repository接口添加专门的方法，但为了简化这里用List方法
	tickets, _, err := manualActionRepo.List(ctx, 1, 100, map[string]interface{}{})
	if err != nil {
		return nil, err
	}

	// 过滤出满足条件的工单
	var result []*model.FaultTicket
	for _, ticket := range tickets {
		if ticket.WaitingManualTrigger && ticket.CurrentWaitingStage != "" {
			result = append(result, ticket)
		}
	}

	return result, nil
}

// cleanupTicketAfterTrigger 清理工单的等待状态
func cleanupTicketAfterTrigger(ticketID uint, stage string) {
	if manualActionRepo == nil || activityLogger == nil {
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 获取工单
	ticket, err := manualActionRepo.GetByID(ctx, ticketID)
	if err != nil {
		activityLogger.Error("获取工单失败", zap.Error(err), zap.Uint("ticketID", ticketID))
		return
	}

	// 清除等待标记
	ticket.WaitingManualTrigger = false
	ticket.CurrentWaitingStage = ""

	// 添加备注
	remark := fmt.Sprintf("[%s] 系统恢复: 工作流阶段[%s]被手动触发",
		time.Now().Format("2006-01-02 15:04:05"), stage)

	if ticket.Remarks != "" {
		ticket.Remarks = ticket.Remarks + "\n" + remark
	} else {
		ticket.Remarks = remark
	}

	// 更新工单
	if err := manualActionRepo.Update(ctx, ticket); err != nil {
		activityLogger.Error("清除工单等待标记失败", zap.Error(err), zap.Uint("ticketID", ticketID))
	} else {
		activityLogger.Info("工单等待标记已清除", zap.Uint("ticketID", ticketID), zap.String("stage", stage))
	}
}

// ActivityInterface 活动接口，供外部调用
type ActivityInterface interface {
	TriggerManualAction(ctx context.Context, ticketID uint, stage string) error
	GetWaitingStages(ticketID uint) []string
}

// activityInterfaceImpl 活动接口实现
type activityInterfaceImpl struct{}

// TriggerManualAction 触发手动操作
func (a *activityInterfaceImpl) TriggerManualAction(ctx context.Context, ticketID uint, stage string) error {
	return TriggerManualAction(ctx, ticketID, stage)
}

// GetWaitingStages 实现ActivityInterface接口
func (a *activityInterfaceImpl) GetWaitingStages(ticketID uint) []string {
	return GetWaitingStages(ticketID)
}

// GetActivityInterface 返回工作流活动接口实现
func GetActivityInterface() ActivityInterface {
	return &activityInterfaceImpl{}
}
