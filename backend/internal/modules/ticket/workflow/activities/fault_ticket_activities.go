package activities

import (
	"backend/internal/infrastructure/database"
	"backend/internal/modules/ticket/common"
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	"backend/internal/modules/ticket/repository/mysql"
	"backend/internal/modules/ticket/service"
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	cmdbAssetRepo "backend/internal/modules/cmdb/repository/asset"

	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
)

// 错误定义
var (
	ErrActivityDependenciesNotInitialized = errors.New("activity dependencies not initialized")
	ErrOperationFailed                    = errors.New("操作失败")
)

// 全局依赖
var (
	faultTicketRepo      repository.FaultTicketRepository
	repairTicketRepo     repository.RepairTicketRepository
	repairSelectionRepo  repository.RepairSelectionRepository
	customerApprovalRepo repository.CustomerApprovalRepository
	verificationRepo     repository.VerificationRepository
	faultTicketService   service.FaultTicketService
	repairTicketService  service.RepairTicketService
	userService          service.UserService
	logger               *zap.Logger
	deviceRepo           cmdbAssetRepo.DeviceRepository
	resourceRepo         cmdbAssetRepo.ResourceRepository
	statusChangeRepo     cmdbAssetRepo.StatusChangeRepository
)

// 添加自定义上下文键类型
type contextKey string

// 定义源信息键
const sourceContextKey contextKey = "source"

// InitActivities 初始化活动依赖
func InitActivities(
	ftRepo repository.FaultTicketRepository,
	ftSvc service.FaultTicketService,
	rtRepo repository.RepairTicketRepository,
	rsRepo repository.RepairSelectionRepository,
	caRepo repository.CustomerApprovalRepository,
	vRepo repository.VerificationRepository,
	rtSvc service.RepairTicketService,
	userSvc service.UserService,
	log *zap.Logger,
) {
	if log == nil {
		panic("logger cannot be nil when initializing activities")
	}

	faultTicketRepo = ftRepo
	faultTicketService = ftSvc
	repairTicketRepo = rtRepo
	repairSelectionRepo = rsRepo
	customerApprovalRepo = caRepo
	verificationRepo = vRepo
	repairTicketService = rtSvc
	userService = userSvc
	logger = log

	// 初始化设备仓库和资源仓库依赖
	db := database.GetDB()
	if db != nil {
		deviceRepo = cmdbAssetRepo.NewDeviceRepository(db)
		resourceRepo = cmdbAssetRepo.NewResourceRepository(db)
		statusChangeRepo = cmdbAssetRepo.NewStatusChangeRepository(db)

		// 初始化冷迁移记录仓库
		cmRepo := mysql.NewColdMigrationRepository(db)
		logger.Info("冷迁移记录仓库依赖初始化完成")

		// 初始化设备状态变更活动
		InitDeviceStatusActivities(deviceRepo, resourceRepo, statusChangeRepo, ftRepo, cmRepo)

		logger.Info("设备、资源和状态变更仓库依赖初始化完成")
	} else {
		logger.Warn("无法初始化CMDB仓库依赖，数据库连接为空")
	}

	logger.Info("故障工单活动初始化完成")

	// 初始化手动触发活动
	InitManualActionActivities(ftRepo, log)
}

// AcknowledgeFaultActivity 确认报障单活动
func AcknowledgeFaultActivity(ctx context.Context, ticketID uint) error {
	logger.Info("执行确认报障单活动", zap.Uint("ticketID", ticketID))

	// 检查活动依赖是否正确初始化
	if faultTicketRepo == nil || faultTicketService == nil {
		err := errors.New("活动依赖未正确初始化")
		logger.Error("活动依赖未初始化", zap.Error(err))
		return err
	}

	// 获取当前报障单
	ticket, err := faultTicketRepo.GetByID(ctx, ticketID)
	if err != nil {
		logger.Error("获取报障单失败", zap.Error(err))
		return err
	}

	// 检查是否为高频故障设备
	if ticket.DeviceSN != "" {
		// 检查最近7天内的故障数
		faults7Days, err := faultTicketRepo.CountRecentFaultsByDeviceSN(ctx, ticket.DeviceSN, 7)
		if err != nil {
			logger.Warn("检查设备最近7天故障记录失败",
				zap.Error(err),
				zap.String("deviceSN", ticket.DeviceSN))
		} else if faults7Days >= 2 {
			// 更新IsFrequentFault标记
			fields := map[string]interface{}{
				"is_frequent_fault": true,
			}
			if err := faultTicketRepo.UpdateFields(ctx, ticketID, fields); err != nil {
				logger.Warn("更新高频故障标记失败",
					zap.Error(err),
					zap.Uint("ticketID", ticketID))
			} else {
				logger.Info("设备为高频故障(最近7天内>=2次)",
					zap.String("deviceSN", ticket.DeviceSN),
					zap.Int64("故障次数", faults7Days))
			}
		} else {
			// 检查最近30天内的故障数
			faults30Days, err := faultTicketRepo.CountRecentFaultsByDeviceSN(ctx, ticket.DeviceSN, 30)
			if err != nil {
				logger.Warn("检查设备最近30天故障记录失败",
					zap.Error(err),
					zap.String("deviceSN", ticket.DeviceSN))
			} else if faults30Days >= 3 {
				// 更新IsFrequentFault标记
				fields := map[string]interface{}{
					"is_frequent_fault": true,
				}
				if err := faultTicketRepo.UpdateFields(ctx, ticketID, fields); err != nil {
					logger.Warn("更新高频故障标记失败",
						zap.Error(err),
						zap.Uint("ticketID", ticketID))
				} else {
					logger.Info("设备为高频故障(最近30天内>=3次)",
						zap.String("deviceSN", ticket.DeviceSN),
						zap.Int64("故障次数", faults30Days))
				}
			}
		}
	}

	// 更新报障单状态
	err = faultTicketService.UpdateFaultTicketStatus(ctx, ticketID, "acknowledged", 0, "报障单已确认")
	if err != nil {
		logger.Error("更新报障单状态失败", zap.Error(err))
		return err
	}

	return nil
}

// DiagnoseFaultActivity 诊断故障活动
func DiagnoseFaultActivity(ctx context.Context, ticketID uint) (*common.RepairSelectionInput, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("执行诊断故障活动", zap.Uint("ticketID", ticketID))

	// 检查活动依赖是否正确初始化
	if faultTicketRepo == nil || faultTicketService == nil {
		logger.Error("Repository或Service未初始化")
		return nil, ErrActivityDependenciesNotInitialized
	}

	// 获取报障单信息
	ticket, err := faultTicketRepo.GetByID(ctx, ticketID)
	if err != nil {
		logger.Error("获取报障单失败", zap.Error(err))
		return nil, err
	}

	// 根据故障类型和组件类型确定维修类型
	componentType := ticket.ComponentType
	repairType := determineRepairType(ticket.FaultType, componentType)

	// 判断是否需要客户审批
	requireApproval := ticket.RequireApproval

	logger.Info("诊断完成",
		zap.String("repairType", repairType),
		zap.Bool("需要审批", requireApproval))

	return &common.RepairSelectionInput{
		RepairType:              repairType,
		Diagnosis:               "系统诊断结果: " + ticket.FaultType + " 故障",
		RequireCustomerApproval: requireApproval,
	}, nil
}

// CreateCustomerApprovalActivity 创建客户审批活动
func CreateCustomerApprovalActivity(ctx context.Context, ticketID uint, approvalType string) error {
	logger.Info("执行创建客户审批活动",
		zap.Uint("ticketID", ticketID),
		zap.String("approvalType", approvalType),
	)

	// 获取报障单信息
	_, err := faultTicketRepo.GetByID(ctx, ticketID)
	if err != nil {
		logger.Error("获取报障单失败", zap.Error(err))
		return err
	}

	// 创建客户审批记录 - 这里需要实际调用创建客户审批的服务
	// 这里仅示意，实际需要实现相应的服务和仓库方法

	// 更新报障单状态
	return faultTicketService.UpdateFaultTicketStatus(ctx, ticketID, "waiting_approval", 0, "系统")
}

// WaitForCustomerApprovalActivity 等待客户审批活动
func WaitForCustomerApprovalActivity(ctx context.Context, ticketID uint) error {
	if faultTicketRepo == nil || customerApprovalRepo == nil || logger == nil {
		logger.Error("活动依赖未初始化", zap.Uint("ticket_id", ticketID))
		return ErrActivityDependenciesNotInitialized
	}

	logger.Info("等待客户审批", zap.Uint("ticket_id", ticketID))

	// 获取工单
	ticket, err := faultTicketRepo.GetByID(ctx, ticketID)
	if err != nil {
		logger.Error("获取工单失败", zap.Error(err), zap.Uint("ticket_id", ticketID))
		return err
	}

	// 更新工单状态为"等待审批"
	if ticket.Status != "waiting_approval" {
		ticket.Status = "waiting_approval"
		if err := faultTicketRepo.Update(ctx, ticket); err != nil {
			logger.Error("更新工单状态失败", zap.Error(err), zap.Uint("ticket_id", ticketID))
			return err
		}
	}

	// 定期检查客户审批结果
	checkInterval := 15 * time.Second
	heartbeatInterval := 30 * time.Second
	lastHeartbeat := time.Now()

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(checkInterval):
			// 发送心跳以防止活动超时
			if time.Since(lastHeartbeat) >= heartbeatInterval {
				activity.RecordHeartbeat(ctx)
				lastHeartbeat = time.Now()
			}

			// 检查客户审批结果
			approval, err := customerApprovalRepo.GetByTicketID(ctx, ticketID)
			if err == nil && approval != nil {
				switch approval.Status {
				case "approved":
					logger.Info("客户已批准", zap.Uint("ticket_id", ticketID))
					return nil
				case "rejected":
					logger.Info("客户已拒绝", zap.Uint("ticket_id", ticketID))
					return errors.New("客户拒绝了维修方案")
				}
			}
		}
	}
}

// CancelFaultTicketActivity 取消报障单活动
func CancelFaultTicketActivity(ctx context.Context, ticketID uint, reason string, cancellationType string, relatedTicketID uint, isFalseAlarm bool, isDuplicateFault bool) error {
	logger.Info("执行取消报障单活动",
		zap.Uint("ticketID", ticketID),
		zap.String("reason", reason),
		zap.String("cancellationType", cancellationType),
		zap.Uint("relatedTicketID", relatedTicketID),
	)

	// 现在直接从函数参数获取isFalseAlarm和isDuplicateFault，无需在这里重新声明
	// 添加日志记录参数值，便于跟踪
	logger.Info("从活动参数中获取取消标志",
		zap.Bool("isFalseAlarm", isFalseAlarm),
		zap.Bool("isDuplicateFault", isDuplicateFault))

	// 先获取报障单
	ticket, err := faultTicketRepo.GetByID(ctx, ticketID)
	if err != nil {
		logger.Error("获取报障单失败", zap.Error(err), zap.Uint("ticketID", ticketID))
		return fmt.Errorf("获取报障单失败: %w", err)
	}

	// 设置不计入SLA和取消状态
	ticket.CountInSLA = false
	ticket.Status = common.StatusCancelled // 明确设置状态为cancelled

	// 根据取消类型设置相应字段
	switch cancellationType {
	case "false_alarm": // 误报
		ticket.IsFalseAlarm = true
		logger.Info("根据cancellationType设置工单为误报",
			zap.Uint("ticketID", ticketID),
			zap.Bool("IsFalseAlarm", ticket.IsFalseAlarm))
	case "duplicate_fault": // 重复故障
		ticket.IsDuplicateFault = true
		if relatedTicketID > 0 {
			ticket.RelatedTicketID = relatedTicketID
			logger.Info("根据cancellationType设置工单为重复故障，关联到工单",
				zap.Uint("ticketID", ticketID),
				zap.Uint("relatedTicketID", relatedTicketID),
				zap.Bool("IsDuplicateFault", ticket.IsDuplicateFault))

			// 单独更新关联工单ID，确保它被设置
			updateFields := map[string]interface{}{
				"is_duplicate_fault": true,
				"related_ticket_id":  relatedTicketID,
			}
			if err := faultTicketRepo.UpdateFields(ctx, ticketID, updateFields); err != nil {
				logger.Error("在取消活动中单独更新相关字段失败",
					zap.Error(err),
					zap.Uint("ticketID", ticketID),
					zap.Uint("relatedTicketID", relatedTicketID))
			} else {
				logger.Info("在取消活动中成功单独更新关联工单ID",
					zap.Uint("ticketID", ticketID),
					zap.Uint("relatedTicketID", relatedTicketID))
			}
		} else {
			logger.Warn("设置工单为重复故障，但未提供有效的关联工单ID",
				zap.Uint("ticketID", ticketID),
				zap.Bool("IsDuplicateFault", ticket.IsDuplicateFault))
		}
	default:
		// 如果cancellationType未指定，则使用直接传递的字段
		if isFalseAlarm {
			ticket.IsFalseAlarm = true
			logger.Info("根据isFalseAlarm参数设置工单为误报",
				zap.Uint("ticketID", ticketID),
				zap.Bool("IsFalseAlarm", ticket.IsFalseAlarm),
				zap.Bool("原始isFalseAlarm", isFalseAlarm))
		} else if isDuplicateFault {
			ticket.IsDuplicateFault = true
			if relatedTicketID > 0 {
				ticket.RelatedTicketID = relatedTicketID
				logger.Info("根据isDuplicateFault参数设置工单为重复故障，关联到工单",
					zap.Uint("ticketID", ticketID),
					zap.Uint("relatedTicketID", relatedTicketID),
					zap.Bool("IsDuplicateFault", ticket.IsDuplicateFault),
					zap.Bool("原始isDuplicateFault", isDuplicateFault))
			} else {
				logger.Warn("设置工单为重复故障，但未提供有效的关联工单ID",
					zap.Uint("ticketID", ticketID),
					zap.Bool("IsDuplicateFault", ticket.IsDuplicateFault),
					zap.Bool("原始isDuplicateFault", isDuplicateFault))
			}
		} else {
			logger.Info("未指定特定取消类型或使用了未知类型",
				zap.Uint("ticketID", ticketID),
				zap.String("cancellationType", cancellationType),
				zap.Bool("isFalseAlarm", isFalseAlarm),
				zap.Bool("isDuplicateFault", isDuplicateFault))
		}
	}

	// 强制记录最终的取消相关字段值
	logger.Info("最终设置的取消相关字段值",
		zap.Uint("ticketID", ticketID),
		zap.Bool("IsFalseAlarm", ticket.IsFalseAlarm),
		zap.Bool("IsDuplicateFault", ticket.IsDuplicateFault),
		zap.Uint("RelatedTicketID", ticket.RelatedTicketID),
		zap.Bool("CountInSLA", ticket.CountInSLA))

	// 设置取消原因到故障总结字段
	if reason != "" {
		ticket.FaultSummary = fmt.Sprintf("取消原因: %s", reason)
		logger.Info("已设置取消原因到故障总结字段",
			zap.Uint("ticketID", ticketID),
			zap.String("faultSummary", ticket.FaultSummary))
	}

	// 记录详细的更新信息，帮助调试
	logger.Info("准备使用完整Update方法更新以下字段",
		zap.Uint("ticketID", ticketID),
		zap.Bool("CountInSLA", ticket.CountInSLA),
		zap.String("Status", ticket.Status),
		zap.Bool("IsFalseAlarm", ticket.IsFalseAlarm),
		zap.Bool("IsDuplicateFault", ticket.IsDuplicateFault),
		zap.Uint("RelatedTicketID", ticket.RelatedTicketID),
		zap.String("FaultSummary", ticket.FaultSummary))

	// 使用完整的Update方法以确保所有字段都被正确更新
	err = faultTicketRepo.Update(ctx, ticket)
	if err != nil {
		logger.Error("使用完整Update方法更新报障单失败", zap.Error(err), zap.Uint("ticketID", ticketID))

		// 如果完整Update失败，再尝试只更新关键字段
		updateFields := map[string]interface{}{
			"count_in_sla": false,
			"status":       common.StatusCancelled,
		}

		// 添加取消类型相关字段
		switch cancellationType {
		case "false_alarm":
			updateFields["is_false_alarm"] = true
		case "duplicate_fault":
			updateFields["is_duplicate_fault"] = true
			if relatedTicketID > 0 {
				updateFields["related_ticket_id"] = relatedTicketID
			}
		default:
			// 如果cancellationType未指定，则使用直接传递的字段
			if isFalseAlarm {
				updateFields["is_false_alarm"] = true
			} else if isDuplicateFault {
				updateFields["is_duplicate_fault"] = true
				if relatedTicketID > 0 {
					updateFields["related_ticket_id"] = relatedTicketID
				}
			}
		}

		// 也包含故障总结字段的更新
		if reason != "" {
			updateFields["fault_summary"] = fmt.Sprintf("取消原因: %s", reason)
		}

		// 记录详细的字段值
		logger.Info("准备使用UpdateFields方法更新以下字段",
			zap.Uint("ticketID", ticketID),
			zap.Any("updateFields", updateFields),
			zap.Bool("is_false_alarm", updateFields["is_false_alarm"] == true),
			zap.Bool("count_in_sla", updateFields["count_in_sla"] == false),
			zap.String("status", fmt.Sprintf("%v", updateFields["status"])))

		err = faultTicketRepo.UpdateFields(ctx, ticketID, updateFields)
		if err != nil {
			logger.Error("更新报障单关键字段失败", zap.Error(err), zap.Uint("ticketID", ticketID))
			// 即使更新失败也继续处理状态变更
		} else {
			logger.Info("已设置报障单不计入SLA并更新状态为cancelled",
				zap.Uint("ticketID", ticketID),
				zap.Any("updatedFields", updateFields))
		}
	} else {
		logger.Info("已使用完整Update方法设置报障单不计入SLA和cancelled状态",
			zap.Uint("ticketID", ticketID),
			zap.Bool("isFalseAlarm", ticket.IsFalseAlarm),
			zap.Bool("isDuplicateFault", ticket.IsDuplicateFault),
			zap.Uint("relatedTicketID", ticket.RelatedTicketID))
	}

	// 另外调用UpdateCancelledTicketSLAActivity确保count_in_sla被设置
	err = UpdateCancelledTicketSLAActivity(ctx, ticketID)
	if err != nil {
		logger.Error("确保已取消工单不计入SLA失败", zap.Error(err), zap.Uint("ticketID", ticketID))
		// 错误不阻止继续处理
	}

	// 更新报障单状态为已取消
	err = faultTicketService.UpdateFaultTicketStatus(ctx, ticketID, common.StatusCancelled, 0, reason)
	if err != nil {
		logger.Error("更新报障单状态失败", zap.Error(err), zap.Uint("ticketID", ticketID))
		return err
	}

	// 完成取消流程后再次获取工单记录，验证字段是否正确更新
	updatedTicket, getErr := faultTicketRepo.GetByID(ctx, ticketID)
	if getErr != nil {
		logger.Error("完成取消后获取工单失败", zap.Error(getErr), zap.Uint("ticketID", ticketID))
	} else {
		logger.Info("完成取消流程后的工单状态",
			zap.Uint("ticketID", ticketID),
			zap.String("Status", updatedTicket.Status),
			zap.Bool("CountInSLA", updatedTicket.CountInSLA),
			zap.Bool("IsFalseAlarm", updatedTicket.IsFalseAlarm),
			zap.Bool("IsDuplicateFault", updatedTicket.IsDuplicateFault),
			zap.Uint("RelatedTicketID", updatedTicket.RelatedTicketID),
			zap.String("FaultSummary", updatedTicket.FaultSummary))
	}

	return nil
}

// CreateRepairTicketActivity 创建维修单活动
func CreateRepairTicketActivity(ctx context.Context, ticketID uint, repairType string) (uint, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("执行创建维修单活动", zap.Uint("ticketID", ticketID), zap.String("repairType", repairType))

	if faultTicketService == nil {
		return 0, errors.New("故障单服务未初始化")
	}

	// 首先检查故障单信息
	faultTicket, err := faultTicketRepo.GetByID(ctx, ticketID)
	if err != nil {
		logger.Error("获取故障单失败",
			zap.Error(err),
			zap.Uint("ticketID", ticketID))
		return 0, fmt.Errorf("获取故障单失败: %w", err)
	}

	// 如果故障单已经关联了维修单，直接返回维修单ID
	if faultTicket.RepairTicketID > 0 {
		logger.Info("故障单已关联维修单，直接返回",
			zap.Uint("ticketID", ticketID),
			zap.Uint("repairTicketID", faultTicket.RepairTicketID))
		return faultTicket.RepairTicketID, nil
	}

	// 直接使用原始维修类型作为维修方法，但仍需要决定是否创建维修单和使用哪种维修类型
	// 定义哪些维修类型需要创建维修单
	needRepairTicketTypes := map[string]bool{
		"hardware_fix":    true,
		"restart":         false,
		"cold_migration":  false,
		"software_fix":    false, // 软件修复不需要创建维修单
		"firmware_update": false, // 固件更新不需要创建维修单
		"config_change":   false, // 配置变更不需要创建维修单
	}

	// 数据库维修类型映射（仅用于创建维修单时确定正确的枚举值）
	dbRepairTypeMap := map[string]string{
		"hardware_fix":    "hardware_fix",
		"restart":         "restart",
		"cold_migration":  "cold_migration",
		"software_fix":    "software_fix",
		"firmware_update": "firmware_update",
		"config_change":   "config_change",
	}

	// 获取数据库维修类型
	dbRepairType, exists := dbRepairTypeMap[repairType]
	if !exists {
		// 如果没有匹配项，使用默认安全值
		logger.Warn("未找到维修类型映射，使用默认值",
			zap.String("输入类型", repairType))
		dbRepairType = "hardware_fix"
	}

	// 判断是否需要创建维修单
	needRepairTicket, exists := needRepairTicketTypes[repairType]
	if !exists {
		// 默认情况下不创建维修单
		needRepairTicket = false
	}

	logger.Info("维修类型处理结果",
		zap.String("原始类型", repairType),
		zap.String("数据库维修类型", dbRepairType),
		zap.Bool("需要创建维修单", needRepairTicket))

	// 更新故障单的维修方法字段 - 直接使用原始维修类型
	if faultTicket.RepairMethod == "" {
		faultTicket.RepairMethod = repairType
		if updateErr := faultTicketRepo.Update(ctx, faultTicket); updateErr != nil {
			logger.Warn("更新故障单维修方法失败，但继续处理",
				zap.Error(updateErr),
				zap.Uint("ticketID", ticketID))
		} else {
			logger.Info("成功更新故障单维修方法",
				zap.Uint("ticketID", ticketID),
				zap.String("维修方法", repairType))
		}
	}

	// 检查是否需要创建维修单
	if !needRepairTicket {
		logger.Info("当前维修类型不需要创建维修单",
			zap.String("维修类型", repairType))
		return 0, nil
	}

	// 以下是创建维修单的逻辑
	// 最多尝试3次创建维修单，处理可能的重复错误
	var repairTicket *model.RepairTicket
	var createErr error

	for attempt := 1; attempt <= 3; attempt++ {
		// 创建维修单
		repairTicket, createErr = faultTicketService.CreateRepairTicket(ctx, ticketID, dbRepairType)

		// 如果成功创建或遇到非重复键错误，退出循环
		if createErr == nil || !strings.Contains(createErr.Error(), "Duplicate entry") {
			break
		}

		logger.Warn("创建维修单时遇到重复错误，重试",
			zap.Error(createErr),
			zap.Int("尝试次数", attempt),
			zap.Uint("ticketID", ticketID))

		// 等待一段时间后重试，避免高并发时冲突
		time.Sleep(100 * time.Millisecond)
	}

	// 处理最终错误
	if createErr != nil {
		logger.Error("创建维修单失败",
			zap.Error(createErr),
			zap.Uint("ticketID", ticketID),
			zap.String("维修类型", dbRepairType))

		// 如果是重复键错误，尝试查找已存在的维修单
		if strings.Contains(createErr.Error(), "Duplicate entry") {
			// 尝试从故障单信息中获取维修单ID
			updatedFaultTicket, getErr := faultTicketRepo.GetByID(ctx, ticketID)
			if getErr == nil && updatedFaultTicket.RepairTicketID > 0 {
				logger.Info("找到已关联的维修单，返回现有ID",
					zap.Uint("ticketID", ticketID),
					zap.Uint("repairTicketID", updatedFaultTicket.RepairTicketID))
				return updatedFaultTicket.RepairTicketID, nil
			}

			// 使用维修单仓库查询维修单
			existingRepairTicket, getErr := repairTicketRepo.GetByFaultTicketID(ctx, ticketID)
			if getErr == nil && existingRepairTicket != nil {
				logger.Info("找到关联的维修单，返回现有ID",
					zap.Uint("ticketID", ticketID),
					zap.Uint("repairTicketID", existingRepairTicket.ID))

				// 更新故障单的维修单ID关联
				faultTicket.RepairTicketID = existingRepairTicket.ID
				if updateErr := faultTicketRepo.Update(ctx, faultTicket); updateErr != nil {
					logger.Warn("更新故障单维修单ID关联失败，但不影响流程",
						zap.Error(updateErr),
						zap.Uint("ticketID", ticketID),
						zap.Uint("repairTicketID", existingRepairTicket.ID))
				}

				return existingRepairTicket.ID, nil
			}
		}

		return 0, fmt.Errorf("创建维修单失败: %w", createErr)
	}

	if repairTicket == nil {
		return 0, errors.New("创建维修单返回nil")
	}

	logger.Info("成功创建维修单",
		zap.Uint("repairTicketID", repairTicket.ID),
		zap.String("原始类型", repairType),
		zap.String("使用类型", dbRepairType))
	return repairTicket.ID, nil
}

// VerifyRepairActivity 验证维修结果活动
func VerifyRepairActivity(ctx context.Context, ticketID uint) (*common.VerificationResult, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("执行验证维修结果活动", zap.Uint("ticketID", ticketID))

	// 获取报障单信息
	ticket, err := faultTicketRepo.GetByID(ctx, ticketID)
	if err != nil {
		logger.Error("获取报障单失败", zap.Error(err))
		return nil, err
	}

	// 根据工单状态判断验证结果
	success := ticket.Status == "completed"

	return &common.VerificationResult{
		Success:  success,
		Comments: "系统自动验证通过",
	}, nil
}

// SummarizeFaultTicketActivity 总结报障单活动
func SummarizeFaultTicketActivity(ctx context.Context, ticketID uint, summary string, repairMethod string, preventionMeasures string, status string, faultType string, isFalseAlarm bool, isDuplicateFault bool, relatedTicketID uint) error {
	logger.Info("执行总结报障单活动",
		zap.Uint("ticketID", ticketID),
		zap.String("status", status),
		zap.Bool("isFalseAlarm", isFalseAlarm),
		zap.Bool("isDuplicateFault", isDuplicateFault),
		zap.Uint("relatedTicketID", relatedTicketID))

	// 获取报障单信息
	ticket, err := faultTicketRepo.GetByID(ctx, ticketID)
	if err != nil {
		logger.Error("获取报障单失败", zap.Error(err))
		return err
	}

	// 只更新需要的字段，保留其他原始数据
	originalStatus := ticket.Status

	// 如果当前状态是cancelled，保持cancelled状态并确保不计入SLA
	if originalStatus == common.StatusCancelled || status == common.StatusCancelled {
		// 如果当前状态已经是cancelled或要设置为cancelled
		logger.Info("检测到summary阶段状态为cancelled，自动设置count_in_sla=false，工单ID=" + fmt.Sprintf("%d", ticketID))
		status = common.StatusCancelled

		// 明确设置不计入SLA
		ticket.CountInSLA = false
		logger.Info("已确保已取消工单不计入SLA", zap.Uint("ticketID", ticketID))

		// 优先使用传入的参数值
		if isFalseAlarm {
			ticket.IsFalseAlarm = true
			logger.Info("工单ID=" + fmt.Sprintf("%d", ticketID) + "根据传入参数设置is_false_alarm=true")
		} else if strings.Contains(strings.ToLower(summary), "误报") {
			// 如果参数未设置，但摘要包含"误报"，也设置为true
			ticket.IsFalseAlarm = true
			logger.Info("工单ID=" + fmt.Sprintf("%d", ticketID) + "的取消原因包含\"误报\"，自动设置is_false_alarm=true")
		}

		if isDuplicateFault {
			ticket.IsDuplicateFault = true
			logger.Info("工单ID=" + fmt.Sprintf("%d", ticketID) + "根据传入参数设置is_duplicate_fault=true")

			// 设置关联工单ID
			if relatedTicketID > 0 {
				ticket.RelatedTicketID = relatedTicketID
				logger.Info("工单ID=" + fmt.Sprintf("%d", ticketID) + "根据传入参数设置关联工单ID=" + fmt.Sprintf("%d", relatedTicketID))

				// 单独更新关联工单ID以确保它被设置
				relatedFields := map[string]interface{}{
					"is_duplicate_fault": true,
					"related_ticket_id":  relatedTicketID,
				}
				if updateErr := faultTicketRepo.UpdateFields(ctx, ticketID, relatedFields); updateErr != nil {
					logger.Error("单独更新关联工单ID失败",
						zap.Error(updateErr),
						zap.Uint("ticketID", ticketID),
						zap.Uint("relatedTicketID", relatedTicketID))
				} else {
					logger.Info("SummarizeFaultTicketActivity中成功单独更新关联工单ID",
						zap.Uint("ticketID", ticketID),
						zap.Uint("relatedTicketID", relatedTicketID))
				}
			}
		} else if strings.Contains(strings.ToLower(summary), "重复") {
			// 如果参数未设置，但摘要包含"重复"，也设置为true
			ticket.IsDuplicateFault = true
			logger.Info("工单ID=" + fmt.Sprintf("%d", ticketID) + "的取消原因包含\"重复\"，自动设置is_duplicate_fault=true")
		}

		// 在已取消状态下，如果已有取消原因（以"取消原因:"开头），则不要覆盖
		if strings.HasPrefix(ticket.FaultSummary, "取消原因:") {
			logger.Info("已取消工单已有取消原因，保留原有内容",
				zap.Uint("ticketID", ticketID),
				zap.String("保留的取消原因", ticket.FaultSummary))
		} else if summary != "" {
			// 如果新提供的总结不为空，且原来没有取消原因，则更新
			ticket.FaultSummary = fmt.Sprintf("取消原因: %s", summary)
			logger.Info("工单ID=" + fmt.Sprintf("%d", ticketID) + "的取消原因: " + summary + "，已设置到故障总结字段")
		}
	} else {
		// 非cancelled状态，正常更新FaultSummary
		ticket.FaultSummary = summary
	}

	ticket.RepairMethod = repairMethod
	ticket.PreventionMeasures = preventionMeasures

	// 如果提供了故障类型，则更新故障类型字段
	if faultType != "" {
		// 验证故障类型是否有效
		validFaultTypes := map[string]bool{
			"hardware":  true,
			"software":  true,
			"network":   true,
			"system":    true,
			"operation": true,
			"other":     true,
		}

		if validFaultTypes[faultType] {
			ticket.FaultType = faultType
			logger.Info("更新故障类型",
				zap.Uint("ticketID", ticketID),
				zap.String("故障类型", faultType))
		} else {
			logger.Warn("提供的故障类型无效，使用默认值",
				zap.Uint("ticketID", ticketID),
				zap.String("无效故障类型", faultType))
		}
	}

	logger.Info("更新报障单",
		zap.Uint("ticketID", ticketID),
		zap.String("状态", originalStatus),
		zap.String("摘要", ticket.FaultSummary))

	if err := faultTicketRepo.Update(ctx, ticket); err != nil {
		logger.Error("更新报障单失败", zap.Error(err))
		return err
	}

	// 如果原始状态是cancelled，并且目标状态不是cancelled，记录警告并维持cancelled状态
	if originalStatus == common.StatusCancelled && status != common.StatusCancelled {
		logger.Warn("尝试将已取消工单更改为其他状态，将保持cancelled状态",
			zap.Uint("ticketID", ticketID),
			zap.String("原状态", originalStatus),
			zap.String("目标状态", status))
		status = common.StatusCancelled

		// 再次确保额外调用UpdateCancelledTicketSLAActivity确保count_in_sla被设置为false
		err = UpdateCancelledTicketSLAActivity(ctx, ticketID)
		if err != nil {
			logger.Error("调用UpdateCancelledTicketSLAActivity失败", zap.Error(err), zap.Uint("ticketID", ticketID))
			// 错误不阻止继续处理
		}
	}

	return faultTicketService.UpdateFaultTicketStatus(ctx, ticketID, status, 0, "系统")
}

// UpdateSLARecordActivity 更新SLA记录活动
func UpdateSLARecordActivity(ctx context.Context, ticketID uint) error {
	logger.Info("执行更新SLA记录活动", zap.Uint("ticketID", ticketID))

	// 获取报障单信息
	ticket, err := faultTicketRepo.GetByID(ctx, ticketID)
	if err != nil {
		logger.Error("获取报障单失败", zap.Error(err))
		return err
	}

	// 只更新SLA状态，保留其他原始数据
	ticket.SLAStatus = "in_compliance"
	logger.Info("更新SLA状态",
		zap.Uint("ticketID", ticketID),
		zap.String("SLA状态", ticket.SLAStatus))

	return faultTicketRepo.Update(ctx, ticket)
}

// 工具函数
func determineRepairType(faultType string, componentType string) string {
	// 根据故障类型和组件类型判断维修类型
	switch faultType {
	case "software":
		return "software_fix"
	case "hardware":
		// 统一使用 hardware_fix，不再根据组件类型区分
		return "hardware_fix"
	case "network":
		return "network_fix"
	case "storage":
		return "storage_fix"
	default:
		return "general_fix"
	}
}

// WaitForTicketAcceptanceActivity 等待工单接受活动
func WaitForTicketAcceptanceActivity(ctx context.Context, ticketID uint) error {
	if faultTicketRepo == nil || logger == nil {
		logger.Error("活动依赖未初始化", zap.Uint("ticket_id", ticketID))
		return ErrActivityDependenciesNotInitialized
	}

	logger.Info("等待工单接受", zap.Uint("ticket_id", ticketID))

	// 获取工单
	ticket, err := faultTicketRepo.GetByID(ctx, ticketID)
	if err != nil {
		logger.Error("获取工单失败", zap.Error(err), zap.Uint("ticket_id", ticketID))
		return err
	}

	// 如果工单状态已经是"investigating"或之后的状态，说明已经被接受
	if ticket.Status == "investigating" || ticket.Status == "repairing" ||
		ticket.Status == "waiting_approval" || ticket.Status == "verifying" ||
		ticket.Status == "completed" {
		logger.Info("工单已被接受", zap.Uint("ticket_id", ticketID))
		return nil
	}

	// 更新工单状态为"等待接受"
	if ticket.Status != "waiting_accept" {
		ticket.Status = "waiting_accept"
		if err := faultTicketRepo.Update(ctx, ticket); err != nil {
			logger.Error("更新工单状态失败", zap.Error(err), zap.Uint("ticket_id", ticketID))
			return err
		}
	}

	// 定期检查工单是否被接受
	checkInterval := 10 * time.Second
	heartbeatInterval := 30 * time.Second
	lastHeartbeat := time.Now()

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(checkInterval):
			// 发送心跳以防止活动超时
			if time.Since(lastHeartbeat) >= heartbeatInterval {
				activity.RecordHeartbeat(ctx)
				lastHeartbeat = time.Now()
			}

			// 检查工单状态
			updatedTicket, err := faultTicketRepo.GetByID(ctx, ticketID)
			if err != nil {
				logger.Error("获取工单失败", zap.Error(err), zap.Uint("ticket_id", ticketID))
				continue
			}

			if updatedTicket.Status == "investigating" || updatedTicket.Status == "repairing" {
				logger.Info("工单已被接受", zap.Uint("ticket_id", ticketID))
				return nil
			}
		}
	}
}

// WaitForRepairSelectionActivity 等待维修选择活动
func WaitForRepairSelectionActivity(ctx context.Context, ticketID uint) error {
	if faultTicketRepo == nil || repairSelectionRepo == nil || logger == nil {
		logger.Error("活动依赖未初始化", zap.Uint("ticket_id", ticketID))
		return ErrActivityDependenciesNotInitialized
	}

	logger.Info("等待维修选择", zap.Uint("ticket_id", ticketID))

	// 获取工单
	ticket, err := faultTicketRepo.GetByID(ctx, ticketID)
	if err != nil {
		logger.Error("获取工单失败", zap.Error(err), zap.Uint("ticket_id", ticketID))
		return err
	}

	// 更新工单状态为"调查中"
	if ticket.Status != "investigating" {
		ticket.Status = "investigating"
		if err := faultTicketRepo.Update(ctx, ticket); err != nil {
			logger.Error("更新工单状态失败", zap.Error(err), zap.Uint("ticket_id", ticketID))
			return err
		}
	}

	// 定期检查是否已有维修选择
	checkInterval := 10 * time.Second
	heartbeatInterval := 30 * time.Second
	lastHeartbeat := time.Now()

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(checkInterval):
			// 发送心跳以防止活动超时
			if time.Since(lastHeartbeat) >= heartbeatInterval {
				activity.RecordHeartbeat(ctx)
				lastHeartbeat = time.Now()
			}

			// 检查是否有维修选择记录
			selection, err := repairSelectionRepo.GetByTicketID(ctx, ticketID)
			if err == nil && selection != nil {
				// 更新工单状态为"维修中"
				ticket.Status = "repairing"
				if err := faultTicketRepo.Update(ctx, ticket); err != nil {
					logger.Error("更新工单状态失败", zap.Error(err), zap.Uint("ticket_id", ticketID))
					continue
				}

				logger.Info("已选择维修方案", zap.Uint("ticket_id", ticketID), zap.String("repair_type", selection.RepairType))
				return nil
			}
		}
	}
}

// WaitForVerificationActivity 等待验证活动
func WaitForVerificationActivity(ctx context.Context, ticketID uint) error {
	if faultTicketRepo == nil || verificationRepo == nil || logger == nil {
		logger.Error("活动依赖未初始化", zap.Uint("ticket_id", ticketID))
		return ErrActivityDependenciesNotInitialized
	}

	logger.Info("等待验证结果", zap.Uint("ticket_id", ticketID))

	// 获取工单
	ticket, err := faultTicketRepo.GetByID(ctx, ticketID)
	if err != nil {
		logger.Error("获取工单失败", zap.Error(err), zap.Uint("ticket_id", ticketID))
		return err
	}

	// 更新工单状态为"等待验证"
	if ticket.Status != "waiting_verification" {
		ticket.Status = "waiting_verification"
		if err := faultTicketRepo.Update(ctx, ticket); err != nil {
			logger.Error("更新工单状态失败", zap.Error(err), zap.Uint("ticket_id", ticketID))
			return err
		}
	}

	// 定期检查验证结果
	checkInterval := 15 * time.Second
	heartbeatInterval := 30 * time.Second
	lastHeartbeat := time.Now()

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(checkInterval):
			// 发送心跳以防止活动超时
			if time.Since(lastHeartbeat) >= heartbeatInterval {
				activity.RecordHeartbeat(ctx)
				lastHeartbeat = time.Now()
			}

			// 检查验证结果
			verification, err := verificationRepo.GetByTicketID(ctx, ticketID)
			if err == nil && verification != nil {
				if verification.Success {
					logger.Info("验证通过", zap.Uint("ticket_id", ticketID))
					return nil
				} else {
					logger.Info("验证失败", zap.Uint("ticket_id", ticketID))
					return errors.New("验证未通过")
				}
			}
		}
	}
}

// WaitForSummaryActivity 等待故障总结活动
func WaitForSummaryActivity(ctx context.Context, ticketID uint) error {
	if faultTicketRepo == nil || logger == nil {
		logger.Error("活动依赖未初始化", zap.Uint("ticket_id", ticketID))
		return ErrActivityDependenciesNotInitialized
	}

	logger.Info("等待故障总结", zap.Uint("ticket_id", ticketID))

	// 获取工单
	ticket, err := faultTicketRepo.GetByID(ctx, ticketID)
	if err != nil {
		logger.Error("获取工单失败", zap.Error(err), zap.Uint("ticket_id", ticketID))
		return err
	}

	// 更新工单状态为"总结中"
	if ticket.Status != "summarizing" {
		ticket.Status = "summarizing"
		if err := faultTicketRepo.Update(ctx, ticket); err != nil {
			logger.Error("更新工单状态失败", zap.Error(err), zap.Uint("ticket_id", ticketID))
			return err
		}
	}

	// 定期检查是否已添加故障总结
	checkInterval := 15 * time.Second
	heartbeatInterval := 30 * time.Second
	lastHeartbeat := time.Now()

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(checkInterval):
			// 发送心跳以防止活动超时
			if time.Since(lastHeartbeat) >= heartbeatInterval {
				activity.RecordHeartbeat(ctx)
				lastHeartbeat = time.Now()
			}

			// 检查故障总结
			updatedTicket, err := faultTicketRepo.GetByID(ctx, ticketID)
			if err != nil {
				logger.Error("获取工单失败", zap.Error(err), zap.Uint("ticket_id", ticketID))
				continue
			}

			if updatedTicket.FaultSummary != "" {
				// 更新工单状态不再硬编码为"completed"
				// 由调用方通过SummarizeFaultTicketActivity和UpdateFaultTicketStatusActivity控制状态
				logger.Info("已添加故障总结", zap.Uint("ticket_id", ticketID))
				return nil
			}
		}
	}
}

// updateBusinessTables 更新业务相关表
//
//nolint:unused
func updateBusinessTables(ctx context.Context, ticketID uint, status string, operatorID uint, operatorName string, data map[string]interface{}) error {
	now := time.Now()

	switch status {
	case "waiting_approval":
		// 创建客户审批记录
		approval := &model.CustomerApproval{
			TicketID:     ticketID,
			Status:       "pending",
			ResponseTime: now,
			CustomerID:   operatorID,
			CustomerName: operatorName,
		}
		if err := customerApprovalRepo.Create(ctx, approval); err != nil {
			return fmt.Errorf("创建客户审批记录失败: %w", err)
		}

	case "waiting_verification":
		// 创建验证记录
		verification := &model.Verification{
			TicketID:         ticketID,
			Success:          false,
			VerificationTime: now,
			OperatorID:       operatorID,
			OperatorName:     operatorName,
		}
		if err := verificationRepo.Create(ctx, verification); err != nil {
			return fmt.Errorf("创建验证记录失败: %w", err)
		}

	case "repairing", "restarting", "migrating", "software_fixing":
		// 更新维修选择记录
		if repairType, ok := data["repair_type"].(string); ok {
			selection := &model.RepairSelection{
				TicketID:     ticketID,
				RepairType:   repairType,
				OperatorID:   operatorID,
				OperatorName: operatorName,
			}
			if err := repairSelectionRepo.Create(ctx, selection); err != nil {
				return fmt.Errorf("创建维修选择记录失败: %w", err)
			}
		}
	}

	return nil
}

// UpdateFaultTicketStatusActivity 更新报障单状态活动
func UpdateFaultTicketStatusActivity(ctx context.Context, ticketID uint, status string, operatorID uint, operatorName string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("开始执行状态更新活动",
		zap.Uint("ticketID", ticketID),
		zap.String("newStatus", status),
		zap.Uint("operatorID", operatorID),
		zap.String("operatorName", operatorName))

	if faultTicketRepo == nil {
		logger.Error("Repository未初始化", zap.Uint("ticketID", ticketID))
		return errors.New("faultTicketRepo 未初始化")
	}

	// 1. 首先获取工单以获取原始状态
	originalStatus := ""
	ticket, err := faultTicketRepo.GetByID(ctx, ticketID)
	if err != nil {
		logger.Warn("获取原始状态失败，将使用空值", zap.Error(err), zap.Uint("ticketID", ticketID))
	} else {
		originalStatus = ticket.Status

		// 如果状态未变化，直接返回
		if originalStatus == status {
			logger.Info("工单状态未变化，跳过更新", zap.Uint("ticketID", ticketID), zap.String("status", status))
			return nil
		}

		// 如果当前状态已经是cancelled，并且尝试更新为其他状态，则阻止此操作
		if originalStatus == common.StatusCancelled && status != common.StatusCancelled {
			logger.Warn("工单已处于已取消状态，无法更改为其他状态",
				zap.Uint("ticketID", ticketID),
				zap.String("当前状态", originalStatus),
				zap.String("目标状态", status))
			return nil
		}
	}

	// 2. 准备更新字段
	updateFields := map[string]interface{}{
		"status": status,
	}

	// 如果状态是cancelled，设置不计入SLA
	if status == common.StatusCancelled {
		updateFields["count_in_sla"] = false
		logger.Info("工单状态设置为已取消，同时设置不计入SLA", zap.Uint("ticketID", ticketID))
	}

	// 根据新状态设置相关时间字段
	now := time.Now()
	switch status {
	case common.StatusInvestigating:
		updateFields["acknowledge_time"] = now
	case common.StatusRepairing, common.StatusRestarting, common.StatusMigrating, common.StatusSoftwareFixing:
		// 之前的时间字段设置逻辑
		if ticket != nil && ticket.ExpectedFixTime == nil {
			// 根据维修类型估算预计修复时间
			var estimatedDuration time.Duration
			switch status {
			case common.StatusRestarting:
				estimatedDuration = 30 * time.Minute
			case common.StatusMigrating:
				estimatedDuration = 2 * time.Hour
			case common.StatusSoftwareFixing:
				estimatedDuration = 1 * time.Hour
			case common.StatusRepairing:
				estimatedDuration = 4 * time.Hour
			}
			expectedTime := now.Add(estimatedDuration)
			updateFields["expected_fix_time"] = expectedTime
		}
	case common.StatusWaitingVerification:
		updateFields["actual_fix_time"] = now
		updateFields["verification_start_time"] = now
	case common.StatusCompleted:
		if ticket == nil || ticket.CloseTime == nil {
			updateFields["close_time"] = now
		}
		if ticket != nil && ticket.VerificationEndTime == nil && ticket.VerificationStartTime != nil {
			updateFields["verification_end_time"] = now
		}
	case common.StatusCancelled:
		updateFields["close_time"] = now
	}

	// 不管之前的阶段是什么，总是清除手动触发相关的字段
	updateFields["waiting_manual_trigger"] = false
	updateFields["current_waiting_stage"] = ""

	// 3. 更新工单状态和其他字段 - 不使用事务
	if err := faultTicketRepo.UpdateFields(ctx, ticketID, updateFields); err != nil {
		logger.Error("更新工单状态失败", zap.Error(err), zap.Uint("ticketID", ticketID))
		return fmt.Errorf("更新工单状态失败: %w", err)
	}

	// 4. 创建状态历史记录 - 单独操作
	history := &model.FaultTicketStatusHistory{
		FaultTicketID:    ticketID,
		PreviousStatus:   originalStatus,
		NewStatus:        status,
		OperatorID:       operatorID,
		OperatorName:     operatorName,
		OperationTime:    now,
		Remarks:          fmt.Sprintf("状态从 %s 变更为 %s", originalStatus, status),
		ActivityCategory: getActivityCategory(status),
		IsSLAPause:       isSLAPauseStatus(status),
		PauseReason:      getPauseReason(status),
	}

	// 如果是系统操作，添加标记
	if operatorID == 0 {
		history.OperatorName = "系统"
		history.Remarks = fmt.Sprintf("系统自动将状态从 %s 变更为 %s", originalStatus, status)
	}

	// 单独创建历史记录，即使失败也不影响整体流程
	if err := faultTicketRepo.CreateStatusHistory(ctx, history); err != nil {
		logger.Error("创建状态历史记录失败，但不影响状态更新",
			zap.Error(err),
			zap.Uint("ticketID", ticketID))
		// 继续执行，不返回错误
	}

	logger.Info("工单状态更新成功", zap.Uint("ticketID", ticketID), zap.String("newStatus", status))
	return nil
}

// getActivityCategory 根据状态获取活动类别
func getActivityCategory(status string) string {
	switch status {
	case common.StatusWaitingAccept:
		return "ticket_acceptance"
	case common.StatusInvestigating:
		return "diagnosis"
	case common.StatusWaitingApproval:
		return "customer_approval"
	case common.StatusApprovedWaiting:
		return "repair_preparation"
	case common.StatusRepairing, common.StatusRestarting, common.StatusMigrating, common.StatusSoftwareFixing:
		return "repair_execution"
	case common.StatusWaitingVerification:
		return "verification"
	case common.StatusSummarizing:
		return "summary"
	case common.StatusCompleted:
		return "completion"
	case common.StatusCancelled:
		return "cancellation"
	default:
		return "status_change"
	}
}

// isSLAPauseStatus 判断是否为SLA暂停状态
func isSLAPauseStatus(status string) bool {
	switch status {
	case common.StatusWaitingApproval, common.StatusApprovedWaiting, common.StatusCancelled:
		return true
	default:
		return false
	}
}

// getPauseReason 获取暂停原因
func getPauseReason(status string) string {
	switch status {
	case common.StatusWaitingApproval:
		return "等待客户审批"
	case common.StatusApprovedWaiting:
		return "等待开始维修"
	case common.StatusCancelled:
		return "工单已取消"
	default:
		return ""
	}
}

// ExecuteSystemRestartActivity 执行系统重启活动
func ExecuteSystemRestartActivity(ctx context.Context, ticketID uint) error {
	logger.Info("执行系统重启活动", zap.Uint("ticketID", ticketID))

	// 检查活动依赖是否正确初始化
	if faultTicketService == nil {
		err := errors.New("活动依赖未正确初始化")
		logger.Error("活动依赖未初始化", zap.Error(err))
		return err
	}

	// 更新报障单状态为"重启中"
	err := faultTicketService.UpdateFaultTicketStatus(ctx, ticketID, "restarting", 0, "系统重启中")
	if err != nil {
		logger.Error("更新报障单状态失败", zap.Error(err))
		return err
	}

	// 模拟重启过程（实际环境中应调用相关服务进行重启）
	activity.RecordHeartbeat(ctx)
	time.Sleep(5 * time.Second)
	activity.RecordHeartbeat(ctx)

	// 更新报障单状态为"重启完成"
	err = faultTicketService.UpdateFaultTicketStatus(ctx, ticketID, "restart_completed", 0, "系统重启完成")
	if err != nil {
		logger.Error("更新报障单状态失败", zap.Error(err))
		return err
	}

	logger.Info("系统重启完成", zap.Uint("ticketID", ticketID))
	return nil
}

// ExecuteHardwareRepairActivity 执行硬件维修活动
func ExecuteHardwareRepairActivity(ctx context.Context, ticketID uint, componentType string) error {
	logger.Info("执行硬件维修活动", zap.Uint("ticketID", ticketID), zap.String("componentType", componentType))

	// 检查活动依赖是否正确初始化
	if faultTicketService == nil {
		err := errors.New("活动依赖未正确初始化")
		logger.Error("活动依赖未初始化", zap.Error(err))
		return err
	}

	// 更新报障单状态为"硬件维修中"
	err := faultTicketService.UpdateFaultTicketStatus(ctx, ticketID, "hardware_repairing", 0, "硬件维修中: "+componentType)
	if err != nil {
		logger.Error("更新报障单状态失败", zap.Error(err))
		return err
	}

	// 模拟硬件维修过程（实际环境中应调用相关服务进行维修）
	activity.RecordHeartbeat(ctx)
	time.Sleep(10 * time.Second)
	activity.RecordHeartbeat(ctx)

	// 更新报障单状态为"硬件维修完成"
	err = faultTicketService.UpdateFaultTicketStatus(ctx, ticketID, "hardware_repair_completed", 0, "硬件维修完成: "+componentType)
	if err != nil {
		logger.Error("更新报障单状态失败", zap.Error(err))
		return err
	}

	logger.Info("硬件维修完成", zap.Uint("ticketID", ticketID), zap.String("componentType", componentType))
	return nil
}

// ExecuteSoftwareRepairActivity 执行软件修复活动
func ExecuteSoftwareRepairActivity(ctx context.Context, ticketID uint, softwareType string) error {
	logger.Info("执行软件修复活动", zap.Uint("ticketID", ticketID), zap.String("softwareType", softwareType))

	// 检查活动依赖是否正确初始化
	if faultTicketService == nil {
		err := errors.New("活动依赖未正确初始化")
		logger.Error("活动依赖未初始化", zap.Error(err))
		return err
	}

	// 更新报障单状态为"软件修复中"
	err := faultTicketService.UpdateFaultTicketStatus(ctx, ticketID, "software_repairing", 0, "软件修复中: "+softwareType)
	if err != nil {
		logger.Error("更新报障单状态失败", zap.Error(err))
		return err
	}

	// 模拟软件修复过程（实际环境中应调用相关服务进行修复）
	activity.RecordHeartbeat(ctx)
	time.Sleep(8 * time.Second)
	activity.RecordHeartbeat(ctx)

	// 更新报障单状态为"软件修复完成"
	err = faultTicketService.UpdateFaultTicketStatus(ctx, ticketID, "software_repair_completed", 0, "软件修复完成: "+softwareType)
	if err != nil {
		logger.Error("更新报障单状态失败", zap.Error(err))
		return err
	}

	logger.Info("软件修复完成", zap.Uint("ticketID", ticketID), zap.String("softwareType", softwareType))
	return nil
}

// GetRepairSelectionActivity 获取维修选择记录活动
func GetRepairSelectionActivity(ctx context.Context, ticketID uint) (*common.RepairSelectionInput, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("开始获取维修选择记录", "ticketID", ticketID)

	if repairSelectionRepo == nil {
		logger.Error("Repository未初始化", "ticketID", ticketID)
		// 返回默认结果，避免工作流失败
		return &common.RepairSelectionInput{
			RepairType:              "hardware_fix", // 默认为硬件维修
			Diagnosis:               "系统自动处理：Repository未初始化，使用默认维修类型",
			RequireCustomerApproval: false,
			FaultDetailType:         "",
			SlotPosition:            "",
		}, nil
	}

	// 获取维修选择记录
	selection, err := repairSelectionRepo.GetByTicketID(ctx, ticketID)
	if err != nil {
		logger.Error("获取维修选择记录失败", "error", err, "ticketID", ticketID)
		// 返回默认结果，避免工作流失败
		return &common.RepairSelectionInput{
			RepairType:              "hardware_fix", // 默认为硬件维修
			Diagnosis:               "系统自动处理：获取维修选择记录失败，使用默认维修类型",
			RequireCustomerApproval: false,
			FaultDetailType:         "",
			SlotPosition:            "",
		}, nil
	}

	if selection == nil {
		logger.Warn("未找到维修选择记录，使用默认值", "ticketID", ticketID)
		// 返回默认结果，避免工作流失败
		return &common.RepairSelectionInput{
			RepairType:              "hardware_fix", // 默认为硬件维修
			Diagnosis:               "系统自动处理：未找到维修选择记录，使用默认维修类型",
			RequireCustomerApproval: false,
			FaultDetailType:         "",
			SlotPosition:            "",
		}, nil
	}

	// 转换为工作流输入格式
	input := &common.RepairSelectionInput{
		RepairType:              selection.RepairType,
		Diagnosis:               selection.Diagnosis,
		RequireCustomerApproval: false,
		FaultDetailType:         selection.FaultDetailType,
		SlotPosition:            selection.SlotPosition,
	}

	return input, nil
}

// GetCustomerApprovalActivity 获取客户审批活动
func GetCustomerApprovalActivity(ctx context.Context, ticketID uint) (*common.CustomerApprovalResult, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("执行获取客户审批活动", zap.Uint("ticketID", ticketID))

	if customerApprovalRepo == nil {
		logger.Error("Repository未初始化", zap.Uint("ticketID", ticketID))
		// 返回默认结果，避免工作流失败
		return &common.CustomerApprovalResult{
			Status:   "pending",
			Comments: "系统自动处理：Repository未初始化",
		}, nil
	}

	// 获取客户审批记录
	approval, err := customerApprovalRepo.GetByTicketID(ctx, ticketID)
	if err != nil {
		logger.Error("获取客户审批记录失败", zap.Error(err))
		// 返回默认结果，避免工作流失败
		return &common.CustomerApprovalResult{
			Status:   "pending",
			Comments: "系统自动处理：获取客户审批记录失败",
		}, nil
	}

	if approval == nil {
		logger.Warn("未找到客户审批记录，使用默认值", zap.Uint("ticketID", ticketID))
		// 返回默认结果，避免工作流失败
		return &common.CustomerApprovalResult{
			Status:   "approved", // 直接默认为已审批，确保工作流能继续
			Comments: "系统自动处理：未找到客户审批记录，默认为已审批",
		}, nil
	}

	// 返回客户审批结果
	return &common.CustomerApprovalResult{
		Status:   approval.Status,
		Comments: approval.Comments,
	}, nil
}

// GetVerificationActivity 获取验证结果活动
func GetVerificationActivity(ctx context.Context, ticketID uint) (*common.VerificationResult, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("执行获取验证结果活动", zap.Uint("ticketID", ticketID))

	if verificationRepo == nil {
		logger.Error("Repository未初始化", zap.Uint("ticketID", ticketID))
		// 返回默认结果，避免工作流失败
		return &common.VerificationResult{
			Success:  true, // 默认验证成功
			Comments: "系统自动处理：Repository未初始化，默认验证成功",
		}, nil
	}

	// 获取验证记录
	verification, err := verificationRepo.GetByTicketID(ctx, ticketID)
	if err != nil {
		logger.Error("获取验证记录失败", zap.Error(err))
		// 返回默认结果，避免工作流失败
		return &common.VerificationResult{
			Success:  true, // 默认验证成功
			Comments: "系统自动处理：获取验证记录失败，默认验证成功",
		}, nil
	}

	if verification == nil {
		logger.Warn("未找到验证记录，使用默认值", zap.Uint("ticketID", ticketID))
		// 返回默认结果，避免工作流失败
		return &common.VerificationResult{
			Success:  true, // 默认验证成功
			Comments: "系统自动处理：未找到验证记录，默认验证成功",
		}, nil
	}

	// 返回验证结果
	return &common.VerificationResult{
		Success:  verification.Success,
		Comments: verification.Comments,
	}, nil
}

// SendNotificationActivity 发送通知活动
func SendNotificationActivity(ctx context.Context, ticketID uint, notificationType string, message string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行发送通知活动",
		zap.Uint("ticketID", ticketID),
		zap.String("type", notificationType),
		zap.String("message", message))

	// 这里实现实际的通知逻辑，例如发送邮件、短信或系统内通知
	// 目前仅记录日志，不进行实际发送
	logger.Info("通知已发送（模拟）",
		zap.Uint("ticketID", ticketID),
		zap.String("type", notificationType))

	return nil
}

// AuthorizeRepairTicketForFaultActivity 为故障单授权关联维修单
func AuthorizeRepairTicketForFaultActivity(ctx context.Context, faultTicketID uint, repairTicketID uint, operatorID uint, operatorName string) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行为故障单授权关联维修单活动",
		zap.Uint("faultTicketID", faultTicketID),
		zap.Uint("repairTicketID", repairTicketID),
		zap.Uint("operatorID", operatorID))

	if repairTicketService == nil {
		return ErrActivityDependenciesNotInitialized
	}

	// 如果操作人名称为空或是默认格式，使用特定标识，明确来源是故障单授权
	if operatorName == "" || strings.HasPrefix(operatorName, "用户") {
		var userName string
		var err error

		// 尝试获取真实姓名
		userName, err = GetUserInfoForOperator(ctx, operatorID)
		if err != nil || userName == "" {
			// 如果获取失败，使用明确的标识格式
			operatorName = fmt.Sprintf("故障单(ID:%d)授权人(ID:%d)", faultTicketID, operatorID)
			logger.Info("使用故障单特定标识作为操作人名称",
				zap.String("operatorName", operatorName),
				zap.Uint("operatorID", operatorID))
		} else {
			// 获取成功，使用真实姓名但添加故障单来源信息
			operatorName = fmt.Sprintf("%s(故障单授权)", userName)
			logger.Info("使用真实姓名+故障单标识作为操作人名称",
				zap.String("operatorName", operatorName),
				zap.String("userName", userName))
		}
	}

	// 创建新的上下文，添加故障单信息
	ctxWithSource := context.WithValue(ctx, sourceContextKey, map[string]interface{}{
		"type": "fault_ticket",
		"id":   faultTicketID,
	})

	// 调用维修单服务执行授权
	err := repairTicketService.AuthorizeRepairTicket(ctxWithSource, repairTicketID, operatorID, operatorName)
	if err != nil {
		logger.Error("为故障单授权关联维修单失败",
			zap.Error(err),
			zap.Uint("faultTicketID", faultTicketID),
			zap.Uint("repairTicketID", repairTicketID))
		return err
	}

	logger.Info("为故障单授权关联维修单成功",
		zap.Uint("faultTicketID", faultTicketID),
		zap.Uint("repairTicketID", repairTicketID),
		zap.String("operatorName", operatorName))
	return nil
}

// GetFaultTicketByIDActivity 获取故障单活动
func GetFaultTicketByIDActivity(ctx context.Context, faultTicketID uint) (*model.FaultTicket, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("执行获取故障单活动", zap.Uint("faultTicketID", faultTicketID))

	if faultTicketRepo == nil {
		return nil, ErrActivityDependenciesNotInitialized
	}

	// 使用仓库方法获取故障单信息
	faultTicket, err := faultTicketRepo.GetByID(ctx, faultTicketID)
	if err != nil {
		logger.Error("获取故障单失败", zap.Error(err))
		return nil, err
	}

	return faultTicket, nil
}

// CalculateDurationsActivity 计算时间指标活动
func CalculateDurationsActivity(ctx context.Context, ticketID uint) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行计算时间指标活动", zap.Uint("ticketID", ticketID))

	// 获取报障单信息
	ticket, err := faultTicketRepo.GetByID(ctx, ticketID)
	if err != nil {
		logger.Error("获取报障单失败", zap.Error(err))
		return err
	}

	// 如果工单状态是cancelled，不执行计算，直接返回
	if ticket.Status == common.StatusCancelled {
		logger.Info("工单处于已取消状态，跳过时间指标计算",
			zap.Uint("ticketID", ticketID))
		return nil
	}

	// 判断工单是否已经是完成状态
	if ticket.Status != common.StatusCompleted && ticket.Status != common.StatusSummarizing {
		logger.Warn("工单未处于完成或总结状态，跳过时间计算",
			zap.Uint("ticketID", ticketID),
			zap.String("当前状态", ticket.Status))
		return nil
	}

	// 获取状态历史记录
	histories, err := faultTicketRepo.GetStatusHistory(ctx, ticketID)
	if err != nil {
		logger.Error("获取状态历史记录失败", zap.Error(err))
		return err
	}

	// 确保CloseTime存在，如果不存在则使用当前时间
	now := time.Now()
	if ticket.CloseTime == nil {
		ticket.CloseTime = &now
		logger.Warn("工单缺少CloseTime，使用当前时间",
			zap.Uint("ticketID", ticketID),
			zap.Time("设置的结束时间", now))
	}

	// 1. 计算响应时长：AcknowledgeTime - CreationTime
	if ticket.AcknowledgeTime != nil && !ticket.CreationTime.IsZero() {
		duration := int(ticket.AcknowledgeTime.Sub(ticket.CreationTime).Minutes())
		if duration >= 0 {
			ticket.ResponseDuration = duration
			logger.Info("计算响应时长",
				zap.Uint("ticketID", ticketID),
				zap.Int("响应时长(分钟)", ticket.ResponseDuration))
		} else {
			logger.Warn("计算出负的响应时长，设置为0",
				zap.Uint("ticketID", ticketID),
				zap.Time("响应时间", *ticket.AcknowledgeTime),
				zap.Time("创建时间", ticket.CreationTime))
			ticket.ResponseDuration = 0
		}
	} else {
		logger.Warn("缺少计算响应时长所需的时间点",
			zap.Uint("ticketID", ticketID),
			zap.Bool("有AcknowledgeTime", ticket.AcknowledgeTime != nil),
			zap.Bool("有CreationTime", !ticket.CreationTime.IsZero()))
	}

	// 2. 查找从approved_waiting_action转向repairing的时间点
	var repairingStartTime *time.Time
	for i := 0; i < len(histories); i++ {
		if histories[i].PreviousStatus == common.StatusApprovedWaiting &&
			(histories[i].NewStatus == common.StatusRepairing ||
				histories[i].NewStatus == common.StatusRestarting ||
				histories[i].NewStatus == common.StatusMigrating ||
				histories[i].NewStatus == common.StatusSoftwareFixing) {
			repairingStartTime = &histories[i].OperationTime
			logger.Info("找到维修开始时间点",
				zap.Uint("ticketID", ticketID),
				zap.Time("维修开始时间", histories[i].OperationTime),
				zap.String("前状态", histories[i].PreviousStatus),
				zap.String("新状态", histories[i].NewStatus))
			break
		}
	}

	// 如果没有找到从approved_waiting_action转向维修状态的记录，尝试找到任何进入维修状态的记录
	if repairingStartTime == nil {
		for i := 0; i < len(histories); i++ {
			if histories[i].NewStatus == common.StatusRepairing ||
				histories[i].NewStatus == common.StatusRestarting ||
				histories[i].NewStatus == common.StatusMigrating ||
				histories[i].NewStatus == common.StatusSoftwareFixing {
				repairingStartTime = &histories[i].OperationTime
				logger.Warn("未找到approved_waiting_action转向维修的记录，使用任意进入维修状态的记录",
					zap.Uint("ticketID", ticketID),
					zap.Time("维修开始时间", histories[i].OperationTime),
					zap.String("前状态", histories[i].PreviousStatus),
					zap.String("新状态", histories[i].NewStatus))
				break
			}
		}
	}

	// 3. 计算硬件处理时长：VerificationStartTime - repairingStartTime
	// 但前提是有关联的维修单且有开始维修时间点
	if ticket.RepairTicketID > 0 && ticket.VerificationStartTime != nil && repairingStartTime != nil {
		duration := int(ticket.VerificationStartTime.Sub(*repairingStartTime).Minutes())
		if duration >= 0 {
			ticket.HardwareRepairDuration = duration
			logger.Info("计算硬件处理时长",
				zap.Uint("ticketID", ticketID),
				zap.Uint("维修单ID", ticket.RepairTicketID),
				zap.Int("硬件处理时长(分钟)", ticket.HardwareRepairDuration))
		} else {
			logger.Warn("计算出负的硬件处理时长，设置为0",
				zap.Uint("ticketID", ticketID),
				zap.Time("验证开始时间", *ticket.VerificationStartTime),
				zap.Time("维修开始时间", *repairingStartTime))
			ticket.HardwareRepairDuration = 0
		}
	} else {
		logger.Warn("缺少计算硬件处理时长所需的条件",
			zap.Uint("ticketID", ticketID),
			zap.Uint("维修单ID", ticket.RepairTicketID),
			zap.Bool("有VerificationStartTime", ticket.VerificationStartTime != nil),
			zap.Bool("有repairingStartTime", repairingStartTime != nil))
	}

	// 4. 计算业务影响时长
	// 如果有客户审批时间：CloseTime - CustomerApprovalTime
	// 如果没有客户审批时间：CloseTime - CreationTime
	if ticket.CloseTime != nil {
		if ticket.CustomerApprovalTime != nil {
			duration := int(ticket.CloseTime.Sub(*ticket.CustomerApprovalTime).Minutes())
			if duration >= 0 {
				ticket.BusinessImpactTime = duration
				logger.Info("计算业务影响时长(基于审批时间)",
					zap.Uint("ticketID", ticketID),
					zap.Int("业务影响时长(分钟)", ticket.BusinessImpactTime))
			} else {
				logger.Warn("计算出负的业务影响时长，设置为0",
					zap.Uint("ticketID", ticketID),
					zap.Time("结束时间", *ticket.CloseTime),
					zap.Time("审批时间", *ticket.CustomerApprovalTime))
				ticket.BusinessImpactTime = 0
			}
		} else if !ticket.CreationTime.IsZero() {
			duration := int(ticket.CloseTime.Sub(ticket.CreationTime).Minutes())
			if duration >= 0 {
				ticket.BusinessImpactTime = duration
				logger.Info("计算业务影响时长(基于创建时间)",
					zap.Uint("ticketID", ticketID),
					zap.Int("业务影响时长(分钟)", ticket.BusinessImpactTime))
			} else {
				logger.Warn("计算出负的业务影响时长，设置为0",
					zap.Uint("ticketID", ticketID),
					zap.Time("结束时间", *ticket.CloseTime),
					zap.Time("创建时间", ticket.CreationTime))
				ticket.BusinessImpactTime = 0
			}
		} else {
			logger.Warn("缺少计算业务影响时长所需的时间点",
				zap.Uint("ticketID", ticketID),
				zap.Bool("有CloseTime", ticket.CloseTime != nil),
				zap.Bool("有CustomerApprovalTime", ticket.CustomerApprovalTime != nil),
				zap.Bool("有CreationTime", !ticket.CreationTime.IsZero()))
		}
	}

	// 5. 软件处理时长 = 业务影响时长 - 硬件处理时长
	if ticket.BusinessImpactTime > 0 {
		// 如果没有硬件处理时长，软件处理时长等于业务影响时长
		if ticket.HardwareRepairDuration > 0 {
			ticket.SoftwareFixDuration = ticket.BusinessImpactTime - ticket.HardwareRepairDuration
			// 确保软件处理时长不为负值
			if ticket.SoftwareFixDuration < 0 {
				logger.Warn("计算出负的软件处理时长，设置为0",
					zap.Uint("ticketID", ticketID),
					zap.Int("业务影响时长", ticket.BusinessImpactTime),
					zap.Int("硬件处理时长", ticket.HardwareRepairDuration))
				ticket.SoftwareFixDuration = 0
			}
		} else {
			ticket.SoftwareFixDuration = ticket.BusinessImpactTime
		}
		logger.Info("计算软件处理时长",
			zap.Uint("ticketID", ticketID),
			zap.Int("软件处理时长(分钟)", ticket.SoftwareFixDuration))
	} else {
		logger.Warn("业务影响时长为0，无法计算软件处理时长",
			zap.Uint("ticketID", ticketID))
	}

	// 6. 总停机时间 = 业务影响时长
	ticket.TotalDowntime = ticket.BusinessImpactTime

	// 更新报障单
	err = faultTicketRepo.Update(ctx, ticket)
	if err != nil {
		logger.Error("更新报障单失败", zap.Error(err))
		return err
	}

	logger.Info("成功计算并更新时间指标",
		zap.Uint("ticketID", ticketID),
		zap.Int("响应时长", ticket.ResponseDuration),
		zap.Int("硬件处理时长", ticket.HardwareRepairDuration),
		zap.Int("软件处理时长", ticket.SoftwareFixDuration),
		zap.Int("业务影响时长", ticket.BusinessImpactTime),
		zap.Int("总停机时间", ticket.TotalDowntime))

	return nil
}

// UpdateFieldsActivity 通用字段更新活动
func UpdateFieldsActivity(ctx context.Context, ticketID uint, fields map[string]interface{}) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行通用字段更新活动",
		zap.Uint("ticketID", ticketID),
		zap.Any("fields", fields))

	if faultTicketRepo == nil {
		logger.Error("Repository未初始化", zap.Uint("ticketID", ticketID))
		return errors.New("faultTicketRepo 未初始化")
	}

	// 直接更新字段，不进行状态检查
	err := faultTicketRepo.UpdateFields(ctx, ticketID, fields)
	if err != nil {
		logger.Error("通用字段更新失败", zap.Error(err), zap.Uint("ticketID", ticketID))
		return fmt.Errorf("通用字段更新失败: %w", err)
	}

	logger.Info("成功执行通用字段更新",
		zap.Uint("ticketID", ticketID),
		zap.Any("updatedFields", fields))
	return nil
}

// UpdateCancelledTicketSLAActivity 更新已取消工单的SLA状态
func UpdateCancelledTicketSLAActivity(ctx context.Context, ticketID uint) error {
	logger := activity.GetLogger(ctx)
	logger.Info("执行更新已取消工单SLA状态活动", zap.Uint("ticketID", ticketID))

	if faultTicketRepo == nil {
		logger.Error("Repository未初始化", zap.Uint("ticketID", ticketID))
		return errors.New("faultTicketRepo 未初始化")
	}

	// 直接更新count_in_sla字段为false
	updateFields := map[string]interface{}{
		"count_in_sla": false,
	}

	// 直接更新字段，不进行状态检查
	err := faultTicketRepo.UpdateFields(ctx, ticketID, updateFields)
	if err != nil {
		logger.Error("更新已取消工单SLA状态失败", zap.Error(err), zap.Uint("ticketID", ticketID))
		return fmt.Errorf("更新已取消工单SLA状态失败: %w", err)
	}

	logger.Info("成功更新已取消工单SLA状态为不计入SLA", zap.Uint("ticketID", ticketID))
	return nil
}

// CreateFaultTicketAfterColdMigrationActivity 冷迁移完成后创建新报障单用于后续返厂维修
func CreateFaultTicketAfterColdMigrationActivity(ctx context.Context, ticketInput model.FaultTicket) (uint, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("开始执行冷迁移后创建新报障单活动",
		"deviceSN", ticketInput.DeviceSN,
		"relatedTicketID", ticketInput.RelatedTicketID)

	// 确保服务已正确初始化
	if faultTicketService == nil {
		return 0, fmt.Errorf("故障单服务未初始化")
	}

	// 创建新报障单
	err := faultTicketService.CreateFaultTicket(ctx, &ticketInput)
	if err != nil {
		logger.Error("创建冷迁移后新报障单失败",
			"error", err,
			"deviceSN", ticketInput.DeviceSN,
			"relatedTicketID", ticketInput.RelatedTicketID)
		return 0, fmt.Errorf("创建冷迁移后新报障单失败: %w", err)
	}

	logger.Info("成功创建冷迁移后新报障单",
		"新报障单ID", ticketInput.ID,
		"设备SN", ticketInput.DeviceSN,
		"原报障单ID", ticketInput.RelatedTicketID)

	return ticketInput.ID, nil
}
