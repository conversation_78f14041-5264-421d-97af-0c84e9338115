package activities

import (
	"fmt"
)

// InvalidInputError 表示输入参数无效的错误，该错误类型在工作流中被标记为不可重试
type InvalidInputError struct {
	Message string
}

// Error 实现error接口
func (e *InvalidInputError) Error() string {
	return fmt.Sprintf("无效的输入参数: %s", e.Message)
}

// NewInvalidInputError 创建一个新的InputValidationError
func NewInvalidInputError(message string) error {
	return &InvalidInputError{
		Message: message,
	}
}

// IsInvalidInputError 检查错误是否为InputValidationError类型
func IsInvalidInputError(err error) bool {
	_, ok := err.(*InvalidInputError)
	return ok
}
