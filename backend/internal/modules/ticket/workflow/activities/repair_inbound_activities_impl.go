package activities

import (
	"backend/internal/common/constants"
	"backend/internal/common/utils/notifier"
	"backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/cmdb/model/inbound"
	"backend/internal/modules/cmdb/model/inventory"
	inboundrepo "backend/internal/modules/cmdb/repository/inbound"
	inboundSvc "backend/internal/modules/cmdb/service/inbound"
	inventorySvc "backend/internal/modules/cmdb/service/inventory"
	fileSvc "backend/internal/modules/file/service"
	"backend/internal/modules/ticket/common"
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type repairInboundActivities struct {
	db                *gorm.DB
	inboundSvc        inboundSvc.InboundService
	inventorySvc      inventorySvc.InventoryService
	inboundRepo       inboundrepo.Repository
	fileSvc           fileSvc.FileService
	inboundTicketRepo repository.InboundTicketRepository
	inboundNotifier   *notifier.InboundNotifier
}

func NewRepairInboundActivities(db *gorm.DB, inboundRepo inboundrepo.Repository, inboundSvc inboundSvc.InboundService, inboundTicketRepo repository.InboundTicketRepository, inventorySvc inventorySvc.InventoryService, fileSvc fileSvc.FileService, inboundNotifier *notifier.InboundNotifier) RepairInboundActivity {
	return &repairInboundActivities{
		db:                db,
		inboundRepo:       inboundRepo,
		inventorySvc:      inventorySvc,
		inboundSvc:        inboundSvc,
		fileSvc:           fileSvc,
		inboundTicketRepo: inboundTicketRepo,
		inboundNotifier:   inboundNotifier,
	}
}

func (r repairInboundActivities) UpdateInboundActivity(ctx context.Context, input common.UpdateInput) error {
	//TODO implement me
	panic("implement me")
}

func (r repairInboundActivities) UpdateTicketAndHistory(ctx context.Context, input common.UpdateTicketInput) error {
	// 获取入库工单信息
	logger.Info("更新入库工单信息：", zap.Any("UpdateInput", input))
	ticket, err := r.inboundTicketRepo.GetRepairPartInboundTicketByNo(ctx, input.InboundNo)
	if err != nil {
		return fmt.Errorf("获取入库工单信息失败: %v", err)
	}

	// 更新工单状态
	ticket.PreviousStatus = ticket.Status
	ticket.Status = input.NextStatus
	ticket.UpdatedAt = time.Now()
	ticket.TryCount = input.TryCount
	ticket.Stage = input.Stage

	switch input.CurrentStage {
	case common.StageAssetApproval:
		ticket.AssetApprover = input.OperatorName
		ticket.AssetApproverID = input.OperatorID
	case common.StageVerify:
		ticket.EngineerApproverID = input.OperatorID
		ticket.EngineerApproverID = input.OperatorID
	default:
		return fmt.Errorf("错误输入，输入的阶段：%v", input.CurrentStage)
	}
	// 创建历史记录
	history := &model.RepairPartTicketHistory{
		InboundNo:      ticket.InboundNo,
		PreviousStatus: input.CurrentStatus,
		NewStatus:      input.NextStatus,
		OperatorID:     input.OperatorID,
		OperatorName:   input.OperatorName,
		Comment:        input.Comments,
		Stage:          input.Stage,
		OperationTime:  time.Now(),
	}
	err = r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 更新工单
		err := tx.Model(&model.RepairPartInboundTicket{}).Where("id = ?", input.InboundTicketID).Updates(ticket).Error
		if err != nil {
			return fmt.Errorf("更新返修入库工单状态失败，事务回滚: %v", err)
		}

		// 创建历史记录
		err = tx.Create(history).Error
		if err != nil {
			return fmt.Errorf("创建入库工单历史记录失败，事务回滚: %v", err)
		}
		return nil
	})
	logger.Info("更新工单及历史记录成功", zap.Any("TicketHistory", history), zap.Any("Ticket", ticket))
	if err != nil {
		return err
	}
	return nil
}

func (r repairInboundActivities) ProcessInboundActivity(ctx context.Context, inboundID uint, operatorID uint, location string, quantity int, productDetails string) error {
	//TODO implement me
	panic("implement me")
}

func (r repairInboundActivities) UpdateInboundListStage(ctx context.Context, inboundNo, Stage string) error {
	return r.inboundRepo.UpdateInboundListStage(ctx, inboundNo, Stage)
}

func (r repairInboundActivities) UpdateCMDBwithRepair(ctx context.Context, inboundNo string) error {
	repairInbounds, _, err := r.inboundRepo.GetRepairedInboundByNo(ctx, inboundNo)
	if err != nil {
		return err
	}

	for _, item := range repairInbounds.RepairDetails {
		if item.RepairType == "renew" {
			if err := r.handleRenewRepair(ctx, item); err != nil {
				return fmt.Errorf("更新换新返修信息失败: %v", err)
			}
		} else {
			if err := r.handleNormalRepair(ctx, item); err != nil {
				return fmt.Errorf("更新普通返修信息失败: %v", err)
			}
		}
	}

	return nil
}

func (r repairInboundActivities) handleRenewRepair(ctx context.Context, repair inbound.RepairInboundDetails) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 报废旧资产
		if err := tx.Model(&asset.AssetSpare{}).
			Where("sn = ?", repair.ReplaceSN).
			Updates(map[string]interface{}{
				"asset_status":    inbound.AssetStatusScrapped,
				"hardware_status": inbound.HardwareStatusFaulty,
			}).Error; err != nil {
			return err
		}

		// 查询旧资产
		var old asset.AssetSpare
		if err := tx.Where("sn = ?", repair.ReplaceSN).First(&old).Error; err != nil {
			return err
		}

		// 创建新资产
		newAsset := old
		newAsset.ID = 0 //确保在gorm中可以自增
		newAsset.SN = repair.SN
		newAsset.WarehouseID = repair.WarehouseID
		newAsset.AssetStatus = inbound.AssetStatusIdle
		newAsset.HardwareStatus = inbound.HardwareStatusNormal
		if err := tx.Create(&newAsset).Error; err != nil {
			return fmt.Errorf("换新%s设备创建asset信息失败：: %v", newAsset.SN, err)
		}

		// 更新库存
		return r.updateInventory(ctx, repair.RepairInboundID, old.ProductID, newAsset.WarehouseID, 1)
		//return r.updateStock(tx, old.ProductID, newAsset.WarehouseID, 1)
	})
}

func (r repairInboundActivities) handleNormalRepair(ctx context.Context, repair inbound.RepairInboundDetails) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 更新资产状态
		if err := tx.Model(&asset.AssetSpare{}).
			Where("sn = ?", repair.SN).
			Updates(map[string]interface{}{
				"source_type":     inbound.SourceTypeReturnRepaired,
				"asset_status":    inbound.AssetStatusIdle,
				"hardware_status": inbound.HardwareStatusNormal,
				"warehouse_id":    repair.WarehouseID,
			}).Error; err != nil {
			return err
		}

		// 获取 product_id
		var productID uint
		if err := tx.Model(&asset.AssetSpare{}).
			Where("sn = ?", repair.SN).
			Pluck("product_id", &productID).Error; err != nil {
			return err
		}

		// 更新库存
		return r.updateInventory(ctx, repair.RepairInboundID, productID, repair.WarehouseID, 1)
		//return r.updateStock(tx, productID, repair.WarehouseID, 1)
	})
}

func (r repairInboundActivities) updateStock(tx *gorm.DB, productID uint, warehouseID uint, delta int) error {
	var (
		detail inventory.InventoryDetail
	)
	err := tx.Model(&inventory.InventoryDetail{}).
		Where("product_id = ? AND warehouse_id = ?", productID, warehouseID).
		First(&detail).Error

	if errors.Is(err, gorm.ErrRecordNotFound) { // 线上不应该出现这个问题
		newDetail := inventory.InventoryDetail{
			ProductID:      productID,
			WarehouseID:    warehouseID,
			CurrentStock:   delta,
			AvailableStock: delta,
		}
		return tx.Create(&newDetail).Error
	} else if err != nil {
		return fmt.Errorf("通过productID和warehouseID获取库存详情失败，请联系管理员：%w", err)
	}

	history := inventory.StockHistory{
		ProductID:    productID,
		WarehouseID:  warehouseID,
		DetailID:     detail.ID,
		ChangeType:   inbound.TypeRepairedPartInbound,
		ChangeAmount: 1,
		ChangeTime:   time.Now(),
		OldQuantity:  detail.CurrentStock,
		NewQuantity:  detail.CurrentStock + delta,
	}
	if err := tx.Create(&history).Error; err != nil {
		return fmt.Errorf("创建库存历史失败：%w", err)
	}
	return nil
}

// updateInventory 更新库存
func (r repairInboundActivities) updateInventory(ctx context.Context, ticketID, productID, warehouseID uint, delta int) error {
	var (
		detail inventory.InventoryDetail
	)
	err := r.db.Model(&inventory.InventoryDetail{}).
		Where("product_id = ? AND warehouse_id = ?", productID, warehouseID).
		First(&detail).Error
	if err != nil {
		return fmt.Errorf("查询库存信息失败%w", err)
	}
	err = r.inventorySvc.AdjustStock(ctx, detail.ID, delta, constants.ChangeTypeInbound, constants.ChangeReasonReturnRepair, ticketID, 0)
	if err != nil {
		return err
	}
	return nil
}

func (r repairInboundActivities) SendInboundMsg(ctx context.Context, inboundNo string) error {
	//Inbound, ticket, err := r.inboundRepo.GetRepairedInboundByNo(ctx, inboundNo)
	//if err != nil {
	//	return fmt.Errorf("获取飞书通知前置信息失败: %v", err)
	//}
	//err = r.inboundNotifier.SendInboundNotification(Inbound, ticket)
	//if err != nil {
	//	return fmt.Errorf("发送飞书通知失败: %v", err)
	//}
	return nil
}

func (r repairInboundActivities) SendInboundMsgToSecurityGuard(ctx context.Context, inboundNo string) error {
	Inbound, ticket, err := r.inboundRepo.GetRepairedInboundByNo(ctx, inboundNo)
	if err != nil {
		return fmt.Errorf("获取飞书通知前置信息失败: %v", err)
	}
	moduleType := fmt.Sprintf("%s-photo", Inbound.InboundType())
	module, err := r.fileSvc.GetFilesByModule(moduleType, ticket.ID)
	if err != nil {
		return fmt.Errorf("获取验收单失败: %v", err)
	}
	err = r.inboundNotifier.SendInboundMsgToSecurityGuard(Inbound, ticket, module[0].URL)
	if err != nil {
		return fmt.Errorf("发送信息到保安群失败: %v", err)
	}
	return nil
}
