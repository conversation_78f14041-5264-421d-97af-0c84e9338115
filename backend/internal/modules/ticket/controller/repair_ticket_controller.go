package controller

import (
	"backend/internal/modules/ticket/common"
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/service"
	"backend/response"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"go.temporal.io/sdk/client"
	"go.uber.org/zap"
)

// RepairTicketController 维修单控制器
type RepairTicketController struct {
	service service.RepairTicketService
}

// NewRepairTicketController 创建维修单控制器
func NewRepairTicketController(service service.RepairTicketService) *RepairTicketController {
	return &RepairTicketController{service: service}
}

// RegisterRoutes 注册路由
func (c *RepairTicketController) RegisterRoutes(router *gin.RouterGroup) {
	repairRouter := router.Group("/repair-tickets")
	{
		// 创建维修单
		repairRouter.POST("", c.CreateRepairTicket)

		// 获取维修单列表
		repairRouter.GET("", c.ListRepairTickets)

		// 获取维修单详情
		repairRouter.GET("/:id", c.GetRepairTicket)

		// 更新维修单
		repairRouter.PUT("/:id", c.UpdateRepairTicket)

		// 工程师接单(重定向到transition接口)
		repairRouter.PUT("/:id/take", c.TakeRepairTicketRedirect)

		// 获取可接单的维修单列表
		repairRouter.GET("/available", c.ListAvailableRepairTickets)

		// 状态转换 - 统一接口
		repairRouter.PUT("/:id/transition", c.TransitionRepairTicket)

		// 申请备件
		//repairRouter.POST("/:id/spare-requests", c.RequestSpare)

		// 申请备机
		//repairRouter.POST("/:id/spare-machine-requests", c.RequestSpareMachine)

		// 获取维修单历史记录
		repairRouter.GET("/:id/history", c.GetRepairTicketHistory)
	}
}

// CreateRepairTicket 创建维修单
// @Summary 创建维修单
// @Description 创建新的维修单
// @Tags 工单管理-维修单
// @Accept json
// @Produce json
// @Param repair_ticket body model.RepairTicketCreateRequest true "维修单信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/repair-tickets [post]
func (c *RepairTicketController) CreateRepairTicket(ctx *gin.Context) {
	var request model.RepairTicketCreateRequest

	if err := ctx.ShouldBindJSON(&request); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 获取日志记录器
	logger := GetLoggerFromContext(ctx)
	if logger != nil {
		logger.Info("尝试创建维修单",
			zap.Uint("faultTicketID", request.FaultTicketID),
			zap.String("repairType", request.RepairType))
	}

	// 验证维修类型是否有效
	validTypes := map[string]bool{
		"restart":          true,
		"hardware_fix":     true,
		"hardware_replace": true, // 会被服务层自动转换为hardware_fix
		"board_repair":     true,
		"cold_migration":   true,
		"software_fix":     true, // 会被服务层自动转换为restart
		// 添加额外的变体以提高兼容性
		"hardware": true,
		"software": true,
		"reboot":   true,
	}

	// 标准化输入
	normalizedType := strings.ToLower(strings.TrimSpace(request.RepairType))

	if !validTypes[normalizedType] {
		// 如果不是预定义类型，尝试基于关键词识别
		if strings.Contains(normalizedType, "hardware") {
			if logger != nil {
				logger.Info("使用关键词匹配识别类型",
					zap.String("原始类型", request.RepairType),
					zap.String("标准化为", "hardware_fix"))
			}
			request.RepairType = "hardware_fix"
		} else if strings.Contains(normalizedType, "software") ||
			strings.Contains(normalizedType, "restart") ||
			strings.Contains(normalizedType, "reboot") {
			if logger != nil {
				logger.Info("使用关键词匹配识别类型",
					zap.String("原始类型", request.RepairType),
					zap.String("标准化为", "restart"))
			}
			request.RepairType = "restart"
		} else {
			if logger != nil {
				logger.Warn("未识别的维修类型",
					zap.String("输入类型", request.RepairType),
					zap.String("将使用默认值", "hardware_fix"))
			}

			// 使用最通用的类型以减少错误
			request.RepairType = "hardware_fix"
		}
	}

	repairTicket, err := c.service.CreateRepairTicket(ctx, request.FaultTicketID, request.RepairType)
	if err != nil {
		errMsg := fmt.Sprintf("创建维修单失败: %s", err.Error())
		if logger != nil {
			logger.Error("创建维修单失败",
				zap.Error(err),
				zap.Uint("faultTicketID", request.FaultTicketID),
				zap.String("repairType", request.RepairType))
		}

		// 检查是否是数据截断错误
		if strings.Contains(err.Error(), "Data truncated") {
			// 最后尝试使用硬编码的安全值
			if logger != nil {
				logger.Info("尝试使用硬编码的安全值", zap.String("repairType", "hardware_fix"))
			}

			repairTicket, err = c.service.CreateRepairTicket(ctx, request.FaultTicketID, "hardware_fix")
			if err != nil {
				response.Fail(ctx, http.StatusInternalServerError, fmt.Sprintf(
					"即使使用硬编码安全值仍然创建失败: %s", err.Error()))
				return
			}

			// 成功使用安全值创建
			response.Success(ctx, gin.H{
				"id":        repairTicket.ID,
				"ticket_no": repairTicket.TicketNo,
				"note":      "使用默认维修类型'hardware_fix'创建成功",
			}, "创建维修单成功")
			return
		}

		response.Fail(ctx, http.StatusInternalServerError, errMsg)
		return
	}

	response.Success(ctx, gin.H{"id": repairTicket.ID, "ticket_no": repairTicket.TicketNo}, "创建维修单成功")
}

// GetRepairTicket 获取维修单
// @Summary 获取维修单详情
// @Description 根据ID获取维修单详情
// @Tags 工单管理-维修单
// @Accept json
// @Produce json
// @Param id path int true "维修单ID"
// @Success 200 {object} response.ResponseStruct{data=model.RepairTicket}
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/repair-tickets/{id} [get]
func (c *RepairTicketController) GetRepairTicket(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	repairTicket, err := c.service.GetRepairTicketByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取维修单失败: "+err.Error())
		return
	}

	response.Success(ctx, repairTicket, "获取维修单成功")
}

// UpdateRepairTicket 更新维修单
// @Summary 更新维修单
// @Description 更新维修单信息
// @Tags 工单管理-维修单
// @Accept json
// @Produce json
// @Param id path int true "维修单ID"
// @Param repair_ticket body model.RepairTicket true "维修单信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/repair-tickets/{id} [put]
func (c *RepairTicketController) UpdateRepairTicket(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	// 先获取现有记录
	existingTicket, err := c.service.GetRepairTicketByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取维修单失败: "+err.Error())
		return
	}

	// 解析用户提交的更新
	var updateData map[string]interface{}
	if err := ctx.ShouldBindJSON(&updateData); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 检查是否更新了接单人信息
	var engineerNameUpdated bool
	var originalEngineerName = existingTicket.AssignedEngineerName
	var newEngineerName string

	// 根据用户提交的数据更新指定字段
	if v, ok := updateData["status"]; ok {
		if status, ok := v.(string); ok {
			existingTicket.Status = status
		} else {
			response.Fail(ctx, http.StatusBadRequest, "状态字段类型错误")
			return
		}
	}
	if v, ok := updateData["repair_type"]; ok {
		if repairType, ok := v.(string); ok {
			existingTicket.RepairType = repairType
		} else {
			response.Fail(ctx, http.StatusBadRequest, "维修类型字段类型错误")
			return
		}
	}

	if v, ok := updateData["assigned_engineer_name"]; ok {
		if engineerName, ok := v.(string); ok {
			newEngineerName = engineerName
			if originalEngineerName != newEngineerName {
				engineerNameUpdated = true
				existingTicket.AssignedEngineerName = newEngineerName
			}
		} else {
			response.Fail(ctx, http.StatusBadRequest, "工程师名称字段类型错误")
			return
		}
	}

	if v, ok := updateData["solution"]; ok {
		if solution, ok := v.(string); ok {
			existingTicket.Solution = solution
		} else {
			response.Fail(ctx, http.StatusBadRequest, "解决方案字段类型错误")
			return
		}
	}
	if v, ok := updateData["repair_result"]; ok {
		if repairResult, ok := v.(string); ok {
			existingTicket.RepairResult = repairResult
		} else {
			response.Fail(ctx, http.StatusBadRequest, "维修结果字段类型错误")
			return
		}
	}
	if v, ok := updateData["verification_result"]; ok {
		if verificationResult, ok := v.(string); ok {
			existingTicket.VerificationResult = verificationResult
		} else {
			response.Fail(ctx, http.StatusBadRequest, "验证结果字段类型错误")
			return
		}
	}
	if v, ok := updateData["verification_description"]; ok {
		if verificationDescription, ok := v.(string); ok {
			existingTicket.VerificationDescription = verificationDescription
		} else {
			response.Fail(ctx, http.StatusBadRequest, "验证描述字段类型错误")
			return
		}
	}
	// 根据需要添加其他可更新字段

	// 更新维修单
	if err := c.service.UpdateRepairTicket(ctx, existingTicket); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新维修单失败: "+err.Error())
		return
	}

	// 如果更新了接单人信息，记录到历史记录表
	if engineerNameUpdated {
		// 获取当前操作人信息
		operatorID, operatorName, err := common.GetCurrentUserInfo(ctx)
		if err != nil {
			// 记录日志但不中断操作
			log.Printf("记录接单人变更历史失败，无法获取操作人信息: %v", err)
		} else {
			// 使用新方法记录工程师变更历史
			if err := c.service.RecordEngineerChange(ctx, uint(id), originalEngineerName, newEngineerName, operatorID, operatorName); err != nil {
				// 记录日志但不中断操作
				log.Printf("记录接单人变更历史失败: %v", err)
			} else {
				// 记录成功，创建工作流信号
				tempClient := GetTemporalClientFromContext(ctx)
				if tempClient != nil {
					// 触发工作流信号，将接单人变更记录到历史记录
					workflowID := fmt.Sprintf("%s%d", common.RepairTicketWorkflowIDPrefix, id)
					signal := common.WorkflowControlSignal{
						Stage:        "engineer_update",
						OperatorID:   operatorID,
						OperatorName: operatorName,
						Comments:     fmt.Sprintf("接单人信息从 %s 更新为 %s", originalEngineerName, newEngineerName),
						Data: map[string]interface{}{
							"old_engineer_name": originalEngineerName,
							"new_engineer_name": newEngineerName,
						},
					}
					if err := tempClient.SignalWorkflow(ctx, workflowID, "", common.WorkflowControlSignalName, signal); err != nil {
						log.Printf("发送接单人变更工作流信号失败: %v", err)
					}
				}
			}
		}
	}

	response.Success(ctx, nil, "更新维修单成功")
}

// UpdateRepairTicketStatus 更新维修单状态
// @Summary 更新维修单状态
// @Description 更新维修单状态
// @Tags 工单管理-维修单
// @Accept json
// @Produce json
// @Param id path int true "维修单ID"
// @Param status body model.RepairTicketStatusUpdate true "状态信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/repair-tickets/{id}/status [put]
func (c *RepairTicketController) UpdateRepairTicketStatus(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var statusUpdate model.RepairTicketStatusUpdate

	if err := ctx.ShouldBindJSON(&statusUpdate); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := c.service.UpdateRepairTicketStatus(ctx, uint(id), statusUpdate.Status, statusUpdate.OperatorID, statusUpdate.OperatorName); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新状态失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新状态成功")
}

// AssignRepairTicket 分配维修单
// @Summary 分配维修单
// @Description 将维修单分配给工程师
// @Tags 工单管理-维修单
// @Accept json
// @Produce json
// @Param id path int true "维修单ID"
// @Param engineer body model.RepairTicketAssignment true "工程师信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/repair-tickets/{id}/assign [put]
func (c *RepairTicketController) AssignRepairTicket(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var assignRequest model.RepairTicketAssignment

	if err := ctx.ShouldBindJSON(&assignRequest); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := c.service.AssignRepairTicket(ctx, uint(id), assignRequest.EngineerID); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "分配维修单失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "分配维修单成功")
}

// StartRepair 开始维修
// @Summary 开始维修
// @Description 开始执行维修工作
// @Tags 工单管理-维修单
// @Accept json
// @Produce json
// @Param id path int true "维修单ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/repair-tickets/{id}/start [put]
func (c *RepairTicketController) StartRepair(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	if err := c.service.StartRepair(ctx, uint(id)); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "开始维修失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "开始维修成功")
}

// ArriveOnSite 工程师到达现场
// @Summary 工程师到达现场
// @Description 记录工程师到达现场时间
// @Tags 工单管理-维修单
// @Accept json
// @Produce json
// @Param id path int true "维修单ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/repair-tickets/{id}/arrive [put]
func (c *RepairTicketController) ArriveOnSite(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	if err := c.service.ArriveOnSite(ctx, uint(id)); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "记录到达时间失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "记录到达时间成功")
}

// StartHardwareReplace 开始更换硬件
// @Summary 开始更换硬件
// @Description 记录开始更换硬件时间
// @Tags 工单管理-维修单
// @Accept json
// @Produce json
// @Param id path int true "维修单ID"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/repair-tickets/{id}/hardware-replace/start [put]
func (c *RepairTicketController) StartHardwareReplace(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	if err := c.service.StartHardwareReplace(ctx, uint(id)); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "记录硬件更换开始时间失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "记录硬件更换开始时间成功")
}

// CompleteHardwareReplace 完成硬件更换
// @Summary 完成硬件更换
// @Description 记录硬件更换完成
// @Tags 工单管理-维修单
// @Accept json
// @Produce json
// @Param id path int true "维修单ID"
// @Param data body model.HardwareReplaceRequest true "备件信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/repair-tickets/{id}/hardware-replace/complete [put]
func (c *RepairTicketController) CompleteHardwareReplace(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var request model.HardwareReplaceRequest

	if err := ctx.ShouldBindJSON(&request); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := c.service.CompleteHardwareReplace(ctx, uint(id), request.SpareID); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "记录硬件更换完成失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "记录硬件更换完成成功")
}

// CompleteRepair 完成维修
// @Summary 完成维修
// @Description 记录维修完成
// @Tags 工单管理-维修单
// @Accept json
// @Produce json
// @Param id path int true "维修单ID"
// @Param data body service.RepairResult true "维修结果"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/repair-tickets/{id}/complete [put]
func (c *RepairTicketController) CompleteRepair(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var request service.RepairResult

	if err := ctx.ShouldBindJSON(&request); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	result := &service.RepairResult{
		RepairResult: request.RepairResult,
		Solution:     request.Solution,
		RepairSteps:  request.RepairSteps,
	}

	if err := c.service.CompleteRepair(ctx, uint(id), result); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "完成维修失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "完成维修成功")
}

// ListRepairTickets 获取维修单列表
// @Summary 获取维修单列表
// @Description 分页获取维修单列表，支持筛选
// @Tags 工单管理-维修单
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Param query query string false "搜索关键词"
// @Param status query string false "状态筛选"
// @Param engineer_id query int false "工程师ID筛选"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/repair-tickets [get]
func (c *RepairTicketController) ListRepairTickets(ctx *gin.Context) {
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}
	query := ctx.DefaultQuery("query", "")
	status := ctx.DefaultQuery("status", "")
	engineerIDStr := ctx.DefaultQuery("engineer_id", "0")
	engineerID, err := strconv.ParseUint(engineerIDStr, 10, 32)
	if err != nil {
		// 解析错误时使用默认值0
		engineerID = 0
	}

	repairTickets, total, err := c.service.ListRepairTickets(ctx, page, pageSize, query, status, uint(engineerID))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取维修单列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"list":  repairTickets,
		"total": total,
	}, "获取维修单列表成功")
}

// RequestSpare 申请备件
// @Summary 申请备件
// @Description 为维修工单申请备件
// @Tags 工单管理-维修单
// @Accept json
// @Produce json
// @Param id path int true "维修单ID"
// @Param data body model.SpareRequest true "备件信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/repair-tickets/{id}/spare-request [post]
func (c *RepairTicketController) RequestSpare(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var request model.SpareRequest

	if err := ctx.ShouldBindJSON(&request); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	result, err := c.service.RequestSpare(ctx, uint(id), request.ProductID, request.Quantity)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "申请备件失败: "+err.Error())
		return
	}

	response.Success(ctx, result, "申请备件成功")
}

// RequestSpareMachine 申请备机
// @Summary 申请备机
// @Description 为维修工单申请备机
// @Tags 工单管理-维修单
// @Accept json
// @Produce json
// @Param id path int true "维修单ID"
// @Param data body model.SpareMachineRequest true "备机模板信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/repair-tickets/{id}/spare-machine-request [post]
func (c *RepairTicketController) RequestSpareMachine(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var request model.SpareMachineRequest

	if err := ctx.ShouldBindJSON(&request); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	result, err := c.service.RequestSpareMachine(ctx, uint(id), request.TemplateID)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "申请备机失败: "+err.Error())
		return
	}

	response.Success(ctx, result, "申请备机成功")
}

// TransitionRepairTicket 状态转换与工作流阶段触发
// @Summary 状态转换与触发工作流
// @Description 统一的维修工单状态转换接口，支持所有工作流阶段
// @Tags 工单管理-维修单
// @Accept json
// @Produce json
// @Param id path int true "维修单ID"
// @Param transition body object true "转换信息"
// @Success 200 {object} response.ResponseStruct
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/repair-tickets/{id}/transition [put]
func (c *RepairTicketController) TransitionRepairTicket(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var transitionRequest struct {
		Stage    string                 `json:"stage" binding:"required"`  // 工作流阶段
		Status   string                 `json:"status" binding:"required"` // 新状态
		Comments string                 `json:"comments"`                  // 备注
		Data     map[string]interface{} `json:"data"`                      // 阶段相关数据
	}

	if err := ctx.ShouldBindJSON(&transitionRequest); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 获取当前操作人信息
	operatorID, operatorName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "未获取到用户信息: "+err.Error())
		return
	}

	// 获取当前工单
	ticket, err := c.service.GetRepairTicketByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取维修单失败: "+err.Error())
		return
	}

	// 验证状态转换是否合法
	if !isValidStatusTransition(ticket.Status, transitionRequest.Status) {
		response.Fail(ctx, http.StatusBadRequest,
			fmt.Sprintf("无效的状态转换: 从 %s 到 %s", ticket.Status, transitionRequest.Status))
		return
	}

	// 验证阶段转换是否合法
	isValid, message := validateStageTransition(ticket.Status, transitionRequest.Stage)
	if !isValid {
		response.Fail(ctx, http.StatusBadRequest, "当前状态下不能触发此阶段: "+message)
		return
	}

	// 根据不同的阶段执行不同的操作
	switch transitionRequest.Stage {
	case common.StageEngineerTake:
		// 处理工程师接单
		var engineerID = operatorID // 默认使用当前操作人

		// 如果请求数据中指定了工程师ID，则使用指定的ID
		if engIDFloat, ok := transitionRequest.Data["engineer_id"].(float64); ok {
			engineerID = uint(engIDFloat)
		}

		err = c.service.TakeRepairTicket(ctx, uint(id), engineerID)
		if err != nil {
			response.Fail(ctx, http.StatusInternalServerError, "接单失败: "+err.Error())
			return
		}

	case common.StageStartRepair:
		err = c.service.StartRepair(ctx, uint(id))
		if err != nil {
			response.Fail(ctx, http.StatusInternalServerError, "开始维修失败: "+err.Error())
			return
		}

	case common.StageArriveOnSite:
		err = c.service.ArriveOnSite(ctx, uint(id))
		if err != nil {
			response.Fail(ctx, http.StatusInternalServerError, "记录到达现场失败: "+err.Error())
			return
		}

	case common.StageStartHardwareReplace:
		if ticket.RepairType != "hardware_fix" {
			response.Fail(ctx, http.StatusBadRequest, "只有硬件更换类型的维修单可以执行此操作")
			return
		}

		err = c.service.StartHardwareReplace(ctx, uint(id))
		if err != nil {
			response.Fail(ctx, http.StatusInternalServerError, "开始硬件更换失败: "+err.Error())
			return
		}

	case common.StageCompleteHardwareReplace:
		if ticket.RepairType != "hardware_fix" {
			response.Fail(ctx, http.StatusBadRequest, "只有硬件更换类型的维修单可以执行此操作")
			return
		}

		var spareID uint
		if spareIDValue, ok := transitionRequest.Data["spare_id"].(float64); ok {
			spareID = uint(spareIDValue)
		}

		err = c.service.CompleteHardwareReplace(ctx, uint(id), spareID)
		if err != nil {
			response.Fail(ctx, http.StatusInternalServerError, "完成硬件更换失败: "+err.Error())
			return
		}

	case common.StageCompleteRepair:
		var repairResult service.RepairResult

		if resultData, ok := transitionRequest.Data["repair_result"].(string); ok {
			repairResult.RepairResult = resultData
		}

		if solution, ok := transitionRequest.Data["solution"].(string); ok {
			repairResult.Solution = solution
		}

		if steps, ok := transitionRequest.Data["repair_steps"].(string); ok {
			repairResult.RepairSteps = steps
		}

		err = c.service.CompleteRepair(ctx, uint(id), &repairResult)
		if err != nil {
			response.Fail(ctx, http.StatusInternalServerError, "完成维修失败: "+err.Error())
			return
		}
	}

	// 触发工作流信号
	temporalClient := GetTemporalClientFromContext(ctx)
	if temporalClient != nil {
		// 确保工作流ID格式一致
		workflowID := fmt.Sprintf("%s%d", common.RepairTicketWorkflowIDPrefix, id)

		// 增强用户名处理，确保使用真实姓名
		signalOperatorName := operatorName
		if signalOperatorName == "" || strings.HasPrefix(signalOperatorName, "用户") {
			// 尝试从上下文中的userInfo直接获取
			if userInfo, exists := ctx.Get("userInfo"); exists {
				if infoMap, ok := userInfo.(map[string]interface{}); ok {
					if realName, ok := infoMap["realName"].(string); ok && realName != "" {
						signalOperatorName = realName
						log.Printf("从上下文userInfo获取到用户真实姓名: %s\n", signalOperatorName)
					}
				}
			}
		}

		// 使用常量，确保与工作流中接收信号的名称一致
		signal := common.WorkflowControlSignal{
			Stage:        transitionRequest.Stage,
			OperatorID:   operatorID,
			OperatorName: signalOperatorName,
			Comments:     transitionRequest.Comments,
			Data:         transitionRequest.Data,
		}

		// 使用常量替换硬编码字符串
		err = temporalClient.SignalWorkflow(ctx, workflowID, "", common.WorkflowControlSignalName, signal)
		if err != nil {
			log.Printf("发送工作流信号失败: %v\n", err)
			// 信号发送失败不影响API返回，仅记录日志
		} else {
			log.Printf("成功发送工作流信号: workflowID=%s, signal=%s, operatorName=%s\n",
				workflowID, common.WorkflowControlSignalName, signalOperatorName)
		}
	}

	// 工作流触发成功后，更新状态（如果不是在上述阶段处理中已经更新过）
	if !isStatusUpdatedInStage(transitionRequest.Stage) {
		err = c.service.UpdateRepairTicketStatus(ctx, uint(id), transitionRequest.Status, operatorID, operatorName)
		if err != nil {
			response.Fail(ctx, http.StatusInternalServerError, "更新状态失败: "+err.Error())
			return
		}
	}

	response.Success(ctx, nil, "状态转换成功")
}

// isValidStatusTransition 检查状态转换是否合法
func isValidStatusTransition(from, to string) bool {
	validTransitions := map[string][]string{
		"waiting_authorization":      {"waiting_accept", "cancelled"},
		"waiting_accept":             {"assigned", "cancelled"},
		"assigned":                   {"in_progress", "cancelled"},
		"in_progress":                {"replacing_hardware", "restarting", "migrating", "waiting_verification", "failed", "cancelled"},
		"replacing_hardware":         {"hardware_replace_completed", "hardware_replace_failed", "in_progress", "failed", "cancelled"},
		"hardware_replace_completed": {"in_progress", "waiting_verification", "completed", "failed"},
		"hardware_replace_failed":    {"in_progress", "failed", "cancelled"},
		"restarting":                 {"restart_completed", "restart_failed", "in_progress", "failed", "cancelled"},
		"restart_completed":          {"in_progress", "waiting_verification", "completed", "failed"},
		"restart_failed":             {"in_progress", "failed", "cancelled"},
		"waiting_verification":       {"completed", "in_progress", "failed"},
		"completed":                  {},
		"failed":                     {"waiting_accept", "cancelled"},
		"cancelled":                  {},
	}

	transitions, ok := validTransitions[from]
	if !ok {
		return false
	}

	for _, validTo := range transitions {
		if validTo == to {
			return true
		}
	}

	return false
}

// validateStageTransition 验证阶段转换是否合法
func validateStageTransition(status, stage string) (bool, string) {
	validStages := map[string][]string{
		"waiting_authorization":      {common.StageAuthorize},
		"waiting_accept":             {common.StageEngineerTake},
		"assigned":                   {common.StageStartRepair},
		"in_progress":                {common.StageArriveOnSite, common.StageStartHardwareReplace, "start_restart", common.StageCompleteRepair},
		"replacing_hardware":         {common.StageCompleteHardwareReplace},
		"hardware_replace_completed": {common.StageCompleteRepair},
		"restarting":                 {"complete_restart"},
		"migrating":                  {"complete_migration"},
		"waiting_verification":       {common.StageVerify},
	}

	stages, ok := validStages[status]
	if !ok {
		return false, fmt.Sprintf("当前状态(%s)下没有有效的阶段转换", status)
	}

	for _, validStage := range stages {
		if validStage == stage {
			return true, ""
		}
	}

	return false, fmt.Sprintf("当前状态(%s)下不能触发阶段(%s)", status, stage)
}

// isStatusUpdatedInStage 检查状态是否在阶段处理中已经更新
func isStatusUpdatedInStage(stage string) bool {
	// 这些阶段在处理中会更新状态
	updatedInStage := map[string]bool{
		common.StageStartRepair:             true,
		common.StageStartHardwareReplace:    true,
		common.StageCompleteHardwareReplace: true,
		common.StageCompleteRepair:          true,
	}

	return updatedInStage[stage]
}

// GetTemporalClientFromContext 从上下文获取Temporal客户端
func GetTemporalClientFromContext(ctx *gin.Context) client.Client {
	if v, exists := ctx.Get("temporal_client"); exists {
		if client, ok := v.(client.Client); ok {
			return client
		}
	}
	return nil
}

// GetLoggerFromContext 从上下文获取日志器
func GetLoggerFromContext(ctx *gin.Context) *zap.Logger {
	if v, exists := ctx.Get("logger"); exists {
		if logger, ok := v.(*zap.Logger); ok {
			return logger
		}
	}
	return nil
}

// TakeRepairTicketRedirect 接单重定向API (更安全的实现，获取当前登录用户并进行接单)
func (c *RepairTicketController) TakeRepairTicketRedirect(ctx *gin.Context) {
	// 从路径参数中获取工单ID
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "无效的维修单ID"})
		return
	}

	// 使用common包提供的方法获取当前用户信息
	operatorID, operatorName, err := common.GetCurrentUserInfo(ctx)
	fmt.Println("operatorID", operatorID)
	fmt.Println("operatorName", operatorName)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "未授权，无法获取用户信息: " + err.Error()})
		return
	}

	// 构建临时客户端
	client := GetTemporalClientFromContext(ctx)
	if client == nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "无法创建工作流客户端"})
		return
	}

	// 创建工作流ID
	workflowID := fmt.Sprintf("repair_ticket_%d", id)

	// 给工作流发送信号
	err = client.SignalWorkflow(ctx, workflowID, "", common.WorkflowControlSignalName, common.WorkflowControlSignal{
		Stage:    common.StageEngineerTake,
		Comments: "工程师接单处理",
		Data: map[string]interface{}{
			"engineer_id":   operatorID,
			"engineer_name": operatorName,
		},
	})

	if err != nil {
		GetLoggerFromContext(ctx).Error("发送接单信号失败", zap.Error(err), zap.String("workflowID", workflowID))
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "发送工作流信号失败: " + err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "接单请求已发送",
		"data": gin.H{
			"ticket_id":     id,
			"engineer_id":   operatorID,
			"engineer_name": operatorName,
		},
	})
}

// ListAvailableRepairTickets 获取可接单的维修单列表
// @Summary 获取可接单的维修单列表
// @Description 获取状态为"waiting_accept"的维修单列表，供工程师主动接单
// @Tags 工单管理-维修单
// @Acc	ept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认10"
// @Success 200 {object} response.ResponseStruct{data=[]model.RepairTicket}
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/repair-tickets/available [get]
func (c *RepairTicketController) ListAvailableRepairTickets(ctx *gin.Context) {
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}

	tickets, total, err := c.service.ListAvailableRepairTickets(ctx, page, pageSize)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取维修单列表失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{
		"items": tickets,
		"total": total,
		"page":  page,
		"size":  pageSize,
	}, "获取维修单列表成功")
}

// GetRepairTicketHistory 获取维修单历史记录
// @Summary 获取维修单历史记录
// @Description 获取维修单的状态变更历史记录
// @Tags 工单管理-维修单
// @Accept json
// @Produce json
// @Param id path int true "维修单ID"
// @Success 200 {object} response.ResponseStruct{data=[]model.RepairTicketStatusHistory}
// @Failure 400 {object} response.ResponseStruct
// @Router /ticket/repair-tickets/{id}/history [get]
func (c *RepairTicketController) GetRepairTicketHistory(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	// 首先检查维修单是否存在
	_, err = c.service.GetRepairTicketByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusNotFound, "未找到维修单: "+err.Error())
		return
	}

	histories, err := c.service.GetRepairTicketHistory(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取维修单历史记录失败: "+err.Error())
		return
	}

	response.Success(ctx, histories, "获取维修单历史记录成功")
}
