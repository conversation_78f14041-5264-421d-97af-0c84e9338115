package controller

import (
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/service"
	"backend/response"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"backend/internal/modules/ticket/common"

	"github.com/gin-gonic/gin"
)

// EntryTicketController 入室单控制器
type EntryTicketController struct {
	service      service.EntryTicketService
	entryService service.EntryPersonService
}

// NewEntryTicketController 创建入室单控制器
func NewEntryTicketController(service service.EntryTicketService, entryService service.EntryPersonService) *EntryTicketController {
	return &EntryTicketController{service: service, entryService: entryService}
}

// RegisterRoutes 注册路由
func (c *EntryTicketController) RegisterRoutes(router *gin.RouterGroup) {
	entryTicketRouter := router.Group("/entry-tickets")
	{
		entryTicketRouter.POST("", c.<PERSON>reateEntryTicket)
		entryTicketRouter.GET("/:id", c.GetEntryTicket)
		entryTicketRouter.PUT("/:id", c.UpdateEntryTicket)
		entryTicketRouter.PUT("/:id/close", c.CloseEntryTicket)
		entryTicketRouter.GET("", c.ListEntryTickets)
		entryTicketRouter.GET("/:id/history", c.GetEntryTicketStatusHistory)
		entryTicketRouter.PUT("/:id/transition", c.TransitionEntryTicket)
		entryTicketRouter.PUT("/:id/fields", c.UpdateEntryTicketFields)
	}

	entryPersonRouter := router.Group("/entry-tickets/person")
	{
		entryPersonRouter.POST("create", c.CreateEntryPerson)
		entryPersonRouter.GET("", c.ListEntryPerson)
		entryPersonRouter.PUT("/:id/fields", c.UpdateEntryPersonFields)
		//entryTicketRouter.PUT("/:id", c.UpdateEntryPerson)
	}
}

// CreateEntryTicket 创建入室单
func (c *EntryTicketController) CreateEntryTicket(ctx *gin.Context) {
	var requestBody struct {
		Persons        []model.EntryPersonRequest `json:"persons" binding:"required"`
		EntryStartTime string                     `json:"entry_start_time" binding:"required"`
		EntryEndTime   string                     `json:"entry_end_time" binding:"required"`
		Reason         string                     `json:"reason" binding:"required"`
		CreationTime   string                     `json:"creationTime"`
		CarryEquipment string                     `json:"carryEquipment"`
		IsOutSource    *bool                      `json:"isOutSource"`
		//RequireApproval    *bool  `json:"require_approval"`
	}

	if err := ctx.ShouldBindJSON(&requestBody); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 获取当前用户信息
	currentUserID, currentUserName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "获取用户信息失败: "+err.Error())
		return
	}

	creationTime := time.Now()
	if requestBody.CreationTime != "" {
		parsedTime, err := time.Parse(time.RFC3339, requestBody.CreationTime)
		if err == nil {
			creationTime = parsedTime
		}
	}

	persons, err := json.Marshal(requestBody.Persons)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "序列化人员信息失败: "+err.Error())
		return
	}
	entryStartTime, err := time.Parse(time.RFC3339, requestBody.EntryStartTime)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "入室开始时间格式错误: "+err.Error())
		return
	}

	entryEndTime, err := time.Parse(time.RFC3339, requestBody.EntryEndTime)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "入室结束时间格式错误: "+err.Error())
		return
	}

	start := entryStartTime.Add(-time.Hour * 8)
	end := entryEndTime.Add(-time.Hour * 8)

	// 创建一个只包含必要信息的入室单对象
	entryTicket := model.EntryTicket{
		EntryPersons: string(persons),
		CreationTime: creationTime,
		Reason:       requestBody.Reason,
		// 设置申请人信息为当前登录用户
		ApplicantID:    currentUserID,
		ApplicantName:  currentUserName,
		EntryStartTime: &start,
		EntryEndTime:   &end,

		IsOutSource: requestBody.IsOutSource,
	}

	if requestBody.CarryEquipment != "" {
		entryTicket.IsCarryEquipment = true
		entryTicket.CarryEquipment = requestBody.CarryEquipment
	}

	if err := c.service.CreateEntryTicket(ctx, &entryTicket); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建入室单失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{"id": entryTicket.ID, "ticket_no": entryTicket.TicketNo}, "创建入室单成功")
}

// GetEntryTicket 获取入室单
func (c *EntryTicketController) GetEntryTicket(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	entryTicket, err := c.service.GetEntryTicketByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取入室单失败: "+err.Error())
		return
	}

	// 将完整的工单对象转换为DTO
	//faultTicketDTO := repository.ConvertToDTO(faultTicket)
	response.Success(ctx, entryTicket, "获取入室单成功")
}

// UpdateEntryTicket 更新入室单
func (c *EntryTicketController) UpdateEntryTicket(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var entryTicket model.EntryTicket
	if err := ctx.ShouldBindJSON(&entryTicket); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	entryTicket.ID = uint(id)

	if err := c.service.UpdateEntryTicket(ctx, &entryTicket); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新入室单失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新入室单成功")
}

// CloseEntryTicket 关闭入室单
func (c *EntryTicketController) CloseEntryTicket(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var closeRequest struct {
		CreationTime *string `json:"creation_time"`
	}

	if err := ctx.ShouldBindJSON(&closeRequest); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 更新入室单状态为已完成
	// 先获取入室单
	ticket, err := c.service.GetEntryTicketByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取入室单失败: "+err.Error())
		return
	}

	// 处理创建时间
	if closeRequest.CreationTime != nil && *closeRequest.CreationTime != "" {
		creationTime, err := time.Parse(time.RFC3339, *closeRequest.CreationTime)
		if err == nil {
			ticket.CreationTime = creationTime
		}
	}

	// 准备更新字段
	updateFields := map[string]interface{}{
		"status":     "completed",
		"close_time": time.Now(),
	}

	// 关闭入室单
	if err := c.service.UpdateEntryTicketFields(ctx, uint(id), updateFields); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "关闭入室单失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "关闭入室单成功")
}

// ListEntryTickets 获取入室单列表
func (c *EntryTicketController) ListEntryTickets(ctx *gin.Context) {
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}

	query := ctx.DefaultQuery("query", "")

	// 获取状态参数，支持单个状态和多状态数组
	var statusValues []string
	status := ctx.DefaultQuery("status", "")
	if status != "" {
		statusValues = append(statusValues, status)
	}

	// 获取status[]参数
	statusArray := ctx.QueryArray("status[]")
	if len(statusArray) > 0 {
		statusValues = append(statusValues, statusArray...)
	}

	//priority := ctx.DefaultQuery("priority", "")

	// 新增查询参数
	//title := ctx.DefaultQuery("title", "")
	//resourceIdentifier := ctx.DefaultQuery("resource_identifier", "")
	//faultType := ctx.DefaultQuery("faultType", "")
	//faultDetailType := ctx.DefaultQuery("fault_detail_type", "")
	reporterName := ctx.DefaultQuery("applicantName", "")
	//assignedTo := ctx.DefaultQuery("assignedTo", "")
	//var creationTimeRange string

	// 处理创建时间范围
	var startTime, endTime *time.Time

	// 获取开始时间参数
	creationTimeStart := ctx.DefaultQuery("creationTimeStart", "")
	if creationTimeStart != "" {
		parsedTime, err := time.Parse(time.RFC3339, creationTimeStart)
		if err == nil {
			startTime = &parsedTime
		} else {
			// 尝试其他格式
			parsedTime, err = time.Parse("2006-01-02 15:04:05", creationTimeStart)
			if err == nil {
				startTime = &parsedTime
			}
		}
	}

	// 获取结束时间参数
	creationTimeEnd := ctx.DefaultQuery("creationTimeEnd", "")
	if creationTimeEnd != "" {
		parsedTime, err := time.Parse(time.RFC3339, creationTimeEnd)
		if err == nil {
			endTime = &parsedTime
		} else {
			// 尝试其他格式
			parsedTime, err = time.Parse("2006-01-02 15:04:05", creationTimeEnd)
			if err == nil {
				endTime = &parsedTime
			}
		}
	}

	//// 为了兼容性，也处理creationTimeRange参数
	//creationTimeRange = ctx.DefaultQuery("creationTimeRange", "")
	//if creationTimeRange != "" && startTime == nil && endTime == nil {
	//	timeRanges := strings.Split(creationTimeRange, ",")
	//	if len(timeRanges) == 2 {
	//		startTimeStr := strings.TrimSpace(timeRanges[0])
	//		endTimeStr := strings.TrimSpace(timeRanges[1])
	//
	//		if startTimeStr != "" && startTime == nil {
	//			parsedTime, err := time.Parse("2006-01-02 15:04:05", startTimeStr)
	//			if err == nil {
	//				startTime = &parsedTime
	//			}
	//		}
	//
	//		if endTimeStr != "" && endTime == nil {
	//			parsedTime, err := time.Parse("2006-01-02 15:04:05", endTimeStr)
	//			if err == nil {
	//				endTime = &parsedTime
	//			}
	//		}
	//	}
	//}

	// 构建查询条件
	filters := map[string]interface{}{
		"query":        query,
		"status":       statusValues,
		"reporterName": reporterName,
		"startTime":    startTime,
		"endTime":      endTime,
	}

	entryTickets, total, err := c.service.ListEntryTickets(ctx, page, pageSize, filters)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取入室单列表失败: "+err.Error())
		return
	}

	// 将完整的工单列表转换为DTO列表
	//faultTicketDTOs := repository.ConvertToDTOList(faultTickets)

	response.Success(ctx, gin.H{
		"list":  entryTickets,
		"total": total,
	}, "获取入室单列表成功")
}

// GetEntryTicketStatusHistory 获取入室单状态历史
func (c *EntryTicketController) GetEntryTicketStatusHistory(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	history, err := c.service.GetEntryTicketStatusHistory(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取状态历史失败: "+err.Error())
		return
	}

	response.Success(ctx, history, "获取状态历史成功")
}

// TransitionEntryTicket 状态转换与工作流阶段触发
func (c *EntryTicketController) TransitionEntryTicket(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	var transitionRequest struct {
		Stage    string                 `json:"stage" binding:"required"`  // 工作流阶段
		Status   string                 `json:"status" binding:"required"` // 新状态
		Comments string                 `json:"comments"`                  // 备注
		Data     map[string]interface{} `json:"data"`                      // 阶段相关数据
	}

	if err := ctx.ShouldBindJSON(&transitionRequest); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 获取当前操作人信息
	operatorID, operatorName, err := common.GetCurrentUserInfo(ctx)
	if err != nil {
		response.Fail(ctx, http.StatusUnauthorized, "未获取到用户信息: "+err.Error())
		return
	}

	// 获取当前工单
	ticket, err := c.service.GetEntryTicketByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取入室单失败: "+err.Error())
		return
	}

	// 验证状态转换是否合法
	if !isValidEntryStatusTransition(ticket.Status, transitionRequest.Status) {
		response.Fail(ctx, http.StatusBadRequest,
			fmt.Sprintf("无效的状态转换: 从 %s 到 %s", ticket.Status, transitionRequest.Status))
		return
	}

	// 验证阶段转换是否合法
	isValid, message := common.ValidateEntryStageTransition(ticket.Status, transitionRequest.Stage)
	if !isValid {
		response.Fail(ctx, http.StatusBadRequest, "当前状态下不能触发此阶段: "+message)
		return
	}

	// 根据不同的阶段创建相应的业务记录
	switch transitionRequest.Stage {
	case "customer_approval":
		// 创建客户审批记录
		approval := &model.EntryApproval{
			TicketID:     uint(id),
			TicketNo:     ticket.TicketNo,
			Status:       "",
			ResponseTime: time.Now(),
			Comments:     transitionRequest.Comments,
			CustomerID:   operatorID,
			CustomerName: operatorName,
		}

		if transitionRequest.Data != nil {
			if status, ok := transitionRequest.Data["status"].(string); ok {
				approval.Status = status
			} else {
				response.Fail(ctx, http.StatusBadRequest, "审批数据获取不到: "+err.Error())
				return
			}
		}

		if err := c.service.CreateEntryApproval(ctx, approval); err != nil {
			response.Fail(ctx, http.StatusInternalServerError, "创建客户审批记录失败: "+err.Error())
			return
		}

		// 如果审批通过，将RequireApproval设置为false
		if approval.Status == "approved" {
			// 更新票据RequireApproval字段以及客户审批时间
			updateData := map[string]interface{}{
				//"require_approval":       false,
				"approval_time": approval.ResponseTime,
			}

			if err := c.service.UpdateEntryTicketFields(ctx, uint(id), updateData); err != nil {
				response.Fail(ctx, http.StatusInternalServerError, "更新票据审批状态失败: "+err.Error())
				return
			}
		}

	case common.StageSecondApproval:
		approval, err := c.service.GetApprovalById(ctx, uint(id))
		if err != nil {
			fmt.Println(approval)
			response.Fail(ctx, http.StatusBadRequest, "没有第一次审批数据: "+err.Error())
			return
		}

		if transitionRequest.Data != nil {
			if status, ok := transitionRequest.Data["status"].(string); ok {
				approval.Status = status
			} else {
				response.Fail(ctx, http.StatusBadRequest, "二次审批数据获取不到: "+err.Error())
				return
			}

			prevName := approval.CustomerName
			approval.CustomerName = strings.Join([]string{prevName, operatorName}, ",")

			preComment := approval.Comments
			approval.Comments = strings.Join([]string{preComment, transitionRequest.Comments}, ",")

		}
		if err := c.service.UpdateApproval(ctx, approval); err != nil {
			response.Fail(ctx, http.StatusInternalServerError, "更新审批记录失败: "+err.Error())
			return
		}
	}

	// 先触发工作流，确保工作流可以正常工作
	err = c.service.TriggerWorkflowStage(
		ctx,
		uint(id),
		transitionRequest.Stage,
		operatorID,
		operatorName,
		transitionRequest.Comments,
		transitionRequest.Data,
	)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "触发工作流失败: "+err.Error())
		return
	}

	// 工作流触发成功后，再更新状态
	if err := c.service.UpdateEntryTicketStatus(ctx, uint(id), transitionRequest.Status, operatorID, operatorName); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新状态失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "状态转换成功")
}

// UpdateEntryTicketFields 更新入室单指定字段
func (c *EntryTicketController) UpdateEntryTicketFields(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	// 获取原始工单信息，检查工单是否存在
	_, err = c.service.GetEntryTicketByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取入室单失败: "+err.Error())
		return
	}

	// 解析请求体中的字段
	var updateFields map[string]interface{}
	if err := ctx.ShouldBindJSON(&updateFields); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 验证状态字段
	if status, exists := updateFields["status"]; exists {
		statusStr, ok := status.(string)
		if !ok || !validTicketStatus(statusStr) {
			delete(updateFields, "status")
		}
	}

	// 如果没有有效的更新字段，则返回成功但不执行更新
	if len(updateFields) == 0 {
		response.Success(ctx, nil, "无有效更新字段")
		return
	}

	// 调用服务更新字段
	if err := c.service.UpdateEntryTicketFields(ctx, uint(id), updateFields); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新入室单字段失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新入室单字段成功")
}

// 辅助函数：检查状态字符串是否有效
func validTicketStatus(status string) bool {
	validStatuses := []string{
		"waiting_approval", "completed", "cancelled",
	}

	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// ListEntryPerson 获取入室人员列表
func (c *EntryTicketController) ListEntryPerson(ctx *gin.Context) {
	page, err := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的页码: "+err.Error())
		return
	}
	pageSize, err := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的每页数量: "+err.Error())
		return
	}
	//query := ctx.DefaultQuery("query", "")
	//
	//// 获取状态参数，支持单个状态和多状态数组
	//var statusValues []string
	//status := ctx.DefaultQuery("status", "")
	//if status != "" {
	//	statusValues = append(statusValues, status)
	//}
	//
	//// 获取status[]参数
	//statusArray := ctx.QueryArray("status[]")
	//if len(statusArray) > 0 {
	//	statusValues = append(statusValues, statusArray...)
	//}

	//priority := ctx.DefaultQuery("priority", "")

	// 新增查询参数
	//title := ctx.DefaultQuery("title", "")
	//resourceIdentifier := ctx.DefaultQuery("resource_identifier", "")
	//faultType := ctx.DefaultQuery("faultType", "")
	//faultDetailType := ctx.DefaultQuery("fault_detail_type", "")
	//reporterName := ctx.DefaultQuery("applicantName", "")
	//assignedTo := ctx.DefaultQuery("assignedTo", "")
	//var creationTimeRange string

	// 处理创建时间范围
	//var startTime, endTime *time.Time
	//
	//// 获取开始时间参数
	//creationTimeStart := ctx.DefaultQuery("creationTimeStart", "")
	//if creationTimeStart != "" {
	//	parsedTime, err := time.Parse(time.RFC3339, creationTimeStart)
	//	if err == nil {
	//		startTime = &parsedTime
	//	} else {
	//		// 尝试其他格式
	//		parsedTime, err = time.Parse("2006-01-02 15:04:05", creationTimeStart)
	//		if err == nil {
	//			startTime = &parsedTime
	//		}
	//	}
	//}
	//
	//// 获取结束时间参数
	//creationTimeEnd := ctx.DefaultQuery("creationTimeEnd", "")
	//if creationTimeEnd != "" {
	//	parsedTime, err := time.Parse(time.RFC3339, creationTimeEnd)
	//	if err == nil {
	//		endTime = &parsedTime
	//	} else {
	//		// 尝试其他格式
	//		parsedTime, err = time.Parse("2006-01-02 15:04:05", creationTimeEnd)
	//		if err == nil {
	//			endTime = &parsedTime
	//		}
	//	}
	//}

	//// 为了兼容性，也处理creationTimeRange参数
	//creationTimeRange = ctx.DefaultQuery("creationTimeRange", "")
	//if creationTimeRange != "" && startTime == nil && endTime == nil {
	//	timeRanges := strings.Split(creationTimeRange, ",")
	//	if len(timeRanges) == 2 {
	//		startTimeStr := strings.TrimSpace(timeRanges[0])
	//		endTimeStr := strings.TrimSpace(timeRanges[1])
	//
	//		if startTimeStr != "" && startTime == nil {
	//			parsedTime, err := time.Parse("2006-01-02 15:04:05", startTimeStr)
	//			if err == nil {
	//				startTime = &parsedTime
	//			}
	//		}
	//
	//		if endTimeStr != "" && endTime == nil {
	//			parsedTime, err := time.Parse("2006-01-02 15:04:05", endTimeStr)
	//			if err == nil {
	//				endTime = &parsedTime
	//			}
	//		}
	//	}
	//}

	// 构建查询条件
	filters := map[string]interface{}{
		//"query":        query,
		//"status":       statusValues,
		//"reporterName": reporterName,
		//"startTime":    startTime,
		//"endTime":      endTime,
		"time": time.Now(),
	}

	entryTickets, total, err := c.entryService.ListEntryPersons(ctx, page, pageSize, filters)
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取入室单列表失败: "+err.Error())
		return
	}

	// 将完整的工单列表转换为DTO列表
	//faultTicketDTOs := repository.ConvertToDTOList(faultTickets)

	response.Success(ctx, gin.H{
		"list":  entryTickets,
		"total": total,
	}, "获取入室单列表成功")
}

// UpdateEntryPerson 更新入室人员
func (c *EntryTicketController) UpdateEntryPerson(ctx *gin.Context) {
	var entryPerson model.EntryPerson
	if err := ctx.ShouldBindJSON(&entryPerson); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	if err := c.entryService.UpdateEntryPerson(ctx, &entryPerson); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新入室人员失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新入室人员成功")
}

// UpdateEntryPerson 更新入室人员
func (c *EntryTicketController) UpdateEntryPersonFields(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "无效的ID")
		return
	}

	// 获取原始工单信息，检查工单是否存在
	_, err = c.entryService.GetEntryPersonByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "获取报障单失败: "+err.Error())
		return
	}

	// 解析请求体中的字段
	var updateFields map[string]interface{}
	if err := ctx.ShouldBindJSON(&updateFields); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	//// 验证SLA状态字段
	//if status, exists := updateFields["status"]; exists {
	//	statusStr, ok := status.(string)
	//	if !ok || (statusStr != "enabled" && statusStr != "disabled") {
	//		// 如果SLA状态字段无效，则从更新字段中移除
	//		delete(updateFields, "status")
	//	}
	//}
	//
	//// 验证优先级字段
	//if priority, exists := updateFields["priority"]; exists {
	//	priorityStr, ok := priority.(string)
	//	if !ok || (priorityStr != "low" && priorityStr != "medium" && priorityStr != "high" && priorityStr != "critical") {
	//		delete(updateFields, "priority")
	//	}
	//}

	// 验证状态字段
	if status, exists := updateFields["status"]; exists {
		statusStr, ok := status.(string)
		if !ok || !isValidStatus(statusStr) {
			delete(updateFields, "status")
		}
	}

	// 如果没有有效的更新字段，则返回成功但不执行更新
	if len(updateFields) == 0 {
		response.Success(ctx, nil, "无有效更新字段")
		return
	}

	// 调用服务更新字段
	if err := c.entryService.UpdateEntryPersonFields(ctx, uint(id), updateFields); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "更新报障单字段失败: "+err.Error())
		return
	}

	response.Success(ctx, nil, "更新报障单字段成功")
}

// 辅助函数：检查状态字符串是否有效
func isValidStatus(status string) bool {
	validStatuses := []string{
		"enabled", "disabled",
	}

	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// isValidateEntryStageTransition 验证工作流阶段转换是否有效
//
//nolint:all
func isValidateEntryStageTransition(currentStatus, stage string) (bool, string) {
	validStages := make([]string, 0)
	switch currentStatus {
	case common.StatusWaitingApproval:
		validStages = []string{common.StageCustomerApproval}
	case common.StatusWaitingSecondApproval:
		validStages = []string{common.StageSecondApproval}
		//case StatusOutbounding:
		//	validStages = []string{StageStartOutbound}
		//case StatusCompleteOutbound:
		//	validStages = []string{StageCompleteOutbound}
		//case StatusCancelled:
		//	validStages = []string{StageCompleteTicket}
	}

	for _, validStage := range validStages {
		if validStage == stage {
			return true, ""
		}
	}

	validStagesStr := ""
	for i, stage := range validStages {
		if i > 0 {
			validStagesStr += ", "
		}
		validStagesStr += stage
	}

	return false, fmt.Sprintf("当前状态[%s]只允许触发以下阶段: %s", currentStatus, validStagesStr)
}

// isValidEntryStatusTransition 检查状态转换是否合法
func isValidEntryStatusTransition(currentStatus, newStatus string) bool {
	// 定义状态转换映射
	validTransitions := map[string][]string{
		common.StatusWaitingApproval:       {common.StatusWaitingSecondApproval, common.StatusApproved, common.StatusRejected},
		common.StatusWaitingSecondApproval: {common.StatusApproved, common.StatusRejected},
		//StatusOutbounding:                  {StatusCompleteOutbound},
	}

	// 任何状态都可以转换为相同的状态（无变化）
	if currentStatus == newStatus {
		return true
	}

	// 检查目标状态是否在当前状态允许的转换列表中
	allowedStatuses, exists := validTransitions[currentStatus]
	if !exists {
		return false
	}

	for _, status := range allowedStatuses {
		if status == newStatus {
			return true
		}
	}

	return false
}

// CreateEntryPerson 创建入室人员记录
func (c *EntryTicketController) CreateEntryPerson(ctx *gin.Context) {
	// 定义一个请求结构体数组
	var requests []struct {
		Name                 string `json:"name"`
		IdentificationNumber string `json:"identification_number"`
		Telephone            string `json:"telephone"`
		PersonType           string `json:"person_type"`
		Company              string `json:"company"`
		DataCenterName       string `json:"dataCenterName"`
		RoomName             string `json:"roomName"`
		EntryStartTimeStr    string `json:"entry_start_time"`
		EntryEndTimeStr      string `json:"entry_end_time"`
		Status               string `json:"status"`
	}

	if err := ctx.ShouldBindJSON(&requests); err != nil {
		response.Fail(ctx, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 检查是否有数据
	if len(requests) == 0 {
		response.Fail(ctx, http.StatusBadRequest, "请求数据为空")
		return
	}

	// 处理第一个请求项（或者可以循环处理所有项）
	request := requests[0]

	// 解析时间字符串为time.Time类型
	entryStartTime, err := time.Parse(time.RFC3339, request.EntryStartTimeStr)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "入室开始时间格式错误: "+err.Error())
		return
	}

	entryEndTime, err := time.Parse(time.RFC3339, request.EntryEndTimeStr)
	if err != nil {
		response.Fail(ctx, http.StatusBadRequest, "入室结束时间格式错误: "+err.Error())
		return
	}

	// 创建入室人员记录
	entryPerson := &model.EntryPerson{
		Name:                 request.Name,
		IdentificationNumber: request.IdentificationNumber,
		Telephone:            request.Telephone,
		PersonType:           request.PersonType,
		Company:              request.Company,
		EntryStartTime:       &entryStartTime,
		EntryEndTime:         &entryEndTime,
		Status:               request.Status, // 默认状态为enabled
	}

	// 调用服务层创建入室人员记录
	if err := c.entryService.Create(ctx, entryPerson); err != nil {
		response.Fail(ctx, http.StatusInternalServerError, "创建入室人员失败: "+err.Error())
		return
	}

	response.Success(ctx, entryPerson, "创建入室人员成功")
}
