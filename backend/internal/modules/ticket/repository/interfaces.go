package repository

import (
	"backend/internal/modules/ticket/model"
	"context"
)

// RepairSelectionRepository 维修选择仓库接口
type RepairSelectionRepository interface {
	// Create 创建维修选择记录
	Create(ctx context.Context, repairSelection *model.RepairSelection) error
	// GetByTicketID 根据工单ID获取维修选择记录
	GetByTicketID(ctx context.Context, ticketID uint) (*model.RepairSelection, error)
	// Update 更新维修选择记录
	Update(ctx context.Context, repairSelection *model.RepairSelection) error
	// Delete 删除维修选择记录
	Delete(ctx context.Context, id uint) error
}

// CustomerApprovalRepository 客户审批仓库接口
type CustomerApprovalRepository interface {
	// Create 创建客户审批记录
	Create(ctx context.Context, customerApproval *model.CustomerApproval) error
	// GetByTicketID 根据工单ID获取客户审批记录
	GetByTicketID(ctx context.Context, ticketID uint) (*model.CustomerApproval, error)
	// Update 更新客户审批记录
	Update(ctx context.Context, customerApproval *model.CustomerApproval) error
	// Delete 删除客户审批记录
	Delete(ctx context.Context, id uint) error
}

// VerificationRepository 验证结果仓库接口
type VerificationRepository interface {
	// Create 创建验证结果记录
	Create(ctx context.Context, verification *model.Verification) error
	// GetByTicketID 根据工单ID获取验证结果记录
	GetByTicketID(ctx context.Context, ticketID uint) (*model.Verification, error)
	// Update 更新验证结果记录
	Update(ctx context.Context, verification *model.Verification) error
	// Delete 删除验证结果记录
	Delete(ctx context.Context, id uint) error
}

// EntryPersonRepository 入室人员仓库接口
type EntryPersonRepository interface {
	// Create 创建入室人员
	Create(ctx context.Context, repairSelection *model.EntryPerson) error
	// GetByTicketID 根据工单ID获取入室人员
	GetByTicketID(ctx context.Context, ticketID uint) (*model.EntryPerson, error)
	// GetByTicketID 根据工单ID获取入室人员
	GetByID(ctx context.Context, id uint) (*model.EntryPerson, error)
	// Update 更新入室人员
	Update(ctx context.Context, repairSelection *model.EntryPerson) error

	UpdateFields(ctx context.Context, id uint, fields map[string]interface{}) error
	// Delete 删除入室人员
	Delete(ctx context.Context, id uint) error
	// List 查询入室人员
	List(ctx context.Context, page, pageSize int, conditions map[string]interface{}) ([]*model.EntryPerson, int64, error)

	WithTransaction(ctx context.Context, fn func(txCtx context.Context, repo EntryPersonRepository) error) error
}

// EntryApprovalRepository 入室审批仓库接口
type EntryApprovalRepository interface {
	// Create 创建客户审批记录
	Create(ctx context.Context, customerApproval *model.EntryApproval) error
	// GetByTicketID 根据工单ID获取客户审批记录
	GetByTicketID(ctx context.Context, ticketID uint) (*model.EntryApproval, error)
	// Update 更新客户审批记录
	Update(ctx context.Context, customerApproval *model.EntryApproval) error
	// Delete 删除客户审批记录
	Delete(ctx context.Context, id uint) error
}
