package mysql

import (
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	"context"

	"gorm.io/gorm"
)

type verificationRepository struct {
	db *gorm.DB
}

// NewVerificationRepository 创建新的验证结果仓库
func NewVerificationRepository(db *gorm.DB) repository.VerificationRepository {
	return &verificationRepository{db: db}
}

// Create 创建验证结果记录
func (r *verificationRepository) Create(ctx context.Context, verification *model.Verification) error {
	return r.db.WithContext(ctx).Create(verification).Error
}

// GetByTicketID 根据工单ID获取验证结果记录
func (r *verificationRepository) GetByTicketID(ctx context.Context, ticketID uint) (*model.Verification, error) {
	var verification model.Verification
	err := r.db.WithContext(ctx).Where("ticket_id = ?", ticketID).First(&verification).Error
	if err != nil {
		return nil, err
	}
	return &verification, nil
}

// Update 更新验证结果记录
func (r *verificationRepository) Update(ctx context.Context, verification *model.Verification) error {
	return r.db.WithContext(ctx).Save(verification).Error
}

// Delete 删除验证结果记录
func (r *verificationRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.Verification{}, id).Error
}
