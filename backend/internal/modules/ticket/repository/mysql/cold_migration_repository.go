package mysql

import (
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	"context"
	"fmt"

	"gorm.io/gorm"
)

// ColdMigrationRepository 冷迁移记录MySQL仓库实现
type ColdMigrationRepository struct {
	db *gorm.DB
}

// NewColdMigrationRepository 创建新的冷迁移记录仓库
func NewColdMigrationRepository(db *gorm.DB) repository.ColdMigrationRepository {
	return &ColdMigrationRepository{db: db}
}

// Create 创建冷迁移记录
func (r *ColdMigrationRepository) Create(ctx context.Context, coldMigration *model.ColdMigration) error {
	return r.db.WithContext(ctx).Create(coldMigration).Error
}

// GetByID 根据ID获取冷迁移记录
func (r *ColdMigrationRepository) GetByID(ctx context.Context, id uint) (*model.ColdMigration, error) {
	var coldMigration model.ColdMigration
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&coldMigration).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &coldMigration, nil
}

// GetByTicketID 根据故障单ID获取冷迁移记录
func (r *ColdMigrationRepository) GetByTicketID(ctx context.Context, ticketID uint) (*model.ColdMigration, error) {
	var coldMigration model.ColdMigration
	if err := r.db.WithContext(ctx).Where("ticket_id = ?", ticketID).First(&coldMigration).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &coldMigration, nil
}

// GetByFaultDeviceSN 根据故障机SN获取冷迁移记录
func (r *ColdMigrationRepository) GetByFaultDeviceSN(ctx context.Context, faultDeviceSN string) ([]*model.ColdMigration, error) {
	var coldMigrations []*model.ColdMigration
	if err := r.db.WithContext(ctx).Where("fault_device_sn = ?", faultDeviceSN).Find(&coldMigrations).Error; err != nil {
		return nil, err
	}
	return coldMigrations, nil
}

// GetByBackupDeviceSN 根据备机SN获取冷迁移记录
func (r *ColdMigrationRepository) GetByBackupDeviceSN(ctx context.Context, backupDeviceSN string) ([]*model.ColdMigration, error) {
	var coldMigrations []*model.ColdMigration
	if err := r.db.WithContext(ctx).Where("backup_device_sn = ?", backupDeviceSN).Find(&coldMigrations).Error; err != nil {
		return nil, err
	}
	return coldMigrations, nil
}

// Update 更新冷迁移记录
func (r *ColdMigrationRepository) Update(ctx context.Context, coldMigration *model.ColdMigration) error {
	return r.db.WithContext(ctx).Save(coldMigration).Error
}

// Delete 删除冷迁移记录
func (r *ColdMigrationRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.ColdMigration{}, id).Error
}

// List 获取冷迁移记录列表
func (r *ColdMigrationRepository) List(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*model.ColdMigration, int64, error) {
	var coldMigrations []*model.ColdMigration
	var total int64

	// 构建查询
	query := r.db.WithContext(ctx).Model(&model.ColdMigration{})

	// 应用过滤条件
	for key, value := range filters {
		if value != nil {
			switch key {
			case "ticket_id", "status", "operator_id":
				query = query.Where(fmt.Sprintf("%s = ?", key), value)
			case "fault_device_sn", "backup_device_sn":
				if strValue, ok := value.(string); ok && strValue != "" {
					query = query.Where(fmt.Sprintf("%s = ?", key), strValue)
				}
			case "start_time":
				if timeValue, ok := value.(string); ok && timeValue != "" {
					query = query.Where("execution_time >= ?", timeValue)
				}
			case "end_time":
				if timeValue, ok := value.(string); ok && timeValue != "" {
					query = query.Where("execution_time <= ?", timeValue)
				}
			}
		}
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("id DESC").Find(&coldMigrations).Error; err != nil {
		return nil, 0, err
	}

	return coldMigrations, total, nil
}
