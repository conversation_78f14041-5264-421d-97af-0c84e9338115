package mysql

import (
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	"context"

	"gorm.io/gorm"
)

type repairSelectionRepository struct {
	db *gorm.DB
}

// NewRepairSelectionRepository 创建新的维修选择仓库
func NewRepairSelectionRepository(db *gorm.DB) repository.RepairSelectionRepository {
	return &repairSelectionRepository{db: db}
}

// Create 创建维修选择记录
func (r *repairSelectionRepository) Create(ctx context.Context, repairSelection *model.RepairSelection) error {
	return r.db.WithContext(ctx).Create(repairSelection).Error
}

// GetByTicketID 根据工单ID获取维修选择记录
func (r *repairSelectionRepository) GetByTicketID(ctx context.Context, ticketID uint) (*model.RepairSelection, error) {
	var repairSelection model.RepairSelection
	// 添加排序，确保获取最新的记录
	err := r.db.WithContext(ctx).
		Where("ticket_id = ?", ticketID).
		Order("created_at DESC").
		First(&repairSelection).Error
	if err != nil {
		return nil, err
	}
	return &repairSelection, nil
}

// Update 更新维修选择记录
func (r *repairSelectionRepository) Update(ctx context.Context, repairSelection *model.RepairSelection) error {
	return r.db.WithContext(ctx).Save(repairSelection).Error
}

// Delete 删除维修选择记录
func (r *repairSelectionRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.RepairSelection{}, id).Error
}
