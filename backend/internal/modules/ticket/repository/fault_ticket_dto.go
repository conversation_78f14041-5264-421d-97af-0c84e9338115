package repository

import (
	"backend/internal/modules/ticket/model"
	"time"
)

// DeviceDTO 设备DTO
type DeviceDTO struct {
	ID               uint        `json:"id,omitempty"`
	SN               string      `json:"sn,omitempty"`
	Brand            string      `json:"brand,omitempty"`
	Model            string      `json:"model,omitempty"`
	AssetStatus      string      `json:"assetStatus,omitempty"`
	HardwareStatus   string      `json:"hardwareStatus,omitempty"`
	ResidualValue    float64     `json:"residualValue,omitempty"`
	AssetType        string      `json:"assetType,omitempty"`
	Remark           string      `json:"remark,omitempty"`
	LastStatusChange interface{} `json:"lastStatusChange,omitempty"`
	Resource         interface{} `json:"resource,omitempty"`
	TemplateID       uint        `json:"templateID,omitempty"`
	Template         interface{} `json:"template,omitempty"`
}

// RegionDTO 区域DTO
type RegionDTO struct {
	ID   uint   `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

// AzDTO 可用区DTO
type AzDTO struct {
	ID       uint       `json:"id,omitempty"`
	Name     string     `json:"name,omitempty"`
	RegionID uint       `json:"regionId,omitempty"`
	Region   *RegionDTO `json:"region,omitempty"`
}

// DataCenterDTO 数据中心DTO
type DataCenterDTO struct {
	ID   uint   `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
	AZID uint   `json:"azId,omitempty"`
	AZ   *AzDTO `json:"az,omitempty"`
}

// RoomDTO 机房DTO
type RoomDTO struct {
	ID           uint           `json:"id,omitempty"`
	Name         string         `json:"name,omitempty"`
	DataCenterID uint           `json:"dataCenterID,omitempty"`
	DataCenter   *DataCenterDTO `json:"dataCenter,omitempty"`
}

// CabinetDTO 机柜DTO
type CabinetDTO struct {
	ID                 uint     `json:"id,omitempty"`
	Name               string   `json:"name,omitempty"`
	RoomID             uint     `json:"roomID,omitempty"`
	Room               *RoomDTO `json:"room,omitempty"`
	CapacityUnits      int      `json:"capacityUnits,omitempty"`
	Row                string   `json:"row,omitempty"`
	Column             string   `json:"column,omitempty"`
	CabinetType        string   `json:"cabinetType,omitempty"`
	NetworkEnvironment string   `json:"networkEnvironment,omitempty"`
	BondType           string   `json:"bondType,omitempty"`
}

// ResourceDTO 资源DTO
type ResourceDTO struct {
	ID                  uint        `json:"id,omitempty"`
	SN                  string      `json:"sn,omitempty"`
	Hostname            string      `json:"hostname,omitempty"`
	Cluster             string      `json:"cluster,omitempty"`
	VpcIP               string      `json:"vpcIP,omitempty"`
	TenantIP            string      `json:"tenantIP,omitempty"`
	AssetID             uint        `json:"assetID,omitempty"`
	Device              *DeviceDTO  `json:"device,omitempty"`
	BizStatus           string      `json:"bizStatus,omitempty"`
	ResStatus           string      `json:"resStatus,omitempty"`
	Project             string      `json:"project,omitempty"`
	CabinetID           uint        `json:"cabinetID,omitempty"`
	Cabinet             *CabinetDTO `json:"cabinet,omitempty"`
	RoomID              uint        `json:"roomID,omitempty"`
	Room                *RoomDTO    `json:"room,omitempty"`
	RackPosition        int         `json:"rackPosition,omitempty"`
	Height              int         `json:"height,omitempty"`
	BmcIP               string      `json:"bmcIP,omitempty"`
	IsBackup            bool        `json:"isBackup,omitempty"`
	LastBizStatusChange interface{} `json:"lastBizStatusChange,omitempty"`
}

// FaultTicketDTO 用于控制报障单返回字段的数据传输对象
type FaultTicketDTO struct {
	ID                     uint         `json:"id"`
	CreatedAt              time.Time    `json:"created_at"`
	UpdatedAt              time.Time    `json:"updated_at"`
	TicketNo               string       `json:"ticketNo"`
	Source                 string       `json:"source"`
	Title                  string       `json:"title"`
	Priority               string       `json:"priority"`
	Status                 string       `json:"status"`
	DeviceSN               string       `json:"deviceSN"`
	ComponentSN            string       `json:"componentSN"`
	ComponentType          string       `json:"componentType"`
	Resource               *ResourceDTO `json:"resource,omitempty"`
	ResourceIdentifier     string       `json:"resource_identifier"`
	FaultType              string       `json:"faultType"`
	FaultDetailType        string       `json:"fault_detail_type"`
	RepairTicketID         uint         `json:"repair_ticket_id"`
	FaultDescription       string       `json:"faultDescription"`
	Symptom                string       `json:"symptom"`
	SlotPosition           string       `json:"slotPosition"`
	ReporterID             uint         `json:"reporterID"`
	ReporterName           string       `json:"reporterName"`
	AssignedTo             string       `json:"assignedTo"`
	CreationTime           time.Time    `json:"creationTime"`
	AcknowledgeTime        *time.Time   `json:"acknowledgeTime"`
	TriageCompleteTime     *time.Time   `json:"triageCompleteTime"`
	AssignmentTime         *time.Time   `json:"assignmentTime"`
	CustomerApprovalTime   *time.Time   `json:"customerApprovalTime"`
	ExpectedFixTime        *time.Time   `json:"expectedFixTime"`
	ActualFixTime          *time.Time   `json:"actualFixTime"`
	VerificationStartTime  *time.Time   `json:"verificationStartTime"`
	VerificationEndTime    *time.Time   `json:"verificationEndTime"`
	CloseTime              *time.Time   `json:"closeTime"`
	DiagnosisDuration      int          `json:"diagnosis_duration"`
	HardwareRepairDuration int          `json:"hardware_repair_duration"`
	SoftwareFixDuration    int          `json:"software_fix_duration"`
	TotalDowntime          int          `json:"totalDowntime"`
	BusinessImpactTime     int          `json:"business_impact_time"`
	IsFrequentFault        bool         `json:"is_frequent_fault"`
	RequireApproval        bool         `json:"require_approval"`
	ImmediateRepair        bool         `json:"immediate_repair"`
	CountInSLA             bool         `json:"count_in_sla"`
	FaultSummary           string       `json:"faultSummary"`
	RepairMethod           string       `json:"repairMethod"`
	PreventionMeasures     string       `json:"preventionMeasures"`
	BusinessImpact         string       `json:"business_impact"`
	SLAStatus              string       `json:"slaStatus"`
	SLAViolationReason     string       `json:"sla_violation_reason"`
	Remarks                string       `json:"remarks"`
	NeedsWorkflowRetry     bool         `json:"needsWorkflowRetry"`
	LastWorkflowRetryTime  *time.Time   `json:"lastWorkflowRetryTime"`
	WorkflowRetryCount     int          `json:"workflow_retry_count"`
	WaitingManualTrigger   bool         `json:"waitingManualTrigger"`
	CurrentWaitingStage    string       `json:"currentWaitingStage"`
	LastWaitingTime        *time.Time   `json:"lastWaitingTime"`
	ResponseDuration       int          `json:"response_duration"`
	IsFalseAlarm           bool         `json:"is_false_alarm"`
	IsDuplicateFault       bool         `json:"is_duplicate_fault"`
	RelatedTicketID        uint         `json:"related_ticket_id"`
}

// ConvertDeviceToDTO 将Device模型转换为DTO
func ConvertDeviceToDTO(device interface{}) *DeviceDTO {
	if device == nil {
		return nil
	}

	// 尝试获取Device的基本信息
	deviceMap, ok := device.(map[string]interface{})
	if !ok {
		// 如果无法转换为map，返回空DTO
		return &DeviceDTO{}
	}

	dto := &DeviceDTO{}

	// 提取ID
	if id, ok := deviceMap["id"].(float64); ok {
		dto.ID = uint(id)
	}

	// 提取SN
	if sn, ok := deviceMap["sn"].(string); ok {
		dto.SN = sn
	}

	// 提取Brand
	if brand, ok := deviceMap["brand"].(string); ok {
		dto.Brand = brand
	}

	// 提取Model
	if model, ok := deviceMap["model"].(string); ok {
		dto.Model = model
	}

	// 提取AssetStatus
	if assetStatus, ok := deviceMap["assetStatus"].(string); ok {
		dto.AssetStatus = assetStatus
	}

	// 提取HardwareStatus
	if hardwareStatus, ok := deviceMap["hardwareStatus"].(string); ok {
		dto.HardwareStatus = hardwareStatus
	}

	// 提取AssetType
	if assetType, ok := deviceMap["assetType"].(string); ok {
		dto.AssetType = assetType
	}

	// 提取Remark
	if remark, ok := deviceMap["remark"].(string); ok {
		dto.Remark = remark
	}

	return dto
}

// ConvertResourceToDTO 将Resource模型转换为DTO
func ConvertResourceToDTO(resource interface{}) *ResourceDTO {
	if resource == nil {
		return nil
	}

	// 尝试获取Resource的基本信息
	resourceMap, ok := resource.(map[string]interface{})
	if !ok {
		// 如果无法转换为map，返回空DTO
		return &ResourceDTO{}
	}

	dto := &ResourceDTO{}

	// 提取ID
	if id, ok := resourceMap["id"].(float64); ok {
		dto.ID = uint(id)
	}

	// 提取SN
	if sn, ok := resourceMap["sn"].(string); ok {
		dto.SN = sn
	}

	// 提取Hostname
	if hostname, ok := resourceMap["hostname"].(string); ok {
		dto.Hostname = hostname
	}

	// 提取Cluster
	if cluster, ok := resourceMap["cluster"].(string); ok {
		dto.Cluster = cluster
	}

	// 提取VpcIP
	if vpcIP, ok := resourceMap["vpcIP"].(string); ok {
		dto.VpcIP = vpcIP
	}

	// 提取TenantIP
	if tenantIP, ok := resourceMap["tenantIP"].(string); ok {
		dto.TenantIP = tenantIP
	}

	// 提取BizStatus
	if bizStatus, ok := resourceMap["bizStatus"].(string); ok {
		dto.BizStatus = bizStatus
	}

	// 提取ResStatus
	if resStatus, ok := resourceMap["resStatus"].(string); ok {
		dto.ResStatus = resStatus
	}

	// 提取Project
	if project, ok := resourceMap["project"].(string); ok {
		dto.Project = project
	}

	// 提取RackPosition
	if rackPosition, ok := resourceMap["rackPosition"].(float64); ok {
		dto.RackPosition = int(rackPosition)
	}

	// 提取BmcIP
	if bmcIP, ok := resourceMap["bmcIP"].(string); ok {
		dto.BmcIP = bmcIP
	}

	// 提取IsBackup
	if isBackup, ok := resourceMap["isBackup"].(bool); ok {
		dto.IsBackup = isBackup
	}

	// 提取Device并转换为DeviceDTO
	if device, ok := resourceMap["device"]; ok {
		dto.Device = ConvertDeviceToDTO(device)
	}

	return dto
}

// ConvertToDTO 将完整的FaultTicket模型转换为DTO
func ConvertToDTO(ticket *model.FaultTicket) *FaultTicketDTO {
	dto := &FaultTicketDTO{
		ID:                     ticket.ID,
		CreatedAt:              ticket.CreatedAt,
		UpdatedAt:              ticket.UpdatedAt,
		TicketNo:               ticket.TicketNo,
		Source:                 ticket.Source,
		Title:                  ticket.Title,
		Priority:               ticket.Priority,
		Status:                 ticket.Status,
		DeviceSN:               ticket.DeviceSN,
		ComponentSN:            ticket.ComponentSN,
		ComponentType:          ticket.ComponentType,
		ResourceIdentifier:     ticket.ResourceIdentifier,
		FaultType:              ticket.FaultType,
		FaultDetailType:        ticket.FaultDetailType,
		RepairTicketID:         ticket.RepairTicketID,
		FaultDescription:       ticket.FaultDescription,
		Symptom:                ticket.Symptom,
		SlotPosition:           ticket.SlotPosition,
		ReporterID:             ticket.ReporterID,
		ReporterName:           ticket.ReporterName,
		AssignedTo:             ticket.AssignedTo,
		CreationTime:           ticket.CreationTime,
		AcknowledgeTime:        ticket.AcknowledgeTime,
		TriageCompleteTime:     ticket.TriageCompleteTime,
		AssignmentTime:         ticket.AssignmentTime,
		CustomerApprovalTime:   ticket.CustomerApprovalTime,
		ExpectedFixTime:        ticket.ExpectedFixTime,
		ActualFixTime:          ticket.ActualFixTime,
		VerificationStartTime:  ticket.VerificationStartTime,
		VerificationEndTime:    ticket.VerificationEndTime,
		CloseTime:              ticket.CloseTime,
		DiagnosisDuration:      ticket.DiagnosisDuration,
		HardwareRepairDuration: ticket.HardwareRepairDuration,
		SoftwareFixDuration:    ticket.SoftwareFixDuration,
		TotalDowntime:          ticket.TotalDowntime,
		BusinessImpactTime:     ticket.BusinessImpactTime,
		IsFrequentFault:        ticket.IsFrequentFault,
		RequireApproval:        ticket.RequireApproval,
		ImmediateRepair:        ticket.ImmediateRepair,
		CountInSLA:             ticket.CountInSLA,
		FaultSummary:           ticket.FaultSummary,
		RepairMethod:           ticket.RepairMethod,
		PreventionMeasures:     ticket.PreventionMeasures,
		BusinessImpact:         ticket.BusinessImpact,
		SLAStatus:              ticket.SLAStatus,
		SLAViolationReason:     ticket.SLAViolationReason,
		Remarks:                ticket.Remarks,
		NeedsWorkflowRetry:     ticket.NeedsWorkflowRetry,
		LastWorkflowRetryTime:  ticket.LastWorkflowRetryTime,
		WorkflowRetryCount:     ticket.WorkflowRetryCount,
		WaitingManualTrigger:   ticket.WaitingManualTrigger,
		CurrentWaitingStage:    ticket.CurrentWaitingStage,
		LastWaitingTime:        ticket.LastWaitingTime,
		ResponseDuration:       ticket.ResponseDuration,
		IsFalseAlarm:           ticket.IsFalseAlarm,
		IsDuplicateFault:       ticket.IsDuplicateFault,
		RelatedTicketID:        ticket.RelatedTicketID,
	}

	// 转换Resource对象为ResourceDTO
	if ticket.Resource.ID > 0 && ticket.DeviceSN != "" {
		resourceDTO := &ResourceDTO{
			ID:                  ticket.Resource.ID,
			SN:                  ticket.Resource.SN,
			Hostname:            ticket.Resource.Hostname,
			Cluster:             ticket.Resource.Cluster,
			VpcIP:               ticket.Resource.VpcIP,
			TenantIP:            ticket.Resource.TenantIP,
			AssetID:             ticket.Resource.AssetID,
			BizStatus:           ticket.Resource.BizStatus,
			ResStatus:           ticket.Resource.ResStatus,
			Project:             ticket.Resource.Project,
			CabinetID:           ticket.Resource.CabinetID,
			RoomID:              ticket.Resource.RoomID,
			RackPosition:        ticket.Resource.RackPosition,
			Height:              ticket.Resource.Height,
			BmcIP:               ticket.Resource.BmcIP,
			IsBackup:            ticket.Resource.IsBackup,
			LastBizStatusChange: ticket.Resource.LastBizStatusChange,
		}

		// 如果Resource.Device不为空，则转换Device
		if ticket.Resource.Device.ID > 0 && ticket.DeviceSN != "" {
			resourceDTO.Device = &DeviceDTO{
				ID:               ticket.Resource.Device.ID,
				SN:               ticket.Resource.Device.SN,
				Brand:            ticket.Resource.Device.Brand,
				Model:            ticket.Resource.Device.Model,
				AssetStatus:      ticket.Resource.Device.AssetStatus,
				HardwareStatus:   ticket.Resource.Device.HardwareStatus,
				ResidualValue:    ticket.Resource.Device.ResidualValue,
				AssetType:        ticket.Resource.Device.AssetType,
				Remark:           ticket.Resource.Device.Remark,
				LastStatusChange: ticket.Resource.Device.LastStatusChange,
				Resource:         ticket.Resource.Device.Resource,
				TemplateID:       ticket.Resource.Device.TemplateID,
				Template:         ticket.Resource.Device.Template,
			}
		} else {
			// 确保返回空对象而不是null
			resourceDTO.Device = &DeviceDTO{}
		}

		// 处理Cabinet
		if ticket.Resource.CabinetID > 0 || (ticket.Resource.Cabinet != nil && ticket.Resource.Cabinet.ID > 0) {
			if ticket.Resource.Cabinet != nil {
				resourceDTO.Cabinet = &CabinetDTO{
					ID:                 ticket.Resource.Cabinet.ID,
					Name:               ticket.Resource.Cabinet.Name,
					RoomID:             ticket.Resource.Cabinet.RoomID,
					CapacityUnits:      ticket.Resource.Cabinet.CapacityUnits,
					Row:                ticket.Resource.Cabinet.Row,
					Column:             ticket.Resource.Cabinet.Column,
					CabinetType:        ticket.Resource.Cabinet.CabinetType,
					NetworkEnvironment: ticket.Resource.Cabinet.NetworkEnvironment,
					BondType:           ticket.Resource.Cabinet.BondType,
				}
			} else {
				resourceDTO.Cabinet = &CabinetDTO{}
			}

			// 处理Room
			if ticket.Resource.Cabinet.RoomID > 0 || ticket.Resource.Cabinet.Room.ID > 0 {
				resourceDTO.Cabinet.Room = &RoomDTO{
					ID:           ticket.Resource.Cabinet.Room.ID,
					Name:         ticket.Resource.Cabinet.Room.Name,
					DataCenterID: ticket.Resource.Cabinet.Room.DataCenterID,
				}

				// 处理DataCenter
				if ticket.Resource.Cabinet.Room.DataCenterID > 0 || (ticket.Resource.Cabinet.Room.DataCenter != nil && ticket.Resource.Cabinet.Room.DataCenter.ID > 0) {
					if ticket.Resource.Cabinet.Room.DataCenter != nil {
						resourceDTO.Cabinet.Room.DataCenter = &DataCenterDTO{
							ID:   ticket.Resource.Cabinet.Room.DataCenter.ID,
							Name: ticket.Resource.Cabinet.Room.DataCenter.Name,
							AZID: ticket.Resource.Cabinet.Room.DataCenter.AZID,
						}
					} else {
						resourceDTO.Cabinet.Room.DataCenter = &DataCenterDTO{}
					}

					// 处理Az
					if ticket.Resource.Cabinet.Room.DataCenter.AZID > 0 || ticket.Resource.Cabinet.Room.DataCenter.AZ.ID > 0 {
						resourceDTO.Cabinet.Room.DataCenter.AZ = &AzDTO{
							ID:       ticket.Resource.Cabinet.Room.DataCenter.AZ.ID,
							Name:     ticket.Resource.Cabinet.Room.DataCenter.AZ.Name,
							RegionID: ticket.Resource.Cabinet.Room.DataCenter.AZ.RegionID,
						}

						// 处理Region
						if ticket.Resource.Cabinet.Room.DataCenter.AZ.RegionID > 0 || ticket.Resource.Cabinet.Room.DataCenter.AZ.Region.ID > 0 {
							resourceDTO.Cabinet.Room.DataCenter.AZ.Region = &RegionDTO{
								ID:   ticket.Resource.Cabinet.Room.DataCenter.AZ.Region.ID,
								Name: ticket.Resource.Cabinet.Room.DataCenter.AZ.Region.Name,
							}
						} else {
							resourceDTO.Cabinet.Room.DataCenter.AZ.Region = &RegionDTO{}
						}
					} else {
						resourceDTO.Cabinet.Room.DataCenter.AZ = &AzDTO{}
					}
				} else {
					resourceDTO.Cabinet.Room.DataCenter = &DataCenterDTO{}
				}
			} else {
				resourceDTO.Cabinet.Room = &RoomDTO{}
			}
		} else {
			resourceDTO.Cabinet = &CabinetDTO{}
		}

		// 处理Room (如果直接引用)
		if ticket.Resource.RoomID > 0 || (ticket.Resource.Room != nil && ticket.Resource.Room.ID > 0) {
			if ticket.Resource.Room != nil {
				resourceDTO.Room = &RoomDTO{
					ID:           ticket.Resource.Room.ID,
					Name:         ticket.Resource.Room.Name,
					DataCenterID: ticket.Resource.Room.DataCenterID,
				}
			} else {
				resourceDTO.Room = &RoomDTO{}
			}

			// 如果需要，可以添加DataCenter和其他嵌套对象的处理
		} else {
			resourceDTO.Room = &RoomDTO{}
		}

		dto.Resource = resourceDTO
	} else {
		// 确保返回空对象而不是null
		dto.Resource = &ResourceDTO{
			Device: &DeviceDTO{},
			Cabinet: &CabinetDTO{
				Room: &RoomDTO{
					DataCenter: &DataCenterDTO{
						AZ: &AzDTO{
							Region: &RegionDTO{},
						},
					},
				},
			},
			Room: &RoomDTO{},
		}
	}

	return dto
}

// ConvertToDTOList 将FaultTicket模型列表转换为DTO列表
func ConvertToDTOList(tickets []*model.FaultTicket) []*FaultTicketDTO {
	dtoList := make([]*FaultTicketDTO, 0, len(tickets))
	for _, ticket := range tickets {
		dtoList = append(dtoList, ConvertToDTO(ticket))
	}
	return dtoList
}
