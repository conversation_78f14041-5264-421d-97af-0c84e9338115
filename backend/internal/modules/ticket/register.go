package ticket

import (
	"backend/configs"
	outboundCtl "backend/internal/modules/cmdb/controller/outbound"
	cmdbOutbound "backend/internal/modules/cmdb/model/outbound"
	cmdbAssetRepo "backend/internal/modules/cmdb/repository/asset"
	inboundRepository "backend/internal/modules/cmdb/repository/inbound"
	"backend/internal/modules/cmdb/repository/inventory"
	"backend/internal/modules/cmdb/repository/outbound"
	"backend/internal/modules/cmdb/repository/outbound/mysql"
	cmdbProductRepo "backend/internal/modules/cmdb/repository/product"
	cmdbAsset "backend/internal/modules/cmdb/service/asset"
	inboundService "backend/internal/modules/cmdb/service/inbound"
	cmdbInventory "backend/internal/modules/cmdb/service/inventory"
	outboundSrv "backend/internal/modules/cmdb/service/outbound"
	"backend/internal/modules/cmdb/service/product"
	cmdbActivities "backend/internal/modules/cmdb/workflow/activities"
	inventoryService "backend/internal/modules/inventory/service"
	purchaseRep "backend/internal/modules/purchase/repository"
	purchaseSer "backend/internal/modules/purchase/service"
	purchaseRepository "backend/internal/modules/purchase_old/repository"
	purchaseService "backend/internal/modules/purchase_old/service"
	scheduleRepo "backend/internal/modules/schedule/repository"
	scheduleService "backend/internal/modules/schedule/service"
	serverRepository "backend/internal/modules/server/repository"
	serverService "backend/internal/modules/server/service"
	"backend/internal/modules/ticket/controller"
	"backend/internal/modules/ticket/model"
	"backend/internal/modules/ticket/repository"
	mysqlRepo "backend/internal/modules/ticket/repository/mysql"
	"backend/internal/modules/ticket/service"
	"backend/internal/modules/ticket/workflow"
	"backend/internal/modules/ticket/workflow/activities"
	userService "backend/internal/modules/user/service"

	"context"
	"time"

	"github.com/gin-gonic/gin"
	"go.temporal.io/sdk/client"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// contextKey 定义自定义的 context key 类型
type contextKey string

const (
	// WorkflowActivitiesKey 工作流活动的 context key
	WorkflowActivitiesKey contextKey = "workflowActivities"
)

// Module 工单管理模块
type Module struct {
	db             *gorm.DB
	logger         *zap.Logger
	temporalClient client.Client
	config         *configs.Config

	// 外部服务引用
	externalUserService userService.IUserService
	userSvc             userService.IUserService
	scheduleService     scheduleService.SoftScheduleService

	// 服务
	entryTicketService      service.EntryTicketService
	faultTicketService      service.FaultTicketService
	repairTicketService     service.RepairTicketService
	repairSelectionService  service.RepairSelectionService
	customerApprovalService service.CustomerApprovalService
	verificationService     service.VerificationService
	coldMigrationService    service.ColdMigrationService
	userService             service.UserService
	inboundTicketService    service.InboundTicketService
	outboundTicketService   outboundSrv.OutboundTicketService

	// 控制器
	faultTicketController    *controller.FaultTicketController
	repairTicketController   *controller.RepairTicketController
	outboundTicketController *outboundCtl.OutboundTicketController
	workflowController       *controller.WorkflowController
	inboundTicketController  *controller.InboundTicketController

	// 入室
	entryPersonService    service.EntryPersonService
	entryTicketController *controller.EntryTicketController
}

// NewModule 创建工单管理模块
func NewModule(db *gorm.DB, logger *zap.Logger, temporalClient client.Client, userService userService.IUserService, config *configs.Config, userSvc userService.IUserService) *Module {
	return &Module{
		db:                  db,
		logger:              logger,
		temporalClient:      temporalClient,
		externalUserService: userService,
		userSvc:             userSvc,
		config:              config,
	}
}

// Initialize 初始化模块
func (m *Module) Initialize() error {
	// 初始化仓库
	entryTicketRepo := repository.NewEntryTicketRepository(m.db)
	faultTicketRepo := repository.NewFaultTicketRepository(m.db)
	repairTicketRepo := repository.NewRepairTicketRepository(m.db)
	outboundTicketRepo := outbound.NewOutboundTicketRepository(m.db)
	inventoryRepo := inventory.NewInventoryRepository(m.db)
	inboundTicketRepo := repository.NewInboundTicketRepository(m.db, inventoryRepo)
	inboundRepo := inboundRepository.NewInboundRepository(m.db)
	serverRepo := serverRepository.NewServerRepository(m.db)
	deviceRepo := cmdbAssetRepo.NewDeviceRepository(m.db)
	productRepo := cmdbProductRepo.NewProductRepository(m.db)
	spareRepo := cmdbAssetRepo.NewSpareRepository(m.db)

	// 初始化新增的三个仓库
	outboundApprovalRepo := mysql.NewOutboundApprovalRepository(m.db)
	entryPersonRepo := mysqlRepo.NewEntryPersonRepository(m.db)
	entryApprovalRepo := mysqlRepo.NewEntryApprovalRepository(m.db)

	repairSelectionRepo := mysqlRepo.NewRepairSelectionRepository(m.db)
	customerApprovalRepo := mysqlRepo.NewCustomerApprovalRepository(m.db)
	verificationRepo := mysqlRepo.NewVerificationRepository(m.db)
	// 初始化冷迁移记录仓库
	coldMigrationRepo := mysqlRepo.NewColdMigrationRepository(m.db)
	// 采购
	purchaseRepo := purchaseRepository.NewPurchaseRepository(m.db)

	// 初始化备件服务
	spareService := inventoryService.NewSpareService()

	// 初始化用户服务代理
	m.userService = service.NewUserService(m.externalUserService)

	// 尝试获取值班服务
	scheduleRepo := scheduleRepo.NewSoftScheRepository(m.db)
	m.scheduleService = scheduleService.NewSoftScheService(scheduleRepo)

	// 初始化库存服务
	inventorySvc := cmdbInventory.NewInventoryService(inventoryRepo, m.db)

	// 初始化服务
	purchaseSvc := purchaseService.NewPurchaseService(purchaseRepo)
	serverSvc := serverService.NewServerService(serverRepo)
	deviceSvc := cmdbAsset.NewDeviceService(deviceRepo)
	inboundSvc := inboundService.InitInboundService(inboundRepo, purchaseSvc, inventorySvc, inventoryRepo, serverSvc)
	productSvc := product.NewProductService(productRepo)
	AssetSpareSvc := cmdbAsset.NewSpareService(spareRepo, inventorySvc, deviceSvc)

	// 项目模块
	projectRepo := purchaseRep.NewProjectRepository(m.db)
	projectSvc := purchaseSer.NewProjectService(projectRepo)

	// 供应商模块
	supplierRepo := purchaseRep.NewSupplierRepository(m.db)
	supplierSvc := purchaseSer.NewSupplierService(supplierRepo)

	// 公司模块
	companyRepo := purchaseRep.NewCompanyRepository(m.db)
	companySvc := purchaseSer.NewCompanyService(companyRepo)

	// 采购模块
	requestRepo := purchaseRep.NewPurchaseRequestRepository(m.db)
	inquiryRepo := purchaseRep.NewPurchaseInquiryRepository(m.db)
	contractRepo := purchaseRep.NewPurchaseContractRepository(m.db)
	requestSvc := purchaseSer.NewPurchaseRequestService(requestRepo, m.temporalClient, m.userSvc, projectSvc)
	inquirySvc := purchaseSer.NewPurchaseInquiryService(inquiryRepo, requestRepo, m.temporalClient, m.userSvc, projectSvc, supplierSvc)
	contractSvc := purchaseSer.NewPurchaseContractService(contractRepo, m.temporalClient, m.userSvc, supplierSvc, projectSvc, companySvc, productSvc, inquirySvc)

	// 先初始化维修单服务（因为故障单服务依赖它）
	m.repairTicketService = service.NewRepairTicketService(repairTicketRepo, faultTicketRepo, spareService, m.userService)

	// 设置Temporal客户端到维修单服务
	if m.temporalClient != nil {
		// 使用类型断言确保接口实现了SetTemporalClient方法
		if rs, ok := m.repairTicketService.(interface{ SetTemporalClient(client.Client) }); ok {
			rs.SetTemporalClient(m.temporalClient)
			m.logger.Info("成功设置维修单服务的Temporal客户端")
		} else {
			m.logger.Warn("维修单服务未实现SetTemporalClient方法，工作流信号可能无法正常触发")
		}
	} else {
		m.logger.Warn("Temporal客户端未初始化，维修单工作流功能将被禁用")
	}

	// 然后初始化故障单服务，并传入维修单服务
	m.faultTicketService = service.NewFaultTicketService(
		faultTicketRepo,
		repairSelectionRepo,
		customerApprovalRepo,
		verificationRepo,
		m.temporalClient,
		m.logger,
		m.repairTicketService,
		m.scheduleService, // 使用值班服务
	)
	m.inboundTicketService = service.InitInboundTicketService(inboundTicketRepo, inboundSvc, purchaseSvc, inventorySvc, productSvc, AssetSpareSvc, deviceSvc, requestSvc, inquirySvc, contractSvc, m.temporalClient, m.logger)

	// 移除重复创建的维修单服务，保留第一次创建和初始化的
	m.outboundTicketService = outboundSrv.NewOutboundTicketService(outboundTicketRepo, outboundApprovalRepo, deviceSvc, productSvc, inventorySvc, AssetSpareSvc, m.temporalClient, m.logger)

	m.entryTicketService = service.NewEntryTicketService(entryTicketRepo, entryPersonRepo, entryApprovalRepo, m.temporalClient, m.logger)

	// 初始化流程服务
	m.repairSelectionService = service.NewRepairSelectionService(repairSelectionRepo, faultTicketRepo)
	m.customerApprovalService = service.NewCustomerApprovalService(customerApprovalRepo, faultTicketRepo, m.repairTicketService)
	m.verificationService = service.NewVerificationService(verificationRepo, faultTicketRepo)
	// 初始化冷迁移服务
	m.coldMigrationService = service.NewColdMigrationService(coldMigrationRepo)
	m.entryPersonService = service.NewEntryPersonService(entryPersonRepo, entryTicketRepo, m.logger)

	// 初始化控制器
	m.entryTicketController = controller.NewEntryTicketController(m.entryTicketService, m.entryPersonService)
	m.faultTicketController = controller.NewFaultTicketController(m.faultTicketService)
	m.repairTicketController = controller.NewRepairTicketController(m.repairTicketService)
	m.outboundTicketController = outboundCtl.NewOutboundTicketController(m.outboundTicketService)
	m.workflowController = controller.NewWorkflowController(
		m.faultTicketService,
		m.repairSelectionService,
		m.customerApprovalService,
		m.verificationService,
		m.repairTicketService,
		m.outboundTicketService,
		m.entryPersonService,
		m.userService,
		m.coldMigrationService,
		m.logger,
	)
	m.inboundTicketController = controller.NewInboundTicketController(m.inboundTicketService, purchaseSvc)

	// 初始化工作流活动
	if m.temporalClient != nil {
		m.logger.Info("Initializing Temporal workflow activities")

		activities.InitActivities(
			faultTicketRepo,
			m.faultTicketService,
			repairTicketRepo,
			repairSelectionRepo,
			customerApprovalRepo,
			verificationRepo,
			m.repairTicketService,
			m.userService,
			m.logger,
		)

		// 为冷迁移活动初始化设备状态依赖
		deviceRepo := cmdbAssetRepo.NewDeviceRepository(m.db)
		resourceRepo := cmdbAssetRepo.NewResourceRepository(m.db)
		statusChangeRepo := cmdbAssetRepo.NewStatusChangeRepository(m.db)
		activities.InitDeviceStatusActivities(
			deviceRepo,
			resourceRepo,
			statusChangeRepo,
			faultTicketRepo,
			coldMigrationRepo,
		)
		m.logger.Info("冷迁移活动状态依赖初始化完成")

		activities.InitEntryActivities(entryTicketRepo, m.entryTicketService, entryPersonRepo, m.entryPersonService, entryApprovalRepo)

		// 注意：飞书通知器已在temporal worker中初始化，这里不需要重复初始化
		m.logger.Info("飞书通知器已在temporal worker中初始化，跳过重复初始化")

		inventoryRepo := inventory.NewInventoryRepository(m.db)
		assetSpareRepo := cmdbAssetRepo.NewSpareRepository(m.db)
		inventoryService := cmdbInventory.NewInventoryService(inventoryRepo, m.db)
		assetService := cmdbAsset.NewSpareService(assetSpareRepo, inventoryService, deviceSvc)
		activities.InitActivities(faultTicketRepo, m.faultTicketService, repairTicketRepo, repairSelectionRepo, customerApprovalRepo, verificationRepo, m.repairTicketService, m.userService, m.logger)
		cmdbActivities.InitActivities(outboundTicketRepo, m.outboundTicketService, outboundApprovalRepo, assetService, inventoryService, m.logger)
		// 初始化信号处理器
		workflow.InitSignalHandlers(faultTicketRepo, m.logger)

		// 启动一个goroutine来恢复不一致的工作流状态
		go func() {
			// 等待系统启动稳定后再执行恢复
			time.Sleep(5 * time.Second)
			m.logger.Info("开始恢复不一致的工作流状态")

			// 创建一个带有工作流活动上下文的context
			ctx := context.Background()
			ctx = context.WithValue(ctx, WorkflowActivitiesKey, activities.GetActivityInterface())

			if err := m.faultTicketService.RecoverInconsistentWorkflows(ctx); err != nil {
				m.logger.Error("恢复工作流状态失败", zap.Error(err))
			} else {
				m.logger.Info("工作流状态恢复完成")
			}

			if err := m.outboundTicketService.RecoverInconsistentWorkflows(ctx); err != nil {
				m.logger.Error("恢复工作流状态失败", zap.Error(err))
			} else {
				m.logger.Info("工作流状态恢复完成")
			}

			if err := m.entryTicketService.RecoverInconsistentWorkflows(ctx); err != nil {
				m.logger.Error("恢复工作流状态失败", zap.Error(err))
			} else {
				m.logger.Info("工作流状态恢复完成")
			}
		}()
	} else {
		m.logger.Info("Temporal client not provided, workflow activities will be disabled")
		// 即使没有 Temporal client，也需要初始化信号处理器
		workflow.InitSignalHandlers(faultTicketRepo, m.logger)
	}

	return nil
}

// RegisterRoutes 注册路由
func (m *Module) RegisterRoutes(router *gin.RouterGroup) {
	// 使用全局认证中间件保护路由
	ticketGroup := router.Group("/ticket")
	// 全局已经添加了AuthMiddleware和AuthorizeWithCasbin，这里不需要重复添加
	// ticketGroup.Use(middleware.AuthMiddleware()) // 移除重复的认证中间件

	m.entryTicketController.RegisterRoutes(ticketGroup)
	m.faultTicketController.RegisterRoutes(ticketGroup)
	m.repairTicketController.RegisterRoutes(ticketGroup)
	m.inboundTicketController.RegisterRoutes(ticketGroup)
	m.outboundTicketController.RegisterRoutes(ticketGroup)
	m.workflowController.RegisterRoutes(ticketGroup)
}

// AutoMigrate 自动迁移数据库
func (m *Module) AutoMigrate() error {
	// 现在重新创建表
	return m.db.AutoMigrate(
		&model.FaultTicket{},
		&model.FaultTicketStatusHistory{},
		&model.RepairTicket{},
		&model.RepairSelection{},
		&model.CustomerApproval{},
		&model.Verification{},
		&model.RepairTicketStatusHistory{},
		&model.ColdMigration{},

		// 出库模型
		&cmdbOutbound.SpareOutboundTicket{},
		&cmdbOutbound.OutboundApproval{},
		&cmdbOutbound.OutboundTicketStatusHistory{},
		&cmdbOutbound.OutboundDetail{},
		&cmdbOutbound.OutboundInfo{},

		// 添加其他需要迁移的模型
		// 入库模型
		&model.PartInboundTicket{},
		&model.NewInboundTicket{},
		&model.RepairPartInboundTicket{},
		&model.InboundTicketHistory{},
		&model.RepairPartTicketHistory{},
		&model.DismantledPartInboundTicket{},
		&model.DismantledPartTicketHistory{},
		&model.DeviceInboundTicket{},
		&model.DeviceInboundTicketHistory{},

		// 重构后的入库模型
		&model.InboundTicket{},
		&model.InboundInfo{},
		&model.InboundDetail{},
		&model.InboundHistory{},

		// 入室
		&model.EntryPerson{},
		&model.EntryTicket{},
		&model.EntryTicketStatusHistory{},
		&model.EntryApproval{},
	)
}

// GetFaultTicketService 获取故障工单服务
func (m *Module) GetFaultTicketService() service.FaultTicketService {
	return m.faultTicketService
}
