package common

import (
	"fmt"
)

// 状态转换规则
var ValidStatusTransitions = map[string][]string{

	// 终态
	StatusCompleted: {}, // 已完成状态为终态
	StatusCancelled: {}, // 已取消状态为终态

	// 入库阶段（客户核对）
	StatusWaitingAssetApproval: {StatusAssetApprovalPass, StatusAssetApprovalFail},   // 等待资产管理员审核--->通过 | 不通过
	StatusWaitingVerify:        {StatusVerificationPassed, StatusVerificationFailed}, //等待资产管理员验证 ---> 验证通过 | 验证不通过

	// 入库阶段（弃用）
	StatusReEdit:                {StatusWaitingManageApproval, StatusCancelled},          //重新编辑---> 待审核 | 取消
	StatusWaitingManageApproval: {StatusManageApproval, StatusCancelled, StatusRejected}, //等待审批--->审批完成 | 拒绝 | 取消
	StatusManageApproval:        {StatusCounting, StatusCancelled},                       // 审批完成--->入库清点 | 取消
	StatusCounting:              {StatusCompletedCount, StatusCancelled},                 //入库清点--->清点完成 | 取消
	StatusCompletedCount:        {StatusInbounding, StatusCancelled},                     //清点完成--->入库 |取消
	StatusInbounding:            {StatusCompletedInboud, StatusCancelled},                //入库--->入库完成 | 取消
	StatusCompletedInboud:       {StatusWaitingAssetApproval, StatusRejected},            // 入库完成---> 等待入库审批 | 拒绝
	StatusRejected:              {StatusReEdit, StatusCancelled},                         // 审批拒绝--->重新编辑 | 取消
	"":                          {StatusWaitingApproval},                                 //无状态--->等待审批

	StatusWaitingAccept:       {StatusInvestigating, StatusCancelled},
	StatusInvestigating:       {StatusWaitingApproval, StatusApprovedWaiting, StatusRepairing, StatusRestarting, StatusMigrating, StatusSoftwareFixing, StatusCompleted, StatusCancelled},
	StatusWaitingApproval:     {StatusApprovedWaiting, StatusCancelled},
	StatusApprovedWaiting:     {StatusRepairing, StatusRestarting, StatusMigrating, StatusSoftwareFixing, StatusCancelled},
	StatusRepairing:           {StatusWaitingVerification, StatusCancelled},
	StatusRestarting:          {StatusWaitingVerification, StatusCancelled},
	StatusMigrating:           {StatusWaitingVerification, StatusCancelled},
	StatusSoftwareFixing:      {StatusWaitingVerification, StatusCancelled},
	StatusWaitingVerification: {StatusSummarizing, StatusRepairing, StatusRestarting, StatusMigrating, StatusSoftwareFixing, StatusCancelled},
	StatusSummarizing:         {StatusCompleted, StatusCancelled},
}

// StatusTransitionError 状态转换错误
type StatusTransitionError struct {
	CurrentStatus string
	NewStatus     string
	Message       string
}

func (e *StatusTransitionError) Error() string {
	return fmt.Sprintf("无效的状态转换: 从 %s 到 %s - %s", e.CurrentStatus, e.NewStatus, e.Message)
}

// IsValidStatusTransition 检查状态转换是否有效
func IsValidStatusTransition(currentStatus, newStatus string) bool {
	validTransitions, exists := ValidStatusTransitions[currentStatus]
	if !exists {
		return false
	}

	for _, validStatus := range validTransitions {
		if validStatus == newStatus {
			return true
		}
	}
	return false
}

// ValidateStatusTransition 验证状态转换并返回错误
func ValidateStatusTransition(currentStatus, newStatus string) error {
	if !IsValidStatusTransition(currentStatus, newStatus) {
		return &StatusTransitionError{
			CurrentStatus: currentStatus,
			NewStatus:     newStatus,
			Message:       "不支持的状态转换",
		}
	}
	return nil
}

// GetValidTransitions 获取当前状态可以转换到的所有有效状态
func GetValidTransitions(currentStatus string) []string {
	return ValidStatusTransitions[currentStatus]
}

// MapStatusToStage 根据状态转换映射到工作流阶段
func MapStatusToStage(status string) string {
	switch status {
	// 初始状态
	case StatusWaitingAccept:
		return StageAcceptTicket
	// 排查阶段
	case StatusInvestigating:
		return StageRepairSelection
	// 审批阶段
	case StatusWaitingApproval:
		return StageCustomerApproval
	case StatusApprovedWaiting:
		return StageStartRepair
	// 维修阶段
	case StatusRepairing, StatusRestarting, StatusMigrating, StatusSoftwareFixing:
		return StageCompleteRepair
	// 验证阶段
	case StatusWaitingVerification:
		return StageCompleteVerification
	// 总结阶段
	case StatusSummarizing:
		return StageSummary
	// 终态
	case StatusCompleted:
		return StageCompleteTicket
	case StatusCancelled:
		return StageCompleteTicket
	default:
		return ""
	}
}

// IsValidStageForStatus 检查当前状态是否可以触发指定阶段
func IsValidStageForStatus(currentStatus, stage string) (bool, string) {
	validStages := make([]string, 0)
	switch currentStatus {
	case StatusWaitingAccept:
		validStages = []string{StageAcceptTicket}
	case StatusInvestigating:
		validStages = []string{StageRepairSelection, StageStartRepair, StageSummary}
	case StatusWaitingApproval:
		validStages = []string{StageCustomerApproval}
	case StatusApprovedWaiting:
		validStages = []string{StageStartRepair}
	case StatusRepairing, StatusRestarting, StatusMigrating, StatusSoftwareFixing:
		validStages = []string{StageCompleteRepair, StageStartVerification}
	case StatusWaitingVerification:
		validStages = []string{StageCompleteVerification, StageSummary}
	case StatusSummarizing:
		validStages = []string{StageSummary}
	case StatusCompleted:
		validStages = []string{StageCompleteTicket}
	case StatusCancelled:
		validStages = []string{StageCompleteTicket}
	}

	for _, validStage := range validStages {
		if validStage == stage {
			return true, ""
		}
	}

	validStagesStr := ""
	for i, stage := range validStages {
		if i > 0 {
			validStagesStr += ", "
		}
		validStagesStr += stage
	}

	return false, fmt.Sprintf("当前状态[%s]只允许触发以下阶段: %s", currentStatus, validStagesStr)
}

// ValidateStageTransition 验证工作流阶段转换是否有效
func ValidateStageTransition(currentStatus, stage string) (bool, string) {
	validStages := make([]string, 0)
	switch currentStatus {
	case StatusWaitingAccept:
		validStages = []string{StageAcceptTicket}
	case StatusInvestigating:
		validStages = []string{StageRepairSelection, StageStartRepair, StageSummary}
	case StatusWaitingApproval:
		validStages = []string{StageCustomerApproval, StageManageApproval}
	case StatusApprovedWaiting:
		validStages = []string{StageStartRepair}
	case StatusRepairing, StatusRestarting, StatusMigrating, StatusSoftwareFixing:
		validStages = []string{StageCompleteRepair, StageStartVerification}
	case StatusWaitingVerification:
		validStages = []string{StageCompleteVerification, StageSummary}
	case StatusSummarizing:
		validStages = []string{StageSummary, StageCompleteTicket}
	case StatusCompleted:
		validStages = []string{StageCompleteTicket}
	case StatusCancelled:
		validStages = []string{StageCompleteTicket}
	//入库阶段⬇️
	case StatusReEdit:
		validStages = []string{StageWaitingManageApproval, StageCancelled}
	case StatusWaitingManageApproval:
		validStages = []string{StageManageApproval, StageCancelled}
	case StatusManageApproval:
		validStages = []string{StageCount, StageCancelled}
	case StatusCounting:
		validStages = []string{StageCompleteCount, StageCancelled}
	case StatusCompletedCount:
		validStages = []string{StageInbound, StageCancelled}
	case StatusInbounding:
		validStages = []string{StageCompleteInbound, StageRejected, StageCancelled}
	case StatusRejected:
		validStages = []string{StageReEdit, StageCancelled}
	case StatusCompletedInboud:
		validStages = []string{StageAssetApproval, StageCancelled}
	case StatusWaitingAssetApproval: // 等待审核 ---> 验证阶段 | 入库工单完成阶段
		validStages = []string{StageAssetApproval, StageVerify, StageCompleteInbound}
	case StatusWaitingVerify: // 等待验证---> 资产管理员审核阶段 | 入库工单完成阶段
		validStages = []string{StageVerify, StageAssetApproval, StageCompleteInbound}
	}

	for _, validStage := range validStages {
		if validStage == stage {
			return true, ""
		}
	}

	validStagesStr := ""
	for i, stage := range validStages {
		if i > 0 {
			validStagesStr += ", "
		}
		validStagesStr += stage
	}

	return false, fmt.Sprintf("当前状态[%s]只允许触发以下阶段: %s", currentStatus, validStagesStr)
}

// ValidateEntryStageTransition 验证工作流阶段转换是否有效
func ValidateEntryStageTransition(currentStatus, stage string) (bool, string) {
	validStages := make([]string, 0)
	switch currentStatus {
	case StatusWaitingApproval:
		validStages = []string{StageCustomerApproval}
	case StatusWaitingSecondApproval:
		validStages = []string{StageSecondApproval}
		//case StatusOutbounding:
		//	validStages = []string{StageStartOutbound}
		//case StatusCompleteOutbound:
		//	validStages = []string{StageCompleteOutbound}
		//case StatusCancelled:
		//	validStages = []string{StageCompleteTicket}
	}

	for _, validStage := range validStages {
		if validStage == stage {
			return true, ""
		}
	}

	validStagesStr := ""
	for i, stage := range validStages {
		if i > 0 {
			validStagesStr += ", "
		}
		validStagesStr += stage
	}

	return false, fmt.Sprintf("当前状态[%s]只允许触发以下阶段: %s", currentStatus, validStagesStr)
}
