package common

import (
	"backend/internal/modules/cmdb/model/inbound"
	"time"
)

type StartInboundWorkflowInput interface {
	GetInboundType() string
}

type WorkflowSignalInterface interface {
	GetSignalType() string
}

// WorkflowControlSignal 工作流控制信号
type WorkflowControlSignal struct {
	Stage        string                 // 目标阶段
	Status       string                 //状态
	OperatorID   uint                   // 操作人ID
	OperatorName string                 // 操作人姓名
	Comments     string                 // 备注
	Data         map[string]interface{} `json:"data"` // 额外数据
	Timestamp    int64                  // 操作时间戳
}

type NewPartInboundSignal struct {
	Status           string
	OperatorID       uint
	OperatorName     string
	RequiredApproval bool
	RequiredVerify   bool
	Comments         string
}

func (n NewPartInboundSignal) GetSignalType() string {
	return inbound.TypeNewPartInbound
}

type RepairPartInboundSignal struct {
	Status           string
	OperatorID       uint
	OperatorName     string
	RequiredApproval bool
	RequiredVerify   bool
	Comments         string
}

func (r RepairPartInboundSignal) GetSignalType() string {
	return inbound.TypeRepairedPartInbound
}

// 拆机入库信号
type DismantledPartInboundSignal struct {
	Status           string
	OperatorID       uint
	OperatorName     string
	RequiredApproval bool
	RequiredVerify   bool
	Comments         string
}

func (d DismantledPartInboundSignal) GetSignalType() string {
	return inbound.TypeDismantledPartInbound
}

// 重构后只保留这两个更新信号
type PartInboundSignal struct {
	Status        string
	OperatorID    uint
	OperatorName  string
	RequireVerify bool
	Comments      string
}

func (p PartInboundSignal) GetSignalType() string { return inbound.TypePartInbound }

type DeviceInboundSignal struct {
	Status        string
	OperatorID    uint
	OperatorName  string
	RequireVerify bool
	Comments      string
}

func (d DeviceInboundSignal) GetSignalType() string {
	return inbound.TypeDeviceInbound
}

// RepairSelectionInput 维修选择输入
type RepairSelectionInput struct {
	RepairType              string // 维修类型
	Diagnosis               string // 诊断结果
	RequireCustomerApproval bool   // 是否需要客户审批
	FaultDetailType         string // 故障具体类型
	SlotPosition            string // 插槽位置
}

// CustomerApprovalResult 客户审批结果
type CustomerApprovalResult struct {
	Status   string // 审批状态：approved/rejected
	Comments string // 审批意见
}

// VerificationResult 验证结果
type VerificationResult struct {
	Success  bool   // 是否验证成功
	Comments string // 验证意见
}

// PartInboundWorkflowInput 入库工作流输入
//
//	type PartInboundWorkflowInput struct {
//		PartInboundID uint   `json:"part_inbound_id"` //旧件入库ID
//		InboundID     uint   `json:"inbound_id"`      //新件入库ID
//		OperatorID    uint   `json:"operator_id"`     // 操作人ID
//		OperatorName  string `json:"operator_name"`   // 操作人姓名
//		Completed     bool   `json:"completed"`       // 是否已完成
//
//		// 扩展字段 - 保存入库单的更多信息
//		InboundNo        string `json:"inbound_no"`        // 入库单号
//		Status           string `json:"status"`            // 当前状态(审核中、清点中、入库中、完成)
//		Stage            string `json:"stage"`             // 阶段(审核、清点、入库、审核、完成)
//		Title            string `json:"title"`             // 入库标题
//		RequireApproval  bool   `json:"require_approval"`  // 是否需要审核(默认需要)
//		ApproverID       uint   `json:"approver_id"`       // 审核人ID
//		ApproverName     string `json:"approver_name"`     // 审核人姓名
//		ApprovalComments string `json:"approval_comments"` // 审核意见
//	}
type PartInboundWorkflowInput struct {
	InboundTicketID  uint   `json:"inbound_ticket_id"`
	InboundNo        string `json:"inbound_no"`
	RequireVarify    bool   `json:"require_varify"`
	InboundReason    string `json:"inbound_reason"`
	RepairTicketID   uint   `json:"repair_ticket_id"`   // 维修工单ID
	OutboundTicketID uint   `json:"outbound_ticket_id"` // 出库工单ID
}

func (p PartInboundWorkflowInput) GetInboundType() string {
	return "part_inbound"
}

type NewInboundWorkflowInput struct {
	NewInboundTicketID uint   `json:"new_inbound_ticket_id" binding:"required"`
	InboundNo          string `json:"inbound_no"`
	RequireApproval    bool   `json:"require_approval"`
	FileID             uint   `json:"file_id" binding:"required"`
}

func (n NewInboundWorkflowInput) GetInboundType() string {
	return "new_part"
}

type NewPartInboundWorkflowInput struct {
	NewInboundTicketID uint   `json:"new_inbound_ticket_id" binding:"required"`
	InboundNo          string `json:"inbound_no"`
	RequireApproval    bool   `json:"require_approval"`
}

func (n NewPartInboundWorkflowInput) GetInboundType() string {
	return inbound.TypeNewPartInbound
}

// 返修入库输入
type RepairInboundWorkflowInput struct {
	RepairInboundTicketID uint   `json:"repair_inbound_ticket_id" binding:"required"`
	RequireVerification   bool   `json:"require_verification"`
	InboundNo             string `json:"inbound_no"`
}

func (n RepairInboundWorkflowInput) GetInboundType() string {
	return inbound.TypeRepairedPartInbound
}

type InitPartInboundResult struct {
	ProductIDs   []uint `json:"product_ids"`
	AssetIDs     []uint `json:"asset_ids"`
	ComponentIDs []uint `json:"component_ids"`
	InboundID    uint   `json:"inbound_id"`
}

// 拆机入库输入
type DismantledInboundWorkflowInput struct {
	DismantledInboundTicketID uint   `json:"dismantled_ticket_id" binding:"required"`
	InboundNo                 string `json:"inbound_no"`
}

func (n DismantledInboundWorkflowInput) GetInboundType() string {
	return inbound.TypeDismantledPartInbound
}

type DeviceInboundWorkflowInput struct {
	InboundTicketID  uint   `json:"inbound_ticket_id"`
	InboundNo        string `json:"inbound_no"`
	RequireVarify    bool   `json:"require_varify"`
	InboundReason    string `json:"inbound_reason"`
	RepairTicketID   uint   `json:"repair_ticket_id"`   // 维修工单ID
	OutboundTicketID uint   `json:"outbound_ticket_id"` // 出库工单ID
}

func (n DeviceInboundWorkflowInput) GetInboundType() string {
	return inbound.TypeDeviceInbound
}

// InboundResult 入库结果
type InboundResult struct {
	Success        bool      `json:"success"`
	InboundTime    time.Time `json:"inbound_time"`
	Comments       string    `json:"comments"`
	Location       string    `json:"location"`        // 入库位置
	Quantity       int       `json:"quantity"`        // 入库数量
	ProductDetails string    `json:"product_details"` // 产品详情
}

// HardwareRepairWorkflowResult 硬件维修工作流结果
type HardwareRepairWorkflowResult struct {
	Success      bool   `json:"success"`       // 是否成功
	RepairResult string `json:"repair_result"` // 维修结果
	Solution     string `json:"solution"`      // 解决方案
	RepairSteps  string `json:"repair_steps"`  // 维修步骤
}

// 更新工单输入
type UpdateTicketInput struct {
	InboundTicketID  uint
	InboundNo        string
	Stage            string
	CurrentStage     string
	CurrentStatus    string
	NextStatus       string
	OperatorID       uint
	OperatorName     string
	Comments         string
	TryCount         uint
	RequiredApproval bool
}

type UpdateInput struct {
	InboundID    uint
	Stage        string
	CurrentStage string
	NextStatus   string
	OperatorID   uint
	OperatorName string
	Comments     string
	Data         map[string]interface{}
	WareHouseID  uint
	AssetStatus  string
	TryCount     uint
}

type CompleteInput struct {
	ProductID     []uint   `json:"product_id"`   //产品ID
	WarehouseID   uint     `json:"warehouse_id"` //仓库ID
	Warehouse     string   `json:"warehouse"`    //仓库
	Location      string   `json:"location"`     //货架号
	Summary       string   `json:"summary"`      //总结
	Operator      string   `json:"operator"`     //最终操作人
	OperatorID    uint     `json:"operator_id"`  // 操作人ID
	PartInboundID uint     `json:"part_inbound_id"`
	NewInboundID  uint     `json:"new_inbound_id"`
	SN            []string `json:"sn"`       // 设备SN
	AssetID       []uint   `json:"asset_id"` // 备件ID
	Status        string   `json:"status"`   //Active | Archive
}

type NewInboundCompleteInput struct {
	Summary      string `json:"summary"`     //总结
	Operator     string `json:"operator"`    //最终操作人
	OperatorID   uint   `json:"operator_id"` // 操作人ID
	NewInboundID uint   `json:"new_inbound_id"`
	FileID       uint   `json:"file_id"`
	Status       string `json:"status"` //Active | Archive
}
