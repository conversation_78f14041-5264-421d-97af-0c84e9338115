package workflow

import (
	common1 "backend/internal/modules/software_maintenance/common"
	"backend/internal/modules/ticket/common"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
)

type offlineWorkflowState struct {
	OfflineTicketID     uint
	OfflineNo           string
	CurrentStatus       string
	CurrentStage        string
	RequireVerification bool
	WorkflowRetryCount  int
}

// SoftwareOfflineWorkflow 软件下线工作流
func SoftwareOfflineWorkflow(ctx workflow.Context, input common1.OfflineWorkflowInput) error {
	// 设置工作流选项
	ao := workflow.ActivityOptions{
		StartToCloseTimeout: common.ActivityStartToCloseTimeout,
		HeartbeatTimeout:    common.ActivityHeartbeatTimeout,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:        common.InitialInterval,
			BackoffCoefficient:     common.BackoffCoefficient,
			MaximumInterval:        common.MaximumInterval,
			MaximumAttempts:        common.MaxAttempts,
			NonRetryableErrorTypes: []string{"InvalidInputError"},
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)
	// 创建工作流日志
	logger := workflow.GetLogger(ctx)
	logger.Info("初始化SoftwareOfflineWorkflow工作流日志成功")
	logger.Info("输入信息为", "SoftwareOfflineInput", input)

	launchState := offlineWorkflowState{
		OfflineTicketID:     input.OfflineTicketID,
		OfflineNo:           input.OfflineNo,
		CurrentStatus:       common1.OfflineStatusPending,
		CurrentStage:        common1.OfflineStageDataCleaning,
		RequireVerification: input.RequireVerification,
		WorkflowRetryCount:  0,
	}
	var isWorkflowComplete bool
	completeChan := make(chan struct{})
	// 注册 SetUpdateHandler接收更新信号
	err := workflow.SetUpdateHandler(ctx, common1.SoftwareOfflineUpdateSignal, func(ctx workflow.Context, signal common1.OfflineSignal) error {
		logger.Info("收到UpdateHandler信号",
			"SoftwareOfflineTicketID", input.OfflineTicketID,
			"status", signal.Status,
			"operator", signal.OperatorName)
		ctx = workflow.WithActivityOptions(ctx, ao)
		// 处理信号
		switch signal.Status {
		case common1.OfflineStatusDataCleared:
			// 数据清理完成
			// 更新ticket和history
			updateInput := common1.UpdateOfflineTicketInput{
				OfflineTicketID: input.OfflineTicketID,
				OfflineNo:       input.OfflineNo,
				OperatorID:      signal.OperatorID,
				OperatorName:    signal.OperatorName,
				Comments:        signal.Comments,
				CurrentStage:    common1.OfflineStageDataCleaning,
				CurrentStatus:   common1.OfflineStatusDataCleared,
				NextStage:       common1.OfflineStageMachineDown,
				NextStatus:      common1.OfflineStatusWaitingDown,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}
			// 更新下线工单阶段
			err = workflow.ExecuteActivity(ctx, "UpdateOfflineTicketStage", input.OfflineNo, common1.OfflineStageMachineDown).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 更新内部状态 日志记录..
			launchState.CurrentStatus = common1.OfflineStatusWaitingDown
			launchState.CurrentStage = common1.OfflineStageMachineDown
		case common1.OfflineStatusMachineDown:
			// 物理机下线完成
			if err := workflow.ExecuteActivity(ctx, "UpdateCMDBOfflineStatus", input.OfflineTicketID, input.OfflineNo, "闲置中", signal.OperatorID, signal.OperatorName).Get(ctx, nil); err != nil {
				logger.Error("更新CMDB状态失败：", zap.Error(err))
			}
			// 更新ticket和history
			updateInput := common1.UpdateOfflineTicketInput{
				OfflineTicketID: input.OfflineTicketID,
				OfflineNo:       input.OfflineNo,
				OperatorID:      signal.OperatorID,
				OperatorName:    signal.OperatorName,
				Comments:        signal.Comments,
				CurrentStage:    common1.OfflineStageMachineDown,
				CurrentStatus:   common1.OfflineStatusMachineDown,
				NextStage:       common1.OfflineStageComplete,
				NextStatus:      common1.OfflineStatusCompleted,
			}
			err := workflow.ExecuteActivity(ctx, "UpdateTicketAndHistory", updateInput).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 更新下线工单阶段
			err = workflow.ExecuteActivity(ctx, "UpdateOfflineTicketStage", input.OfflineNo, common1.OfflineStatusCompleted).Get(ctx, nil)
			if err != nil {
				return err
			}

			// 终止流程
			isWorkflowComplete = true
			close(completeChan)
		}
		return nil
	})
	if err != nil {
		logger.Error("SetUpdateHandler failed.", "error:", err, zap.Error(err))
	}

	// 使用 workflow.Await 等待条件满足
	err = workflow.Await(ctx, func() bool {
		select {
		case <-completeChan:
			return true
		default:
			return isWorkflowComplete
		}
	})

	if err != nil {
		logger.Error("Workflow await failed.", "error:", err, zap.Error(err))
		return err
	}
	return nil
}
