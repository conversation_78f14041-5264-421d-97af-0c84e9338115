package activities

import (
	"backend/internal/common/constants"
	"backend/internal/modules/cmdb/model/asset"
	"backend/internal/modules/software_maintenance/common"
	"backend/internal/modules/software_maintenance/model"
	launchRepo "backend/internal/modules/software_maintenance/repository"
	offlineSvc "backend/internal/modules/software_maintenance/service"
	"context"
	"fmt"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"time"
)

type LaunchActivity interface {
	UpdateLaunchTicketAndHistory(ctx context.Context, input common.UpdateLaunchTicketInput) error
	UpdateLaunchListStage(ctx context.Context, launchNo string, stage string) error
	UpdateCMDBLaunchStatus(ctx context.Context, launchTicketID uint, launchNo string, newStatus string, operatorID uint, operatorName string) error
	UpdateDeviceDeliveryStatus(ctx context.Context, launchNo string, deviceStatuses map[string]string) error
}

type launchActivities struct {
	db         *gorm.DB
	launchSvc  offlineSvc.LaunchService
	launchRepo launchRepo.LaunchRepository
	logger     *zap.Logger
	offlineSvc offlineSvc.OfflineService
}

func NewLaunchActivities(db *gorm.DB, launchSvc offlineSvc.LaunchService, launchRepo launchRepo.LaunchRepository, logger *zap.Logger, offlineSvc offlineSvc.OfflineService) LaunchActivity {
	return &launchActivities{
		db:         db,
		launchSvc:  launchSvc,
		launchRepo: launchRepo,
		logger:     logger,
		offlineSvc: offlineSvc,
	}
}
func (l *launchActivities) UpdateLaunchTicketAndHistory(ctx context.Context, input common.UpdateLaunchTicketInput) error {
	// 获取上线工单信息
	l.logger.Info("更新上线工单信息：", zap.Any("UpdateInput", input))
	launchTicket, err := l.launchRepo.GetByID(ctx, input.LaunchTicketID)
	if err != nil {
		return fmt.Errorf("获取上线工单信息失败: %v", err)
	}

	// 更新工单状态
	launchTicket.PreviousStatus = launchTicket.Status
	launchTicket.Status = input.NextStatus
	launchTicket.UpdatedAt = time.Now()
	launchTicket.Stage = input.NextStage

	launchTicket.Handler = input.OperatorName
	launchTicket.HandlerID = input.OperatorID

	// 创建历史记录
	history := &model.LaunchHistory{
		SoftwareLaunchID: launchTicket.LaunchTicketNo,
		PreviousStatus:   input.CurrentStatus,
		NewStatus:        input.NextStatus,
		OperatorID:       input.OperatorID,
		OperatorName:     input.OperatorName,
		Comment:          input.Comments,
		Stage:            input.NextStage,
		OperationTime:    time.Now(),
	}
	err = l.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 更新工单
		if err := tx.Save(launchTicket).Error; err != nil {
			return fmt.Errorf("更新软件上线工单状态失败，事务回滚: %v", err)
		}

		// 创建历史记录
		if err := tx.Create(history).Error; err != nil {
			return fmt.Errorf("创建软件上线工单历史记录失败，事务回滚: %v", err)
		}
		return nil
	})

	l.logger.Info("更新工单及历史记录成功", zap.Any("TicketHistory", history), zap.Any("Ticket", launchTicket))
	if err != nil {
		return err
	}
	return nil
}

// UpdateLaunchListStage 更新上线列表阶段
func (l *launchActivities) UpdateLaunchListStage(ctx context.Context, launchNo string, stage string) error {
	l.logger.Info("开始更新工单列表阶段", zap.String("工单号", launchNo), zap.String("阶段", stage))
	update := map[string]interface{}{
		"stage": stage,
	}
	err := l.db.WithContext(ctx).Model(&model.SoftwareLaunchTicket{}).Where("launch_ticket_no = ?", launchNo).Updates(update).Error
	if err != nil {
		return fmt.Errorf("更新LaunchList的stage字段失败：: %w", err)
	}
	return nil
}

// UpdateCMDBLaunchStatus 更新CMDB状态
func (l *launchActivities) UpdateCMDBLaunchStatus(ctx context.Context, launchTicketID uint, launchNo string, newStatus string, operatorID uint, operatorName string) error {
	l.logger.Info("开始更新CMDB状态", zap.String("工单号", launchNo), zap.String("新状态", newStatus))
	// 获取工单关联的所有设备
	devices, err := l.launchRepo.GetDevicesByLaunchTicketNo(ctx, launchNo)
	if err != nil {
		l.logger.Error("获取工单设备失败", zap.Error(err))
		return fmt.Errorf("获取工单设备失败: %w", err)
	}

	// 遍历设备，更新交付状态为成功的设备在CMDB中的状态
	for _, device := range devices {
		// 只处理交付状态为成功的设备
		if device.DeliveryStatus != "success" {
			l.logger.Info("跳过非成功交付的设备",
				zap.String("SN", device.SN),
				zap.String("交付状态", device.DeliveryStatus))
			continue
		}
		l.logger.Info("更新CMDB设备状态",
			zap.String("SN", device.SN),
			zap.String("新状态", newStatus))

		// 创建当前迭代的设备副本
		currentDevice := device
		err := l.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
			// 获取设备当前状态，用于记录状态变更前的状态
			var currentAssetStatus, currentBizStatus string
			assetStatusResult := tx.Table("asset_devices").
				Where("sn = ?", currentDevice.SN).
				Select("asset_status").
				Scan(&currentAssetStatus)
			if assetStatusResult.Error != nil {
				return fmt.Errorf("获取当前资产状态失败: %w", assetStatusResult.Error)
			}

			bizStatusResult := tx.Table("resources").
				Where("sn = ?", currentDevice.SN).
				Select("biz_status").
				Scan(&currentBizStatus)
			if bizStatusResult.Error != nil {
				return fmt.Errorf("获取当前业务状态失败: %w", bizStatusResult.Error)
			}
			// 更新资产状态
			assetResult := tx.Table("asset_devices").
				Where("sn = ?", currentDevice.SN).
				Updates(map[string]interface{}{
					"asset_status": asset.AssetStatusInUse, // 使用中状态
				})
			// 更新业务状态
			resourceResult := tx.Table("resources").
				Where("sn = ?", currentDevice.SN).
				Updates(map[string]interface{}{
					"biz_status": asset.BizStatusActive,  // 业务状态更新为active
					"hostname":   currentDevice.Hostname, // 更新主机名
					"vpc_ip":     currentDevice.VPCIP,    // 更新VPC IP
					"bmc_ip":     currentDevice.BMCIP,    // 更新BMC IP

				})
			if assetResult.Error != nil {
				return fmt.Errorf("更新CMDB资产状态失败: %w", assetResult.Error)
			}
			if resourceResult.Error != nil {
				return fmt.Errorf("更新CMDB业务状态失败: %w", resourceResult.Error)
			}
			if assetResult.RowsAffected == 0 {
				l.logger.Warn("未找到对应的资产记录",
					zap.String("SN", currentDevice.SN))
			}
			if resourceResult.RowsAffected == 0 {
				l.logger.Warn("未找到对应的资源记录",
					zap.String("SN", currentDevice.SN))
			}
			return nil
		})

		if err != nil {
			l.logger.Error("更新CMDB资产状态和业务状态失败",
				zap.String("SN", device.SN),
				zap.Error(err))
			continue
		}

		// 记录状态变更
		var (
			workFlowID = fmt.Sprintf("%s%d", common.PrefixLaunchTicketWorkflowID, launchTicketID)
		)
		// 获取工单关联的所有设备
		ticket, err := l.launchRepo.GetByID(ctx, launchTicketID)
		if err != nil {
			l.logger.Error("获取工单失败", zap.Error(err))
			return fmt.Errorf("获取工单失败: %w", err)
		}

		// 调用service层方法记录状态变更
		err = l.offlineSvc.RecordAssetStatusChange(
			ctx,
			device.SN,
			constants.AssetStatusOnRack,    // 旧资产状态为"上架中"
			constants.AssetStatusInUse,     // 新资产状态为"使用中"
			constants.BizStatusMaintaining, // 旧业务状态为"维护中"
			constants.BizStatusActive,      // 新业务状态为"运行中"
			workFlowID,
			constants.HardwareStatusNormal,
			constants.HardwareStatusNormal,
			ticket.LaunchTicketNo,
			common.SOFTLAUNCHSOURCE,
			ticket.Title,
			operatorID, // 使用传入的操作人ID
			operatorName,
		)

		if err != nil {
			l.logger.Error("记录资产状态变更失败",
				zap.String("SN", device.SN),
				zap.Error(err))

		}
	}
	l.logger.Info("完成CMDB资产状态和业务状态和记录状态变更", zap.String("工单号", launchNo))
	return nil
}

// UpdateDeviceDeliveryStatus 更新设备交付状态 更新每个设备的交付状态
func (l *launchActivities) UpdateDeviceDeliveryStatus(ctx context.Context, launchNo string, deviceStatuses map[string]string) error {
	l.logger.Info("开始更新设备交付状态",
		zap.String("工单号", launchNo),
		zap.Any("设备状态", deviceStatuses))

	if len(deviceStatuses) == 0 {
		l.logger.Warn("没有设备状态需要更新", zap.String("工单号", launchNo))
		return nil
	}
	// 获取工单关联的所有设备
	devices, err := l.launchRepo.GetDevicesByLaunchTicketNo(ctx, launchNo)
	if err != nil {
		l.logger.Error("获取工单设备失败", zap.Error(err))
		return fmt.Errorf("获取工单设备失败: %w", err)
	}
	successCount := 0
	failedCount := 0
	// 遍历设备，根据SN更新状态
	for i, device := range devices {
		if status, exists := deviceStatuses[device.SN]; exists {
			// 更新设备状态  前端的数据逐个给device
			devices[i].DeliveryStatus = status

			// 统计成功和失败的数量
			switch status {
			case "success":
				successCount++
			case "failed":
				failedCount++
			}

			l.logger.Info("设备状态已更新",
				zap.String("SN", device.SN),
				zap.String("状态", status))
		}
	}
	// 批量更新设备状态
	err = l.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, device := range devices {
			if _, exists := deviceStatuses[device.SN]; exists {
				if err := tx.Model(&model.LaunchDevice{}).
					Where("sn = ? AND launch_ticket_no = ?", device.SN, launchNo).
					Update("delivery_status", device.DeliveryStatus).Error; err != nil {
					return fmt.Errorf("更新设备状态失败: %w", err)
				}
			}
		}
		// 更新工单的整体交付结果
		var deliveryResult string
		if failedCount == 0 && successCount > 0 {
			deliveryResult = "success"
		} else if successCount > 0 && failedCount > 0 {
			deliveryResult = "partial_success"
		} else if failedCount > 0 && successCount == 0 {
			deliveryResult = "failed"
		}

		if deliveryResult != "" {
			if err := tx.Model(&model.SoftwareLaunchTicket{}).
				Where("launch_ticket_no = ?", launchNo).
				Update("delivery_result", deliveryResult).Error; err != nil {
				return fmt.Errorf("更新工单交付结果失败: %w", err)
			}
		}

		return nil
	})

	if err != nil {
		l.logger.Error("更新设备交付状态失败", zap.Error(err))
		return err
	}

	l.logger.Info("成功更新设备交付状态",
		zap.String("工单号", launchNo),
		zap.Int("成功设备数", successCount),
		zap.Int("失败设备数", failedCount))

	return nil
}
