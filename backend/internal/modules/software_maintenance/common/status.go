package common

import (
	"fmt"
	"strings"
)

// LaunchValidStatusTransitions 软件上线工单状态转换规则
var LaunchValidStatusTransitions = map[string][]string{
	StatusWaitingBeforeInstallation:     {StatusBeforeInstallationCompleted},
	StatusBeforeInstallationCompleted:   {StatusWaitingBeforeExpansion},
	StatusWaitingBeforeExpansion:        {StatusBeforeExpansionCompleted},
	StatusBeforeExpansionCompleted:      {StatusWaitingAfterExpansion},
	StatusWaitingAfterExpansion:         {StatusAfterExpansionCompleted},
	StatusAfterExpansionCompleted:       {StatusWaitingDeliveryConfirmation},
	StatusWaitingDeliveryConfirmation:   {StatusDeliveryConfirmationCompleted},
	StatusDeliveryConfirmationCompleted: {StatusCompleted},
	// 如果需要，可以添加其他状态转换路径
}

// LaunchStatusTransitionError 状态转换错误
type LaunchStatusTransitionError struct {
	CurrentStatus string
	NewStatus     string
	Message       string
}

func (e *LaunchStatusTransitionError) Error() string {
	return fmt.Sprintf("无效的状态转换: 从 %s 到 %s - %s", e.CurrentStatus, e.NewStatus, e.Message)
}

// IsValidLaunchStatusTransition 检查软件上线工单状态转换是否有效
func IsValidLaunchStatusTransition(currentStatus, newStatus string) bool {
	validTransitions, exists := LaunchValidStatusTransitions[currentStatus]
	if !exists {
		return false
	}

	for _, validStatus := range validTransitions {
		if validStatus == newStatus {
			return true
		}
	}
	return false
}

// ValidateLaunchStageTransition 验证软件上线工单阶段转换是否有效
func ValidateLaunchStageTransition(currentStatus, stage string) (bool, string) {
	// 定义每个状态允许触发的阶段
	validStages := map[string][]string{
		StatusWaitingBeforeInstallation:     {StageBeforeInstallation},
		StatusBeforeInstallationCompleted:   {StageBeforeExpansion},
		StatusWaitingBeforeExpansion:        {StageBeforeExpansion},
		StatusBeforeExpansionCompleted:      {StageAfterExpansion},
		StatusWaitingAfterExpansion:         {StageAfterExpansion},
		StatusAfterExpansionCompleted:       {StageWaitingDelivery},
		StatusWaitingDeliveryConfirmation:   {StageWaitingDelivery},
		StatusDeliveryConfirmationCompleted: {StageCompleted},
		// 可以根据需要添加更多状态和阶段的对应关系
	}

	// 获取当前状态允许的阶段列表
	allowedStages, exists := validStages[currentStatus]
	if !exists {
		return false, fmt.Sprintf("未定义状态 [%s] 的有效阶段", currentStatus)
	}

	// 检查请求的阶段是否在允许列表中
	for _, validStage := range allowedStages {
		if validStage == stage {
			return true, ""
		}
	}

	// 如果不在允许列表中，生成友好的错误消息
	validStagesStr := strings.Join(allowedStages, ", ")
	return false, fmt.Sprintf("当前状态[%s]只允许触发以下阶段: %s", currentStatus, validStagesStr)
}

// OfflineValidStatusTransitions 软件下线工单状态转换规则
var OfflineValidStatusTransitions = map[string][]string{
	OfflineStatusPending:     {OfflineStatusDataCleared},
	OfflineStatusDataCleared: {OfflineStatusWaitingDown},
	OfflineStatusWaitingDown: {OfflineStatusMachineDown},
	OfflineStatusMachineDown: {OfflineStatusCompleted},
	// 如果需要，可以添加其他状态转换路径
}

// OfflineStatusTransitionError 状态转换错误
type OfflineStatusTransitionError struct {
	CurrentStatus string
	NewStatus     string
	Message       string
}

func (e *OfflineStatusTransitionError) Error() string {
	return fmt.Sprintf("无效的状态转换: 从 %s 到 %s - %s", e.CurrentStatus, e.NewStatus, e.Message)
}

// IsValidOfflineStatusTransition 检查软件下线工单状态转换是否有效
func IsValidOfflineStatusTransition(currentStatus, newStatus string) bool {
	validTransitions, exists := OfflineValidStatusTransitions[currentStatus]
	if !exists {
		return false
	}

	for _, validStatus := range validTransitions {
		if validStatus == newStatus {
			return true
		}
	}
	return false
}

// ValidateOfflineStageTransition 验证软件下线工单阶段转换是否有效
func ValidateOfflineStageTransition(currentStatus, stage string) (bool, string) {
	// 定义每个状态允许触发的阶段
	validStages := map[string][]string{
		OfflineStatusPending:     {OfflineStageDataCleaning},
		OfflineStatusDataCleared: {OfflineStageMachineDown},
		OfflineStatusWaitingDown: {OfflineStageMachineDown},
		OfflineStatusMachineDown: {OfflineStageComplete},
		// 可以根据需要添加更多状态和阶段的对应关系
	}

	// 获取当前状态允许的阶段列表
	allowedStages, exists := validStages[currentStatus]
	if !exists {
		return false, fmt.Sprintf("未定义状态 [%s] 的有效阶段", currentStatus)
	}

	// 检查请求的阶段是否在允许列表中
	for _, validStage := range allowedStages {
		if validStage == stage {
			return true, ""
		}
	}

	// 如果不在允许列表中，生成友好的错误消息
	validStagesStr := strings.Join(allowedStages, ", ")
	return false, fmt.Sprintf("当前状态[%s]只允许触发以下阶段: %s", currentStatus, validStagesStr)
}
