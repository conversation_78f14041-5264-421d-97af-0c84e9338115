package common

type LaunchWorkflowInput struct {
	LaunchTicketID      uint   `json:"launch_ticket_id" binding:"required"`
	RequireVerification bool   `json:"require_verification"`
	LaunchNo            string `json:"launch_no"`
}

type LaunchSignal struct {
	Status          string
	OperatorID      uint
	OperatorName    string
	RequiredHandler bool
	Comments        string
	Data            map[string]interface{}
}

type UpdateLaunchTicketInput struct {
	LaunchTicketID   uint
	LaunchNo         string
	CurrentStage     string
	CurrentStatus    string
	NextStage        string
	NextStatus       string
	OperatorID       uint
	OperatorName     string
	Comments         string
	TryCount         uint
	RequiredApproval bool
}

type OfflineWorkflowInput struct {
	OfflineTicketID     uint   `json:"offline_ticket_id" binding:"required"`
	RequireVerification bool   `json:"require_verification"`
	OfflineNo           string `json:"offline_no"`
}

type OfflineSignal struct {
	Status          string
	OperatorID      uint
	OperatorName    string
	RequiredHandler bool
	Comments        string
	Data            map[string]interface{}
}

type UpdateOfflineTicketInput struct {
	OfflineTicketID  uint
	OfflineNo        string
	CurrentStage     string
	CurrentStatus    string
	NextStage        string
	NextStatus       string
	OperatorID       uint
	OperatorName     string
	Comments         string
	TryCount         uint
	RequiredApproval bool
}
