package repository

import (
	common1 "backend/internal/modules/software_maintenance/common"
	"backend/internal/modules/software_maintenance/model"
	"context"
	"fmt"

	"gorm.io/gorm"
)

type LaunchRepository interface {
	List(ctx context.Context, page, pageSize int, ticketNo, status, title string) ([]*model.SoftwareLaunchTicket, int64, error)
	GetByID(ctx context.Context, id uint) (*model.SoftwareLaunchTicket, error)
	GetByNo(ctx context.Context, ticketNo string) (*model.SoftwareLaunchTicket, error)
	InitLaunchTicket(ctx context.Context, ticket *model.SoftwareLaunchTicket, history *model.LaunchHistory) error
	GetLaunchStatusHistory(ctx context.Context, id uint) ([]*model.LaunchHistory, error)
	Update(ctx context.Context, ticket *model.SoftwareLaunchTicket) error
	GetDevicesByLaunchTicketNo(ctx context.Context, launchNo string) ([]model.LaunchDevice, error)
}

type launchRepository struct {
	db *gorm.DB
}

func NewLaunchRepository(db *gorm.DB) LaunchRepository {
	return &launchRepository{db: db}
}

func (r *launchRepository) InitLaunchTicket(ctx context.Context, ticket *model.SoftwareLaunchTicket, history *model.LaunchHistory) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 先清空设备关联，避免自动创建
		devices := ticket.Devices
		ticket.Devices = nil
		// 创建工单
		ticket.ID = 0
		if err := tx.Create(ticket).Error; err != nil {
			return fmt.Errorf("创建软件上线工单失败: %v", err)
		}
		// 创建历史记录
		history.ID = 0
		if err := tx.Create(history).Error; err != nil {
			return fmt.Errorf("创建软件上线工单历史记录失败: %v", err)
		}
		// 手动创建设备记录
		if len(devices) > 0 {
			for i := range devices {
				devices[i].ID = 0
				devices[i].LaunchTicketNo = ticket.LaunchTicketNo
			}
			// 创建设备记录
			if err := tx.Omit("id").CreateInBatches(devices, 100).Error; err != nil {
				return fmt.Errorf("创建设备记录失败: %v", err)
			}
		}
		return nil
	})
}

// GetByID 根据ID获取上线工单
func (r *launchRepository) GetByID(ctx context.Context, id uint) (*model.SoftwareLaunchTicket, error) {
	var ticket model.SoftwareLaunchTicket
	err := r.db.WithContext(ctx).Preload("Devices").Preload("CheckItems").First(&ticket, id).Error
	if err != nil {
		return nil, err
	}
	return &ticket, nil
}

// GetByNo 根据工单号获取上线工单
func (r *launchRepository) GetByNo(ctx context.Context, ticketNo string) (*model.SoftwareLaunchTicket, error) {
	var ticket model.SoftwareLaunchTicket
	err := r.db.WithContext(ctx).Where("launch_ticket_no = ?", ticketNo).First(&ticket).Error
	if err != nil {
		return nil, fmt.Errorf("根据工单号获取工单失败: %w", err)
	}
	return &ticket, nil
}

// List 分页获取软件上线工单列表
func (r *launchRepository) List(ctx context.Context, page, pageSize int, ticketNo, status, title string) ([]*model.SoftwareLaunchTicket, int64, error) {
	var tickets []*model.SoftwareLaunchTicket
	var total int64
	// 快速检查：如果工单编号存在，先快速检查是否有完全匹配的记录
	if ticketNo != "" {
		var count int64
		// 快速检查是否有完全匹配的工单编号
		quickCheck := r.db.WithContext(ctx).Model(&model.SoftwareLaunchTicket{}).
			Where("launch_ticket_no = ?", ticketNo).
			Count(&count)

		if quickCheck.Error != nil {
			return nil, 0, fmt.Errorf("快速检查工单编号失败: %w", quickCheck.Error)
		}
		// 如果找到完全匹配的工单编号，直接查询该工单
		if count > 0 {
			var exactTicket []*model.SoftwareLaunchTicket
			if err := r.db.WithContext(ctx).Model(&model.SoftwareLaunchTicket{}).
				Where("launch_ticket_no = ?", ticketNo).
				Find(&exactTicket).Error; err != nil {
				return nil, 0, fmt.Errorf("查询精确匹配工单失败: %w", err)
			}
			return exactTicket, count, nil
		}
	}

	query := r.db.WithContext(ctx).Model(&model.SoftwareLaunchTicket{})
	// 应用筛选条件
	if ticketNo != "" {
		query = query.Where("offline_ticket_no LIKE ?", "%"+ticketNo+"%")
	}
	// 添加标题筛选条件
	if title != "" {
		query = query.Where("title LIKE ?", "%"+title+"%")
	}

	if status != "" {
		// 验证状态是否有效
		validStatuses := []string{
			common1.StatusWaitingBeforeInstallation,
			common1.StatusBeforeInstallationCompleted,
			common1.StatusWaitingBeforeExpansion,
			common1.StatusBeforeExpansionCompleted,
			common1.StatusWaitingAfterExpansion,
			common1.StatusAfterExpansionCompleted,
			common1.StatusWaitingDeliveryConfirmation,
			common1.StatusDeliveryConfirmationCompleted,
			common1.StatusCompleted}
		isValid := false
		for _, s := range validStatuses {
			if status == s {
				isValid = true
				break
			}
		}
		if isValid {
			query = query.Where("status = ?", status)
		} else {
			// 如果状态无效，记录警告但不应用筛选
			fmt.Printf("警告: 无效的状态值 '%s'，忽略此筛选条件\n", status)
		}
	}
	// 计算总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("计算总数失败: %w", err)
	}

	// 如果总数为0，直接返回空结果
	if total == 0 {
		return tickets, 0, nil
	}
	// 分页查询
	offset := (page - 1) * pageSize
	err = query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&tickets).Error
	if err != nil {
		return nil, 0, fmt.Errorf("查询工单列表失败: %w", err)
	}

	return tickets, total, nil
}

func (r *launchRepository) GetLaunchStatusHistory(ctx context.Context, ticketID uint) ([]*model.LaunchHistory, error) {
	var histories []*model.LaunchHistory

	// 先获取工单信息，获取工单号
	var ticket model.SoftwareLaunchTicket
	if err := r.db.WithContext(ctx).First(&ticket, ticketID).Error; err != nil {
		return nil, fmt.Errorf("获取工单信息失败: %w", err)
	}

	// 使用工单号查询状态历史记录
	err := r.db.WithContext(ctx).
		Where("software_launch_id = ?", ticket.LaunchTicketNo).
		Order("operation_time DESC").
		Find(&histories).Error
	if err != nil {
		return nil, fmt.Errorf("查询状态历史记录失败: %w", err)
	}

	return histories, nil
}

// Update 更新上线工单
func (r *launchRepository) Update(ctx context.Context, ticket *model.SoftwareLaunchTicket) error {
	return r.db.WithContext(ctx).Save(ticket).Error
}

// GetDevicesByLaunchTicketNo 根据工单号获取关联设备列表
func (r *launchRepository) GetDevicesByLaunchTicketNo(ctx context.Context, launchNo string) ([]model.LaunchDevice, error) {
	var devices []model.LaunchDevice
	err := r.db.WithContext(ctx).
		Where("launch_ticket_no = ?", launchNo).
		Find(&devices).Error
	if err != nil {
		return nil, fmt.Errorf("获取工单关联设备失败: %w", err)
	}
	return devices, nil
}
