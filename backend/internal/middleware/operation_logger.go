package middleware

import (
	"backend/internal/infrastructure/database"
	"backend/internal/modules/audit/hooks"
	audit "backend/internal/modules/audit/model"
	"backend/internal/modules/audit/service"
	"bytes"
	"context"
	"encoding/json"
	"io"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// OperationLoggerMiddleware 操作日志中间件
type OperationLoggerMiddleware struct {
	logService service.OperationLogService
	logger     *zap.Logger
	// 排除的路径，不记录日志
	excludePaths []string
	// 数据库连接
	db *gorm.DB
}

// NewOperationLoggerMiddleware 创建操作日志中间件
func NewOperationLoggerMiddleware(logService service.OperationLogService, logger *zap.Logger) *OperationLoggerMiddleware {
	return &OperationLoggerMiddleware{
		logService: logService,
		logger:     logger,
		db:         database.GetDB(), // 获取数据库连接
		excludePaths: []string{
			// 健康检查和指标接口
			"/api/health",
			"/api/metrics",
			"/health",
			"/metrics",

			// 审计日志相关接口
			"/api/v1/audit",                // 审计模块所有接口
			"/audit",                       // 审计模块所有接口
			"/api/v1/audit/operation-logs", // 操作日志接口
			"/audit/operation-logs",        // 操作日志接口
			"/api/v1/audit/change-logs",    // 变更日志接口
			"/audit/change-logs",           // 变更日志接口

			// Swagger文档
			"/swagger",

			// 静态资源
			"/static",
			"/assets",
			"/favicon.ico",

			// 系统监控
			"/debug",

			// 权限检查接口
			"/api/v1/auth/check-permission-status", // 检查权限更新状态接口

			// 登录接口 - 避免记录密码
			// "/api/v1/auth/login",
		},
	}
}

// Handle 处理请求
func (m *OperationLoggerMiddleware) Handle() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否需要跳过日志记录
		path := c.Request.URL.Path
		for _, excludePath := range m.excludePaths {
			if strings.HasPrefix(path, excludePath) {
				c.Next()
				return
			}
		}

		// 特殊处理文件上传接口，不记录请求体
		isFileUpload := strings.Contains(path, "/file/upload") || strings.Contains(path, "/upload")

		// 开始时间
		startTime := time.Now()

		// 获取请求体
		var requestBodyStr string

		if c.Request.Body != nil {
			// 处理请求体
			var bodyBytes []byte
			var err error

			bodyBytes, err = io.ReadAll(c.Request.Body)
			if err != nil {
				m.logger.Error("读取请求体失败", zap.Error(err))
			}
			// 还原请求体，以便后续中间件和控制器可以读取
			c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

			// 根据请求类型处理请求体
			if isFileUpload {
				// 文件上传接口不记录二进制数据
				requestBodyStr = "{\"message\":\"文件上传内容，已省略二进制数据\"}"
			} else if strings.HasSuffix(path, "/api/v1/auth/login") || strings.HasSuffix(path, "/auth/login") {
				// 处理敏感路径，如登录接口，过滤密码信息
				var loginData map[string]interface{}
				if json.Unmarshal(bodyBytes, &loginData) == nil {
					// 如果包含密码字段，则移除或替换为占位符
					if _, exists := loginData["password"]; exists {
						loginData["password"] = "******"
					}
					// 重新序列化
					if maskedData, err := json.Marshal(loginData); err == nil {
						requestBodyStr = string(maskedData)
					}
				} else {
					requestBodyStr = string(bodyBytes)
				}
			} else {
				// 其他请求，直接使用请求体
				requestBodyStr = string(bodyBytes)
			}
		}

		// 使用自定义响应写入器捕获响应内容
		responseBody := &bytes.Buffer{}
		writer := &responseBodyWriter{
			ResponseWriter: c.Writer,
			body:           responseBody,
		}
		c.Writer = writer

		// 继续处理请求
		c.Next()

		// 结束时间和耗时
		endTime := time.Now()
		latency := endTime.Sub(startTime)

		// 获取操作模块和操作类型
		module, operation := extractModuleAndOperation(c)

		// 获取控制器方法名
		controllerMethod := extractControllerMethod(c)

		// 获取用户信息
		userID, _ := c.Get("userID")     // 从userID获取（认证中间件中设置的key）
		userInfo, _ := c.Get("userInfo") // 获取用户信息对象

		var userIDValue uint
		var usernameValue string

		// 处理userID
		if id, ok := userID.(uint); ok {
			userIDValue = id
		} else if idFloat, ok := userID.(float64); ok {
			userIDValue = uint(idFloat)
		} else if idStr, ok := userID.(string); ok {
			idInt, err := strconv.ParseUint(idStr, 10, 32)
			if err != nil {
				m.logger.Warn("解析用户ID失败", zap.String("userId", idStr), zap.Error(err))
			} else {
				userIDValue = uint(idInt)
			}
		}

		// 从userInfo中获取真实姓名作为用户名
		if info, ok := userInfo.(map[string]interface{}); ok {
			if realName, exists := info["realName"]; exists && realName != nil {
				if name, ok := realName.(string); ok && name != "" {
					usernameValue = name
				}
			}
		}

		// 如果用户名为空，尝试从请求中获取
		if usernameValue == "" {
			// 从token中获取username（如果存在）
			if claims, exists := c.Get("claims"); exists {
				if claimsMap, ok := claims.(map[string]interface{}); ok {
					if username, exists := claimsMap["username"]; exists {
						if name, ok := username.(string); ok {
							usernameValue = name
						}
					}
				}
			}
		}

		// 处理请求体和响应体，避免过大
		requestBodyStr = limitTextSize(requestBodyStr, 65000)

		// 处理响应体
		var responseBodyStr string
		// GET请求或文件上传接口不记录响应体
		if c.Request.Method == "GET" || isFileUpload {
			responseBodyStr = ""
		} else {
			responseBodyStr = limitTextSize(responseBody.String(), 65000)

			// 检测响应体是否包含二进制数据
			if containsBinaryData(responseBodyStr) {
				responseBodyStr = "{\"message\":\"响应包含二进制数据，已省略\"}"
			}
		}

		// 创建操作日志
		operationLog := &audit.OperationLog{
			Module:        module,
			Operation:     operation,
			UserID:        userIDValue,
			Username:      usernameValue,
			IPAddress:     c.ClientIP(),
			UserAgent:     c.Request.UserAgent(),
			RequestURL:    path,
			RequestMethod: c.Request.Method,
			OperationFunc: controllerMethod,
			RequestBody:   requestBodyStr,
			ResponseBody:  responseBodyStr,
			Status:        getOperationStatus(c.Writer.Status()),
			ExecutionTime: latency.Milliseconds(),
			OperationTime: startTime,
		}

		// 创建带有用户信息的上下文
		logCtx := hooks.WithAuditContext(
			context.Background(),
			userIDValue,
			usernameValue,
			c.ClientIP(),
			c.Request.UserAgent(),
		)

		// 异步记录操作日志，避免影响主请求
		go func(ctx context.Context, log *audit.OperationLog) {
			if err := m.logService.CreateLog(ctx, log); err != nil {
				m.logger.Error("记录操作日志失败",
					zap.String("path", path),
					zap.Error(err),
				)
			}
		}(logCtx, operationLog)

		// 重要：处理数据库上下文，防止GORM钩子产生重复记录
		// 1. 标记该操作为已记录，GORM钩子应跳过日志记录
		// 2. 设置父操作信息，以便GORM钩子能继承用户信息

		// 为数据库操作创建新的上下文，包含所有需要的信息
		dbCtx := hooks.WithAuditContext(
			context.Background(),
			userIDValue,
			usernameValue,
			c.ClientIP(),
			c.Request.UserAgent(),
		)

		// 设置父操作信息
		dbCtx = hooks.WithParentOperationInfo(
			dbCtx,
			userIDValue,
			usernameValue,
		)

		// 明确标记该操作已被记录，避免GORM钩子重复记录
		dbCtx = context.WithValue(dbCtx, hooks.ParentOperationRecordedKey, true)

		// 更新GORM默认数据库连接的上下文
		// 这确保所有后续的数据库操作都会继承这个上下文
		m.db = m.db.WithContext(dbCtx)
		database.SetDB(m.db)

		// 同时更新请求上下文，以便控制器中的数据库操作也能继承这些信息
		c.Request = c.Request.WithContext(dbCtx)
	}
}

// 自定义响应写入器，用于捕获响应内容
type responseBodyWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *responseBodyWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

func (w *responseBodyWriter) WriteString(s string) (int, error) {
	w.body.WriteString(s)
	return w.ResponseWriter.WriteString(s)
}

// 提取模块和操作
func extractModuleAndOperation(c *gin.Context) (string, string) {
	path := c.Request.URL.Path

	// 去除开头的斜杠
	path = strings.TrimPrefix(path, "/")

	parts := strings.Split(path, "/")

	// 默认值
	module := "未知"
	operation := "其他"

	if len(parts) >= 1 && parts[0] != "" {
		module = parts[0]
	}

	if len(parts) >= 2 && parts[1] != "" {
		operation = parts[1]
	}

	// 如果有更多层级，则组合后面的部分
	if len(parts) > 2 {
		operation = strings.Join(parts[1:], "/")
	}

	// 也可以从请求头或请求参数中获取更精确的模块和操作信息
	if moduleHeader := c.GetHeader("X-Module"); moduleHeader != "" {
		module = moduleHeader
	}

	if operationHeader := c.GetHeader("X-Operation"); operationHeader != "" {
		operation = operationHeader
	}

	return module, operation
}

// 提取控制器方法名
func extractControllerMethod(c *gin.Context) string {
	// 从请求头获取操作方法，如果前端有传递
	if method := c.GetHeader("X-Operation-Method"); method != "" {
		return method
	}

	// 默认返回请求路径
	return c.Request.URL.Path
}

// 获取操作状态
func getOperationStatus(statusCode int) string {
	if statusCode >= 200 && statusCode < 400 {
		return "正常"
	}
	return "异常"
}

// 限制文本大小
func limitTextSize(text string, maxSize int) string {
	if len(text) <= maxSize {
		return text
	}

	// 创建简化版数据
	simplified := map[string]interface{}{
		"truncated": true,
		"message":   "数据过大已截断",
		"preview":   text[:1000] + "...",
	}
	data, err := json.Marshal(simplified)
	if err != nil {
		return "{\"truncated\":true,\"message\":\"数据过大已截断且JSON序列化失败\"}"
	}
	return string(data)
}

// containsBinaryData 检查字符串是否包含二进制数据特征
func containsBinaryData(s string) bool {
	// 检查是否包含常见的二进制文件头标记
	binarySignatures := []string{
		"\\x89PNG",        // PNG 文件头
		"\x89PNG",         // PNG 文件头 (未转义)
		"\\xFF\\xD8\\xFF", // JPEG 文件头
		"\xFF\xD8\xFF",    // JPEG 文件头 (未转义)
		"GIF8",            // GIF 文件头
		"PK\\x03\\x04",    // ZIP 文件头
		"PK\x03\x04",      // ZIP 文件头 (未转义)
		"%PDF",            // PDF 文件头
		"data:image",      // Data URL for images
	}

	for _, sig := range binarySignatures {
		if strings.Contains(s, sig) {
			return true
		}
	}

	// 检查是否包含过多的不可打印字符
	nonPrintableCount := 0
	for _, r := range s {
		if r < 32 && r != 9 && r != 10 && r != 13 { // 不是tab、LF、CR的控制字符
			nonPrintableCount++
			if nonPrintableCount > 5 { // 允许少量不可打印字符
				return true
			}
		}
	}

	return false
}
