package middleware

import (
	"backend/internal/infrastructure/auth"
	"backend/internal/infrastructure/database"
	"backend/internal/modules/user/model"
	"net/http"

	"github.com/gin-gonic/gin"
)

func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取authorization header
		tokenString := c.<PERSON>("Authorization")

		// 验证token格式
		if tokenString == "" || len(tokenString) < 7 || tokenString[0:7] != "Bearer " {
			c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "权限不足"})
			c.Abort()
			return
		}
		tokenString = tokenString[7:]

		// 使用统一的方法获取JWT密钥
		secretKey := auth.GetJWTSecretKey()

		// 验证token有效性
		claims, err := auth.ParseToken(tokenString, secretKey)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "无效的token"})
			c.Abort()
			return
		}

		// 从claims中获取userId
		userId := claims.UserID
		DB := database.GetDB()
		var user model.User
		DB.First(&user, userId)

		// 验证用户是否存在
		if user.ID == 0 {
			c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "权限不足"})
			c.Abort()
			return
		}

		// 用户存在，只保存必要的信息
		c.Set("userID", user.ID)
		// 确保角色以标准的[]string类型保存
		roles := make([]string, len(user.Roles))
		copy(roles, user.Roles)
		c.Set("userRoles", roles)
		c.Set("userInfo", map[string]interface{}{
			"realName": user.RealName,
		})

		c.Next()
	}
}
