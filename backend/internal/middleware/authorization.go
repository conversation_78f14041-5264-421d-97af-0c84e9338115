package middleware

import (
	"backend/internal/config"
	"backend/internal/infrastructure/casbin"
	"backend/internal/modules/system/model"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// 查找最佳匹配的路径和权限码
func findBestPathMatch(relativePath, method string) (string, bool) {
	// 检查精确匹配
	authCode, exists := config.GetAuthCode(relativePath, method)
	if exists {
		return authCode, true
	}

	// 获取所有配置的路径
	allPaths := config.GetAuthPathMethodMap()
	var longestMatchPath string
	var longestMatchLength int

	// 查找最长匹配的通配符路径
	for configPath, methods := range allPaths {
		if _, methodExists := methods[method]; !methodExists {
			continue
		}

		if strings.HasSuffix(configPath, "/**") {
			prefix := strings.TrimSuffix(configPath, "/**")
			if strings.HasPrefix(relativePath, prefix) && len(prefix) > longestMatchLength {
				longestMatchPath = configPath
				longestMatchLength = len(prefix)
			}
		}
	}

	// 如果找到匹配的通配符路径
	if longestMatchPath != "" {
		return config.GetAuthCode(longestMatchPath, method)
	}

	return "", false
}

// AuthorizeWithCasbin 使用Casbin进行权限验证的中间件
func AuthorizeWithCasbin(casbinService *casbin.CasbinService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取请求路径和方法
		requestPath := c.FullPath() // 使用FullPath获取路由模式而不是实际路径
		if requestPath == "" {
			requestPath = c.Request.URL.Path
		}
		method := c.Request.Method

		// 处理掉API基础路径前缀
		relativePath := requestPath
		if len(requestPath) > len(config.APIBasePath) && requestPath[:len(config.APIBasePath)] == config.APIBasePath {
			relativePath = requestPath[len(config.APIBasePath):]
		}

		// 检查是否在白名单中
		if config.IsInWhiteList(relativePath) {
			//fmt.Printf("【权限检查】路径: %s 在白名单中，直接放行\n", requestPath)
			c.Next()
			return
		}

		// 查找最佳匹配的权限码
		authCode, methodExists := findBestPathMatch(relativePath, method)

		// 如果找不到匹配的权限码
		if !methodExists {
			// 修改此处，取消拒绝访问，改为默认放行未配置的方法
			//fmt.Printf("【权限检查】警告: 路径 %s 的 %s 方法未配置，默认放行\n", requestPath, method)
			c.Next()
			return
		}

		// 从上下文获取角色
		rolesVal, exists := c.Get("userRoles")
		if !exists {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "未获取到用户角色"})
			return
		}

		// 获取角色列表
		var roles []string

		// 尝试不同的类型转换
		if rolesArray, ok := rolesVal.([]string); ok {
			// 字符串数组
			roles = rolesArray
		} else if stringArray, ok := rolesVal.(model.StringArray); ok {
			// 自定义StringArray类型
			roles = []string(stringArray)
		} else if anyArray, ok := rolesVal.([]interface{}); ok {
			// []interface{}类型
			for _, v := range anyArray {
				if s, ok := v.(string); ok {
					roles = append(roles, s)
				}
			}
		} else if role, ok := rolesVal.(string); ok {
			// 单一字符串
			roles = []string{role}
		} else {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": "用户角色格式错误"})
			return
		}

		// 检查是否有参数条件
		paramConditions, hasParamConditions := config.GetParamConditions(relativePath, method)

		// 如果有参数条件，需要检查参数
		if hasParamConditions && len(paramConditions) > 0 && (method == "POST" || method == "PUT" || method == "PATCH") {
			// 检查Content-Type
			contentType := c.GetHeader("Content-Type")

			// 创建一个requestParams映射来存储参数
			requestParams := make(map[string]interface{})

			// 处理不同类型的请求
			if strings.Contains(contentType, "multipart/form-data") {
				// 处理文件上传请求
				err := c.Request.ParseMultipartForm(32 << 20) // 32MB
				if err != nil {
					fmt.Printf("【权限检查】警告: 解析multipart表单失败 %v\n", err)
				} else {
					// 从表单中获取字段
					for key, values := range c.Request.PostForm {
						if len(values) > 0 {
							requestParams[key] = values[0]
						}
					}
					//fmt.Printf("【权限检查】从multipart表单获取参数: %v\n", requestParams)
				}
			} else if strings.Contains(contentType, "application/json") {
				// 保存请求体内容，以便后续处理
				bodyBytes, err := io.ReadAll(c.Request.Body)
				if err != nil {
					c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "读取请求体失败"})
					return
				}

				// 恢复请求体以便后续处理
				c.Request.Body = io.NopCloser(bytes.NewReader(bodyBytes))

				// 解析请求体
				if err := json.Unmarshal(bodyBytes, &requestParams); err != nil {
					fmt.Printf("【权限检查】警告: 无法解析JSON请求体 %v，使用基础权限码: %s\n", err, authCode)
				}
			} else {
				// 处理普通表单请求
				err := c.Request.ParseForm()
				if err != nil {
					fmt.Printf("【权限检查】警告: 解析表单失败 %v\n", err)
				} else {
					for key, values := range c.Request.PostForm {
						if len(values) > 0 {
							requestParams[key] = values[0]
						}
					}
				}
			}

			// 检查参数条件
			for _, condition := range paramConditions {
				matched := true

				// 检查每个参数是否匹配
				for paramKey, paramValue := range condition.Params {
					// 获取请求中的参数值
					reqValue, exists := requestParams[paramKey]
					if !exists {
						matched = false
						break
					}

					// 将请求值转换为字符串进行比较
					var reqValueStr string
					if str, ok := reqValue.(string); ok {
						reqValueStr = str
					} else {
						// 尝试将其他类型转换为字符串
						reqValueStr = fmt.Sprintf("%v", reqValue)
					}

					// 比较参数值
					if reqValueStr != paramValue {
						matched = false
						break
					}
				}

				// 如果所有参数都匹配，使用此条件的权限码
				if matched {
					authCode = condition.AuthCode
					//fmt.Printf("【权限检查】匹配参数条件: %v, 使用权限码: %s\n", condition.Params, authCode)
					break
				}
			}
		}

		// 检查权限，将HTTP方法作为act参数传递
		hasPermission := casbinService.CheckPermission(roles, authCode, method)

		if hasPermission {
			c.Next()
		} else {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "无权限访问,请联系管理员"})
			return
		}
	}
}
