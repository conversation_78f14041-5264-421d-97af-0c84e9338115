package middleware

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
)

// RequestLoggerMiddleware 创建请求日志中间件
func RequestLoggerMiddleware(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 开始时间
		start := time.Now()
		path := c.Request.URL.Path

		// 添加请求ID
		requestID := generateRequestID()
		c.Set("request_id", requestID)

		// 处理请求
		c.Next()

		// 结束时间
		cost := time.Since(start)
		status := c.Writer.Status()

		// 忽略一些常见的404请求
		if status == 404 {
			ignorePaths := []string{"/favicon.ico", "/robots.txt", "/apple-touch-icon.png", "/apple-touch-icon-precomposed.png"}
			for _, ignorePath := range ignorePaths {
				if path == ignorePath {
					return
				}
			}
		}

		// 只记录错误和慢请求
		if status >= 500 {
			logger.Error("HTTP错误",
				zap.String("req_id", requestID),
				zap.String("method", c.Request.Method),
				zap.String("path", path),
				zap.Int("status", status),
				zap.Duration("time", cost))
		} else if status >= 400 {
			logger.Warn("HTTP警告",
				zap.String("req_id", requestID),
				zap.String("method", c.Request.Method),
				zap.String("path", path),
				zap.Int("status", status))
		} else if cost > time.Millisecond*500 {
			logger.Info("慢请求",
				zap.String("req_id", requestID),
				zap.String("method", c.Request.Method),
				zap.String("path", path),
				zap.Duration("time", cost))
		}
	}
}

// 生成唯一请求ID
func generateRequestID() string {
	id, err := uuid.NewRandom()
	if err != nil {
		// 如果生成UUID失败，使用时间戳作为备选
		return fmt.Sprintf("fallback-%d", time.Now().UnixNano())
	}
	return id.String()
}
