version: "2"
run:
  # 并发数，默认为 CPU 核心数
  concurrency: 4
  # 要检查的 Go 版本
  go: "1.23"
  # 构建标签
  build-tags:
    - integration
  # 是否包含测试文件
  tests: true

linters:
  enable:
    # 安全检查 - 重要！
    - gosec
    # 检查 sql.Rows.Err
    - rowserrcheck
    # 检查 sql.Rows 和 sql.Stmt 是否关闭
    - sqlclosecheck
    # 检查不必要的类型转换
    - unconvert
    # 检查浪费的赋值
    - wastedassign

    # 最关键的 linters
    - errcheck      # 检查错误处理 - 非常重要！
    - govet         # Go 官方工具 - 必须启用
    - ineffassign   # 检测无效赋值
    - staticcheck   # 静态分析 - 非常重要！
    - unused        # 检测未使用的变量/函数/类型

    # 可选但推荐的检查 - 需要时可以取消注释
    #- bodyclose     # 检查 HTTP response body 是否关闭
    #- contextcheck  # 检查 context.Context 使用
    #- durationcheck # 检查 time.Duration 的错误用法
    #- errorlint     # 检查错误处理
    #- goconst       # 检查可以用常量替换的重复字符串
    #- misspell      # 检查拼写错误
    #- nilerr        # 检查 nil 错误返回
    #- nilnil        # 检查同时返回 nil 指针和 nil 错误

    # 代码质量和风格检查 - 团队规范需要时启用
    #- revive        # golint 的替代品
    #- stylecheck    # 样式检查
    #- gofumpt       # 更严格的 gofmt
    #- gocritic      # 提供多种检查
    #- whitespace    # 检查空白符

    # 复杂度检查 - 代码重构时启用
    #- funlen        # 检查函数长度
    #- gocognit      # 检查认知复杂度
    #- gocyclo       # 检查圈复杂度
    #- cyclop        # 检查函数和包的圈复杂度
    #- nestif        # 检查嵌套 if 语句

    # 其他可选检查
    #- asasalint     # 检查 []any 的传递
    #- asciicheck    # 检查非 ASCII 字符
    #- bidichk       # 检查危险的 unicode 字符序列
    #- copyloopvar   # 检查循环变量的拷贝
    #- dupl          # 检查重复代码
    #- errname       # 检查错误命名
    #- exhaustive    # 检查 switch 语句的穷尽性
    #- forbidigo     # 禁用特定标识符
    #- gci           # 控制导入顺序
    #- godot         # 检查注释是否以句点结尾
    #- goheader      # 检查文件头
    #- mnd           # 检查魔术数字
    #- gomoddirectives # 检查 go.mod 指令
    #- gomodguard    # 检查模块依赖
    #- goprintffuncname # 检查 printf 函数命名
    #- lll           # 检查行长度
    #- loggercheck   # 检查日志参数
    #- makezero      # 检查 slice 初始化
    #- nakedret      # 检查裸返回
    #- noctx         # 检查 HTTP 请求是否传递了 context
    #- nolintlint    # 检查 nolint 指令的正确性
    #- nonamedreturns # 禁用命名返回值
    #- nosprintfhostport # 检查 sprintf 主机端口
    #- predeclared   # 检查预声明标识符的重新声明
    #- promlinter    # 检查 Prometheus 指标
    #- reassign      # 检查包级变量的重新赋值
    #- usetesting    # 检查测试中环境变量的使用
    #- testableexamples # 检查示例测试
    #- tparallel     # 检查 t.Parallel() 的使用
    #- unparam       # 检查未使用的函数参数
    #- usestdlibvars # 检查是否使用标准库变量

  disable:
    # 可能过于严格
    - containedctx
    # 需要额外配置
    - depguard
    # 允许空白标识符
    - dogsled
    # 错误处理风格选择
    - err113
    # 有时需要强制类型断言
    - forcetypeassert
    # 接口返回风格选择
    - ireturn
    # 变量名长度检查可能过于严格
    - varnamelen
    # 错误包装风格选择
    - wrapcheck

linters-settings:
  # 错误检查配置
  errcheck:
    # 检查类型断言
    check-type-assertions: true
    # 检查空白赋值
    check-blank: true
    # 排除的函数调用
    exclude-functions:
      - fmt:.*
      - io/ioutil:^Read.*

  # 安全检查配置
  gosec:
    excludes:
      # 审计错误不检查
      - G104
    severity: medium
    confidence: medium

  # 其他配置 - 需要时取消注释
  # forbidigo:
  #   # 禁用的模式
  #   forbid:
  #     - ^print.*$
  #     - 'fmt\.Print.*'

  # funlen:
  #   lines: 80
  #   statements: 40

  # gci:
  #   sections:
  #     - standard # 标准库
  #     - default  # 默认包
  #     - prefix(github.com/your-org) # 组织前缀

  # gocognit:
  #   min-complexity: 15

  # goconst:
  #   min-len: 2
  #   min-occurrences: 3

  # gocritic:
  #   enabled-tags:
  #     - diagnostic
  #     - experimental
  #     - opinionated
  #     - performance
  #     - style
  #   disabled-checks:
  #     - dupImport # https://github.com/go-critic/go-critic/issues/845
  #     - ifElseChain
  #     - octalLiteral
  #     - whyNoLint

  # gocyclo:
  #   min-complexity: 15

  # goheader:
  #   values:
  #     const:
  #       COMPANY: YourCompany
  #     regexp:
  #       YEAR: 20\d\d
  #   template: |-
  #     Copyright (c) {{ YEAR }} {{ COMPANY }}
  #     Licensed under the Apache License, Version 2.0

  # mnd:
  #   checks:
  #     - argument
  #     - case
  #     - condition
  #     - operation
  #     - return
  #   ignored-numbers:
  #     - '0'
  #     - '1'
  #     - '2'
  #     - '3'
  #   ignored-functions:
  #     - args.Error
  #     - strconv.FormatInt
  #     - strconv.ParseInt

  # lll:
  #   line-length: 120

  # misspell:
  #   locale: US

  # nakedret:
  #   max-func-lines: 30

  # nestif:
  #   min-complexity: 4

  # nolintlint:
  #   allow-leading-space: true # 允许前导空格
  #   allow-unused: false # 报告未使用的 nolint 指令
  #   require-explanation: false # 不要求解释
  #   require-specific: false # 不要求具体的 linter

  # revive:
  #   rules:
  #     - name: blank-imports
  #     - name: context-as-argument
  #     - name: context-keys-type
  #     - name: dot-imports
  #     - name: error-return
  #     - name: error-strings
  #     - name: error-naming
  #     - name: exported
  #     - name: if-return
  #     - name: increment-decrement
  #     - name: var-naming
  #     - name: var-declaration
  #     - name: package-comments
  #     - name: range
  #     - name: receiver-naming
  #     - name: time-naming
  #     - name: unexported-return
  #     - name: indent-error-flow
  #     - name: errorf
  #     - name: empty-block
  #     - name: superfluous-else
  #     - name: unused-parameter
  #     - name: unreachable-code
  #     - name: redefines-builtin-id

  # stylecheck:
  #   checks: ["all", "-ST1000", "-ST1003", "-ST1016", "-ST1020", "-ST1021", "-ST1022"]

  # unparam:
  #   check-exported: false

issues:
  # 最大问题数量，0 表示无限制
  max-issues-per-linter: 0
  max-same-issues: 0
  # 新问题检查（仅在 CI 中有用）
  new: false
  # 修复问题
  fix: false
  # 排除规则
  exclude-rules:
    # 生成的代码
    - path: '.*\.pb\.go$'
      linters:
        - errcheck
        - gosec
    - path: '.*\.gen\.go$'
      linters:
        - errcheck
        - gosec
    # 测试文件中的某些检查
    - path: _test\.go
      linters:
        - errcheck  # 测试文件中的错误处理可能不严格
        - gosec     # 测试文件可以放宽安全检查
    # main 文件中的某些检查
    - path: cmd/
      linters:
        - gosec  # main 文件可能需要一些特殊操作

  # 排除的文件和目录
  exclude-dirs:
    - vendor
    - third_party
    - testdata
    - examples
    - Godeps
    - builtin

  # 排除的文件模式
  exclude-files:
    - '.*\.pb\.go$'
    - '.*\.gen\.go$'

# 输出配置
output:
  # 输出格式：colored-line-number|line-number|json|colored-tab|tab|checkstyle|code-climate|junit-xml|github-actions|teamcity
  format: colored-line-number
  # 打印 linter 名称
  print-linter-name: true
  # 打印欢迎信息
  print-welcome: false
