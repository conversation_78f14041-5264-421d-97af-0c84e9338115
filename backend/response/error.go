package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// CustomError 自定义错误
type CustomError struct {
	Message    string
	StatusCode int
}

// Error 实现error接口
func (e *CustomError) Error() string {
	return e.Message
}

// NewError 创建自定义错误
func NewError(message string) error {
	return &CustomError{
		Message:    message,
		StatusCode: http.StatusInternalServerError, // 默认为内部服务器错误
	}
}

// NewErrorWithStatusCode 创建带状态码的自定义错误
func NewErrorWithStatusCode(message string, statusCode int) error {
	return &CustomError{
		Message:    message,
		StatusCode: statusCode,
	}
}

// FailWithError 使用自定义错误响应
func FailWithError(c *gin.Context, err error) {
	if customErr, ok := err.(*CustomError); ok {
		Fail(c, customErr.StatusCode, customErr.Message)
		return
	}
	// 默认视为内部服务器错误
	Fail(c, http.StatusInternalServerError, err.Error())
}
