# 基于项目的维修单通知系统

## 功能概述

实现了根据服务器SN所属项目，将维修单待接单通知发送到不同webhook地址的功能。系统能够自动识别设备所属项目，并使用对应的飞书群进行通知。

## 实现内容

### 1. 配置结构扩展

**新增项目webhook配置结构** (`configs/config.go`)：
```go
// ProjectWebhookConfig 项目专用webhook配置
type ProjectWebhookConfig struct {
    WebhookURL string `mapstructure:"webhook_url" json:"webhook_url"`
    Secret     string `mapstructure:"secret" json:"secret"`
}

// ProjectWebhookMappings 项目webhook映射配置
type ProjectWebhookMappings map[string]ProjectWebhookConfig

// FeishuConfig 飞书机器人配置
type FeishuConfig struct {
    // ... 其他配置

    // 项目专用webhook映射配置
    ProjectWebhooks ProjectWebhookMappings `mapstructure:"project_webhooks" json:"project_webhooks"`
}
```

**配置文件示例**：
```yaml
feishu:
  # 项目专用webhook映射配置
  project_webhooks:
    "project_a":
      webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/project-a-webhook"
      secret: "project-a-secret"
    "project_b":
      webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/project-b-webhook"
      secret: "project-b-secret"
    "default":
      webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/default-webhook"
      secret: "default-secret"
```

### 2. 设备项目查询服务

**新增设备项目服务** (`internal/modules/ticket/service/device_project_service.go`)：
```go
// DeviceProjectService 设备项目查询服务接口
type DeviceProjectService interface {
    // GetProjectBySN 根据设备SN获取项目信息
    GetProjectBySN(ctx context.Context, sn string) (string, error)
}

// deviceProjectService 设备项目查询服务实现
type deviceProjectService struct {
    deviceSearchService asset.DeviceSearchService
}

// GetProjectBySN 根据设备SN获取项目信息
func (s *deviceProjectService) GetProjectBySN(ctx context.Context, sn string) (string, error) {
    // 通过设备搜索服务查找设备信息
    searchResult, err := s.deviceSearchService.SearchBySN(ctx, sn)
    if err != nil {
        return "", err
    }

    // 优先从资源信息中获取项目
    if searchResult.Resource != nil && searchResult.Resource.Project != "" {
        return searchResult.Resource.Project, nil
    }

    // 如果资源信息中没有项目，尝试从设备信息中获取
    if searchResult.Device != nil && searchResult.Device.Resource != nil && searchResult.Device.Resource.Project != "" {
        return searchResult.Device.Resource.Project, nil
    }

    // 如果都没有找到项目信息，返回空字符串（将使用默认webhook）
    return "", nil
}
```

### 3. 飞书通知器扩展

**扩展FeishuNotifier结构体**：
```go
type FeishuNotifier struct {
    WebhookURL        string
    Secret            string
    DetailUrlTemplate string
    Logger            *zap.Logger
    // 维修单相关配置
    RepairWebhookURL        string
    RepairSecret            string
    RepairDetailUrlTemplate string
    // 项目专用webhook映射
    ProjectWebhooks configs.ProjectWebhookMappings
}
```

**新增项目webhook配置获取方法**：
```go
// GetProjectWebhookConfig 根据项目获取webhook配置
func (f *FeishuNotifier) GetProjectWebhookConfig(project string) (webhookURL, secret string) {
    // 如果项目为空，使用默认配置
    if project == "" {
        project = "default"
    }

    // 查找项目专用配置
    if config, exists := f.ProjectWebhooks[project]; exists {
        if config.WebhookURL != "" && config.Secret != "" {
            return config.WebhookURL, config.Secret
        }
    }

    // 如果没有找到项目专用配置，尝试使用默认配置
    if defaultConfig, exists := f.ProjectWebhooks["default"]; exists {
        if defaultConfig.WebhookURL != "" && defaultConfig.Secret != "" {
            return defaultConfig.WebhookURL, defaultConfig.Secret
        }
    }

    // 最后回退到维修单配置
    return f.RepairWebhookURL, f.RepairSecret
}
```

**新增支持自定义webhook的通知方法**：
```go
// SendRepairTicketWaitingAcceptNotificationWithWebhook 发送维修单待接单通知（支持自定义webhook）
func (f *FeishuNotifier) SendRepairTicketWaitingAcceptNotificationWithWebhook(
    repairTicketID, repairTicketNo, deviceSN, faultTicketNo string,
    reporterName string, cabinetName, roomName, dataCenterName string,
    webhookURL, secret string
) error {
    // 构建消息内容
    // 生成自定义签名
    // 发送到指定webhook
}
```

### 4. 维修单活动集成

**修改维修单待接单通知活动**：
```go
// SendRepairTicketWaitingAcceptNotificationActivity 发送维修单待接单通知
func SendRepairTicketWaitingAcceptNotificationActivity(ctx context.Context, repairTicketID uint) error {
    // ... 获取维修单和设备信息

    // 根据设备SN获取项目信息
    var project string
    if deviceProjectServiceImpl != nil && deviceSN != "未知设备" {
        projectInfo, err := deviceProjectServiceImpl.GetProjectBySN(ctx, deviceSN)
        if err != nil {
            logger.Warn("获取设备项目信息失败，使用默认webhook",
                zap.Error(err),
                zap.String("deviceSN", deviceSN))
        } else {
            project = projectInfo
            logger.Info("成功获取设备项目信息",
                zap.String("deviceSN", deviceSN),
                zap.String("project", project))
        }
    }

    // 根据项目获取对应的webhook配置
    webhookURL, secret := feishuNotifier.GetProjectWebhookConfig(project)
    logger.Info("使用项目专用webhook配置",
        zap.String("project", project),
        zap.String("webhookURL", webhookURL))

    // 发送飞书通知（使用项目专用的webhook配置）
    err = feishuNotifier.SendRepairTicketWaitingAcceptNotificationWithWebhook(
        fmt.Sprintf("%d", repairTicketID),
        repairTicket.TicketNo,
        deviceSN,
        faultTicketNo,
        reporterName,
        cabinetName,
        roomName,
        dataCenterName,
        webhookURL,
        secret,
    )

    // ... 错误处理
}
```

## 工作流程

### 1. 维修单创建流程
1. **维修单创建** - 用户创建维修单，指定设备SN
2. **设备信息查询** - 系统根据设备SN查询设备信息
3. **项目信息获取** - 从设备资源信息中提取项目信息
4. **Webhook配置获取** - 根据项目获取对应的webhook配置
5. **通知发送** - 使用项目专用webhook发送通知

### 2. 项目映射逻辑
```
设备SN → 设备信息 → 资源信息 → 项目信息 → Webhook配置 → 飞书通知
```

### 3. 配置优先级
1. **项目专用配置** - 优先使用项目对应的webhook配置
2. **默认配置** - 如果项目配置不存在，使用default配置
3. **维修单配置** - 最后回退到原有的维修单webhook配置

## 配置示例

### 开发环境配置
```yaml
feishu:
  # 项目专用webhook映射配置
  project_webhooks:
    "project_a":
      webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/project-a-dev"
      secret: "project-a-dev-secret"
    "project_b":
      webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/project-b-dev"
      secret: "project-b-dev-secret"
    "default":
      webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/default-dev"
      secret: "default-dev-secret"
```

### 生产环境配置
```yaml
feishu:
  # 项目专用webhook映射配置
  project_webhooks:
    "project_a":
      webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/project-a-prod"
      secret: "project-a-prod-secret"
    "project_b":
      webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/project-b-prod"
      secret: "project-b-prod-secret"
    "default":
      webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/default-prod"
      secret: "default-prod-secret"
```

## 使用场景

### 场景1：项目A的设备故障
- **设备SN**: SERVER-A-001
- **项目**: project_a
- **通知群**: 项目A专用飞书群
- **Webhook**: project-a-webhook

### 场景2：项目B的设备故障
- **设备SN**: SERVER-B-001
- **项目**: project_b
- **通知群**: 项目B专用飞书群
- **Webhook**: project-b-webhook

### 场景3：未知项目的设备故障
- **设备SN**: SERVER-UNKNOWN-001
- **项目**: (空)
- **通知群**: 默认飞书群
- **Webhook**: default-webhook

# 维修单待接单通知项目匹配功能实现总结

## 功能概述

成功实现了维修单待接单通知的项目匹配功能，现在系统可以根据设备SN自动识别项目，并发送通知到对应项目的飞书群。

## 核心设计

### 1. 配置简化
- **删除了** `repair_webhook_url` 和 `repair_secret` 配置参数
- **保留了** `repair_ticket_detail_url_template` 配置
- **新增了** `repair_project_webhooks` 配置，支持项目专用webhook映射

### 2. 配置结构
```yaml
feishu:
  repair_ticket_detail_url_template: "http://localhost:5666/hardware-order/%s"

  # 维修单项目专用webhook映射配置（仅用于维修单待接单通知）
  repair_project_webhooks:
    "cloud17":
      webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/cloud17-repair-webhook"
      secret: "cloud17-repair-secret"
    "cloud27":
      webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/298cfae5-e1e3-4a5a-8c6e-6f915287b9df"
      secret: "itghARXHCCLDrXmsP99fLc"
    "default":
      webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/f0622c72-701a-4eec-a880-87b88ad47a47"
      secret: "JEYx81Iq4k1SMAMXUeM6q"
```

### 3. 工作流程
1. **设备项目识别**：根据设备SN查询设备关联的资源信息，获取项目名称
2. **项目配置匹配**：根据项目名称查找对应的webhook配置
3. **回退机制**：如果没有找到项目专用配置，使用 `default` 配置
4. **通知发送**：使用匹配到的webhook配置发送通知

## 主要修改内容

### 1. 配置文件修改

**开发环境** (`configs/config.dev.yaml`)：
```yaml
# 删除
# repair_webhook_url: "..."
# repair_secret: "..."

# 新增
repair_project_webhooks:
  "cloud17": { webhook_url: "...", secret: "..." }
  "cloud27": { webhook_url: "...", secret: "..." }
  "default": { webhook_url: "...", secret: "..." }
```

**生产环境** (`configs/config.prod.yaml`)：
```yaml
# 同样的修改结构
```

### 2. 配置结构体修改

**configs/config.go**：
```go
// 新增类型定义
type ProjectWebhookConfig struct {
    WebhookURL string `mapstructure:"webhook_url" json:"webhook_url"`
    Secret     string `mapstructure:"secret" json:"secret"`
}

type ProjectWebhookMappings map[string]ProjectWebhookConfig

// FeishuConfig结构体修改
type FeishuConfig struct {
    // 删除
    // RepairWebhookURL string
    // RepairSecret     string

    // 保留
    RepairTicketDetailUrlTemplate string `mapstructure:"repair_ticket_detail_url_template"`

    // 新增
    RepairProjectWebhooks ProjectWebhookMappings `mapstructure:"repair_project_webhooks"`
}
```

### 3. FeishuNotifier重构

**internal/common/utils/notifier/feishu_notifier.go**：

**结构体简化**：
```go
type FeishuNotifier struct {
    WebhookURL        string
    Secret            string
    DetailUrlTemplate string
    Logger            *zap.Logger
    // 删除
    // RepairWebhookURL string
    // RepairSecret     string

    // 保留和新增
    RepairDetailUrlTemplate string
    RepairProjectWebhooks   configs.ProjectWebhookMappings
}
```

**构造函数简化**：
```go
// 修改前：8个参数
func NewFeishuNotifier(webhookURL, secret, detailUrlTemplate, repairWebhookURL, repairSecret, repairDetailUrlTemplate string, repairProjectWebhooks configs.ProjectWebhookMappings, logger *zap.Logger)

// 修改后：6个参数
func NewFeishuNotifier(webhookURL, secret, detailUrlTemplate, repairDetailUrlTemplate string, repairProjectWebhooks configs.ProjectWebhookMappings, logger *zap.Logger)
```

**新增核心方法**：
```go
// 项目webhook配置查找
func (f *FeishuNotifier) GetRepairProjectWebhookConfig(project string) (webhookURL, secret string)

// 支持自定义webhook的通知方法
func (f *FeishuNotifier) SendRepairTicketWaitingAcceptNotificationWithWebhook(...)
```

### 4. 设备项目服务

**internal/modules/ticket/service/device_project_service.go**：
- 实现了根据设备SN查询项目信息的功能
- 支持从资源信息中获取项目名称
- 添加了详细的调试日志

### 5. 维修单活动增强

**internal/modules/ticket/workflow/activities/repair_ticket_activities.go**：
- 集成了设备项目服务
- 实现了项目自动识别逻辑
- 使用项目专用webhook发送通知

### 6. 调用点更新

更新了所有 `NewFeishuNotifier` 调用点（共7个文件）：
- `internal/modules/ticket/register.go`
- `internal/infrastructure/temporal/repositories.go`
- `internal/modules/purchase/workflow/activity/request/purchase_request_activity.go`
- `internal/modules/purchase/workflow/activity/inquiry/purchase_inquiry_activity.go`
- `internal/modules/purchase/workflow/activity/purchase_approval_activity.go`
- `internal/modules/customerapi/service/ticket_service.go`

## 功能特性

### ✅ 项目自动识别
- 根据设备SN自动查询关联的项目信息
- 支持从设备资源表中获取项目名称
- 详细的调试日志帮助问题排查

### ✅ 灵活的配置管理
- 支持任意数量的项目配置
- 每个项目可以有独立的webhook URL和secret
- `default` 配置作为回退方案

### ✅ 向后兼容
- 保持了原有的通知功能
- 其他类型的通知（故障单、验证等）不受影响
- 平滑的迁移路径

### ✅ 错误处理
- 配置缺失时的错误提示
- 设备项目查询失败的回退机制
- 详细的日志记录

## 使用示例

### 配置示例
```yaml
feishu:
  repair_project_webhooks:
    "cloud17":
      webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/cloud17-webhook"
      secret: "cloud17-secret"
    "cloud27":
      webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/cloud27-webhook"
      secret: "cloud27-secret"
    "default":
      webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/default-webhook"
      secret: "default-secret"
```

### 工作流程示例
1. 设备SN: `21C910544` → 查询资源表 → 项目: `cloud27`
2. 查找配置: `repair_project_webhooks["cloud27"]`
3. 发送通知到: `cloud27` 项目的飞书群
4. 如果 `cloud27` 配置不存在，使用 `default` 配置

## 日志示例

```
[DEBUG] 设备搜索结果 - SN: 21C910544, Source: device
[DEBUG] 资源信息存在 - SN: 21C910544, Project: cloud27
[DEBUG] 从资源信息获取项目 - SN: 21C910544, Project: cloud27
INFO 成功获取设备项目信息 - deviceSN: 21C910544, project: cloud27
INFO 查找维修单项目webhook配置 - 项目: cloud27, 可用配置数量: 3
INFO 找到维修单项目配置 - 项目: cloud27, webhookURL: https://..., 有secret: true
INFO 使用维修单项目专用webhook配置 - project: cloud27, webhookURL: https://...
INFO 维修单待接单通知发送成功（项目专用webhook） - webhookURL: https://...
```
