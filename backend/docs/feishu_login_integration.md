# 飞书登录集成文档

## 概述

本文档描述了如何在现有系统中集成飞书OAuth登录功能，支持用户通过飞书账号登录系统。

## 功能特性

1. **飞书OAuth 2.0登录**：支持标准的OAuth 2.0授权流程
2. **智能用户匹配**：通过邮箱、手机号等信息匹配现有用户
3. **账号绑定**：支持将飞书账号绑定到现有用户账号
4. **安全保障**：包含CSRF防护、状态验证等安全措施
5. **用户信息同步**：自动同步飞书用户信息到本地

## 配置说明

### 1. 飞书应用配置

在飞书开放平台创建应用并获取以下信息：
- App ID：应用ID
- App Secret：应用密钥
- 重定向URI：授权回调地址

### 2. 系统配置

在 `configs/config.dev.yaml` 中添加飞书OAuth配置：

```yaml
feishu:
  oauth:
    app_id: "cli_a123456789abcdef"  # 飞书应用ID
    app_secret: "your_app_secret_here"  # 飞书应用密钥
    redirect_uri: "http://localhost:8080/api/v1/auth/feishu/callback"  # 授权回调地址
    scope: ""  # 授权范围，空字符串表示获取基本信息
    enabled: true  # 启用飞书登录
    auto_create: false  # 是否自动创建用户
```

## API接口

### 1. 获取飞书授权URL

**接口**：`GET /api/v1/auth/feishu/auth-url`

**响应**：
```json
{
  "code": 200,
  "data": {
    "authUrl": "https://open.feishu.cn/open-apis/authen/v1/authorize?...",
    "state": "random_state_string"
  },
  "message": "获取飞书授权URL成功"
}
```

### 2. 飞书登录

**接口**：`POST /api/v1/auth/feishu/login`

**请求体**：
```json
{
  "code": "authorization_code_from_feishu",
  "state": "state_from_previous_request"
}
```

**响应**：
```json
{
  "code": 200,
  "data": {
    "accessToken": "jwt_token",
    "expires_at": 1234567890,
    "user_id": 123
  },
  "message": "飞书登录成功"
}
```

### 3. 绑定飞书账号

**接口**：`POST /api/v1/auth/feishu/bind`

**请求体**：
```json
{
  "username": "existing_username",
  "password": "user_password",
  "code": "authorization_code_from_feishu",
  "state": "state_from_previous_request"
}
```

## 前端集成示例

### 1. 获取授权URL并跳转

```javascript
// 获取飞书授权URL
async function getFeishuAuthUrl() {
  try {
    const response = await fetch('/api/v1/auth/feishu/auth-url');
    const result = await response.json();
    
    if (result.code === 200) {
      // 跳转到飞书授权页面
      window.location.href = result.data.authUrl;
      // 保存state用于后续验证
      localStorage.setItem('feishu_state', result.data.state);
    }
  } catch (error) {
    console.error('获取飞书授权URL失败:', error);
  }
}
```

### 2. 处理授权回调

```javascript
// 在回调页面处理授权结果
async function handleFeishuCallback() {
  const urlParams = new URLSearchParams(window.location.search);
  const code = urlParams.get('code');
  const state = urlParams.get('state');
  const savedState = localStorage.getItem('feishu_state');
  
  if (!code || !state || state !== savedState) {
    alert('授权失败，请重试');
    return;
  }
  
  try {
    const response = await fetch('/api/v1/auth/feishu/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ code, state }),
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      // 登录成功，保存token
      localStorage.setItem('access_token', result.data.accessToken);
      // 跳转到主页
      window.location.href = '/dashboard';
    } else {
      alert(result.message || '登录失败');
    }
  } catch (error) {
    console.error('飞书登录失败:', error);
  }
  
  // 清除保存的state
  localStorage.removeItem('feishu_state');
}
```

## 用户匹配策略

系统按以下优先级匹配用户：

1. **飞书OpenID匹配**：如果用户已绑定飞书账号，直接匹配
2. **邮箱匹配**：通过飞书用户邮箱匹配现有用户
3. **手机号匹配**：通过飞书用户手机号匹配现有用户
4. **自动创建**：如果启用自动创建且未匹配到用户，则创建新用户

## 安全考虑

1. **CSRF防护**：使用state参数防止跨站请求伪造攻击
2. **状态验证**：state参数存储在Redis中，有效期5分钟
3. **IP记录**：记录用户登录IP地址
4. **日志记录**：记录所有登录尝试和关键操作

## 数据库变更

新增字段：
- `feishu_open_id`：飞书用户OpenID
- `feishu_union_id`：飞书用户UnionID
- `login_type`：登录方式（password/feishu/both）
- `last_login_time`：最后登录时间
- `last_login_ip`：最后登录IP

## 故障排除

### 常见问题

1. **"飞书登录未启用"**
   - 检查配置文件中 `feishu.oauth.enabled` 是否为 `true`
   - 确认 `app_id` 和 `app_secret` 配置正确

2. **"无效的状态参数"**
   - 检查Redis连接是否正常
   - 确认state参数未过期（5分钟有效期）

3. **"未找到匹配的用户"**
   - 检查用户邮箱和手机号是否与飞书账号一致
   - 考虑启用自动创建用户或手动绑定账号

### 日志查看

相关日志会记录在系统日志中，可以通过以下关键词搜索：
- "飞书登录"
- "FeishuLogin"
- "OAuth"
- "token交换"

## 扩展功能

### 批量导入飞书用户

可以扩展实现批量从飞书导入用户的功能：

```go
// 示例：批量导入飞书用户
func (s *UserService) ImportFeishuUsers(ctx context.Context, departmentID string) error {
    // 1. 获取部门用户列表
    // 2. 批量创建用户
    // 3. 发送通知
    return nil
}
```

### 用户信息同步

可以定期同步飞书用户信息：

```go
// 示例：同步用户信息
func (s *UserService) SyncFeishuUserInfo(ctx context.Context, userID uint) error {
    // 1. 获取用户飞书信息
    // 2. 更新本地用户信息
    return nil
}
```
