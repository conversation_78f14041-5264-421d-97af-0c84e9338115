# Git提交钩子检查工具下载
- 下载pre-commit工具
``` shell
pip install pre-commit
# 或者mac使用brew进行安装
brew install pre-commit
```
- 在项目根目录下执行
``` shell
pre-commit install
```



**Pull Request:**

1. Fork 代码!
2. 创建自己的分支: `git checkout -b feature/xxxx`
3. 提交你的修改: `git commit -am 'feat(function): add xxxxx'`
4. 推送您的分支: `git push origin feature/xxxx`
5. 提交`pull request`

## Git 提交规范

- 参考 [vue](https://github.com/vuejs/vue/blob/dev/.github/COMMIT_CONVENTION.md) 规范 ([Angular](https://github.com/conventional-changelog/conventional-changelog/tree/master/packages/conventional-changelog-angular))

    - `feat` 增加新功能
    - `fix` 修复问题/BUG
    - `style` 代码风格相关无影响运行结果的
    - `perf` 优化/性能提升
    - `refactor` 重构
    - `revert` 撤销修改
    - `test` 测试相关
    - `docs` 文档/注释
    - `chore` 依赖更新/脚手架配置修改等
    - `ci` 持续集成
    - `types` 类型定义文件更改
    - `wip` 开发中


# 项目目录结构
```
.
├── cmd
│   └── main.go                # 入口文件
├── configs
│   ├── config.dev.yaml        # 开发环境配置
│   ├── config.prod.yaml       # 生产环境配置
│   └── config.go              # 配置结构体定义
├── internal
│   ├── app                    # 应用核心逻辑
│   │   ├── controller         # HTTP 控制器层
│   │   │   ├── user.go
│   │   │   └── product.go
│   │   ├── service            # 业务服务层
│   │   │   ├── user_service.go
│   │   │   └── product_service.go
│   │   ├── repository         # 数据访问层
│   │   │   ├── user_repo.go
│   │   │   └── product_repo.go
│   │   └── model              # 数据模型
│   │       ├── user.go
│   │       └── product.go
│   ├── middleware             # 中间件
│   │   ├── auth.go            # JWT 认证
│   │   ├── logger.go          # 日志处理
│   │   └── recovery.go        # Panic 恢复
│   ├── routes                 # 路由配置
│   │   ├── api_v1.go          # API 路由分组
│   │   └── router.go          # 路由初始化
│   └── pkg
│       ├── database           # 数据库连接
│       │   └── mysql.go
│       ├── cache              # 缓存组件
│       │   └── redis.go
│       └── utils              # 工具函数
│           ├── validator.go   # 参数验证
│           └── response.go    # 统一响应格式
├── api
│   └── swagger                # API 文档
│       ├── docs.go
│       └── swagger.yaml
├── scripts                    # 部署/运维脚本
│   ├── migrate.sh             # 数据库迁移
│   └── deploy.sh
├── test                       # 集成测试
│   ├── integration
│   └── e2e
├── web                        # 前端资源（可选）
│   ├── static
│   └── templates
├── storage
│   ├── logs                   # 日志文件
│   └── uploads                # 文件上传目录
├── go.mod
├── go.sum
├── Makefile                   # 构建命令
├── Dockerfile
└── README.md
```

## 分层架构
```shell
HTTP请求 → 路由 → 中间件 → 控制器 → 服务层 → 仓储层 → 数据库
```
