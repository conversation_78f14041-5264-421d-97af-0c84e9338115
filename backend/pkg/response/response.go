package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// Response 统一响应结构
type Response struct {
	Code    int         `json:"code"`    // 业务状态码
	Message string      `json:"message"` // 提示信息
	Data    interface{} `json:"data"`    // 数据
}

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "success",
		Data:    data,
	})
}

// Error 错误响应
func Error(c *gin.Context, status int, message string) {
	c.<PERSON><PERSON>(status, Response{
		Code:    status,
		Message: message,
		Data:    nil,
	})
}

// SuccessWithStatus 自定义状态码的成功响应
func SuccessWithStatus(c *gin.Context, status int, data interface{}) {
	c.<PERSON>(status, Response{
		Code:    2000,
		Message: "success",
		Data:    data,
	})
}
