package feishuapp

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// FeishuOAuthClient 飞书OAuth客户端
type FeishuOAuthClient struct {
	AppID       string
	AppSecret   string
	RedirectURI string
	Scope       string
}

// NewFeishuOAuthClient 创建飞书OAuth客户端
func NewFeishuOAuthClient(appID, appSecret, redirectURI, scope string) *FeishuOAuthClient {
	return &FeishuOAuthClient{
		AppID:       appID,
		AppSecret:   appSecret,
		RedirectURI: redirectURI,
		Scope:       scope,
	}
}

// GenerateState 生成随机状态参数
func (c *FeishuOAuthClient) GenerateState() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// GetAuthURL 获取飞书授权URL
func (c *FeishuOAuthClient) GetAuthURL(state string) string {
	params := url.Values{}
	params.Set("app_id", c.AppID)
	params.Set("redirect_uri", c.RedirectURI)
	params.Set("response_type", "code")
	params.Set("state", state)
	if c.Scope != "" {
		params.Set("scope", c.Scope)
	}

	return fmt.Sprintf("https://open.feishu.cn/open-apis/authen/v1/authorize?%s", params.Encode())
}

// TokenResponse 飞书token响应
type TokenResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		AccessToken      string `json:"access_token"`
		TokenType        string `json:"token_type"`
		ExpiresIn        int    `json:"expires_in"`
		RefreshToken     string `json:"refresh_token"`
		RefreshExpiresIn int    `json:"refresh_expires_in"`
		Scope            string `json:"scope"`
	} `json:"data"`
}

// UserInfoData 飞书用户信息数据
type UserInfoData struct {
	OpenID       string `json:"open_id"`
	UnionID      string `json:"union_id"`
	Name         string `json:"name"`
	EnName       string `json:"en_name"`
	Email        string `json:"email"`
	Mobile       string `json:"mobile"`
	Avatar       Avatar `json:"avatar"`
	EmployeeNo   string `json:"employee_no"`
	EmployeeType int    `json:"employee_type"`
}

// UserInfoResponse 飞书用户信息响应
type UserInfoResponse struct {
	Code int          `json:"code"`
	Msg  string       `json:"msg"`
	Data UserInfoData `json:"data"`
}

// Avatar 头像信息
type Avatar struct {
	Avatar72     string `json:"avatar_72"`
	Avatar240    string `json:"avatar_240"`
	Avatar640    string `json:"avatar_640"`
	AvatarOrigin string `json:"avatar_origin"`
}

// ExchangeToken 用授权码换取访问令牌
func (c *FeishuOAuthClient) ExchangeToken(ctx context.Context, code string) (*TokenResponse, error) {
	data := url.Values{}
	data.Set("grant_type", "authorization_code")
	data.Set("code", code)

	req, err := http.NewRequestWithContext(ctx, "POST", "https://open.feishu.cn/open-apis/authen/v1/oidc/access_token", strings.NewReader(data.Encode()))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", c.getAppAccessToken(ctx)))

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var tokenResp TokenResponse
	if err := json.Unmarshal(body, &tokenResp); err != nil {
		return nil, err
	}

	if tokenResp.Code != 0 {
		return nil, fmt.Errorf("飞书API错误: %s", tokenResp.Msg)
	}

	return &tokenResp, nil
}

// GetUserInfo 获取用户信息
func (c *FeishuOAuthClient) GetUserInfo(ctx context.Context, accessToken string) (*UserInfoResponse, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", "https://open.feishu.cn/open-apis/authen/v1/user_info", nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", accessToken))

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var userResp UserInfoResponse
	if err := json.Unmarshal(body, &userResp); err != nil {
		return nil, err
	}

	if userResp.Code != 0 {
		return nil, fmt.Errorf("飞书API错误: %s", userResp.Msg)
	}

	return &userResp, nil
}

// AppAccessTokenResponse 应用访问令牌响应
type AppAccessTokenResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		AppAccessToken string `json:"app_access_token"`
		Expire         int    `json:"expire"`
	} `json:"data"`
}

// getAppAccessToken 获取应用访问令牌
func (c *FeishuOAuthClient) getAppAccessToken(ctx context.Context) string {
	data := map[string]string{
		"app_id":     c.AppID,
		"app_secret": c.AppSecret,
	}

	jsonData, _ := json.Marshal(data)
	req, err := http.NewRequestWithContext(ctx, "POST", "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal", strings.NewReader(string(jsonData)))
	if err != nil {
		return ""
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return ""
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return ""
	}

	var tokenResp AppAccessTokenResponse
	if err := json.Unmarshal(body, &tokenResp); err != nil {
		return ""
	}

	if tokenResp.Code != 0 {
		return ""
	}

	return tokenResp.Data.AppAccessToken
}

// ValidateState 验证状态参数（简单实现，实际应用中应该使用Redis等存储）
func (c *FeishuOAuthClient) ValidateState(state string) error {
	if state == "" {
		return errors.New("状态参数不能为空")
	}
	// 这里应该验证state是否是之前生成的，防止CSRF攻击
	// 简单实现，实际应用中应该使用Redis等存储state
	return nil
}
