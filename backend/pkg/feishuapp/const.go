package feishuapp

type IDType string  // 用户ID类型
type MsgType string // 消息类型

const (
	IDType_Open_ID  IDType = "open_id"
	IDType_User_ID  IDType = "user_id"
	IDType_Union_ID IDType = "union_id"
)

// 飞书消息类型，详情请参考https://open.feishu.cn/document/server-docs/im-v1/message-content-description/create_json
const (
	MsgType_Text        MsgType = "text"        // 文本
	MsgType_Image       MsgType = "image"       // 图片
	MsgType_Post        MsgType = "post"        // 富文本
	MsgType_Interactice MsgType = "interactive" // 卡片
	MsgType_ShareChat   MsgType = "share_chat"  // 群名片
	MsgType_ShareUser   MsgType = "share_user"  // 个人名片
	MsgType_Audio       MsgType = "audio"       // 语音
	MsgType_Media       MsgType = "media"       // 视频
	MsgType_File        MsgType = "file"        // 文件
	MsgType_Sticker     MsgType = "sticker"     // 表情
	MsgType_System      MsgType = "system"      // 系统消息
)

//
