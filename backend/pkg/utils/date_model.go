package utils

import (
	"database/sql/driver"
	"fmt"
	"strings"
	"time"
)

// Date 自定义日期类型，支持多种格式解析
type Date time.Time

// UnmarshalJSON 实现自定义的JSON解析
func (d *Date) UnmarshalJSON(data []byte) error {
	str := string(data)
	str = strings.Trim(str, "\"")

	if str == "" || str == "null" {
		*d = Date(time.Time{})
		return nil
	}

	// 尝试多种格式解析
	formats := []string{
		"2006-01-02",
		"2006-01-02T15:04:05Z07:00",
		time.RFC3339,
	}

	var t time.Time
	var err error

	for _, format := range formats {
		t, err = time.Parse(format, str)
		if err == nil {
			*d = Date(t)
			return nil
		}
	}

	return fmt.Errorf("无法解析日期: %s", str)
}

// MarshalJSON 实现自定义的JSON序列化
func (d Date) MarshalJSON() ([]byte, error) {
	t := time.Time(d)
	if t.<PERSON><PERSON>() {
		return []byte("null"), nil
	}
	return []byte(fmt.Sprintf("\"%s\"", t.Format(time.RFC3339))), nil
}

// Scan 实现sql.Scanner接口
func (d *Date) Scan(value interface{}) error {
	if value == nil {
		*d = Date(time.Time{})
		return nil
	}

	switch v := value.(type) {
	case time.Time:
		*d = Date(v)
		return nil
	}

	return fmt.Errorf("无法将 %T 转换为 Date", value)
}

// Value 实现driver.Valuer接口
func (d Date) Value() (driver.Value, error) {
	t := time.Time(d)
	if t.IsZero() {
		return nil, nil
	}
	return t, nil
}

// String 返回日期的字符串表示
func (d Date) String() string {
	t := time.Time(d)
	if t.IsZero() {
		return ""
	}
	return t.Format("2006-01-02")
}
