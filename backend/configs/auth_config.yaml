# 权限配置文件
resources:
  # CMDB模块
  - path: "/cmdb"
    children:
      # 服务器
      - path: "/device-resources"
        operations:
          #- method: "GET"
          # auth_code: "Cmdb:ServerInfo:List"
          - method: "POST"
            auth_code: "Cmdb:ServerInfo:Create"
      - path: "/device-resources/:id"
        operations:
          - method: "PUT"
            auth_code: "Cmdb:ServerInfo:Edit"
          - method: "DELETE"
            auth_code: "Cmdb:ServerInfo:Delete"

      # 网络设备
      - path: "/network-devices"
        operations:
          #- method: "GET"
          #  auth_code: "Cmdb:NetDeviceInfo:List"
          - method: "POST"
            auth_code: "Cmdb:NetDeviceInfo:Create"
      - path: "/network-devices/with-device"
        operations:
          #- method: "GET"
          # auth_code: "Cmdb:NetDeviceInfo:List"
          - method: "POST"
            auth_code: "Cmdb:NetDeviceInfo:Create"
      - path: "/network-devices/:id"
        operations:
          - method: "PUT"
            auth_code: "Cmdb:NetDeviceInfo:Edit"
          - method: "DELETE"
            auth_code: "Cmdb:NetDeviceInfo:Delete"
      - path: "/network-devices/with-device/:id"
        operations:
          - method: "PUT"
            auth_code: "Cmdb:NetDeviceInfo:Edit"
          - method: "DELETE"
            auth_code: "Cmdb:NetDeviceInfo:Delete"

      # 区域
      - path: "/regions"
        operations:
          #- method: "GET"
          # auth_code: "Cmdb:RegionInfo:List"
          - method: "POST"
            auth_code: "Cmdb:RegionInfo:Create"
      - path: "/regions/:id"
        operations:
          - method: "PUT"
            auth_code: "Cmdb:RegionInfo:Edit"
          - method: "DELETE"
            auth_code: "Cmdb:RegionInfo:Delete"

      # 可用区
      - path: "/azs"
        operations:
          #- method: "GET"
          # auth_code: "Cmdb:AzInfo:List"
          - method: "POST"
            auth_code: "Cmdb:AzInfo:Create"
      - path: "/azs/:id"
        operations:
          - method: "PUT"
            auth_code: "Cmdb:AzInfo:Edit"
          - method: "DELETE"
            auth_code: "Cmdb:AzInfo:Delete"

      # 机房
      - path: "/datacenters"
        operations:
          #- method: "GET"
          # auth_code: "Cmdb:IdcInfo:List"
          - method: "POST"
            auth_code: "Cmdb:IdcInfo:Create"
      - path: "/datacenters/:id"
        operations:
          - method: "PUT"
            auth_code: "Cmdb:IdcInfo:Edit"
          - method: "DELETE"
            auth_code: "Cmdb:IdcInfo:Delete"

      # 包间
      - path: "/rooms"
        operations:
          #- method: "GET"
          # auth_code: "Cmdb:IdcRoomInfo:List"
          - method: "POST"
            auth_code: "Cmdb:IdcRoomInfo:Create"
      - path: "/rooms/:id"
        operations:
          - method: "PUT"
            auth_code: "Cmdb:IdcRoomInfo:Edit"
          - method: "DELETE"
            auth_code: "Cmdb:IdcRoomInfo:Delete"

      # 机柜
      - path: "/cabinets"
        operations:
          #- method: "GET"
          #  auth_code: "Cmdb:CabinetInfo:List"
          - method: "POST"
            auth_code: "Cmdb:CabinetInfo:Create"
      - path: "/cabinets/:id"
        operations:
          - method: "PUT"
            auth_code: "Cmdb:CabinetInfo:Edit"
          - method: "DELETE"
            auth_code: "Cmdb:CabinetInfo:Delete"

      # 资产管理
      # 资产入库
      - path: "/inbound"
        operations:
        #- method: "GET"
        # auth_code: "AssetMgt:AssetInbound:List"

      - path: "/inbound/new-input" # 手动创建新购入库单
        operations:
          - method: "POST"
            auth_code: "AssetMgt:AssetInbound:Create"
      - path: "/inbound/new-import" # 批量导入新购入库单
        operations:
          - method: "POST"
            auth_code: "AssetMgt:AssetInbound:Create"
      - path: "/inbound/new/update-details-import" # 更新详情信息
        operations:
          - method: "PUT"
            auth_code: "AssetMgt:AssetInbound:Edit"
      - path: "/inbound/new/update-details-input" # 更新详情信息
        operations:
          - method: "PUT"
            auth_code: "AssetMgt:AssetInbound:Edit"
      - path: "/inbound/trans/dismantled-input" # 创建拆机入库单
        operations:
          - method: "POST"
            auth_code: "AssetMgt:AssetInbound:Create"
      - path: "/inbound/trans/dismantled-import"
        operations:
          - method: "POST"
            auth_code: "AssetMgt:AssetInbound:Create"
      - path: "/inbound/repair-input" # 创建返修入库
        operations:
          - method: "POST"
            auth_code: "AssetMgt:AssetInbound:Create"
      - path: "/inbound/repair-import"
        operations:
          - method: "POST"
            auth_code: "AssetMgt:AssetInbound:Create"

      # 服务器套餐
      - path: "/machine-templates"
        operations:
          #- method: "GET"
          # auth_code: "AssetMgt:ServerPackageInfo:List"
          - method: "POST"
            auth_code: "AssetMgt:ServerPackageInfo:Create"
      - path: "/machine-templates/:id"
        operations:
          - method: "PUT"
            auth_code: "AssetMgt:ServerPackageInfo:Edit"
          - method: "DELETE"
            auth_code: "AssetMgt:ServerPackageInfo:Delete"

      # PN/规格
      - path: "/products"
        operations:
          #- method: "GET"
          # auth_code: "AssetMgt:SpecificationsInfo:List"
          - method: "POST"
            auth_code: "AssetMgt:SpecificationsInfo:Create"
      - path: "/products/:id"
        operations:
          - method: "PUT"
            auth_code: "AssetMgt:SpecificationsInfo:Edit"
          - method: "DELETE"
            auth_code: "AssetMgt:SpecificationsInfo:Delete"

      # 配件信息
      - path: "/server-components"
        operations:
          #- method: "GET"
          # auth_code: "AssetMgt:AccessoryInfo:List"
          - method: "POST"
            auth_code: "AssetMgt:AccessoryInfo:Create"
      - path: "/server-components/:id"
        operations:
          - method: "PUT"
            auth_code: "AssetMgt:AccessoryInfo:Edit"
          - method: "DELETE"
            auth_code: "AssetMgt:AccessoryInfo:Delete"

      # 备件信息
      - path: "/spares"
        operations:
          #- method: "GET"
          # auth_code: "AssetMgt:Spares:List"
          - method: "POST"
            auth_code: "AssetMgt:Spares:Create"
      - path: "/spares/:id"
        operations:
          - method: "PUT"
            auth_code: "AssetMgt:Spares:Edit"
          - method: "DELETE"
            auth_code: "AssetMgt:Spares:Delete"

      # 仓库管理
      - path: "/warehouses"
        operations:
          #- method: "GET"
          # auth_code: "AssetMgt:Warehouse:List"
          - method: "POST"
            auth_code: "AssetMgt:Warehouse:Create"
      - path: "/warehouses/:id"
        operations:
          - method: "PUT"
            auth_code: "AssetMgt:Warehouse:Edit"
          - method: "DELETE"
            auth_code: "AssetMgt:Warehouse:Delete"

      # 库存统计
      - path: "/inventory"
        operations:
        #- method: "GET"
        # auth_code: "AssetMgt:Inventory:List"

  # 工单模块
  - path: "/ticket"
    children:
      # 硬件维修工单
      - path: "/repair-tickets"
        operations:
        #- method: "GET"
        # auth_code: "HardwareMaint:HardwareOrder:List"
      - path: "/repair-tickets/:id/transition"
        operations:
          - method: "PUT"
            auth_code: "HardwareMaint:HardwareOrder:Handle"

      # 故障工单
      - path: "/fault-tickets"
        operations:
        #- method: "GET"
        # auth_code: "SoftMaint:FaultReport:List"


      - path: "/fault-tickets/:id/transition"
        operations:
          - method: "PUT"
            auth_code: "SoftMaint:FaultReport:Handle"
            # param_conditions:
            #   - params:
            #       stage: "accept_ticket"
            #       status: "investigating"
            #     auth_code: "ticket:transition:accept:investigating"
            #   - params:
            #       stage: "accept_ticket"
            #       status: "waiting_approval"
            #     auth_code: "ticket:transition:accept:waiting_approval"

      # 资产出库
      - path: "/outbound-tickets"
        operations:
        #- method: "GET"
        # auth_code: "AssetMgt:AssetOutbound:List"
      - path: "/outbound-tickets/v2"
        operations:
          - method: "POST"
            auth_code: "AssetMgt:AssetOutbound:Create"
      - path: "/outbound-tickets/:id/transition/v2/engineer"
        operations:
          - method: "PUT"
            auth_code: "AssetMgt:AssetOutbound:Engineer"

      - path: "/outbound-tickets/:id/transition/v2/asset"
        operations:
          - method: "PUT"
            auth_code: "AssetMgt:AssetOutbound:Asset"

      - path: "/outbound-tickets/:id/transition/v2/asset"
        operations:
          - method: "PUT"
            auth_code: "AssetMgt:AssetOutbound:DestAsset"

      - path: "/outbound-tickets/:id/transition/v2/replace"
        operations:
          - method: "PUT"
            auth_code: "AssetMgt:AssetOutbound:Replace"

      - path: "/ticket/inbound-tickets/:inboundNo/transition" # 入库状态转换
        operations:
          - method: "PUT"
            auth_code: "AssetMgt:AssetInbound:Verify"
            # 资产管理员
          - method: "PUT"
            auth_code: "AssetMgt:AssetInbound:Asset"
    # 看板模块
  - path: "/dashboard"
    children:
      # 为dashboard下所有子路径设置相同权限
      - path: "/**"
        operations:
          - method: "GET"
            auth_code: "Dashboard:Dashboard:View"

  # 采购模块
  - path: "/purchase"
    children:
      # 项目管理
      - path: "/projects"
        operations:
          - method: "POST"
            auth_code: "Purchase:Project:Create"
      - path: "/projects/:id"
        operations:
          - method: "PUT"
            auth_code: "Purchase:Project:Edit"
          - method: "DELETE"
            auth_code: "Purchase:Project:Delete"

      # 供应商管理
      - path: "/suppliers"
        operations:
          - method: "POST"
            auth_code: "Purchase:Supplier:Create"
      - path: "/suppliers/:id"
        operations:
          - method: "PUT"
            auth_code: "Purchase:Supplier:Edit"
          - method: "DELETE"
            auth_code: "Purchase:Supplier:Delete"

      # 我司档案信息
      - path: "/companies"
        operations:
          - method: "POST"
            auth_code: "Purchase:Company:Create"
      - path: "/companies/:id"
        operations:
          - method: "PUT"
            auth_code: "Purchase:Company:Edit"
          - method: "DELETE"
            auth_code: "Purchase:Company:Delete"

      # 采购申请
      - path: "/requests"
        operations:
          - method: "POST"
            auth_code: "Purchase:PurchaseRequest:Create"


      # 审批采购申请（包括审批通过和拒绝）
      - path: "/requests/approve"
        operations:
          - method: "POST"
            param_conditions:
              # 项目经理审批阶段 - 审批通过或拒绝
              - params:
                  current_stage: "project_manager_review"
                auth_code: "Purchase:PurchaseRequest:ProjectManagerReview"

              # 采购负责人审批阶段 - 审批通过或拒绝
              - params:
                  current_stage: "purchase_manager_review"
                auth_code: "Purchase:PurchaseRequest:PurchaseManagerReview"

      # 采购询价
      - path: "/inquiries"
        operations:
          - method: "POST"
            auth_code: "Purchase:PurchaseInquiry:Create"

      # 审批采购询价（包括审批通过、拒绝、回退）
      - path: "/inquiries/approve"
        operations:
          - method: "POST"
            param_conditions:
              # 财务负责人审批阶段（审批、拒绝、回退共用一个权限）
              - params:
                  current_stage: "finance_review"
                auth_code: "Purchase:PurchaseInquiry:FinanceManagerReview"
              # 企业负责人审批阶段（审批、拒绝、回退共用一个权限）
              - params:
                  current_stage: "enterprise_review"
                auth_code: "Purchase:PurchaseInquiry:EnterpriseManagerReview"

      # 回退采购询价
      - path: "/inquiries/rollback"
        operations:
          - method: "POST"
            param_conditions:
              # 从企业负责人审批阶段回退（使用企业负责人权限）
              - params:
                  current_stage: "enterprise_review"
                auth_code: "Purchase:PurchaseInquiry:EnterpriseReview"
              # 从财务负责人审批阶段回退（使用财务负责人权限）
              - params:
                  current_stage: "finance_review"
                auth_code: "Purchase:PurchaseInquiry:FinanceReview"

      # 采购合同
      - path: "/contracts"
        operations:
          - method: "POST"
            auth_code: "Purchase:PurchaseContract:Create"

      # 审批采购合同（包括审批通过、拒绝、回退）
      - path: "/contracts/approve"
        operations:
          - method: "POST"
            param_conditions:
              # 采购负责人审批阶段（审批、拒绝、回退共用一个权限）
              - params:
                  current_stage: "purchase_review"
                auth_code: "Purchase:PurchaseContract:PurchaseManagerReview"
              # 财务负责人审批阶段（审批、拒绝、回退共用一个权限）
              - params:
                  current_stage: "finance_review"
                auth_code: "Purchase:PurchaseContract:FinanceManagerReview"
              # 法务负责人审批阶段（审批、拒绝、回退共用一个权限）
              - params:
                  current_stage: "legal_review"
                auth_code: "Purchase:PurchaseContract:LegalManagerReview"
              # 企业负责人审批阶段（审批、拒绝、回退共用一个权限）
              - params:
                  current_stage: "enterprise_review"
                auth_code: "Purchase:PurchaseContract:EnterpriseManagerReview"
              # 内部签章阶段（审批、拒绝、回退共用一个权限）
              - params:
                  current_stage: "internal_sign"
                auth_code: "Purchase:PurchaseContract:InternalSign"
              # 双方签章阶段（审批、拒绝、回退共用一个权限）
              - params:
                  current_stage: "double_sign"
                auth_code: "Purchase:PurchaseContract:DoubleSign"

      # 回退采购合同
      - path: "/contracts/rollback"
        operations:
          - method: "POST"
            param_conditions:
              # 各阶段回退使用对应阶段的权限
              - params:
                  current_stage: "purchase_review"
                auth_code: "Purchase:PurchaseContract:PurchaseManagerReview"
              - params:
                  current_stage: "finance_review"
                auth_code: "Purchase:PurchaseContract:FinanceManagerReview"
              - params:
                  current_stage: "legal_review"
                auth_code: "Purchase:PurchaseContract:LegalManagerReview"
              - params:
                  current_stage: "enterprise_review"
                auth_code: "Purchase:PurchaseContract:EnterpriseManagerReview"
              - params:
                  current_stage: "internal_sign"
                auth_code: "Purchase:PurchaseContract:InternalSign"
              - params:
                  current_stage: "double_sign"
                auth_code: "Purchase:PurchaseContract:DoubleSign"

      # 付款申请
      - path: "/payment-requests"
        operations:
          - method: "POST"
            auth_code: "Purchase:PaymentRequest:Create"

      # 审批付款申请（包括审批通过、拒绝、回退）
      - path: "/payment-requests/approve"
        operations:
          - method: "POST"
            param_conditions:
              # 采购与供应链负责人审批阶段
              - params:
                  current_stage: "purchase_review"
                auth_code: "Purchase:PaymentRequest:PurchaseManagerReview"
              # 财务负责人审批阶段
              - params:
                  current_stage: "finance_manager_review"
                auth_code: "Purchase:PaymentRequest:FinanceManagerReview"
              # 企业负责人审批阶段
              - params:
                  current_stage: "enterprise_manager_review"
                auth_code: "Purchase:PaymentRequest:EnterpriseManagerReview"
              # 赛博付款申请复核阶段
              - params:
                  current_stage: "cyber_payment_review"
                auth_code: "Purchase:PaymentRequest:CyberPaymentReview"
              # 其他主体付款申请复核阶段
              - params:
                  current_stage: "other_entity_payment_review"
                auth_code: "Purchase:PaymentRequest:OtherEntityPaymentReview"
              # 资金负责人审批阶段
              - params:
                  current_stage: "fund_manager_review"
                auth_code: "Purchase:PaymentRequest:FundManagerReview"
              # 赛博付款经办阶段
              - params:
                  current_stage: "cyber_handler"
                auth_code: "Purchase:PaymentRequest:CyberPaymentHandler"
              # 其他主体付款经办阶段
              - params:
                  current_stage: "other_entity_handler"
                auth_code: "Purchase:PaymentRequest:OtherEntityPaymentHandler"
              # 赛博复核2阶段
              - params:
                  current_stage: "cyber_review_2"
                auth_code: "Purchase:PaymentRequest:CyberReview2"
              # 最终复核阶段
              - params:
                  current_stage: "final_review"
                auth_code: "Purchase:PaymentRequest:FinalReview"

      # 回退付款申请
      - path: "/payment-requests/rollback"
        operations:
          - method: "POST"
            param_conditions:
              # 各阶段回退使用对应阶段的权限
              - params:
                  current_stage: "purchase_review"
                auth_code: "Purchase:PaymentRequest:PurchaseManagerReview"
              # 财务负责人审批阶段
              - params:
                  current_stage: "finance_manager_review"
                auth_code: "Purchase:PaymentRequest:FinanceManagerReview"
              # 企业负责人审批阶段
              - params:
                  current_stage: "enterprise_manager_review"
                auth_code: "Purchase:PaymentRequest:EnterpriseManagerReview"
              # 赛博付款申请复核阶段
              - params:
                  current_stage: "cyber_payment_review"
                auth_code: "Purchase:PaymentRequest:CyberPaymentReview"
              # 其他主体付款申请复核阶段
              - params:
                  current_stage: "other_entity_payment_review"
                auth_code: "Purchase:PaymentRequest:OtherEntityPaymentReview"
              # 资金负责人审批阶段
              - params:
                  current_stage: "fund_manager_review"
                auth_code: "Purchase:PaymentRequest:FundManagerReview"
              # 赛博付款经办阶段
              - params:
                  current_stage: "cyber_handler"
                auth_code: "Purchase:PaymentRequest:CyberPaymentHandler"
              # 其他主体付款经办阶段
              - params:
                  current_stage: "other_entity_handler"
                auth_code: "Purchase:PaymentRequest:OtherEntityPaymentHandler"
              # 赛博复核2阶段
              - params:
                  current_stage: "cyber_review_2"
                auth_code: "Purchase:PaymentRequest:CyberReview2"
              # 最终复核阶段
              - params:
                  current_stage: "final_review"
                auth_code: "Purchase:PaymentRequest:FinalReview"

      # 到货管理
      - path: "/arrivals"
        operations:
          - method: "POST"
            auth_code: "Purchase:Arrival:Create"

      # 审批到货（包括审批通过、拒绝、回退）
      - path: "/arrivals/approve"
        operations:
          - method: "POST"
            param_conditions:
              # 采购负责人审批阶段
              - params:
                  current_stage: "purchase_review"
                auth_code: "Purchase:Arrival:PurchaseManagerReview"
              # 服务器管理员审批阶段
              - params:
                  current_stage: "server_admin_review"
                auth_code: "Purchase:Arrival:ServerAdminReview"
              # 网络管理员审批阶段
              - params:
                  current_stage: "network_admin_review"
                auth_code: "Purchase:Arrival:NetworkAdminReview"
              # 资产管理审批阶段
              - params:
                  current_stage: "asset_admin_review"
                auth_code: "Purchase:Arrival:AssetAdminReview"

      # 回退到货
      - path: "/arrivals/rollback"
        operations:
          - method: "POST"
            param_conditions:
              # 各阶段回退使用对应阶段的权限
              - params:
                  current_stage: "purchase_review"
                auth_code: "Purchase:Arrival:PurchaseManagerReview"
              - params:
                  current_stage: "server_admin_review"
                auth_code: "Purchase:Arrival:ServerAdminReview"
              - params:
                  current_stage: "network_admin_review"
                auth_code: "Purchase:Arrival:NetworkAdminReview"
              - params:
                  current_stage: "asset_admin_review"
                auth_code: "Purchase:Arrival:AssetAdminReview"

      # 发货管理
      - path: "/shipments"
        operations:
          - method: "POST"
            auth_code: "Purchase:Shipment:Create"

      # 审批发货（包括审批通过、拒绝、回退）
      - path: "/shipments/approve"
        operations:
          - method: "POST"
            param_conditions:
              # 采购负责人审批阶段
              - params:
                  current_stage: "purchase_review"
                auth_code: "Purchase:Shipment:PurchaseManagerReview"
              # 财务负责人审批阶段
              - params:
                  current_stage: "finance_review"
                auth_code: "Purchase:Shipment:FinanceManagerReview"
              # 仓库管理员审批阶段
              - params:
                  current_stage: "warehouse_review"
                auth_code: "Purchase:Shipment:WarehouseManagerReview"

      # 回退发货
      - path: "/shipments/rollback"
        operations:
          - method: "POST"
            param_conditions:
              # 各阶段回退使用对应阶段的权限
              - params:
                  current_stage: "purchase_review"
                auth_code: "Purchase:Shipment:PurchaseManagerReview"
              - params:
                  current_stage: "finance_review"
                auth_code: "Purchase:Shipment:FinanceManagerReview"
              - params:
                  current_stage: "warehouse_review"
                auth_code: "Purchase:Shipment:WarehouseManagerReview"

      # 发票管理
      - path: "/invoices"
        operations:
          - method: "POST"
            auth_code: "Purchase:Invoice:Create"

  # 用户板块
  - path: "/auth"
    children:
      - path: "/admin/create-user"
        operations:
          - method: "POST"
            auth_code: "System:User:Create"
      - path: "/user/:id"
        operations:
          - method: "DELETE"
            auth_code: "System:User:Delete"
      - path: "/admin/reset-password/:id"
        operations:
          - method: "PUT"
            auth_code: "System:User:Edit"
      - path: "/user-update"
        operations:
          - method: "PUT"
            auth_code: "System:User:Edit"

  # 系统管理模块
  - path: "/system"
    children:
      # 菜单管理相关接口
      - path: "/menu"
        operations:
          - method: "POST"
            auth_code: "System:Menu:Create"
          - method: "PUT"
            auth_code: "System:Menu:Edit"
          - method: "DELETE"
            auth_code: "System:Menu:Delete"
      - path: "/menu/:id"
        operations:
          - method: "DELETE"
            auth_code: "System:Menu:Delete"

      - path: "/menu/list"
        operations:
          - method: "GET"
            auth_code: "System:Menu:List"




      # 角色管理相关接口
      - path: "/role"
        operations:
          - method: "POST"
            auth_code: "System:Role:Create"
          - method: "PUT"
            auth_code: "System:Role:Edit"
          - method: "DELETE"
            auth_code: "System:Role:Delete"

      - path: "/role/list"
        operations:
          - method: "GET"
            auth_code: "System:Role:List"

      - path: "/role/:id"
        operations:
          - method: "GET"
            auth_code: "System:Role:View"
          - method: "DELETE"
            auth_code: "System:Role:Delete"

  - path: "/audit"
    children:
      - path: "/audit-logs"
        operations:
          - method: "GET"
            auth_code: "System:AuditLog:List"




  # 导入模块
  - path: "/import"
    operations:
      - method: "POST"
        param_conditions:
          # 服务器导入
          - params:
              model: "device-resource"
            auth_code: "Cmdb:ServerInfo:Import"
          # 网络设备导入
          - params:
              model: "network-device"
            auth_code: "Cmdb:NetDeviceInfo:Import"
          # 区域导入
          - params:
              model: "region"
            auth_code: "Cmdb:RegionInfo:Import"
          # 包间导入
          - params:
              model: "room"
            auth_code: "Cmdb:IdcRoomInfo:Import"
          # 机柜导入
          - params:
              model: "cabinet"
            auth_code: "Cmdb:CabinetInfo:Import"
          # PN/规格导入
          - params:
              model: "product"
            auth_code: "AssetMgt:SpecificationsInfo:Import"
          # 备件导入
          - params:
              model: "spare"
            auth_code: "AssetMgt:Spares:Import"

  # 排班表模块
  - path: "/schedule"
    children:
      - path: "/soft-sche"
        operations:
          - method: "POST"
            auth_code: "System:SoftSchedule:Create"

      - path: "/soft-sche/:date"
        operations:
          - method: "PUT"
            auth_code: "System:SoftSchedule:Update"


      - path: "/hard-sche"
        operations:
          - method: "POST"
            auth_code: "System:HardSchedule:Create"
          - method: "PUT"
            auth_code: "System:HardSchedule:Update"


  # 入室人员申请模块
  - path: "/ticket/entry-tickets"
    operations:
      - method: "POST"
        auth_code: "System:EntryRoomApplication:Create"

  - path: "/ticket/entry-tickets/:id/transition"
    operations:
      - method: "PUT"
        auth_code: "EntryRoom:Handle"




  # 入室人员管理模块
  - path: "/ticket/entry-tickets/person/create"
    operations:
      - method: "POST"
        auth_code: "System:EntryRoomManage:Create"

  # 硬件运维模块
  - path: "/hardware_maintenance"
    children:
      - path: "asset/acceptance"
        operations:
          - method: "GET"
        children:
          - path: "/create_order"
            operations:
              - method: "POST"
#                auth_code: "AssetAcceptance:Create"
          - path: "item_components"

      - path: "asset/racking"
        children:
          - path: "/create_ticket"
            operations:
              - method: "Post"
#              auth_code: "HardwareMaintenance:AssetRacking:Create"
          - path: "/transition"
            operations:
              - method: "Post"
#              auth_code: "HardwareMaintenance:AssetRacking:Transition"

  # 软件上线线模块
  - path: "/software_maintenance/launch"
    operations:
      - method: "POST"
        auth_code: "Software:Launch:Create"


  - path: "/software_maintenance/offline"
    operations:
      - method: "POST"
        auth_code: "Software:Offline:Create"
