package configs

import (
	"os"

	"github.com/spf13/viper"
)

// ProjectWebhookConfig 项目专用webhook配置
type ProjectWebhookConfig struct {
	WebhookURL string `mapstructure:"webhook_url" json:"webhook_url"`
	Secret     string `mapstructure:"secret" json:"secret"`
}

// ProjectWebhookMappings 项目webhook映射配置
type ProjectWebhookMappings map[string]ProjectWebhookConfig

type Config struct {
	Port     string          `mapstructure:"port"`
	Mode     string          `mapstructure:"mode"`
	DB       DBConfig        `mapstructure:"db"`
	Logger   LogConfig       `mapstructure:"logger"`
	JWT      JWTConfig       `mapstructure:"jwt"`
	Temporal *TemporalConfig `mapstructure:"temporal"`
	Feishu   *FeishuConfig   `mapstructure:"feishu"`
	Redis    RedisConfig     `mapstructure:"redis"`
	Email    *EmailConfig    `mapstructure:"email"` // 邮件相关配置
}

type DBConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	User     string `mapstructure:"user"`
	Password string `mapstructure:"password"`
	Name     string `mapstructure:"name"`
}

// 超级管理员配置
type SuperAdmin struct {
	Username    string   `mapstructure:"userName" json:"username"`
	Password    string   `mapstructure:"password" json:"password"`
	RealName    string   `mapstructure:"realName" json:"realName"`
	Email       string   `mapstructure:"email" json:"email"`
	Roles       []string `mapstructure:"roles" json:"roles"`
	AccessCodes []string `mapstructure:"accessCodes" json:"accessCodes"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level          string   `mapstructure:"level"`
	OutputPaths    []string `mapstructure:"output_paths"`
	ErrorPaths     []string `mapstructure:"error_paths"`
	Encoding       string   `mapstructure:"encoding"`
	MaxSize        int      `mapstructure:"max_size"`
	MaxAge         int      `mapstructure:"max_age"`
	MaxBackups     int      `mapstructure:"max_backups"`
	Compress       bool     `mapstructure:"compress"`
	Development    bool     `mapstructure:"development"`
	EnableSampling bool     `mapstructure:"enable_sampling"`
	Console        bool     `mapstructure:"console"`
	GormConsole    bool     `mapstructure:"gorm_console"`
}

// JWTConfig 定义 JWT 相关配置
type JWTConfig struct {
	SecretKey string `mapstructure:"secretKey"` // JWT 密钥
	Expires   int    `mapstructure:"expires"`   // 过期时间（分钟）
	Issuer    string `mapstructure:"issuer"`    // 颁发者
}

// Temporal配置
type TemporalConfig struct {
	Address   string `mapstructure:"address"`
	Namespace string `mapstructure:"namespace"`
}

// PurchaseUrlTemplates 采购模块URL模板配置
type PurchaseUrlTemplates struct {
	Request  string `mapstructure:"request" json:"request"`   // 采购申请
	Inquiry  string `mapstructure:"inquiry" json:"inquiry"`   // 采购询价
	Contract string `mapstructure:"contract" json:"contract"` // 合同管理
	Payment  string `mapstructure:"payment" json:"payment"`   // 付款管理
	Arrival  string `mapstructure:"arrival" json:"arrival"`   // 到货管理
	Shipment string `mapstructure:"shipment" json:"shipment"` // 发货管理
	Invoice  string `mapstructure:"invoice" json:"invoice"`   // 发票管理
	Default  string `mapstructure:"default" json:"default"`   // 默认链接
}

// FeishuConfig 飞书机器人配置
type FeishuConfig struct {
	WebhookURL              string `mapstructure:"webhook_url" json:"webhook_url"`
	Secret                  string `mapstructure:"secret" json:"secret"`
	Enabled                 bool   `mapstructure:"enabled" json:"enabled"`
	TicketDetailUrlTemplate string `mapstructure:"ticket_detail_url_template" json:"ticket_detail_url_template"`

	// OAuth登录配置
	OAuth FeishuOAuthConfig `mapstructure:"oauth" json:"oauth"`
	// 采购通知专用配置
	PurchaseWebhookURL   string               `mapstructure:"purchase_webhook_url" json:"purchase_webhook_url"`
	PurchaseSecret       string               `mapstructure:"purchase_secret" json:"purchase_secret"`
	PurchaseUrlTemplates PurchaseUrlTemplates `mapstructure:"purchase_url_templates" json:"purchase_url_templates"`
	PurchaseEnabled      bool                 `mapstructure:"purchase_enabled" json:"purchase_enabled"`
	// 维修单专用配置
	RepairTicketDetailUrlTemplate string `mapstructure:"repair_ticket_detail_url_template" json:"repair_ticket_detail_url_template"`
	// 软件排班表专用配置
	SoftScheWebhookURL        string `mapstructure:"soft_sche_webhook_url" json:"soft_sche_webhook_url"`
	SoftScheSecret            string `mapstructure:"soft_sche_secret" json:"soft_sche_secret"`
	SoftScheDetailUrlTemplate string `mapstructure:"soft_sche_detail_url_template" json:"soft_sche_detail_url_template"`
	SoftScheTemplate          string `mapstructure:"soft_sche_template" json:"soft_sche_template"` //卡片ID
	SoftScheEnabled           bool   `mapstructure:"soft_sche_enabled" json:"soft_sche_enabled"`
	// 硬件排班表专用配置
	HardScheWebhookURL        string `mapstructure:"hard_sche_webhook_url" json:"hard_sche_webhook_url"`
	HardScheSecret            string `mapstructure:"hard_sche_secret" json:"hard_sche_secret"`
	HardScheDetailUrlTemplate string `mapstructure:"hard_sche_detail_url_template" json:"hard_sche_detail_url_template"`
	HardScheMorningTemplate   string `mapstructure:"hard_sche_morning_template" json:"hard_sche_morning_template"` //卡片ID
	HardScheEveningTemplate   string `mapstructure:"hard_sche_evening_template" json:"hard_sche_evening_template"`
	HardScheEnabled           bool   `mapstructure:"hard_sche_enabled" json:"hard_sche_enabled"`

	// 出入库单专用配置
	InboundWebhookURL              string `mapstructure:"inbound_webhook_url" json:"inbound_webhook_url"`
	InboundSecret                  string `mapstructure:"inbound_secret" json:"inbound_secret"`
	InboundTicketDetailUrlTemplate string `mapstructure:"inbound_ticket_detail_url_template" json:"inbound_ticket_detail_url_template"`

	// 维修单项目专用webhook映射配置（仅用于维修单待接单通知）
	RepairProjectWebhooks ProjectWebhookMappings `mapstructure:"repair_project_webhooks" json:"repair_project_webhooks"`

	// 保安群专用配置
	SecurityGuardWebhookURL string `mapstructure:"security_webhook_url" json:"security_webhook_url"`
	SecurityGuardSecret     string `mapstructure:"security_guard_secret" json:"security_guard_secret"`
}

// FeishuOAuthConfig 飞书OAuth登录配置
type FeishuOAuthConfig struct {
	AppID       string `mapstructure:"app_id" json:"app_id"`             // 飞书应用ID
	AppSecret   string `mapstructure:"app_secret" json:"app_secret"`     // 飞书应用密钥
	RedirectURI string `mapstructure:"redirect_uri" json:"redirect_uri"` // 授权回调地址
	Scope       string `mapstructure:"scope" json:"scope"`               // 授权范围，默认为空获取基本信息
	Enabled     bool   `mapstructure:"enabled" json:"enabled"`           // 是否启用飞书登录
	AutoCreate  bool   `mapstructure:"auto_create" json:"auto_create"`   // 是否自动创建用户
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
	TTL      int    `mapstructure:"ttl"` // 缓存过期时间（秒）
}

// EmailConfig 包含所有邮件相关的设置
type EmailConfig struct {
	SenderAccounts map[string]SenderAccount `mapstructure:"senderAccounts"` // 发件人账户映射
}

// SenderAccount 定义 SMTP 账户的凭证信息
type SenderAccount struct {
	Host     string `mapstructure:"host"`     // SMTP 服务器地址
	Port     int    `mapstructure:"port"`     // SMTP 端口
	Username string `mapstructure:"username"` // 登录用户名
	Password string `mapstructure:"password"` // 登录密码
	From     string `mapstructure:"from"`     // 发件人，如："技术支持 <<EMAIL>>"
}

// 加载配置，根据环境变量APP_ENV决定使用开发或生产环境配置
// APP_ENV=prod或APP_ENV=production时加载生产环境配置，其他情况加载开发环境配置
func LoadConfig() (*Config, error) {
	// 从环境变量中获取环境类型
	env := os.Getenv("APP_ENV")

	// 根据环境变量选择配置文件
	configFile := "config.dev"
	if env == "prod" || env == "production" {
		configFile = "config.prod"
	}

	viper.SetConfigName(configFile)
	viper.AddConfigPath("./configs")
	viper.SetConfigType("yaml")
	if err := viper.ReadInConfig(); err != nil {
		return nil, err
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, err
	}
	return &config, nil
}
