-- 为用户表添加飞书相关字段
-- 执行时间：2025-08-01

-- 添加飞书相关字段
ALTER TABLE users ADD COLUMN IF NOT EXISTS feishu_open_id VARCHAR(100) DEFAULT '' COMMENT '飞书用户open_id';
ALTER TABLE users ADD COLUMN IF NOT EXISTS feishu_union_id VARCHAR(100) DEFAULT '' COMMENT '飞书用户union_id';
ALTER TABLE users ADD COLUMN IF NOT EXISTS login_type VARCHAR(20) DEFAULT 'password' COMMENT '登录方式：password/feishu/both';
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login_time DATETIME NULL COMMENT '最后登录时间';
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login_ip VARCHAR(45) DEFAULT '' COMMENT '最后登录IP';

-- 添加索引
CREATE INDEX IF NOT EXISTS idx_users_feishu_open_id ON users(feishu_open_id);
CREATE INDEX IF NOT EXISTS idx_users_feishu_union_id ON users(feishu_union_id);
CREATE INDEX IF NOT EXISTS idx_users_login_type ON users(login_type);
CREATE INDEX IF NOT EXISTS idx_users_last_login_time ON users(last_login_time);

-- 更新现有用户的登录类型为password
UPDATE users SET login_type = 'password' WHERE login_type = '' OR login_type IS NULL;
