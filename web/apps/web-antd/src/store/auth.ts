import type { Recordable, UserInfo } from '@vben/types';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { LOGIN_PATH } from '@vben/constants';
import { preferences } from '@vben/preferences';
import { resetAllStores, useAccessStore, useUserStore } from '@vben/stores';

import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';

import {
  checkPermissionStatusApi,
  clearPermissionFlagApi,
  getAccessCodesApi,
  getUserInfoApi,
  loginApi,
  logoutApi,
} from '#/api';
import { $t } from '#/locales';

export const useAuthStore = defineStore('auth', () => {
  const accessStore = useAccessStore();
  const userStore = useUserStore();
  const router = useRouter();

  const loginLoading = ref(false);

  // 权限检查间隔时间（毫秒）
  const PERMISSION_CHECK_INTERVAL = 1 * 60 * 1000; // 1分钟
  let permissionCheckTimer: null | ReturnType<typeof setInterval> = null;

  /**
   * 异步处理登录操作
   * Asynchronously handle the login process
   * @param params 登录表单数据
   */
  async function authLogin(
    params: Recordable<any>,
    onSuccess?: () => Promise<void> | void,
  ) {
    // 异步处理用户登录操作并获取 accessToken
    let userInfo: null | UserInfo = null;
    try {
      loginLoading.value = true;
      const { accessToken } = await loginApi(params);

      // 如果成功获取到 accessToken
      if (accessToken) {
        accessStore.setAccessToken(accessToken);

        // 获取用户信息并存储到 accessStore 中
        const [fetchUserInfoResult, accessCodes] = await Promise.all([
          fetchUserInfo(),
          getAccessCodesApi(),
        ]);

        userInfo = fetchUserInfoResult;

        userStore.setUserInfo(userInfo);
        accessStore.setAccessCodes(accessCodes);

        // 登录成功后启动权限检查定时器
        startPermissionCheckTimer();

        if (accessStore.loginExpired) {
          accessStore.setLoginExpired(false);
        } else {
          onSuccess
            ? await onSuccess?.()
            : await router.push(
                userInfo.homePath || preferences.app.defaultHomePath,
              );
        }

        if (userInfo?.realName) {
          notification.success({
            description: `${$t('authentication.loginSuccessDesc')}:${userInfo?.realName}`,
            duration: 3,
            message: $t('authentication.loginSuccess'),
          });
        }
      }
    } finally {
      loginLoading.value = false;
    }

    return {
      userInfo,
    };
  }

  async function logout(redirect: boolean = true) {
    try {
      await logoutApi();
    } catch {
      // 不做任何处理
    }
    // 登出时停止权限检查定时器
    stopPermissionCheckTimer();

    resetAllStores();
    accessStore.setLoginExpired(false);

    // 回登录页带上当前路由地址
    await router.replace({
      path: LOGIN_PATH,
      query: redirect
        ? {
            redirect: encodeURIComponent(router.currentRoute.value.fullPath),
          }
        : {},
    });
  }

  async function fetchUserInfo() {
    let userInfo: null | UserInfo = null;
    userInfo = await getUserInfoApi();
    userStore.setUserInfo(userInfo);
    return userInfo;
  }

  /**
   * 刷新权限码
   * 用于后端动态更新权限后，前端刷新权限码
   */
  async function refreshAccessCodes() {
    try {
      const accessCodes = await getAccessCodesApi();
      accessStore.setAccessCodes(accessCodes);

      // 获取新的权限码后，手动清除权限更新标志
      await clearPermissionFlag();

      return accessCodes;
    } catch (error) {
      console.error('获取权限码失败', error);
      return [];
    }
  }

  /**
   * 清除权限更新标志
   * 在获取最新权限后调用，通知后端已经完成权限更新
   */
  async function clearPermissionFlag() {
    try {
      await clearPermissionFlagApi();
    } catch (error) {
      console.error('清除权限更新标志失败:', error);
    }
  }

  /**
   * 检查权限是否需要更新
   */
  async function checkPermissionUpdate() {
    // 未登录状态不检查
    if (!accessStore.accessToken) return;

    try {
      // 调用基于Redis标志位的GET API检查权限是否需要更新
      const { needRefresh } = await checkPermissionStatusApi();

      if (needRefresh) {
        // 权限有更新，刷新权限码
        await refreshAccessCodes();
      }
    } catch (error) {
      console.error('检查权限更新失败:', error);
      // 出错时继续保持定时检查，不影响正常业务
    }
  }

  /**
   * 启动权限检查定时器
   */
  function startPermissionCheckTimer() {
    // 确保不会重复启动
    stopPermissionCheckTimer();

    // 立即检查一次
    checkPermissionUpdate();

    // 设置定时器定期检查
    permissionCheckTimer = setInterval(
      checkPermissionUpdate,
      PERMISSION_CHECK_INTERVAL,
    );
  }

  /**
   * 停止权限检查定时器
   */
  function stopPermissionCheckTimer() {
    if (permissionCheckTimer) {
      clearInterval(permissionCheckTimer);
      permissionCheckTimer = null;
    }
  }

  function $reset() {
    loginLoading.value = false;
    stopPermissionCheckTimer();
  }

  return {
    $reset,
    authLogin,
    fetchUserInfo,
    loginLoading,
    logout,
    refreshAccessCodes,
    startPermissionCheckTimer,
    stopPermissionCheckTimer,
    clearPermissionFlag,
  };
});
