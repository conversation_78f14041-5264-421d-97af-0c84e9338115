<script lang="ts" setup>
import type { Dayjs } from 'dayjs';

import type { StockHistory } from '#/api/core/cmdb/inventory/inventory';

import { ref } from 'vue';

import {
  But<PERSON>,
  Card,
  DatePicker,
  Descriptions,
  Empty,
  Spin,
  Tag,
} from 'ant-design-vue';

const props = defineProps<{
  historyList: StockHistory[];
  loading: boolean;
}>();

const emit = defineEmits<{
  (e: 'timeRangeChange', startTime?: string, endTime?: string): void;
}>();

// 变更类型映射
const changeTypeMap: {
  [key: string]: { color: string; text: string };
} = {
  inbound: { text: '入库', color: 'green' },
  outbound: { text: '出库', color: 'purple' },
  adjust: { text: '调整', color: 'orange' },
  sync: { text: '同步', color: 'blue' },
  allocate: { text: '分配', color: 'blue' },
  release: { text: '释放', color: 'purple' },
};

// 时间范围
const timeRange = ref<[Dayjs, Dayjs] | undefined>(undefined);

// 格式化日期
const formatDate = (dateString?: string): string => {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  } catch (error) {
    console.error('日期格式化错误:', error);
    return dateString;
  }
};

// 时间范围变化处理
const handleTimeRangeChange = (
  value: [Dayjs, Dayjs] | [string, string] | undefined,
  dateStrings: [string, string],
) => {
  timeRange.value =
    Array.isArray(value) && typeof value[0] !== 'string'
      ? (value as [Dayjs, Dayjs])
      : undefined;
  if (!value || !dateStrings[0] || !dateStrings[1]) {
    emit('timeRangeChange');
    return;
  }

  emit('timeRangeChange', dateStrings[0], dateStrings[1]);
};

// 重置时间范围
const resetTimeRange = () => {
  timeRange.value = undefined;
  emit('timeRangeChange');
};
</script>

<template>
  <div class="stock-history-list">
    <div class="mb-4 flex items-center justify-between">
      <h3 class="m-0">变更历史</h3>
      <div class="flex items-center">
        <Button
          type="default"
          size="small"
          class="mr-2"
          @click="resetTimeRange"
        >
          查看全部
        </Button>
        <DatePicker.RangePicker
          v-model:value="timeRange"
          :show-time="{ format: 'HH:mm:ss' }"
          format="YYYY-MM-DD HH:mm:ss"
          @change="handleTimeRangeChange"
          class="w-96"
          :placeholder="['开始时间', '结束时间']"
        />
      </div>
    </div>

    <Spin :spinning="props.loading">
      <div
        v-if="props.historyList.length === 0"
        class="flex h-64 items-center justify-center"
      >
        <Empty description="暂无变更历史记录" />
      </div>
      <div v-else class="history-list">
        <Card
          v-for="item in props.historyList"
          :key="item.id"
          class="stock-history-card mb-4"
          :bordered="true"
        >
          <div class="mb-2 flex items-center justify-between">
            <div class="flex items-center">
              <Tag
                :color="changeTypeMap[item.change_type]?.color || 'default'"
                class="mr-2"
              >
                {{ changeTypeMap[item.change_type]?.text || item.change_type }}
              </Tag>
              <span class="text-lg font-medium">
                {{ item.change_amount >= 0 ? '+' : '' }}{{ item.change_amount }}
              </span>
            </div>
            <div class="text-gray-500">
              {{ formatDate(item.change_time) }}
            </div>
          </div>

          <Descriptions size="small" :column="2" bordered>
            <Descriptions.Item label="操作前数量">
              {{ item.old_quantity }}
            </Descriptions.Item>
            <Descriptions.Item label="操作后数量">
              {{ item.new_quantity }}
            </Descriptions.Item>
            <Descriptions.Item label="操作人">
              {{ item.operator || '系统' }}
            </Descriptions.Item>
            <Descriptions.Item label="批次号">
              {{ item.batch_number || '无' }}
            </Descriptions.Item>
            <template v-if="item.inbound_id > 0">
              <Descriptions.Item label="入库单">
                {{ item.inbound_id }}
              </Descriptions.Item>
            </template>
            <template v-if="item.outbound_id > 0">
              <Descriptions.Item label="出库单">
                {{ item.outbound_id }}
              </Descriptions.Item>
            </template>
          </Descriptions>

          <div class="mt-2">
            <strong>原因：</strong>
            <p class="mt-1 text-gray-700">{{ item.reason || '无' }}</p>
          </div>
        </Card>
      </div>
    </Spin>
  </div>
</template>

<style scoped>
.stock-history-card {
  box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
  transition: all 0.3s;
}

.stock-history-card:hover {
  box-shadow: 0 4px 12px rgb(0 0 0 / 15%);
}

.history-list {
  max-height: 600px;
  padding-right: 8px;
  overflow-y: auto;
}
</style>
