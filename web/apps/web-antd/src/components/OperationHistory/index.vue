<script lang="ts" setup>
import { Spin, Tag, Timeline, TimelineItem } from 'ant-design-vue';

import { formatToDateTime } from '#/utils/common/time';

interface OperationHistoryItem {
  action: string;
  approver_name?: string;
  operator_name?: string;
  created_at: string;
  operation_time?: string;
  comments?: string;
  from_status?: string;
  to_status?: string;
  action_display?: string;
  previous_status?: string;
  previous_status_display?: string;
  new_status?: string;
  new_status_display?: string;
}

interface StatusConfig {
  text: string;
  color: string;
}

interface Props {
  // 操作历史数据
  historyData: OperationHistoryItem[];
  // 加载状态
  loading?: boolean;
  // 状态配置映射
  statusConfig?: Record<string, StatusConfig>;
  // 是否显示状态变更信息
  showStatusChange?: boolean;
  // 空状态提示文本
  emptyText?: string;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  statusConfig: () => ({}),
  showStatusChange: true,
  emptyText: '暂无操作历史记录',
});

// 操作配置
const ACTION_CONFIG: Record<string, { color: string; text: string }> = {
  create: { text: '创建', color: 'blue' },
  submit: { text: '提交', color: 'blue' },
  approve: { text: '审批通过', color: 'green' },
  reject: { text: '审批拒绝', color: 'red' },
  rollback: { text: '回退', color: 'orange' },
  cancel: { text: '取消', color: 'gray' },
};

// 获取操作配置
const getActionConfig = (action: string) => {
  return ACTION_CONFIG[action] || { text: action, color: 'blue' };
};

// 获取状态配置
const getStatusConfig = (status: string) => {
  if (props.statusConfig && props.statusConfig[status]) {
    return props.statusConfig[status];
  }
  return { text: status, color: 'default' };
};
</script>

<template>
  <div
    class="overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm"
  >
    <div
      class="border-b border-gray-100 bg-gradient-to-r from-amber-50 to-yellow-50 px-6 py-4"
    >
      <h2 class="flex items-center text-lg font-semibold text-gray-900">
        <div class="mr-3 h-6 w-1 rounded-full bg-amber-500"></div>
        操作历史记录
      </h2>
    </div>
    <div class="p-6">
      <Spin :spinning="loading">
        <Timeline v-if="historyData.length > 0" class="custom-timeline !pl-0">
          <TimelineItem
            v-for="(history, index) in historyData"
            :key="index"
            :color="getActionConfig(history.action).color"
          >
            <template #dot>
              <div
                class="flex h-8 w-8 items-center justify-center rounded-full font-semibold text-white shadow-lg"
                :class="{
                  'bg-gradient-to-br from-green-500 to-green-600':
                    history.action === 'approve',
                  'bg-gradient-to-br from-red-500 to-red-600':
                    history.action === 'reject',
                  'bg-gradient-to-br from-orange-500 to-amber-600':
                    history.action === 'rollback',
                  'bg-gradient-to-br from-blue-500 to-blue-600':
                    history.action === 'create' || history.action === 'submit',
                  'bg-gradient-to-br from-gray-500 to-gray-600':
                    history.action === 'cancel',
                }"
              >
                <span v-if="history.action === 'approve'">✓</span>
                <span v-else-if="history.action === 'reject'">✗</span>
                <span v-else-if="history.action === 'rollback'">↺</span>
                <span
                  v-else-if="
                    history.action === 'create' || history.action === 'submit'
                  "
                  >+</span
                >
                <span v-else-if="history.action === 'cancel'">×</span>
                <span v-else>•</span>
              </div>
            </template>

            <div
              class="rounded-lg border border-gray-100 bg-white p-4 shadow-sm transition-shadow duration-200 hover:shadow-md"
            >
              <div class="mb-3 flex items-center gap-3">
                <div
                  class="flex h-8 w-8 items-center justify-center rounded-full text-white"
                  :class="{
                    'bg-gradient-to-br from-green-500 to-green-600':
                      history.action === 'approve',
                    'bg-gradient-to-br from-red-500 to-red-600':
                      history.action === 'reject',
                    'bg-gradient-to-br from-orange-500 to-amber-600':
                      history.action === 'rollback',
                    'bg-gradient-to-br from-blue-500 to-blue-600':
                      history.action === 'create' ||
                      history.action === 'submit',
                    'bg-gradient-to-br from-gray-500 to-gray-600':
                      history.action === 'cancel',
                  }"
                >
                  {{
                    String(
                      history.approver_name || history.operator_name || 'U',
                    ).charAt(0)
                  }}
                </div>
                <div class="flex-1">
                  <div class="font-medium text-gray-900">
                    {{
                      history.approver_name || history.operator_name || '系统'
                    }}
                  </div>
                  <div class="text-sm text-gray-500">
                    {{
                      formatToDateTime(
                        history.operation_time || history.created_at,
                      )
                    }}
                  </div>
                </div>
                <Tag
                  :color="getActionConfig(history.action).color"
                  class="!rounded-full !px-3 !py-1 !text-xs !font-medium shadow-sm"
                >
                  {{
                    history.action_display ||
                    getActionConfig(history.action).text
                  }}
                </Tag>
              </div>

              <!-- 状态变更信息 -->
              <div
                v-if="
                  showStatusChange &&
                  ((history.from_status && history.to_status) ||
                    (history.previous_status && history.new_status))
                "
                class="mb-3 flex items-center gap-2 text-sm"
              >
                <span class="text-gray-500">状态变更:</span>
                <Tag color="orange" size="small" class="!rounded-md !text-xs">
                  {{
                    history.previous_status_display ||
                    history.previous_status ||
                    getStatusConfig(history.from_status || '').text
                  }}
                </Tag>
                <span class="text-gray-400">→</span>
                <Tag color="green" size="small" class="!rounded-md !text-xs">
                  {{
                    history.new_status_display ||
                    history.new_status ||
                    getStatusConfig(history.to_status || '').text
                  }}
                </Tag>
              </div>

              <!-- 操作意见 -->
              <div
                v-if="history.comments"
                class="rounded-lg border-l-4 bg-gray-50 p-3"
                :class="{
                  'border-green-500': history.action === 'approve',
                  'border-red-500': history.action === 'reject',
                  'border-orange-500': history.action === 'rollback',
                  'border-blue-500':
                    history.action === 'create' || history.action === 'submit',
                  'border-gray-500': history.action === 'cancel',
                }"
              >
                <div
                  class="mb-1 text-xs font-semibold uppercase tracking-wide text-gray-500"
                >
                  {{
                    {
                      approve: '审批意见',
                      reject: '拒绝原因',
                      rollback: '回退原因',
                      create: '创建说明',
                      submit: '提交说明',
                      cancel: '取消原因',
                    }[history.action] || '操作说明'
                  }}
                </div>
                <div class="text-sm leading-relaxed text-gray-700">
                  {{ history.comments }}
                </div>
              </div>
            </div>
          </TimelineItem>
        </Timeline>
        <div
          v-else
          class="flex flex-col items-center justify-center py-12 text-gray-500"
        >
          <div class="mb-4 text-6xl">📋</div>
          <div class="text-lg font-medium">暂无操作记录</div>
          <div class="text-sm">{{ emptyText }}</div>
        </div>
      </Spin>
    </div>
  </div>
</template>

<style scoped>
.custom-timeline :deep(.ant-timeline-item-tail) {
  border-left: 2px solid #e5e7eb;
}

.custom-timeline :deep(.ant-timeline-item-content) {
  margin-left: 20px;
}
</style>
