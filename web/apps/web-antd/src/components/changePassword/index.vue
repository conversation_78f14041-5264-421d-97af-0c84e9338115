<script lang="ts" setup>
import type { VbenFormSchema } from '@vben/common-ui';

import { ref } from 'vue';

import { useVbenForm, useVbenModal, VbenButton, z } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { changePasswordApi } from '#/api/core/user';

// 表单实例引用，使用any类型避免类型错误
const formApiRef = ref<any>(null);

// 模态框状态
const isLoading = ref(false);

// 表单配置
const formSchema: VbenFormSchema[] = [
  {
    component: 'VbenInputPassword',
    componentProps: {
      placeholder: '请输入当前密码',
    },
    fieldName: 'oldPassword',
    label: '当前密码',
    rules: z.string().min(1, { message: '请输入当前密码' }),
  },
  {
    component: 'VbenInputPassword',
    componentProps: {
      placeholder: '请输入新密码',
      passwordStrength: true,
    },
    fieldName: 'newPassword',
    label: '新密码',
    renderComponentContent() {
      return {
        strengthText: () => '密码强度',
      };
    },
    rules: z.string().min(8, { message: '新密码长度不能少于8位' }),
  },
  {
    component: 'VbenInputPassword',
    componentProps: {
      placeholder: '请再次输入新密码',
    },
    fieldName: 'confirmPassword',
    label: '确认新密码',
    rules: z
      .string()
      .min(1, { message: '请确认新密码' })
      .refine(
        async (value) => {
          try {
            const formValues = await formApiRef.value?.getValues?.();
            return value === formValues?.newPassword;
          } catch {
            return false;
          }
        },
        { message: '两次输入的密码不一致' },
      ),
  },
];

// 使用useVbenForm创建表单
const [Form, formApi] = useVbenForm({
  schema: formSchema,
  showDefaultActions: false,
});

// 在表单API创建后立即将其赋值给ref
formApiRef.value = formApi;

// 创建模态框
const [Modal, modalApi] = useVbenModal({
  title: '修改密码',
  // 确认按钮点击处理
  onConfirm: async () => {
    try {
      isLoading.value = true;
      modalApi.setState({ confirmLoading: true });

      // 表单验证
      const valid = await formApi.validate();
      if (!valid) {
        modalApi.setState({ confirmLoading: false });
        isLoading.value = false;
        return;
      }

      // 获取表单值
      const values = await formApi.getValues();

      // 调用修改密码API
      await changePasswordApi({
        oldPassword: values.oldPassword,
        newPassword: values.newPassword,
      });

      // 成功后提示
      message.success('密码修改成功');

      // 关闭弹窗
      modalApi.setState({ isOpen: false, confirmLoading: false });
      isLoading.value = false;

      // 重置表单
      formApi.resetForm();
    } catch (error) {
      console.error('密码修改失败:', error);
      message.error('密码修改失败');
      // 重置确认按钮loading状态
      modalApi.setState({ confirmLoading: false });
      isLoading.value = false;
    }
  },
  // 取消按钮点击处理
  onCancel: () => {
    // 重置表单
    formApi.resetForm();
    // 关闭弹窗
    modalApi.setState({ isOpen: false });
  },
});

// 打开修改密码弹窗
function openModal() {
  // 打开弹窗
  modalApi.setState({ isOpen: true });
}
</script>

<template>
  <!-- 修改密码按钮 -->
  <VbenButton type="primary" @click="openModal">修改密码</VbenButton>

  <!-- 修改密码模态框 -->
  <Modal>
    <Form />
  </Modal>
</template>
