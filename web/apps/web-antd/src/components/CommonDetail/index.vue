<script lang="ts" setup>
import { computed, ref, watch } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { Descriptions, Empty, Tabs, Tag } from 'ant-design-vue';

// 定义属性
const props = defineProps<{
  data: any;
  fields: Array<{
    field: string;
    format?: (value: any, record?: any) => any;
    group?: string;
    label: string;
    statusMap?: any;
    type?: 'custom' | 'date' | 'status' | 'text';
  }>;
  showAuditLog?: boolean;
  showGroup?: boolean;
  statusMap?: { [key: string]: { color: string; text: string } };
  tableName?: string;
  title?: string;
}>();

const activeTab = ref('basic');
// const auditLogRef = ref();

// 获取嵌套属性值的函数
function getNestedValue(obj: any, path: string) {
  if (!obj || !path) return undefined;

  // 处理数组路径，如 items[0].name
  const pathParts = path.split(/[.[\]]/).filter(Boolean);

  let value = obj;
  for (const part of pathParts) {
    if (value === null || value === undefined) return undefined;
    value = value[part];
  }

  return value;
}

// 获取状态显示信息
function getStatusInfo(field: any, value: string) {
  const statusMap = field.statusMap || props.statusMap;
  if (!statusMap) return { text: value, color: 'default' };
  return statusMap[value] || { text: value, color: 'default' };
}

// 创建抽屉
const [Drawer, drawerApi] = useVbenDrawer({
  title: props.title || '详情',
});

// 获取分组字段
const groupedFields = computed(() => {
  if (!props.showGroup) return { 基本信息: props.fields };

  const groups: Record<string, typeof props.fields> = {};

  props.fields.forEach((field) => {
    const group = field.group || '基本信息';
    if (!groups[group]) {
      groups[group] = [];
    }
    groups[group].push(field);
  });

  return groups;
});

// 监听 data 变化，确保数据存在时才渲染内容
watch(
  () => props.data,
  (newData) => {
    if (newData) {
      // 确保数据存在时再打开抽屉
      activeTab.value = 'basic';
    }
  },
  { immediate: true },
);

// 暴露抽屉 API 给父组件
defineExpose({
  drawerApi,
});
</script>

<template>
  <Drawer>
    <template v-if="data">
      <Tabs v-model:active-key="activeTab">
        <Tabs.TabPane key="basic" tab="基本信息">
          <!-- 使用分组显示 -->
          <template v-if="showGroup">
            <div
              v-for="(groupFields, groupName) in groupedFields"
              :key="groupName"
              class="mb-6"
            >
              <!-- 在内部使用 groupFields 代替 fields -->
              <h3 class="mb-2 text-lg font-medium">{{ groupName }}</h3>
              <Descriptions bordered :column="1" size="middle">
                <Descriptions.Item
                  v-for="field in groupFields"
                  :key="field.field"
                  :label="field.label"
                >
                  <!-- 状态类型 -->
                  <template v-if="field.type === 'status'">
                    <Tag
                      :color="
                        getStatusInfo(field, getNestedValue(data, field.field))
                          .color
                      "
                    >
                      {{
                        getStatusInfo(field, getNestedValue(data, field.field))
                          .text
                      }}
                    </Tag>
                  </template>
                  <!-- 自定义插槽 -->
                  <template v-else-if="field.type === 'custom'">
                    <slot
                      :name="field.field"
                      :value="getNestedValue(data, field.field)"
                      :data="data"
                    ></slot>
                  </template>
                  <!-- 默认显示 -->
                  <template v-else>
                    {{
                      field.format
                        ? field.format(getNestedValue(data, field.field), data)
                        : getNestedValue(data, field.field) || '暂无'
                    }}
                  </template>
                </Descriptions.Item>
              </Descriptions>
            </div>
          </template>

          <!-- 不分组显示 -->
          <template v-else>
            <Descriptions bordered :column="1" size="middle" class="mt-4">
              <Descriptions.Item
                v-for="field in fields"
                :key="field.field"
                :label="field.label"
              >
                <!-- 状态类型 -->
                <template v-if="field.type === 'status'">
                  <Tag
                    :color="
                      getStatusInfo(field, getNestedValue(data, field.field))
                        .color
                    "
                  >
                    {{
                      getStatusInfo(field, getNestedValue(data, field.field))
                        .text
                    }}
                  </Tag>
                </template>
                <!-- 自定义插槽 -->
                <template v-else-if="field.type === 'custom'">
                  <slot
                    :name="field.field"
                    :value="getNestedValue(data, field.field)"
                    :data="data"
                  ></slot>
                </template>
                <!-- 默认显示 -->
                <template v-else>
                  {{
                    field.format
                      ? field.format(getNestedValue(data, field.field), data)
                      : getNestedValue(data, field.field) || '暂无'
                  }}
                </template>
              </Descriptions.Item>
            </Descriptions>
          </template>

          <slot name="basic"></slot>
        </Tabs.TabPane>
        <!-- <Tabs.TabPane
          v-if="showAuditLog && tableName"
          key="changelog"
          tab="变更记录"
        >
          <slot name="audit-log">
            <AuditLog
              ref="auditLogRef"
              :table-name="tableName"
              :entity-id="data.id"
            />
          </slot>
        </Tabs.TabPane> -->
        <slot name="tabs"></slot>
      </Tabs>
    </template>
    <template v-else>
      <Empty description="暂无数据" />
    </template>
  </Drawer>
</template>
