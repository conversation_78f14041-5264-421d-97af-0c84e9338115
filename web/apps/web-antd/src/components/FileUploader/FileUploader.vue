<script lang="ts" setup>
import type { UploadChangeParam, UploadProps } from 'ant-design-vue';

import type { ModuleFileListResult } from '#/api/core/upload';

import { ref, watch } from 'vue';

import { IconUpload } from '@vben/icons';

import { Image, message, Upload } from 'ant-design-vue';

import { getModuleFiles, uploadFileWithProgress } from '#/api/core/upload';

const props = defineProps<{
  accept?: string;
  // 新增紧凑模式
  compact?: boolean;
  disabled?: boolean;
  fileDescription: string;
  listType?: 'picture' | 'picture-card' | 'text';
  maxCount?: number;
  moduleId?: null | number;
  moduleType: string;
  multiple?: boolean;
  showFileList?: boolean;
  showUploadList?: boolean;
}>();

const emit = defineEmits(['update:fileList', 'filesUploaded']);

// 文件列表
const files = ref<ModuleFileListResult[]>([]);
// 临时存储上传文件，使用Ant Design的文件列表格式
const pendingFiles = ref<UploadProps['fileList']>([]);

// 监听moduleId变化，加载文件
watch(
  () => props.moduleId,
  (newId) => {
    if (newId) {
      loadFiles(newId);
    } else {
      files.value = [];
    }
  },
  { immediate: true },
);

// 加载关联文件
async function loadFiles(moduleId: number) {
  try {
    const allFiles = await getModuleFiles(props.moduleType, moduleId);

    // 根据文件描述过滤
    files.value = allFiles.filter(
      (file) => file.description === props.fileDescription,
    );
  } catch (error) {
    console.error(`获取${props.fileDescription}文件失败:`, error);
    files.value = [];
  }
}

// 动态获取文件上传的附加数据
const getUploadData = () => {
  const baseData = {
    module_type: props.moduleType,
    description: props.fileDescription,
  };

  // 如果有ID，则添加module_id
  if (props.moduleId) {
    return {
      ...baseData,
      module_id: props.moduleId,
    };
  }

  return baseData;
};

// 文件处理 - 仅用于自定义上传逻辑，通过返回false阻止默认上传行为
const beforeUpload: UploadProps['beforeUpload'] = () => {
  // 在handleChange中处理文件，这里只阻止默认上传
  return false;
};

// 删除文件
const handleRemove: UploadProps['onRemove'] = (file) => {
  pendingFiles.value =
    pendingFiles.value?.filter((item) => item.uid !== file.uid) || [];
  emit('update:fileList', pendingFiles.value);
  return true;
};

// 处理文件变化事件 - 这里是实际处理文件的地方
const handleChange = (info: UploadChangeParam) => {
  // 当文件被添加时，处理文件
  if (info.file.status === 'uploading' && info.file.originFileObj) {
    const fileExists = pendingFiles.value?.some((item) => {
      // 使用名称和大小来判断文件是否已存在
      const itemOriginFile = item.originFileObj as File | undefined;
      const infoOriginFile = info.file.originFileObj as File;

      return (
        item.name === info.file.name &&
        itemOriginFile &&
        itemOriginFile.size === infoOriginFile.size
      );
    });

    // 如果文件不存在于列表中，则添加
    if (!fileExists) {
      pendingFiles.value = [
        ...(pendingFiles.value || []),
        {
          uid: Date.now().toString(),
          name: info.file.name,
          status: 'uploading',
          url: URL.createObjectURL(info.file.originFileObj),
          originFileObj: info.file.originFileObj,
        },
      ];

      emit('update:fileList', pendingFiles.value);
    }
  }

  // 处理文件状态变化
  if (info.file.status === 'done') {
    message.success(`${info.file.name} 文件上传成功`);
  } else if (info.file.status === 'error') {
    message.error(`${info.file.name} 文件上传失败`);
  }
};

// 处理拖拽事件
function handleDrop(e: DragEvent) {
  e.preventDefault();
  // 在拖拽时可以添加一些视觉反馈
  const uploadElement = document.querySelector('.upload-dragger');
  if (uploadElement) {
    uploadElement.classList.remove('dragging');
  }
}

// 添加拖拽进入事件处理
function handleDragOver(e: DragEvent) {
  e.preventDefault();
  // 添加视觉反馈
  const uploadElement = document.querySelector('.upload-dragger');
  if (uploadElement) {
    uploadElement.classList.add('dragging');
  }
}

// 添加拖拽离开事件处理
function handleDragLeave(e: DragEvent) {
  e.preventDefault();
  // 移除视觉反馈
  const uploadElement = document.querySelector('.upload-dragger');
  if (uploadElement) {
    uploadElement.classList.remove('dragging');
  }
}

// 上传文件到服务器
async function uploadFilesToServer(targetId: number) {
  if (!pendingFiles.value || pendingFiles.value.length === 0) return [];

  const uploadPromises = pendingFiles.value.map((file) => {
    if (!file.originFileObj) return Promise.resolve(null);

    return new Promise((resolve, reject) => {
      uploadFileWithProgress({
        file: file.originFileObj as File, // 明确类型转换为File
        description: props.fileDescription,
        module_id: targetId,
        module_type: props.moduleType,
        onProgress: (progress) => {
          // 更新上传进度
          const updatedFile = pendingFiles.value?.find(
            (item) => item.uid === file.uid,
          );
          if (updatedFile) {
            updatedFile.percent = progress.percent;
          }
        },
        onSuccess: (data) => {
          // 更新文件状态为成功
          const updatedFile = pendingFiles.value?.find(
            (item) => item.uid === file.uid,
          );
          if (updatedFile) {
            updatedFile.status = 'done';
          }
          resolve(data);
        },
        onError: (error) => {
          // 更新文件状态为失败
          const updatedFile = pendingFiles.value?.find(
            (item) => item.uid === file.uid,
          );
          if (updatedFile) {
            updatedFile.status = 'error';
          }
          reject(error);
        },
      });
    });
  });

  try {
    const results = await Promise.all(uploadPromises);
    // 上传完成后重新加载文件列表
    if (targetId) {
      await loadFiles(targetId);
    }
    pendingFiles.value = [];
    emit('update:fileList', pendingFiles.value);
    emit('filesUploaded', results);
    return results;
  } catch (error) {
    console.error(`上传${props.fileDescription}文件失败:`, error);
    message.error(`上传${props.fileDescription}文件失败`);
    throw error;
  }
}

// 清空待上传文件
function clearPendingFiles() {
  pendingFiles.value = [];
  emit('update:fileList', pendingFiles.value);
}

// 暴露方法给父组件
defineExpose({
  uploadFilesToServer,
  clearPendingFiles,
  pendingFiles,
  files,
  beforeUpload,
  handleRemove,
  getUploadData,
  data: getUploadData,
  fileList: pendingFiles,
});
</script>

<template>
  <div>
    <!-- 显示文件列表 -->
    <div v-if="showFileList && files.length > 0" class="mb-2">
      <div class="flex flex-wrap gap-2">
        <div v-for="file in files" :key="file.id" class="w-20 overflow-hidden">
          <Image
            v-if="file.mime_type.startsWith('image/')"
            :src="file.url"
            :alt="file.file_name"
            class="h-20 w-20 rounded object-cover shadow-sm"
            :preview="{
              src: file.url,
              title: file.file_name,
            }"
          />
          <div
            v-else
            class="flex h-20 w-20 items-center justify-center rounded bg-gray-50 shadow-sm"
          >
            <span class="text-sm font-bold uppercase">{{
              file.file_name.split('.').pop()
            }}</span>
          </div>
          <div
            class="mt-1 truncate text-xs text-gray-500"
            :title="file.file_name"
          >
            {{ file.file_name }}
          </div>
        </div>
      </div>
    </div>
    <div
      v-else-if="showFileList && files.length === 0"
      class="empty-files mb-2"
    >
      暂无{{ fileDescription }}附件
    </div>

    <!-- 拖拽上传区域 -->
    <Upload.Dragger
      v-model:file-list="pendingFiles"
      name="file"
      :multiple="multiple"
      :max-count="maxCount"
      :accept="accept"
      :show-upload-list="showUploadList"
      :list-type="listType"
      :disabled="disabled"
      :custom-request="() => {}"
      :before-upload="beforeUpload"
      @remove="handleRemove"
      @change="handleChange"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragleave="handleDragLeave"
      class="upload-dragger"
      :class="{ 'upload-dragger-compact': compact }"
    >
      <div :class="[compact ? 'p-2' : 'p-3']">
        <div class="flex items-center justify-center">
          <div v-if="!compact" class="flex flex-col items-center">
            <p class="ant-upload-drag-icon mb-2">
              <IconUpload class="text-primary text-[32px]" />
            </p>
            <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p class="ant-upload-hint">支持单个或批量上传文件</p>
          </div>
          <div v-else class="flex items-center">
            <IconUpload class="text-primary mr-2 text-[24px]" />
            <div>
              <p class="text-sm font-medium">点击或拖拽上传</p>
              <p class="text-xs text-gray-400">支持多文件上传</p>
            </div>
          </div>
        </div>
        <div class="mt-2 flex justify-center">
          <slot></slot>
        </div>
      </div>
    </Upload.Dragger>
  </div>
</template>

<style scoped>
.empty-files {
  padding: 8px 0;
  font-size: 13px;
  color: #999;
}

.upload-dragger {
  transition: all 0.3s;
}

.upload-dragger-compact {
  min-height: auto !important;
}

:deep(.ant-upload-drag-icon) {
  margin-bottom: 0;
  color: #1890ff;
}

:deep(.ant-upload-text) {
  margin: 0 0 2px;
  font-size: 14px;
  color: #262626;
}

:deep(.ant-upload-hint) {
  font-size: 13px;
  color: rgb(0 0 0 / 45%);
}

:deep(.ant-upload.ant-upload-drag) {
  background: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  transition: all 0.3s;
}

:deep(.upload-dragger-compact .ant-upload.ant-upload-drag) {
  min-height: auto;
  padding: 8px;
}

:deep(.ant-upload.ant-upload-drag:hover),
:deep(.ant-upload.ant-upload-drag.dragging) {
  background: #f0f7ff;
  border-color: #1890ff;
}

/* 拖拽状态样式 */
.upload-dragger.dragging :deep(.ant-upload.ant-upload-drag) {
  background: #f0f7ff;
  border-color: #1890ff;
  border-style: dashed;
  border-width: 2px;
  box-shadow: 0 0 10px rgb(24 144 255 / 20%);
}

:deep(.ant-upload-btn) {
  padding: 0 !important;
}
</style>
