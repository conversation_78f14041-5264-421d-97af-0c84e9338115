<script lang="ts" setup>
import type {
  IDomEditor,
  IEditorConfig,
  IToolbarConfig,
} from '@wangeditor/editor';

/**
 * 基于 wangEditor 的富文本编辑器组件
 * 支持文本格式化、图片上传、粘贴图片等功能
 */
import { computed, onBeforeUnmount, ref, shallowRef, watch } from 'vue';

import { Editor, Toolbar } from '@wangeditor/editor-for-vue';

import '@wangeditor/editor/dist/css/style.css';

// 定义属性
const props = defineProps({
  // 编辑器内容
  modelValue: {
    type: String,
    default: '',
  },
  // 编辑器高度，默认300px
  height: {
    type: String,
    default: '300px',
  },
  // 编辑器模式（simple: 简洁模式, default: 默认模式）
  mode: {
    type: String,
    default: 'default',
  },
  // 占位符文本
  placeholder: {
    type: String,
    default: '请输入内容...',
  },
  // 自定义工具栏配置
  customToolbar: {
    type: Object,
    default: () => ({}),
  },
  // 是否只读
  readOnly: {
    type: Boolean,
    default: false,
  },
  // 编辑器最大内容长度
  maxLength: {
    type: Number,
    default: 5000,
  },
  // 是否允许上传图片
  allowUploadImage: {
    type: Boolean,
    default: true,
  },
  // 图片上传URL
  imageUploadUrl: {
    type: String,
    default: '',
  },
  // 图片上传的请求头部
  imageUploadHeaders: {
    type: Object,
    default: () => ({}),
  },
  // 图片上传字段名
  imageFieldName: {
    type: String,
    default: 'file',
  },
});

// 定义事件
const emit = defineEmits([
  'update:modelValue', // v-model双向绑定更新
  'change', // 内容变化
  'focus', // 聚焦
  'blur', // 失焦
  'created', // 编辑器创建完成
  'destroyed', // 编辑器销毁
  'maxLength', // 内容达到最大长度
  'error', // 错误信息
]);

// 编辑器实例 - 必须使用shallowRef
const editorRef = shallowRef<IDomEditor | null>(null);

// HTML内容
const valueHtml = ref(props.modelValue);

// 工具栏配置
const toolbarConfig = computed<Partial<IToolbarConfig>>(() => {
  const baseConfig: Partial<IToolbarConfig> = {
    excludeKeys: [],
  };

  // 合并自定义工具栏配置
  return { ...baseConfig, ...props.customToolbar };
});

// 编辑器配置
const editorConfig = computed<Partial<IEditorConfig>>(() => {
  const baseConfig: Partial<IEditorConfig> = {
    placeholder: props.placeholder,
    readOnly: props.readOnly,
    maxLength: props.maxLength,
    MENU_CONF: {},
  };

  // 配置图片上传
  if (props.allowUploadImage && props.imageUploadUrl) {
    baseConfig.MENU_CONF = {
      uploadImage: {
        server: props.imageUploadUrl,
        fieldName: props.imageFieldName,
        headers: props.imageUploadHeaders,
        // 上传之前触发
        onBeforeUpload(file: File) {
          // 限制图片格式和大小
          const isImage = /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(file.name);
          const isLt5M = file.size / 1024 / 1024 < 5;

          if (!isImage) {
            emit('error', '只能上传图片文件!');
            return false;
          }
          if (!isLt5M) {
            emit('error', '图片大小不能超过 5MB!');
            return false;
          }
          return true;
        },
        // 上传成功回调
        onSuccess(file: File, res: any) {
          console.log('图片上传成功', file, res);
          // 可以在这里做一些额外处理，如果后端返回的格式需要适配
          return res.data?.url || res.url || '';
        },
        // 上传失败回调
        onError(file: File, err: any) {
          console.error('图片上传失败', file, err);
          emit('error', `图片上传失败: ${err.message || '未知错误'}`);
        },
      },
    };
  }

  return baseConfig;
});

// 处理编辑器创建完成事件
const handleCreated = (editor: IDomEditor) => {
  editorRef.value = editor;
  emit('created', editor);
};

// 处理编辑器内容变化
const handleChange = (editor: IDomEditor) => {
  const html = editor.getHtml();
  valueHtml.value = html;
  emit('update:modelValue', html);
  emit('change', { html, text: editor.getText(), editor });
};

// 处理聚焦事件
const handleFocus = (editor: IDomEditor) => {
  emit('focus', editor);
};

// 处理失焦事件
const handleBlur = (editor: IDomEditor) => {
  emit('blur', editor);
};

// 处理内容超出最大长度事件
// 注意: wangEditor 的 onMaxLength 事件会传入 editor 参数
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const handleMaxLength = (_editor: IDomEditor) => {
  const editor = editorRef.value;
  if (!editor) return;
  emit('maxLength', editor);
};

// 监听props.modelValue变化，更新编辑器内容
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue !== valueHtml.value) {
      valueHtml.value = newValue;
    }
  },
);

// 监听readOnly变化
watch(
  () => props.readOnly,
  (readOnly) => {
    if (editorRef.value) {
      editorRef.value.enable(!readOnly);
    }
  },
);

// 暴露编辑器实例和方法
const insertText = (text: string) => {
  const editor = editorRef.value;
  if (!editor) return;
  editor.insertText(text);
};

const insertHTML = (html: string) => {
  const editor = editorRef.value;
  if (!editor) return;
  // 使用可用的API插入HTML，实际方法可能因版本不同而异
  try {
    // @ts-ignore 忽略类型检查
    editor.dangerouslyInsertHtml(html);
  } catch (error) {
    console.warn('插入HTML失败，可能是API不匹配', error);
    // 尝试备用方法
    try {
      // @ts-ignore
      editor.setHtml(editor.getHtml() + html);
    } catch (error) {
      console.error('插入HTML失败', error);
    }
  }
};

const insertImage = (src: string, alt?: string) => {
  const editor = editorRef.value;
  if (!editor) return;
  // 尝试使用编辑器API插入图片
  try {
    // @ts-ignore 忽略类型检查
    if (typeof editor.insertImage === 'function') {
      // @ts-ignore
      editor.insertImage({
        src,
        alt: alt || '',
      });
    } else {
      // 插入图片HTML
      const imgHtml = `<img src="${src}" alt="${alt || ''}" />`;
      insertHTML(imgHtml);
    }
  } catch (error) {
    console.error('插入图片失败', error);
    // 插入图片HTML作为备用
    const imgHtml = `<img src="${src}" alt="${alt || ''}" />`;
    insertHTML(imgHtml);
  }
};

const clear = () => {
  const editor = editorRef.value;
  if (!editor) return;
  editor.clear();
  valueHtml.value = '';
  emit('update:modelValue', '');
};

// 销毁编辑器实例
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (!editor) return;
  editor.destroy();
  emit('destroyed', editor);
});

// 定义对外暴露的属性和方法
defineExpose({
  editor: editorRef,
  insertText,
  insertHTML,
  insertImage,
  clear,
});
</script>

<template>
  <div class="rich-text-editor">
    <Toolbar
      :editor="editorRef"
      :default-config="toolbarConfig"
      :mode="mode"
      class="rich-text-editor-toolbar"
    />
    <Editor
      v-model="valueHtml"
      :default-config="editorConfig"
      :mode="mode"
      :style="{ height }"
      class="rich-text-editor-content"
      @on-created="handleCreated"
      @on-change="handleChange"
      @on-focus="handleFocus"
      @on-blur="handleBlur"
      @on-max-length="handleMaxLength"
    />
  </div>
</template>

<style scoped>
.rich-text-editor {
  z-index: 100;
  border: 1px solid #ccc;
}

.rich-text-editor-toolbar {
  border-bottom: 1px solid #ccc;
}

.rich-text-editor-content {
  overflow-y: auto;
}

:deep(.w-e-text-container [data-slate-editor]) {
  padding: 0 10px;
}

/* 适配暗黑模式 */
:deep(.dark .w-e-toolbar),
:deep(.dark .w-e-text-container) {
  color: #f5f5f5 !important;
  background-color: #1f1f1f !important;
}

:deep(.dark .w-e-toolbar .w-e-menu-item-active) {
  background-color: #303030 !important;
}

:deep(.dark .w-e-text-container [data-slate-editor]) {
  color: #f5f5f5 !important;
}
</style>
