<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { computed, h, ref } from 'vue';

import { AccessControl } from '@vben/access';
import { Page, useVbenDrawer } from '@vben/common-ui';

import { Button, message, Modal, Tag } from 'ant-design-vue';
import XEUtils from 'xe-utils';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  downloadImportTemplate as downloadTemplate,
  importDataApi,
} from '#/api/core/import';
import { useConfirmModal } from '#/hooks/useConfirmModal';

// 定义属性
const props = defineProps<{
  // API 函数
  api: {
    add: (data: any) => Promise<any>;
    delete: (id: any) => Promise<any>;
    edit: (data: any) => Promise<any>;
    getList: (params: any) => Promise<any>;
  };
  // 默认数据
  defaultData: any;
  // 抽屉自定义类名
  drawerClass?: string;
  // 编辑表单配置
  editFormOptions: VbenFormProps;
  // 是否启用二次确认
  enableConfirm?: boolean;
  // 表单配置
  formOptions: VbenFormProps;
  // 表格配置
  gridOptions: VxeGridProps<any>;
  // 导入模型类型
  importModelType?: string;
  // 权限控制
  permissions?: {
    add?: string[];
    delete?: string[];
    edit?: string[];
    import?: string[];
  };
  // 是否显示新增按钮
  showAddButton?: boolean;
  // 是否显示删除按钮
  showDeleteButton?: boolean;
  // 是否显示详情按钮
  showDetailButton?: boolean;
  // 是否显示编辑按钮
  showEditButton?: boolean;
  // 是否显示导入按钮
  showImportButton?: boolean;
  // 是否显示下载模板按钮
  showTemplateButton?: boolean;
  // 状态映射
  statusMap?: { [key: string]: { color: string; text: string } };
}>();

const emit = defineEmits([
  'detail',
  'add',
  'edit',
  'delete',
  'created',
  'edited',
  'deleted',
  'importSuccess', // 修改这里
  'importError', // 修改这里
]);

const selectRow = ref<any | null>(null);
const detailData = ref<any | null>(null);
// 存储当前表单数据，用于导出
const currentFormData = ref<Record<string, any>>({});

// 获取确认对话框hook
const { showConfirm, showDeleteConfirm, showAddConfirm, showEditConfirm } =
  useConfirmModal();

// 获取状态显示信息
function getStatusInfo(status: string) {
  if (!props.statusMap) return { text: status, color: 'default' };
  return props.statusMap[status] || { text: status, color: 'default' };
}

// 处理数据导入
async function handleImportData({
  file,
  options,
}: {
  file: File;
  options?: any;
}) {
  if (!props.importModelType) {
    message.error('未指定导入模型类型');
    return { status: false };
  }

  try {
    // 从VXE表格获取导入模式
    const importMode = options?.mode || 'overwrite';

    // 映射导入模式到后端期望的值
    let backendMode = 'overwrite';
    switch (importMode) {
      case 'insert': {
        backendMode = 'append_bottom';
        break;
      }
      case 'prepend': {
        backendMode = 'append_top';
        break;
      }
      case 'update': {
        backendMode = 'overwrite';
        // No default
        break;
      }
    } // 覆盖

    message.loading({ content: '正在导入数据...', key: 'import' });

    // 调用导入API，传递导入模式
    const result = await importDataApi(
      file,
      props.importModelType,
      backendMode,
    );

    // 显示导入结果
    if (result.failCount === 0) {
      message.success({
        content: `成功导入${result.successCount}条数据`,
        key: 'import',
      });
    } else {
      // 有失败的记录，显示详细错误信息
      message.warning({
        content: `成功导入${result.successCount}条数据，失败${result.failCount}条`,
        key: 'import',
        duration: 5, // 延长显示时间
      });

      // 显示每条失败记录的具体错误信息
      if (result.failures && result.failures.length > 0) {
        // 创建错误消息HTML
        const errorList = result.failures
          .map(
            (failure) =>
              `<li style="margin-bottom: 8px; color: #ff4d4f;">第${failure.row}行: ${failure.message} ${failure.data.SN ? `(SN: ${failure.data.SN})` : ''}</li>`,
          )
          .join('');

        // 显示详细错误信息对话框
        Modal.error({
          title: '导入错误详情',
          icon: h('ExclamationCircleOutlined', { style: 'color: #ff4d4f' }),
          content: h('div', {
            innerHTML: `
              <div style="max-height: 400px; overflow-y: auto; padding: 8px;">
                <p style="font-size: 14px; margin-bottom: 12px;">以下记录导入失败：</p>
                <ul style="padding-left: 20px; margin-bottom: 12px;">${errorList}</ul>
                <p style="font-size: 14px; color: #666;">请修正后重新导入。</p>
              </div>
            `,
          }),
          width: 600,
          class: 'import-error-modal',
        });
      }
    }

    // 刷新表格数据
    gridApi.reload();

    // 触发导入成功事件
    emit('importSuccess', result);

    return { status: true };
  } catch (error: any) {
    // 处理API错误
    const errorMsg = error.message || '未知错误';

    // 检查是否有详细错误信息
    if (error.data && error.data.failures && error.data.failures.length > 0) {
      const failureList = error.data.failures
        .map(
          (failure: any) =>
            `<li style="margin-bottom: 8px; color: #ff4d4f;">第${failure.row}行: ${failure.message} ${failure.data?.SN ? `(SN: ${failure.data.SN})` : ''}</li>`,
        )
        .join('');

      Modal.error({
        title: '导入失败',
        icon: h('ExclamationCircleOutlined', { style: 'color: #ff4d4f' }),
        content: h('div', {
          innerHTML: `
            <div style="max-height: 400px; overflow-y: auto; padding: 8px;">
              <p style="font-size: 14px; margin-bottom: 12px;">${errorMsg}</p>
              <p style="font-size: 14px; margin-bottom: 8px;">详细错误：</p>
              <ul style="padding-left: 20px; margin-bottom: 12px;">${failureList}</ul>
            </div>
          `,
        }),
        width: 600,
        class: 'import-error-modal',
      });
    } else {
      message.error({
        content: `导入失败: ${errorMsg}`,
        key: 'import',
      });
    }

    // 触发导入失败事件
    emit('importError', error);

    return { status: false };
  }
}

// 下载导入模板函数
async function downloadImportTemplate() {
  if (!props.importModelType) {
    message.error('未指定导入模型类型');
    return;
  }

  try {
    message.loading({ content: '正在下载模板...', key: 'downloadTemplate' });
    await downloadTemplate(props.importModelType);
    message.success({ content: '模板下载成功', key: 'downloadTemplate' });
  } catch (error: any) {
    message.error({
      content: error.message || '下载模板失败',
      key: 'downloadTemplate',
    });
  }
}

// 检查用户是否有导入权限
const hasImportPermission = computed(() => {
  if (!props.permissions?.import) return true;
  // 修改判断逻辑，只要有权限值就返回true，不再仅检查'super'
  return props.permissions.import.length > 0;
});

// 初始化表格
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: props.formOptions,
  gridOptions: {
    ...props.gridOptions,
    // 确保当toolbarConfig.import为true时，importConfig也被正确设置
    importConfig:
      (props.showImportButton !== false ||
        props.gridOptions.toolbarConfig?.import === true) &&
      hasImportPermission.value
        ? {
            // 首先使用传入的配置作为基础
            ...props.gridOptions.importConfig,
            // 然后确保必要的属性存在
            types: ['csv'],
            remote: true,
            // 确保显示模式选择
            modes: [
              { label: '插入方式（在表格数据底部追加）', value: 'insert' },
              { label: '覆盖方式（直接覆盖表格数据）', value: 'update' },
            ],
            importMethod: handleImportData,
          }
        : undefined,
    // 确保toolbarConfig中的import设置与importConfig保持一致
    toolbarConfig: {
      ...props.gridOptions.toolbarConfig,
      import:
        (props.showImportButton !== false ||
          props.gridOptions.toolbarConfig?.import === true) &&
        hasImportPermission.value,
    },
    // 确保导出配置正确设置
    exportConfig: {
      // 首先使用传入的配置作为基础
      ...props.gridOptions.exportConfig,
      // 设置默认导出模式包含全量导出
      modes: props.gridOptions.exportConfig?.modes || [
        'current',
        'selected',
        'all',
      ],
      // 设置默认导出类型
      type: props.gridOptions.exportConfig?.type || 'csv',
      // 添加导出之前的钩子函数，确保表单数据传递
      beforeExportMethod: ({ options }: any) => {
        // 使用存储的表单数据
        options._formValues = currentFormData.value;
        return true;
      },
    },
    proxyConfig: {
      ajax: {
        query: async (
          { page }: { page: { currentPage: number; pageSize: number } },
          formValues: Record<string, any>,
        ) => {
          // 保存当前表单数据，用于导出
          currentFormData.value = { ...formValues };

          const res = await props.api.getList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return {
            items: res.list || res.items || res,
            total:
              res.total ||
              (() => {
                if (res.list) return res.list.length;
                if (res.items) return res.items.length;
                return 0;
              })(),
          };
        },
        // 添加全量导出接口
        queryAll: async (params: { options?: Record<string, any> }) => {
          // 全量查询不使用分页参数
          try {
            // 使用保存的表单数据或从选项中获取
            const formValues =
              params.options?._formValues || currentFormData.value || {};

            const res = await props.api.getList({
              page: 1,
              pageSize: 99_999, // 设置一个足够大的数值来获取全部数据
              ...formValues, // 传递过滤参数
            });

            return {
              items: res.list || res.items || res,
              total:
                res.total ||
                (() => {
                  if (res.list) return res.list.length;
                  if (res.items) return res.items.length;
                  if (Array.isArray(res)) return res.length;
                  return 0;
                })(),
            };
          } catch (error) {
            console.error('全量导出数据获取失败:', error);
            message.error('全量导出数据获取失败');
            return { items: [], total: 0 };
          }
        },
      },
    },
  },
});

// 初始化表单
const [Form, formApi] = useVbenForm(props.editFormOptions);

// 新增/编辑抽屉
const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    try {
      const { valid } = await formApi.validate();
      if (!valid) return;

      // 如果启用了二次确认，则显示确认对话框
      if (props.enableConfirm) {
        try {
          const isEdit = !!selectRow.value;
          if (isEdit) {
            const displayName =
              selectRow.value.name ||
              selectRow.value.title ||
              selectRow.value.templateName ||
              selectRow.value.id;
            await showEditConfirm(displayName);
          } else {
            await showAddConfirm();
          }
        } catch {
          return; // 用户取消确认
        }
      }

      // 获取表单值
      const values = await formApi.getValues();
      // 编辑时补充 ID 字段
      if (selectRow.value) {
        values.id = selectRow.value.id;
      }

      // 锁定抽屉
      drawerApi.lock();

      // 根据是否为编辑模式调用不同API
      const isEdit = !!selectRow.value;
      const result = await (isEdit
        ? props.api.edit(values)
        : props.api.add(values));

      // 保存成功后关闭抽屉，并刷新表格数据
      message.success('保存成功');
      drawerApi.close();
      gridApi.reload();

      // 触发相应的事件
      if (isEdit) {
        emit('edited', result);
      } else {
        emit('created', result);
      }
    } catch {
      message.error('保存失败');
      // 解锁抽屉
      drawerApi.unlock();
    }
  },
});

// 新增事件
function addEvent() {
  selectRow.value = null;
  // 先重置表单，确保从干净状态开始
  formApi.resetForm();
  formApi.setValues(XEUtils.clone(props.defaultData, true));
  drawerApi.open();
  emit('add');
}

// 编辑事件
function editEvent(row: any) {
  selectRow.value = row;
  // 先重置表单，清除之前的数据
  formApi.resetForm();
  const newData = Object.assign(XEUtils.clone(props.defaultData, true), row);
  formApi.setValues(newData);
  drawerApi.open();
  emit('edit', row);
}

// 详情事件
function detailEvent(row: any) {
  detailData.value = row;
  emit('detail', row);
}

// 批量删除函数
async function batchDeleteEvent() {
  const selectedRows = gridApi.grid.getCheckboxRecords();
  if (selectedRows.length === 0) {
    message.warning('请选择要删除的记录');
    return;
  }

  if (props.enableConfirm) {
    // 如果启用了二次确认，则显示确认对话框
    try {
      if (selectedRows.length === 1) {
        const row = selectedRows[0];
        const displayName = row.name || row.title || row.templateName || row.id;
        await showDeleteConfirm(displayName);
      } else {
        await showDeleteConfirm(undefined, selectedRows.length);
      }

      // 用户确认删除，执行删除操作
      await performDelete(selectedRows);
    } catch {}
  } else {
    // 不启用二次确认，直接删除
    await performDelete(selectedRows);
  }
}

// 执行删除操作
async function performDelete(selectedRows: any[]) {
  try {
    // 可以选择一次删除一条或批量删除
    for (const row of selectedRows) {
      await props.api.delete(row.id);
    }
    message.success(`成功删除 ${selectedRows.length} 条记录`);
    gridApi.reload();
    emit('delete', selectedRows);
    emit('deleted', selectedRows);
  } catch {
    message.error('删除失败');
  }
}

// 暴露方法给父组件
defineExpose({
  gridApi,
  formApi,
  drawerApi,
  addEvent,
  editEvent,
  detailEvent,
  batchDeleteEvent,
  downloadImportTemplate,
  handleImportData,
  reload: () => gridApi.reload(),
  showConfirm, // 暴露确认框方法供外部使用
});
</script>

<template>
  <Page auto-content-height>
    <!-- 表格区域 -->
    <Grid>
      <!-- 状态插槽 -->
      <template #status="{ row }">
        <Tag :color="getStatusInfo(row.status).color">
          {{ getStatusInfo(row.status).text }}
        </Tag>
      </template>

      <!-- 操作插槽 -->
      <template #operate="{ row }">
        <AccessControl
          v-if="showEditButton !== false"
          :codes="permissions?.edit || ['super']"
          type="code"
        >
          <Button type="link" @click="editEvent(row)">编辑</Button>
        </AccessControl>
        <Button
          v-if="showDetailButton !== false"
          type="link"
          @click="detailEvent(row)"
        >
          详情
        </Button>
        <slot name="operate" :row="row"></slot>
      </template>

      <!-- 工具栏按钮插槽 -->
      <template #toolbar-buttons>
        <AccessControl
          v-if="showAddButton !== false"
          :codes="permissions?.add || ['super']"
          type="code"
        >
          <Button class="mr-2" type="primary" @click="addEvent">新增</Button>
        </AccessControl>
        <AccessControl
          v-if="showDeleteButton !== false"
          :codes="permissions?.delete || ['super']"
          type="code"
        >
          <Button class="mr-2" danger @click="batchDeleteEvent">删除</Button>
        </AccessControl>
        <slot name="toolbar-buttons"></slot>
      </template>

      <!-- 工具栏工具插槽 - 添加下载模板按钮 -->
      <template #toolbar-tools>
        <AccessControl
          v-if="showTemplateButton !== false && importModelType"
          :codes="permissions?.import || ['super']"
          type="code"
        >
          <Button class="mr-2" type="primary" @click="downloadImportTemplate">
            下载导入模板
          </Button>
        </AccessControl>
        <slot name="toolbar-tools"></slot>
      </template>

      <!-- 其他自定义插槽 -->
      <template v-for="(_, name) in $slots" :key="name" #[name]="slotData">
        <slot :name="name" v-bind="slotData"></slot>
      </template>
    </Grid>

    <!-- 新增/编辑抽屉，完全移除confirm-loading属性 -->
    <Drawer :title="selectRow ? '编辑' : '新增'" :class="drawerClass">
      <Form />
      <slot name="form"></slot>
    </Drawer>
  </Page>
</template>
