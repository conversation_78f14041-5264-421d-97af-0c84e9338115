<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { OperationLog } from '#/api/core/audit/operation-log';

import { h } from 'vue';

import { Button, Modal, Space, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getOperationLogsApi } from '#/api/core/audit/operation-log';

// 暴露给父组件的属性
const props = defineProps({
  module: {
    type: String,
    default: '',
  },
  operation: {
    type: String,
    default: '',
  },
  username: {
    type: String,
    default: '',
  },
});

// 格式化日期时间
function formatToDateTime(date: Date | number | string): string {
  if (!date) return '';
  const d = new Date(date);
  return d.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
}

// 表格搜索表单
const formOptions: VbenFormProps = {
  collapsed: true,
  collapsedRows: 1,
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入模块名称',
        allowClear: true,
      },
      fieldName: 'module',
      label: '模块',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入操作类型',
        allowClear: true,
      },
      fieldName: 'operation',
      label: '操作类型',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入操作人',
        allowClear: true,
      },
      fieldName: 'username',
      label: '操作人',
    },
    {
      component: 'RangePicker',
      componentProps: {
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        showTime: { format: 'HH:mm:ss' },
        placeholder: ['开始时间', '结束时间'],
      },
      fieldName: 'time_range',
      label: '操作时间',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: true,
};

// 表格配置
const gridOptions: VxeGridProps<OperationLog> = {
  columns: [
    { field: 'id', title: 'ID', width: 80 },
    {
      field: 'operation_time',
      title: '操作时间',
      width: 180,
      formatter: ({ cellValue }) => {
        return formatToDateTime(cellValue);
      },
    },
    { field: 'module', title: '模块', width: 120 },
    {
      field: 'operation',
      title: '操作类型',
      width: 100,
      slots: { default: 'operation_type' },
    },
    { field: 'username', title: '操作人', width: 120 },
    { field: 'request_method', title: '请求方式', width: 90 },
    { field: 'request_url', title: '请求路径', width: 200 },
    {
      field: 'status',
      title: '状态',
      width: 80,
      slots: { default: 'status' },
    },
    {
      field: 'execution_time',
      title: '执行时间(ms)',
      width: 120,
    },
    { field: 'ip_address', title: 'IP地址', width: 150 },
    {
      field: 'operation',
      title: '操作',
      width: 180,
      slots: { default: 'operation' },
    },
  ],
  keepSource: true,
  height: 600,
  rowConfig: { isHover: true },
  pagerConfig: { pageSize: 10, pageSizes: [10, 20, 50, 100] },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        // 处理表单值
        const params: any = {
          page: page.currentPage,
          page_size: page.pageSize,
        };

        // 添加筛选条件
        if (formValues.module) {
          params.module = formValues.module;
        } else if (props.module) {
          params.module = props.module;
        }

        if (formValues.operation) {
          params.operation = formValues.operation;
        } else if (props.operation) {
          params.operation = props.operation;
        }

        if (formValues.username) {
          params.username = formValues.username;
        } else if (props.username) {
          params.username = props.username;
        }

        // 处理时间范围
        if (formValues.time_range && formValues.time_range.length === 2) {
          params.start_time = formValues.time_range[0];
          params.end_time = formValues.time_range[1];
        }

        try {
          const res = await getOperationLogsApi(params);
          return {
            items: res.list,
            total: res.total,
          };
        } catch (error) {
          console.error('获取操作日志失败:', error);
          return {
            items: [],
            total: 0,
          };
        }
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    export: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });

// 获取操作类型的颜色
function getOperationTypeColor(type: string): string {
  switch (type.toLowerCase()) {
    case 'create': {
      return 'success';
    }
    case 'delete': {
      return 'error';
    }
    case 'update': {
      return 'warning';
    }
    default: {
      return 'default';
    }
  }
}

// 获取状态的颜色
function getStatusColor(status: string): string {
  switch (status) {
    case '异常': {
      return 'error';
    }
    case '正常': {
      return 'success';
    }
    default: {
      return 'default';
    }
  }
}

// 查看请求详情
function viewRequestDetails(log: OperationLog) {
  let requestBody = '';
  try {
    requestBody = JSON.stringify(JSON.parse(log.request_body), null, 2);
  } catch {
    requestBody = log.request_body;
  }

  Modal.info({
    title: '请求详情',
    width: 800,
    content: h('div', {
      style: 'max-height: 500px; overflow: auto;',
      innerHTML: `<pre>${requestBody}</pre>`,
    }),
  });
}

// 查看响应详情
function viewResponseDetails(log: OperationLog) {
  let responseBody = '';
  try {
    responseBody = JSON.stringify(JSON.parse(log.response_body), null, 2);
  } catch {
    responseBody = log.response_body;
  }

  Modal.info({
    title: '响应详情',
    width: 800,
    content: h('div', {
      style: 'max-height: 500px; overflow: auto;',
      innerHTML: `<pre>${responseBody}</pre>`,
    }),
  });
}

// 刷新数据
function refresh() {
  gridApi.reload();
}

// 暴露方法给父组件
defineExpose({
  refresh,
});
</script>

<template>
  <div class="w-full">
    <!-- 表格区域 -->
    <Grid>
      <template #operation_type="{ row }">
        <Tag :color="getOperationTypeColor(row.operation)">
          {{ row.operation }}
        </Tag>
      </template>
      <template #status="{ row }">
        <Tag :color="getStatusColor(row.status)">
          {{ row.status }}
        </Tag>
      </template>
      <template #operation="{ row }">
        <Space>
          <Button type="link" size="small" @click="viewRequestDetails(row)">
            请求详情
          </Button>
          <Button type="link" size="small" @click="viewResponseDetails(row)">
            响应详情
          </Button>
        </Space>
      </template>
    </Grid>
  </div>
</template>
