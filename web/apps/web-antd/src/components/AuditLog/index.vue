<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { OperationLog } from '#/api/core/audit/operation-log';

import { h, onMounted, ref } from 'vue';

import { Button, Modal, Space, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getOperationLogsApi } from '#/api/core/audit/operation-log';
import { getUserListApi } from '#/api/core/user';
import { formatToDateTime } from '#/utils/common/time';

// 暴露给父组件的属性
const props = defineProps({
  module: {
    type: String,
    default: '',
  },
  operation: {
    type: String,
    default: '',
  },
  username: {
    type: String,
    default: '',
  },
});

// 用户列表数据
const userOptions = ref<{ label: string; value: string }[]>([]);

// 请求方式选项
const requestMethodOptions = [
  { label: 'GET', value: 'GET' },
  { label: 'POST', value: 'POST' },
  { label: 'PUT', value: 'PUT' },
  { label: 'DELETE', value: 'DELETE' },
  { label: 'PATCH', value: 'PATCH' },
];

// 加载用户列表
async function loadUserOptions() {
  try {
    const res = await getUserListApi({ page: 1, pageSize: 100 });
    userOptions.value = res.list.map((user) => ({
      label: user.realName || user.userName,
      value: user.realName || user.userName,
    }));
  } catch (error) {
    console.error('获取用户列表失败:', error);
  }
}

// 组件挂载时加载用户列表
onMounted(() => {
  loadUserOptions();
});

// 表格搜索表单
const formOptions: VbenFormProps = {
  collapsed: true,
  collapsedRows: 2,
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入模块名称',
        allowClear: true,
      },
      fieldName: 'module',
      label: '模块',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入操作类型',
        allowClear: true,
      },
      fieldName: 'operation',
      label: '操作类型',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择请求方式',
        allowClear: true,
        options: requestMethodOptions,
      },
      fieldName: 'request_method',
      label: '请求方式',
    },
    {
      component: 'AutoComplete',
      componentProps: {
        placeholder: '请输入或选择操作人',
        allowClear: true,
        options: userOptions,
        filterOption: (input: string, option: { value: string }) => {
          return option?.value?.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
      },
      fieldName: 'username',
      label: '操作人',
    },
    {
      component: 'RangePicker',
      componentProps: {
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        showTime: { format: 'HH:mm:ss' },
        placeholder: ['开始时间', '结束时间'],
      },
      fieldName: 'time_range',
      label: '操作时间',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: true,
};

// 表格配置
const gridOptions: VxeGridProps<OperationLog> = {
  columns: [
    { field: 'id', title: 'ID', width: 80 },
    {
      field: 'operation_time',
      title: '操作时间',
      width: 180,
      formatter: ({ cellValue }) => {
        return formatToDateTime(cellValue);
      },
    },
    { field: 'module', title: '模块', width: 120 },
    {
      field: 'operation',
      title: '操作类型',
      width: 180,
      slots: { default: 'operation_type' },
    },
    { field: 'username', title: '操作人', width: 120 },
    { field: 'request_method', title: '请求方式', width: 90 },
    { field: 'request_url', title: '请求路径', width: 200 },
    {
      field: 'status',
      title: '状态',
      width: 80,
      slots: { default: 'status' },
    },
    {
      field: 'execution_time',
      title: '执行时间(ms)',
      width: 120,
    },
    { field: 'ip_address', title: 'IP地址', width: 150 },
    {
      field: 'operationActions',
      title: '操作',
      fixed: 'right',
      width: 280,
      slots: { default: 'operation' },
    },
  ],
  keepSource: true,
  height: '100%',
  rowConfig: { isHover: true },
  pagerConfig: { pageSize: 10, pageSizes: [10, 20, 50, 100] },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        // 处理表单值
        const params: any = {
          page: page.currentPage,
          page_size: page.pageSize,
        };

        // 添加筛选条件
        if (formValues.module) {
          params.module = formValues.module;
        } else if (props.module) {
          params.module = props.module;
        }

        if (formValues.operation) {
          params.operation = formValues.operation;
        } else if (props.operation) {
          params.operation = props.operation;
        }

        if (formValues.username) {
          params.username = formValues.username;
        } else if (props.username) {
          params.username = props.username;
        }

        if (formValues.request_method) {
          params.request_method = formValues.request_method;
        }

        // 处理时间范围
        if (formValues.time_range && formValues.time_range.length === 2) {
          params.start_time = formValues.time_range[0];
          params.end_time = formValues.time_range[1];
        }

        try {
          const res = await getOperationLogsApi(params);
          return {
            items: res.list,
            total: res.total,
          };
        } catch (error) {
          console.error('获取操作日志失败:', error);
          return {
            items: [],
            total: 0,
          };
        }
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    export: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });

// 获取操作类型的颜色
function getOperationTypeColor(type: string): string {
  switch (type.toLowerCase()) {
    case 'create': {
      return 'success';
    }
    case 'delete': {
      return 'error';
    }
    case 'update': {
      return 'warning';
    }
    default: {
      return 'default';
    }
  }
}

// 获取状态的颜色
function getStatusColor(status: string): string {
  switch (status) {
    case '异常': {
      return 'error';
    }
    case '正常': {
      return 'success';
    }
    default: {
      return 'default';
    }
  }
}

// 查看请求详情
function viewRequestDetails(log: OperationLog) {
  let requestBody = '';
  try {
    requestBody = JSON.stringify(JSON.parse(log.request_body), null, 2);
  } catch {
    requestBody = log.request_body;
  }

  Modal.info({
    title: '请求详情',
    width: 800,
    content: h('div', {
      style: 'max-height: 500px; overflow: auto;',
      innerHTML: `<pre>${requestBody}</pre>`,
    }),
  });
}

// 查看响应详情
function viewResponseDetails(log: OperationLog) {
  let responseBody = '';
  try {
    responseBody = JSON.stringify(JSON.parse(log.response_body), null, 2);
  } catch {
    responseBody = log.response_body;
  }

  Modal.info({
    title: '响应详情',
    width: 800,
    content: h('div', {
      style: 'max-height: 500px; overflow: auto;',
      innerHTML: `<pre>${responseBody}</pre>`,
    }),
  });
}

// 刷新数据
function refresh() {
  gridApi.reload();
}

// 暴露方法给父组件
defineExpose({
  refresh,
});
</script>

<template>
  <div class="flex h-[calc(100vh-120px)] flex-col">
    <!-- 表格区域 -->
    <Grid class="flex-1">
      <template #operation_type="{ row }">
        <Tag :color="getOperationTypeColor(row.operation)">
          {{ row.operation }}
        </Tag>
      </template>
      <template #status="{ row }">
        <Tag :color="getStatusColor(row.status)">
          {{ row.status }}
        </Tag>
      </template>
      <template #operation="{ row }">
        <Space>
          <Button type="link" size="small" @click="viewRequestDetails(row)">
            请求详情
          </Button>
          <Button type="link" size="small" @click="viewResponseDetails(row)">
            响应详情
          </Button>
        </Space>
      </template>
    </Grid>
  </div>
</template>
#/utils/common/time
