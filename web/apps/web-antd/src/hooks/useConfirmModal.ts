import type { ModalFuncProps } from 'ant-design-vue/es/modal/Modal';

import { Modal } from 'ant-design-vue';

/**
 * 提供通用的确认对话框功能
 */
export function useConfirmModal() {
  /**
   * 显示确认对话框
   * @param config 配置选项
   * @returns Promise，确认时resolve，取消时reject
   */
  const showConfirm = (config: {
    cancelButtonProps?: Record<string, any>;
    cancelText?: string;
    centered?: boolean;
    content?: string;
    keyboard?: boolean;
    maskClosable?: boolean;
    okButtonProps?: Record<string, any>;
    okText?: string;
    okType?: 'danger' | 'default' | 'primary';
    title?: string;
    width?: number | string;
  }) => {
    return new Promise<void>((resolve, reject) => {
      const modalConfig: ModalFuncProps = {
        title: config.title || '确认操作',
        content: config.content || '确定要执行此操作吗？',
        okText: config.okText || '确定',
        cancelText: config.cancelText || '取消',
        okType: config.okType || 'primary',
        maskClosable: config.maskClosable ?? false,
        keyboard: config.keyboard ?? true,
        centered: config.centered ?? true,
        width: config.width,
        okButtonProps: config.okButtonProps || {},
        cancelButtonProps: config.cancelButtonProps || {},
        onOk: () => {
          resolve();
        },
        onCancel: () => {
          reject(new Error('用户取消'));
        },
      };

      Modal.confirm(modalConfig);
    });
  };

  /**
   * 显示删除确认对话框
   * @param name 被删除项的名称，如果为空则显示"此项"
   * @param count 如果大于1，则表示批量删除的数量
   * @returns Promise
   */
  const showDeleteConfirm = (name?: string, count?: number) => {
    let content = '';

    if (count && count > 1) {
      content = `确定要删除选中的${count}条记录吗？`;
    } else if (name) {
      content = `确定要删除"${name}"吗？`;
    } else {
      content = '确定要删除此项吗？';
    }

    return showConfirm({
      title: '确认删除',
      content,
      okText: '删除',
      okType: 'danger',
      okButtonProps: { danger: true },
    });
  };

  /**
   * 显示新增确认对话框
   * @param type 新增的类型，如"用户"、"设备"等
   * @returns Promise
   */
  const showAddConfirm = (type?: string) => {
    const content = type ? `确定要新增${type}吗？` : '确定要新增吗？';

    return showConfirm({
      title: '确认新增',
      content,
    });
  };

  /**
   * 显示编辑确认对话框
   * @param name 被编辑项的名称，如果为空则显示"此项"
   * @returns Promise
   */
  const showEditConfirm = (name?: string) => {
    const content = name
      ? `确定要保存对"${name}"的修改吗？`
      : '确定要保存修改吗？';

    return showConfirm({
      title: '确认编辑',
      content,
    });
  };

  return {
    showConfirm,
    showDeleteConfirm,
    showAddConfirm,
    showEditConfirm,
  };
}
