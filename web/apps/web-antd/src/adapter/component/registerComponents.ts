import type { Component } from 'vue';

import { globalShareState } from '@vben/common-ui';

// 导入自定义组件
import FileUploader from '../../components/FileUploader/FileUploader.vue';

/**
 * 注册自定义组件到表单组件库
 */
export function registerCustomComponents() {
  const components: Record<string, Component> = {
    FileUploader,
  };

  // 将组件注册到全局共享状态中
  // 获取现有组件并合并新组件
  const existingComponents = globalShareState.getComponents();
  globalShareState.setComponents({
    ...existingComponents,
    ...components,
  });
}
