import type { ModuleFileListResult } from '#/api/core/upload';

import { message } from 'ant-design-vue';

/**
 * 处理文件预览
 * @param file 文件信息
 * @param options 预览选项
 * @param options.onImagePreview 图片预览回调函数
 * @returns 如果是图片，返回预览信息；否则直接在新窗口打开
 */
export function handlePreviewFile(
  file: ModuleFileListResult,
  options?: {
    onImagePreview?: (imageUrl: string, title: string) => void;
  },
): undefined | { isImage: boolean; previewUrl: string; title: string } {
  // 判断文件类型
  const isImage = /\.(?:jpg|jpeg|png|gif|webp|bmp)$/i.test(file.file_name);
  const isPdf = /\.pdf$/i.test(file.file_name);

  if (isImage) {
    // 图片预览
    if (options?.onImagePreview) {
      options.onImagePreview(file.url, file.file_name);
    }
    return {
      isImage: true,
      previewUrl: file.url,
      title: file.file_name,
    };
  } else if (isPdf || !isImage) {
    // PDF或其他文件预览，直接在新窗口打开
    window.open(file.url, '_blank');
  }
}

/**
 * 处理文件下载
 * @param file 文件信息
 */
export async function handleDownloadFile(
  file: ModuleFileListResult,
): Promise<void> {
  try {
    // 对于图片文件，使用fetch和blob方式强制下载
    const isImage = /\.(?:jpg|jpeg|png|gif|webp|bmp)$/i.test(file.file_name);

    if (isImage) {
      // 显示下载中消息
      message.loading('正在准备下载...', 1);

      // 获取图片数据
      const response = await fetch(file.url);
      const blob = await response.blob();

      // 创建Blob URL
      const blobUrl = URL.createObjectURL(blob);

      // 使用Blob URL创建下载链接
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = file.file_name;
      document.body.append(link);
      link.click();

      // 清理
      link.remove();
      setTimeout(() => URL.revokeObjectURL(blobUrl), 100);

      message.success('下载已开始');
    } else {
      // 非图片文件使用原来的方法
      const link = document.createElement('a');
      link.href = file.url;
      link.download = file.file_name;
      link.target = '_blank';
      document.body.append(link);
      link.click();
      link.remove();
    }
  } catch (error) {
    console.error('下载文件时出错:', error);
    message.error('下载文件失败，请稍后重试');
  }
}
