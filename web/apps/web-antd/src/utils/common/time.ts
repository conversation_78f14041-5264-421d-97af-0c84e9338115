import dayjs from 'dayjs';

// 在 web/apps/web-antd/src/utils/common/date.ts 创建该文件
export function formatToDateTime(date: Date | number | string): string {
  if (!date) return '';
  const d = new Date(date);
  return d.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
}

// 添加日期处理函数
export const formatDateToISO = (date: null | string): null | string => {
  if (!date) return null;
  // 如果已经是ISO格式就直接返回
  if (date.includes('T')) return date;
  // 否则添加时间部分
  return `${date}T00:00:00Z`;
};

// 格式化日期为年月日格式
export const formatDate = (value: any): string => {
  if (!value) return '';
  return dayjs(value).format('YYYY-MM-DD');
};

// 格式化日期为年月日时分秒格式
export const formatDateTime = (value: any): string => {
  if (!value) return '';
  return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
};
