declare module '@wangeditor/editor-for-vue' {
  import type { IDomEditor } from '@wangeditor/editor';

  import { Component } from 'vue';

  export const Editor: Component;
  export const Toolbar: Component;

  export interface ToolbarProps {
    editor: IDomEditor | null;
    defaultConfig?: Record<string, any>;
    mode?: string;
    style?: Record<string, any>;
    class?: string;
  }

  export interface EditorProps {
    defaultConfig?: Record<string, any>;
    mode?: string;
    style?: Record<string, any>;
    class?: string;
    modelValue?: string;
    onCreated?: (editor: IDomEditor) => void;
    onChange?: (editor: IDomEditor) => void;
    onDestroyed?: (editor: IDomEditor) => void;
    onFocus?: (editor: IDomEditor) => void;
    onBlur?: (editor: IDomEditor) => void;
    onMaxLength?: (editor: IDomEditor) => void;
    customAlert?: (info: string, type: string) => void;
    customPaste?: (
      editor: IDomEditor,
      event: ClipboardEvent,
      callback: (val: boolean) => void,
    ) => void;
  }
}

declare module '@wangeditor/editor' {
  interface IDomEditor {
    // 扩展IDomEditor接口，添加缺少的方法
    enable: (enabled: boolean) => void;
    getHtml: () => string;
    getText: () => string;
    clear: () => void;
    destroy: () => void;
    insertText: (text: string) => void;
    dangerouslyInsertHtml?: (html: string) => void;
    setHtml?: (html: string) => void;
    insertImage?: (options: { alt?: string; src: string }) => void;
    insertNode?: (node: any) => void;
  }

  // 添加缺少的接口
  export interface IEditorConfig {
    placeholder?: string;
    readOnly?: boolean;
    autoFocus?: boolean;
    decorate?: any[];
    maxLength?: number;
    MENU_CONF?: Record<string, any>;
    customPaste?: (
      editor: IDomEditor,
      event: ClipboardEvent,
      next: (val: boolean) => void,
    ) => void;
    onCreated?: (editor: IDomEditor) => void;
    onChange?: (editor: IDomEditor) => void;
    onDestroyed?: (editor: IDomEditor) => void;
    onFocus?: (editor: IDomEditor) => void;
    onBlur?: (editor: IDomEditor) => void;
    onMaxLength?: (editor: IDomEditor) => void;
  }

  export interface IToolbarConfig {
    toolbarKeys?: string[];
    excludeKeys?: string[];
    insertKeys?: {
      index: number;
      keys: string[];
    }[];
    modalAppendToBody?: boolean;
  }
}
