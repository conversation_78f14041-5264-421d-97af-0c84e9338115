{"assetManagement": {"accessoryDetail": "Accessories details", "accessoryInfo": "Accessories information", "assetOutStorage": "Asset out of warehouse", "assetOutWarehouse": "Asset out of warehouse", "assetOutWarehouseDetail": "Asset out of warehouse details", "assetReallocation": "Asset allocation", "assetRetirement": "Assets are scrapped", "assetStorage": "Asset storage", "assetStorageDetail": "Asset storage details", "azInfo": "AZ", "cabinetInfo": "Cabinet", "idcInfo": "engine room", "idcRoomInfo": "Room", "inventoryStats": "", "netDeviceInfo": "Network equipment", "rackInfo": "Rack position", "regionInfo": "Region", "serverInfo": "server", "serverPackageInfo": "Server package", "spares": "Spare parts management", "specifications": "PN/Specification", "title": "asset Management", "warehouse": "Warehouse management"}, "auth": {"codeLogin": "Code Login", "forgetPassword": "Forget Password", "login": "<PERSON><PERSON>", "qrcodeLogin": "Qr Code Login", "register": "Register"}, "bossDashboard": {"assetDashboard": "Asset Dashboard", "faultDashboard": "Operation and maintenance board", "financialDashboard": "Financial Dashboard", "slaDashboard": "Customer board", "title": "BOSS Dashboard"}, "cmdb": {"azInfo": "AZ", "cabinetInfo": "Cabinet", "idcInfo": "engine room", "idcRoomInfo": "Private room", "netDeviceInfo": "Network equipment", "rackInfo": "Rack position", "regionInfo": "Region", "serverInfo": "server", "title": "CMDB"}, "dashboard": {"analytics": "Analytics", "title": "Overview", "workspace": "Workspace"}, "financialManagement": {"accountStatement": "Bill details", "billingDetails": "Billing details", "financialDashboard": "Financial board", "pricingManagement": "Pricing management", "productManagement": "Product Management", "resourceActivation": "Resources are activated", "resourceBilling": "Resource accounting", "title": "Financial Management"}, "hardwareMaintenance": {"accessApplication": "Apply for entry personnel", "accessManagement": "Management of incoming personnel", "accessManagementDetail": "Detail of incoming personnel management", "assetRacking": "Assets Racking", "hardwareOrder": "Hardware work order", "hardwareOrderDetail": "", "title": "Hardware operation and maintenance"}, "nodeSearch": {"title": "Node query"}, "purchaseManagement": {"contractManagement": "Contract Management", "resourceResale": "Reselling resources", "title": "Procurement Management", "arrivalManagement": "Arrival Management", "arrivalManagementDetail": "Arrival Record Details"}, "softMaintenance": {"faultReportManagement": "SoftWare Fault Ticket ", "title": "Software Operation and Maintenance"}, "sysAdmin": {"title": ""}, "systemAdmin": {"auditLog": "Operation record", "deptManagement": "Department Management", "title": "System Management", "userManagement": "User Management", "userSet": "User settings"}}