<script lang="ts" setup>
import type { VbenFormSchema } from '@vben/common-ui';

import { computed, markRaw, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { AuthenticationLogin, SliderCaptcha, z } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { bindFeishuAccountApi } from '#/api';

defineOptions({ name: 'FeishuBind' });

const route = useRoute();
const router = useRouter();

const loading = ref(false);
const error = ref('');
const code = ref('');
const state = ref('');

// 表单配置
const formSchema = computed((): VbenFormSchema[] => {
  return [
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: $t('authentication.usernameTip'),
      },
      fieldName: 'username',
      label: $t('authentication.username'),
      rules: z.string().min(1, { message: $t('authentication.usernameTip') }),
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        placeholder: $t('authentication.password'),
      },
      fieldName: 'password',
      label: $t('authentication.password'),
      rules: z.string().min(1, { message: $t('authentication.passwordTip') }),
    },
    // 滑动验证组件
    {
      component: markRaw(SliderCaptcha),
      fieldName: 'captcha',
      rules: z.boolean().refine((value) => value, {
        message: $t('authentication.verifyRequiredTip'),
      }),
    },
  ];
});

// 处理绑定提交
async function handleBind(params: any) {
  try {
    loading.value = true;
    error.value = '';

    await bindFeishuAccountApi({
      username: params.username,
      password: params.password,
      code: code.value,
      state: state.value,
    });

    // 绑定成功，跳转到登录页
    await router.replace('/auth/login?message=bind_success');
  } catch (err: any) {
    console.error('绑定飞书账号失败:', err);
    error.value = err.message || '绑定失败，请重试';
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  // 从URL参数中获取授权码和状态
  code.value = route.query.code as string;
  state.value = route.query.state as string;

  if (!code.value || !state.value) {
    error.value = '缺少必要的授权参数';
    setTimeout(() => {
      router.replace('/auth/login');
    }, 3000);
  }

  // 验证state参数
  const savedState = localStorage.getItem('feishu_oauth_state');
  if (!savedState || savedState !== state.value) {
    error.value = '状态参数验证失败，可能存在安全风险';
    setTimeout(() => {
      router.replace('/auth/login');
    }, 3000);
  }
});
</script>

<template>
  <div class="flex min-h-screen items-center justify-center">
    <div class="w-full max-w-md">
      <div v-if="error" class="mb-6 rounded-md bg-red-50 p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">
              {{ error }}
            </h3>
          </div>
        </div>
      </div>

      <div v-else>
        <div class="text-center mb-8">
          <h2 class="text-2xl font-bold text-gray-900">绑定飞书账号</h2>
          <p class="mt-2 text-sm text-gray-600">
            请输入您的用户名和密码来绑定飞书账号
          </p>
        </div>

        <AuthenticationLogin
          :form-schema="formSchema"
          :loading="loading"
          submit-button-text="绑定账号"
          :show-remember-me="false"
          :show-forget-password="false"
          :show-third-party-login="false"
          @submit="handleBind"
        />

        <div class="mt-6 text-center">
          <button
            type="button"
            class="text-sm text-blue-600 hover:text-blue-500"
            @click="router.replace('/auth/login')"
          >
            返回登录页面
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
