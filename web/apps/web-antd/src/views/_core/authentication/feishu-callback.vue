<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { feishuLoginApi } from '#/api';
import { useAuthStore } from '#/store';

defineOptions({ name: 'FeishuCallback' });

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();

const loading = ref(true);
const error = ref('');

onMounted(async () => {
  // 从URL参数中获取授权码和状态
  const code = route.query.code as string;
  const state = route.query.state as string;

  try {

    if (!code || !state) {
      throw new Error('缺少必要的授权参数');
    }

    // 验证state参数
    const savedState = localStorage.getItem('feishu_oauth_state');
    if (!savedState || savedState !== state) {
      throw new Error('状态参数验证失败，可能存在安全风险');
    }

    // 调用飞书登录API
    const response = await feishuLoginApi({ code, state });

    if (response.accessToken) {
      // 清除保存的state
      localStorage.removeItem('feishu_oauth_state');

      // 设置token并获取用户信息
      const { userInfo } = await authStore.authLogin({
        accessToken: response.accessToken
      });

      // 跳转到主页或重定向页面
      const redirectPath = route.query.redirect as string;
      await router.replace(
        redirectPath ||
        userInfo?.homePath ||
        '/dashboard/analysis'
      );
    } else {
      throw new Error('登录失败，未获取到访问令牌');
    }
  } catch (err: any) {
    console.error('飞书登录回调处理失败:', err);

    // 如果是用户不存在的错误，跳转到绑定页面
    if (err.message && err.message.includes('用户不存在')) {
      // 保留授权参数，跳转到绑定页面
      await router.replace({
        path: '/auth/feishu-bind',
        query: { code, state }
      });
      return;
    }

    error.value = err.message || '飞书登录失败，请重试';

    // 清除保存的state
    localStorage.removeItem('feishu_oauth_state');

    // 3秒后跳转到登录页
    setTimeout(() => {
      router.replace('/auth/login');
    }, 3000);
  } finally {
    loading.value = false;
  }
});
</script>

<template>
  <div class="flex min-h-screen items-center justify-center">
    <div class="text-center">
      <div v-if="loading" class="space-y-4">
        <div class="mx-auto h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
        <p class="text-lg">正在处理飞书登录...</p>
      </div>
      
      <div v-else-if="error" class="space-y-4">
        <div class="mx-auto h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
          <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </div>
        <p class="text-lg text-red-600">{{ error }}</p>
        <p class="text-sm text-gray-500">3秒后自动跳转到登录页...</p>
      </div>
      
      <div v-else class="space-y-4">
        <div class="mx-auto h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
          <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <p class="text-lg text-green-600">登录成功！</p>
        <p class="text-sm text-gray-500">正在跳转...</p>
      </div>
    </div>
  </div>
</template>
