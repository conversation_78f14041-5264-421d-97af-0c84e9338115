<script lang="ts" setup>
import { ref } from 'vue';

import { MdiFeishu } from '@vben/icons';
import { $t } from '@vben/locales';
import { VbenButton } from '@vben/common-ui';

import { getFeishuAuthUrlApi } from '#/api';

defineOptions({ name: 'Login' });

const loading = ref(false);

// 处理飞书登录
async function handleFeishuLogin() {
  try {
    loading.value = true;
    const response = await getFeishuAuthUrlApi();
    if (response.authUrl) {
      // 保存state用于后续验证
      localStorage.setItem('feishu_oauth_state', response.state);
      // 跳转到飞书授权页面
      window.location.href = response.authUrl;
    }
  } catch (error) {
    console.error('获取飞书授权URL失败:', error);
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <div class="w-full sm:mx-auto md:max-w-md">
    <!-- 标题 -->
    <div class="mb-8 text-center">
      <h1 class="text-2xl font-bold">{{ $t('authentication.welcomeBack') }} 👋🏻</h1>
      <p class="text-muted-foreground mt-2">
        {{ $t('authentication.loginSubtitle') }}
      </p>
    </div>

    <!-- 飞书登录按钮 -->
    <div class="space-y-4">
      <VbenButton
        :loading="loading"
        class="w-full h-12 text-lg"
        size="lg"
        @click="handleFeishuLogin"
      >
        <MdiFeishu class="mr-2 h-5 w-5" />
        使用飞书登录
      </VbenButton>

      <p class="text-center text-sm text-muted-foreground">
        点击上方按钮将跳转到飞书进行身份验证
      </p>
    </div>
  </div>
</template>
