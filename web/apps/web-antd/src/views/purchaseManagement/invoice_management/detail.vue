<script lang="ts" setup>
import type { InvoiceDetail } from '#/api/core/purchase/invoice';
import type { ModuleFileListResult } from '#/api/core/upload';

import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { ArrowLeft, FileText } from '@vben/icons';

import { Button, message, Spin } from 'ant-design-vue';

import { getInvoiceById } from '#/api/core/purchase/invoice';
import { getModuleFiles } from '#/api/core/upload';

const route = useRoute();
const router = useRouter();

// 数据状态
const loading = ref(false);
const invoice = ref<InvoiceDetail | null>(null);
const attachments = ref<ModuleFileListResult[]>([]);
const attachmentLoading = ref(false);

// 获取发票ID
const invoiceId = Number(route.params.id);

// 加载发票详情
async function loadInvoiceDetail() {
  if (!invoiceId) {
    message.error('发票ID无效');
    return;
  }

  loading.value = true;
  try {
    const response = await getInvoiceById(invoiceId);
    invoice.value = response;

    // 加载附件
    await loadAttachments();
  } catch (error: any) {
    console.error('加载发票详情失败:', error);
    message.error(error.message || '加载发票详情失败');
  } finally {
    loading.value = false;
  }
}

// 加载附件
async function loadAttachments() {
  if (!invoiceId) return;

  attachmentLoading.value = true;
  try {
    const files = await getModuleFiles('invoices', invoiceId);
    attachments.value = files.filter((file) => file.description === '发票附件');
  } catch (error) {
    console.error('加载附件失败:', error);
    attachments.value = [];
  } finally {
    attachmentLoading.value = false;
  }
}

// 返回列表
function goBack() {
  router.push('/purchase-management/invoice-management');
}

// 下载附件
function downloadFile(file: ModuleFileListResult) {
  const link = document.createElement('a');
  link.href = file.url;
  link.download = file.file_name;
  link.target = '_blank';
  document.body.append(link);
  link.click();
  link.remove();
}

// 预览附件
function previewFile(file: ModuleFileListResult) {
  window.open(file.url, '_blank');
}

// 获取文件图标
function getFileIcon(fileName: string) {
  const ext = fileName.split('.').pop()?.toLowerCase();
  switch (ext) {
    case 'doc':
    case 'docx': {
      return '📝';
    }
    case 'gif':
    case 'jpeg':
    case 'jpg':
    case 'png': {
      return '🖼️';
    }
    case 'pdf': {
      return '📄';
    }
    case 'xls':
    case 'xlsx': {
      return '📊';
    }
    default: {
      return '📎';
    }
  }
}

// 格式化文件大小
function formatFileSize(bytes: number) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
}

// 格式化金额
function formatAmount(amount: number) {
  return amount.toLocaleString('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2,
  });
}

// 格式化日期
function formatDate(dateString?: string) {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleDateString('zh-CN');
}

// 组件挂载时加载数据
onMounted(() => {
  loadInvoiceDetail();
});
</script>

<template>
  <Page auto-content-height>
    <div class="invoice-detail">
      <!-- 页面头部 -->
      <div class="detail-header">
        <div class="header-left">
          <Button type="text" @click="goBack" class="back-btn">
            <ArrowLeft class="size-5" />
            返回列表
          </Button>
          <div class="header-divider"></div>
          <div class="header-info">
            <div class="header-icon">
              <FileText class="size-8 text-blue-600" />
            </div>
            <div class="header-text">
              <h1 class="header-title">发票详情</h1>
              <p class="header-subtitle">查看发票的完整信息</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <Spin size="large" />
        <p class="loading-text">加载中...</p>
      </div>

      <!-- 发票信息 -->
      <div v-else-if="invoice" class="detail-content">
        <!-- 发票概览卡片 -->
        <div class="invoice-overview">
          <div class="overview-header">
            <div class="invoice-icon">
              <FileText class="size-12 text-blue-500" />
            </div>
            <div class="invoice-info">
              <h2 class="invoice-title">{{ invoice.invoice_no }}</h2>
              <p class="invoice-subtitle">发票详细信息</p>
            </div>
            <div class="invoice-amount">
              <div class="amount-value">
                {{ formatAmount(invoice.invoice_amount || 0) }}
              </div>
              <div class="amount-label">开票金额</div>
            </div>
          </div>
        </div>
        <!-- 发票基本信息 -->
        <div class="info-section">
          <h3 class="section-title">
            <span class="title-icon">📋</span>
            发票基本信息
          </h3>
          <div class="info-grid">
            <div class="info-item">
              <div class="info-label">发票编号</div>
              <div class="info-value primary">{{ invoice.invoice_no }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">开票日期</div>
              <div class="info-value">
                {{ formatDate(invoice.invoice_date) }}
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">创建人</div>
              <div class="info-value">{{ invoice.creator_name || '-' }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">创建时间</div>
              <div class="info-value">{{ formatDate(invoice.created_at) }}</div>
            </div>
            <div class="info-item full-width">
              <div class="info-label">备注</div>
              <div class="info-value">{{ invoice.remark || '无备注' }}</div>
            </div>
          </div>
        </div>

        <!-- 关联合同信息 -->
        <div class="info-section">
          <h3 class="section-title">
            <span class="title-icon">📄</span>
            关联合同信息
          </h3>
          <div class="info-grid">
            <div class="info-item">
              <div class="info-label">合同编号</div>
              <div class="info-value primary">{{ invoice.contract_no }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">供应商</div>
              <div class="info-value">{{ invoice.supplier_name }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">项目名称</div>
              <div class="info-value">{{ invoice.project_name }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">我方公司</div>
              <div class="info-value">{{ invoice.our_company_name }}</div>
            </div>
          </div>
        </div>

        <!-- 附件展示 -->
        <div class="info-section">
          <h3 class="section-title">
            <span class="title-icon">📎</span>
            发票附件
            <span v-if="attachmentLoading" class="loading-indicator">
              <Spin size="small" />
            </span>
          </h3>

          <div v-if="attachments.length > 0" class="attachments-grid">
            <div
              v-for="file in attachments"
              :key="file.id"
              class="attachment-card"
            >
              <div class="attachment-icon">
                <span class="file-icon">{{ getFileIcon(file.file_name) }}</span>
              </div>
              <div class="attachment-info">
                <div class="file-name" :title="file.file_name">
                  {{ file.file_name }}
                </div>
                <div class="file-meta">
                  <span class="file-size">{{
                    formatFileSize(file.file_size || 0)
                  }}</span>
                  <span class="upload-time">{{
                    formatDate(file.created_at)
                  }}</span>
                </div>
              </div>
              <div class="attachment-actions">
                <Button
                  type="text"
                  size="small"
                  @click="previewFile(file)"
                  title="预览"
                >
                  👁️
                </Button>
                <Button
                  type="text"
                  size="small"
                  @click="downloadFile(file)"
                  title="下载"
                >
                  ⬇️
                </Button>
              </div>
            </div>
          </div>

          <div v-else-if="!attachmentLoading" class="no-attachments">
            <div class="no-attachments-icon">📎</div>
            <div class="no-attachments-text">暂无附件</div>
          </div>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else class="error-container">
        <div class="error-content">
          <FileText class="size-16 text-gray-400" />
          <h3>发票不存在</h3>
          <p>请检查发票ID是否正确</p>
          <Button type="primary" @click="goBack">返回列表</Button>
        </div>
      </div>
    </div>
  </Page>
</template>

<style lang="less" scoped>
.invoice-detail {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 32px;

  .header-left {
    display: flex;
    align-items: center;

    .back-btn {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #666;

      &:hover {
        color: #1890ff;
      }
    }
    gap: 16px;
  }

  .header-divider {
    width: 1px;
    height: 32px;
    background: #e8e8e8;
  }

  .header-info {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .header-icon {
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .header-title {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0 0 4px 0;
  }

  .header-subtitle {
    font-size: 14px;
    color: #666;
    margin: 0;
  }

  .header-actions {
    display: flex;
    gap: 12px;

    :deep(.ant-btn) {
      height: 40px;
      padding: 0 20px;
      border-radius: 8px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;

      &:hover {
        transform: translateY(-1px);
      }
    }
  }
}

// 发票概览
.invoice-overview {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  color: white;

  .overview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .invoice-icon {
      width: 80px;
      height: 80px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10px);
    }

    .invoice-info {
      flex: 1;
      margin-left: 24px;

      .invoice-title {
        font-size: 32px;
        font-weight: 700;
        margin: 0 0 8px 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .invoice-subtitle {
        font-size: 16px;
        opacity: 0.9;
        margin: 0;
      }
    }

    .invoice-amount {
      text-align: right;

      .amount-value {
        font-size: 36px;
        font-weight: 700;
        margin-bottom: 4px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .amount-label {
        font-size: 14px;
        opacity: 0.9;
      }
    }
  }
}

// 信息展示区域
.detail-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.info-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f0f0;

  .section-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0 0 24px 0;
    padding-bottom: 16px;
    border-bottom: 2px solid #f0f0f0;

    .title-icon {
      font-size: 20px;
    }

    .loading-indicator {
      margin-left: auto;
    }
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 8px;

    &.full-width {
      grid-column: 1 / -1;
    }

    .info-label {
      font-size: 14px;
      color: #666;
      font-weight: 500;
    }

    .info-value {
      font-size: 16px;
      color: #1a1a1a;
      font-weight: 600;

      &.primary {
        color: #1890ff;
        font-size: 18px;
      }
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f0f0f0;
    border-top: 3px solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  .loading-text {
    font-size: 16px;
    color: #666;
    margin: 16px 0 0 0;
  }
}

// 附件展示
.attachments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
}

.attachment-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    background: #f0f9ff;
    border-color: #1890ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  }

  .attachment-icon {
    width: 48px;
    height: 48px;
    background: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .file-icon {
      font-size: 24px;
    }
  }

  .attachment-info {
    flex: 1;
    min-width: 0;

    .file-name {
      font-size: 14px;
      font-weight: 500;
      color: #1a1a1a;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .file-meta {
      display: flex;
      gap: 12px;
      font-size: 12px;
      color: #666;

      .file-size {
        &::after {
          content: '•';
          margin: 0 8px;
          color: #ccc;
        }
      }
    }
  }

  .attachment-actions {
    display: flex;
    gap: 4px;

    :deep(.ant-btn) {
      width: 32px;
      height: 32px;
      padding: 0;
      border: none;
      background: transparent;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6px;

      &:hover {
        background: rgba(24, 144, 255, 0.1);
      }
    }
  }
}

.no-attachments {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 20px;
  text-align: center;

  .no-attachments-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  .no-attachments-text {
    font-size: 16px;
    color: #999;
  }
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .info-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;

    :deep(.ant-card-head) {
      border-bottom: 1px solid #f0f0f0;
      padding: 16px 24px;

      .ant-card-head-title {
        font-size: 16px;
        font-weight: 600;
        color: #1a1a1a;
      }
    }

    :deep(.ant-card-body) {
      padding: 24px;
    }

    .status-tag {
      font-weight: 500;
      padding: 4px 12px;
      border-radius: 6px;
    }
  }

  :deep(.ant-descriptions) {
    .ant-descriptions-item-label {
      background: #fafafa;
      font-weight: 500;
      color: #666;
      width: 120px;
    }

    .ant-descriptions-item-content {
      background: white;
      color: #1a1a1a;
    }
  }

  .invoice-no {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-weight: 600;
    color: #1890ff;
    background: #f0f9ff;
    padding: 4px 8px;
    border-radius: 4px;
  }

  .contract-no {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-weight: 600;
    color: #52c41a;
    background: #f6ffed;
    padding: 4px 8px;
    border-radius: 4px;
  }

  .amount {
    font-size: 18px;
    font-weight: 700;
    color: #52c41a;
  }
}

.invoice-stats {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
  }

  .stat-item {
    text-align: center;
    padding: 20px;
    background: #fafafa;
    border-radius: 12px;
    border: 1px solid #f0f0f0;

    .stat-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }

    .stat-value {
      font-size: 24px;
      font-weight: 700;
      line-height: 1;

      &.total {
        color: #1890ff;
      }

      &.invoiced {
        color: #52c41a;
      }

      &.remaining {
        color: #fa8c16;
      }

      &.progress {
        color: #722ed1;
      }
    }
  }

  .progress-bar {
    .progress-track {
      width: 100%;
      height: 8px;
      background: #f0f0f0;
      border-radius: 4px;
      overflow: hidden;
      margin-bottom: 8px;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
        border-radius: 4px;
        transition: width 0.3s ease;
      }
    }

    .progress-text {
      text-align: center;
      font-size: 14px;
      color: #666;
      font-weight: 500;
    }
  }
}

.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;

  .error-content {
    text-align: center;
    padding: 40px;

    h3 {
      font-size: 20px;
      color: #1a1a1a;
      margin: 16px 0 8px 0;
    }

    p {
      font-size: 14px;
      color: #666;
      margin: 0 0 24px 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .invoice-detail {
    padding: 0 12px;
  }

  .detail-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 16px 0;

    .header-left {
      width: 100%;
    }

    .header-actions {
      width: 100%;
      justify-content: flex-end;
    }
  }

  .invoice-overview {
    padding: 24px 16px;

    .overview-header {
      flex-direction: column;
      text-align: center;
      gap: 20px;

      .invoice-info {
        margin-left: 0;

        .invoice-title {
          font-size: 24px;
        }
      }

      .invoice-amount {
        .amount-value {
          font-size: 28px;
        }
      }
    }
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .attachments-grid {
    grid-template-columns: 1fr;
  }

  .attachment-card {
    padding: 12px;

    .attachment-icon {
      width: 40px;
      height: 40px;

      .file-icon {
        font-size: 20px;
      }
    }
  }
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;

  .error-content {
    text-align: center;
    padding: 40px;

    h3 {
      font-size: 20px;
      color: #1a1a1a;
      margin: 16px 0 8px 0;
    }

    p {
      font-size: 14px;
      color: #666;
      margin: 0 0 24px 0;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .invoice-detail {
    padding: 0 16px;
  }

  .detail-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    .header-left {
      width: 100%;
    }

    .header-actions {
      width: 100%;
      justify-content: flex-end;
    }
  }

  .invoice-stats {
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
    }

    .stat-item {
      padding: 16px;

      .stat-value {
        font-size: 20px;
      }
    }
  }

  :deep(.ant-descriptions) {
    .ant-descriptions-item-label {
      width: 100px;
    }
  }
}

@media (max-width: 480px) {
  .detail-header {
    .header-info {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }

    .header-actions {
      flex-direction: column;

      :deep(.ant-btn) {
        width: 100%;
        justify-content: center;
      }
    }
  }

  .invoice-stats {
    .stats-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
