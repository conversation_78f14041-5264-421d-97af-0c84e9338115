<script lang="ts" setup>
import type {
  ContractInvoiceGroup,
  InvoiceListItem,
  InvoiceSearchParams,
} from '#/api/core/purchase/invoice';

import { computed, onMounted, onUnmounted, ref, watch } from 'vue';

import { AccessControl } from '@vben/access';
import { Page } from '@vben/common-ui';
import { ChevronDown, FileText, Plus } from '@vben/icons';

import {
  Button,
  Card,
  Empty,
  Input,
  Pagination,
  Spin,
  Tooltip,
} from 'ant-design-vue';

import { getInvoicesByContract } from '#/api/core/purchase/invoice';

import InvoiceDetailModal from './modules/invoice-detail-modal.vue';
import InvoiceForm from './modules/invoice-form.vue';

const invoiceFormRef = ref();
const invoiceDetailModalRef = ref();

// 数据状态
const loading = ref(false);
const contractGroups = ref<ContractInvoiceGroup[]>([]);
const expandedContracts = ref<Set<number>>(new Set());

const searchKeyword = ref('');
const isMounted = ref(true);

// 分页状态
const currentPage = ref(1);
const pageSize = ref(5);
const total = ref(0);

// 计算属性 - 现在直接返回从后端获取的数据
const filteredContractGroups = computed(() => {
  return contractGroups.value;
});

// 统计信息
const totalStats = computed(() => {
  const stats = {
    totalContracts: 0,
    totalInvoices: 0,
    totalAmount: 0,
    remainingAmount: 0,
  };

  for (const group of contractGroups.value) {
    stats.totalContracts += 1;
    stats.totalInvoices += group.invoice_count;
    stats.totalAmount += group.total_invoiced_amount;
    stats.remainingAmount += group.remaining_amount;
  }

  return stats;
});

// 加载数据
async function loadData() {
  if (!isMounted.value) return;

  loading.value = true;
  try {
    const params: InvoiceSearchParams = {
      page: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchKeyword.value,
    };

    const response = await getInvoicesByContract(params);
    // 检查组件是否仍然挂载
    if (isMounted.value) {
      contractGroups.value = response.list;
      total.value = response.total;
    }
  } catch (error) {
    console.error('加载发票数据失败:', error);
    if (isMounted.value) {
      contractGroups.value = [];
      total.value = 0;
    }
  } finally {
    if (isMounted.value) {
      loading.value = false;
    }
  }
}

// 切换合同展开状态
function toggleContract(contractId: number) {
  if (expandedContracts.value.has(contractId)) {
    expandedContracts.value.delete(contractId);
  } else {
    expandedContracts.value.add(contractId);
  }
}

// 检查合同是否应该展开
function isContractExpanded(contractId: number) {
  return expandedContracts.value.has(contractId);
}

// 创建发票
function createInvoice(contractId?: number) {
  invoiceFormRef.value?.modalApi.setData({ contractId }).open();
}

// 获取进度百分比
function getProgressPercentage(group: ContractInvoiceGroup) {
  if (group.contract_total_amount === 0) return 0;
  return Math.round(
    (group.total_invoiced_amount / group.contract_total_amount) * 100,
  );
}

// 获取进度颜色
function getProgressColor(percentage: number) {
  if (percentage >= 100) return '#52c41a';
  if (percentage >= 80) return '#faad14';
  if (percentage >= 50) return '#1890ff';
  return '#f5222d';
}

// 分页处理
function handlePageChange(page: number) {
  currentPage.value = page;
  loadData(); // 重新加载数据
}

function handlePageSizeChange(_current: number, size: number) {
  pageSize.value = size;
  currentPage.value = 1; // 重置到第一页
  loadData(); // 重新加载数据
}

// 搜索处理
function handleSearch() {
  currentPage.value = 1; // 搜索时重置到第一页
  loadData(); // 重新加载数据
}

// 查看发票详情
function viewInvoice(invoice: InvoiceListItem) {
  invoiceDetailModalRef.value?.modalApi.setData(invoice).open();
}

// 格式化日期
function formatDate(dateString?: string) {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleDateString('zh-CN');
}

// 格式化金额
function formatAmount(amount: number) {
  return amount.toLocaleString('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2,
  });
}

// 刷新数据
function refreshData() {
  loadData();
}

// 监听搜索关键词变化，添加防抖处理
let searchTimeout: NodeJS.Timeout | null = null;
watch(searchKeyword, (newValue, oldValue) => {
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }

  // 如果是清空搜索框，立即搜索
  if (newValue === '' && oldValue !== '') {
    currentPage.value = 1;
    loadData();
    return;
  }

  // 其他情况使用防抖
  searchTimeout = setTimeout(() => {
    currentPage.value = 1; // 搜索时重置到第一页
    loadData(); // 重新加载数据
  }, 500);
});

// 组件挂载时加载数据
onMounted(() => {
  // 初始化时重置分页状态
  currentPage.value = 1;
  searchKeyword.value = '';
  loadData();
});

// 组件卸载时清理状态
onUnmounted(() => {
  isMounted.value = false;
});
</script>

<template>
  <Page auto-content-height>
    <InvoiceForm ref="invoiceFormRef" @success="refreshData" />

    <!-- 页面头部 -->
    <div class="invoice-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">
            <FileText class="size-8 text-blue-600" />
          </div>
          <div class="header-info">
            <h1 class="header-title">发票管理</h1>
          </div>
        </div>
        <div class="header-actions">
          <AccessControl :codes="['Purchase:Invoice:Create']" type="code">
            <Button type="primary" size="large" @click="createInvoice()">
              <Plus class="size-5" />
              创建发票
            </Button>
          </AccessControl>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <Card class="stats-card" :style="{ '--index': 0 }">
        <div class="stats-content">
          <div class="stats-icon contract-icon">
            <FileText class="size-6" />
          </div>
          <div class="stats-info">
            <div class="stats-value">{{ totalStats.totalContracts }}</div>
            <div class="stats-label">合同数量</div>
          </div>
        </div>
      </Card>

      <Card class="stats-card" :style="{ '--index': 1 }">
        <div class="stats-content">
          <div class="stats-icon invoice-icon">
            <FileText class="size-6" />
          </div>
          <div class="stats-info">
            <div class="stats-value">{{ totalStats.totalInvoices }}</div>
            <div class="stats-label">发票数量</div>
          </div>
        </div>
      </Card>

      <Card class="stats-card" :style="{ '--index': 2 }">
        <div class="stats-content">
          <div class="stats-icon amount-icon">
            <FileText class="size-6" />
          </div>
          <div class="stats-info">
            <div class="stats-value">
              {{ formatAmount(totalStats.totalAmount) }}
            </div>
            <div class="stats-label">已开票金额</div>
          </div>
        </div>
      </Card>

      <Card class="stats-card" :style="{ '--index': 3 }">
        <div class="stats-content">
          <div class="stats-icon remaining-icon">
            <FileText class="size-6" />
          </div>
          <div class="stats-info">
            <div class="stats-value">
              {{ formatAmount(totalStats.remainingAmount) }}
            </div>
            <div class="stats-label">剩余可开票</div>
          </div>
        </div>
      </Card>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <Input.Search
        v-model:value="searchKeyword"
        placeholder="搜索合同编号、供应商或项目名称..."
        size="large"
        allow-clear
        :loading="loading"
        @search="handleSearch"
        @change="handleSearch"
        class="search-input"
      />
    </div>

    <!-- 合同文件夹列表 -->
    <div class="contract-folders">
      <Spin :spinning="loading">
        <div
          v-if="filteredContractGroups.length === 0 && !loading"
          class="empty-state"
        >
          <Empty
            :description="
              searchKeyword
                ? `未找到包含「${searchKeyword}」的合同`
                : '暂无发票数据'
            "
            :image="Empty.PRESENTED_IMAGE_SIMPLE"
          >
            <template v-if="searchKeyword" #extra>
              <Button type="primary" @click="searchKeyword = ''">
                清除搜索条件
              </Button>
            </template>
          </Empty>
        </div>

        <div v-else class="folders-grid">
          <div
            v-for="group in filteredContractGroups"
            :key="group.contract_id"
            class="contract-folder"
            :class="{
              expanded: isContractExpanded(group.contract_id),
            }"
          >
            <!-- 合同文件夹头部 -->
            <div
              class="folder-header"
              @click="toggleContract(group.contract_id)"
              title="点击展开/收起发票列表"
            >
              <div class="folder-icon">
                <FileText
                  class="size-8 transition-transform duration-300"
                  :class="{
                    'rotate-12': isContractExpanded(group.contract_id),
                  }"
                />
              </div>

              <div class="folder-info">
                <div class="folder-title">{{ group.contract_no }}</div>
                <div class="folder-subtitle">
                  {{ group.supplier_name }} · {{ group.project_name }}
                </div>
                <div class="folder-stats">
                  <span class="stat-item"
                    >{{ group.invoice_count }} 张发票</span
                  >
                  <span class="stat-item">{{
                    formatAmount(group.total_invoiced_amount)
                  }}</span>
                  <span class="stat-item"
                    >对方公司名称：{{ group.supplier_name }}</span
                  >
                  <span class="stat-item"
                    >我方公司名称：{{ group.our_company_name }}</span
                  >
                  <span class="stat-item">项目：{{ group.project_name }}</span>
                </div>
              </div>

              <div class="folder-progress">
                <div class="progress-info">
                  <span class="progress-text"
                    >{{ getProgressPercentage(group) }}%</span
                  >
                  <span class="progress-amount">
                    {{ formatAmount(group.remaining_amount) }} 剩余
                  </span>
                </div>
                <div class="progress-bar">
                  <div
                    class="progress-fill"
                    :style="{
                      width: `${getProgressPercentage(group)}%`,
                      backgroundColor: getProgressColor(
                        getProgressPercentage(group),
                      ),
                    }"
                  ></div>
                </div>
              </div>

              <div class="folder-actions" @click.stop>
                <div class="expand-indicator">
                  <ChevronDown
                    class="size-5 text-gray-400 transition-transform duration-300"
                    :class="{
                      'rotate-180': isContractExpanded(group.contract_id),
                    }"
                  />
                </div>
                <Tooltip title="为此合同创建发票">
                  <AccessControl
                    :codes="['Purchase:Invoice:Create']"
                    type="code"
                  >
                    <Button
                      type="primary"
                      size="small"
                      @click="createInvoice(group.contract_id)"
                    >
                      <Plus class="size-4" />
                    </Button>
                  </AccessControl>
                </Tooltip>
              </div>
            </div>

            <!-- 发票列表 -->
            <div
              v-if="isContractExpanded(group.contract_id)"
              class="invoice-list"
            >
              <div
                v-for="invoice in group.invoices"
                :key="invoice.id"
                class="invoice-item"
                @click="viewInvoice(invoice)"
              >
                <div class="invoice-icon">
                  <FileText class="size-5 text-blue-500" />
                </div>

                <div class="invoice-info">
                  <div class="invoice-title">{{ invoice.invoice_no }}</div>
                  <div class="invoice-meta">
                    {{ formatDate(invoice.invoice_date) }} ·
                    {{ invoice.creator_name }}
                  </div>
                </div>

                <div class="invoice-amount">
                  {{ formatAmount(invoice.invoice_amount) }}
                </div>
              </div>

              <div v-if="group.invoices.length === 0" class="no-invoices">
                <Empty
                  :image="Empty.PRESENTED_IMAGE_SIMPLE"
                  description="此合同暂无发票"
                />
              </div>
            </div>
          </div>
        </div>
      </Spin>
    </div>

    <!-- 搜索结果提示 -->
    <div v-if="searchKeyword && total > 0" class="search-result-tip">
      <span>搜索「{{ searchKeyword }}」找到 {{ total }} 个合同</span>
      <Button type="link" size="small" @click="searchKeyword = ''">
        清除搜索
      </Button>
    </div>

    <!-- 分页 -->
    <div v-if="total > 0" class="pagination-wrapper">
      <Pagination
        v-model:current="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :show-size-changer="true"
        :show-quick-jumper="true"
        :show-total="
          (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 个合同`
        "
        :page-size-options="['5', '10', '20', '50']"
        size="default"
        @change="handlePageChange"
        @show-size-change="handlePageSizeChange"
        class="pagination"
      />
    </div>

    <!-- 发票表单Modal -->
    <InvoiceForm ref="invoiceFormRef" @success="refreshData" />

    <!-- 查看发票详情Modal -->
    <InvoiceDetailModal ref="invoiceDetailModalRef" />
  </Page>
</template>

<style lang="less" scoped>
// 性能优化
* {
  box-sizing: border-box;
}

// 启用硬件加速和性能优化
.invoice-header,
.stats-card,
.contract-folder,
.invoice-item {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

// 减少重绘
.stats-cards,
.contract-folders {
  contain: layout style paint;
}

// 优化滚动性能
.invoice-detail-content {
  contain: layout style paint;
  overflow-anchor: none;
}
// 优化的动画关键帧 - 使用transform和opacity提升性能
@keyframes shimmer {
  0% {
    transform: translate3d(-100%, 0, 0);
  }
  100% {
    transform: translate3d(100%, 0, 0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale3d(1, 1, 1);
  }
  50% {
    transform: scale3d(1.02, 1.02, 1);
  }
}
.invoice-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24px;
  padding: 40px;
  margin-bottom: 32px;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 20px 60px rgba(102, 126, 234, 0.3),
    0 8px 32px rgba(0, 0, 0, 0.1);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(
        circle at 20% 80%,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 40%
      ),
      radial-gradient(
        circle at 80% 20%,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 40%
      );
    pointer-events: none;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.1) 50%,
      transparent 70%
    );
    transform: translate3d(-100%, 0, 0);
    animation: shimmer 6s infinite;
    pointer-events: none;
    will-change: transform;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 24px;
  }

  .header-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(20px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      inset: -1px;
      border-radius: 21px;
      background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.2),
        rgba(255, 255, 255, 0.05)
      );
      z-index: -1;
      opacity: 0.5;
    }
  }

  .header-title {
    font-size: 36px;
    font-weight: 800;
    margin: 0;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    letter-spacing: -1px;
    background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .header-actions {
    :deep(.ant-btn) {
      background: rgba(255, 255, 255, 0.15);
      border: 2px solid rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(20px);
      color: white;
      font-weight: 700;
      height: 52px;
      padding: 0 28px;
      font-size: 16px;
      border-radius: 16px;
      box-shadow:
        0 8px 24px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.6s;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.25);
        border-color: rgba(255, 255, 255, 0.4);
        transform: translateY(-3px);
        box-shadow:
          0 12px 32px rgba(0, 0, 0, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.3);

        &::before {
          left: 100%;
        }
      }

      &:active {
        transform: translateY(-1px);
      }
    }
  }
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;

  .stats-card {
    border-radius: 20px;
    border: none;
    background: white;
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.08),
      0 4px 16px rgba(0, 0, 0, 0.04);
    transition:
      transform 0.3s ease,
      box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.4s ease-out;
    animation-delay: calc(var(--index) * 0.05s);
    animation-fill-mode: both;
    will-change: transform;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: var(--gradient);
      transform: scaleX(0);
      transition: transform 0.4s ease;
    }

    &:hover {
      transform: translate3d(0, -6px, 0) scale3d(1.01, 1.01, 1);
      box-shadow:
        0 16px 40px rgba(0, 0, 0, 0.12),
        0 6px 20px rgba(0, 0, 0, 0.06);

      &::before {
        transform: scaleX(1);
      }

      .stats-icon {
        transform: scale3d(1.05, 1.05, 1) rotate3d(0, 0, 1, 3deg);
      }

      .stats-value {
        color: var(--primary-color);
      }
    }

    :deep(.ant-card-body) {
      padding: 28px;
    }
  }

  .stats-content {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .stats-icon {
    width: 64px;
    height: 64px;
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    background: var(--gradient);
    box-shadow:
      0 8px 24px var(--shadow-color),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition:
      transform 0.3s ease,
      box-shadow 0.3s ease;
    position: relative;
    will-change: transform;

    &::before {
      content: '';
      position: absolute;
      inset: -1px;
      border-radius: 19px;
      background: var(--gradient);
      z-index: -1;
      opacity: 0.3;
    }

    &.contract-icon {
      --gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --shadow-color: rgba(102, 126, 234, 0.3);
      --primary-color: #667eea;
    }

    &.invoice-icon {
      --gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      --shadow-color: rgba(240, 147, 251, 0.3);
      --primary-color: #f093fb;
    }

    &.amount-icon {
      --gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      --shadow-color: rgba(79, 172, 254, 0.3);
      --primary-color: #4facfe;
    }

    &.remaining-icon {
      --gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      --shadow-color: rgba(67, 233, 123, 0.3);
      --primary-color: #43e97b;
    }
  }

  .stats-value {
    font-size: 32px;
    font-weight: 800;
    color: #1a1a1a;
    line-height: 1;
    transition: color 0.3s ease;
    margin-bottom: 6px;
  }

  .stats-label {
    font-size: 15px;
    color: #666;
    font-weight: 600;
    letter-spacing: 0.5px;
  }
}

.search-bar {
  margin-bottom: 24px;

  .search-input {
    border-radius: 12px;

    :deep(.ant-input) {
      border-radius: 16px;
      border: 2px solid #e8e8e8;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      background: white;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
      height: 56px;
      font-size: 16px;
      padding: 16px 20px;

      &::placeholder {
        color: #999;
        font-weight: 500;
      }

      &:focus {
        border-color: #667eea;
        box-shadow:
          0 0 0 3px rgba(102, 126, 234, 0.1),
          0 6px 16px rgba(102, 126, 234, 0.12);
        transform: translate3d(0, -1px, 0);
      }
    }

    :deep(.ant-input-search-button) {
      border-radius: 0 16px 16px 0;
      border: 2px solid #667eea;
      border-left: none;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      height: 52px;
      width: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: scale(1.05);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }
}

.search-result-tip {
  margin: 16px 0;
  padding: 12px 16px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #0369a1;
  font-size: 14px;
}

.pagination-wrapper {
  margin-top: 32px;
  display: flex;
  justify-content: center;

  .pagination {
    :deep(.ant-pagination-item) {
      border-radius: 8px;
      border: 1px solid #e8e8e8;

      &:hover {
        border-color: #667eea;
      }

      &.ant-pagination-item-active {
        background: #667eea;
        border-color: #667eea;

        a {
          color: white;
        }
      }
    }

    :deep(.ant-pagination-prev),
    :deep(.ant-pagination-next) {
      border-radius: 8px;
      border: 1px solid #e8e8e8;

      &:hover {
        border-color: #667eea;
        color: #667eea;
      }
    }

    :deep(.ant-pagination-jump-prev),
    :deep(.ant-pagination-jump-next) {
      border-radius: 8px;
    }
  }
}

.contract-folders {
  .empty-state {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .folders-grid {
    display: grid;
    gap: 20px;
  }

  .contract-folder {
    background: white;
    border-radius: 20px;
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.08),
      0 4px 16px rgba(0, 0, 0, 0.04);
    transition:
      transform 0.3s ease,
      box-shadow 0.3s ease,
      border-color 0.3s ease;
    overflow: hidden;
    position: relative;
    border: 1px solid rgba(102, 126, 234, 0.1);
    will-change: transform;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 5px;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
      transform: scaleX(0);
      transition: transform 0.4s ease;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        45deg,
        transparent 30%,
        rgba(102, 126, 234, 0.03) 50%,
        transparent 70%
      );
      transform: translate3d(-100%, 0, 0);
      transition: transform 0.4s ease;
      pointer-events: none;
      will-change: transform;
    }

    &:hover {
      box-shadow:
        0 16px 40px rgba(0, 0, 0, 0.12),
        0 6px 20px rgba(102, 126, 234, 0.08);
      transform: translate3d(0, -3px, 0) scale3d(1.005, 1.005, 1);
      border-color: rgba(102, 126, 234, 0.2);

      &::before {
        transform: scaleX(1);
      }

      &::after {
        transform: translate3d(100%, 0, 0);
      }

      .folder-header {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      }

      .folder-icon {
        transform: scale3d(1.08, 1.08, 1) rotate3d(0, 0, 1, 3deg);
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
    }

    &.expanded {
      border-color: rgba(24, 144, 255, 0.3);
      box-shadow:
        0 12px 40px rgba(24, 144, 255, 0.15),
        0 4px 16px rgba(0, 0, 0, 0.08);

      &::before {
        background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
        transform: scaleX(1);
      }

      .folder-header {
        background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
      }

      .folder-icon {
        background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
        transform: scale(1.1);
      }
    }
  }

  .folder-header {
    display: flex;
    align-items: center;
    padding: 24px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    gap: 20px;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 24px;
      right: 24px;
      height: 1px;
      background: linear-gradient(
        90deg,
        transparent 0%,
        #e2e8f0 50%,
        transparent 100%
      );
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

      &::after {
        opacity: 1;
      }
    }
  }

  .folder-icon {
    color: white;
    flex-shrink: 0;
    width: 72px;
    height: 72px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow:
      0 8px 24px rgba(102, 126, 234, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background:
        radial-gradient(
          circle at 30% 30%,
          rgba(255, 255, 255, 0.2) 0%,
          transparent 50%
        ),
        radial-gradient(
          circle at 70% 70%,
          rgba(255, 255, 255, 0.1) 0%,
          transparent 50%
        );
      opacity: 1;
      transition: opacity 0.3s ease;
    }

    &::after {
      content: '';
      position: absolute;
      inset: -1px;
      border-radius: 21px;
      background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.2),
        rgba(255, 255, 255, 0.05)
      );
      z-index: -1;
      opacity: 0.3;
    }
  }

  .folder-info {
    flex: 1;
    min-width: 0;
  }

  .folder-title {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 4px;
  }

  .folder-subtitle {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }

  .folder-stats {
    display: flex;
    gap: 16px;

    .stat-item {
      font-size: 12px;
      color: #999;
      background: #f0f0f0;
      padding: 4px 8px;
      border-radius: 6px;
    }
  }

  .folder-progress {
    flex-shrink: 0;
    width: 200px;
    text-align: right;
  }

  .progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 12px;
  }

  .progress-text {
    font-weight: 600;
    color: #1a1a1a;
  }

  .progress-amount {
    color: #666;
  }

  .progress-bar {
    width: 100%;
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 3px;
  }

  .folder-actions {
    flex-shrink: 0;
    margin-left: 16px;
    display: flex;
    align-items: center;
    gap: 12px;

    .expand-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.8);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .invoice-list {
    border-top: 1px solid #f0f0f0;
    background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
    animation: slideDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
      opacity: 0.3;
    }
  }

  .invoice-item {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    gap: 16px;
    cursor: pointer;
    transition:
      transform 0.3s ease,
      box-shadow 0.3s ease,
      background 0.3s ease;
    border-bottom: 1px solid #f0f0f0;
    position: relative;
    margin: 0 8px;
    border-radius: 8px;
    will-change: transform;

    &:last-child {
      border-bottom: none;
    }

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      transform: scaleY(0);
      transition: transform 0.3s ease;
      border-radius: 2px;
    }

    &:hover {
      background: white;
      transform: translate3d(6px, -1px, 0);
      box-shadow:
        0 6px 16px rgba(0, 0, 0, 0.1),
        0 3px 8px rgba(102, 126, 234, 0.08);
      border-radius: 12px;

      &::before {
        transform: scaleY(1);
      }

      .invoice-icon {
        transform: scale3d(1.05, 1.05, 1) rotate3d(0, 0, 1, 3deg);
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

        :deep(.anticon) {
          color: white;
        }
      }

      .invoice-amount {
        color: #667eea;
        transform: scale3d(1.02, 1.02, 1);
      }
    }
  }

  .invoice-icon {
    flex-shrink: 0;
    width: 52px;
    height: 52px;
    background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition:
      transform 0.3s ease,
      background 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    border: 2px solid rgba(102, 126, 234, 0.1);
    will-change: transform;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background:
        radial-gradient(
          circle at 30% 30%,
          rgba(255, 255, 255, 0.3) 0%,
          transparent 50%
        ),
        radial-gradient(
          circle at 70% 70%,
          rgba(255, 255, 255, 0.2) 0%,
          transparent 50%
        );
      opacity: 1;
      transition: opacity 0.3s ease;
    }
  }

  .invoice-info {
    flex: 1;
    min-width: 0;
  }

  .invoice-title {
    font-size: 17px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 6px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    letter-spacing: 0.3px;
  }

  .invoice-meta {
    font-size: 14px;
    color: #666;
    font-weight: 500;
  }

  .invoice-amount {
    font-size: 18px;
    font-weight: 800;
    color: #1890ff;
    flex-shrink: 0;
    transition: all 0.3s ease;
    padding: 8px 16px;
    background: rgba(24, 144, 255, 0.1);
    border-radius: 10px;
    border: 1px solid rgba(24, 144, 255, 0.2);
  }

  .no-invoices {
    padding: 40px 24px;
    text-align: center;
  }
}

// 动画效果
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

// 添加加载动画
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

// 添加悬停动画
.contract-folder {
  animation: fadeIn 0.5s ease;

  &:hover {
    .folder-icon {
      animation: pulse 0.6s ease-in-out;
    }
  }
}

// 添加统计卡片动画
.stats-card {
  animation: fadeIn 0.6s ease;
  animation-delay: calc(var(--index) * 0.1s);
  animation-fill-mode: both;
}

// 响应式设计
@media (max-width: 768px) {
  .invoice-header {
    padding: 20px;

    .header-content {
      flex-direction: column;
      gap: 20px;
      text-align: center;
    }

    .header-title {
      font-size: 24px;
    }
  }

  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .contract-folder {
    .folder-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .folder-progress {
      width: 100%;
    }

    .folder-actions {
      margin-left: 0;
      align-self: flex-end;
    }
  }

  .invoice-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;

    .invoice-amount {
      align-self: flex-end;
    }
  }
}

@media (max-width: 480px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }

  .invoice-header {
    .header-title {
      font-size: 20px;
    }

    .header-subtitle {
      font-size: 14px;
    }
  }
}
</style>
