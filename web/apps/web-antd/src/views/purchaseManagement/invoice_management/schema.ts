import type { VbenFormSchema } from '#/adapter/form';

import { z } from '#/adapter/form';
import { INVOICE_STATUS_NAMES } from '#/api/core/purchase/invoice';
import { getPurchaseContractList } from '#/api/core/purchase/purchase_contract';

// 定义全局窗口接口
declare global {
  interface Window {
    handleContractChange?: (id: number) => void;
  }
}

/**
 * 获取发票表单的字段配置
 */
export function useInvoiceSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      componentProps: {
        showSearch: true,
        filterOption: (
          input: string,
          option: { label: string; value: number },
        ) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
        allowClear: false,
        api: async () => {
          const res = await getPurchaseContractList({
            page: 1,
            pageSize: 100,
            status: 'completed',
          });
          return res.list.map((item) => ({
            label: `${item.contract_no} - ${item.supplier_name}`,
            value: item.id,
          }));
        },
        class: 'w-full',
        labelField: 'label',
        valueField: 'value',
        placeholder: '请选择关联合同',
        onChange: (value: number) => {
          if (window.handleContractChange && value) {
            window.handleContractChange(value);
          }
        },
      },
      fieldName: 'contract_id',
      label: '关联合同',
      rules: 'selectRequired',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入发票号',
        class: 'w-full',
        maxlength: 50,
      },
      fieldName: 'invoice_no',
      label: '发票号',
      rules: z
        .string()
        .min(1, '请输入发票号')
        .max(50, '发票号不能超过50个字符'),
    },
    {
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入开票金额',
        min: 0.01,
        precision: 2,
        class: 'w-full',
        formatter: (value: number) =>
          `¥ ${value}`.replaceAll(/\B(?=(?:\d{3})+(?!\d))/g, ','),
        parser: (value: string) => value.replaceAll(/¥\s?|(,*)/g, ''),
      },
      fieldName: 'invoice_amount',
      label: '开票金额',
      rules: z.number().min(0.01, '开票金额必须大于0'),
    },

    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入备注信息',
        rows: 3,
        maxlength: 500,
        class: 'w-full',
      },
      fieldName: 'remark',
      label: '备注',
    },
  ];
}

/**
 * 获取发票搜索表单的字段配置
 */
export function useInvoiceSearchSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入发票号',
        class: 'w-full',
      },
      fieldName: 'invoice_no',
      label: '发票号',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        showSearch: true,
        filterOption: true,
        allowClear: true,
        api: async () => {
          const res = await getPurchaseContractList({
            page: 1,
            pageSize: 1000,
          });
          return res.list.map((item) => ({
            label: `${item.contract_no} - ${item.supplier_name}`,
            value: item.id,
          }));
        },
        class: 'w-full',
        labelField: 'label',
        valueField: 'value',
        placeholder: '请选择合同',
      },
      fieldName: 'contract_id',
      label: '关联合同',
    },
    {
      component: 'Select',
      componentProps: {
        options: Object.entries(INVOICE_STATUS_NAMES).map(([value, label]) => ({
          label,
          value,
        })),
        placeholder: '请选择发票状态',
        class: 'w-full',
        allowClear: true,
      },
      fieldName: 'status',
      label: '发票状态',
    },
    {
      component: 'RangePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        placeholder: ['开始日期', '结束日期'],
        valueFormat: 'YYYY-MM-DD',
        class: 'w-full',
      },
      fieldName: 'date_range',
      label: '开票日期',
    },
    {
      component: 'InputNumber',
      componentProps: {
        placeholder: '最小金额',
        min: 0,
        precision: 2,
        class: 'w-full',
      },
      fieldName: 'min_amount',
      label: '最小金额',
    },
    {
      component: 'InputNumber',
      componentProps: {
        placeholder: '最大金额',
        min: 0,
        precision: 2,
        class: 'w-full',
      },
      fieldName: 'max_amount',
      label: '最大金额',
    },
  ];
}
