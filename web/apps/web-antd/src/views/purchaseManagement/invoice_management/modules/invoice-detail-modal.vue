<script lang="ts" setup>
import type {
  InvoiceDetail,
  InvoiceListItem,
} from '#/api/core/purchase/invoice';
import type { ModuleFileListResult } from '#/api/core/upload';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { FileText } from '@vben/icons';

import { Modal as AntModal, Button, Spin } from 'ant-design-vue';

import { getInvoiceById } from '#/api/core/purchase/invoice';
import { getModuleFiles } from '#/api/core/upload';
import { handleDownloadFile, handlePreviewFile } from '#/utils/common/download';

// Modal状态
const viewInvoiceData = ref<InvoiceDetail | null>(null);
const viewAttachments = ref<ModuleFileListResult[]>([]);
const viewLoading = ref(false);
const attachmentLoading = ref(false);

// 图片预览状态
const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');

// 创建VbenModal
const [Modal, modalApi] = useVbenModal({
  class: 'w-3/4',
  onOpenChange: async (isOpen: boolean) => {
    if (isOpen) {
      const data = modalApi.getData<InvoiceListItem>();
      if (data?.id) {
        await loadInvoiceDetail(data.id);
      }
    } else {
      // 关闭时清理数据
      viewInvoiceData.value = null;
      viewAttachments.value = [];
    }
  },
});

// 加载发票详情
async function loadInvoiceDetail(invoiceId: number) {
  viewLoading.value = true;

  try {
    // 获取发票详情
    const detail = await getInvoiceById(invoiceId);
    viewInvoiceData.value = detail;

    // 加载附件
    await loadAttachments(invoiceId);
  } catch (error) {
    console.error('加载发票详情失败:', error);
    viewInvoiceData.value = null;
  } finally {
    viewLoading.value = false;
  }
}

// 加载附件
async function loadAttachments(invoiceId: number) {
  attachmentLoading.value = true;
  try {
    const files = await getModuleFiles('invoices', invoiceId);
    viewAttachments.value = files.filter(
      (file) => file.description === '发票附件',
    );
  } catch (error) {
    console.error('加载附件失败:', error);
    viewAttachments.value = [];
  } finally {
    attachmentLoading.value = false;
  }
}

// 处理文件预览
function handlePreview(file: ModuleFileListResult) {
  // 使用通用预览函数
  handlePreviewFile(file, {
    onImagePreview: (imageUrl, title) => {
      previewImage.value = imageUrl;
      previewTitle.value = title;
      previewVisible.value = true;
    },
  });
}

// 处理文件下载
async function handleDownload(file: ModuleFileListResult) {
  // 使用通用下载函数
  await handleDownloadFile(file);
}

// 获取文件图标
function getFileIcon(fileName: string) {
  const ext = fileName.split('.').pop()?.toLowerCase();
  switch (ext) {
    case 'doc':
    case 'docx': {
      return '📝';
    }
    case 'gif':
    case 'jpeg':
    case 'jpg':
    case 'png': {
      return '🖼️';
    }
    case 'pdf': {
      return '📄';
    }
    case 'xls':
    case 'xlsx': {
      return '📊';
    }
    default: {
      return '📎';
    }
  }
}

// 格式化文件大小
function formatFileSize(bytes: number) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
}

// 格式化日期
function formatDate(dateString?: string) {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleDateString('zh-CN');
}

// 格式化金额
function formatAmount(amount: number) {
  return amount.toLocaleString('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2,
  });
}

// 暴露modalApi供父组件使用
defineExpose({
  modalApi,
});
</script>

<template>
  <Modal title="发票详情">
    <div class="invoice-detail-modal">
      <div v-if="viewLoading" class="loading-container">
        <Spin size="large" />
        <p class="loading-text">加载中...</p>
      </div>

      <div v-else-if="viewInvoiceData" class="invoice-detail-content">
        <!-- 发票概览 -->
        <div class="invoice-overview">
          <div class="overview-header">
            <div class="invoice-icon">
              <FileText class="size-12 text-blue-500" />
            </div>
            <div class="invoice-info">
              <h2 class="invoice-title">{{ viewInvoiceData.invoice_no }}</h2>
              <p class="invoice-subtitle">发票详细信息</p>
            </div>
            <div class="invoice-amount">
              <div class="amount-value">
                {{ formatAmount(viewInvoiceData.invoice_amount || 0) }}
              </div>
              <div class="amount-label">开票金额</div>
            </div>
          </div>
        </div>

        <!-- 发票基本信息 -->
        <div class="info-section">
          <h3 class="section-title">
            <span class="title-icon">📋</span>
            发票基本信息
          </h3>
          <div class="info-grid">
            <div class="info-item">
              <div class="info-label">发票编号</div>
              <div class="info-value primary">
                {{ viewInvoiceData.invoice_no }}
              </div>
            </div>

            <div class="info-item">
              <div class="info-label">创建人</div>
              <div class="info-value">
                {{ viewInvoiceData.creator_name || '-' }}
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">创建时间</div>
              <div class="info-value">
                {{ formatDate(viewInvoiceData.created_at) }}
              </div>
            </div>
            <div class="info-item full-width">
              <div class="info-label">备注</div>
              <div class="info-value">
                {{ viewInvoiceData.remark || '无备注' }}
              </div>
            </div>
          </div>
        </div>

        <!-- 关联合同信息 -->
        <div class="info-section">
          <h3 class="section-title">
            <span class="title-icon">📄</span>
            关联合同信息
          </h3>
          <div class="info-grid">
            <div class="info-item">
              <div class="info-label">合同编号</div>
              <div class="info-value primary">
                {{ viewInvoiceData.contract_no }}
              </div>
            </div>

            <div class="info-item">
              <div class="info-label">项目名称</div>
              <div class="info-value">{{ viewInvoiceData.project_name }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">我方公司</div>
              <div class="info-value">
                {{ viewInvoiceData.our_company_name }}
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">对方公司名称</div>
              <div class="info-value">{{ viewInvoiceData.supplier_name }}</div>
            </div>
          </div>
        </div>

        <!-- 附件展示 -->
        <div class="info-section">
          <h3 class="section-title">
            <span class="title-icon">📎</span>
            发票附件
            <span v-if="attachmentLoading" class="loading-indicator">
              <Spin size="small" />
            </span>
          </h3>

          <div v-if="viewAttachments.length > 0" class="attachments-grid">
            <div
              v-for="file in viewAttachments"
              :key="file.id"
              class="attachment-card"
            >
              <div class="attachment-icon">
                <span class="file-icon">{{ getFileIcon(file.file_name) }}</span>
              </div>
              <div class="attachment-info">
                <div class="file-name" :title="file.file_name">
                  {{ file.file_name }}
                </div>
                <div class="file-meta">
                  <span class="file-size">{{
                    formatFileSize(file.file_size || 0)
                  }}</span>
                  <span class="upload-time">{{
                    formatDate(file.created_at)
                  }}</span>
                </div>
              </div>
              <div class="attachment-actions">
                <Button
                  type="text"
                  size="small"
                  @click="handlePreview(file)"
                  title="预览"
                >
                  👁️
                </Button>
                <Button
                  type="text"
                  size="small"
                  @click="handleDownload(file)"
                  title="下载"
                >
                  ⬇️
                </Button>
              </div>
            </div>
          </div>

          <div v-else-if="!attachmentLoading" class="no-attachments">
            <div class="no-attachments-icon">📎</div>
            <div class="no-attachments-text">暂无附件</div>
          </div>
        </div>
      </div>
    </div>
  </Modal>

  <!-- 图片预览Modal -->
  <AntModal
    v-model:open="previewVisible"
    :title="previewTitle"
    :footer="false"
    width="80%"
    centered
  >
    <div class="flex justify-center">
      <img
        :src="previewImage"
        :alt="previewTitle"
        class="max-h-[70vh] max-w-full object-contain"
      />
    </div>
  </AntModal>
</template>

<style lang="less" scoped>
// 根容器
.invoice-detail-modal {
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue',
    Arial, sans-serif;
}

// 加载状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;

  .loading-text {
    font-size: 16px;
    color: #666;
    margin: 16px 0 0 0;
  }
}

.invoice-detail-content {
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 8px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// 发票概览
.invoice-overview {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  color: white;

  .overview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .invoice-icon {
      width: 60px;
      height: 60px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10px);
    }

    .invoice-info {
      flex: 1;
      margin-left: 20px;

      .invoice-title {
        font-size: 24px;
        font-weight: 700;
        margin: 0 0 6px 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .invoice-subtitle {
        font-size: 14px;
        opacity: 0.9;
        margin: 0;
      }
    }

    .invoice-amount {
      text-align: right;

      .amount-value {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 4px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .amount-label {
        font-size: 12px;
        opacity: 0.9;
      }
    }
  }
}

// 信息展示区域
.info-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #f0f0f0;

  .section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0 0 16px 0;
    padding-bottom: 12px;
    border-bottom: 2px solid #f0f0f0;

    .title-icon {
      font-size: 18px;
    }

    .loading-indicator {
      margin-left: auto;
    }
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 6px;

    &.full-width {
      grid-column: 1 / -1;
    }

    .info-label {
      font-size: 13px;
      color: #666;
      font-weight: 500;
    }

    .info-value {
      font-size: 14px;
      color: #1a1a1a;
      font-weight: 600;

      &.primary {
        color: #1890ff;
        font-size: 16px;
      }
    }
  }
}

// 附件展示
.attachments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 12px;
}

.attachment-card {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    background: #f0f9ff;
    border-color: #1890ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
  }

  .attachment-icon {
    width: 40px;
    height: 40px;
    background: white;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .file-icon {
      font-size: 20px;
    }
  }

  .attachment-info {
    flex: 1;
    min-width: 0;

    .file-name {
      font-size: 13px;
      font-weight: 500;
      color: #1a1a1a;
      margin-bottom: 3px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .file-meta {
      display: flex;
      gap: 8px;
      font-size: 11px;
      color: #666;

      .file-size {
        &::after {
          content: '•';
          margin: 0 6px;
          color: #ccc;
        }
      }
    }
  }

  .attachment-actions {
    display: flex;
    gap: 2px;

    :deep(.ant-btn) {
      width: 28px;
      height: 28px;
      padding: 0;
      border: none;
      background: transparent;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;

      &:hover {
        background: rgba(24, 144, 255, 0.1);
      }
    }
  }
}

.no-attachments {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;

  .no-attachments-icon {
    font-size: 40px;
    margin-bottom: 12px;
    opacity: 0.5;
  }

  .no-attachments-text {
    font-size: 14px;
    color: #999;
  }
}
</style>
