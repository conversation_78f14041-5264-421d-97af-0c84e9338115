<script lang="ts" setup>
import type {
  CreateInvoiceParams,
  InvoiceListItem,
  UpdateInvoiceParams,
} from '#/api/core/purchase/invoice';

import { computed, onMounted, onUnmounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Modal as AntModal, Button, message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  createInvoice,
  getInvoiceSummaryByContractId,
  updateInvoice,
} from '#/api/core/purchase/invoice';
import { getPurchaseContractById } from '#/api/core/purchase/purchase_contract';
import FileUploader from '#/components/FileUploader/FileUploader.vue';
import { formatDate } from '#/utils/common/time';
import { useInvoiceSchema } from '#/views/purchaseManagement/invoice_management/schema';

import { CONTRACT_TYPE_CONFIG } from '../../purchase_contract/constants';

const emit = defineEmits(['success']);

// 表单状态
const contractSummary = ref<any>(null);
const contractData = ref<any>(null); // 完整的合同信息
const currentContractId = ref<null | number>(null);
const isMounted = ref(true);
const fileUploaderRef = ref<InstanceType<typeof FileUploader> | null>(null);
const readOnly = ref(false);

// 创建表单
const [InvoiceForm, invoiceFormApi] = useVbenForm({
  layout: 'vertical',
  schema: useInvoiceSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-2',
});

// 在script部分顶部添加全局处理函数
onMounted(() => {
  // 注册全局处理函数
  window.handleContractChange = (id: number) => {
    // 检查组件是否仍然挂载
    if (isMounted.value) {
      handleContractChange(id);
    }
  };
});

// 组件卸载时清除全局函数
onUnmounted(() => {
  isMounted.value = false;
  window.handleContractChange = undefined;
});

// 处理合同变更
async function handleContractChange(contractId: number) {
  if (!contractId) {
    if (isMounted.value) {
      contractSummary.value = null;
      contractData.value = null;
      currentContractId.value = null;
    }
    return;
  }

  if (isMounted.value) {
    currentContractId.value = contractId;
  }

  try {
    // 获取合同汇总信息
    const summary = await getInvoiceSummaryByContractId(contractId);
    // 获取完整的合同信息
    const contract = await getPurchaseContractById(contractId);

    // 检查组件是否仍然挂载
    if (isMounted.value) {
      contractSummary.value = summary;
      contractData.value = contract;
    }
  } catch (error) {
    console.error('获取合同汇总信息失败:', error);
    // 如果API不存在，设置一个默认的汇总信息
    if (isMounted.value) {
      contractSummary.value = {
        contract_id: contractId,
        contract_no: `合同-${contractId}`,
        contract_total_amount: 0,
        total_invoiced_amount: 0,
        remaining_amount: 0,
        invoice_count: 0,
      };
    }
  }
}

// 验证开票金额
async function validateAmount(amount: number, contractId: number) {
  if (!amount || !contractId) return true;

  try {
    // 暂时跳过API验证，因为后端接口还未实现
    // TODO: 等后端实现 /purchase/invoices/validate 接口后启用
    /*
    const result = await validateInvoiceAmount({
      contract_id: contractId,
      invoice_amount: amount,
      exclude_invoice_id: excludeInvoiceId,
    });

    if (!result.valid) {
      message.error(result.message || '开票金额验证失败');
      return false;
    }
    */

    // 简单的前端验证：检查是否超过合同剩余金额
    if (contractSummary.value && contractSummary.value.remaining_amount) {
      const remainingAmount = contractSummary.value.remaining_amount;
      if (amount > remainingAmount) {
        message.error(
          `开票金额不能超过剩余可开票金额 ¥${remainingAmount.toLocaleString()}`,
        );
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('验证开票金额失败:', error);
    // 即使验证失败也允许继续，避免阻塞用户操作
    return true;
  }
}

// 实际提交函数
async function handleActualSubmit() {
  try {
    const { valid } = await invoiceFormApi.validate();
    if (!valid) {
      modalApi.lock(false);
      message.error('请检查必填项');
      return;
    }

    modalApi.lock();

    // 获取表单数据
    const formData = await invoiceFormApi.getValues();

    // 验证开票金额
    const data = modalApi.getData<InvoiceListItem & { contractId?: number }>();
    const amountValid = await validateAmount(
      formData.invoice_amount,
      formData.contract_id,
    );
    if (!amountValid) {
      modalApi.lock(false);
      return;
    }

    try {
      if (data?.id) {
        // 更新发票
        const updateData: UpdateInvoiceParams = {
          invoice_no: formData.invoice_no,
          invoice_amount: formData.invoice_amount,
          remark: formData.remark,
        };
        try {
          await updateInvoice(data.id, updateData);
          message.success('发票更新成功');
        } catch (error: any) {
          if (error.response?.status === 404) {
            message.error('发票更新API暂未实现，请联系后端开发人员');
          } else {
            throw error; // 重新抛出其他错误
          }
          return;
        }
      } else {
        // 创建发票
        const createData: CreateInvoiceParams = {
          contract_id: formData.contract_id,
          invoice_no: formData.invoice_no,
          invoice_amount: formData.invoice_amount,
          remark: formData.remark,
        };
        try {
          const result = await createInvoice(createData);

          // 上传附件
          if (
            fileUploaderRef.value &&
            fileUploaderRef.value.pendingFiles &&
            fileUploaderRef.value.pendingFiles.length > 0
          ) {
            try {
              if (result.id) {
                await fileUploaderRef.value.uploadFilesToServer(result.id);
                message.success('发票创建成功，附件上传完成');
              } else {
                message.warning('发票创建成功，但无法获取发票ID，附件上传失败');
              }
            } catch (uploadError) {
              console.error('附件上传失败:', uploadError);
              message.warning('发票创建成功，但附件上传失败');
            }
          } else {
            message.success('发票创建成功');
          }
        } catch (error: any) {
          if (error.response?.status === 404) {
            message.error('发票创建API暂未实现，请联系后端开发人员');
          } else {
            throw error; // 重新抛出其他错误
          }
          return;
        }
      }

      modalApi.close();
      emit('success');
    } catch (error: any) {
      console.error('操作失败:', error);
      message.error(error.message || '操作失败');
      modalApi.lock(false);
    }
  } catch (error) {
    console.error('表单验证错误:', error);
    modalApi.lock(false);
  }
}

// 创建Modal
const [Modal, modalApi] = useVbenModal({
  class: '!w-full',
  async onConfirm() {
    // 只读模式下不允许提交
    if (readOnly.value) {
      modalApi.close();
      return;
    }

    // 先进行基本验证
    try {
      const { valid } = await invoiceFormApi.validate();
      if (!valid) {
        message.error('请检查必填项');
        return;
      }
    } catch (error) {
      console.error('表单验证错误:', error);
      return;
    }

    // 获取表单数据用于确认弹框
    const formData = await invoiceFormApi.getValues();
    const data = modalApi.getData<InvoiceListItem & { contractId?: number }>();

    // 显示确认弹框
    AntModal.confirm({
      title: data?.id ? '确认更新发票' : '确认创建发票',
      content: `请确认以下信息：

• 发票编号：${formData.invoice_no || '未填写'}
• 开票金额：¥${formData.invoice_amount?.toLocaleString() || '0'}
• 关联合同：${contractData.value?.contract_no || '未选择'}
• 备注：${formData.remark || '无'}

${data?.id ? '确定要更新此发票吗？' : '确定要创建此发票吗？'}`,
      okText: data?.id ? '确认更新' : '确认创建',
      cancelText: '取消',
      okType: 'primary',
      width: 480,
      centered: true,
      onOk() {
        handleActualSubmit();
      },
    });
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      // 重置挂载状态
      isMounted.value = true;

      const data = modalApi.getData<
        InvoiceListItem & { contractId?: number }
      >();
      if (data) {
        if (data.id) {
          // 查看模式 - 已创建的发票不允许编辑
          readOnly.value = true;
          invoiceFormApi.setValues({
            contract_id: data.contract_id,
            invoice_no: data.invoice_no,
            invoice_amount: data.invoice_amount,
            remark: data.remark,
            id: data.id, // 用于判断是否为编辑模式
          });
          if (isMounted.value) {
            handleContractChange(data.contract_id);
          }
        } else if (data.contractId) {
          // 创建模式，预选合同
          readOnly.value = false;
          invoiceFormApi.setValues({
            contract_id: data.contractId,
          });
          if (isMounted.value) {
            handleContractChange(data.contractId);
          }
        } else {
          // 创建模式，无预选
          readOnly.value = false;
          invoiceFormApi.setValues({});
        }
      } else {
        // 默认创建模式
        readOnly.value = false;
        invoiceFormApi.setValues({});
      }
    } else {
      // Modal关闭时，标记组件为卸载状态
      isMounted.value = false;
    }
  },
});

// 获取标题
const getTitle = computed(() => {
  const data = modalApi.getData<InvoiceListItem & { contractId?: number }>();
  if (readOnly.value) {
    return '查看发票';
  }
  return data?.id ? '编辑发票' : '创建发票';
});

// 暴露Modal API给父组件
defineExpose({
  modalApi,
});
</script>

<template>
  <Modal
    :title="getTitle"
    width="800px"
    :show-ok-button="!readOnly"
    :cancel-text="readOnly ? '关闭' : '取消'"
  >
    <!-- 提示信息 -->
    <div
      v-if="!contractData && !readOnly"
      class="mx-4 mb-6 rounded-lg border border-yellow-200 bg-yellow-50 p-4"
    >
      <div class="flex items-center text-yellow-700">
        <span class="mr-2 text-lg">💡</span>
        <span class="font-medium"
          >请先选择关联合同，系统将自动显示合同信息</span
        >
      </div>
    </div>

    <!-- 合同关键信息展示 -->
    <div v-if="contractData" class="mx-4 mb-6">
      <!-- 关键信息单行展示 -->
      <div class="space-y-3">
        <!-- 第一行：我方公司、对方公司、项目信息、合同类型 -->
        <div class="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-4">
          <!-- 我方公司 -->
          <div class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm">
            <div class="flex items-center">
              <span class="mr-2 text-cyan-500">🏛️</span>
              <span class="text-sm font-medium text-gray-600">我方公司：</span>
              <span class="ml-2 font-semibold text-gray-900">{{
                contractData.our_company_name || '-'
              }}</span>
            </div>
          </div>
          <!-- 对方公司 -->
          <div class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm">
            <div class="flex items-center">
              <span class="mr-2 text-blue-500">🏢</span>
              <span class="text-sm font-medium text-gray-600">供应商：</span>
              <span class="ml-2 font-semibold text-gray-900">{{
                contractData.supplier_name || '-'
              }}</span>
            </div>
          </div>
          <!-- 项目信息 -->
          <div class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm">
            <div class="flex items-center">
              <span class="mr-2 text-green-500">🎯</span>
              <span class="text-sm font-medium text-gray-600">项目名称：</span>
              <span class="ml-2 font-semibold text-gray-900">{{
                contractData.project_name || '-'
              }}</span>
            </div>
          </div>
          <!-- 合同类型 -->
          <div class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm">
            <div class="flex items-center">
              <span class="mr-2 text-purple-500">📝</span>
              <span class="text-sm font-medium text-gray-600">合同类型：</span>
              <span class="ml-2 font-semibold text-gray-900">{{
                CONTRACT_TYPE_CONFIG[contractData.contract_type]?.text ||
                contractData.contract_type ||
                '-'
              }}</span>
            </div>
          </div>
        </div>

        <!-- 第二行：签署日期、申请人 -->
        <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
          <div class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm">
            <div class="flex items-center">
              <span class="mr-2 text-orange-500">📅</span>
              <span class="text-sm font-medium text-gray-600">签署日期：</span>
              <span class="ml-2 font-semibold text-gray-900">
                {{
                  contractData.signing_date
                    ? formatDate(contractData.signing_date)
                    : '-'
                }}
              </span>
            </div>
          </div>
          <div class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm">
            <div class="flex items-center">
              <span class="mr-2 text-yellow-500">👤</span>
              <span class="text-sm font-medium text-gray-600">申请人：</span>
              <span class="ml-2 font-semibold text-gray-900">{{
                contractData.creator_name || '-'
              }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 发票统计信息 -->
      <div v-if="contractSummary" class="mt-4">
        <div
          class="rounded-xl border border-green-200 bg-gradient-to-r from-green-50 to-emerald-50 p-4 shadow-sm"
        >
          <div class="mb-3 flex items-center">
            <div
              class="mr-3 flex h-8 w-8 items-center justify-center rounded-full bg-green-500 text-white"
            >
              <span class="text-sm">💰</span>
            </div>
            <h4 class="text-base font-bold text-green-900">发票统计</h4>
          </div>
          <div class="grid grid-cols-2 gap-3 md:grid-cols-4">
            <div class="text-center">
              <div class="text-lg font-bold text-blue-600">
                ¥{{ contractSummary.contract_total_amount.toLocaleString() }}
              </div>
              <div class="text-xs text-gray-600">合同总金额</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-bold text-green-600">
                ¥{{ contractSummary.total_invoiced_amount.toLocaleString() }}
              </div>
              <div class="text-xs text-gray-600">已开票金额</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-bold text-orange-600">
                ¥{{ contractSummary.remaining_amount.toLocaleString() }}
              </div>
              <div class="text-xs text-gray-600">剩余可开票</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-bold text-purple-600">
                {{ contractSummary.invoice_count }}
              </div>
              <div class="text-xs text-gray-600">发票数量</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-content">
      <InvoiceForm :disabled="readOnly" />
    </div>

    <!-- 附件上传 -->
    <div class="mx-4 mt-6">
      <div class="mb-3 text-base font-medium text-gray-800">发票附件</div>
      <div class="rounded-lg border border-gray-200 bg-gray-50 p-4">
        <FileUploader
          ref="fileUploaderRef"
          file-description="发票附件"
          module-type="invoices"
          :module-id="modalApi.getData()?.id"
          :disabled="readOnly"
          show-upload-list
          :max-count="10"
          multiple
          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.xlsx,.xls,.csv"
          list-type="text"
          compact
        >
          <Button v-if="!readOnly" type="primary" size="small">
            <span class="mr-1">📎</span>
            选择附件
          </Button>
        </FileUploader>
        <div class="mt-2 text-xs text-gray-500">
          支持格式：PDF、Word文档、Excel表格、CSV文件、图片文件，最多上传10个文件
        </div>
      </div>
    </div>
  </Modal>
</template>

<style lang="less" scoped>
.contract-summary {
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  border: 1px solid #91d5ff;
  border-radius: 12px;
  padding: 20px;

  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;

    .label {
      font-size: 14px;
    }

    .value {
      font-size: 14px;
      font-weight: 500;
    }
  }
}
</style>
