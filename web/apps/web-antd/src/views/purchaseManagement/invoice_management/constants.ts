// 发票管理相关常量

// 发票状态配置
export const INVOICE_STATUS_CONFIG = {
  draft: {
    text: '草稿',
    color: 'orange',
    description: '新创建的发票，可以修改和删除',
  },
  issued: {
    text: '已开票',
    color: 'green',
    description: '已正式开票，可以修改但不能删除',
  },
  cancelled: {
    text: '已取消',
    color: 'red',
    description: '已取消的发票，不能修改和删除',
  },
};

// 发票操作权限
export const INVOICE_PERMISSIONS = {
  CREATE: 'invoice:create',
  UPDATE: 'invoice:update',
  DELETE: 'invoice:delete',
  VIEW: 'invoice:view',
  EXPORT: 'invoice:export',
};

// 发票表单字段配置
export const INVOICE_FORM_FIELDS = {
  CONTRACT_ID: 'contract_id',
  INVOICE_NO: 'invoice_no',
  INVOICE_AMOUNT: 'invoice_amount',
  INVOICE_DATE: 'invoice_date',
  STATUS: 'status',
  REMARK: 'remark',
};

// 发票列表列配置
export const INVOICE_TABLE_COLUMNS = [
  {
    key: 'invoice_no',
    title: '发票号',
    width: 150,
    fixed: 'left',
  },
  {
    key: 'contract_no',
    title: '合同编号',
    width: 150,
  },
  {
    key: 'supplier_name',
    title: '供应商',
    width: 200,
  },
  {
    key: 'invoice_amount',
    title: '开票金额',
    width: 120,
    align: 'right',
  },
  {
    key: 'invoice_date',
    title: '开票日期',
    width: 120,
  },
  {
    key: 'status',
    title: '状态',
    width: 100,
  },
  {
    key: 'creator_name',
    title: '创建人',
    width: 100,
  },
  {
    key: 'created_at',
    title: '创建时间',
    width: 150,
  },
  {
    key: 'actions',
    title: '操作',
    width: 150,
    fixed: 'right',
  },
];

// 发票搜索配置
export const INVOICE_SEARCH_CONFIG = {
  PLACEHOLDER: '搜索合同编号、供应商或项目名称...',
  DEBOUNCE_TIME: 300,
};

// 发票分页配置
export const INVOICE_PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZE_OPTIONS: ['10', '20', '50', '100'],
  SHOW_SIZE_CHANGER: true,
  SHOW_QUICK_JUMPER: true,
};

// 发票导出配置
export const INVOICE_EXPORT_CONFIG = {
  FILENAME_PREFIX: 'invoices_',
  DATE_FORMAT: 'YYYY-MM-DD',
  SUPPORTED_FORMATS: ['xlsx', 'csv'],
};

// 发票金额格式化配置
export const INVOICE_AMOUNT_CONFIG = {
  CURRENCY: 'CNY',
  LOCALE: 'zh-CN',
  MINIMUM_FRACTION_DIGITS: 2,
  MAXIMUM_FRACTION_DIGITS: 2,
};

// 发票日期格式配置
export const INVOICE_DATE_CONFIG = {
  DISPLAY_FORMAT: 'YYYY-MM-DD',
  INPUT_FORMAT: 'YYYY-MM-DD',
  DATETIME_FORMAT: 'YYYY-MM-DD HH:mm:ss',
};

// 发票验证规则
export const INVOICE_VALIDATION_RULES = {
  INVOICE_NO: {
    MAX_LENGTH: 50,
    PATTERN: /^[\w-]+$/,
    MESSAGE: '发票号只能包含字母、数字、横线和下划线',
  },
  INVOICE_AMOUNT: {
    MIN: 0.01,
    MAX: 999_999_999.99,
    PRECISION: 2,
  },
  REMARK: {
    MAX_LENGTH: 500,
  },
};

// 发票文件夹动画配置
export const INVOICE_ANIMATION_CONFIG = {
  DURATION: {
    FAST: 200,
    NORMAL: 300,
    SLOW: 500,
  },
  EASING: {
    EASE: 'ease',
    EASE_IN: 'ease-in',
    EASE_OUT: 'ease-out',
    EASE_IN_OUT: 'ease-in-out',
  },
};

// 发票统计配置
export const INVOICE_STATS_CONFIG = {
  REFRESH_INTERVAL: 30_000, // 30秒
  CHART_COLORS: {
    PRIMARY: '#1890ff',
    SUCCESS: '#52c41a',
    WARNING: '#faad14',
    DANGER: '#f5222d',
  },
};
