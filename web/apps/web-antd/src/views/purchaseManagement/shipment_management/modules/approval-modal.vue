<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { approveShipment } from '#/api/core/purchase/shipment';

import { useApprovalFormSchema } from '../schema';

interface Props {
  shipmentId?: number;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  success: [];
}>();

const loading = ref(false);

const [Modal, modalApi] = useVbenModal({
  onOpenChange: (isOpen: boolean) => {
    if (!isOpen) {
      formApi.resetForm();
    }
  },
});

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useApprovalFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1',
});

/**
 * 提交审批
 */
async function handleSubmit() {
  const { valid } = await formApi.validate();
  if (!valid) {
    return;
  }

  if (!props.shipmentId) {
    message.error('发货记录ID不能为空');
    return;
  }

  const values = await formApi.getValues();

  loading.value = true;
  try {
    await approveShipment({
      shipment_id: props.shipmentId,
      action: values.action,
      comments: values.comments,
    });

    message.success(
      values.action === 'approve' ? '审批通过成功' : '审批拒绝成功',
    );
    modalApi.close();
    emit('success');
  } catch (error) {
    console.error('审批失败:', error);
    message.error('审批失败');
  } finally {
    loading.value = false;
  }
}

/**
 * 打开模态框
 */
function open() {
  modalApi.open();
}

defineExpose({
  open,
});
</script>

<template>
  <Modal title="发货审批" width="600px" @confirm="handleSubmit">
    <Form />

    <template #footer>
      <div class="flex justify-end space-x-2">
        <Button @click="modalApi.close()">取消</Button>
        <Button type="primary" :loading="loading" @click="handleSubmit">
          提交
        </Button>
      </div>
    </template>
  </Modal>
</template>
