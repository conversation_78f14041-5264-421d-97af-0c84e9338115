<script lang="ts" setup>
import type { ShipmentItem } from '#/api/core/purchase/shipment';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';

import { useItemFormSchema } from '../schema';

interface Props {
  contractItems?: any[];
}

const props = defineProps<Props>();

const emit = defineEmits<{
  success: [item: ShipmentItem];
}>();

const loading = ref(false);

const [Modal, modalApi] = useVbenModal({
  onOpenChange: (isOpen: boolean) => {
    if (!isOpen) {
      formApi.resetForm();
    }
  },
});

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useItemFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4',
});

/**
 * 提交表单
 */
async function handleSubmit() {
  const { valid } = await formApi.validate();
  if (!valid) {
    return;
  }

  const values = await formApi.getValues();

  // 构建发货明细项数据
  const item: ShipmentItem = {
    contract_item_id: values.contract_item_id,
    current_shipment_quantity: values.current_shipment_quantity,
    current_shipment_amount: values.current_shipment_amount,
    expected_arrival_date: values.expected_arrival_date,
    remark: values.remark,
    // 其他字段会在父组件中补充
    contract_quantity: 0,
    contract_amount: 0,
    shipped_quantity: 0,
    shipped_amount: 0,
    unshipped_quantity: 0,
    unshipped_amount: 0,
  };

  modalApi.close();
  emit('success', item);
}

/**
 * 打开模态框
 */
function open() {
  // 更新合同明细选项
  if (props.contractItems) {
    const contractItemOptions = props.contractItems.map((item) => ({
      label: `${item.material_type || ''} - ${item.model || ''} - ${item.brand || ''}`,
      value: item.id,
    }));

    // 更新表单schema中的选项
    const schema = useItemFormSchema();
    const contractItemField = schema.find(
      (field) => field.fieldName === 'contract_item_id',
    );
    if (contractItemField && contractItemField.componentProps) {
      contractItemField.componentProps.options = contractItemOptions;
    }
  }

  modalApi.open();
}

defineExpose({
  open,
});
</script>

<template>
  <Modal title="添加发货明细" width="800px" @confirm="handleSubmit">
    <Form />

    <template #footer>
      <div class="flex justify-end space-x-2">
        <Button @click="modalApi.close()">取消</Button>
        <Button type="primary" :loading="loading" @click="handleSubmit">
          确定
        </Button>
      </div>
    </template>
  </Modal>
</template>