<script lang="ts" setup>
import type { Shipment, ShipmentItem } from '#/api/core/purchase/shipment';

import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';

import {
  Modal as AModal,
  Button,
  Checkbox,
  message,
  Tabs,
} from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { getPaymentRequestsByContract } from '#/api/core/purchase/payment_request';
import { getPurchaseContractById } from '#/api/core/purchase/purchase_contract';
import {
  createShipment,
  getShipmentsByContract,
  updateShipment,
} from '#/api/core/purchase/shipment';
import FileUploader from '#/components/FileUploader/FileUploader.vue';
import { formatDate } from '#/utils/common/time';

import { CONTRACT_TYPE_CONFIG } from '../../purchase_contract/constants';
import ContractItemShipmentStatus from '../components/ContractItemShipmentStatus.vue';
import { useItemColumns } from '../data';
import { useFormSchema } from '../schema';

interface Props {
  data?: null | Shipment;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  success: [];
}>();

// 表单数据和状态
const formData = ref<null | Shipment>(null);
const readOnly = ref(false);
const contractData = ref<any>(null);
const supplierData = ref<any>(null);

// 发货明细相关
const shipmentItems = ref<ShipmentItem[]>([]);
const showContractItemsModal = ref(false);
const selectAllChecked = ref(false);
const selectAllIndeterminate = ref(false);

// 附件上传相关
const shipmentDocumentUploaderRef = ref();

const activeKey = ref('basic');

// 二次确认弹窗
const confirmModalVisible = ref(false);
const confirmLoading = ref(false);
const pendingData = ref<any>(null);

const getTitle = computed(() => {
  if (readOnly.value) {
    return '查看发货记录';
  }
  return formData.value?.id ? '编辑发货记录' : '创建发货记录';
});

const [Modal, modalApi] = useVbenModal({
  class: '!w-full',
  onConfirm: handleSubmit,
  onOpenChange: (isOpen: boolean) => {
    if (!isOpen) {
      resetForm();
    }
  },
});

const [BasicForm, basicFormApi] = useVbenForm({
  layout: 'vertical',
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-2',
});

// 发货明细表格
const [ItemGrid, itemGridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useItemColumns(),
    keepSource: true,
    pagerConfig: {
      enabled: false,
    },
    editConfig: {
      trigger: 'click',
      mode: 'cell',
    },
    editRules: {
      current_shipment_quantity: [
        { required: true, message: '请输入本次发货数量' },
        { type: 'number', min: 0, message: '本次发货数量不能小于0' },
      ],
    },
  },
  gridEvents: {
    editClosed: ({ row, column }: any) => {
      // 当本次发货数量变化时，自动计算本次发货金额
      if (column.field === 'current_shipment_quantity') {
        const quantity = Number(row.current_shipment_quantity) || 0;
        const price = Number(row.contract_item?.contract_price) || 0;
        row.current_shipment_amount = quantity * price;
      }
    },
  },
});

// 监听发货明细数据变化，更新表格
watch(
  shipmentItems,
  async (newItems) => {
    // 等待DOM更新
    await nextTick();

    // 使用setState来更新表格数据
    if (itemGridApi && itemGridApi.setState) {
      try {
        itemGridApi.setState({
          gridOptions: {
            data: newItems,
          },
        });
      } catch (error) {
        console.error('更新表格数据失败:', error);
      }
    }
  },
  { deep: true, immediate: false },
);

const formValues = ref<Record<string, any>>({});

async function updateFormValues() {
  try {
    const values = await basicFormApi.getValues();
    if (values) {
      formValues.value = values;
    }
  } catch (error) {
    console.error('获取表单值失败:', error);
  }
}

// 监听合同ID变化
watch(
  () => formValues.value?.contract_id,
  (newVal, oldVal) => {
    if (newVal !== oldVal && newVal) {
      handleContractChange(Number(newVal));
    }
  },
);

// 定期更新表单值以确保实时性
let updateInterval: NodeJS.Timeout | null = null;

onMounted(() => {
  nextTick(() => {
    updateFormValues();
  });

  // 设置定时器定期更新表单值
  updateInterval = setInterval(() => {
    updateFormValues();
  }, 500); // 每500ms更新一次
});

onUnmounted(() => {
  // 清理定时器
  if (updateInterval) {
    clearInterval(updateInterval);
    updateInterval = null;
  }
});

/**
 * 检查合同明细是否已选择
 */
function isContractItemSelected(contractItemId: number | undefined): boolean {
  if (!contractItemId) return false;
  return shipmentItems.value.some(
    (item) => item.contract_item_id === contractItemId,
  );
}

/**
 * 获取合同明细项的付款信息
 */
async function getContractItemPaymentInfo(
  contractItemId: number,
): Promise<{ paidAmount: number; paidQuantity: number }> {
  try {
    if (!contractData.value?.id) {
      return { paidQuantity: 0, paidAmount: 0 };
    }

    // 获取该合同的所有付款申请
    const paymentRequests = await getPaymentRequestsByContract(
      contractData.value.id,
    );

    let totalPaidQuantity = 0;
    let totalPaidAmount = 0;

    // 遍历所有已完成的付款申请
    for (const payment of paymentRequests) {
      if (payment.status === 'completed' && payment.items) {
        // 查找该明细项的付款记录
        const paymentItem = payment.items.find(
          (item) => item.contract_item_id === contractItemId,
        );
        if (paymentItem) {
          totalPaidQuantity += paymentItem.current_payment_quantity || 0;
          totalPaidAmount += paymentItem.current_payment_amount || 0;
        }
      }
    }

    return {
      paidQuantity: totalPaidQuantity,
      paidAmount: totalPaidAmount,
    };
  } catch (error) {
    console.error('获取付款信息失败:', error);
    return { paidQuantity: 0, paidAmount: 0 };
  }
}

/**
 * 获取合同明细项的已发货统计信息
 */
async function getContractItemShipmentStats(
  contractItemId: number,
): Promise<{ shippedAmount: number; shippedQuantity: number }> {
  try {
    if (!contractData.value?.id) {
      return { shippedQuantity: 0, shippedAmount: 0 };
    }

    // 获取该合同的所有发货记录
    const shipments = await getShipmentsByContract(contractData.value.id);

    let totalShippedQuantity = 0;
    let totalShippedAmount = 0;

    // 遍历所有已审批完成的发货记录
    for (const shipment of shipments) {
      if (shipment.status === 'completed' && shipment.items) {
        // 查找该明细项的发货记录
        const shipmentItem = shipment.items.find(
          (item) => item.contract_item_id === contractItemId,
        );
        if (shipmentItem) {
          totalShippedQuantity += shipmentItem.current_shipment_quantity || 0;
          totalShippedAmount += shipmentItem.current_shipment_amount || 0;
        }
      }
    }

    return {
      shippedQuantity: totalShippedQuantity,
      shippedAmount: totalShippedAmount,
    };
  } catch (error) {
    console.error('获取发货统计信息失败:', error);
    return { shippedQuantity: 0, shippedAmount: 0 };
  }
}

/**
 * 切换合同明细选择状态
 */
async function toggleContractItem(contractItem: any, event: Event) {
  const target = event.target as HTMLInputElement;
  const isChecked = target.checked;

  if (isChecked) {
    // 获取付款信息
    const paymentInfo = await getContractItemPaymentInfo(contractItem.id);

    // 获取已发货统计信息
    const shipmentStats = await getContractItemShipmentStats(contractItem.id);

    // 计算未发货数量和金额
    const unshippedQuantity =
      contractItem.contract_quantity - shipmentStats.shippedQuantity;
    const unshippedAmount =
      contractItem.contract_amount - shipmentStats.shippedAmount;

    // 检查是否已全部发货
    if (unshippedQuantity <= 0) {
      message.warning(
        `该合同明细项"${contractItem.material_type || ''}${contractItem.model ? ` - ${contractItem.model}` : ''}"已全部发货，无需再次创建发货记录！`,
      );
      // 取消选择
      target.checked = false;
      return;
    }

    // 添加到发货明细
    const shipmentItem: ShipmentItem = {
      id: undefined,
      shipment_id: undefined,
      contract_item_id: contractItem.id!,
      contract_quantity: contractItem.contract_quantity,
      contract_amount: contractItem.contract_amount,
      shipped_quantity: shipmentStats.shippedQuantity, // 从已审批完成的发货单统计
      shipped_amount: shipmentStats.shippedAmount, // 从已审批完成的发货单统计
      unshipped_quantity: Math.max(0, unshippedQuantity), // 确保不为负数
      unshipped_amount: Math.max(0, unshippedAmount), // 确保不为负数
      current_shipment_quantity: 0,
      current_shipment_amount: 0,
      expected_arrival_date: undefined, // 需要用户手动填写
      serial_numbers: undefined,
      remark: undefined,

      // 付款统计信息
      paid_quantity: paymentInfo.paidQuantity,
      paid_amount: paymentInfo.paidAmount,
      unpaid_quantity: Math.max(
        0,
        contractItem.contract_quantity - paymentInfo.paidQuantity,
      ),
      unpaid_amount: Math.max(
        0,
        contractItem.contract_amount - paymentInfo.paidAmount,
      ),
      contract_item: {
        id: contractItem.id || 0,
        material_type: contractItem.material_type,
        model: contractItem.model || '',
        brand: contractItem.brand || '',
        pn: contractItem.pn || '',
        spec: contractItem.spec || '',
        unit: contractItem.unit || '',
        contract_quantity: contractItem.contract_quantity,
        contract_price: contractItem.contract_price,
      },
      serial_number_list: [],
    };
    shipmentItems.value.push(shipmentItem);
  } else {
    // 从发货明细中移除
    const index = shipmentItems.value.findIndex(
      (item) => item.contract_item_id === contractItem.id,
    );
    if (index !== -1) {
      shipmentItems.value.splice(index, 1);
    }
  }

  // 更新全选状态
  updateSelectAllStatus();
}

/**
 * 更新全选状态
 */
function updateSelectAllStatus() {
  if (!contractData.value?.items) {
    selectAllChecked.value = false;
    selectAllIndeterminate.value = false;
    return;
  }

  // 获取可选择的合同明细项（未全部发货的）
  const selectableItems = contractData.value.items.filter((_item: any) => {
    // 这里可以添加过滤逻辑，比如过滤掉已全部发货的项
    return true; // 暂时允许选择所有项
  });

  const selectedCount = selectableItems.filter((item: any) =>
    isContractItemSelected(item.id),
  ).length;

  if (selectedCount === 0) {
    selectAllChecked.value = false;
    selectAllIndeterminate.value = false;
  } else if (selectedCount === selectableItems.length) {
    selectAllChecked.value = true;
    selectAllIndeterminate.value = false;
  } else {
    selectAllChecked.value = false;
    selectAllIndeterminate.value = true;
  }
}

/**
 * 处理全选/取消全选
 */
async function handleSelectAll(event: any) {
  const isChecked = event.target?.checked ?? event;

  if (!contractData.value?.items) return;

  // 获取可选择的合同明细项
  const selectableItems = contractData.value.items.filter((_item: any) => {
    // 这里可以添加过滤逻辑，比如过滤掉已全部发货的项
    return true; // 暂时允许选择所有项
  });

  for (const item of selectableItems) {
    const isCurrentlySelected = isContractItemSelected(item.id);

    if (isChecked && !isCurrentlySelected) {
      // 需要选中但当前未选中
      const success = await toggleContractItemDirect(item, true);
      if (!success) {
        // 如果选择失败，跳过这个项目
        continue;
      }
    } else if (!isChecked && isCurrentlySelected) {
      // 需要取消选中但当前已选中
      await toggleContractItemDirect(item, false);
    }
  }

  updateSelectAllStatus();
}

/**
 * 直接切换合同明细选择状态（不依赖事件）
 * @returns {Promise<boolean>} 返回是否成功
 */
async function toggleContractItemDirect(
  contractItem: any,
  isChecked: boolean,
): Promise<boolean> {
  if (isChecked) {
    // 获取付款信息
    const paymentInfo = await getContractItemPaymentInfo(contractItem.id);

    // 获取已发货统计信息
    const shipmentStats = await getContractItemShipmentStats(contractItem.id);

    // 计算未发货数量和金额
    const unshippedQuantity =
      contractItem.contract_quantity - shipmentStats.shippedQuantity;
    const unshippedAmount =
      contractItem.contract_amount - shipmentStats.shippedAmount;

    // 检查是否已全部发货
    if (unshippedQuantity <= 0) {
      message.warning(
        `该合同明细项"${contractItem.material_type || ''}${contractItem.model ? ` - ${contractItem.model}` : ''}"已全部发货，无需再次创建发货记录！`,
      );
      return false; // 返回false表示选择失败
    }

    // 添加到发货明细
    const shipmentItem: ShipmentItem = {
      id: undefined,
      shipment_id: undefined,
      contract_item_id: contractItem.id!,
      contract_quantity: contractItem.contract_quantity,
      contract_amount: contractItem.contract_amount,
      shipped_quantity: shipmentStats.shippedQuantity,
      shipped_amount: shipmentStats.shippedAmount,
      unshipped_quantity: Math.max(0, unshippedQuantity),
      unshipped_amount: Math.max(0, unshippedAmount),
      current_shipment_quantity: 0,
      current_shipment_amount: 0,
      expected_arrival_date: undefined,
      serial_numbers: undefined,
      remark: undefined,

      // 付款统计信息
      paid_quantity: paymentInfo.paidQuantity,
      paid_amount: paymentInfo.paidAmount,
      unpaid_quantity: Math.max(
        0,
        contractItem.contract_quantity - paymentInfo.paidQuantity,
      ),
      unpaid_amount: Math.max(
        0,
        contractItem.contract_amount - paymentInfo.paidAmount,
      ),

      contract_item: {
        id: contractItem.id || 0,
        material_type: contractItem.material_type,
        model: contractItem.model || '',
        brand: contractItem.brand || '',
        pn: contractItem.pn || '',
        spec: contractItem.spec || '',
        unit: contractItem.unit || '',
        contract_quantity: contractItem.contract_quantity,
        contract_price: contractItem.contract_price,
      },
      serial_number_list: [],
    };
    shipmentItems.value.push(shipmentItem);
    return true; // 成功添加
  } else {
    // 从发货明细中移除
    const index = shipmentItems.value.findIndex(
      (item) => item.contract_item_id === contractItem.id,
    );
    if (index !== -1) {
      shipmentItems.value.splice(index, 1);
    }
    return true; // 成功移除
  }
}

/**
 * 确认选择合同明细
 */
function confirmContractItems() {
  showContractItemsModal.value = false;
}

/**
 * 删除行
 */
function handleDeleteRow(rowIndex: number) {
  if (shipmentItems.value.length <= 1) {
    message.warning('至少需要保留一行发货明细');
    return;
  }

  // 从数组中删除对应的行
  shipmentItems.value.splice(rowIndex, 1);

  // 更新表格数据
  if (itemGridApi && itemGridApi.setState) {
    try {
      itemGridApi.setState({
        gridOptions: {
          data: shipmentItems.value,
        },
      });
    } catch (error) {
      console.error('更新表格数据失败:', error);
    }
  }

  message.success('删除成功');
}

// 显示确认弹窗
function showConfirmModal(data: any) {
  pendingData.value = data;
  confirmModalVisible.value = true;
}

// 确认提交
async function handleConfirmSubmit() {
  if (!pendingData.value) return;

  confirmLoading.value = true;
  try {
    let result;

    if (formData.value?.id) {
      // 更新发货记录
      result = await updateShipment(formData.value.id, pendingData.value);
      message.success('更新发货记录成功');
    } else {
      // 创建发货记录
      result = await createShipment(pendingData.value);
      message.success('创建发货记录成功');
    }

    // 上传发货相关文件（如果有）
    if (shipmentDocumentUploaderRef.value && result?.id) {
      try {
        await shipmentDocumentUploaderRef.value.uploadFilesToServer(result.id);
      } catch (error) {
        console.error('发货文件上传失败:', error);
      }
    }

    confirmModalVisible.value = false;
    modalApi.close();
    emit('success');
  } catch (error) {
    console.error('提交失败:', error);
    message.error('提交失败');
  } finally {
    confirmLoading.value = false;
    modalApi.lock(false);
  }
}

// 取消确认
function handleCancelConfirm() {
  confirmModalVisible.value = false;
  modalApi.lock(false);
  pendingData.value = null;
}

// 根据合同明细ID获取合同明细信息
function getContractItemById(contractItemId: number) {
  return contractData.value?.items?.find(
    (item: any) => item.id === contractItemId,
  );
}

// 重置表单
function resetForm() {
  formData.value = null;
  contractData.value = null;
  supplierData.value = null;
  shipmentItems.value = [];
  activeKey.value = 'basic';
  readOnly.value = false;
  basicFormApi.resetForm();
}

// 处理合同变化
async function handleContractChange(contractId: number) {
  if (!contractId) {
    contractData.value = null;
    supplierData.value = null;
    shipmentItems.value = [];
    return;
  }

  try {
    // 获取合同详情
    const contract = await getPurchaseContractById(contractId);
    contractData.value = contract;

    // 设置供应商信息（从合同的supplier_name获取）
    if (contract.supplier_name) {
      supplierData.value = {
        id: contract.supplier_id,
        name: contract.supplier_name,
      };
    }

    // 清空发货明细，让用户手动选择
    shipmentItems.value = [];
  } catch (error) {
    console.error('获取合同信息失败:', error);
    message.error('获取合同信息失败');
    shipmentItems.value = [];
  }
}

// 打开模态框
async function openModal(data?: Shipment, readonly = false) {
  resetForm();
  readOnly.value = readonly;

  if (data) {
    formData.value = data;
    await nextTick();
    basicFormApi.setValues(data);

    if (data.contract_id) {
      await handleContractChange(data.contract_id);
    }

    // 设置发货明细数据
    if (data.items && data.items.length > 0) {
      shipmentItems.value = data.items;
    }
  }

  modalApi.open();
}

// 提交表单
async function handleSubmit() {
  const { valid } = await basicFormApi.validate();
  if (!valid) {
    modalApi.lock(false);
    message.error('请检查必填项');
    return;
  }

  const basicData = await basicFormApi.getValues();

  // 验证发货明细
  if (!shipmentItems.value || shipmentItems.value.length === 0) {
    modalApi.lock(false);
    message.error('请选择发货明细项');
    return;
  }

  // 从表格获取最新数据并同步到原始数组
  if (itemGridApi && itemGridApi.grid) {
    try {
      const tableData = itemGridApi.grid.getTableData();
      if (tableData && tableData.fullData && tableData.fullData.length > 0) {
        // 同步表格数据到原始数组
        tableData.fullData.forEach((tableRow: any, index: number) => {
          if (shipmentItems.value[index]) {
            shipmentItems.value[index].current_shipment_quantity =
              tableRow.current_shipment_quantity;
            shipmentItems.value[index].current_shipment_amount =
              tableRow.current_shipment_amount;
            shipmentItems.value[index].expected_arrival_date =
              tableRow.expected_arrival_date;
          }
        });
      }
    } catch (error) {
      console.warn('同步表格数据失败:', error);
    }
  }

  // 验证发货明细中的必填项
  for (let i = 0; i < shipmentItems.value.length; i++) {
    const item = shipmentItems.value[i];
    if (!item) {
      modalApi.lock(false);
      message.error(`第${i + 1}项发货明细数据异常`);
      return;
    }

    // 转换为数字进行验证
    const quantity = Number(item.current_shipment_quantity);
    if (Number.isNaN(quantity) || quantity <= 0) {
      modalApi.lock(false);
      message.error(
        `第${i + 1}项发货明细的发货数量不能为空或小于等于0，当前值: ${item.current_shipment_quantity}`,
      );
      return;
    }
  }

  try {
    const data = formData.value?.id
      ? {
          ...basicData,
          items: shipmentItems.value.map((item) => ({
            id: item.id,
            contract_item_id: item.contract_item_id,
            current_shipment_quantity:
              Number(item.current_shipment_quantity) || 0,
            current_shipment_amount: Number(item.current_shipment_amount) || 0,
            expected_arrival_date: item.expected_arrival_date,
            serial_numbers: item.serial_number_list || [],
            remark: item.remark,
          })),
        }
      : {
          contract_id: basicData.contract_id,
          shipment_notice: basicData.shipment_notice || '',
          tracking_info: basicData.tracking_info || '',
          delivery_address: basicData.delivery_address || '',
          expected_arrival_date: basicData.expected_arrival_date,
          remark: basicData.remark || '',
          items: shipmentItems.value.map((item) => ({
            contract_item_id: item.contract_item_id,
            current_shipment_quantity:
              Number(item.current_shipment_quantity) || 0,
            current_shipment_amount: Number(item.current_shipment_amount) || 0,
            expected_arrival_date: item.expected_arrival_date,
            serial_numbers: item.serial_number_list || [],
            remark: item.remark,
          })),
        };

    // 显示确认弹窗
    showConfirmModal(data);
  } catch (error) {
    console.error('数据准备失败:', error);
    modalApi.lock(false);
  }
}

// 监听props变化
watch(
  () => props.data,
  (data) => {
    if (data !== undefined) {
      openModal(data || undefined);
    }
  },
  { immediate: true },
);

// 监听modal显示状态，更新全选状态
watch(
  () => showContractItemsModal.value,
  (isVisible) => {
    if (isVisible) {
      updateSelectAllStatus();
    }
  },
);

// 监听发货明细变化，更新全选状态
watch(
  () => shipmentItems.value.length,
  () => {
    if (showContractItemsModal.value) {
      updateSelectAllStatus();
    }
  },
);

defineExpose({
  openModal,
});
</script>

<template>
  <Modal :title="getTitle" width="1000px">
    <Tabs v-model:active-key="activeKey" class="mx-4">
      <Tabs.TabPane key="basic" tab="基本信息">
        <!-- 提示信息 -->
        <div
          v-if="!contractData && !readOnly"
          class="mx-4 mb-4 rounded-lg border border-yellow-200 bg-yellow-50 p-4"
        >
          <div class="flex items-center text-yellow-700">
            <span class="mr-2 text-lg">💡</span>
            <span class="font-medium"
              >请先选择关联合同，系统将自动显示合同信息和对方公司信息</span
            >
          </div>
        </div>

        <!-- 合同关键信息展示 -->
        <div v-if="contractData" class="mx-4 mb-6">
          <!-- 合同标题和基本信息 -->
          <div
            class="mb-4 rounded-xl border border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 p-4 shadow-sm"
          >
            <div class="mb-3 flex items-center">
              <div
                class="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-blue-500 text-white"
              >
                <span class="text-lg">📋</span>
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-bold text-blue-900">
                  {{ contractData.contract_title || '合同标题' }}
                </h3>
                <p class="text-sm text-blue-600">
                  合同编号：{{ contractData.contract_no || '-' }}
                </p>
              </div>
              <div class="text-right">
                <div class="text-lg font-bold text-red-600">
                  ¥{{ Number(contractData.total_amount || 0).toLocaleString() }}
                </div>
                <div class="text-xs text-gray-500">合同总金额</div>
              </div>
            </div>
          </div>

          <!-- 关键信息单行展示 -->
          <div class="space-y-3">
            <!-- 第一行：我方公司、对方公司、项目信息、合同类型 -->
            <div class="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-4">
              <!-- 我方公司 -->
              <div
                class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm"
              >
                <div class="flex items-center">
                  <span class="mr-2 text-cyan-500">🏛️</span>
                  <span class="text-sm font-medium text-gray-600"
                    >我方公司：</span
                  >
                  <span class="ml-2 font-semibold text-gray-900">{{
                    contractData.our_company_name || '-'
                  }}</span>
                </div>
              </div>
              <!-- 对方公司 -->
              <div
                class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm"
              >
                <div class="flex items-center">
                  <span class="mr-2 text-blue-500">🏢</span>
                  <span class="text-sm font-medium text-gray-600"
                    >对方公司名称：</span
                  >
                  <span class="ml-2 font-semibold text-gray-900">{{
                    contractData.supplier_name || '-'
                  }}</span>
                </div>
              </div>
              <div
                class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm"
              >
                <div class="flex items-center">
                  <span class="mr-2 text-green-500">🎯</span>
                  <span class="text-sm font-medium text-gray-600"
                    >项目名称：</span
                  >
                  <span class="ml-2 font-semibold text-gray-900">{{
                    contractData.project_name || '-'
                  }}</span>
                </div>
              </div>
              <div
                class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm"
              >
                <div class="flex items-center">
                  <span class="mr-2 text-purple-500">📝</span>
                  <span class="text-sm font-medium text-gray-600"
                    >合同类型：</span
                  >
                  <span class="ml-2 font-semibold text-gray-900">{{
                    CONTRACT_TYPE_CONFIG[contractData.contract_type]?.text ||
                    contractData.contract_type ||
                    '-'
                  }}</span>
                </div>
              </div>
            </div>

            <!-- 第二行：签署日期、交付地址、申请人 -->
            <div class="grid grid-cols-1 gap-3 md:grid-cols-3">
              <div
                class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm"
              >
                <div class="flex items-center">
                  <span class="mr-2 text-orange-500">📅</span>
                  <span class="text-sm font-medium text-gray-600"
                    >签署日期：</span
                  >
                  <span class="ml-2 font-semibold text-gray-900">
                    {{
                      contractData.signing_date
                        ? formatDate(contractData.signing_date)
                        : '-'
                    }}
                  </span>
                </div>
              </div>
              <div
                class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm"
              >
                <div class="flex items-center">
                  <span class="mr-2 text-red-500">📍</span>
                  <span class="text-sm font-medium text-gray-600"
                    >交付地址：</span
                  >
                  <span class="ml-2 font-semibold text-gray-900">{{
                    contractData.delivery_address || '-'
                  }}</span>
                </div>
              </div>
              <div
                class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm"
              >
                <div class="flex items-center">
                  <span class="mr-2 text-yellow-500">👤</span>
                  <span class="text-sm font-medium text-gray-600"
                    >申请人：</span
                  >
                  <span class="ml-2 font-semibold text-gray-900">{{
                    contractData.creator_name || '-'
                  }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 基本信息表单 -->
        <div class="mx-4">
          <BasicForm />
        </div>

        <!-- 附件上传 -->
        <div class="mx-4 mt-6">
          <div class="mb-3 text-base font-medium text-gray-800">相关附件</div>
          <div class="rounded-lg border border-gray-200 bg-gray-50 p-4">
            <FileUploader
              ref="shipmentDocumentUploaderRef"
              file-description="发货文件"
              module-type="shipments"
              :module-id="formData?.id"
              :disabled="readOnly"
              show-upload-list
              :max-count="10"
              multiple
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.xlsx,.xls,.csv"
              list-type="text"
              compact
            >
              <Button v-if="!readOnly" type="primary" size="small">
                <span class="mr-1">📎</span>
                选择附件
              </Button>
            </FileUploader>
            <div class="mt-2 text-xs text-gray-500">
              支持格式：PDF、Word文档、Excel表格、CSV文件、图片文件，最多上传10个文件
            </div>
          </div>
        </div>
      </Tabs.TabPane>

      <Tabs.TabPane key="items" tab="发货明细">
        <div class="mx-4">
          <!-- 提示信息 -->
          <div
            v-if="!contractData && !readOnly"
            class="mb-4 rounded-lg border border-yellow-200 bg-yellow-50 p-4"
          >
            <div class="flex items-center text-yellow-700">
              <span class="mr-2 text-lg">💡</span>
              <span class="font-medium"
                >请先在基本信息中选择关联合同，然后在此处选择需要发货的合同明细项</span
              >
            </div>
          </div>

          <!-- 发货明细表格 -->
          <div v-if="contractData" class="space-y-4">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold">发货明细</h3>
              <Button
                type="primary"
                @click="showContractItemsModal = true"
                :disabled="readOnly"
              >
                选择合同明细
              </Button>
            </div>

            <!-- 明细表格 -->
            <div v-if="shipmentItems.length > 0">
              <ItemGrid>
                <!-- 操作列插槽 -->
                <template #actions="{ rowIndex }">
                  <Button
                    type="text"
                    danger
                    size="small"
                    @click="handleDeleteRow(rowIndex)"
                    title="删除这行"
                  >
                    🗑️
                  </Button>
                </template>
              </ItemGrid>
            </div>

            <div v-else class="py-8 text-center text-gray-500">
              暂无发货明细，请点击"选择合同明细"添加
            </div>
          </div>

          <div v-else class="py-8 text-center text-gray-500">
            <div class="mb-2">暂无发货明细</div>
            <div class="text-xs text-gray-400">请先选择关联合同</div>
          </div>
        </div>
      </Tabs.TabPane>
    </Tabs>
  </Modal>

  <!-- 合同明细选择模态框 -->
  <AModal
    v-model:open="showContractItemsModal"
    title="选择合同明细"
    width="1400px"
    :footer="null"
  >
    <div v-if="contractData?.items" class="space-y-4">
      <div class="mb-4 text-sm text-gray-600">
        请选择需要发货的合同明细项，系统将自动计算已发货和未发货数量。可使用表头的"全选"按钮快速选择所有项目。
      </div>

      <div class="overflow-x-auto">
        <table class="w-full rounded-lg border border-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                <div class="flex items-center space-x-2">
                  <Checkbox
                    :checked="selectAllChecked"
                    :indeterminate="selectAllIndeterminate"
                    @change="handleSelectAll"
                  />
                  <span>全选</span>
                </div>
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                物料类型
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                品牌
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                规格
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                型号
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                原厂PN
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                单位
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                合同数量
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                合同单价
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                合同金额
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                发货状态
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="item in contractData.items"
              :key="item.id"
              class="hover:bg-gray-50"
            >
              <td class="border-b px-4 py-3">
                <input
                  type="checkbox"
                  :checked="isContractItemSelected(item.id)"
                  @change="toggleContractItem(item, $event)"
                  class="rounded border-gray-300"
                />
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                {{ item.material_type || '-' }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                {{ item.brand || '-' }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                {{ item.spec || '-' }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                {{ item.model || '-' }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                <span class="font-mono text-sm text-blue-600">
                  {{ item.pn || '-' }}
                </span>
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                {{ item.unit || '-' }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                {{ item.contract_quantity || 0 }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                ¥{{ Number(item.contract_price || 0).toLocaleString() }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                ¥{{ Number(item.contract_amount || 0).toLocaleString() }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                <ContractItemShipmentStatus
                  :contract-item="item"
                  :contract-id="contractData?.id"
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="flex justify-end space-x-2 pt-4">
        <Button @click="showContractItemsModal = false"> 取消 </Button>
        <Button type="primary" @click="confirmContractItems"> 确定 </Button>
      </div>
    </div>

    <div v-else class="py-8 text-center text-gray-500">暂无合同明细数据</div>
  </AModal>

  <!-- 确认提交弹窗 -->
  <AModal
    v-model:open="confirmModalVisible"
    title="确认提交发货记录"
    width="800px"
    :confirm-loading="confirmLoading"
    @ok="handleConfirmSubmit"
    @cancel="handleCancelConfirm"
  >
    <div class="space-y-6">
      <!-- 基本信息 -->
      <div class="rounded border border-green-200 bg-green-50 p-4">
        <div class="mb-3 flex items-center">
          <div class="mr-2 h-4 w-1 rounded bg-green-500"></div>
          <h3 class="text-sm font-semibold text-green-800">发货基本信息</h3>
        </div>
        <div class="grid grid-cols-2 gap-2 text-sm text-green-700">
          <div>
            <span class="font-medium">关联合同:</span>
            {{ contractData?.contract_no }}
          </div>
          <div>
            <span class="font-medium">预计到货时间:</span>
            {{
              pendingData?.expected_arrival_date
                ? new Date(pendingData.expected_arrival_date).toLocaleString()
                : ''
            }}
          </div>
          <div v-if="pendingData?.shipment_notice">
            <span class="font-medium">发货通知:</span>
            {{ pendingData.shipment_notice }}
          </div>
          <div v-if="pendingData?.tracking_info">
            <span class="font-medium">物流跟踪:</span>
            {{ pendingData.tracking_info }}
          </div>
          <div v-if="pendingData?.delivery_address" class="col-span-2">
            <span class="font-medium">收货地址:</span>
            {{ pendingData.delivery_address }}
          </div>
          <div v-if="pendingData?.remark" class="col-span-2">
            <span class="font-medium">备注:</span>
            {{ pendingData.remark }}
          </div>
        </div>
      </div>

      <!-- 发货明细 -->
      <div class="rounded border border-blue-200 bg-blue-50 p-4">
        <div class="mb-3 flex items-center">
          <div class="mr-2 h-4 w-1 rounded bg-blue-500"></div>
          <h3 class="text-sm font-semibold text-blue-800">发货明细</h3>
        </div>
        <div class="space-y-3">
          <div
            v-for="(item, index) in pendingData?.items"
            :key="index"
            class="rounded border border-blue-300/50 bg-white p-3"
          >
            <div class="grid grid-cols-2 gap-2 text-sm text-blue-700">
              <div>
                <span class="font-medium">物料类型:</span>
                {{ getContractItemById(item.contract_item_id)?.material_type }}
              </div>
              <div>
                <span class="font-medium">品牌:</span>
                {{ getContractItemById(item.contract_item_id)?.brand }}
              </div>
              <div>
                <span class="font-medium">规格:</span>
                {{ getContractItemById(item.contract_item_id)?.spec }}
              </div>
              <div>
                <span class="font-medium">型号:</span>
                {{ getContractItemById(item.contract_item_id)?.model }}
              </div>
              <div>
                <span class="font-medium">本次发货数量:</span>
                {{ item.current_shipment_quantity }}
                {{ getContractItemById(item.contract_item_id)?.unit }}
              </div>
              <div>
                <span class="font-medium">本次发货金额:</span>
                ¥{{ item.current_shipment_amount?.toFixed(2) }}
              </div>

              <div v-if="item.remark" class="col-span-2">
                <span class="font-medium">备注:</span>
                {{ item.remark }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="border-t pt-4 text-center text-sm text-gray-500">
        请确认以上信息无误后点击确定提交
      </div>
    </div>
  </AModal>
</template>
