/**
 * 发货管理常量定义
 */

// 定义标签配置接口
export interface TagConfig {
  color: string;
  text: string;
}

// 定义标签配置映射接口
export interface TagConfigMap {
  [key: string]: TagConfig;
}

// 发货管理状态配置
export const SHIPMENT_STATUS_CONFIG: TagConfigMap = {
  draft: { color: '', text: '草稿' },
  purchase_review: { color: 'blue', text: '采购负责人审批' },
  finance_review: { color: 'purple', text: '财务负责人审批' },
  warehouse_review: { color: 'orange', text: '仓库管理员审批' },
  completed: { color: 'green', text: '已完成' },
  cancelled: { color: 'red', text: '已取消' },
};

// 发货管理状态选项
export const SHIPMENT_STATUS_OPTIONS = [
  { label: '草稿', value: 'draft' },
  { label: '采购负责人审批', value: 'purchase_review' },
  { label: '财务负责人审批', value: 'finance_review' },
  { label: '仓库管理员审批', value: 'warehouse_review' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'cancelled' },
];

// 发货完成状态配置
export const SHIPMENT_COMPLETE_CONFIG: TagConfigMap = {
  true: { color: 'green', text: '已全部发货' },
  false: { color: 'orange', text: '部分发货' },
};

// 发货完成状态选项
export const SHIPMENT_COMPLETE_OPTIONS = [
  { label: '已全部发货', value: true },
  { label: '部分发货', value: false },
];

// 操作类型映射
export const SHIPMENT_ACTION_CONFIG: TagConfigMap = {
  create: { text: '创建发货记录', color: 'blue' },
  submit: { text: '提交发货记录', color: 'green' },
  approve: { text: '审批通过', color: 'success' },
  reject: { text: '审批拒绝', color: 'error' },
  rollback: { text: '回退发货记录', color: 'orange' },
  cancel: { text: '取消发货记录', color: 'error' },
  update: { text: '更新发货记录', color: 'blue' },
  delete: { text: '删除发货记录', color: 'error' },
};

// 审批动作选项
export const SHIPMENT_APPROVAL_ACTION_OPTIONS = [
  { label: '批准', value: 'approve' },
  { label: '拒绝', value: 'reject' },
];

// 物料类型配置（可根据实际业务调整）
export const MATERIAL_TYPE_CONFIG: TagConfigMap = {
  server: { color: 'blue', text: '服务器' },
  network_device: { color: 'green', text: '网络设备' },
  storage: { color: 'purple', text: '存储设备' },
  software: { color: 'orange', text: '软件' },
  service: { color: 'cyan', text: '服务' },
  maintenance: { color: 'geekblue', text: '维保' },
  office: { color: 'magenta', text: '办公用品' },
  consulting: { color: 'gold', text: '咨询' },
  training: { color: 'lime', text: '培训' },
  marketing: { color: 'volcano', text: '营销' },
  other: { color: 'default', text: '其他' },
};

// 单位选项
export const UNIT_OPTIONS = [
  { label: '台', value: '台' },
  { label: '套', value: '套' },
  { label: '个', value: '个' },
  { label: '件', value: '件' },
  { label: '批', value: '批' },
  { label: '项', value: '项' },
  { label: '年', value: '年' },
  { label: '月', value: '月' },
  { label: '次', value: '次' },
  { label: '人', value: '人' },
  { label: '天', value: '天' },
  { label: '小时', value: '小时' },
];

// 表单验证规则
export const FORM_RULES = {
  contract_id: [
    { required: true, message: '请选择关联合同', trigger: 'change' },
  ],
  delivery_address: [
    { required: true, message: '请输入收货地址', trigger: 'blur' },
    { max: 500, message: '收货地址不能超过500个字符', trigger: 'blur' },
  ],
  tracking_info: [
    { max: 500, message: '物流信息不能超过500个字符', trigger: 'blur' },
  ],
  remark: [{ max: 1000, message: '备注不能超过1000个字符', trigger: 'blur' }],
  current_shipment_quantity: [
    { required: true, message: '请输入本次发货数量', trigger: 'blur' },
    { type: 'number', min: 0, message: '发货数量不能小于0', trigger: 'blur' },
  ],
  current_shipment_amount: [
    { required: true, message: '请输入本次发货金额', trigger: 'blur' },
    {
      type: 'number',
      min: 0.01,
      message: '发货金额必须大于0',
      trigger: 'blur',
    },
  ],
};

// 默认分页配置
export const DEFAULT_PAGE_CONFIG = {
  page: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
  pageSizeOptions: ['10', '20', '50', '100'],
};

// 表格列宽配置
export const COLUMN_WIDTHS = {
  shipment_no: 180,
  status: 150,
  contract_no: 160,
  supplier_name: 200,
  total_quantity: 120,
  total_amount: 140,
  is_complete: 120,
  shipment_notice: 200,
  creator_name: 100,
  created_at: 180,
  updated_at: 180,
  action: 200,
};

// 导出配置
export const EXPORT_CONFIG = {
  filename: '发货管理列表',
  sheetName: '发货记录',
  columns: [
    { key: 'shipment_no', title: '发货通知单号' },
    { key: 'contract_no', title: '关联合同' },
    { key: 'supplier_name', title: '供应商' },
    { key: 'total_quantity', title: '发货总数量' },
    { key: 'total_amount', title: '发货总金额' },
    { key: 'is_complete', title: '是否全部发货' },
    { key: 'status', title: '状态' },
    { key: 'shipment_notice', title: '发货通知' },
    { key: 'creator_name', title: '创建人' },
    { key: 'created_at', title: '创建时间' },
  ],
};
