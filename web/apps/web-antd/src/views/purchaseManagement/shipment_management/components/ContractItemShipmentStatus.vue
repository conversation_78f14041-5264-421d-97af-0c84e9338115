<script lang="ts" setup>
import type { PurchaseContractItem } from '#/api/core/purchase/purchase_contract';

import { computed, onMounted, ref } from 'vue';

import { Progress, Tooltip } from 'ant-design-vue';

import { getShipmentsByContract } from '#/api/core/purchase/shipment';

interface Props {
  contractItem: PurchaseContractItem;
  contractId?: number;
}

const props = defineProps<Props>();

// 发货统计数据
const shipmentStats = ref({
  shippedQuantity: 0,
  shippedAmount: 0,
  unshippedQuantity: 0,
  unshippedAmount: 0,
  shipmentProgress: 0,
});

const loading = ref(false);

// 计算属性
const statusColor = computed(() => {
  const progress = shipmentStats.value.shipmentProgress;
  if (progress === 0) return 'gray';
  if (progress < 50) return 'red';
  if (progress < 100) return 'orange';
  return 'green';
});

const statusText = computed(() => {
  const progress = shipmentStats.value.shipmentProgress;
  if (progress === 0) return '未发货';
  if (progress < 100) return '部分发货';
  return '已完成';
});

const statusIcon = computed(() => {
  const progress = shipmentStats.value.shipmentProgress;
  if (progress === 0) return '⏳';
  if (progress < 100) return '🚚';
  return '✅';
});

/**
 * 获取合同明细项的发货统计信息
 */
async function fetchShipmentStats() {
  if (!props.contractId || !props.contractItem.id) {
    return;
  }

  loading.value = true;
  try {
    // 获取该合同的所有发货记录
    const shipments = await getShipmentsByContract(props.contractId);

    let totalShippedQuantity = 0;
    let totalShippedAmount = 0;

    // 遍历所有已完成的发货记录
    for (const shipment of shipments) {
      if (shipment.status === 'completed' && shipment.items) {
        // 查找该明细项的发货记录
        const shipmentItem = shipment.items.find(
          (item) => item.contract_item_id === props.contractItem.id,
        );
        if (shipmentItem) {
          totalShippedQuantity += shipmentItem.current_shipment_quantity || 0;
          totalShippedAmount += shipmentItem.current_shipment_amount || 0;
        }
      }
    }

    // 计算未发货数量和金额
    const contractQuantity = props.contractItem.contract_quantity || 0;
    const contractAmount = props.contractItem.contract_amount || 0;
    const unshippedQuantity = Math.max(0, contractQuantity - totalShippedQuantity);
    const unshippedAmount = Math.max(0, contractAmount - totalShippedAmount);

    // 计算发货进度
    const shipmentProgress = contractAmount > 0 ? (totalShippedAmount / contractAmount) * 100 : 0;

    shipmentStats.value = {
      shippedQuantity: totalShippedQuantity,
      shippedAmount: totalShippedAmount,
      unshippedQuantity,
      unshippedAmount,
      shipmentProgress: Math.min(100, shipmentProgress),
    };
  } catch (error) {
    console.error('获取发货统计信息失败:', error);
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  fetchShipmentStats();
});
</script>

<template>
  <div class="space-y-2">
    <!-- 状态标签 -->
    <div class="flex items-center space-x-2">
      <span class="text-sm">{{ statusIcon }}</span>
      <span
        class="rounded-full px-2 py-1 text-xs font-medium"
        :class="{
          'bg-gray-100 text-gray-600': statusColor === 'gray',
          'bg-red-100 text-red-600': statusColor === 'red',
          'bg-orange-100 text-orange-600': statusColor === 'orange',
          'bg-green-100 text-green-600': statusColor === 'green',
        }"
      >
        {{ statusText }}
      </span>
    </div>

    <!-- 进度条 -->
    <div class="w-full">
      <Tooltip>
        <template #title>
          <div class="space-y-1 text-xs">
            <div>已发货数量: {{ shipmentStats.shippedQuantity }} {{ contractItem.unit || '' }}</div>
            <div>已发货金额: ¥{{ shipmentStats.shippedAmount.toLocaleString() }}</div>
            <div>未发货数量: {{ shipmentStats.unshippedQuantity }} {{ contractItem.unit || '' }}</div>
            <div>未发货金额: ¥{{ shipmentStats.unshippedAmount.toLocaleString() }}</div>
            <div>发货进度: {{ shipmentStats.shipmentProgress.toFixed(1) }}%</div>
          </div>
        </template>
        <Progress
          :percent="shipmentStats.shipmentProgress"
          :stroke-color="{
            '0%': statusColor === 'gray' ? '#9CA3AF' : 
                  statusColor === 'red' ? '#EF4444' :
                  statusColor === 'orange' ? '#F59E0B' : '#10B981',
            '100%': statusColor === 'gray' ? '#6B7280' : 
                    statusColor === 'red' ? '#DC2626' :
                    statusColor === 'orange' ? '#D97706' : '#059669',
          }"
          :show-info="false"
          size="small"
          class="w-20"
        />
      </Tooltip>
    </div>

    <!-- 数量统计 -->
    <div class="text-xs text-gray-500">
      <div>已发: {{ shipmentStats.shippedQuantity }}/{{ contractItem.contract_quantity || 0 }}</div>
      <div>金额: ¥{{ shipmentStats.shippedAmount.toLocaleString() }}</div>
    </div>
  </div>
</template>