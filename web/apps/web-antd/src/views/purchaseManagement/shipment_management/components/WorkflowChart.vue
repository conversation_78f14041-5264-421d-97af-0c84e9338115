<script lang="ts" setup>
import { computed } from 'vue';

import { Background } from '@vue-flow/background';
import { Controls } from '@vue-flow/controls';
import { useVueFlow, VueFlow } from '@vue-flow/core';

// 导入 Vue Flow 样式
import '@vue-flow/core/dist/style.css';
import '@vue-flow/core/dist/theme-default.css';
import '@vue-flow/controls/dist/style.css';

interface Props {
  currentStatus: string;
  approvalHistory?: any[]; // 操作历史记录
}

const props = withDefaults(defineProps<Props>(), {
  currentStatus: 'draft',
  approvalHistory: () => [],
});

// 状态映射 - 定义每个状态对应的节点状态
const getNodeStatus = (nodeId: string, currentStatus: string) => {
  // 定义流程步骤顺序
  const flowSteps = ['draft', 'purchase_review', 'finance_review', 'warehouse_review', 'completed'];

  // 特殊状态处理
  if (currentStatus === 'cancelled') {
    // 如果流程被取消，显示到取消前的最后一个状态为完成，其余为待处理
    const cancelIndex = flowSteps.indexOf('draft'); // 从草稿开始都可能被取消
    const nodeIndex = flowSteps.indexOf(nodeId);
    
    if (nodeIndex <= cancelIndex) return 'completed';
    return 'cancelled';
  }

  // 正常流程状态判断
  const currentIndex = flowSteps.indexOf(currentStatus);
  const nodeIndex = flowSteps.indexOf(nodeId);

  if (currentIndex === -1 || nodeIndex === -1) {
    return nodeId === currentStatus ? 'active' : 'pending';
  }

  if (nodeIndex < currentIndex) return 'completed';
  if (nodeIndex === currentIndex) return 'active';
  return 'pending';
};

// 发货管理流程节点定义
const nodes = computed(() => [
  {
    id: 'draft',
    type: 'custom',
    position: { x: 400, y: 50 },
    data: {
      label: '📝 创建',
      description: '发货记录创建',
      status: getNodeStatus('draft', props.currentStatus),
      category: 'start',
    },
  },
  {
    id: 'purchase_review',
    type: 'custom',
    position: { x: 400, y: 180 },
    data: {
      label: '🛒 采购负责人审批',
      description: '采购负责人审批',
      status: getNodeStatus('purchase_review', props.currentStatus),
      category: 'approval',
    },
  },
  {
    id: 'finance_review',
    type: 'custom',
    position: { x: 400, y: 310 },
    data: {
      label: '💰 财务负责人审批',
      description: '财务负责人审批',
      status: getNodeStatus('finance_review', props.currentStatus),
      category: 'approval',
    },
  },
  {
    id: 'warehouse_review',
    type: 'custom',
    position: { x: 400, y: 440 },
    data: {
      label: '📦 仓库管理员审批',
      description: '仓库管理员审批',
      status: getNodeStatus('warehouse_review', props.currentStatus),
      category: 'approval',
    },
  },
  {
    id: 'completed',
    type: 'custom',
    position: { x: 400, y: 570 },
    data: {
      label: '🎉 已完成',
      description: '流程完成',
      status: getNodeStatus('completed', props.currentStatus),
      category: 'end',
    },
  },
]);

// 流程连接线定义
const edges = computed(() => {
  const baseEdges = [
    // 主流程线
    {
      id: 'e1',
      source: 'draft',
      target: 'purchase_review',
      animated: props.currentStatus !== 'draft',
      style: { stroke: '#6b7280', strokeWidth: 2 },
    },
    {
      id: 'e2',
      source: 'purchase_review',
      target: 'finance_review',
      animated: ['finance_review', 'warehouse_review', 'completed'].includes(props.currentStatus),
      style: { stroke: '#6b7280', strokeWidth: 2 },
    },
    {
      id: 'e3',
      source: 'finance_review',
      target: 'warehouse_review',
      animated: ['warehouse_review', 'completed'].includes(props.currentStatus),
      style: { stroke: '#6b7280', strokeWidth: 2 },
    },
    {
      id: 'e4',
      source: 'warehouse_review',
      target: 'completed',
      animated: props.currentStatus === 'completed',
      style: { stroke: '#6b7280', strokeWidth: 2 },
    },
  ];

  return baseEdges;
});

const { onInit } = useVueFlow();

// 初始化流程图
onInit((vueFlowInstance) => {
  vueFlowInstance.fitView();
});
</script>

<template>
  <div class="workflow-chart-container">
    <VueFlow
      :nodes="nodes"
      :edges="edges"
      :default-viewport="{ zoom: 0.8 }"
      :min-zoom="0.2"
      :max-zoom="2"
      fit-view-on-init
      class="workflow-chart"
    >
      <Background pattern-color="#e5e7eb" :gap="20" />
      <Controls />

      <!-- 自定义节点模板 -->
      <template #node-custom="{ data }">
        <div
          class="custom-node"
          :class="[
            `node-${data.status}`,
            `category-${data.category}`,
            {
              'node-completed': data.status === 'completed',
              'node-active': data.status === 'active',
              'node-pending': data.status === 'pending',
              'node-rejected': data.status === 'rejected',
              'node-cancelled': data.status === 'cancelled',
            },
          ]"
        >
          <div class="node-content">
            <div class="node-label">{{ data.label }}</div>
            <div class="node-description">{{ data.description }}</div>
          </div>
          <div class="node-status-indicator" :class="`status-${data.status}`">
            <span v-if="data.status === 'completed'" class="status-icon"
              >✓</span
            >
            <span v-else-if="data.status === 'active'" class="status-icon"
              >⏳</span
            >
            <span v-else-if="data.status === 'rejected'" class="status-icon"
              >✗</span
            >
            <span v-else-if="data.status === 'cancelled'" class="status-icon"
              >⊘</span
            >
          </div>
        </div>
      </template>
    </VueFlow>
  </div>
</template>

<style scoped>
@keyframes pulse {
  0%,
  100% {
    box-shadow: 0 4px 12px rgb(59 130 246 / 30%);
  }

  50% {
    box-shadow: 0 4px 20px rgb(59 130 246 / 50%);
  }
}

@keyframes pulse-indicator {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.2);
  }
}

@keyframes dashdraw {
  from {
    stroke-dashoffset: 10;
  }

  to {
    stroke-dashoffset: 0;
  }
}

.workflow-chart-container {
  width: 100%;
  height: 600px;
  overflow: hidden;
  background: #f8fafc;
  border-radius: 12px;
}

.workflow-chart {
  width: 100%;
  height: 100%;
}

.custom-node {
  position: relative;
  min-width: 200px;
  padding: 18px;
  overflow: hidden;
  cursor: pointer;
  background: white;
  border: 3px solid;
  border-radius: 16px;
  box-shadow: 0 6px 20px rgb(0 0 0 / 8%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.custom-node::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 4px;
  content: '';
  background: linear-gradient(
    90deg,
    transparent,
    rgb(255 255 255 / 80%),
    transparent
  );
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.custom-node:hover {
  box-shadow: 0 12px 32px rgb(0 0 0 / 12%);
  transform: translateY(-4px) scale(1.02);
}

.custom-node:hover::before {
  transform: translateX(100%);
}

/* 状态样式 */
.node-completed {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  border-color: #10b981;
  box-shadow: 0 6px 20px rgb(16 185 129 / 15%);
}

.node-active {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-color: #3b82f6;
  box-shadow: 0 6px 20px rgb(59 130 246 / 20%);
  animation: pulse 2s infinite;
}

.node-pending {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  border-color: #d1d5db;
  opacity: 0.6;
}

.node-rejected {
  background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
  border-color: #ef4444;
  box-shadow: 0 6px 20px rgb(239 68 68 / 15%);
}

.node-cancelled {
  background: linear-gradient(135deg, #f9fafb 0%, #e5e7eb 100%);
  border-color: #6b7280;
  opacity: 0.5;
}

/* 类别样式 */
.category-start {
  border-left: 6px solid #10b981;
}

.category-approval {
  border-left: 6px solid #8b5cf6;
}

.category-end {
  border-left: 6px solid #f59e0b;
}

.node-content {
  text-align: center;
}

.node-label {
  margin-bottom: 4px;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.node-description {
  font-size: 12px;
  color: #6b7280;
}

.node-status-indicator {
  position: absolute;
  top: -8px;
  right: -8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  font-size: 10px;
  font-weight: bold;
  border: 3px solid white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
}

.status-completed {
  color: white;
  background: linear-gradient(135deg, #10b981, #059669);
}

.status-active {
  color: white;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  animation: pulse-indicator 1.5s infinite;
}

.status-pending {
  color: white;
  background: linear-gradient(135deg, #9ca3af, #6b7280);
}

.status-rejected {
  color: white;
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.status-cancelled {
  color: white;
  background: linear-gradient(135deg, #6b7280, #4b5563);
}

.status-icon {
  font-size: 10px;
  line-height: 1;
}

/* Vue Flow 样式覆盖 */
:deep(.vue-flow__edge-path) {
  stroke: #6b7280;
  stroke-width: 2;
}

:deep(.vue-flow__edge.animated .vue-flow__edge-path) {
  stroke-dasharray: 5;
  animation: dashdraw 0.5s linear infinite;
}

:deep(.vue-flow__controls) {
  bottom: 20px;
  left: 20px;
}
</style>