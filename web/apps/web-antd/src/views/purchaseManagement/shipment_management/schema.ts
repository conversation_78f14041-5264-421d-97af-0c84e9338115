import type { VbenFormSchema } from '#/adapter/form';

import { z } from '#/adapter/form';
import { getPurchaseContractList } from '#/api/core/purchase/purchase_contract';
import { getSupplierListApi } from '#/api/core/purchase/supplier';

import {
  SHIPMENT_COMPLETE_OPTIONS,
  SHIPMENT_STATUS_OPTIONS,
} from './constants';

/**
 * 获取发货管理查询表单配置
 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入发货通知单号',
      },
      fieldName: 'shipment_no',
      label: '发货通知单号',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入合同编号',
      },
      fieldName: 'contract_no',
      label: '合同编号',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        showSearch: true,
        filterOption: true,
        api: async () => {
          const res = await getSupplierListApi({
            page: 1,
            pageSize: 100,
          });
          return res.list.map((item) => ({
            label: item.supplier_name,
            value: item.id,
          }));
        },
        placeholder: '请选择供应商',
      },
      fieldName: 'supplier_id',
      label: '供应商',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: SHIPMENT_STATUS_OPTIONS,
        placeholder: '请选择状态',
      },
      fieldName: 'status',
      label: '状态',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: SHIPMENT_COMPLETE_OPTIONS,
        placeholder: '请选择是否全部发货',
      },
      fieldName: 'is_complete',
      label: '是否全部发货',
    },
    {
      component: 'RangePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        placeholder: ['开始日期', '结束日期'],
      },
      fieldName: 'create_time',
      label: '创建时间',
    },
  ];
}

/**
 * 获取发货管理表单配置
 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      componentProps: {
        style: { width: '100%' },
        allowClear: true,
        showSearch: true,
        filterOption: (
          input: string,
          option: { label: string; value: number },
        ) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
        api: async () => {
          const res = await getPurchaseContractList({
            page: 1,
            pageSize: 100,
            status: 'completed',
            contract_type: 'sale',
          });
          return res.list.map((item) => ({
            label: `${item.contract_no} - ${item.contract_title}`,
            value: item.id,
          }));
        },
        placeholder: '请选择关联合同',
      },
      fieldName: 'contract_id',
      label: '关联合同',
      rules: 'selectRequired',
    },
    {
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DDT00:00:00Z',
        placeholder: '请选择预计到货时间',
        style: { width: '100%' },
      },
      fieldName: 'expected_arrival_date',
      label: '预计到货时间',
      rules: z.string().min(1, '预计到货时间不能为空'),
    },
    {
      component: 'Textarea',
      componentProps: {
        maxLength: 1000,
        placeholder: '请输入发货通知内容',
        rows: 2,
        showCount: true,
        style: { width: '100%' },
      },
      fieldName: 'shipment_notice',
      label: '发货通知',
      rules: z.string().min(1, '请输入发货通知内容'),
    },
    {
      component: 'Input',
      componentProps: {
        maxLength: 500,
        placeholder: '请输入物流信息',
        showCount: true,
        style: { width: '100%' },
      },
      fieldName: 'tracking_info',
      label: '物流信息',
      rules: z.string().min(1, '请输入物流信息'),
    },
    {
      component: 'Input',
      componentProps: {
        maxLength: 500,
        placeholder: '请输入收货地址',
        showCount: true,
        style: { width: '100%' },
      },
      fieldName: 'delivery_address',
      label: '收货地址',
      rules: z.string().min(1, '请输入收货地址'),
    },
    {
      component: 'Textarea',
      componentProps: {
        maxLength: 1000,
        placeholder: '请输入备注信息',
        rows: 2,
        showCount: true,
        style: { width: '100%' },
      },
      fieldName: 'remark',
      label: '备注',
    },
  ];
}

/**
 * 获取发货明细表单配置
 */
export function useItemFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择合同明细',
        showSearch: true,
        style: { width: '100%' },
      },
      fieldName: 'contract_item_id',
      label: '合同明细',
      rules: 'selectRequired',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        placeholder: '请输入本次发货数量',
        precision: 0,
        style: { width: '100%' },
      },
      fieldName: 'current_shipment_quantity',
      label: '本次发货数量',
      rules: z.number().min(0, '发货数量不能小于0'),
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0.01,
        placeholder: '请输入本次发货金额',
        precision: 2,
        style: { width: '100%' },
      },
      fieldName: 'current_shipment_amount',
      label: '本次发货金额',
      rules: z.number().min(0.01, '发货金额必须大于0'),
    },

    {
      component: 'Textarea',
      componentProps: {
        maxLength: 500,
        placeholder: '请输入备注信息',
        rows: 2,
        showCount: true,
        style: { width: '100%' },
      },
      fieldName: 'remark',
      label: '备注',
    },
  ];
}

/**
 * 获取审批表单配置
 */
export function useApprovalFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '批准', value: 'approve' },
          { label: '拒绝', value: 'reject' },
        ],
        style: { width: '100%' },
      },
      fieldName: 'action',
      label: '审批结果',
      rules: 'selectRequired',
    },
    {
      component: 'Textarea',
      componentProps: {
        maxLength: 500,
        placeholder: '请输入审批意见',
        rows: 4,
        showCount: true,
        style: { width: '100%' },
      },
      fieldName: 'comments',
      label: '审批意见',
    },
  ];
}

/**
 * 获取回退表单配置
 */
export function useRollbackFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Textarea',
      componentProps: {
        maxLength: 500,
        placeholder: '请输入回退原因',
        rows: 4,
        showCount: true,
        style: { width: '100%' },
      },
      fieldName: 'comments',
      label: '回退原因',
      rules: z.string().min(1, '请输入回退原因'),
    },
  ];
}
