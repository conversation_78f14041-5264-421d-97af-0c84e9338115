<script lang="ts" setup>
import type {
  ContractApprovalHistory,
  ContractApprovalParams,
  ContractRollbackParams,
  PurchaseContract,
} from '#/api/core/purchase/purchase_contract';
import type { ModuleFileListResult } from '#/api/core/upload';

import { computed, nextTick, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { AccessControl } from '@vben/access';
import { Page } from '@vben/common-ui';
import { ArrowLeft, FileText, Tag as TagIcon, User } from '@vben/icons';

import {
  BackTop,
  Button,
  Image,
  message,
  Modal,
  Select,
  Spin,
  Step,
  Steps,
  Tag,
  Textarea,
  Timeline,
  TimelineItem,
  Upload,
} from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  approvePurchaseContract,
  getPurchaseContractById,
  getPurchaseContractHistory,
  rollbackPurchaseContract,
} from '#/api/core/purchase/purchase_contract';
import { getModuleFiles, uploadFile } from '#/api/core/upload';
import { handleDownloadFile, handlePreviewFile } from '#/utils/common/download';
import { formatToDateTime } from '#/utils/common/time';

import { ACTION_CONFIG, FLOW_STATUS_CONFIG } from './constants';
import { useItemColumns } from './data';

const route = useRoute();
const router = useRouter();

const loading = ref(false);
const workflowLoading = ref(false);
const purchaseContract = ref<PurchaseContract>();
const approvalHistory = ref<ContractApprovalHistory[]>([]);
const approvalComments = ref('');
const rollbackComments = ref('');
const isApprovalModalVisible = ref(false);
const isRollbackModalVisible = ref(false);
const currentAction = ref<'approve' | 'reject'>('approve');
const fileList = ref<any[]>([]);
const uploadLoading = ref(false);
const uploadError = ref('');
// 添加回退目标环节的选择
const rollbackTarget = ref('');
const availableRollbackTargets = ref<{ label: string; value: string }[]>([]);

// 可能的回退目标环节
const STAGE_OPTIONS = [
  { label: '采购审批', value: 'purchase_review' },
  { label: '财务审批', value: 'finance_review' },
  { label: '法务审批', value: 'legal_review' },
  { label: '企业审批', value: 'enterprise_review' },
  { label: '内部签章', value: 'internal_sign' },
  { label: '双方签章', value: 'double_sign' },
];

const [ItemGrid, itemGridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useItemColumns(),
    height: 'auto',
    pagerConfig: {
      enabled: false,
    },
    proxyConfig: {
      autoLoad: false,
      ajax: {
        query: async () => {
          return {
            items: purchaseContract.value?.items || [],
            total: purchaseContract.value?.items?.length || 0,
          };
        },
      },
    },
    toolbarConfig: {
      enabled: false,
    },
  },
});

// 附件相关变量
const attachmentsLoading = ref(false);
const attachments = ref<ModuleFileListResult[]>([]);
const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');

// 按description分组的附件
const groupedAttachments = computed(() => {
  const grouped: Record<string, ModuleFileListResult[]> = {};

  attachments.value.forEach((file) => {
    const key = file.description || '其他附件';
    if (!grouped[key]) {
      grouped[key] = [];
    }
    grouped[key].push(file);
  });

  return grouped;
});

// 获取合同ID
const contractId = computed(() => {
  return Number(route.params.id);
});

// 工作流步骤
const workflowSteps = computed(() => {
  const status = purchaseContract.value?.status || 'draft';

  const steps = [
    { title: '创建合同', status: 'finish', description: '合同已创建' },
    { title: '采购审批', status: 'wait', description: '采购负责人审批' },
    { title: '财务审批', status: 'wait', description: '财务负责人审批' },
    { title: '法务审批', status: 'wait', description: '法务负责人审批' },
    { title: '企业审批', status: 'wait', description: '企业负责人审批' },
    { title: '内部签章', status: 'wait', description: '内部签章' },
    { title: '双方签章', status: 'wait', description: '双方签章' },
    { title: '合同生效', status: 'wait', description: '合同审批完成' },
  ];

  const statusOrder = [
    'draft',
    'purchase_review',
    'finance_review',
    'legal_review',
    'enterprise_review',
    'internal_sign',
    'double_sign',
    'completed',
  ];
  const currentIndex = statusOrder.indexOf(status);

  steps.forEach((step, index) => {
    if (index < currentIndex) {
      step.status = 'finish';
    } else if (index === currentIndex) {
      step.status = status === 'approved' ? 'finish' : 'process';
    } else {
      step.status = 'wait';
    }

    if (['cancelled', 'rejected'].includes(status) && index <= currentIndex) {
      step.status = 'error';
    }
  });

  return steps;
});

// 权限检查
const canApprove = computed(() => {
  return (
    purchaseContract.value?.status === 'purchase_review' ||
    purchaseContract.value?.status === 'finance_review' ||
    purchaseContract.value?.status === 'enterprise_review' ||
    purchaseContract.value?.status === 'legal_review' ||
    purchaseContract.value?.status === 'internal_sign' ||
    purchaseContract.value?.status === 'double_sign'
  );
});

const canRollback = computed(() => {
  // 除了draft（第一个环节）、purchase_review（采购审批）和completed（完成阶段）外，所有环节都可以回退
  return (
    purchaseContract.value?.status &&
    purchaseContract.value.status !== 'draft' &&
    purchaseContract.value.status !== 'purchase_review' &&
    purchaseContract.value.status !== 'cancelled' &&
    purchaseContract.value.status !== 'rejected' &&
    purchaseContract.value.status !== 'completed'
  );
});

// 获取当前阶段的权限码
const getPermissionCodes = computed(() => {
  const status = purchaseContract.value?.status;

  switch (status) {
    case 'double_sign': {
      return {
        approve: ['Purchase:PurchaseContract:DoubleSign'],
        reject: ['Purchase:PurchaseContract:DoubleSign'],
        rollback: ['Purchase:PurchaseContract:DoubleSign'],
      };
    }
    case 'enterprise_review': {
      return {
        approve: ['Purchase:PurchaseContract:EnterpriseManagerReview'],
        reject: ['Purchase:PurchaseContract:EnterpriseManagerReview'],
        rollback: ['Purchase:PurchaseContract:EnterpriseManagerReview'],
      };
    }
    case 'finance_review': {
      return {
        approve: ['Purchase:PurchaseContract:FinanceManagerReview'],
        reject: ['Purchase:PurchaseContract:FinanceManagerReview'],
        rollback: ['Purchase:PurchaseContract:FinanceManagerReview'],
      };
    }
    case 'internal_sign': {
      return {
        approve: ['Purchase:PurchaseContract:InternalSign'],
        reject: ['Purchase:PurchaseContract:InternalSign'],
        rollback: ['Purchase:PurchaseContract:InternalSign'],
      };
    }
    case 'legal_review': {
      return {
        approve: ['Purchase:PurchaseContract:LegalManagerReview'],
        reject: ['Purchase:PurchaseContract:LegalManagerReview'],
        rollback: ['Purchase:PurchaseContract:LegalManagerReview'],
      };
    }
    case 'purchase_review': {
      return {
        approve: ['Purchase:PurchaseContract:PurchaseManagerReview'],
        reject: ['Purchase:PurchaseContract:PurchaseManagerReview'],
        rollback: [], // 采购审批阶段不能回退
      };
    }
    default: {
      return {
        approve: [],
        reject: [],
        rollback: [],
      };
    }
  }
});

// 获取合同详情
async function fetchContractDetail() {
  try {
    loading.value = true;
    const response = await getPurchaseContractById(contractId.value);
    purchaseContract.value = response;

    // 加载数据后更新表格数据
    if (purchaseContract.value?.items) {
      await nextTick();
      if (itemGridApi && itemGridApi.grid) {
        await itemGridApi.grid.commitProxy('query');
      }
    }
  } catch {
    message.error('获取合同详情失败');
  } finally {
    loading.value = false;
  }
}

// 获取审批历史
async function fetchApprovalHistory() {
  try {
    workflowLoading.value = true;
    const response = await getPurchaseContractHistory(contractId.value);
    approvalHistory.value = Array.isArray(response) ? response : [];
  } catch {
    message.error('获取审批历史失败');
    approvalHistory.value = [];
  } finally {
    workflowLoading.value = false;
  }
}

// 获取附件列表
async function fetchAttachments() {
  if (!contractId.value) return;

  try {
    attachmentsLoading.value = true;
    const files = await getModuleFiles('purchase_contracts', contractId.value);
    attachments.value = Array.isArray(files) ? files : [];
  } catch {
    message.error('获取附件列表失败');
    attachments.value = [];
  } finally {
    attachmentsLoading.value = false;
  }
}

// 处理文件预览
function handlePreview(file: ModuleFileListResult) {
  // 使用通用预览函数
  handlePreviewFile(file, {
    onImagePreview: (imageUrl, title) => {
      previewImage.value = imageUrl;
      previewTitle.value = title;
      previewVisible.value = true;
    },
  });
}

// 处理文件下载
async function handleDownload(file: ModuleFileListResult) {
  // 使用通用下载函数
  await handleDownloadFile(file);
}

// 处理审批
function handleApproval(action: 'approve' | 'reject') {
  currentAction.value = action;
  approvalComments.value = '';
  uploadError.value = '';
  fileList.value = [];
  isApprovalModalVisible.value = true;
}

// 确认审批
async function confirmApproval() {
  if (!approvalComments.value.trim()) {
    message.error('请输入审批意见');
    return;
  }

  // 验证内部签章和双方签章状态下必须上传文件
  if (
    currentAction.value === 'approve' &&
    (purchaseContract.value?.status === 'internal_sign' ||
      purchaseContract.value?.status === 'double_sign') &&
    fileList.value.length === 0
  ) {
    uploadError.value = `${purchaseContract.value?.status === 'internal_sign' ? '内部签章' : '双方签章'}环节必须上传至少一个签章文件`;
    message.error(uploadError.value);
    return;
  }

  try {
    // 如果有上传文件，先上传文件
    if (fileList.value.length > 0) {
      uploadLoading.value = true;

      for (const file of fileList.value) {
        if (file.originFileObj) {
          await uploadFile(
            file.originFileObj,
            'purchase_contracts',
            contractId.value,
            `${purchaseContract.value?.status === 'internal_sign' ? '内部签章' : '双方签章'}文件`,
          );
        }
      }
      uploadLoading.value = false;

      // 上传成功后刷新附件列表
      await fetchAttachments();
    }

    const params: ContractApprovalParams = {
      contract_id: contractId.value,
      action: currentAction.value,
      comments: approvalComments.value,
      current_stage: purchaseContract.value?.status || '',
    };

    await approvePurchaseContract(params);
    message.success(
      currentAction.value === 'approve' ? '审批通过' : '审批拒绝',
    );
    isApprovalModalVisible.value = false;

    // 添加延迟确保后端状态更新
    await new Promise((resolve) => setTimeout(resolve, 800));

    // 刷新数据
    let retries = 0;
    const maxRetries = 3;

    const refreshData = async () => {
      try {
        await Promise.all([fetchContractDetail(), fetchApprovalHistory()]);
      } catch {
        if (retries < maxRetries) {
          retries++;
          await new Promise((resolve) => setTimeout(resolve, 500));
          await refreshData();
        } else {
          message.warning('数据刷新失败，请手动刷新页面');
        }
      }
    };

    await refreshData();
  } catch {
    message.error('审批失败');
    uploadLoading.value = false;
  }
}

// 处理文件上传变更
function handleFileChange(info: any) {
  fileList.value = info.fileList;

  if (info.file.status === 'done') {
    message.success(`${info.file.name} 文件上传成功`);
    uploadError.value = '';
  } else if (info.file.status === 'error') {
    message.error(`${info.file.name} 文件上传失败`);
  }
}

// 文件上传前验证
function beforeUpload(file: any) {
  const isValidType = /\.(?:pdf|doc|docx|jpg|jpeg|png)$/i.test(file.name);
  const isLt10M = file.size / 1024 / 1024 < 10;

  if (!isValidType) {
    message.error('只允许上传PDF、Word文档或图片文件!');
    return false;
  }

  if (!isLt10M) {
    message.error('文件必须小于10MB!');
    return false;
  }

  return isValidType && isLt10M;
}

// 处理回退
function handleRollback() {
  rollbackComments.value = '';
  rollbackTarget.value = '';

  // 根据当前状态，确定可回退的目标环节
  const statusOrder = [
    'draft',
    'purchase_review',
    'finance_review',
    'legal_review',
    'enterprise_review',
    'internal_sign',
    'double_sign',
    'completed',
  ];

  const currentStatus = purchaseContract.value?.status || '';
  const currentIndex = statusOrder.indexOf(currentStatus);

  // 重置可用目标
  availableRollbackTargets.value = [];
  rollbackTarget.value = '';

  // 找出当前状态之前的所有可回退状态
  STAGE_OPTIONS.forEach((option) => {
    const optionIndex = statusOrder.indexOf(option.value);
    // 只有在当前阶段之前的阶段才能作为回退目标
    if (optionIndex > 0 && optionIndex < currentIndex) {
      availableRollbackTargets.value.push(option);
    }
  });

  // 如果有可回退的目标，默认选择第一个
  if (availableRollbackTargets.value.length > 0) {
    rollbackTarget.value = availableRollbackTargets.value[0]?.value || '';
  }

  isRollbackModalVisible.value = true;
}

// 确认回退
async function confirmRollback() {
  if (!rollbackComments.value.trim()) {
    message.error('请输入回退原因');
    return;
  }

  if (!rollbackTarget.value && availableRollbackTargets.value.length > 0) {
    message.error('请选择回退目标环节');
    return;
  }

  try {
    let contractId = 0;
    if (route.params.id) {
      contractId = Number(route.params.id);
    }

    // 使用默认回退目标
    const currentStage = purchaseContract.value?.status || '';
    // 如果没有选择回退目标，默认使用finance_review
    const targetStage = rollbackTarget.value || 'finance_review';

    const params: ContractRollbackParams = {
      contract_id: contractId,
      rollback_to: targetStage,
      current_stage: currentStage,
      comments: rollbackComments.value,
    };

    await rollbackPurchaseContract(params);
    message.success('回退成功');
    isRollbackModalVisible.value = false;

    // 添加延迟确保后端状态更新
    await new Promise((resolve) => setTimeout(resolve, 800));

    // 刷新数据
    let retries = 0;
    const maxRetries = 3;

    const refreshData = async () => {
      try {
        await Promise.all([fetchContractDetail(), fetchApprovalHistory()]);
      } catch {
        if (retries < maxRetries) {
          retries++;
          await new Promise((resolve) => setTimeout(resolve, 500));
          await refreshData();
        } else {
          message.warning('数据刷新失败，请手动刷新页面');
        }
      }
    };

    await refreshData();
  } catch {
    message.error('回退失败');
  }
}

// 返回列表
function goBack() {
  router.push('/purchase-management/purchase-contract');
}

// 页面初始化
onMounted(async () => {
  await Promise.all([
    fetchContractDetail(),
    fetchApprovalHistory(),
    fetchAttachments(),
  ]);

  // 确保表格数据已加载
  if (itemGridApi && itemGridApi.grid && purchaseContract.value?.items) {
    await nextTick();
    await itemGridApi.grid.commitProxy('query');
  }
});
</script>

<template>
  <Page auto-content-height class="bg-gray-50/50">
    <div class="mx-auto max-w-full space-y-6 px-4 py-4">
      <!-- 头部操作区 -->
      <div
        class="relative overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm transition-shadow duration-300 hover:shadow-md"
      >
        <div
          class="absolute -right-20 -top-20 h-48 w-48 rounded-full bg-blue-500/5"
        ></div>
        <div
          class="absolute -right-10 -top-10 h-32 w-32 rounded-full bg-indigo-500/10"
        ></div>
        <div class="px-6 py-5">
          <div class="flex flex-wrap items-center justify-between gap-4">
            <div class="flex items-center space-x-6">
              <Button
                type="primary"
                size="large"
                class="flex items-center justify-center rounded-xl !bg-gradient-to-r !from-blue-500 !to-indigo-600 !px-5 !py-3 shadow-sm transition-all duration-300 hover:scale-105 hover:!from-blue-600 hover:!to-indigo-700 hover:shadow-md"
                @click="goBack"
              >
                <div class="flex items-center justify-center">
                  <ArrowLeft class="mr-2 size-5 text-white" />
                  <span class="font-medium text-white">返回列表</span>
                </div>
              </Button>
              <div class="border-l-2 border-indigo-100 pl-6">
                <div class="flex items-center">
                  <h1
                    class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-2xl font-bold text-transparent"
                  >
                    合同详情
                  </h1>
                  <Tag
                    v-if="purchaseContract?.status"
                    :color="
                      FLOW_STATUS_CONFIG[purchaseContract.status as string]
                        ?.color
                    "
                    class="ml-3 !rounded-full !px-3 !py-0.5 !text-xs !font-medium shadow-sm"
                  >
                    {{
                      FLOW_STATUS_CONFIG[purchaseContract.status as string]
                        ?.text || purchaseContract.status
                    }}
                  </Tag>
                </div>
                <div
                  v-if="purchaseContract"
                  class="mt-2 flex items-center gap-4"
                >
                  <div class="flex items-center">
                    <div class="mr-2 h-2 w-2 rounded-full bg-blue-500"></div>
                    <span class="text-sm font-medium text-gray-600"
                      >合同编号:
                    </span>
                    <span class="ml-1 text-sm font-bold text-gray-800">{{
                      purchaseContract.contract_no
                    }}</span>
                  </div>
                  <div
                    v-if="purchaseContract.signing_date"
                    class="flex items-center"
                  >
                    <div class="mr-2 h-2 w-2 rounded-full bg-emerald-500"></div>
                    <span class="text-sm font-medium text-gray-600"
                      >签订日期:
                    </span>
                    <span class="ml-1 text-sm font-bold text-gray-800">
                      {{
                        new Date(
                          purchaseContract.signing_date,
                        ).toLocaleDateString()
                      }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div class="flex space-x-3">
              <AccessControl
                v-if="canApprove"
                :codes="getPermissionCodes.approve"
                type="code"
              >
                <Button
                  type="primary"
                  size="large"
                  class="!h-auto !rounded-xl !border-0 !bg-gradient-to-r !from-green-500 !to-emerald-600 !px-8 !py-2.5 !font-medium shadow-md transition-all hover:scale-105 hover:!from-green-600 hover:!to-emerald-700 hover:shadow-lg"
                  @click="handleApproval('approve')"
                >
                  <span class="text-base text-white">审批通过</span>
                </Button>
              </AccessControl>
              <AccessControl
                v-if="canApprove"
                :codes="getPermissionCodes.reject"
                type="code"
              >
                <Button
                  danger
                  size="large"
                  class="!h-auto !rounded-xl !border-0 !bg-gradient-to-r !from-red-500 !to-rose-600 !px-8 !py-2.5 !font-medium shadow-md transition-all hover:scale-105 hover:!from-red-600 hover:!to-rose-700 hover:shadow-lg"
                  @click="handleApproval('reject')"
                >
                  <span class="text-base text-white">审批拒绝</span>
                </Button>
              </AccessControl>
              <AccessControl
                v-if="canRollback"
                :codes="getPermissionCodes.rollback"
                type="code"
              >
                <Button
                  size="large"
                  class="!h-auto !rounded-xl !border-0 !bg-gradient-to-r !from-amber-500 !to-orange-600 !px-8 !py-2.5 !font-medium shadow-md transition-all hover:scale-105 hover:!from-amber-600 hover:!to-orange-700 hover:shadow-lg"
                  @click="handleRollback"
                >
                  <span class="text-base text-white">回退</span>
                </Button>
              </AccessControl>
            </div>
          </div>
        </div>
      </div>

      <Spin :spinning="loading">
        <!-- 基本信息和合同进度并排 -->
        <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
          <!-- 基本信息 -->
          <div class="lg:col-span-2">
            <div
              class="overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm"
            >
              <div
                class="border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4"
              >
                <h2
                  class="flex items-center text-lg font-semibold text-gray-900"
                >
                  <div class="mr-3 h-6 w-1 rounded-full bg-blue-500"></div>
                  基本信息
                </h2>
              </div>
              <div class="p-6">
                <!-- 基本信息部分修改 -->
                <div
                  v-if="purchaseContract"
                  class="grid grid-cols-1 gap-6 md:grid-cols-3"
                >
                  <!-- 合同基本信息 -->
                  <div
                    class="group relative overflow-hidden rounded-xl border border-blue-200/50 bg-gradient-to-br from-blue-50 to-blue-100/30 p-5 shadow-sm transition-all duration-300 hover:border-blue-300/70 hover:shadow-md"
                  >
                    <div
                      class="absolute -right-6 -top-6 h-16 w-16 rounded-full bg-blue-500/10"
                    ></div>
                    <div
                      class="absolute -right-2 -top-2 h-8 w-8 rounded-full bg-blue-500/20"
                    ></div>
                    <div class="flex items-center gap-4">
                      <div
                        class="flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 p-3 shadow-md shadow-blue-200 transition-transform duration-300 group-hover:scale-110"
                      >
                        <TagIcon class="size-7 text-white" />
                      </div>
                      <div class="flex-1">
                        <div
                          class="mb-1.5 text-xs font-semibold uppercase tracking-wider text-blue-700 opacity-80"
                        >
                          合同编号
                        </div>
                        <div class="text-base font-bold text-blue-800">
                          {{ purchaseContract.contract_no || '未生成' }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="group relative overflow-hidden rounded-xl border border-cyan-200/50 bg-gradient-to-br from-cyan-50 to-cyan-100/30 p-5 shadow-sm transition-all duration-300 hover:border-cyan-300/70 hover:shadow-md"
                  >
                    <div
                      class="absolute -right-6 -top-6 h-16 w-16 rounded-full bg-cyan-500/10"
                    ></div>
                    <div
                      class="absolute -right-2 -top-2 h-8 w-8 rounded-full bg-cyan-500/20"
                    ></div>
                    <div class="flex items-center gap-4">
                      <div
                        class="flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-cyan-500 to-cyan-600 p-3 shadow-md shadow-cyan-200 transition-transform duration-300 group-hover:scale-110"
                      >
                        <FileText class="size-7 text-white" />
                      </div>
                      <div class="flex-1">
                        <div
                          class="mb-1.5 text-xs font-semibold uppercase tracking-wider text-cyan-700 opacity-80"
                        >
                          合同标题
                        </div>
                        <div class="truncate text-base font-bold text-cyan-800">
                          {{ purchaseContract.contract_title || '-' }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="group relative overflow-hidden rounded-xl border border-purple-200/50 bg-gradient-to-br from-purple-50 to-purple-100/30 p-5 shadow-sm transition-all duration-300 hover:border-purple-300/70 hover:shadow-md"
                  >
                    <div
                      class="absolute -right-6 -top-6 h-16 w-16 rounded-full bg-purple-500/10"
                    ></div>
                    <div
                      class="absolute -right-2 -top-2 h-8 w-8 rounded-full bg-purple-500/20"
                    ></div>
                    <div class="flex items-center gap-4">
                      <div
                        class="flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 p-3 shadow-md shadow-purple-200 transition-transform duration-300 group-hover:scale-110"
                      >
                        <TagIcon class="size-7 text-white" />
                      </div>
                      <div class="flex-1">
                        <div
                          class="mb-1.5 text-xs font-semibold uppercase tracking-wider text-purple-700 opacity-80"
                        >
                          工作流状态
                        </div>
                        <Tag
                          :color="
                            FLOW_STATUS_CONFIG[
                              purchaseContract.status as string
                            ]?.color
                          "
                          class="!rounded-md !px-3 !py-1 !text-sm !font-semibold shadow-sm"
                        >
                          {{
                            FLOW_STATUS_CONFIG[
                              purchaseContract.status as string
                            ]?.text || purchaseContract.status
                          }}
                        </Tag>
                      </div>
                    </div>
                  </div>

                  <!-- 合同方信息 -->
                  <div
                    class="group relative overflow-hidden rounded-xl border border-green-200/50 bg-gradient-to-br from-green-50 to-green-100/30 p-5 shadow-sm transition-all duration-300 hover:border-green-300/70 hover:shadow-md"
                  >
                    <div
                      class="absolute -right-6 -top-6 h-16 w-16 rounded-full bg-green-500/10"
                    ></div>
                    <div
                      class="absolute -right-2 -top-2 h-8 w-8 rounded-full bg-green-500/20"
                    ></div>
                    <div class="flex items-center gap-4">
                      <div
                        class="flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-green-500 to-green-600 p-3 shadow-md shadow-green-200 transition-transform duration-300 group-hover:scale-110"
                      >
                        <User class="size-7 text-white" />
                      </div>
                      <div class="flex-1">
                        <div
                          class="mb-1.5 text-xs font-semibold uppercase tracking-wider text-green-700 opacity-80"
                        >
                          供应商
                        </div>
                        <div class="text-base font-bold text-green-800">
                          {{ purchaseContract.supplier_name || '未指定' }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="group relative overflow-hidden rounded-xl border border-indigo-200/50 bg-gradient-to-br from-indigo-50 to-indigo-100/30 p-5 shadow-sm transition-all duration-300 hover:border-indigo-300/70 hover:shadow-md"
                  >
                    <div
                      class="absolute -right-6 -top-6 h-16 w-16 rounded-full bg-indigo-500/10"
                    ></div>
                    <div
                      class="absolute -right-2 -top-2 h-8 w-8 rounded-full bg-indigo-500/20"
                    ></div>
                    <div class="flex items-center gap-4">
                      <div
                        class="flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-indigo-500 to-indigo-600 p-3 shadow-md shadow-indigo-200 transition-transform duration-300 group-hover:scale-110"
                      >
                        <FileText class="size-7 text-white" />
                      </div>
                      <div class="flex-1">
                        <div
                          class="mb-1.5 text-xs font-semibold uppercase tracking-wider text-indigo-700 opacity-80"
                        >
                          我方公司
                        </div>
                        <div
                          class="truncate text-base font-bold text-indigo-800"
                        >
                          {{ purchaseContract.our_company_name || '未指定' }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="group relative overflow-hidden rounded-xl border border-gray-200/60 bg-gradient-to-br from-gray-50 to-gray-100/30 p-5 shadow-sm transition-all duration-300 hover:border-gray-300/70 hover:shadow-md"
                  >
                    <div
                      class="absolute -right-6 -top-6 h-16 w-16 rounded-full bg-gray-500/10"
                    ></div>
                    <div
                      class="absolute -right-2 -top-2 h-8 w-8 rounded-full bg-gray-500/20"
                    ></div>
                    <div class="flex items-center gap-4">
                      <div
                        class="flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-gray-500 to-gray-600 p-3 shadow-md shadow-gray-200 transition-transform duration-300 group-hover:scale-110"
                      >
                        <User class="size-7 text-white" />
                      </div>
                      <div class="flex-1">
                        <div
                          class="mb-1.5 text-xs font-semibold uppercase tracking-wider text-gray-700 opacity-80"
                        >
                          创建人
                        </div>
                        <div class="text-base font-bold text-gray-800">
                          {{ purchaseContract.creator_name || '未知' }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 合同条款信息 -->
                  <div
                    class="group relative overflow-hidden rounded-xl border border-orange-200/50 bg-gradient-to-br from-orange-50 to-orange-100/30 p-5 shadow-sm transition-all duration-300 hover:border-orange-300/70 hover:shadow-md"
                  >
                    <div
                      class="absolute -right-6 -top-6 h-16 w-16 rounded-full bg-orange-500/10"
                    ></div>
                    <div
                      class="absolute -right-2 -top-2 h-8 w-8 rounded-full bg-orange-500/20"
                    ></div>
                    <div class="flex items-center gap-4">
                      <div
                        class="flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-orange-500 to-orange-600 p-3 shadow-md shadow-orange-200 transition-transform duration-300 group-hover:scale-110"
                      >
                        <TagIcon class="size-7 text-white" />
                      </div>
                      <div class="flex-1">
                        <div
                          class="mb-1.5 text-xs font-semibold uppercase tracking-wider text-orange-700 opacity-80"
                        >
                          合同金额
                        </div>
                        <div class="text-lg font-bold text-orange-800">
                          ¥{{
                            purchaseContract.total_amount?.toLocaleString() ||
                            '0'
                          }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="group relative overflow-hidden rounded-xl border border-emerald-200/50 bg-gradient-to-br from-emerald-50 to-emerald-100/30 p-5 shadow-sm transition-all duration-300 hover:border-emerald-300/70 hover:shadow-md"
                  >
                    <div
                      class="absolute -right-6 -top-6 h-16 w-16 rounded-full bg-emerald-500/10"
                    ></div>
                    <div
                      class="absolute -right-2 -top-2 h-8 w-8 rounded-full bg-emerald-500/20"
                    ></div>
                    <div class="flex items-center gap-4">
                      <div
                        class="flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-emerald-600 p-3 shadow-md shadow-emerald-200 transition-transform duration-300 group-hover:scale-110"
                      >
                        <TagIcon class="size-7 text-white" />
                      </div>
                      <div class="flex-1">
                        <div
                          class="mb-1.5 text-xs font-semibold uppercase tracking-wider text-emerald-700 opacity-80"
                        >
                          签订日期
                        </div>
                        <div class="text-base font-bold text-emerald-800">
                          {{
                            purchaseContract.signing_date
                              ? new Date(
                                  purchaseContract.signing_date,
                                ).toLocaleDateString()
                              : '-'
                          }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="group relative overflow-hidden rounded-xl border border-rose-200/50 bg-gradient-to-br from-rose-50 to-rose-100/30 p-5 shadow-sm transition-all duration-300 hover:border-rose-300/70 hover:shadow-md"
                  >
                    <div
                      class="absolute -right-6 -top-6 h-16 w-16 rounded-full bg-rose-500/10"
                    ></div>
                    <div
                      class="absolute -right-2 -top-2 h-8 w-8 rounded-full bg-rose-500/20"
                    ></div>
                    <div class="flex items-center gap-4">
                      <div
                        class="flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-rose-500 to-rose-600 p-3 shadow-md shadow-rose-200 transition-transform duration-300 group-hover:scale-110"
                      >
                        <TagIcon class="size-7 text-white" />
                      </div>
                      <div class="flex-1">
                        <div
                          class="mb-1.5 text-xs font-semibold uppercase tracking-wider text-rose-700 opacity-80"
                        >
                          付款条款
                        </div>
                        <div class="text-base font-bold text-rose-800">
                          {{ purchaseContract.payment_terms || '-' }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 合同扩展信息 -->
                <div v-if="purchaseContract" class="mt-6">
                  <div
                    class="group relative overflow-hidden rounded-xl border border-amber-200/50 bg-gradient-to-br from-amber-50 to-amber-100/30 p-5 shadow-sm transition-all duration-300 hover:border-amber-300/70 hover:shadow-md"
                  >
                    <div
                      class="absolute -right-6 -top-6 h-16 w-16 rounded-full bg-amber-500/10"
                    ></div>
                    <div
                      class="absolute -right-2 -top-2 h-8 w-8 rounded-full bg-amber-500/20"
                    ></div>
                    <div class="flex items-center gap-4">
                      <div
                        class="flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-amber-500 to-amber-600 p-3 shadow-md shadow-amber-200 transition-transform duration-300 group-hover:scale-110"
                      >
                        <TagIcon class="size-7 text-white" />
                      </div>
                      <div class="flex-1">
                        <div
                          class="mb-1.5 text-xs font-semibold uppercase tracking-wider text-amber-700 opacity-80"
                        >
                          交货地址
                        </div>
                        <div class="text-base font-bold text-amber-800">
                          {{ purchaseContract.delivery_address || '-' }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 合同进度 -->
          <div class="lg:col-span-1">
            <div
              class="h-full overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm"
            >
              <div
                class="border-b border-gray-100 bg-gradient-to-r from-indigo-50 to-blue-50 px-6 py-4"
              >
                <h2
                  class="flex items-center text-lg font-semibold text-gray-900"
                >
                  <div class="mr-3 h-6 w-1 rounded-full bg-indigo-500"></div>
                  合同进度
                </h2>
              </div>
              <div class="p-6">
                <Spin :spinning="workflowLoading">
                  <Steps
                    v-if="workflowSteps.length > 0"
                    direction="vertical"
                    size="small"
                    :current="
                      workflowSteps.findIndex((s) => s.status === 'process')
                    "
                    class="!pl-0"
                  >
                    <Step
                      v-for="(step, index) in workflowSteps"
                      :key="index"
                      :title="step.title"
                      :description="step.description"
                      :status="step.status"
                    >
                      <template #icon>
                        <div
                          class="flex h-8 w-8 items-center justify-center rounded-full text-white"
                          :class="{
                            'bg-green-500': step.status === 'finish',
                            'bg-blue-500': step.status === 'process',
                            'bg-gray-300': step.status === 'wait',
                            'bg-red-500': step.status === 'error',
                          }"
                        >
                          <span v-if="step.status === 'finish'">✓</span>
                          <span v-else-if="step.status === 'error'">✗</span>
                          <span v-else>{{ index + 1 }}</span>
                        </div>
                      </template>
                    </Step>
                  </Steps>
                </Spin>
              </div>
            </div>
          </div>
        </div>

        <!-- 合同明细单独占一行 -->
        <div
          class="mt-6 overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm"
        >
          <div
            class="border-b border-gray-100 bg-gradient-to-r from-emerald-50 to-teal-50 px-6 py-4"
          >
            <h2 class="flex items-center text-lg font-semibold text-gray-900">
              <div class="mr-3 h-6 w-1 rounded-full bg-emerald-500"></div>
              合同明细
            </h2>
          </div>
          <div class="p-6">
            <div
              v-if="purchaseContract?.items?.length"
              class="rounded-lg bg-gray-50/50 p-4"
            >
              <div class="mb-4 flex items-center justify-between">
                <div class="font-medium text-gray-700">
                  共 {{ purchaseContract.items.length }} 条记录
                </div>
                <div
                  v-if="purchaseContract.total_amount"
                  class="text-lg font-bold text-orange-600"
                >
                  合计金额: ¥{{
                    purchaseContract.total_amount.toLocaleString()
                  }}
                </div>
              </div>
              <ItemGrid />
            </div>
            <div v-else class="py-12 text-center">
              <div class="flex flex-col items-center space-y-3">
                <div
                  class="flex h-16 w-16 items-center justify-center rounded-full bg-gray-100"
                >
                  <FileText class="size-8 text-gray-400" />
                </div>
                <p class="font-medium text-gray-500">暂无合同明细</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 附件和审批历史 -->
        <div class="mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2">
          <!-- 附件信息 -->
          <div
            class="overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm"
          >
            <div
              class="border-b border-gray-100 bg-gradient-to-r from-violet-50 to-purple-50 px-6 py-4"
            >
              <h2 class="flex items-center text-lg font-semibold text-gray-900">
                <div class="mr-3 h-6 w-1 rounded-full bg-violet-500"></div>
                相关附件
              </h2>
            </div>
            <div class="p-6">
              <Spin :spinning="attachmentsLoading">
                <div v-if="attachments.length === 0" class="py-12 text-center">
                  <div class="flex flex-col items-center space-y-3">
                    <div
                      class="flex h-16 w-16 items-center justify-center rounded-full bg-gray-100"
                    >
                      <FileText class="size-8 text-gray-400" />
                    </div>
                    <p class="font-medium text-gray-500">暂无附件</p>
                  </div>
                </div>
                <div v-else class="space-y-6">
                  <div
                    v-for="(files, category) in groupedAttachments"
                    :key="category"
                    class="rounded-lg border border-gray-200 p-4"
                  >
                    <h3
                      class="mb-4 flex items-center text-base font-semibold text-gray-800"
                    >
                      <div
                        class="mr-2 h-4 w-4 rounded-full"
                        :class="{
                          'bg-green-500': category.includes('内部签章'),
                          'bg-blue-500': category.includes('双方签章'),
                          'bg-purple-500':
                            !category.includes('内部签章') &&
                            !category.includes('双方签章'),
                        }"
                      ></div>
                      {{ category }}
                      <span class="ml-2 text-xs text-gray-500"
                        >({{ files.length }}个文件)</span
                      >
                    </h3>
                    <div
                      class="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3"
                    >
                      <div
                        v-for="file in files"
                        :key="file.id"
                        class="group cursor-pointer rounded-xl border border-gray-200/60 bg-gradient-to-br from-gray-50 to-gray-100/80 p-3 transition-all duration-200 hover:border-violet-300/50 hover:shadow-md"
                      >
                        <div class="flex items-start space-x-3">
                          <div
                            class="flex h-10 w-10 items-center justify-center rounded-lg bg-violet-100 transition-colors group-hover:bg-violet-200"
                          >
                            <FileText class="size-5 text-violet-600" />
                          </div>
                          <div class="min-w-0 flex-1">
                            <div
                              class="mb-1 truncate text-sm font-semibold text-gray-900"
                            >
                              {{ file.file_name }}
                            </div>
                            <div class="mb-2 text-xs text-gray-500">
                              {{ (file.file_size / 1024).toFixed(1) }} KB
                            </div>
                            <div class="flex space-x-2">
                              <Button
                                size="small"
                                type="primary"
                                class="!rounded-md !px-3 !text-xs"
                                @click="handlePreview(file)"
                              >
                                预览
                              </Button>
                              <Button
                                size="small"
                                class="!rounded-md !px-3 !text-xs"
                                @click="handleDownload(file)"
                              >
                                下载
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Spin>
            </div>
          </div>

          <!-- 审批历史 -->
          <div
            class="overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm"
          >
            <div
              class="border-b border-gray-100 bg-gradient-to-r from-amber-50 to-yellow-50 px-6 py-4"
            >
              <h2 class="flex items-center text-lg font-semibold text-gray-900">
                <div class="mr-3 h-6 w-1 rounded-full bg-amber-500"></div>
                审批历史
              </h2>
            </div>
            <div class="p-6">
              <Spin :spinning="workflowLoading">
                <Timeline
                  v-if="approvalHistory.length > 0"
                  class="custom-timeline !pl-0"
                >
                  <TimelineItem
                    v-for="(history, index) in approvalHistory"
                    :key="index"
                    :color="
                      ACTION_CONFIG[history.action as string]?.color || 'blue'
                    "
                  >
                    <template #dot>
                      <div
                        class="flex h-8 w-8 items-center justify-center rounded-full font-semibold text-white shadow-lg"
                        :class="{
                          'bg-gradient-to-br from-green-500 to-green-600':
                            history.action === 'approve',
                          'bg-gradient-to-br from-red-500 to-red-600':
                            history.action === 'reject',
                          'bg-gradient-to-br from-orange-500 to-amber-600':
                            history.action === 'rollback',
                          'bg-gradient-to-br from-blue-500 to-blue-600':
                            history.action === 'create',
                          'bg-gradient-to-br from-gray-500 to-gray-600':
                            history.action === 'cancel',
                        }"
                      >
                        <span v-if="history.action === 'approve'">✓</span>
                        <span v-else-if="history.action === 'reject'">✗</span>
                        <span v-else-if="history.action === 'rollback'">↺</span>
                        <span v-else-if="history.action === 'create'">+</span>
                        <span v-else-if="history.action === 'cancel'">×</span>
                        <span v-else>•</span>
                      </div>
                    </template>

                    <div
                      class="rounded-lg border border-gray-100 bg-white p-4 shadow-sm transition-shadow duration-200 hover:shadow-md"
                    >
                      <div class="mb-3 flex items-center gap-3">
                        <div
                          class="flex h-8 w-8 items-center justify-center rounded-full text-white"
                          :class="{
                            'bg-gradient-to-br from-green-500 to-green-600':
                              history.action === 'approve',
                            'bg-gradient-to-br from-red-500 to-red-600':
                              history.action === 'reject',
                            'bg-gradient-to-br from-orange-500 to-amber-600':
                              history.action === 'rollback',
                            'bg-gradient-to-br from-blue-500 to-blue-600':
                              history.action === 'create',
                            'bg-gradient-to-br from-gray-500 to-gray-600':
                              history.action === 'cancel',
                          }"
                        >
                          {{ String(history.operator_name || 'U').charAt(0) }}
                        </div>
                        <div class="flex-1">
                          <div class="font-medium text-gray-900">
                            {{ history.operator_name || '未知用户' }}
                          </div>
                          <div class="text-sm text-gray-500">
                            {{
                              formatToDateTime(history.operation_time) ||
                              '未知时间'
                            }}
                          </div>
                        </div>
                        <Tag
                          :color="
                            ACTION_CONFIG[history.action as string]?.color ||
                            'default'
                          "
                          class="rounded-full px-3 py-1 text-sm font-medium"
                        >
                          {{
                            ACTION_CONFIG[history.action as string]?.text ||
                            '其他操作'
                          }}
                        </Tag>
                      </div>

                      <div class="mb-2 text-sm text-gray-600">
                        <span v-if="history.action === 'rollback'">
                          从
                          <Tag color="blue" class="mx-1 rounded-md">
                            {{
                              FLOW_STATUS_CONFIG[history.previous_status || '']
                                ?.text ||
                              history.previous_status ||
                              '初始状态'
                            }}
                          </Tag>
                          回退到
                          <Tag color="orange" class="mx-1 rounded-md">
                            {{
                              FLOW_STATUS_CONFIG[history.new_status || '']
                                ?.text ||
                              history.new_status ||
                              '初始状态'
                            }}
                          </Tag>
                        </span>
                        <span v-else-if="history.action === 'create'">
                          状态变更为：
                          <Tag color="blue" class="rounded-md">
                            {{
                              FLOW_STATUS_CONFIG[history.new_status || '']
                                ?.text ||
                              history.new_status ||
                              '初始状态'
                            }}
                          </Tag>
                        </span>
                        <span v-else-if="history.action === 'cancel'">
                          取消前状态：
                          <Tag color="blue" class="rounded-md">
                            {{
                              FLOW_STATUS_CONFIG[history.previous_status || '']
                                ?.text ||
                              history.previous_status ||
                              '初始状态'
                            }}
                          </Tag>
                        </span>
                        <span v-else>
                          从
                          <Tag color="blue" class="mx-1 rounded-md">
                            {{
                              FLOW_STATUS_CONFIG[history.previous_status || '']
                                ?.text ||
                              history.previous_status ||
                              '初始状态'
                            }}
                          </Tag>
                          到
                          <Tag color="green" class="mx-1 rounded-md">
                            {{
                              FLOW_STATUS_CONFIG[history.new_status || '']
                                ?.text ||
                              history.new_status ||
                              '初始状态'
                            }}
                          </Tag>
                        </span>
                      </div>

                      <div
                        v-if="history.comments"
                        class="rounded-lg border-l-4 bg-gray-50 p-3"
                        :class="{
                          'border-green-500': history.action === 'approve',
                          'border-red-500': history.action === 'reject',
                          'border-orange-500': history.action === 'rollback',
                          'border-blue-500': history.action === 'create',
                          'border-gray-500': history.action === 'cancel',
                        }"
                      >
                        <div
                          class="mb-1 text-xs font-semibold uppercase tracking-wide text-gray-500"
                        >
                          {{
                            {
                              approve: '审批意见',
                              reject: '拒绝原因',
                              rollback: '回退原因',
                              create: '创建说明',
                              cancel: '取消原因',
                            }[history.action as string] || '操作说明'
                          }}
                        </div>
                        <div class="text-sm leading-relaxed text-gray-800">
                          {{ history.comments }}
                        </div>
                      </div>
                    </div>
                  </TimelineItem>
                </Timeline>
                <div v-else class="py-12 text-center">
                  <div class="flex flex-col items-center space-y-3">
                    <div
                      class="flex h-16 w-16 items-center justify-center rounded-full bg-gray-100"
                    >
                      <User class="size-8 text-gray-400" />
                    </div>
                    <p class="font-medium text-gray-500">暂无审批记录</p>
                  </div>
                </div>
              </Spin>
            </div>
          </div>
        </div>
      </Spin>
    </div>

    <!-- 回到顶部 -->
    <BackTop :visibility-height="100">
      <div
        class="back-top-inner flex h-10 w-10 items-center justify-center rounded-full bg-blue-500 text-white shadow-lg hover:bg-blue-600"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M5 15l7-7 7 7"
          />
        </svg>
      </div>
    </BackTop>

    <!-- 审批弹窗 -->
    <Modal
      v-model:open="isApprovalModalVisible"
      :title="currentAction === 'approve' ? '审批通过' : '审批拒绝'"
      @ok="confirmApproval"
      class="custom-modal !rounded-xl"
      :width="500"
    >
      <div class="space-y-4 py-4">
        <div class="rounded-lg border border-gray-200/60 bg-gray-50 p-4">
          <label class="mb-2 block text-sm font-semibold text-gray-700">
            审批意见
          </label>
          <Textarea
            v-model:value="approvalComments"
            :rows="4"
            placeholder="请输入审批意见"
            class="!rounded-lg !border-gray-300"
          />
        </div>

        <!-- 在内部签章和双方签章环节添加文件上传 -->
        <div
          v-if="
            currentAction === 'approve' &&
            (purchaseContract?.status === 'internal_sign' ||
              purchaseContract?.status === 'double_sign')
          "
          class="rounded-lg border border-blue-200/60 bg-blue-50 p-4"
        >
          <div class="mb-2 flex items-center">
            <span class="mr-1 text-sm font-semibold text-blue-800">
              {{
                purchaseContract?.status === 'internal_sign'
                  ? '内部签章文件'
                  : '双方签章文件'
              }}
            </span>
            <span class="font-bold text-red-500">*</span>
            <span class="ml-2 text-xs text-gray-500">(必填)</span>
          </div>

          <div class="mb-2 text-xs text-gray-600">
            请上传{{
              purchaseContract?.status === 'internal_sign'
                ? '我方已盖章的合同文件'
                : '双方已盖章的合同文件'
            }}，支持PDF、Word文档或图片格式，大小不超过10MB
          </div>

          <Upload
            v-model:file-list="fileList"
            :before-upload="beforeUpload"
            :custom-request="() => {}"
            @change="handleFileChange"
            :multiple="true"
            class="upload-demo"
            list-type="picture"
          >
            <Button type="primary">
              <span>选择文件</span>
            </Button>
            <template #tip>
              <div v-if="uploadError" class="mt-1 text-xs text-red-500">
                {{ uploadError }}
              </div>
            </template>
          </Upload>
        </div>

        <div
          v-if="uploadError"
          class="rounded-lg border border-red-200/60 bg-red-50 p-4 text-red-800"
        >
          <p class="mb-2 text-sm font-semibold">上传文件提示:</p>
          <p class="text-xs">{{ uploadError }}</p>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end space-x-3">
          <Button @click="isApprovalModalVisible = false">取消</Button>
          <Button
            type="primary"
            @click="confirmApproval"
            :loading="uploadLoading"
            :class="{
              'bg-green-500 hover:bg-green-600': currentAction === 'approve',
              'bg-red-500 hover:bg-red-600': currentAction === 'reject',
            }"
          >
            确认{{ currentAction === 'approve' ? '通过' : '拒绝' }}
          </Button>
        </div>
      </template>
    </Modal>

    <!-- 回退弹窗 -->
    <Modal
      v-model:open="isRollbackModalVisible"
      title="流程回退"
      @ok="confirmRollback"
      class="custom-modal !rounded-xl"
      :width="500"
    >
      <div class="space-y-4 py-4">
        <div class="rounded-lg border border-gray-200/60 bg-gray-50 p-4">
          <label class="mb-2 block text-sm font-semibold text-gray-700">
            回退目标
          </label>
          <Select
            v-model:value="rollbackTarget"
            style="width: 100%"
            placeholder="请选择回退目标环节"
            :options="availableRollbackTargets"
            :disabled="availableRollbackTargets.length === 0"
          />
          <div
            v-if="availableRollbackTargets.length === 0"
            class="mt-2 text-xs text-yellow-600"
          >
            没有可回退的目标环节
          </div>
        </div>
        <div class="rounded-lg border border-gray-200/60 bg-gray-50 p-4">
          <label class="mb-2 block text-sm font-semibold text-gray-700">
            回退原因
          </label>
          <Textarea
            v-model:value="rollbackComments"
            :rows="4"
            placeholder="请输入回退原因"
            class="!rounded-lg !border-gray-300"
          />
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end space-x-3">
          <Button @click="isRollbackModalVisible = false">取消</Button>
          <Button
            type="primary"
            @click="confirmRollback"
            class="bg-orange-500 hover:bg-orange-600"
          >
            确认回退
          </Button>
        </div>
      </template>
    </Modal>

    <!-- 图片预览弹窗 -->
    <Image
      :preview="{
        visible: previewVisible,
        onVisibleChange: (vis: boolean) => (previewVisible = vis),
      }"
      :src="previewImage"
      style="display: none"
    />
  </Page>
</template>

<style scoped>
/* Custom Timeline Styling */
:deep(.custom-timeline .ant-timeline-item-tail) {
  border-left: 2px solid #e5e7eb;
}

/* Custom Card Header Styling */
:deep(.ant-card-head) {
  background: linear-gradient(to right, #f9fafb, #f3f4f6);
  border-bottom: 1px solid #e5e7eb;
}

:deep(.ant-card-head-title) {
  font-weight: 600;
  color: #1f2937;
}

/* 文件上传组件样式 */
:deep(.upload-demo .ant-upload-list) {
  margin-top: 10px;
}

:deep(.upload-demo .ant-upload-list-picture-item) {
  overflow: hidden;
  border-radius: 8px;
}

:deep(.upload-demo .ant-upload-select) {
  margin-right: 8px;
}

:deep(.upload-demo .ant-upload-list-item-name) {
  font-size: 12px;
}

/* Modal Styling */
:deep(.custom-modal .ant-modal-header) {
  padding: 16px 24px;
  background: linear-gradient(to right, #eff6ff, #e0e7ff);
  border-bottom: 1px solid #e5e7eb;
  border-radius: 0.75rem 0.75rem 0 0;
}

:deep(.custom-modal .ant-modal-content) {
  overflow: hidden;
  border-radius: 0.75rem;
  box-shadow: 0 10px 25px rgb(0 0 0 / 10%);
}

:deep(.custom-modal .ant-modal-title) {
  font-weight: 600;
  color: #1f2937;
}

:deep(.custom-modal .ant-modal-body) {
  padding: 20px 24px;
}

:deep(.custom-modal .ant-modal-footer) {
  padding: 16px 24px;
  border-top: 1px solid #f3f4f6;
}

/* Transition Effects */
.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover Effects */
/* stylelint-disable selector-class-pattern */
.hover\:shadow-md:hover {
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 10%),
    0 2px 4px -1px rgb(0 0 0 / 6%);
}

.hover\:shadow-lg:hover {
  box-shadow:
    0 10px 15px -3px rgb(0 0 0 / 10%),
    0 4px 6px -2px rgb(0 0 0 / 5%);
}

/* BackTop Button Styling */
.back-top-inner {
  transition: all 0.3s;
}

.back-top-inner:hover {
  box-shadow:
    0 14px 28px rgb(0 0 0 / 25%),
    0 10px 10px rgb(0 0 0 / 22%);
  transform: translateY(-3px);
}
</style>
