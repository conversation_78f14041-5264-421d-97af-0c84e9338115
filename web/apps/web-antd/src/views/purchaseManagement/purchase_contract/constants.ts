/**
 * 采购合同常量定义
 */

// 定义标签配置接口
export interface TagConfig {
  color: string;
  text: string;
}

// 定义标签配置映射接口
export interface TagConfigMap {
  [key: string]: TagConfig;
}

// 工作流状态配置
export const FLOW_STATUS_CONFIG: TagConfigMap = {
  draft: { color: '', text: '草稿' },
  submitted: { color: 'blue', text: '已提交' },
  purchase_review: { color: 'purple', text: '采购负责人审批' },
  finance_review: { color: 'purple', text: '财务负责人审批' },
  legal_review: { color: 'purple', text: '法务负责人审批' },
  enterprise_review: { color: 'orange', text: '企业负责人审批' },
  internal_sign: { color: 'green', text: '公司内部签章' },
  double_sign: { color: 'green', text: '双方签章完成' },
  completed: { color: 'green', text: '审批完成' },
  approved: { color: 'green', text: '已批准' },
  rejected: { color: 'red', text: '已拒绝' },
  cancelled: { color: 'red', text: '已取消' },
};

// 工作流状态选项
export const FLOW_STATUS_OPTIONS = [
  { label: '草稿', value: 'draft' },
  { label: '已提交', value: 'submitted' },
  { label: '采购负责人审批', value: 'purchase_review' },
  { label: '财务负责人审批', value: 'finance_review' },
  { label: '法务负责人审批', value: 'legal_review' },
  { label: '企业负责人审批', value: 'enterprise_review' },
  { label: '公司内部签章', value: 'internal_sign' },
  { label: '双方签章完成', value: 'double_sign' },
  { label: '流程完成', value: 'completed' },
  { label: '已批准', value: 'approved' },
  { label: '已拒绝', value: 'rejected' },
  { label: '已取消', value: 'cancelled' },
];

// 合同类型配置
export const CONTRACT_TYPE_CONFIG: TagConfigMap = {
  purchase: { color: 'blue', text: '采购合同' },
  service: { color: 'green', text: '服务合同' },
  lease: { color: 'orange', text: '租赁合同' },
  sale: { color: 'purple', text: '销售合同' },
  supplement: { color: 'cyan', text: '补充合同' },
  other: { color: 'default', text: '其他合同' },
};

// 合同类型选项
export const CONTRACT_TYPE_OPTIONS = [
  { label: '采购合同', value: 'purchase' },
  { label: '服务合同', value: 'service' },
  { label: '租赁合同', value: 'lease' },
  { label: '销售合同', value: 'sale' },
  { label: '补充合同', value: 'supplement' },
  { label: '其他合同', value: 'other' },
];

// 付款条款选项
export const PAYMENT_TERMS_OPTIONS = [
  { label: '货到付款', value: 'cod' },
  { label: '预付50%，货到付款50%', value: 'prepay_50_cod_50' },
  { label: '预付30%，货到付款70%', value: 'prepay_30_cod_70' },
  { label: '预付100%', value: 'prepay_100' },
  { label: '月结30天', value: 'net_30' },
  { label: '月结60天', value: 'net_60' },
  { label: '季度结算', value: 'quarterly' },
];

// 单位选项
export const UNIT_OPTIONS = [
  { label: '台', value: '台' },
  { label: '个', value: '个' },
  { label: '套', value: '套' },
  { label: '片', value: '片' },
  { label: '块', value: '块' },
  { label: '条', value: '条' },
  { label: '根', value: '根' },
  { label: '米', value: '米' },
  { label: '公斤', value: '公斤' },
  { label: '批', value: '批' },
];

// 操作类型映射
export const ACTION_CONFIG: TagConfigMap = {
  create: { text: '创建合同', color: 'blue' },
  submit: { text: '提交合同', color: 'green' },
  approve: { text: '审批通过', color: 'success' },
  reject: { text: '审批拒绝', color: 'error' },
  rollback: { text: '流程回退', color: 'warning' },
  cancel: { text: '取消合同', color: 'error' },
  update: { text: '更新合同', color: 'blue' },
  delete: { text: '删除合同', color: 'error' },
};

// 审批动作选项
export const APPROVAL_ACTION_OPTIONS = [
  { label: '批准', value: 'approve' },
  { label: '拒绝', value: 'reject' },
];

// 合同执行状态顺序(用于计算进度)
export const CONTRACT_STATUS_ORDER = [
  'draft',
  'in_review',
  'in_execution',
  'completed',
];

// 工作流阶段配置
export const WORKFLOW_STAGE_CONFIG: TagConfigMap = {
  draft: { color: '', text: '草稿阶段' },
  finance_review: { color: 'purple', text: '财务审批' },
  enterprise_review: { color: 'orange', text: '企业审批' },
  approved: { color: 'green', text: '审批完成' },
  rejected: { color: 'red', text: '已拒绝' },
};
