<script lang="ts" setup>
import type { Company } from '#/api/core/purchase/company';
// 扩展 Window 接口声明全局属性
import type {
  CreateContractItemParams,
  CreatePurchaseContractParams,
  PurchaseContract,
} from '#/api/core/purchase/purchase_contract';
import type {
  PurchaseInquiry,
  PurchaseInquiryItem,
} from '#/api/core/purchase/purchase_inquiry';
import type { Supplier } from '#/api/core/purchase/supplier';

import {
  computed,
  nextTick,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  watch,
} from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { Modal as AModal, Button, message, Tabs } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { getCompanyDetailApi } from '#/api/core/purchase/company';
import {
  createPurchaseContract,
  getInquiryItemsPurchaseStats,
  updatePurchaseContract,
} from '#/api/core/purchase/purchase_contract';
import { getPurchaseInquiryById } from '#/api/core/purchase/purchase_inquiry';
import { getSupplierDetailApi } from '#/api/core/purchase/supplier';
import { useBasicSchema } from '#/views/purchaseManagement/purchase_contract/schema';

import FileUploader from '../../../../components/FileUploader/FileUploader.vue';

declare global {
  interface Window {
    handleInquiryChange?: (id: number) => void;
    handleSupplierChange?: (id: number) => void;
    handleCompanyChange?: (id: number) => void;
  }
}

const emit = defineEmits(['success']);
const formData = ref<PurchaseContract>({
  our_company_id: null as unknown as number,
  supplier_id: null as unknown as number,
  total_amount: 0,
  contract_title: '',
  contract_type: '',
  signing_date: null as unknown as string,
  delivery_address: '',
  payment_terms: '',
  warranty_period: '',
  items: [],
});
const readOnly = ref(false);
const supplierData = ref<null | Supplier>(null);
const companyData = ref<Company | null>(null);
const inquiryData = ref<null | PurchaseInquiry>(null);
const currentSupplierId = ref<null | number>(null);
const currentCompanyId = ref<null | number>(null);
const currentInquiryId = ref<null | number>(null);
const fileUploaderRef = ref<InstanceType<typeof FileUploader> | null>(null);
const purchaseStatsMap = ref<Map<number, any>>(new Map()); // 存储询价明细的采购统计信息

const getTitle = computed(() => {
  if (readOnly.value) {
    return '查看采购合同';
  }
  return formData.value?.id ? '编辑采购合同' : '创建采购合同';
});

// 创建表单
const [BasicForm, basicFormApi] = useVbenForm({
  layout: 'vertical',
  schema: useBasicSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-4',
});

// 创建一个响应式的表单值对象
const formValues = ref<Record<string, any>>({});

// 每次获取表单值并更新到响应式对象
async function updateFormValues() {
  try {
    const values = await basicFormApi.getValues();
    if (values) {
      formValues.value = values;
    }
  } catch (error) {
    console.error('获取表单值失败:', error);
  }
}

// 添加表单onChange处理函数
function handleFormFieldChange() {
  setTimeout(updateFormValues, 100);
}

// 强力禁止数字输入框滚轮事件的函数
function preventNumberInputWheel(event: Event) {
  // 多重阻止确保事件被完全拦截
  event.preventDefault();
  event.stopPropagation();
  event.stopImmediatePropagation();

  // 对于某些浏览器，还需要返回false
  return false;
}

// 为数字输入框添加滚轮事件监听器
function addWheelPreventionToNumberInputs() {
  // 查找所有数字输入框
  const numberInputs = document.querySelectorAll('input[type="number"]');

  numberInputs.forEach((input) => {
    // 移除可能已存在的监听器（避免重复添加）
    input.removeEventListener('wheel', preventNumberInputWheel);
    input.removeEventListener('mousewheel', preventNumberInputWheel);
    input.removeEventListener('DOMMouseScroll', preventNumberInputWheel);

    // 添加强制事件监听器，使用 capture 和 passive: false
    input.addEventListener('wheel', preventNumberInputWheel, {
      passive: false,
      capture: true,
    });
    input.addEventListener('mousewheel', preventNumberInputWheel, {
      passive: false,
      capture: true,
    });
    input.addEventListener('DOMMouseScroll', preventNumberInputWheel, {
      passive: false,
      capture: true,
    });
  });
}

// 监听DOM变化，为新添加的数字输入框添加事件监听器
function setupMutationObserver() {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;
            // 检查新添加的元素是否是数字输入框
            if (
              element.tagName === 'INPUT' &&
              element.getAttribute('type') === 'number'
            ) {
              const input = element as HTMLInputElement;
              input.addEventListener('wheel', preventNumberInputWheel, {
                passive: false,
                capture: true,
              });
              input.addEventListener('mousewheel', preventNumberInputWheel, {
                passive: false,
                capture: true,
              });
              input.addEventListener(
                'DOMMouseScroll',
                preventNumberInputWheel,
                {
                  passive: false,
                  capture: true,
                },
              );
            }
            // 检查新添加的元素内部是否包含数字输入框
            const numberInputs = element.querySelectorAll(
              'input[type="number"]',
            );
            numberInputs.forEach((input) => {
              input.addEventListener('wheel', preventNumberInputWheel, {
                passive: false,
                capture: true,
              });
              input.addEventListener('mousewheel', preventNumberInputWheel, {
                passive: false,
                capture: true,
              });
              input.addEventListener(
                'DOMMouseScroll',
                preventNumberInputWheel,
                {
                  passive: false,
                  capture: true,
                },
              );
            });
          }
        });
      }
    });
  });

  // 开始观察整个文档的变化
  observer.observe(document.body, {
    childList: true,
    subtree: true,
  });

  return observer;
}

// 在script部分顶部添加全局处理函数
onMounted(() => {
  // 注册全局处理函数
  window.handleInquiryChange = (id: number) => {
    handleInquiryChange(id);
  };

  window.handleSupplierChange = (id: number) => {
    handleSupplierChange(id);
  };

  window.handleCompanyChange = (id: number) => {
    handleCompanyChange(id);
  };

  // 初始加载完成后的处理
  nextTick(() => {
    updateFormValues();

    // 为所有数字输入框添加滚轮事件阻止
    addWheelPreventionToNumberInputs();

    // 设置DOM变化监听器
    setupMutationObserver();
  });
});

// 组件卸载时清除全局函数
onUnmounted(() => {
  window.handleInquiryChange = undefined;
  window.handleSupplierChange = undefined;
  window.handleCompanyChange = undefined;
});

// 添加防抖标志，避免重复请求
const isHandlingInquiry = ref(false);
const isHandlingSupplier = ref(false);
const isHandlingCompany = ref(false);

// 监听表单值中的inquiry_id变化
watch(
  () => formValues.value?.inquiry_id,
  async (newVal, oldVal) => {
    if (newVal !== oldVal && newVal && !isHandlingInquiry.value) {
      isHandlingInquiry.value = true;
      try {
        await handleInquiryChange(Number(newVal));
      } finally {
        isHandlingInquiry.value = false;
      }
    }
  },
);

// 监听表单值中的supplier_id变化
watch(
  () => formValues.value?.supplier_id,
  async (newVal, oldVal) => {
    if (newVal !== oldVal && newVal && !isHandlingSupplier.value) {
      isHandlingSupplier.value = true;
      try {
        await handleSupplierChange(Number(newVal));
      } finally {
        isHandlingSupplier.value = false;
      }
    }
  },
);

// 监听表单值中的our_company_id变化
watch(
  () => formValues.value?.our_company_id,
  async (newVal, oldVal) => {
    if (newVal !== oldVal && newVal && !isHandlingCompany.value) {
      isHandlingCompany.value = true;
      try {
        await handleCompanyChange(Number(newVal));
      } finally {
        isHandlingCompany.value = false;
      }
    }
  },
);

// 当供应商ID改变时获取供应商详情
async function handleSupplierChange(supplierId: null | number) {
  if (!supplierId) {
    supplierData.value = null;
    return;
  }

  // 如果已经是当前供应商ID，避免重复处理
  if (currentSupplierId.value === supplierId) {
    return;
  }

  currentSupplierId.value = supplierId;

  try {
    const supplierDetail = await getSupplierDetailApi(supplierId);
    supplierData.value = supplierDetail;
  } catch (error) {
    console.error('获取供应商详情失败:', error);
    supplierData.value = null;
  }
}

// 当公司ID改变时获取公司详情
async function handleCompanyChange(companyId: null | number) {
  if (!companyId) {
    companyData.value = null;
    return;
  }

  currentCompanyId.value = companyId;

  try {
    companyData.value = await getCompanyDetailApi(companyId);
  } catch (error) {
    console.error('Failed to get company details:', error);
    companyData.value = null;
  }
}

// 当询价ID改变时获取询价详情并加载明细
async function handleInquiryChange(inquiryId: null | number) {
  if (!inquiryId) {
    inquiryData.value = null;
    itemList.length = 0;
    return;
  }

  // 如果已经是当前询价ID，避免重复处理
  if (currentInquiryId.value === inquiryId) {
    return;
  }

  currentInquiryId.value = inquiryId;

  try {
    const inquiryDetail = await getPurchaseInquiryById(inquiryId);

    if (!inquiryDetail) {
      console.error('未获取到询价详情');
      return;
    }

    inquiryData.value = inquiryDetail;

    // 自动填充供应商信息
    if (inquiryDetail.supplier_id) {
      basicFormApi.setFieldValue('supplier_id', inquiryDetail.supplier_id);

      // 等待供应商数据加载完成
      await handleSupplierChange(inquiryDetail.supplier_id);

      // 等待一个tick确保供应商数据已更新
      await nextTick();

      // 现在检查并填充对方合同收件信息
      if (supplierData.value) {
        // 生成对方合同收件信息
        const addressInfo = [supplierData.value.address]
          .filter(Boolean)
          .join('\n');

        if (addressInfo) {
          basicFormApi.setFieldValue('delivery_address', addressInfo);
        }
      } else {
        console.warn('供应商数据未加载成功');
      }
    }

    // 自动填充项目信息
    if (inquiryDetail.project_id) {
      basicFormApi.setFieldValue('project_id', inquiryDetail.project_id);
    }

    // 清空现有明细
    itemList.length = 0;
    purchaseStatsMap.value.clear();

    // 获取采购统计信息
    try {
      const purchaseStats = await getInquiryItemsPurchaseStats(inquiryId);
      if (purchaseStats && purchaseStats.length > 0) {
        // 将统计信息存储到Map中，以inquiry_item_id为key
        purchaseStats.forEach((stat) => {
          purchaseStatsMap.value.set(stat.inquiry_item_id, stat);
        });
      }
    } catch (error) {
      console.error('获取采购统计信息失败:', error);
      // 不阻断流程，继续执行
    }

    // 从询价明细中加载合同明细
    if (inquiryDetail.items && inquiryDetail.items.length > 0) {
      inquiryDetail.items.forEach((inquiryItem: PurchaseInquiryItem) => {
        if (!inquiryItem || !inquiryItem.id) {
          return;
        }

        // 获取该明细的采购统计信息
        const purchaseStat = purchaseStatsMap.value.get(inquiryItem.id);

        // 将询价明细转换为合同明细
        const contractItem = {
          inquiry_item_id: inquiryItem.id,
          material_type: inquiryItem.material_type || '',
          model: inquiryItem.model || '',
          brand: inquiryItem.brand || '',
          pn: inquiryItem.pn || '',
          spec: inquiryItem.spec || '',
          unit: inquiryItem.unit || '',
          contract_quantity: purchaseStat
            ? purchaseStat.unpurchased_quantity
            : inquiryItem.current_inquiry_quantity,
          contract_price: inquiryItem.budget_price || 0,
          contract_amount: 0, // 将在下面计算
          remark: inquiryItem.remark || '',
          // 添加采购统计信息用于显示
          purchase_stats: purchaseStat || null,
        };

        // 计算合同金额
        contractItem.contract_amount =
          contractItem.contract_quantity * contractItem.contract_price;

        itemList.push(contractItem);
      });

      // 计算总金额
      calculateTotalAmount();

      message.success(`已从询价单加载${itemList.length}个明细项`);
    }
  } catch (error) {
    console.error('获取询价详情失败:', error);
    message.error('获取询价详情失败，请检查网络连接');
    inquiryData.value = null;
  }
}

// 移除 watchEffect，避免与 watch 重复监听

interface ContractItemFormData extends CreateContractItemParams {
  // 可以添加额外的UI相关字段
  purchase_stats?: null | {
    brand: string;
    inquiry_budget: number;
    inquiry_item_id: number;
    inquiry_quantity: number;
    material_type: string;
    model: string;
    pn: string;
    purchased_amount: number;
    purchased_quantity: number;
    spec: string;
    unit: string;
    unpurchased_budget: number;
    unpurchased_quantity: number;
  };
}

const itemList = reactive<ContractItemFormData[]>([]);

// 添加新的明细项
function addNewItem() {
  itemList.push({
    inquiry_item_id: undefined,
    material_type: '',
    model: '',
    brand: '',
    pn: '',
    spec: '',
    unit: '',
    contract_quantity: 1,
    contract_price: 0,
    contract_amount: 0,
    remark: '',
  });
}

// 删除明细项
function deleteContractItem(index: number) {
  if (index >= 0 && index < itemList.length) {
    itemList.splice(index, 1);
    calculateTotalAmount();
    message.success('已删除合同明细项');

    if (itemList.length === 0) {
      message.warning('注意：合同没有明细项，提交前请确保添加合同项目');
    }
  }
}

// 计算单个明细的总价
function calculateItemAmount(item: ContractItemFormData) {
  item.contract_amount =
    (item.contract_quantity || 0) * (item.contract_price || 0);
  calculateTotalAmount();
}

// 计算合同总金额
function calculateTotalAmount() {
  const total = itemList.reduce(
    (sum, item) => sum + (item.contract_amount || 0),
    0,
  );
  formData.value.total_amount = total;
}

async function resetForm() {
  basicFormApi.resetForm();
  basicFormApi.setValues(formData.value || {});
  itemList.length = 0;

  if (formData.value?.items) {
    formData.value.items.forEach((item) => {
      itemList.push({
        inquiry_item_id: item.inquiry_item_id,
        material_type: item.material_type,
        model: item.model || '',
        brand: item.brand || '',
        pn: item.pn || '',
        spec: item.spec || '',
        unit: item.unit || '',
        contract_quantity: item.contract_quantity,
        contract_price: item.contract_price,
        contract_amount: item.contract_amount,
        remark: item.remark || '',
      });
    });
  }

  // 重新获取关联数据
  if (formData.value?.supplier_id) {
    await handleSupplierChange(Number(formData.value.supplier_id));
  }
  if (formData.value?.our_company_id) {
    await handleCompanyChange(Number(formData.value.our_company_id));
  }
  if (formData.value?.inquiry_id) {
    await handleInquiryChange(Number(formData.value.inquiry_id));
  }
}

const [Modal, modalApi] = useVbenModal({
  class: '!w-full',
  async onConfirm() {
    if (readOnly.value) {
      modalApi.close();
      return;
    }

    try {
      const { valid } = await basicFormApi.validate();
      if (!valid) {
        modalApi.lock(false);
        message.error('请检查必填项');
        return;
      }

      modalApi.lock();

      // 检查基本信息
      const basicData = await basicFormApi.getValues();
      if (!basicData.contract_no) {
        modalApi.lock(false);
        message.error('请输入合同编号');
        return;
      }
      if (!basicData.our_company_id) {
        modalApi.lock(false);
        message.error('请选择我方公司');
        return;
      }
      if (!basicData.supplier_id) {
        modalApi.lock(false);
        message.error('请选择供应商');
        return;
      }

      // 检查是否有合同明细
      if (itemList.length === 0) {
        modalApi.lock(false);
        message.error('请添加至少一个合同明细项');
        return;
      }

      // 检查明细项是否填写完整
      for (const item of itemList) {
        if (!item.material_type) {
          message.error('请填写物料类型');
          modalApi.lock(false);
          return;
        }
        if (!item.model) {
          message.error('请填写型号');
          modalApi.lock(false);
          return;
        }
        if (!item.brand) {
          message.error('请填写品牌');
          modalApi.lock(false);
          return;
        }
        if (!item.pn) {
          message.error('请填写PN号');
          modalApi.lock(false);
          return;
        }
        if (!item.unit) {
          message.error('请选择单位');
          modalApi.lock(false);
          return;
        }
        if (!item.spec) {
          message.error('请填写规格说明');
          modalApi.lock(false);
          return;
        }
        if (item.contract_quantity <= 0) {
          message.error('合同数量必须大于0');
          modalApi.lock(false);
          return;
        }
        if (item.contract_price < 0) {
          message.error('合同单价不能为负数');
          modalApi.lock(false);
          return;
        }
      }

      try {
        // 构建提交数据
        const data: CreatePurchaseContractParams = {
          contract_no: basicData.contract_no,
          inquiry_id: basicData.inquiry_id
            ? Number(basicData.inquiry_id)
            : undefined,
          project_id: basicData.project_id
            ? Number(basicData.project_id)
            : undefined,
          our_company_id: Number(basicData.our_company_id),
          supplier_id: Number(basicData.supplier_id),
          contract_title: basicData.contract_title || '',
          contract_type: basicData.contract_type || '',
          signing_date: basicData.signing_date,
          delivery_address: basicData.delivery_address || '',
          payment_terms: basicData.payment_terms || '',
          warranty_period: basicData.warranty_period || '',
          total_amount: formData.value.total_amount,
          items: itemList.map((item) => ({
            inquiry_item_id: item.inquiry_item_id,
            material_type: item.material_type,
            model: item.model || '',
            brand: item.brand || '',
            pn: item.pn || '',
            spec: item.spec || '',
            unit: item.unit || '',
            contract_quantity: item.contract_quantity,
            contract_price: item.contract_price,
            contract_amount: item.contract_amount,
            remark: item.remark || '',
          })),
        };

        // 显示确认弹窗
        showConfirmModal(data);
      } catch (error) {
        console.error('数据准备失败:', error);
        modalApi.lock(false);
      }
    } catch (error) {
      console.error('表单验证错误:', error);
      modalApi.lock(false);
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<
        PurchaseContract & { readOnly?: boolean }
      >();
      if (data) {
        readOnly.value = !!data.readOnly;

        if (data.readOnly === undefined) {
          formData.value = { ...data };
        } else {
          const { readOnly: _, ...restData } = data;
          formData.value = { ...restData };
        }

        basicFormApi.setValues(formData.value || {});

        // 初始化明细项
        itemList.length = 0;
        if (formData.value.items) {
          formData.value.items.forEach((item) => {
            itemList.push({
              inquiry_item_id: item.inquiry_item_id,
              material_type: item.material_type,
              model: item.model || '',
              brand: item.brand || '',
              pn: item.pn || '',
              spec: item.spec || '',
              unit: item.unit || '',
              contract_quantity: item.contract_quantity,
              contract_price: item.contract_price,
              contract_amount: item.contract_amount,
              remark: item.remark || '',
            });
          });
        }

        // 获取关联数据
        if (formData.value.supplier_id) {
          await handleSupplierChange(Number(formData.value.supplier_id));
        }
        if (formData.value.our_company_id) {
          await handleCompanyChange(Number(formData.value.our_company_id));
        }
        if (formData.value.inquiry_id) {
          await handleInquiryChange(Number(formData.value.inquiry_id));
        }
      } else {
        readOnly.value = false;
        formData.value = {
          our_company_id: null as unknown as number,
          supplier_id: null as unknown as number,
          total_amount: 0,
          contract_title: '',
          contract_type: '',
          signing_date: '',
          delivery_address: '',
          payment_terms: '',
          warranty_period: '',
          items: [],
        };
        basicFormApi.setValues(formData.value);
        itemList.length = 0;
      }
    }
  },
});

const activeKey = ref('basic');

// 二次确认弹窗
const confirmModalVisible = ref(false);
const confirmLoading = ref(false);
const pendingData = ref<CreatePurchaseContractParams | null>(null);

// 显示确认弹窗
function showConfirmModal(data: CreatePurchaseContractParams) {
  pendingData.value = data;
  confirmModalVisible.value = true;
}

// 确认提交
async function handleConfirmSubmit() {
  if (!pendingData.value) return;

  confirmLoading.value = true;
  try {
    // 创建或更新合同
    const result = await (formData.value?.id
      ? updatePurchaseContract(formData.value.id, {
          id: formData.value.id,
          ...pendingData.value,
        })
      : createPurchaseContract(pendingData.value));

    // 上传相关文件（如果有）
    if (fileUploaderRef.value && result?.id) {
      try {
        await fileUploaderRef.value.uploadFilesToServer(result.id);
      } catch (error) {
        console.error('文件上传失败:', error);
      }
    }

    confirmModalVisible.value = false;
    modalApi.close();
    emit('success');
  } catch (error) {
    console.error('提交失败:', error);
  } finally {
    confirmLoading.value = false;
    modalApi.lock(false);
  }
}

// 取消确认
function handleCancelConfirm() {
  confirmModalVisible.value = false;
  modalApi.lock(false);
  pendingData.value = null;
}
</script>

<template>
  <Modal :title="getTitle" width="1000px">
    <Tabs v-model:active-key="activeKey" class="mx-4">
      <Tabs.TabPane key="basic" tab="基本信息">
        <!-- 关联询价信息展示 -->
        <div
          v-if="inquiryData"
          class="mx-4 mb-6 rounded-lg border border-blue-100 bg-blue-50 p-4 shadow-sm"
        >
          <div class="mb-3 flex items-center">
            <div class="mr-2 h-5 w-1 rounded bg-blue-500"></div>
            <div class="text-base font-medium text-blue-700">关联询价信息</div>
          </div>
          <div class="grid grid-cols-1 gap-x-8 gap-y-3 text-sm md:grid-cols-2">
            <div class="flex items-center">
              <span class="mr-2 w-24 flex-shrink-0 font-semibold text-blue-700"
                >询价单号:</span
              >
              <span class="font-medium text-gray-700">{{
                inquiryData.inquiry_no
              }}</span>
            </div>
            <div class="flex items-center">
              <span class="mr-2 w-24 flex-shrink-0 font-semibold text-blue-700"
                >供应商:</span
              >
              <span class="text-gray-700">{{ inquiryData.supplier_name }}</span>
            </div>
            <div v-if="inquiryData.project_name" class="flex items-center">
              <span class="mr-2 w-24 flex-shrink-0 font-semibold text-blue-700"
                >所属项目:</span
              >
              <span class="text-gray-700">{{ inquiryData.project_name }}</span>
            </div>
          </div>
        </div>

        <!-- 提示信息 -->
        <div
          v-if="!inquiryData && !readOnly"
          class="mx-4 mb-6 rounded-lg border border-yellow-100 bg-yellow-50 p-4"
        >
          <div class="flex items-center text-yellow-700">
            <span class="mr-2 text-lg">💡</span>
            <span class="font-medium"
              >可以选择关联询价单自动填充明细，也可以手动创建合同</span
            >
          </div>
        </div>

        <BasicForm
          :disabled="readOnly"
          class="mx-4 mb-4"
          @change="handleFormFieldChange"
        />

        <div class="mx-4 mb-6">
          <div class="mb-2 text-base font-medium">附件上传</div>
          <FileUploader
            ref="fileUploaderRef"
            file-description="合同附件"
            module-type="purchase_contracts"
            :module-id="formData?.id"
            :disabled="readOnly"
            show-upload-list
            :max-count="5"
            multiple
            accept="*"
            list-type="text"
            compact
          >
            <Button v-if="!readOnly" type="primary" size="small">
              选择文件
            </Button>
          </FileUploader>
        </div>
      </Tabs.TabPane>

      <Tabs.TabPane key="items" tab="合同明细">
        <div class="mx-4">
          <div class="mb-4 flex items-center justify-between">
            <h3 class="text-lg font-medium">
              合同明细 ({{ itemList.length }}项)
            </h3>
            <div class="flex items-center gap-2">
              <div
                v-if="formData.total_amount > 0"
                class="text-sm text-blue-600"
              >
                合同总金额: ¥{{ formData.total_amount.toLocaleString() }}
              </div>
              <Button
                v-if="!readOnly"
                type="primary"
                size="small"
                @click="addNewItem"
              >
                添加明细
              </Button>
            </div>
          </div>

          <div
            v-if="itemList.length === 0"
            class="rounded border border-dashed border-gray-300 bg-gray-50 py-10 text-center text-gray-500"
          >
            <div class="mb-3">
              <div
                class="mb-3 inline-flex h-12 w-12 items-center justify-center rounded-full bg-gray-100"
              >
                <span class="text-xl text-gray-400">+</span>
              </div>
            </div>
            <div class="mb-1 text-base font-medium">暂无合同明细</div>
            <div class="text-sm text-gray-400">
              可以选择关联询价单自动加载，或手动添加明细项
            </div>
          </div>

          <div v-else>
            <div
              v-for="(item, index) in itemList"
              :key="index"
              class="mb-4 rounded-lg border border-gray-200 bg-white shadow-sm"
            >
              <!-- 紧凑头部 -->
              <div
                class="flex items-center justify-between border-b border-gray-100 px-4 py-3"
              >
                <div class="flex items-center gap-2">
                  <span
                    class="flex h-6 w-6 items-center justify-center rounded-full bg-blue-500 text-xs font-medium text-white"
                  >
                    {{ index + 1 }}
                  </span>
                  <span class="text-sm font-medium text-gray-800">
                    {{ item.material_type || '明细项目' }}
                    {{ item.model ? `- ${item.model}` : '' }}
                  </span>
                  <span
                    v-if="item.purchase_stats"
                    class="ml-2 rounded bg-orange-100 px-2 py-0.5 text-xs text-orange-700"
                  >
                    剩余{{ item.purchase_stats.unpurchased_quantity
                    }}{{ item.unit || '件' }}
                  </span>
                </div>
                <Button
                  v-if="!readOnly"
                  type="text"
                  danger
                  size="small"
                  @click="deleteContractItem(index)"
                >
                  <IconifyIcon icon="lucide:x" class="h-4 w-4" />
                </Button>
              </div>

              <!-- 内容区域 -->
              <div class="p-4">
                <!-- 产品信息 - 紧凑版 -->
                <div class="mb-4 grid grid-cols-2 gap-3 lg:grid-cols-4">
                  <div>
                    <label class="mb-1 block text-xs font-medium text-gray-700">
                      物料类型 <span class="text-red-500">*</span>
                    </label>
                    <input
                      v-model="item.material_type"
                      :disabled="readOnly"
                      class="w-full rounded border border-gray-300 px-2.5 py-2 text-sm focus:border-blue-500 focus:outline-none disabled:bg-gray-50"
                      placeholder="物料类型"
                    />
                  </div>
                  <div>
                    <label class="mb-1 block text-xs font-medium text-gray-700">
                      型号 <span class="text-red-500">*</span>
                    </label>
                    <input
                      v-model="item.model"
                      :disabled="readOnly"
                      class="w-full rounded border border-gray-300 px-2.5 py-2 text-sm focus:border-blue-500 focus:outline-none disabled:bg-gray-50"
                      placeholder="型号"
                    />
                  </div>
                  <div>
                    <label class="mb-1 block text-xs font-medium text-gray-700">
                      品牌 <span class="text-red-500">*</span>
                    </label>
                    <input
                      v-model="item.brand"
                      :disabled="readOnly"
                      class="w-full rounded border border-gray-300 px-2.5 py-2 text-sm focus:border-blue-500 focus:outline-none disabled:bg-gray-50"
                      placeholder="品牌"
                    />
                  </div>
                  <div>
                    <label class="mb-1 block text-xs font-medium text-gray-700">
                      PN号 <span class="text-red-500">*</span>
                    </label>
                    <input
                      v-model="item.pn"
                      :disabled="readOnly"
                      class="w-full rounded border border-gray-300 px-2.5 py-2 text-sm focus:border-blue-500 focus:outline-none disabled:bg-gray-50"
                      placeholder="PN号"
                    />
                  </div>
                </div>

                <!-- 采购统计信息 - 紧凑版 -->
                <div
                  v-if="item.purchase_stats"
                  class="mb-4 rounded border border-blue-200 bg-blue-50 p-3"
                >
                  <div class="mb-2 flex items-center justify-between">
                    <span class="text-xs font-medium text-blue-700"
                      >采购进度统计</span
                    >
                    <span class="text-xs text-gray-600">
                      {{ item.purchase_stats.purchased_quantity }}/{{
                        item.purchase_stats.inquiry_quantity
                      }}
                      ({{
                        Math.round(
                          (item.purchase_stats.purchased_quantity /
                            item.purchase_stats.inquiry_quantity) *
                            100,
                        )
                      }}%)
                    </span>
                  </div>
                  <div class="mb-3 h-1.5 w-full rounded-full bg-gray-200">
                    <div
                      class="h-1.5 rounded-full bg-blue-500 transition-all"
                      :style="{
                        width: `${Math.min((item.purchase_stats.purchased_quantity / item.purchase_stats.inquiry_quantity) * 100, 100)}%`,
                      }"
                    ></div>
                  </div>

                  <!-- 数量统计 -->
                  <div class="mb-2 grid grid-cols-3 gap-2 text-xs">
                    <div class="text-center">
                      <div class="font-medium text-gray-700">
                        {{ item.purchase_stats.inquiry_quantity }}
                      </div>
                      <div class="text-gray-500">询价数量</div>
                    </div>
                    <div class="text-center">
                      <div class="font-medium text-green-600">
                        {{ item.purchase_stats.purchased_quantity }}
                      </div>
                      <div class="text-gray-500">已采购数量</div>
                    </div>
                    <div class="text-center">
                      <div class="font-medium text-orange-600">
                        {{ item.purchase_stats.unpurchased_quantity }}
                      </div>
                      <div class="text-gray-500">未采购数量</div>
                    </div>
                  </div>

                  <!-- 金额统计 -->
                  <div class="grid grid-cols-3 gap-2 text-xs">
                    <div class="text-center">
                      <div class="font-medium text-gray-700">
                        ¥{{
                          item.purchase_stats.inquiry_budget.toLocaleString()
                        }}
                      </div>
                      <div class="text-gray-500">询价预算</div>
                    </div>
                    <div class="text-center">
                      <div class="font-medium text-green-600">
                        ¥{{
                          item.purchase_stats.purchased_amount.toLocaleString()
                        }}
                      </div>
                      <div class="text-gray-500">已采购金额</div>
                    </div>
                    <div class="text-center">
                      <div class="font-medium text-orange-600">
                        ¥{{
                          item.purchase_stats.unpurchased_budget.toLocaleString()
                        }}
                      </div>
                      <div class="text-gray-500">未采购预算</div>
                    </div>
                  </div>
                </div>

                <!-- 数量和价格 - 紧凑版 -->
                <div class="mb-4 grid grid-cols-2 gap-3 lg:grid-cols-4">
                  <div>
                    <label class="mb-1 block text-xs font-medium text-gray-700">
                      单位 <span class="text-red-500">*</span>
                    </label>
                    <input
                      v-model="item.unit"
                      :disabled="readOnly"
                      class="w-full rounded border border-gray-300 px-2.5 py-2 text-sm focus:border-blue-500 focus:outline-none disabled:bg-gray-50"
                      placeholder="单位"
                    />
                  </div>
                  <div>
                    <label class="mb-1 block text-xs font-medium text-gray-700">
                      采购数量 <span class="text-red-500">*</span>
                    </label>
                    <input
                      v-model.number="item.contract_quantity"
                      :disabled="readOnly"
                      class="w-full rounded border border-gray-300 px-2.5 py-2 text-sm focus:border-blue-500 focus:outline-none disabled:bg-gray-50"
                      placeholder="数量"
                      type="number"
                      min="1"
                      @input="calculateItemAmount(item)"
                    />
                  </div>
                  <div>
                    <label class="mb-1 block text-xs font-medium text-gray-700">
                      单价 <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                      <input
                        v-model.number="item.contract_price"
                        :disabled="readOnly"
                        class="w-full rounded border border-gray-300 px-2.5 py-2 pl-6 text-sm focus:border-blue-500 focus:outline-none disabled:bg-gray-50"
                        placeholder="0.00"
                        type="number"
                        min="0"
                        step="0.01"
                        @input="calculateItemAmount(item)"
                      />
                      <div
                        class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-2 text-xs text-gray-500"
                      >
                        ¥
                      </div>
                    </div>
                  </div>
                  <div>
                    <label class="mb-1 block text-xs font-medium text-gray-700"
                      >总价</label
                    >
                    <div class="relative">
                      <input
                        :value="
                          item.contract_amount
                            ? item.contract_amount.toFixed(2)
                            : '0.00'
                        "
                        disabled
                        class="w-full rounded border border-gray-200 bg-gray-50 px-2.5 py-2 pl-6 text-sm text-gray-600"
                        placeholder="自动计算"
                      />
                      <div
                        class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-2 text-xs text-gray-500"
                      >
                        ¥
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 规格和备注 - 紧凑版 -->
                <div class="grid grid-cols-1 gap-3 lg:grid-cols-2">
                  <div>
                    <label class="mb-1 block text-xs font-medium text-gray-700">
                      规格 <span class="text-red-500">*</span>
                    </label>
                    <textarea
                      v-model="item.spec"
                      :disabled="readOnly"
                      class="w-full resize-none rounded border border-gray-300 px-2.5 py-2 text-sm focus:border-blue-500 focus:outline-none disabled:bg-gray-50"
                      placeholder="产品规格、技术参数等..."
                      rows="1"
                    ></textarea>
                  </div>
                  <div>
                    <label class="mb-1 block text-xs font-medium text-gray-700"
                      >备注说明</label
                    >
                    <textarea
                      v-model="item.remark"
                      :disabled="readOnly"
                      class="w-full resize-none rounded border border-gray-300 px-2.5 py-2 text-sm focus:border-blue-500 focus:outline-none disabled:bg-gray-50"
                      placeholder="其他说明信息（可选）..."
                      rows="2"
                    ></textarea>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Tabs.TabPane>
    </Tabs>

    <template #prepend-footer>
      <div v-if="!readOnly" class="flex-auto">
        <Button type="primary" danger @click="() => resetForm()"> 重置 </Button>
      </div>
    </template>
  </Modal>

  <!-- 确认提交弹窗 -->
  <AModal
    v-model:open="confirmModalVisible"
    title="确认提交合同"
    width="700px"
    :confirm-loading="confirmLoading"
    @ok="handleConfirmSubmit"
    @cancel="handleCancelConfirm"
  >
    <div class="space-y-6">
      <!-- 基本信息 -->
      <div class="rounded border border-blue-200 bg-blue-50 p-4">
        <div class="mb-3 flex items-center">
          <div class="mr-2 h-4 w-1 rounded bg-blue-500"></div>
          <h3 class="text-sm font-semibold text-blue-800">合同基本信息</h3>
        </div>
        <div class="grid grid-cols-2 gap-2 text-sm text-blue-700">
          <div>
            <span class="font-medium">合同编号:</span>
            {{ pendingData?.contract_no }}
          </div>
          <div>
            <span class="font-medium">合同标题:</span>
            {{ pendingData?.contract_title }}
          </div>
          <div>
            <span class="font-medium">合同类型:</span>
            {{ pendingData?.contract_type }}
          </div>
          <div>
            <span class="font-medium">质保期限:</span>
            {{ pendingData?.warranty_period }}
          </div>
          <div>
            <span class="font-medium">合同金额:</span>
            ¥{{ pendingData?.total_amount?.toLocaleString() }}
          </div>
        </div>
      </div>

      <!-- 供应商信息 -->
      <div
        v-if="supplierData"
        class="rounded border border-green-200 bg-green-50 p-4"
      >
        <div class="mb-3 flex items-center">
          <div class="mr-2 h-4 w-1 rounded bg-green-500"></div>
          <h3 class="text-sm font-semibold text-green-800">供应商信息</h3>
        </div>
        <div class="grid grid-cols-2 gap-2 text-sm text-green-700">
          <div>
            <span class="font-medium">供应商名称:</span>
            {{ supplierData.supplier_name }}
          </div>
          <div v-if="supplierData.contact_person">
            <span class="font-medium">联系人:</span>
            {{ supplierData.contact_person }}
          </div>
          <div v-if="supplierData.contact_phone">
            <span class="font-medium">联系电话:</span>
            {{ supplierData.contact_phone }}
          </div>
          <div v-if="supplierData.contact_email">
            <span class="font-medium">联系邮箱:</span>
            {{ supplierData.contact_email }}
          </div>
        </div>
      </div>

      <!-- 合同明细 -->
      <div>
        <div class="mb-3 flex items-center">
          <div class="mr-2 h-4 w-1 rounded bg-blue-500"></div>
          <h3 class="text-sm font-semibold text-gray-800">
            合同明细 ({{ itemList.length }}项)
          </h3>
        </div>
        <div class="space-y-3">
          <div
            v-for="(item, index) in itemList"
            :key="index"
            class="rounded border border-gray-200 bg-gray-50 p-3"
          >
            <div class="mb-2 flex items-center">
              <div
                class="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 text-xs font-bold text-blue-800"
              >
                {{ index + 1 }}
              </div>
              <span class="text-sm font-medium text-gray-800">
                {{ item.material_type }}
              </span>
            </div>
            <div class="ml-7 grid grid-cols-2 gap-2 text-xs text-gray-600">
              <div v-if="item.model">
                <span class="font-medium">型号:</span>
                {{ item.model }}
              </div>
              <div v-if="item.brand">
                <span class="font-medium">品牌:</span>
                {{ item.brand }}
              </div>
              <div>
                <span class="font-medium">合同数量:</span>
                {{ item.contract_quantity }} {{ item.unit || '' }}
              </div>
              <div>
                <span class="font-medium">合同单价:</span>
                ¥{{ item.contract_price?.toFixed(2) }}
              </div>
              <div class="col-span-2">
                <span class="font-medium">合同总价:</span>
                ¥{{ item.contract_amount?.toFixed(2) }}
              </div>
              <div v-if="item.pn" class="col-span-2">
                <span class="font-medium">PN:</span> {{ item.pn }}
              </div>
              <div v-if="item.remark" class="col-span-2">
                <span class="font-medium">备注:</span> {{ item.remark }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="border-t pt-4 text-center text-sm text-gray-500">
        请确认以上信息无误后点击确定提交
      </div>
    </div>
  </AModal>
</template>

<style scoped>
/* 禁用数字输入框的滚轮行为 */
input[type='number'] {
  appearance: textfield;
}

input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  margin: 0;
  appearance: none;
}

/* 确保数字输入框在获得焦点时也不响应滚轮 */
input[type='number']:focus {
  appearance: textfield;
}
</style>
