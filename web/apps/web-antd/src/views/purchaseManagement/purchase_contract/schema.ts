import type { VbenFormSchema } from '#/adapter/form';

import { z } from '#/adapter/form';
import { getCompanyListApi } from '#/api/core/purchase/company';
import { getProjectListApi } from '#/api/core/purchase/project';
import { getPurchaseInquiryList } from '#/api/core/purchase/purchase_inquiry';
import { getSupplierListApi } from '#/api/core/purchase/supplier';

import { CONTRACT_TYPE_OPTIONS, FLOW_STATUS_OPTIONS } from './constants';

// 定义全局窗口接口
declare global {
  interface Window {
    handleInquiryChange?: (id: number) => void;
    handleSupplierChange?: (id: number) => void;
    handleCompanyChange?: (id: number) => void;
  }
}

/**
 * 获取合同基本信息表单的字段配置
 */
export function useBasicSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Select',
      componentProps: {
        options: CONTRACT_TYPE_OPTIONS,
        placeholder: '请选择合同类型',
        class: 'w-full',
        showSearch: true,
        filterOption: (
          input: string,
          option: { label: string; value: string },
        ) => {
          return (
            option.label.toLowerCase().includes(input.toLowerCase()) ||
            option.value.toLowerCase().includes(input.toLowerCase())
          );
        },
      },
      fieldName: 'contract_type',
      label: '合同类型',
      rules: z.string().min(1, '请选择合同类型'),
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入合同标题',
        class: 'w-full',
      },
      fieldName: 'contract_title',
      label: '合同标题',
      rules: z.string().min(1, '请输入合同标题'),
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入合同编号',
        class: 'w-full',
      },
      fieldName: 'contract_no',
      label: '合同编号',
      rules: z.string().min(1, '请输入合同编号'),
    },
    {
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        placeholder: '请选择合同签订日期',
        valueFormat: 'YYYY-MM-DDT00:00:00Z',
        class: 'w-full',
      },
      fieldName: 'signing_date',
      label: '合同签订日期',
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        showSearch: true,
        filterOption: true,
        allowClear: false,
        api: async () => {
          const res = await getCompanyListApi({
            page: 1,
            pageSize: 100,
          });
          return res.list.map((item) => ({
            label: item.company_name,
            value: item.id,
          }));
        },
        class: 'w-full',
        labelField: 'label',
        valueField: 'value',
        placeholder: '请选择我方公司',
        onChange: (value: number) => {
          if (window.handleCompanyChange && value) {
            window.handleCompanyChange(value);
          }
        },
      },
      fieldName: 'our_company_id',
      label: '我方公司',
      rules: 'selectRequired',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        showSearch: true,
        filterOption: (
          input: string,
          option: { label: string; value: number },
        ) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
        api: async () => {
          const res = await getPurchaseInquiryList({
            page: 1,
            pageSize: 100,
            status: 'approved',
          });
          return res.list.map((item) => ({
            label: `${item.inquiry_no} - ${item.supplier_name}`,
            value: item.id,
          }));
        },
        class: 'w-full',
        labelField: 'label',
        valueField: 'value',
        placeholder: '请选择关联的询价单',
        onChange: (_value: number) => {
          // 移除直接调用，让 watch 监听器处理，避免重复请求
          // 使用 _value 前缀避免 TypeScript 未使用参数警告
        },
      },
      fieldName: 'inquiry_id',
      label: '关联询价单',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: false,
        showSearch: true,
        filterOption: (
          input: string,
          option: { label: string; value: number },
        ) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
        api: async () => {
          const res = await getSupplierListApi({
            page: 1,
            pageSize: 100,
          });
          return res.list.map((item) => ({
            label: item.supplier_name,
            value: item.id,
          }));
        },
        class: 'w-full',
        labelField: 'label',
        valueField: 'value',
        placeholder: '请选择供应商',
        onChange: (value: number) => {
          if (window.handleSupplierChange && value) {
            window.handleSupplierChange(value);
          }
        },
      },
      fieldName: 'supplier_id',
      label: '对方公司名称',
      rules: 'selectRequired',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        showSearch: true,
        filterOption: true,
        api: async () => {
          const res = await getProjectListApi({
            page: 1,
            pageSize: 100,
          });
          return res.list.map((item) => ({
            label: item.project_name,
            value: item.id,
          }));
        },
        class: 'w-full',
        labelField: 'label',
        valueField: 'value',
        placeholder: '请选择项目',
      },
      fieldName: 'project_id',
      label: '所属项目',
      rules: 'selectRequired',
    },
    {
      component: 'Textarea',
      componentProps: {
        maxLength: 500,
        placeholder: '请输入对方合同收件信息',
        rows: 2,
        showCount: true,
        class: 'w-full',
      },
      fieldName: 'delivery_address',
      label: '对方合同收件信息',
      rules: z.string().min(1, '请输入对方合同收件信息'),
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入付款条款',
        class: 'w-full',
      },
      fieldName: 'payment_terms',
      label: '付款条款',
      rules: z.string().min(1, '请输入付款条款'),
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入质保期限',
        class: 'w-full',
      },
      fieldName: 'warranty_period',
      label: '质保期限',
      rules: z.string().min(1, '请输入质保期限'),
    },
  ];
}

/**
 * 搜索表单字段配置
 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'contract_no',
      label: '合同编号',
      componentProps: {
        placeholder: '请输入合同编号',
      },
    },
    {
      component: 'Input',
      fieldName: 'inquiry_no',
      label: '询价单号',
      componentProps: {
        placeholder: '请输入询价单号',
      },
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        showSearch: true,
        filterOption: true,
        api: async () => {
          const res = await getSupplierListApi({
            page: 1,
            pageSize: 100,
          });
          return res.list.map((item) => ({
            label: item.supplier_name,
            value: item.id,
          }));
        },
        labelField: 'label',
        valueField: 'value',
        placeholder: '请选择供应商',
      },
      fieldName: 'supplier_id',
      label: '供应商',
    },

    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: FLOW_STATUS_OPTIONS,
      },
      fieldName: 'status',
      label: '状态',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        showSearch: true,
        filterOption: true,
        api: async () => {
          const res = await getProjectListApi({
            page: 1,
            pageSize: 100,
          });
          return res.list.map((item) => ({
            label: item.project_name,
            value: item.id,
          }));
        },
        labelField: 'label',
        valueField: 'value',
        placeholder: '请选择项目',
      },
      fieldName: 'project_id',
      label: '所属项目',
    },
    {
      component: 'RangePicker',
      fieldName: 'create_time',
      label: '创建时间',
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DDT00:00:00Z',
      },
    },
  ];
}

/**
 * 获取合同明细项表单的字段配置
 */
export function useItemsSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入物料类型',
        allowClear: true,
      },
      fieldName: 'product_name',
      label: '物料类型',
      rules: z.string().min(1, '请输入物料类型'),
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入型号',
        allowClear: true,
      },
      fieldName: 'model',
      label: '型号',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入品牌',
        allowClear: true,
      },
      fieldName: 'brand',
      label: '品牌',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: 'PN',
        allowClear: true,
      },
      fieldName: 'pn',
      label: 'PN',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入规格',
      },
      fieldName: 'spec',
      label: '规格',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入单位',
      },
      fieldName: 'unit',
      label: '单位',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 1,
        placeholder: '请输入合同数量',
      },
      fieldName: 'contract_quantity',
      label: '合同数量',
      rules: z.number().min(1, '合同数量必须大于0'),
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        placeholder: '请输入合同单价',
      },
      fieldName: 'contract_price',
      label: '合同单价',
      rules: z.number().min(0, '合同单价不能为负数'),
    },
    {
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        placeholder: '请选择预计交货日期',
        valueFormat: 'YYYY-MM-DDT00:00:00Z',
      },
      fieldName: 'delivery_date',
      label: '预计交货日期',
    },
    {
      component: 'Textarea',
      componentProps: {
        maxLength: 200,
        rows: 2,
        showCount: true,
        placeholder: '请输入备注信息',
      },
      fieldName: 'remark',
      label: '备注',
    },
  ];
}

/**
 * 获取审批表单字段配置
 */
export function useApprovalSchema(): VbenFormSchema[] {
  return [
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '批准', value: 'approve' },
          { label: '拒绝', value: 'reject' },
        ],
        optionType: 'button',
      },
      fieldName: 'action',
      label: '审批决定',
      rules: z.string().min(1, '请选择审批决定'),
    },
    {
      component: 'Textarea',
      componentProps: {
        maxLength: 300,
        placeholder: '请输入审批意见',
        rows: 4,
        showCount: true,
      },
      fieldName: 'comments',
      label: '审批意见',
      rules: z.string().min(1, '请输入审批意见'),
    },
  ];
}
