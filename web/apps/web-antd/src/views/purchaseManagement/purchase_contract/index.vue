<script lang="ts" setup>
import type {
  PurchaseContract,
  PurchaseContractQuery,
} from '#/api/core/purchase/purchase_contract';

import { nextTick, ref } from 'vue';
import { useRouter } from 'vue-router';

import { AccessControl } from '@vben/access';
import { Page, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { Button, Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getPurchaseContractList } from '#/api/core/purchase/purchase_contract';

import { CONTRACT_TYPE_CONFIG, FLOW_STATUS_CONFIG } from './constants';
import { useColumns, useItemColumns } from './data';
import Form from './modules/form.vue';
import { useGridFormSchema } from './schema';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

const router = useRouter();

// 合同明细模态框相关数据
const detailModalVisible = ref(false);
const currentItems = ref<any[]>([]);
const currentContractNo = ref('');

// 合同明细表格
const [DetailGrid, detailGridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useItemColumns(),
    height: 'auto',
    maxHeight: 500,
    pagerConfig: {
      enabled: false,
    },
    proxyConfig: {
      autoLoad: false,
      ajax: {
        query: async () => {
          return {
            items: currentItems.value,
            total: currentItems.value.length,
          };
        },
      },
    },
    toolbarConfig: {
      enabled: false,
    },
  },
});

/**
 * 显示合同明细
 */
async function showItemsDetail(row: PurchaseContract) {
  if (row.items && row.items.length > 0) {
    currentItems.value = row.items;
    currentContractNo.value = row.contract_no || '';
    detailModalVisible.value = true;

    // 等待模态框打开后加载数据
    await nextTick();
    if (detailGridApi && detailGridApi.grid) {
      await detailGridApi.grid.commitProxy('query');
    }
  }
}

/**
 * 创建新合同
 */
function onCreate() {
  formModalApi.setData(null).open();
}

/**
 * 查看合同详情
 */
function onView(row: PurchaseContract) {
  router.push(`/purchase-management/purchase-contract/detail/${row.id}`);
}

// 定义分页参数类型
interface PageParams {
  currentPage: number;
  pageSize: number;
}

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['create_time', ['start_date', 'end_date']]],
    schema: useGridFormSchema(),
    submitOnChange: false,
    submitOnEnter: true,
  },
  gridOptions: {
    columns: useColumns(),
    exportConfig: {},
    height: 'auto',
    pagerConfig: {
      enabled: true,
    },
    proxyConfig: {
      ajax: {
        query: async (
          { page }: { page: PageParams },
          formValues: Record<string, any>,
        ) => {
          const params: PurchaseContractQuery = {
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          };

          const res = await getPurchaseContractList(params);

          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    toolbarConfig: {
      custom: true,
      export: true,
      refresh: true,
      refreshOptions: { code: 'query' },
      search: true,
      zoom: true,
      zoomOptions: {},
    },
  },
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid>
      <template #toolbar-tools>
        <AccessControl
          :codes="['Purchase:PurchaseContract:Create']"
          type="code"
        >
          <Button type="primary" @click="onCreate">
            <Plus class="size-5" />
            创建合同
          </Button>
        </AccessControl>
      </template>

      <!-- 工作流状态插槽 -->
      <template #status="{ row }">
        <Tag :color="FLOW_STATUS_CONFIG[row.status as string]?.color">
          {{ FLOW_STATUS_CONFIG[row.status as string]?.text || row.status }}
        </Tag>
      </template>

      <!-- 合同类型插槽 -->
      <template #contract_type="{ row }">
        <Tag :color="CONTRACT_TYPE_CONFIG[row.contract_type as string]?.color">
          {{
            CONTRACT_TYPE_CONFIG[row.contract_type as string]?.text ||
            row.contract_type
          }}
        </Tag>
      </template>

      <!-- 合同明细按钮插槽 -->
      <template #items_action="{ row }">
        <Button
          type="link"
          :disabled="!row.items || row.items.length === 0"
          @click="showItemsDetail(row)"
        >
          查看明细({{ row.items?.length || 0 }})
        </Button>
      </template>

      <!-- 操作列插槽 -->
      <template #operate="{ row }">
        <Button type="link" @click="onView(row)">查看</Button>
      </template>
    </Grid>

    <!-- 合同明细模态框 -->
    <Modal
      v-model:open="detailModalVisible"
      title="合同明细"
      :width="1400"
      :footer="null"
      :destroy-on-close="true"
      class="contract-detail-modal"
    >
      <div class="contract-detail-header">
        <div
          class="flex items-center justify-between rounded-lg bg-blue-50 p-4"
        >
          <div class="flex items-center space-x-3">
            <div
              class="flex h-10 w-10 items-center justify-center rounded-full bg-blue-500"
            >
              <span class="font-semibold text-white">合</span>
            </div>
            <div>
              <div class="text-lg font-semibold text-blue-900">
                合同编号: {{ currentContractNo }}
              </div>
              <div class="text-sm text-blue-600">
                共 {{ currentItems.length }} 个明细项
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <span class="text-sm text-blue-600">总计金额:</span>
            <span class="text-lg font-bold text-blue-800">
              ¥{{
                currentItems
                  .reduce((sum, item) => sum + (item.contract_amount || 0), 0)
                  .toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })
              }}
            </span>
          </div>
        </div>
      </div>

      <div class="contract-detail-table">
        <DetailGrid />
      </div>
    </Modal>
  </Page>
</template>

<style lang="less" scoped>
.contract-detail-modal {
  :deep(.ant-modal-content) {
    border-radius: 12px;
    overflow: hidden;
  }

  :deep(.ant-modal-header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: none;
    padding: 16px 24px;

    .ant-modal-title {
      color: white;
      font-weight: 600;
      font-size: 16px;
    }
  }

  :deep(.ant-modal-close) {
    color: white;

    &:hover {
      color: rgba(255, 255, 255, 0.8);
    }
  }

  :deep(.ant-modal-body) {
    padding: 0;
  }
}

.contract-detail-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.contract-detail-table {
  padding: 0 24px 24px;

  :deep(.vxe-table--body-wrapper) {
    max-height: 500px;
  }

  :deep(.vxe-table) {
    .vxe-header--row {
      background: #f8fafc;

      .vxe-header--column {
        background: #f8fafc;
        border-bottom: 2px solid #e2e8f0;
        font-weight: 600;
        color: #374151;
        font-size: 13px;
      }
    }

    .vxe-body--row {
      &:nth-child(even) {
        background: #fdfdfd;
      }

      &:hover {
        background: #f8fafc !important;
      }

      .vxe-body--column {
        border-bottom: 1px solid #f1f5f9;
        vertical-align: middle;
      }
    }
  }
}
</style>
