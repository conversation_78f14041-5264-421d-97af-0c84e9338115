import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { PurchaseContract } from '#/api/core/purchase/purchase_contract';

/**
 * 获取采购合同列表表格列配置
 */
export function useColumns(): VxeTableGridOptions<PurchaseContract>['columns'] {
  return [
    {
      field: 'contract_no',
      title: '合同编号',
      width: 180,
      fixed: 'left',
    },
    {
      field: 'contract_title',
      title: '合同标题',
      width: 200,
    },
    {
      field: 'inquiry_no',
      title: '关联询价单号',
      width: 160,
    },
    {
      field: 'status',
      title: '状态',
      width: 120,
      slots: { default: 'status' },
    },

    {
      field: 'our_company_name',
      title: '我方公司名称',
      width: 150,
    },
    {
      field: 'supplier_name',
      title: '对方公司名称',
      width: 150,
    },
    {
      field: 'project_name',
      title: '所属项目',
      width: 150,
    },
    {
      field: 'contract_type',
      title: '合同类型',
      width: 100,
      slots: { default: 'contract_type' },
    },
    {
      field: 'items',
      title: '合同明细',
      width: 120,
      slots: { default: 'items_action' },
    },
    {
      field: 'total_amount',
      title: '合同金额',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'signing_date',
      title: '签订日期',
      formatter: 'formatDate',
      width: 120,
    },
    {
      field: 'warranty_period',
      title: '质保期限',
      width: 120,
    },
    {
      field: 'creator_name',
      title: '创建人',
      width: 100,
    },
    {
      field: 'created_at',
      title: '创建时间',
      formatter: 'formatDateTime',
      width: 180,
    },
    {
      field: 'updated_at',
      title: '更新时间',
      formatter: 'formatDateTime',
      width: 180,
      visible: false,
    },
    {
      title: '操作',
      field: 'operation',
      fixed: 'right',
      width: 180,
      slots: { default: 'operate' },
    },
  ];
}

/**
 * 获取合同明细表格列配置
 */
export function useItemColumns(): VxeTableGridOptions<any>['columns'] {
  return [
    {
      field: 'material_type',
      title: '物料类型',
      width: 200,
      fixed: 'left',
    },
    {
      field: 'model',
      title: '型号',
      width: 150,
    },
    {
      field: 'brand',
      title: '品牌',
      width: 120,
    },
    {
      field: 'pn',
      title: 'PN',
      width: 150,
    },
    {
      field: 'spec',
      title: '规格',
      minWidth: 200,
    },
    {
      field: 'unit',
      title: '单位',
      width: 80,
    },
    {
      field: 'contract_quantity',
      title: '合同数量',
      width: 100,
    },
    {
      field: 'contract_price',
      title: '合同单价',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'contract_amount',
      title: '合同总价',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },

    {
      field: 'remark',
      title: '备注',
      minWidth: 150,
    },
  ];
}

/**
 * 获取合同统计表格列配置
 */
export function useContractStatsColumns(): VxeTableGridOptions<any>['columns'] {
  return [
    {
      field: 'supplier_name',
      title: '供应商名称',
      width: 200,
    },
    {
      field: 'contract_count',
      title: '合同数量',
      width: 100,
    },
    {
      field: 'total_amount',
      title: '合同总金额',
      width: 150,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'execution_rate',
      title: '执行完成率',
      width: 120,
      formatter: ({ cellValue }) => `${(cellValue * 100).toFixed(1)}%`,
    },
    {
      field: 'avg_amount',
      title: '平均合同金额',
      width: 150,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
  ];
}
