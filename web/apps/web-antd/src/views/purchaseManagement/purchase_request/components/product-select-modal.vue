<script setup lang="ts">
import type { PurchaseRequestItem } from '#/api/core/purchase/purchase_request';
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { ref } from 'vue';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getProductListApi, getMaterialTypesApi, getBrandsApi, getSpecsApi } from '#/api/core/cmdb/product/product';

const emit = defineEmits(['confirm', 'cancel']);

const searchLoading = ref(false);
const selectedRowKeys = ref<(number | string)[]>([]);
const selectedProducts = ref<PurchaseRequestItem[]>([]);

// 设备搜索表单配置
const productSearchFormOptions: VbenFormProps = {
  collapsed: false,
  collapsedRows: 2,
  schema: [
    
    {
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择物料类型',
        api: async () => {
          const types = await getMaterialTypesApi();
          return types.map(type => ({
            label: type,
            value: type,
          }));
        },
        allowClear: true,
        showSearch: true,
        filterOption: true,
      },
      fieldName: 'material_type',
      label: '物料类型',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择品牌',
        api: async () => {
          const brands = await getBrandsApi();
          return brands.map(brand => ({
            label: brand,
            value: brand,
          }));
        },
        allowClear: true,
        showSearch: true,
        filterOption: true,
      },
      fieldName: 'brand',
      label: '品牌',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择规格',
        api: async () => {
          try {
            const specs = await getSpecsApi();
            return specs.map(spec => ({
              label: spec,
              value: spec,
            }));
          } catch (error) {
            console.error('获取规格失败:', error);
            return [];
          }
        },
        allowClear: true,
        showSearch: true,
        filterOption: true,
      },
      fieldName: 'spec',
      label: '规格',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入型号' },
      fieldName: 'model',
      label: '型号',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入PN号' },
      fieldName: 'pn',
      label: 'PN号',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: true,
};

// 产品搜索表格配置
const productSearchGridOptions: VxeGridProps = {
  columns: [
    { align: 'center', type: 'checkbox', width: 40 },
    { field: 'id', title: 'ID' },
    { field: 'material_type', title: '物料类型' },
    { field: 'spec', title: '规格' },
    { field: 'model', title: '型号' },
    { field: 'brand', title: '品牌' },
    { field: 'pn', title: 'PN号' },    
    { field: 'unit', title: '单位' },
  ],
  showOverflow: true,
  // height: 'auto',
  rowConfig: { isHover: true },
  checkboxConfig: {
    highlight: true,
  },
  keepSource: true,
  pagerConfig: {
    enabled: true,
    pageSize: 10,
    pageSizes: [10, 20, 50, 100],
  },
  toolbarConfig: {
    refresh: true,
    zoom: true,
    custom: true,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        searchLoading.value = true;
        try {
          // 这里需要根据实际API调整
          const { list, total } = await getProductListApi({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return {
            items: list || [],
            total: total || 0,
          };
        } catch (error: any) {
          message.error(`搜索产品失败: ${error.message || '未知错误'}`);
          return { items: [], total: 0 };
        } finally {
          searchLoading.value = false;
        }
      },
    },
  },
};

// 搜索产品
const [ProductSearchGrid, productSearchGridApi] = useVbenVxeGrid({
  gridOptions: productSearchGridOptions,
  formOptions: productSearchFormOptions,
});

// 初始加载数据
function initData() {
  selectedRowKeys.value = [];
  selectedProducts.value = [];
  
  // 初始加载数据
  setTimeout(() => {
    if (productSearchGridApi && productSearchGridApi.grid) {
      try {
        productSearchGridApi.reload();
      } catch (error) {
        console.error('加载产品列表失败:', error);
        message.error('加载产品列表失败，请重试');
      }
    }
  }, 100);
}

// 确认选择
function handleConfirm() {
  if (productSearchGridApi && productSearchGridApi.grid) {
    try {
      // 获取选中的行数据
      const records = productSearchGridApi.grid.getCheckboxRecords();

      // 首先检查是否有选择产品
      if (!records || records.length === 0) {
        message.warning('请选择至少一个产品');
        return false;
      }

      // 转换为采购明细项
      const items: PurchaseRequestItem[] = records.map((product: any) => ({
        material_type: product.material_type || '',
        model: product.model || '',
        brand: product.brand || '',
        pn: product.pn || '',
        spec: product.spec || '',
        unit: product.unit || '',
        quantity: 1,
        remark: '',
      }));

      // 发送选中的产品回上级组件
      emit('confirm', items);
      return true;
    } catch (error) {
      console.error('选择产品失败:', error);
      message.error('选择产品失败，请重试');
      return false;
    }
  } else {
    message.error('获取选中产品失败');
    return false;
  }
}

// 取消选择
function handleCancel() {
  emit('cancel');
}

defineExpose({
  initData,
  handleConfirm,
  handleCancel,
});
</script>

<template>
  <div class="product-select-modal">
    <ProductSearchGrid>
      <template #empty>
        <div class="py-4 text-center text-gray-500">
          {{ searchLoading ? '加载中...' : '暂无数据' }}
        </div>
      </template>
    </ProductSearchGrid>
  </div>
</template> 