<script lang="ts" setup>
import type { PurchaseRequestItem } from '#/api/core/purchase/purchase_request';

import { ref } from 'vue';

import { Modal } from 'ant-design-vue';
import { Button } from 'ant-design-vue';

import ProductSelectModal from './product-select-modal.vue';

const emit = defineEmits(['select']);
const productSelectModalVisible = ref(false);
const productSelectRef = ref<InstanceType<typeof ProductSelectModal> | null>(null);

// 打开产品选择模态框
function openProductSelectModal() {
  productSelectModalVisible.value = true;
  // 初始化数据
  setTimeout(() => {
    if (productSelectRef.value) {
      productSelectRef.value.initData();
    }
  }, 100);
}

// 处理模态框确认
function handleModalOk() {
  if (productSelectRef.value) {
    const result = productSelectRef.value.handleConfirm();
    // 如果确认成功，模态框将在 confirm 事件处理中关闭
    // 如果失败，不关闭模态框
    return result;
  }
  return true;
}

// 处理产品选择确认
function handleProductSelected(items: PurchaseRequestItem[]) {
  emit('select', items);
  productSelectModalVisible.value = false;
}

// 关闭模态框
function handleCancel() {
  productSelectModalVisible.value = false;
  if (productSelectRef.value) {
    productSelectRef.value.handleCancel();
  }
}

// 向父组件暴露方法
defineExpose({
  open: openProductSelectModal,
});
</script>

<template>
  <div>
    <Modal
      v-model:open="productSelectModalVisible"
      title="选择产品"
      width="1000px"
      :body-style="{ height: '700px', overflow: 'auto' }"
      @ok="handleModalOk"
      @cancel="handleCancel"
      okText="确认选择"
      cancelText="取消"
    >
      <ProductSelectModal
        ref="productSelectRef"
        @confirm="handleProductSelected"
        @cancel="handleCancel"
      />
    </Modal>
    
    <Button type="primary" size="middle" @click="openProductSelectModal" class="h-9 px-4 rounded flex items-center gap-1.5">
      <span class="text-sm font-medium">+</span>
      <span class="text-sm">从产品库选择</span>
    </Button>
  </div>
</template>

<style scoped>
.product-select-modal {
  width: 900px;
}
.product-select-modal :deep(.ant-modal-body) {
  height: 650px;
  overflow: auto;
}
</style> 