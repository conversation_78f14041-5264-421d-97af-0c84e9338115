<script lang="ts" setup>
import type { ModuleFileListResult } from '#/api/core/upload';

import { FileText, IconDownload, IconView } from '@vben/icons';

import { Badge, Button, Card, Spin } from 'ant-design-vue';

import { formatToDateTime } from '#/utils/common/time';

defineProps<{
  attachments: ModuleFileListResult[];
  loading: boolean;
}>();

const emit = defineEmits<{
  (e: 'preview', file: ModuleFileListResult): void;
  (e: 'download', file: ModuleFileListResult): void;
}>();

// 处理文件预览
const handlePreview = (file: ModuleFileListResult) => {
  emit('preview', file);
};

// 处理文件下载
const handleDownload = (file: ModuleFileListResult) => {
  emit('download', file);
};

// 文件类型正则表达式（修复捕获组问题）
const imageRegex = /\.(?:jpg|jpeg|png|gif|webp|bmp)$/i;
const pdfRegex = /\.pdf$/i;
const docRegex = /\.(?:doc|docx)$/i;
const excelRegex = /\.(?:xls|xlsx)$/i;
const pptRegex = /\.(?:ppt|pptx)$/i;

// 获取文件图标
const getFileIcon = (fileName: string) => {
  if (imageRegex.test(fileName)) return '📸';
  if (pdfRegex.test(fileName)) return '📄';
  if (docRegex.test(fileName)) return '📝';
  if (excelRegex.test(fileName)) return '📊';
  if (pptRegex.test(fileName)) return '📽️';
  return '📎';
};

// 获取文件类型样式
const getFileTypeClass = (fileName: string) => {
  if (imageRegex.test(fileName)) return 'bg-pink-100 text-pink-600';
  if (pdfRegex.test(fileName)) return 'bg-red-100 text-red-600';
  if (docRegex.test(fileName)) return 'bg-blue-100 text-blue-600';
  if (excelRegex.test(fileName)) return 'bg-green-100 text-green-600';
  if (pptRegex.test(fileName)) return 'bg-orange-100 text-orange-600';
  return 'bg-gray-100 text-gray-600';
};
</script>

<template>
  <Card
    v-if="attachments && attachments.length > 0"
    class="overflow-hidden rounded-xl border-0 bg-white shadow-lg"
    :bordered="false"
  >
    <template #title>
      <div class="flex items-center gap-3 py-2">
        <div
          class="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-cyan-500 to-blue-600"
        >
          <FileText class="h-4 w-4 text-white" />
        </div>
        <span class="text-lg font-semibold text-gray-800">相关附件</span>
        <Badge
          :count="attachments.length"
          class="rounded-full bg-blue-500 px-2 py-1 text-xs font-medium text-white"
        />
      </div>
    </template>

    <div
      class="rounded-lg bg-gradient-to-br from-blue-50/50 to-indigo-50/50 p-4"
    >
      <Spin :spinning="loading">
        <!-- 有附件时显示列表 -->
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div
            v-for="(item, index) in attachments"
            :key="index"
            class="flex items-center justify-between rounded-lg bg-white p-4 shadow-sm transition-shadow duration-200 hover:shadow-md"
          >
            <div class="flex min-w-0 flex-1 items-center">
              <div
                class="mr-3 flex h-10 w-10 items-center justify-center rounded-md"
                :class="getFileTypeClass(item.file_name)"
              >
                <span class="text-lg">{{ getFileIcon(item.file_name) }}</span>
              </div>
              <div class="min-w-0 flex-1">
                <div class="truncate text-sm font-medium text-gray-900">
                  {{ item.file_name }}
                </div>
                <div class="mt-1 flex items-center text-xs text-gray-500">
                  <span>{{ (item.file_size / 1024).toFixed(2) }} KB</span>
                  <span class="mx-2">•</span>
                  <span>{{ formatToDateTime(item.created_at) }}</span>
                </div>
              </div>
            </div>
            <div class="ml-3 flex items-center gap-2">
              <Button
                type="text"
                size="small"
                class="text-blue-500 hover:bg-blue-50 hover:text-blue-600"
                @click="handlePreview(item)"
                title="预览"
              >
                <IconView />
              </Button>
              <Button
                type="text"
                size="small"
                class="text-green-500 hover:bg-green-50 hover:text-green-600"
                @click="handleDownload(item)"
                title="下载"
              >
                <IconDownload />
              </Button>
            </div>
          </div>
        </div>
      </Spin>
    </div>
  </Card>
</template>
