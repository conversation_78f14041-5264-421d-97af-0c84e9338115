import type { VbenFormSchema } from '#/adapter/form';

import { z } from '#/adapter/form';
import {
  getBrandsApi,
  getMaterialTypesApi,
  getSpecsByMaterialTypeApi,
} from '#/api/core/cmdb/product/product';
import { getProjectListApi } from '#/api/core/purchase/project';

import {
  REQUEST_TYPE_OPTIONS,
  STATUS_OPTIONS,
  URGENCY_LEVEL_OPTIONS,
} from './constants';

/**
 * 获取采购申请基本信息表单的字段配置
 */
export function useBasicSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Select',
      componentProps: {
        // allowClear: false,
        class: 'w-full',
        options: REQUEST_TYPE_OPTIONS,
        mode: 'multiple',
      },
      fieldName: 'request_type',
      label: '采购类别',
      rules: z.array(z.string()).min(1, '请选择采购类别'),
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        api: async () => {
          const res = await getProjectListApi({
            page: 1,
            pageSize: 100,
          });
          return res.list.map((item) => ({
            label: `${item.project_name} (${item.project_code})`,
            value: item.id,
          }));
        },
        class: 'w-full',
        labelField: 'label',
        valueField: 'value',
      },
      fieldName: 'project_id',
      label: '所属项目',
      rules: 'required',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: URGENCY_LEVEL_OPTIONS,
        optionType: 'button',
      },
      defaultValue: 'medium',
      fieldName: 'urgency_level',
      label: '紧急程度',
    },
    {
      component: 'DatePicker',
      componentProps: {
        class: 'w-full',
        format: 'YYYY-MM-DD',
        placeholder: '请选择期望交付日期',
        valueFormat: 'YYYY-MM-DDT00:00:00Z', // 修改为完整的ISO 8601格式
      },
      fieldName: 'expected_delivery_date',
      label: '期望交付日期',
      rules: z.string().min(1, '期望交付日期不能为空'),
    },
    {
      component: 'Textarea',
      componentProps: {
        maxLength: 200,
        placeholder: '请输入采购事由',
        rows: 3,
        showCount: true,
        class: 'w-full',
      },
      fieldName: 'reason',
      label: '采购事由',
      rules: z
        .string()
        .min(3, '采购事由不能少于3个字符')
        .max(200, '采购事由不能超过200个字符'),
    },
    {
      component: 'Input',
      componentProps: {
        maxLength: 255,
        placeholder: '请输入接收地址',
        class: 'w-full',
      },
      fieldName: 'receive_address',
      label: '接收地址',
      rules: z.string().min(1, '接收地址不能为空'),
    },
    {
      component: 'Input',
      componentProps: {
        maxLength: 50,
        placeholder: '请输入接收人姓名',
        class: 'w-full',
      },
      fieldName: 'receiver',
      label: '接收人',
      rules: z.string().min(1, '接收人不能为空'),
    },
    {
      component: 'Input',
      componentProps: {
        maxLength: 20,
        placeholder: '请输入接收人电话',
        class: 'w-full',
      },
      fieldName: 'receiver_phone',
      label: '接收电话',
      rules: z.string().min(1, '接收电话不能为空'),
    },
  ];
}

/**
 * 搜索表单字段配置
 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'request_no',
      label: '申请单号',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: REQUEST_TYPE_OPTIONS,
      },
      fieldName: 'request_type',
      label: '采购类别',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: URGENCY_LEVEL_OPTIONS,
      },
      fieldName: 'urgency_level',
      label: '紧急程度',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: STATUS_OPTIONS,
      },
      fieldName: 'status',
      label: '状态',
    },
    {
      component: 'RangePicker',
      fieldName: 'create_time',
      label: '创建时间',
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DDT00:00:00Z',
      },
    },
  ];
}

/**
 * 获取采购明细项表单的字段配置
 */
export function useItemsSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: false,
        api: async () => {
          const res = await getMaterialTypesApi();
          return res.map((item) => ({
            label: item,
            value: item,
          }));
        },
        labelField: 'label',
        valueField: 'value',
        placeholder: '请选择或输入物料类型',
        showSearch: true,
        filterOption: true,
      },
      fieldName: 'material_type',
      label: '物料类型',
      rules: z.string().min(1, '物料类型不能为空'),
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        api: async (params: { material_type: string }) => {
          if (!params.material_type) return [];
          try {
            const res = await getSpecsByMaterialTypeApi({
              material_type: params.material_type,
            });
            return res.map((item) => ({
              label: item,
              value: item,
            }));
          } catch (error) {
            console.error('获取规格型号失败', error);
            return [];
          }
        },
        labelField: 'label',
        valueField: 'value',
        placeholder: '请选择或输入型号',
        params: { material_type: '' },
        showSearch: true,
        filterOption: true,
      },
      fieldName: 'model',
      label: '型号',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        api: async () => {
          const res = await getBrandsApi();
          return res.map((item) => ({
            label: item,
            value: item,
          }));
        },
        labelField: 'label',
        valueField: 'value',
        placeholder: '请选择或输入品牌',
        showSearch: true,
        filterOption: true,
      },
      fieldName: 'brand',
      label: '品牌',
      rules: z.string().min(1, '品牌不能为空'),
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入PN号(可选)',
        allowClear: true,
      },
      fieldName: 'pn',
      label: 'PN号',
    },
    {
      component: 'Input',
      fieldName: 'spec',
      label: '规格',
      rules: z.string().min(1, '规格不能为空'),
    },
    {
      component: 'Input',
      fieldName: 'unit',
      label: '单位',
      rules: z.string().min(1, '单位不能为空'),
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 1,
      },
      defaultValue: 1,
      fieldName: 'quantity',
      label: '数量',
      rules: z.number().min(1, '数量必须大于0'),
    },
    {
      component: 'Textarea',
      componentProps: {
        maxLength: 200,
        rows: 2,
        showCount: true,
      },
      fieldName: 'remark',
      label: '备注',
    },
  ];
}
