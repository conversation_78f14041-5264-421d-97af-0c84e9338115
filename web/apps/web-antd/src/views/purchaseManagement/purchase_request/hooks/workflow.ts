import type { ComputedRef, Ref } from 'vue';

import type {
  PurchaseApprovalHistory,
  PurchaseRequest,
} from '#/api/core/purchase/purchase_request';

import { computed } from 'vue';

import { STATUS_ORDER } from '../constants';

// 定义一个联合类型，可以接受 Ref 或 ComputedRef
type MaybeRef<T> = ComputedRef<T> | Ref<T>;

/**
 * 工作流进度计算
 * @param purchaseRequest 采购申请数据
 */
export function useWorkflowProgress(
  purchaseRequest: MaybeRef<PurchaseRequest | undefined>,
) {
  return computed(() => {
    try {
      const status = purchaseRequest.value?.status || 'draft';
      const currentIndex = STATUS_ORDER.indexOf(status);

      if (status === 'rejected' || status === 'cancelled') {
        return { percent: 0, status: 'exception', strokeColor: '#ff4d4f' };
      }

      const percent =
        currentIndex === -1
          ? 0
          : ((currentIndex + 1) / STATUS_ORDER.length) * 100;

      return {
        percent: Math.round(Math.max(0, Math.min(100, percent))),
        status: status === 'approved' ? 'success' : 'active',
        strokeColor: status === 'approved' ? '#52c41a' : '#1890ff',
      };
    } catch (error) {
      console.error('Error computing workflow progress:', error);
      return { percent: 0, status: 'active', strokeColor: '#1890ff' };
    }
  });
}

/**
 * 工作流步骤定义
 * @param purchaseRequest 采购申请数据
 */
export function useWorkflowSteps(
  purchaseRequest: MaybeRef<PurchaseRequest | undefined>,
) {
  return computed(() => {
    try {
      const status = purchaseRequest.value?.status || '';

      const steps = [
        { title: '创建申请', status: 'finish', description: '申请已创建' },
        {
          title: '项目经理审批',
          status: (() => {
            if (status === 'project_manager_review') {
              return 'process';
            } else if (['draft'].includes(status)) {
              return 'wait';
            } else {
              return 'finish';
            }
          })(),
          description: (() => {
            if (status === 'project_manager_review') {
              return '待审批';
            } else if (status === 'draft') {
              return '未开始';
            } else {
              return '已完成';
            }
          })(),
        },
        {
          title: '采购负责人审批',
          status: (() => {
            if (status === 'purchase_manager_review') {
              return 'process';
            } else if (['draft', 'project_manager_review'].includes(status)) {
              return 'wait';
            } else {
              return 'finish';
            }
          })(),
          description: (() => {
            if (status === 'purchase_manager_review') {
              return '待审批';
            } else if (['draft', 'project_manager_review'].includes(status)) {
              return '未开始';
            } else {
              return '已完成';
            }
          })(),
        },
        {
          title: '审批完成',
          status: (() => {
            if (status === 'approved') {
              return 'finish';
            } else if (status === 'rejected') {
              return 'error';
            } else {
              return 'wait';
            }
          })(),
          description: (() => {
            if (status === 'approved') {
              return '已批准';
            } else if (status === 'rejected') {
              return '已拒绝';
            } else {
              return '未完成';
            }
          })(),
        },
      ];

      return steps;
    } catch (error) {
      console.error('Error computing workflow steps:', error);
      return [
        { title: '创建申请', status: 'finish', description: '申请已创建' },
        { title: '项目经理审批', status: 'wait', description: '未开始' },
        { title: '采购负责人审批', status: 'wait', description: '未开始' },
        { title: '审批完成', status: 'wait', description: '未完成' },
      ];
    }
  });
}

/**
 * 统计信息计算
 * @param purchaseRequest 采购申请数据
 */
export function useStatistics(
  purchaseRequest: MaybeRef<PurchaseRequest | undefined>,
) {
  return computed(() => {
    if (!purchaseRequest.value) return [];

    try {
      const createdDays = purchaseRequest.value.created_at
        ? Math.max(
            0,
            Math.floor(
              (Date.now() -
                new Date(purchaseRequest.value.created_at).getTime()) /
                (1000 * 60 * 60 * 24),
            ),
          )
        : 0;

      const deliveryDays = purchaseRequest.value.expected_delivery_date
        ? Math.ceil(
            (new Date(purchaseRequest.value.expected_delivery_date).getTime() -
              Date.now()) /
              (1000 * 60 * 60 * 24),
          )
        : 0;

      return [
        {
          title: '申请项目数',
          value: Array.isArray(purchaseRequest.value.items)
            ? purchaseRequest.value.items.length
            : 0,
          suffix: '项',
          valueStyle: { color: '#1890ff' },
          icon: '📦',
        },
        {
          title: '申请天数',
          value: createdDays,
          suffix: '天前',
          valueStyle: { color: '#52c41a' },
          icon: '📅',
        },
        {
          title: '预计到货',
          value: Math.max(0, deliveryDays),
          suffix: deliveryDays > 0 ? '天后' : '已逾期',
          valueStyle: { color: deliveryDays > 0 ? '#faad14' : '#f5222d' },
          icon: '🚚',
        },
      ];
    } catch (error) {
      console.error('Error computing statistics:', error);
      return [];
    }
  });
}

/**
 * 计算历史记录统计
 * @param workflowStatus 工作流状态数据
 */
export function useHistoryStats(
  workflowStatus: MaybeRef<PurchaseApprovalHistory[]>,
) {
  return computed(() => {
    try {
      if (!Array.isArray(workflowStatus.value))
        return { approvals: 0, rollbacks: 0, lastAction: null, allHistory: [] };

      // 按操作类型统计
      const approvals = workflowStatus.value.filter(
        (item) => item.action === 'approve',
      ).length;
      const rollbacks = workflowStatus.value.filter(
        (item) => item.action === 'rollback',
      ).length;

      // 获取最后一次操作
      let lastAction = null;
      if (workflowStatus.value.length > 0) {
        try {
          // 数据已经按时间排序（最新的在前），所以直接取第一个
          lastAction = workflowStatus.value[0];
        } catch (sortError) {
          console.warn('Error getting last action:', sortError);
          lastAction = workflowStatus.value[0];
        }
      }

      // 为模板提供所有历史记录
      const allHistory = workflowStatus.value.map((item) => ({
        ...item,
        type: item.action === 'rollback' ? 'rollback' : 'approval',
        timestamp: item.operation_time,
        approver_id: item.operator_name || item.operator_id,
        current_stage: item.previous_status,
        rollback_to: item.new_status,
      }));

      return { approvals, rollbacks, lastAction, allHistory };
    } catch (error) {
      console.error('Error computing history stats:', error);
      return { approvals: 0, rollbacks: 0, lastAction: null, allHistory: [] };
    }
  });
}

/**
 * 权限检查 Hook
 * @param purchaseRequest 采购申请数据
 */
export function usePermissionChecks(
  purchaseRequest: MaybeRef<PurchaseRequest | undefined>,
) {
  // 是否可以审批
  const canApprove = computed(() => {
    const status = purchaseRequest.value?.status;

    // 检查是否是审批阶段
    return (
      status === 'project_manager_review' ||
      status === 'purchase_manager_review'
    );
  });

  // 是否可以回退
  const canRollback = computed(() => {
    const status = purchaseRequest.value?.status;
    return status === 'purchase_manager_review'; // 只有采购经理审批阶段可以回退
  });

  // 获取当前阶段的权限码
  const getPermissionCodes = computed(() => {
    const status = purchaseRequest.value?.status;

    switch (status) {
      case 'project_manager_review': {
        return {
          approve: ['Purchase:PurchaseRequest:ProjectManagerReview'],
          reject: ['Purchase:PurchaseRequest:ProjectManagerReview'],
          rollback: [], // 项目经理阶段不能回退
        };
      }
      case 'purchase_manager_review': {
        return {
          approve: ['Purchase:PurchaseRequest:PurchaseManagerReview'],
          reject: ['Purchase:PurchaseRequest:PurchaseManagerReview'],
          rollback: ['Purchase:PurchaseRequest:PurchaseManagerReview'],
        };
      }
      default: {
        return {
          approve: [],
          reject: [],
          rollback: [],
        };
      }
    }
  });

  return {
    canApprove,
    canRollback,
    getPermissionCodes,
  };
}
