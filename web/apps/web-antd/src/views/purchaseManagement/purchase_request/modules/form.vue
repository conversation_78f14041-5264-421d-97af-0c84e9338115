<script lang="ts" setup>
import type { Project } from '#/api/core/purchase/project';
import type {
  PurchaseRequest,
  PurchaseRequestItem,
} from '#/api/core/purchase/purchase_request';

import { computed, reactive, ref, watchEffect } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Modal as AModal, Button, message, Tabs } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { getProjectDetailApi } from '#/api/core/purchase/project';
import {
  createPurchaseRequest,
  updatePurchaseRequest,
} from '#/api/core/purchase/purchase_request';
import { useBasicSchema } from '#/views/purchaseManagement/purchase_request/schema';

import FileUploader from '../../../../components/FileUploader/FileUploader.vue';
import ProductSelector from '../components/product-selector.vue';

const emit = defineEmits(['success']);
const formData = ref<PurchaseRequest>();
const readOnly = ref(false);
const projectData = ref<null | Project>(null);
const currentProjectId = ref<null | number>(null);
const productSelectorRef = ref<InstanceType<typeof ProductSelector> | null>(
  null,
);
const fileUploaderRef = ref<InstanceType<typeof FileUploader> | null>(null);

const getTitle = computed(() => {
  if (readOnly.value) {
    return '查看采购申请';
  }
  return formData.value?.id ? '编辑采购申请' : '创建采购申请';
});

const [BasicForm, basicFormApi] = useVbenForm({
  layout: 'vertical',
  schema: useBasicSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-6',
});

// 当项目ID改变时获取项目详情
async function handleProjectChange(projectId: null | number) {
  if (!projectId) {
    projectData.value = null;
    return;
  }

  currentProjectId.value = projectId;

  try {
    projectData.value = await getProjectDetailApi(projectId);
  } catch (error) {
    console.error('Failed to get project details:', error);
    projectData.value = null;
  }
}

// 监听项目ID变化
watchEffect(async () => {
  try {
    const values = await basicFormApi.getValues();
    if (values && values.project_id !== undefined) {
      const projectId = Number(values.project_id);
      if (projectId !== currentProjectId.value) {
        handleProjectChange(projectId || null);
      }
    }
  } catch (error) {
    console.error('监听项目ID变化时发生错误:', error);
  }
});

const itemList = reactive<PurchaseRequestItem[]>([]);

// 初始化一个空的采购项目
function initNewItem(): PurchaseRequestItem {
  return {
    material_type: '',
    model: '',
    brand: '',
    pn: '',
    spec: '',
    unit: '',
    quantity: 1,
    remark: '',
  };
}

// 添加新项目
function addItem() {
  itemList.unshift(initNewItem()); // 使用unshift而不是push，将新项添加到开头
}

// 删除项目
function removeItem(index: number) {
  itemList.splice(index, 1);
}

// 处理产品选择结果
function handleProductSelected(products: PurchaseRequestItem[]) {
  if (products && products.length > 0) {
    // 将产品倒序添加，这样最后一个产品会出现在最上面
    [...products].reverse().forEach((product) => {
      itemList.unshift({
        ...product,
        quantity: 1, // 默认数量为1
      });
    });
    message.success(`已添加${products.length}个产品`);
  }
}

function resetForm() {
  basicFormApi.resetForm();
  basicFormApi.setValues(formData.value || {});
  itemList.length = 0;
  if (formData.value?.items) {
    formData.value.items.forEach((item) => itemList.push({ ...item }));
  }

  // 如果有项目ID，重新获取项目详情
  if (formData.value?.project_id) {
    handleProjectChange(Number(formData.value.project_id));
  }
}

const [Modal, modalApi] = useVbenModal({
  class: '!w-full',
  async onConfirm() {
    if (readOnly.value) {
      modalApi.close();
      return;
    }

    try {
      const { valid } = await basicFormApi.validate();
      if (!valid) {
        modalApi.lock(false);
        message.error('请检查必填项');
        return;
      }

      modalApi.lock();

      // 检查是否有采购明细
      if (itemList.length === 0) {
        modalApi.lock(false);
        message.error('请至少添加一个采购明细项');
        return;
      }

      // 检查明细项是否填写完整
      for (const item of itemList) {
        if (!item.material_type) {
          message.error('物料类型不能为空');
          modalApi.lock(false);
          return;
        }
        if (item.quantity <= 0) {
          message.error('数量必须大于0');
          modalApi.lock(false);
          return;
        }
        if (!item.unit) {
          message.error('单位不能为空');
          modalApi.lock(false);
          return;
        }
        if (!item.brand) {
          message.error('品牌不能为空');
          modalApi.lock(false);
          return;
        }
        if (!item.spec) {
          message.error('规格不能为空');
          modalApi.lock(false);
          return;
        }
      }

      const basicData = await basicFormApi.getValues();
      try {
        // 确保包含必需的字段
        const data: PurchaseRequest = {
          request_type: basicData.request_type || '',
          urgency_level: basicData.urgency_level || 'medium',
          reason: basicData.reason || '',
          receive_address: basicData.receive_address || '',
          receiver: basicData.receiver || '',
          receiver_phone: basicData.receiver_phone || '',
          items: [...itemList],
          ...basicData, // 合并其他数据
        };

        // 如果有项目数据，添加项目名称
        if (projectData.value && basicData.project_id) {
          data.project_name = projectData.value.project_name;
        }

        // 显示确认弹窗而不是直接提交
        showConfirmModal(data);
      } catch (error) {
        console.error('验证失败:', error);
        modalApi.lock(false);
      }
    } catch (error) {
      console.error('表单验证错误:', error);
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<PurchaseRequest & { readOnly?: boolean }>();
      if (data) {
        readOnly.value = !!data.readOnly;

        // 删除标记为只读的属性
        if (data.readOnly === undefined) {
          formData.value = data;
        } else {
          const { readOnly: _, ...restData } = data;
          formData.value = restData;
        }

        basicFormApi.setValues(formData.value);

        // 初始化明细项
        itemList.length = 0;
        if (formData.value.items) {
          formData.value.items.forEach((item) => itemList.push({ ...item }));
        }

        // 如果有项目ID，获取项目详情
        if (formData.value.project_id) {
          handleProjectChange(Number(formData.value.project_id));
        }
      } else {
        readOnly.value = false;
        formData.value = {
          request_type: '',
          urgency_level: 'medium',
          reason: '',
          receive_address: '',
          receiver: '',
          receiver_phone: '',
          items: [],
        };
        basicFormApi.setValues(formData.value);
        itemList.length = 0;
      }
    }
  },
});

const activeKey = ref('basic');

// 二次确认弹窗
const confirmModalVisible = ref(false);
const confirmLoading = ref(false);
const pendingData = ref<null | PurchaseRequest>(null);

// 显示确认弹窗
function showConfirmModal(data: PurchaseRequest) {
  pendingData.value = data;
  confirmModalVisible.value = true;
}

// 确认提交
async function handleConfirmSubmit() {
  if (!pendingData.value) return;

  confirmLoading.value = true;
  try {
    // 先创建或更新采购申请
    const result = await (formData.value?.id
      ? updatePurchaseRequest(formData.value.id, pendingData.value)
      : createPurchaseRequest(pendingData.value));

    // 然后上传相关文件（如果有）
    if (fileUploaderRef.value && result?.id) {
      try {
        await fileUploaderRef.value.uploadFilesToServer(result.id);
      } catch (error) {
        console.error('文件上传失败:', error);
        // 不影响整体流程，继续执行后续操作
      }
    }

    confirmModalVisible.value = false;
    modalApi.close();
    emit('success');
  } catch (error) {
    console.error('提交失败:', error);
  } finally {
    confirmLoading.value = false;
    modalApi.lock(false);
  }
}

// 取消确认
function handleCancelConfirm() {
  confirmModalVisible.value = false;
  modalApi.lock(false);
  pendingData.value = null;
}
</script>

<template>
  <Modal :title="getTitle" width="900px">
    <Tabs v-model:active-key="activeKey" class="mx-4">
      <Tabs.TabPane key="basic" tab="基本信息">
        <BasicForm :disabled="readOnly" class="mx-4 mb-4" />

        <div class="mx-4 mb-6">
          <div class="mb-2 text-base font-medium">附件上传</div>
          <FileUploader
            ref="fileUploaderRef"
            file-description="采购申请附件"
            module-type="purchase_requests"
            :module-id="formData?.id"
            :disabled="readOnly"
            show-upload-list
            :max-count="5"
            multiple
            accept="*"
            list-type="text"
            compact
          >
            <Button v-if="!readOnly" type="primary" size="small">
              选择文件
            </Button>
          </FileUploader>
        </div>

        <div
          v-if="projectData && !readOnly"
          class="mx-4 mt-3 rounded-lg border border-blue-100 bg-blue-50 p-4 shadow-sm"
        >
          <div class="mb-3 flex items-center">
            <div class="mr-2 h-5 w-1 rounded bg-blue-500"></div>
            <div class="text-base font-medium text-blue-700">
              已选择项目信息
            </div>
          </div>
          <div
            class="ml-3 grid grid-cols-1 gap-2 text-sm text-blue-700 md:grid-cols-2"
          >
            <div>
              <span class="font-semibold">项目编号:</span>
              {{ projectData.project_code }}
            </div>
            <div>
              <span class="font-semibold">项目名称:</span>
              {{ projectData.project_name }}
            </div>
            <div>
              <span class="font-semibold">客户名称:</span>
              {{ projectData.customer_name }}
            </div>
            <div v-if="projectData.project_manager">
              <span class="font-semibold">项目经理:</span>
              {{ projectData.project_manager }}
            </div>
          </div>
        </div>
      </Tabs.TabPane>

      <Tabs.TabPane key="items" tab="采购明细">
        <div class="mx-4">
          <div v-if="!readOnly" class="mb-6 flex gap-2">
            <Button
              type="primary"
              size="middle"
              @click="addItem"
              class="flex h-9 items-center gap-1.5 rounded px-4 shadow-sm transition-all hover:shadow-md"
            >
              <span class="text-sm font-medium">+</span>
              <span class="text-sm">手动添加</span>
            </Button>

            <ProductSelector
              ref="productSelectorRef"
              @select="handleProductSelected"
            />
          </div>

          <div
            v-if="itemList.length === 0"
            class="rounded border border-dashed border-gray-300 bg-gray-50 py-10 text-center text-gray-500"
          >
            <div class="mb-3">
              <div
                class="mb-3 inline-flex h-12 w-12 items-center justify-center rounded-full bg-gray-100"
              >
                <span class="text-xl text-gray-400">+</span>
              </div>
            </div>
            <div class="mb-1 text-base font-medium">暂无采购明细</div>
            <div class="text-sm text-gray-400">请点击上方按钮添加采购项目</div>
          </div>

          <div v-else>
            <div
              v-for="(item, index) in itemList"
              :key="index"
              class="mb-4 rounded border border-gray-200 bg-white p-4 shadow-sm transition-all hover:border-gray-300"
            >
              <div
                class="mb-4 flex items-center justify-between border-b border-gray-200 pb-2"
              >
                <div class="flex items-center gap-2">
                  <div
                    class="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 text-xs font-bold text-blue-800"
                  >
                    {{ index + 1 }}
                  </div>
                  <h3 class="text-sm font-semibold text-gray-800">
                    采购项目 {{ index + 1 }}
                  </h3>
                </div>
                <Button
                  v-if="!readOnly"
                  type="link"
                  danger
                  size="small"
                  @click="removeItem(index)"
                  class="rounded px-2 py-1 text-xs hover:bg-red-50"
                >
                  删除
                </Button>
              </div>

              <!-- 主要信息区 -->
              <div class="mb-4">
                <div
                  class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4"
                >
                  <div>
                    <div class="mb-1.5 text-xs font-medium text-gray-700">
                      物料类型 <span class="text-red-500">*</span>
                    </div>
                    <input
                      v-model="item.material_type"
                      :disabled="readOnly"
                      class="w-full rounded border border-gray-300 px-2.5 py-2 text-sm transition-colors focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      placeholder="请输入物料类型"
                    />
                  </div>

                  <div class="flex gap-2">
                    <div class="flex-1">
                      <div class="mb-1.5 text-xs font-medium text-gray-700">
                        数量 <span class="text-red-500">*</span>
                      </div>
                      <input
                        v-model.number="item.quantity"
                        :disabled="readOnly"
                        class="w-full rounded border border-gray-300 px-2.5 py-2 text-sm transition-colors focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                        placeholder="数量"
                        type="number"
                        min="1"
                      />
                    </div>
                    <div class="w-16">
                      <div class="mb-1.5 text-xs font-medium text-gray-700">
                        单位 <span class="text-red-500">*</span>
                      </div>
                      <input
                        v-model="item.unit"
                        :disabled="readOnly"
                        class="w-full rounded border border-gray-300 px-2.5 py-2 text-sm transition-colors focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                        placeholder="单位"
                      />
                    </div>
                  </div>

                  <div>
                    <div class="mb-1.5 text-xs font-medium text-gray-700">
                      型号
                    </div>
                    <input
                      v-model="item.model"
                      :disabled="readOnly"
                      class="w-full rounded border border-gray-300 px-2.5 py-2 text-sm transition-colors focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      placeholder="请输入型号"
                    />
                  </div>

                  <div>
                    <div class="mb-1.5 text-xs font-medium text-gray-700">
                      品牌 <span class="text-red-500">*</span>
                    </div>
                    <input
                      v-model="item.brand"
                      :disabled="readOnly"
                      class="w-full rounded border border-gray-300 px-2.5 py-2 text-sm transition-colors focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      placeholder="请输入品牌"
                    />
                  </div>
                </div>
              </div>

              <!-- 详细信息区 -->
              <div class="mb-4">
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div>
                    <div class="mb-1.5 text-xs font-medium text-gray-700">
                      PN号
                    </div>
                    <input
                      v-model="item.pn"
                      :disabled="readOnly"
                      class="w-full rounded border border-gray-300 px-2.5 py-2 text-sm transition-colors focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      placeholder="请输入PN号"
                    />
                  </div>

                  <div>
                    <div class="mb-1.5 text-xs font-medium text-gray-700">
                      规格 <span class="text-red-500">*</span>
                    </div>
                    <input
                      v-model="item.spec"
                      :disabled="readOnly"
                      class="w-full rounded border border-gray-300 px-2.5 py-2 text-sm transition-colors focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      placeholder="请输入规格"
                    />
                  </div>
                </div>
              </div>

              <!-- 备注信息 -->
              <div>
                <div class="mb-1.5 text-xs font-medium text-gray-700">
                  备注说明
                </div>
                <textarea
                  v-model="item.remark"
                  :disabled="readOnly"
                  class="w-full resize-none rounded border border-gray-300 px-2.5 py-2 text-sm transition-colors focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  placeholder="请输入备注说明（可选）"
                  rows="2"
                ></textarea>
              </div>
            </div>
          </div>
        </div>
      </Tabs.TabPane>
    </Tabs>

    <template #prepend-footer>
      <div v-if="!readOnly" class="flex-auto">
        <Button type="primary" danger @click="resetForm"> 重置 </Button>
      </div>
    </template>
  </Modal>

  <!-- 确认提交弹窗 -->
  <AModal
    v-model:open="confirmModalVisible"
    title="确认提交采购申请"
    width="700px"
    :confirm-loading="confirmLoading"
    @ok="handleConfirmSubmit"
    @cancel="handleCancelConfirm"
  >
    <div class="space-y-6">
      <!-- 项目信息 -->
      <div
        v-if="projectData"
        class="rounded border border-blue-200 bg-blue-50 p-4"
      >
        <div class="mb-3 flex items-center">
          <div class="mr-2 h-4 w-1 rounded bg-blue-500"></div>
          <h3 class="text-sm font-semibold text-blue-800">项目信息</h3>
        </div>
        <div class="grid grid-cols-2 gap-2 text-sm text-blue-700">
          <div>
            <span class="font-medium">项目编号:</span>
            {{ projectData.project_code }}
          </div>
          <div>
            <span class="font-medium">项目名称:</span>
            {{ projectData.project_name }}
          </div>
          <div>
            <span class="font-medium">客户名称:</span>
            {{ projectData.customer_name }}
          </div>
          <div v-if="projectData.project_manager">
            <span class="font-medium">项目经理:</span>
            {{ projectData.project_manager }}
          </div>
        </div>
      </div>

      <!-- 采购明细 -->
      <div>
        <div class="mb-3 flex items-center">
          <div class="mr-2 h-4 w-1 rounded bg-green-500"></div>
          <h3 class="text-sm font-semibold text-gray-800">
            采购明细 ({{ itemList.length }}项)
          </h3>
        </div>
        <div class="space-y-3">
          <div
            v-for="(item, index) in itemList"
            :key="index"
            class="rounded border border-gray-200 bg-gray-50 p-3"
          >
            <div class="mb-2 flex items-center">
              <div
                class="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-green-100 text-xs font-bold text-green-800"
              >
                {{ index + 1 }}
              </div>
              <span class="text-sm font-medium text-gray-800">{{
                item.material_type
              }}</span>
            </div>
            <div class="ml-7 grid grid-cols-2 gap-2 text-xs text-gray-600">
              <div v-if="item.spec">
                <span class="font-medium">规格:</span> {{ item.spec }}
              </div>
              <div v-if="item.model">
                <span class="font-medium">型号:</span> {{ item.model }}
              </div>
              <div v-if="item.brand">
                <span class="font-medium">品牌:</span> {{ item.brand }}
              </div>
              <div>
                <span class="font-medium">数量:</span> {{ item.quantity }}
                {{ item.unit || '' }}
              </div>
              <div v-if="item.pn" class="col-span-2">
                <span class="font-medium">PN号:</span> {{ item.pn }}
              </div>
              <div v-if="item.remark" class="col-span-2">
                <span class="font-medium">备注:</span> {{ item.remark }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="border-t pt-4 text-center text-sm text-gray-500">
        请确认以上信息无误后点击确定提交
      </div>
    </div>
  </AModal>
</template>
