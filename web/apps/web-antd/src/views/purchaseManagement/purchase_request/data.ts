import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { PurchaseRequest } from '#/api/core/purchase/purchase_request';

/**
 * 获取表格列配置
 * @param {OnActionClickFn<PurchaseRequest>} [onActionClick] 表格操作按钮点击事件
 */
export function useColumns(): VxeTableGridOptions<PurchaseRequest>['columns'] {
  // onActionClick?: OnActionClickFn<PurchaseRequest>,
  return [
    {
      field: 'request_no',
      title: '申请单号',
      width: 200,
    },

    {
      field: 'request_type',
      title: '采购类别',
      width: 200,
    },
    {
      field: 'status',
      title: '状态',
      width: 120,
      slots: { default: 'status' },
    },
    {
      field: 'urgency_level',
      title: '紧急程度',
      width: 100,
      slots: { default: 'urgency-level' },
    },
    {
      field: 'project_name',
      title: '所属项目',
      width: 150,
    },
    {
      field: 'items',
      title: '申请明细',
      width: 120,
      slots: { default: 'items_action' },
    },
    {
      field: 'receive_address',
      title: '接收地址',
      width: 180,
    },
    {
      field: 'receiver',
      title: '接收人',
      width: 100,
    },
    {
      field: 'receiver_phone',
      title: '联系电话',
      width: 120,
    },

    {
      field: 'creator_name',
      title: '申请人',
      width: 100,
    },

    {
      field: 'expected_delivery_date',
      title: '期望到货日期',
      formatter: 'formatDate',
      width: 180,
    },
    {
      field: 'reason',
      title: '采购事由',
      minWidth: 200,
    },
    {
      field: 'created_at',
      title: '创建时间',
      formatter: 'formatDateTime',
      width: 180,
    },

    {
      title: '操作',
      field: 'operation',
      fixed: 'right',
      width: 150,
      slots: { default: 'operate' },
    },
  ];
}

/**
 * 获取采购申请明细表格列配置
 */
export function useItemColumns(): VxeTableGridOptions<any>['columns'] {
  return [
    {
      field: 'material_type',
      title: '物料类型',
      width: 200,
      fixed: 'left',
    },
    {
      field: 'model',
      title: '型号',
      width: 150,
    },
    {
      field: 'brand',
      title: '品牌',
      width: 120,
    },
    {
      field: 'pn',
      title: 'PN号',
      width: 150,
    },
    {
      field: 'spec',
      title: '规格',
      minWidth: 200,
    },
    {
      field: 'unit',
      title: '单位',
      width: 80,
    },
    {
      field: 'quantity',
      title: '申请数量',
      width: 100,
    },
    {
      field: 'remark',
      title: '备注',
      minWidth: 150,
    },
  ];
}
