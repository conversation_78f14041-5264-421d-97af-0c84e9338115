/**
 * 采购申请常量定义
 */

// 定义标签配置接口
export interface TagConfig {
  color: string;
  text: string;
}

// 定义标签配置映射接口
export interface TagConfigMap {
  [key: string]: TagConfig;
}

// 采购类别选项
export const REQUEST_TYPE_OPTIONS = [
  { label: '服务器', value: '服务器' },
  { label: '服务器配件', value: '服务器配件' },
  { label: '网络设备', value: '网络设备' },
  { label: '综合布线', value: '综合布线' },
  { label: 'IDC机柜', value: 'IDC机柜' },
  { label: '带宽', value: '带宽' },
  { label: '服务', value: '服务' },
  { label: '其他', value: '其他' },
];

// 紧急程度配置
export const URGENCY_CONFIG: TagConfigMap = {
  low: { color: '', text: '低' },
  medium: { color: 'blue', text: '中' },
  high: { color: 'orange', text: '高' },
  critical: { color: 'red', text: '紧急' },
  normal: { color: 'default', text: '一般' },
};

// 紧急程度选项
export const URGENCY_LEVEL_OPTIONS = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'critical' },
];

// 状态配置
export const STATUS_CONFIG: TagConfigMap = {
  draft: { color: '', text: '已创建' },
  project_manager_review: { color: 'blue', text: '项目经理审批' },
  purchase_manager_review: { color: 'orange', text: '采购负责人审批' },
  approved: { color: 'green', text: '已批准' },
  rejected: { color: 'red', text: '已拒绝' },
  cancelled: { color: '', text: '已取消' },
};

// 状态选项
export const STATUS_OPTIONS = [
  { label: '已创建', value: 'draft' },
  { label: '项目经理审批', value: 'project_manager_review' },
  { label: '采购负责人审批', value: 'purchase_manager_review' },
  { label: '已批准', value: 'approved' },
  { label: '已拒绝', value: 'rejected' },
  { label: '已取消', value: 'cancelled' },
];

// 状态顺序(用于计算进度)
export const STATUS_ORDER = [
  'draft',
  'project_manager_review',
  'purchase_manager_review',
  'approved',
];

// 操作类型映射
export const ACTION_CONFIG: TagConfigMap = {
  approve: { text: '审批通过', color: 'success' },
  reject: { text: '审批拒绝', color: 'error' },
  rollback: { text: '流程回退', color: 'warning' },
  create: { text: '创建申请', color: 'blue' },
  cancel: { text: '取消申请', color: 'default' },
};
