<script lang="ts" setup>
import type {
  PurchaseRequest,
  PurchaseRequestQuery,
} from '#/api/core/purchase/purchase_request';

import { nextTick, ref } from 'vue';
import { useRouter } from 'vue-router';

import { AccessControl } from '@vben/access';
import { Page, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { Button, Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getPurchaseRequestList } from '#/api/core/purchase/purchase_request';
import Form from '#/views/purchaseManagement/purchase_request/modules/form.vue';

import { STATUS_CONFIG, URGENCY_CONFIG } from './constants';
import { useColumns, useItemColumns } from './data';
import { useGridFormSchema } from './schema';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

const router = useRouter();

// 申请明细模态框相关数据
const detailModalVisible = ref(false);
const currentItems = ref<any[]>([]);
const currentRequestNo = ref('');

// 申请明细表格
const [DetailGrid, detailGridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useItemColumns(),
    height: 'auto',
    maxHeight: 500,
    pagerConfig: {
      enabled: false,
    },
    proxyConfig: {
      autoLoad: false,
      ajax: {
        query: async () => {
          return {
            items: currentItems.value,
            total: currentItems.value.length,
          };
        },
      },
    },
    toolbarConfig: {
      enabled: false,
    },
  },
});

// /**
//  * 编辑采购申请
//  * @param row
//  */
// function onEdit(row: PurchaseRequest) {
//   formModalApi.setData(row).open();
// }

/**
 * 创建新采购申请
 */
function onCreate() {
  formModalApi.setData(null).open();
}

/**
 * 显示申请明细
 */
async function showItemsDetail(row: PurchaseRequest) {
  if (row.items && row.items.length > 0) {
    currentItems.value = row.items;
    currentRequestNo.value = row.request_no || '';
    detailModalVisible.value = true;

    // 等待模态框打开后加载数据
    await nextTick();
    if (detailGridApi && detailGridApi.grid) {
      await detailGridApi.grid.commitProxy('query');
    }
  }
}

// /**
//  * 删除采购申请
//  * @param row
//  */
// function onDelete(row: PurchaseRequest) {
//   const hideLoading = message.loading({
//     content: `正在删除：${row.request_no || ''}`,
//     duration: 0,
//     key: 'action_process_msg',
//   });
//   deletePurchaseRequest(row.id!)
//     .then(() => {
//       message.success({
//         content: `删除成功：${row.request_no || ''}`,
//         key: 'action_process_msg',
//       });
//       refreshGrid();
//     })
//     .catch(() => {
//       hideLoading();
//     });
// }

// /**
//  * 取消采购申请
//  * @param row
//  */
// function onCancel(row: PurchaseRequest) {
//   const hideLoading = message.loading({
//     content: `正在取消：${row.request_no || ''}`,
//     duration: 0,
//     key: 'action_process_msg',
//   });
//   cancelPurchaseRequest(row.id!)
//     .then(() => {
//       message.success({
//         content: `取消成功：${row.request_no || ''}`,
//         key: 'action_process_msg',
//       });
//       refreshGrid();
//     })
//     .catch(() => {
//       hideLoading();
//     });
// }

/**
 * 查看采购申请详情
 * @param row
 */
function onView(row: PurchaseRequest) {
  // 导航到详情页
  router.push(`/purchase-management/purchase-request/detail/${row.id}`);
}

// 定义分页参数类型
interface PageParams {
  currentPage: number;
  pageSize: number;
}

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['create_time', ['start_date', 'end_date']]],
    schema: useGridFormSchema(),
    submitOnChange: false,
    submitOnEnter: true,
  },
  gridOptions: {
    columns: useColumns(),
    height: 'auto',
    pagerConfig: {
      enabled: true,
    },
    proxyConfig: {
      ajax: {
        query: async (
          { page }: { page: PageParams },
          formValues: Record<string, any>,
        ) => {
          const params: PurchaseRequestQuery = {
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          };

          const res = await getPurchaseRequestList(params);

          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    toolbarConfig: {
      custom: true,
      export: false,
      refresh: true,
      refreshOptions: { code: 'query' },
      search: true,
      zoom: true,
      zoomOptions: {},
    },
  },
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}
</script>
<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid>
      <template #toolbar-tools>
        <AccessControl :codes="['Purchase:PurchaseRequest:Create']" type="code">
          <Button type="primary" @click="onCreate">
            <Plus class="size-5" />
            创建采购申请
          </Button>
        </AccessControl>
      </template>

      <!-- 紧急程度插槽 -->
      <template #urgency-level="{ row }">
        <Tag :color="URGENCY_CONFIG[row.urgency_level as string]?.color">
          {{
            URGENCY_CONFIG[row.urgency_level as string]?.text ||
            row.urgency_level
          }}
        </Tag>
      </template>

      <!-- 状态插槽 -->
      <template #status="{ row }">
        <Tag :color="STATUS_CONFIG[row.status as string]?.color">
          {{ STATUS_CONFIG[row.status as string]?.text || row.status }}
        </Tag>
      </template>

      <!-- 申请明细插槽 -->
      <template #items_action="{ row }">
        <Button
          v-if="row.items && row.items.length > 0"
          type="link"
          size="small"
          @click="showItemsDetail(row)"
        >
          查看明细 ({{ row.items.length }})
        </Button>
        <span v-else class="text-gray-400">无明细</span>
      </template>

      <!-- 操作列插槽 -->
      <template #operate="{ row }">
        <!-- <Button v-if="['project_manager_review', 'purchase_manager_review'].includes(row.status)" type="link" @click="onCancel(row)">取消</Button> -->
        <Button type="link" @click="onView(row)">查看</Button>
      </template>
    </Grid>

    <!-- 申请明细模态框 -->
    <Modal
      v-model:open="detailModalVisible"
      title="申请明细"
      :width="1400"
      :footer="null"
      :destroy-on-close="true"
      class="request-detail-modal"
    >
      <div class="request-detail-header">
        <div
          class="flex items-center justify-between rounded-lg bg-blue-50 p-4"
        >
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
              <span class="text-sm font-medium text-gray-600">申请单号:</span>
              <span class="text-sm font-semibold text-blue-600">{{
                currentRequestNo
              }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm font-medium text-gray-600">明细数量:</span>
              <span class="text-sm font-semibold text-green-600"
                >{{ currentItems.length }} 项</span
              >
            </div>
          </div>
        </div>
      </div>

      <div class="request-detail-table">
        <DetailGrid />
      </div>
    </Modal>
  </Page>
</template>

<style lang="less" scoped>
.request-detail-modal {
  :deep(.ant-modal-content) {
    border-radius: 12px;
    overflow: hidden;
  }

  :deep(.ant-modal-header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: none;
    padding: 16px 24px;

    .ant-modal-title {
      color: white;
      font-weight: 600;
      font-size: 16px;
    }
  }

  :deep(.ant-modal-close) {
    color: white;

    &:hover {
      color: rgba(255, 255, 255, 0.8);
    }
  }

  :deep(.ant-modal-body) {
    padding: 0;
  }
}

.request-detail-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.request-detail-table {
  padding: 0 24px 24px;

  :deep(.vxe-table--body-wrapper) {
    max-height: 500px;
  }

  :deep(.vxe-table) {
    border-radius: 8px;
    overflow: hidden;
  }

  :deep(.vxe-table--header) {
    background: #f8fafc;
  }

  :deep(.vxe-table--header-wrapper) {
    .vxe-table--header {
      th {
        background: #f1f5f9;
        color: #475569;
        font-weight: 600;
        border-bottom: 2px solid #e2e8f0;
      }
    }
  }

  :deep(.vxe-table--body) {
    .vxe-table--body-wrapper {
      .vxe-body--row {
        &:hover {
          background-color: #f8fafc;
        }

        &:nth-child(even) {
          background-color: #fafbfc;
        }
      }
    }
  }
}
</style>
