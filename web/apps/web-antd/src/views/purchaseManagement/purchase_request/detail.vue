<script lang="ts" setup>
import type {
  PurchaseApprovalHistory,
  PurchaseRequest,
  WorkflowApproval,
  WorkflowRollback,
} from '#/api/core/purchase/purchase_request';
import type { ModuleFileListResult } from '#/api/core/upload';

import { computed, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { AccessControl } from '@vben/access';
import { Page } from '@vben/common-ui';
import {
  ArrowLeft,
  Calendar,
  Clock,
  FileText,
  Tag as TagIcon,
  User,
} from '@vben/icons';

import {
  Avatar,
  Badge,
  Button,
  Card,
  Divider,
  Image,
  message,
  Modal,
  Progress,
  Spin,
  Step,
  Steps,
  Table,
  Tag,
  Textarea,
  Timeline,
  TimelineItem,
} from 'ant-design-vue';

import {
  approvePurchaseRequest,
  getPurchaseRequestById,
  getPurchaseRequestHistory,
  rollbackPurchaseRequest,
} from '#/api/core/purchase/purchase_request';
// 导入文件相关API
import { getModuleFiles } from '#/api/core/upload';
import { handleDownloadFile, handlePreviewFile } from '#/utils/common/download';
import { formatDate, formatToDateTime } from '#/utils/common/time';

// 导入附件组件
import AttachmentList from './components/AttachmentList.vue';
import { ACTION_CONFIG, STATUS_CONFIG, URGENCY_CONFIG } from './constants';
import { detailColumns } from './hooks/columns';
import {
  useHistoryStats,
  usePermissionChecks,
  useStatistics,
  useWorkflowProgress,
  useWorkflowSteps,
} from './hooks/workflow';

const route = useRoute();
const router = useRouter();

const loading = ref(false);
const workflowLoading = ref(false);
const purchaseRequest = ref<PurchaseRequest>();
const workflowStatus = ref<PurchaseApprovalHistory[]>([]);
const approvalComments = ref('');
const rollbackComments = ref('');
const isApprovalModalVisible = ref(false);
const isRollbackModalVisible = ref(false);
const currentAction = ref<'approve' | 'reject'>('approve');

// 附件相关变量
const attachmentsLoading = ref(false);
const attachments = ref<ModuleFileListResult[]>([]);
const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');

// 获取采购申请ID
const requestId = computed(() => {
  return Number(route.params.id);
});

// 使用抽离出去的各种Hook
const columns = detailColumns;
const workflowProgress = useWorkflowProgress(purchaseRequest);
const workflowSteps = useWorkflowSteps(purchaseRequest);
const statistics = useStatistics(purchaseRequest);
const historyStats = useHistoryStats(workflowStatus);
const { canApprove, canRollback, getPermissionCodes } =
  usePermissionChecks(purchaseRequest);

const historyItems = computed(() => {
  return (historyStats.value && historyStats.value.allHistory) || [];
});

const totalHistoryCount = computed(() => {
  return (
    ((historyStats.value && historyStats.value.approvals) || 0) +
    ((historyStats.value && historyStats.value.rollbacks) || 0)
  );
});

const progressStatus = computed(() => {
  return workflowProgress.value?.status as
    | 'active'
    | 'exception'
    | 'normal'
    | 'success';
});

// 获取采购申请详情
async function fetchPurchaseRequestDetail() {
  try {
    loading.value = true;
    const response = await getPurchaseRequestById(requestId.value);
    purchaseRequest.value = response;
  } catch {
    message.error('获取采购申请详情失败');
  } finally {
    loading.value = false;
  }
}

// 获取工作流状态
async function fetchWorkflowStatus() {
  try {
    workflowLoading.value = true;
    const response = await getPurchaseRequestHistory(requestId.value);
    // API返回的是数组形式的历史记录
    workflowStatus.value = Array.isArray(response) ? response : [];
  } catch {
    message.error('获取审批历史失败');
    workflowStatus.value = [];
  } finally {
    workflowLoading.value = false;
  }
}

// 获取附件列表
async function fetchAttachments() {
  if (!requestId.value) return;

  try {
    attachmentsLoading.value = true;
    const files = await getModuleFiles('purchase_requests', requestId.value);
    attachments.value = Array.isArray(files) ? files : [];
  } catch {
    message.error('获取附件列表失败');
    attachments.value = [];
  } finally {
    attachmentsLoading.value = false;
  }
}

// 处理文件预览
function handlePreview(file: ModuleFileListResult) {
  // 使用通用预览函数
  handlePreviewFile(file, {
    onImagePreview: (imageUrl, title) => {
      previewImage.value = imageUrl;
      previewTitle.value = title;
      previewVisible.value = true;
    },
  });
}

// 处理文件下载
async function handleDownload(file: ModuleFileListResult) {
  // 使用通用下载函数
  await handleDownloadFile(file);
}

// 处理审批
function handleApproval(action: 'approve' | 'reject') {
  currentAction.value = action;
  approvalComments.value = '';
  isApprovalModalVisible.value = true;
}

// 确认审批
async function confirmApproval() {
  if (!approvalComments.value.trim()) {
    message.warning('请输入审批意见');
    return;
  }

  try {
    const data: WorkflowApproval = {
      request_id: requestId.value,
      action: currentAction.value,
      comments: approvalComments.value,
      current_stage: purchaseRequest.value?.status || '', // 添加当前阶段
    };

    await approvePurchaseRequest(data);
    message.success(
      currentAction.value === 'approve' ? '审批通过' : '审批拒绝',
    );
    isApprovalModalVisible.value = false;

    // 延时后刷新数据
    setTimeout(async () => {
      await fetchPurchaseRequestDetail();
      await fetchWorkflowStatus();
    }, 500);
  } catch {
    message.error('审批操作失败');
  }
}

// 处理回退
function handleRollback() {
  rollbackComments.value = '';
  isRollbackModalVisible.value = true;
}

// 确认回退
async function confirmRollback() {
  if (!rollbackComments.value.trim()) {
    message.warning('请输入回退原因');
    return;
  }

  try {
    const data: WorkflowRollback = {
      request_id: requestId.value,
      approver_id: 1,
      rollback_to: 'project_manager_review', // 指定回退到项目经理审批阶段
      current_stage: purchaseRequest.value?.status || '',
      comments: rollbackComments.value,
    };

    await rollbackPurchaseRequest(data);
    message.success('回退成功');
    isRollbackModalVisible.value = false;

    // 延时后刷新数据
    setTimeout(async () => {
      await fetchPurchaseRequestDetail();
      await fetchWorkflowStatus();
    }, 500);
  } catch {
    message.error('回退操作失败');
  }
}

// 返回列表
function goBack() {
  router.push('/purchase-management/purchase-request');
}

// 页面加载时获取数据
onMounted(async () => {
  await Promise.all([
    fetchPurchaseRequestDetail(),
    fetchWorkflowStatus(),
    fetchAttachments(),
  ]);
});
</script>

<template>
  <div>
    <Page auto-content-height>
      <div
        class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50"
      >
        <!-- 顶部操作栏 -->
        <div class="relative border-b border-gray-200 bg-white shadow-sm">
          <div class="mx-auto max-w-7xl px-6 py-4">
            <div class="flex items-center justify-between">
              <Button
                @click="goBack"
                size="large"
                class="flex items-center gap-2 border-gray-300 transition-colors duration-200 hover:bg-gray-50"
              >
                <ArrowLeft class="h-4 w-4" />
                返回列表
              </Button>

              <!-- 操作按钮 -->
              <div v-if="purchaseRequest" class="flex items-center gap-3">
                <AccessControl
                  v-if="canApprove"
                  :codes="getPermissionCodes.approve"
                  type="code"
                >
                  <Button
                    type="primary"
                    size="large"
                    class="border-0 bg-gradient-to-r from-green-500 to-green-600 shadow-lg transition-all duration-200 hover:from-green-600 hover:to-green-700 hover:shadow-xl"
                    @click="handleApproval('approve')"
                    :loading="loading"
                  >
                    ✓ 审批通过
                  </Button>
                </AccessControl>
                <AccessControl
                  v-if="canApprove"
                  :codes="getPermissionCodes.reject"
                  type="code"
                >
                  <Button
                    size="large"
                    class="border-0 bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg transition-all duration-200 hover:from-red-600 hover:to-red-700 hover:shadow-xl"
                    @click="handleApproval('reject')"
                    :loading="loading"
                  >
                    ✗ 审批拒绝
                  </Button>
                </AccessControl>
                <AccessControl
                  v-if="canRollback"
                  :codes="getPermissionCodes.rollback"
                  type="code"
                >
                  <Button
                    size="large"
                    class="border-0 bg-gradient-to-r from-orange-500 to-orange-600 text-white shadow-lg transition-all duration-200 hover:from-orange-600 hover:to-orange-700 hover:shadow-xl"
                    @click="handleRollback"
                    :loading="loading"
                  >
                    ↺ 回退
                  </Button>
                </AccessControl>
              </div>
            </div>
          </div>
        </div>

        <!-- 标题区域 -->
        <div
          class="bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-800 text-white"
        >
          <div class="mx-auto max-w-7xl px-6 py-8">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-4">
                <div
                  class="flex h-16 w-16 items-center justify-center rounded-2xl bg-white/20 backdrop-blur-sm"
                >
                  <FileText class="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 class="mb-2 text-3xl font-bold text-white">
                    采购申请详情
                  </h1>
                  <p class="text-lg text-blue-100" v-if="purchaseRequest">
                    申请单号：{{ purchaseRequest.request_no }}
                  </p>
                </div>
              </div>
              <div v-if="purchaseRequest" class="flex items-center gap-3">
                <Tag
                  :color="
                    STATUS_CONFIG[
                      purchaseRequest.status as keyof typeof STATUS_CONFIG
                    ]?.color
                  "
                  class="rounded-full border-0 px-4 py-2 text-sm font-semibold"
                >
                  {{
                    STATUS_CONFIG[
                      purchaseRequest.status as keyof typeof STATUS_CONFIG
                    ]?.text
                  }}
                </Tag>
              </div>
            </div>

            <!-- 统计卡片 -->
            <div
              class="mt-8 grid grid-cols-1 gap-6 md:grid-cols-3"
              v-if="purchaseRequest"
            >
              <div
                v-for="stat in statistics"
                :key="stat.title"
                class="rounded-xl border border-white/20 bg-white/10 p-6 backdrop-blur-sm transition-all duration-300 hover:bg-white/15"
              >
                <div class="flex items-center justify-between">
                  <div>
                    <div class="mb-1 text-sm font-medium text-blue-100">
                      {{ stat.title }}
                    </div>
                    <div
                      class="flex items-center gap-1 text-2xl font-bold text-white"
                    >
                      <span>{{ stat.value }}</span>
                      <span v-if="stat.suffix" class="text-lg">{{
                        stat.suffix
                      }}</span>
                    </div>
                  </div>
                  <div
                    class="flex h-12 w-12 items-center justify-center rounded-lg bg-white/20 text-2xl"
                  >
                    {{ stat.icon }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="mx-auto max-w-7xl px-6 py-8">
          <Spin :spinning="loading">
            <div v-if="purchaseRequest" class="space-y-8">
              <div class="grid grid-cols-1 gap-8 lg:grid-cols-4">
                <!-- 左侧主要信息 -->
                <div class="space-y-8 lg:col-span-3">
                  <!-- 基本信息 -->
                  <Card
                    class="overflow-hidden rounded-xl border-0 bg-white shadow-lg"
                    :bordered="false"
                  >
                    <template #title>
                      <div class="flex items-center gap-3 py-2">
                        <div
                          class="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600"
                        >
                          <User class="h-4 w-4 text-white" />
                        </div>
                        <span class="text-lg font-semibold text-gray-800"
                          >基本信息</span
                        >
                      </div>
                    </template>

                    <div
                      class="rounded-lg bg-gradient-to-br from-blue-50/50 to-indigo-50/50 p-6"
                    >
                      <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                        <div
                          class="rounded-lg bg-white p-4 shadow-sm transition-shadow duration-200 hover:shadow-md"
                        >
                          <div
                            class="mb-2 text-xs font-semibold uppercase tracking-wide text-gray-500"
                          >
                            采购类别
                          </div>
                          <div class="flex flex-wrap gap-2">
                            <template
                              v-if="Array.isArray(purchaseRequest.request_type)"
                            >
                              <Tag
                                v-for="(
                                  type, index
                                ) in purchaseRequest.request_type"
                                :key="index"
                                class="rounded-full border-0 bg-gradient-to-r from-blue-500 to-indigo-600 px-3 py-1 text-sm font-medium text-white"
                              >
                                {{ type }}
                              </Tag>
                            </template>
                            <template v-else>
                              <div class="font-medium text-gray-900">
                                {{ purchaseRequest.request_type || '-' }}
                              </div>
                            </template>
                          </div>
                        </div>
                        <div
                          class="rounded-lg bg-white p-4 shadow-sm transition-shadow duration-200 hover:shadow-md"
                        >
                          <div
                            class="mb-2 text-xs font-semibold uppercase tracking-wide text-gray-500"
                          >
                            紧急程度
                          </div>
                          <div class="flex items-center">
                            <Tag
                              :color="
                                URGENCY_CONFIG[
                                  purchaseRequest.urgency_level as keyof typeof URGENCY_CONFIG
                                ]?.color
                              "
                              class="rounded-full px-3 py-1 text-sm font-medium"
                            >
                              {{
                                URGENCY_CONFIG[
                                  purchaseRequest.urgency_level as keyof typeof URGENCY_CONFIG
                                ]?.text
                              }}
                            </Tag>
                          </div>
                        </div>
                        <div
                          class="rounded-lg bg-white p-4 shadow-sm transition-shadow duration-200 hover:shadow-md"
                        >
                          <div
                            class="mb-2 text-xs font-semibold uppercase tracking-wide text-gray-500"
                          >
                            所属项目
                          </div>
                          <div class="font-medium text-gray-900">
                            {{ purchaseRequest.project_name || '-' }}
                          </div>
                        </div>
                        <div
                          class="rounded-lg bg-white p-4 shadow-sm transition-shadow duration-200 hover:shadow-md"
                        >
                          <div
                            class="mb-2 text-xs font-semibold uppercase tracking-wide text-gray-500"
                          >
                            申请人
                          </div>
                          <div class="flex items-center gap-3">
                            <Avatar
                              :size="32"
                              class="bg-gradient-to-br from-blue-500 to-indigo-600 font-semibold text-white"
                            >
                              {{
                                (purchaseRequest.creator_name || 'U').charAt(0)
                              }}
                            </Avatar>
                            <span class="font-medium text-gray-900">{{
                              purchaseRequest.creator_name || '-'
                            }}</span>
                          </div>
                        </div>
                        <div
                          class="rounded-lg bg-white p-4 shadow-sm transition-shadow duration-200 hover:shadow-md"
                        >
                          <div
                            class="mb-2 text-xs font-semibold uppercase tracking-wide text-gray-500"
                          >
                            创建时间
                          </div>
                          <div
                            class="flex items-center gap-2 font-medium text-gray-900"
                          >
                            <Clock class="h-4 w-4 text-gray-500" />
                            {{
                              purchaseRequest.created_at
                                ? formatToDateTime(purchaseRequest.created_at)
                                : '-'
                            }}
                          </div>
                        </div>
                        <div
                          class="rounded-lg bg-white p-4 shadow-sm transition-shadow duration-200 hover:shadow-md"
                        >
                          <div
                            class="mb-2 text-xs font-semibold uppercase tracking-wide text-gray-500"
                          >
                            期望到货日期
                          </div>
                          <div
                            class="flex items-center gap-2 font-medium text-gray-900"
                          >
                            <Calendar class="h-4 w-4 text-gray-500" />
                            {{
                              purchaseRequest.expected_delivery_date
                                ? formatDate(
                                    purchaseRequest.expected_delivery_date,
                                  )
                                : '-'
                            }}
                          </div>
                        </div>
                        <div
                          class="rounded-lg bg-white p-4 shadow-sm transition-shadow duration-200 hover:shadow-md"
                        >
                          <div
                            class="mb-2 text-xs font-semibold uppercase tracking-wide text-gray-500"
                          >
                            接收地址
                          </div>
                          <div class="font-medium text-gray-900">
                            {{ purchaseRequest.receive_address || '-' }}
                          </div>
                        </div>
                        <div
                          class="rounded-lg bg-white p-4 shadow-sm transition-shadow duration-200 hover:shadow-md"
                        >
                          <div
                            class="mb-2 text-xs font-semibold uppercase tracking-wide text-gray-500"
                          >
                            接收人信息
                          </div>
                          <div class="flex flex-col gap-2">
                            <div class="flex items-center">
                              <span class="w-16 font-medium text-gray-500"
                                >接收人：</span
                              >
                              <span class="font-medium text-gray-900">{{
                                purchaseRequest.receiver || '-'
                              }}</span>
                            </div>
                            <div class="flex items-center">
                              <span class="w-24 font-medium text-gray-500"
                                >联系电话：</span
                              >
                              <span class="font-medium text-gray-900">{{
                                purchaseRequest.receiver_phone || '-'
                              }}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <Divider class="my-6" />

                      <div class="rounded-lg bg-white p-4 shadow-sm">
                        <div
                          class="mb-3 text-xs font-semibold uppercase tracking-wide text-gray-500"
                        >
                          采购事由
                        </div>
                        <div
                          class="rounded-lg border-l-4 border-blue-500 bg-gradient-to-r from-blue-50 to-indigo-50 p-4"
                        >
                          <p class="italic leading-relaxed text-gray-800">
                            {{ purchaseRequest.reason }}
                          </p>
                        </div>
                      </div>
                    </div>
                  </Card>

                  <!-- 采购明细 -->
                  <Card
                    class="overflow-hidden rounded-xl border-0 bg-white shadow-lg"
                    :bordered="false"
                  >
                    <template #title>
                      <div class="flex items-center gap-3 py-2">
                        <div
                          class="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-green-500 to-emerald-600"
                        >
                          <TagIcon class="h-4 w-4 text-white" />
                        </div>
                        <span class="text-lg font-semibold text-gray-800"
                          >采购明细</span
                        >
                        <Badge
                          :count="purchaseRequest.items?.length || 0"
                          class="rounded-full bg-green-500 px-2 py-1 text-xs font-medium text-white"
                        />
                      </div>
                    </template>

                    <div
                      class="overflow-hidden rounded-lg border border-gray-200"
                    >
                      <Table
                        :columns="columns"
                        :data-source="purchaseRequest.items"
                        :pagination="false"
                        :scroll="{ x: 800 }"
                        size="middle"
                        class="border-0 shadow-none"
                      >
                        <template #emptyText>
                          <div class="py-12 text-center">
                            <div
                              class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-100"
                            >
                              <TagIcon class="h-8 w-8 text-gray-400" />
                            </div>
                            <p class="text-lg font-medium text-gray-500">
                              暂无采购明细
                            </p>
                          </div>
                        </template>
                      </Table>
                    </div>
                  </Card>

                  <!-- 附件列表组件 -->
                  <AttachmentList
                    :attachments="attachments"
                    :loading="attachmentsLoading"
                    @preview="handlePreview"
                    @download="handleDownload"
                  />
                </div>

                <!-- 右侧信息面板 -->
                <div class="space-y-6">
                  <!-- 审批进度 -->
                  <Card
                    class="overflow-hidden rounded-xl border-0 bg-white shadow-lg"
                    :bordered="false"
                  >
                    <template #title>
                      <div class="flex items-center gap-2 py-1">
                        <div
                          class="flex h-6 w-6 items-center justify-center rounded-md bg-gradient-to-br from-purple-500 to-purple-600"
                        >
                          <Clock class="h-3 w-3 text-white" />
                        </div>
                        <span class="text-sm font-semibold text-gray-800"
                          >审批进度</span
                        >
                      </div>
                    </template>

                    <div
                      class="rounded-lg bg-gradient-to-br from-purple-50/50 to-pink-50/50 p-4"
                    >
                      <Steps
                        :current="
                          workflowSteps.findIndex(
                            (step) => step.status === 'process',
                          )
                        "
                        direction="vertical"
                        size="small"
                        class="custom-steps"
                      >
                        <Step
                          v-for="step in workflowSteps"
                          :key="step.title"
                          :title="step.title"
                          :description="step.description"
                          :status="step.status"
                        />
                      </Steps>
                    </div>
                  </Card>

                  <!-- 历史统计 -->
                  <Card
                    class="overflow-hidden rounded-xl border-0 bg-white shadow-lg"
                    :bordered="false"
                  >
                    <template #title>
                      <div class="flex items-center gap-2 py-1">
                        <div
                          class="flex h-6 w-6 items-center justify-center rounded-md bg-gradient-to-br from-blue-500 to-indigo-600"
                        >
                          <span class="text-xs font-bold text-white">📊</span>
                        </div>
                        <span class="text-sm font-semibold text-gray-800"
                          >操作统计</span
                        >
                      </div>
                    </template>

                    <div class="space-y-3 p-4">
                      <div
                        class="flex items-center justify-between rounded-lg bg-blue-50 p-3"
                      >
                        <span class="text-sm text-gray-600">审批次数</span>
                        <span class="text-lg font-bold text-blue-600">{{
                          (historyStats && historyStats.approvals) || 0
                        }}</span>
                      </div>
                      <div
                        class="flex items-center justify-between rounded-lg bg-orange-50 p-3"
                      >
                        <span class="text-sm text-gray-600">回退次数</span>
                        <span class="text-lg font-bold text-orange-600">{{
                          (historyStats && historyStats.rollbacks) || 0
                        }}</span>
                      </div>
                      <div
                        v-if="historyStats && historyStats.lastAction"
                        class="rounded-lg bg-gray-50 p-3"
                      >
                        <div class="mb-1 text-xs text-gray-500">最近操作</div>
                        <div class="text-sm font-medium">
                          {{
                            historyStats &&
                            historyStats.lastAction &&
                            historyStats.lastAction.action === 'rollback'
                              ? '流程回退'
                              : historyStats &&
                                  historyStats.lastAction &&
                                  historyStats.lastAction.action === 'approve'
                                ? '审批通过'
                                : historyStats &&
                                    historyStats.lastAction &&
                                    historyStats.lastAction.action === 'reject'
                                  ? '审批拒绝'
                                  : historyStats &&
                                      historyStats.lastAction &&
                                      historyStats.lastAction.action ===
                                        'cancel'
                                    ? '取消申请'
                                    : historyStats &&
                                        historyStats.lastAction &&
                                        historyStats.lastAction.action ===
                                          'create'
                                      ? '创建申请'
                                      : '其他操作'
                          }}
                        </div>
                        <div class="text-xs text-gray-500">
                          {{
                            (historyStats &&
                              historyStats.lastAction &&
                              formatToDateTime(
                                historyStats.lastAction.operation_time,
                              )) ||
                            ''
                          }}
                        </div>
                      </div>
                    </div>
                  </Card>

                  <!-- 快速操作 -->
                  <Card
                    class="overflow-hidden rounded-xl border-0 bg-white shadow-lg"
                    :bordered="false"
                    v-if="canApprove || canRollback"
                  >
                    <template #title>
                      <div class="flex items-center gap-2 py-1">
                        <div
                          class="flex h-6 w-6 items-center justify-center rounded-md bg-gradient-to-br from-orange-500 to-red-600"
                        >
                          <span class="text-xs font-bold text-white">⚡</span>
                        </div>
                        <span class="text-sm font-semibold text-gray-800"
                          >快速操作</span
                        >
                      </div>
                    </template>

                    <div class="space-y-2 p-4">
                      <AccessControl
                        v-if="canApprove"
                        :codes="getPermissionCodes.approve"
                        type="code"
                      >
                        <Button
                          block
                          size="middle"
                          class="h-10 rounded-lg border-0 bg-gradient-to-r from-green-500 to-green-600 font-medium text-white shadow-lg transition-all duration-200 hover:from-green-600 hover:to-green-700 hover:shadow-xl"
                          @click="handleApproval('approve')"
                          :loading="loading"
                        >
                          ✓ 审批通过
                        </Button>
                      </AccessControl>
                      <AccessControl
                        v-if="canApprove"
                        :codes="getPermissionCodes.reject"
                        type="code"
                      >
                        <Button
                          block
                          size="middle"
                          class="h-10 rounded-lg border-0 bg-gradient-to-r from-red-500 to-red-600 font-medium text-white shadow-lg transition-all duration-200 hover:from-red-600 hover:to-red-700 hover:shadow-xl"
                          @click="handleApproval('reject')"
                          :loading="loading"
                        >
                          ✗ 审批拒绝
                        </Button>
                      </AccessControl>
                      <AccessControl
                        v-if="canRollback"
                        :codes="getPermissionCodes.rollback"
                        type="code"
                      >
                        <Button
                          block
                          size="middle"
                          class="h-10 rounded-lg border-0 bg-gradient-to-r from-orange-500 to-orange-600 font-medium text-white shadow-lg transition-all duration-200 hover:from-orange-600 hover:to-orange-700 hover:shadow-xl"
                          @click="handleRollback"
                          :loading="loading"
                        >
                          ↺ 回退申请
                        </Button>
                      </AccessControl>
                    </div>
                  </Card>
                </div>
              </div>

              <!-- 工作流进度 -->
              <Card
                class="overflow-hidden rounded-xl border-0 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-lg"
                :bordered="false"
              >
                <template #title>
                  <div class="flex items-center gap-3">
                    <div
                      class="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600"
                    >
                      <Clock class="h-4 w-4 text-white" />
                    </div>
                    <span class="text-lg font-semibold text-gray-800"
                      >审批进度</span
                    >
                  </div>
                </template>

                <div class="mx-6 mb-6 rounded-lg bg-white p-6 shadow-sm">
                  <div class="mb-4 flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-600"
                      >整体进度</span
                    >
                    <span class="text-sm font-semibold text-gray-800"
                      >{{ workflowProgress.percent }}%</span
                    >
                  </div>
                  <Progress
                    :percent="workflowProgress.percent"
                    :status="progressStatus"
                    :stroke-color="workflowProgress.strokeColor"
                    :stroke-width="8"
                    class="mb-6"
                  />

                  <div class="grid grid-cols-4 gap-4">
                    <div
                      v-for="(step, index) in workflowSteps"
                      :key="step.title"
                      class="flex flex-col items-center rounded-lg p-3 transition-all duration-300"
                      :class="{
                        'border-2 border-blue-200 bg-blue-50':
                          step.status === 'process',
                        'border-2 border-green-200 bg-green-50':
                          step.status === 'finish',
                        'border-2 border-red-200 bg-red-50':
                          step.status === 'error',
                        'border border-gray-200 bg-gray-50':
                          step.status === 'wait',
                      }"
                    >
                      <div
                        class="mb-2 flex h-10 w-10 items-center justify-center rounded-full text-sm font-semibold transition-all duration-300"
                        :class="{
                          'bg-blue-500 text-white': step.status === 'process',
                          'bg-green-500 text-white': step.status === 'finish',
                          'bg-red-500 text-white': step.status === 'error',
                          'bg-gray-300 text-gray-600': step.status === 'wait',
                        }"
                      >
                        <span v-if="step.status === 'finish'">✓</span>
                        <span v-else-if="step.status === 'error'">✗</span>
                        <span v-else>{{ index + 1 }}</span>
                      </div>
                      <div
                        class="mb-1 text-center text-xs font-medium text-gray-700"
                      >
                        {{ step.title }}
                      </div>
                      <div class="text-center text-xs text-gray-500">
                        {{ step.description }}
                      </div>
                    </div>
                  </div>
                </div>
              </Card>

              <!-- 详细历史记录 -->
              <Card
                class="overflow-hidden rounded-xl border-0 bg-white shadow-lg"
                :bordered="false"
                v-if="
                  workflowStatus &&
                  Array.isArray(workflowStatus) &&
                  workflowStatus.length > 0
                "
              >
                <template #title>
                  <div class="flex items-center gap-3 py-2">
                    <div
                      class="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-indigo-500 to-purple-600"
                    >
                      <span class="text-sm font-bold text-white">📋</span>
                    </div>
                    <span class="text-lg font-semibold text-gray-800"
                      >操作历史记录</span
                    >
                    <Badge :count="totalHistoryCount" class="ml-2" />
                  </div>
                </template>

                <div class="p-6">
                  <Timeline class="custom-timeline">
                    <!-- 合并并排序所有历史记录 -->
                    <TimelineItem
                      v-for="(item, index) in historyItems"
                      :key="`history-${index}`"
                      :color="
                        (item && item.action) === 'approve'
                          ? 'green'
                          : (item && item.action) === 'reject'
                            ? 'red'
                            : 'orange'
                      "
                    >
                      <template #dot>
                        <div
                          class="flex h-8 w-8 items-center justify-center rounded-full font-semibold text-white shadow-lg"
                          :class="{
                            'bg-gradient-to-br from-green-500 to-green-600':
                              (item && item.action) === 'approve',
                            'bg-gradient-to-br from-red-500 to-red-600':
                              (item && item.action) === 'reject',
                            'bg-gradient-to-br from-orange-500 to-amber-600':
                              (item && item.action) === 'rollback',
                            'bg-gradient-to-br from-blue-500 to-blue-600':
                              (item && item.action) === 'create',
                            'bg-gradient-to-br from-gray-500 to-gray-600':
                              (item && item.action) === 'cancel',
                          }"
                        >
                          <span v-if="(item && item.action) === 'approve'"
                            >✓</span
                          >
                          <span v-else-if="(item && item.action) === 'reject'"
                            >✗</span
                          >
                          <span v-else-if="(item && item.action) === 'rollback'"
                            >↺</span
                          >
                          <span v-else-if="(item && item.action) === 'create'"
                            >+</span
                          >
                          <span v-else-if="(item && item.action) === 'cancel'"
                            >×</span
                          >
                          <span v-else>•</span>
                        </div>
                      </template>

                      <div
                        class="rounded-lg border border-gray-100 bg-white p-4 shadow-sm transition-shadow duration-200 hover:shadow-md"
                      >
                        <div class="mb-3 flex items-center gap-3">
                          <Avatar
                            :size="32"
                            class="font-semibold text-white"
                            :class="{
                              'bg-gradient-to-br from-green-500 to-green-600':
                                item?.action === 'approve',
                              'bg-gradient-to-br from-red-500 to-red-600':
                                item?.action === 'reject',
                              'bg-gradient-to-br from-orange-500 to-amber-600':
                                item?.action === 'rollback',
                              'bg-gradient-to-br from-blue-500 to-blue-600':
                                item?.action === 'create',
                              'bg-gradient-to-br from-gray-500 to-gray-600':
                                item?.action === 'cancel',
                            }"
                          >
                            {{ String(item?.operator_name || 'U').charAt(0) }}
                          </Avatar>
                          <div class="flex-1">
                            <div class="font-medium text-gray-900">
                              {{ item?.operator_name || '未知用户' }}
                            </div>
                            <div class="text-sm text-gray-500">
                              {{
                                formatToDateTime(item?.operation_time) ||
                                '未知时间'
                              }}
                            </div>
                          </div>
                          <Tag
                            :color="
                              ACTION_CONFIG[(item && item.action) as string]
                                ?.color || 'default'
                            "
                            class="rounded-full px-3 py-1 text-sm font-medium"
                          >
                            {{
                              ACTION_CONFIG[(item && item.action) as string]
                                ?.text || '其他操作'
                            }}
                          </Tag>
                        </div>

                        <div class="mb-2 text-sm text-gray-600">
                          <span v-if="item?.action === 'rollback'">
                            {{
                              STATUS_CONFIG[item?.previous_status || '']
                                ?.text || '未知状态'
                            }}
                            回退到
                            {{
                              STATUS_CONFIG[item?.new_status || '']?.text ||
                              '未知状态'
                            }}
                          </span>
                          <span v-else-if="item?.action === 'create'">
                            状态变更：{{
                              STATUS_CONFIG[item?.new_status || '']?.text ||
                              '项目经理审批'
                            }}
                          </span>
                          <span v-else-if="item?.action === 'cancel'">
                            取消前状态：{{
                              STATUS_CONFIG[item?.previous_status || '']
                                ?.text || '未知状态'
                            }}
                          </span>
                          <span v-else>
                            {{
                              STATUS_CONFIG[item?.previous_status || '']
                                ?.text || '未知状态'
                            }}
                            到
                            {{
                              STATUS_CONFIG[item?.new_status || '']?.text ||
                              '未知状态'
                            }}
                          </span>
                        </div>

                        <div
                          v-if="item?.comments"
                          class="rounded-lg border-l-4 bg-gray-50 p-3"
                          :class="{
                            'border-green-500': item?.action === 'approve',
                            'border-red-500': item?.action === 'reject',
                            'border-orange-500': item?.action === 'rollback',
                            'border-blue-500': item?.action === 'create',
                            'border-gray-500': item?.action === 'cancel',
                          }"
                        >
                          <div
                            class="mb-1 text-xs font-semibold uppercase tracking-wide text-gray-500"
                          >
                            {{
                              {
                                approve: '审批意见',
                                reject: '拒绝原因',
                                rollback: '回退原因',
                                create: '创建说明',
                                cancel: '取消原因',
                              }[(item && item.action) as string] || '操作说明'
                            }}
                          </div>
                          <div class="text-sm leading-relaxed text-gray-800">
                            {{ item?.comments }}
                          </div>
                        </div>
                      </div>
                    </TimelineItem>
                  </Timeline>
                </div>
              </Card>
            </div>
          </Spin>
        </div>

        <!-- 审批弹窗 -->
        <Modal
          v-model:open="isApprovalModalVisible"
          :title="currentAction === 'approve' ? '审批通过' : '审批拒绝'"
          :confirm-loading="loading"
          @ok="confirmApproval"
          class="custom-modal"
          :width="600"
        >
          <div class="p-6">
            <div class="mb-6">
              <div class="mb-4 flex items-center gap-3">
                <div
                  class="flex h-12 w-12 items-center justify-center rounded-full"
                  :class="
                    currentAction === 'approve'
                      ? 'bg-green-100 text-green-600'
                      : 'bg-red-100 text-red-600'
                  "
                >
                  <span class="text-xl font-bold">{{
                    currentAction === 'approve' ? '✓' : '✗'
                  }}</span>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900">
                    {{
                      currentAction === 'approve'
                        ? '确认审批通过'
                        : '确认审批拒绝'
                    }}
                  </h3>
                  <p class="text-sm text-gray-500">请输入您的审批意见</p>
                </div>
              </div>
              <label class="mb-3 block text-sm font-semibold text-gray-700"
                >审批意见</label
              >
              <Textarea
                v-model:value="approvalComments"
                :rows="4"
                placeholder="请输入审批意见..."
                :maxlength="500"
                show-count
                class="rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
          </div>
        </Modal>

        <!-- 回退弹窗 -->
        <Modal
          v-model:open="isRollbackModalVisible"
          title="回退申请"
          :confirm-loading="loading"
          @ok="confirmRollback"
          class="custom-modal"
          :width="600"
        >
          <div class="p-6">
            <div class="mb-6">
              <div class="mb-4 flex items-center gap-3">
                <div
                  class="flex h-12 w-12 items-center justify-center rounded-full bg-orange-100 text-orange-600"
                >
                  <span class="text-xl font-bold">↺</span>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900">
                    确认回退申请
                  </h3>
                  <p class="text-sm text-gray-500">请输入回退原因</p>
                </div>
              </div>
              <label class="mb-3 block text-sm font-semibold text-gray-700"
                >回退原因</label
              >
              <Textarea
                v-model:value="rollbackComments"
                :rows="4"
                placeholder="请输入回退原因..."
                :maxlength="500"
                show-count
                class="rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
          </div>
        </Modal>
      </div>
    </Page>

    <!-- 图片预览弹窗 -->
    <div style="display: none">
      <Image
        v-if="previewVisible"
        :src="previewImage"
        :preview="{
          visible: previewVisible,
          onVisibleChange: (vis) => {
            previewVisible = vis;
          },
          title: previewTitle,
        }"
      />
    </div>
  </div>
</template>

<style scoped>
/* Custom Animations */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
/* stylelint-disable selector-class-pattern */
@media (max-width: 1024px) {
  .grid.grid-cols-1.lg\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .lg\:col-span-2 {
    grid-column: span 1 / span 1;
  }
}

@media (max-width: 768px) {
  .grid.grid-cols-1.md\:grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid.grid-cols-1.md\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}
/* stylelint-enable selector-class-pattern */

:deep(.ant-table-thead > tr > th) {
  font-weight: 600;
  color: #374151;
  background: linear-gradient(to right, #f9fafb, #f3f4f6);
  border-bottom: 2px solid #e5e7eb;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background-color: rgb(59 130 246 / 5%);
}

:deep(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid #f3f4f6;
}

/* Custom Steps Styling */
:deep(.custom-steps .ant-steps-item-process .ant-steps-item-icon) {
  background: linear-gradient(to bottom right, #3b82f6, #2563eb);
  border-color: #3b82f6;
}

:deep(.custom-steps .ant-steps-item-finish .ant-steps-item-icon) {
  background: linear-gradient(to bottom right, #10b981, #059669);
  border-color: #10b981;
}

:deep(.custom-steps .ant-steps-item-error .ant-steps-item-icon) {
  background: linear-gradient(to bottom right, #ef4444, #dc2626);
  border-color: #ef4444;
}

:deep(.custom-steps .ant-steps-item-wait .ant-steps-item-icon) {
  background-color: #d1d5db;
  border-color: #d1d5db;
}

/* Custom Timeline Styling */
:deep(.custom-timeline .ant-timeline-item-tail) {
  border-left: 2px solid #e5e7eb;
}

/* Custom Card Header Styling */
:deep(.ant-card-head) {
  background: linear-gradient(to right, #f9fafb, #f3f4f6);
  border-bottom: 1px solid #e5e7eb;
}

:deep(.ant-card-head-title) {
  font-weight: 600;
  color: #1f2937;
}

/* Modal Styling */
:deep(.custom-modal .ant-modal-header) {
  background: linear-gradient(to right, #eff6ff, #e0e7ff);
  border-bottom: 1px solid #e5e7eb;
}

:deep(.custom-modal .ant-modal-title) {
  font-weight: 600;
  color: #1f2937;
}

/* Progress Bar Custom Colors */
:deep(.ant-progress-bg) {
  border-radius: 9999px;
}

/* Hover Effects */
/* stylelint-disable selector-class-pattern */
.hover\:shadow-xl:hover {
  box-shadow:
    0 20px 25px -5px rgb(0 0 0 / 10%),
    0 10px 10px -5px rgb(0 0 0 / 4%);
}
/* stylelint-enable selector-class-pattern */

.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-fadeInUp {
  animation: fade-in-up 0.6s ease-out;
}

/* Gradient Text */
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: #f3f4f6;
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb {
  background-color: #9ca3af;
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280;
}

/* Custom Table Styling */
</style>
