<script lang="ts" setup>
import type {
  CreateInquiryItemParams,
  CreatePurchaseInquiryParams,
  PurchaseInquiry,
} from '#/api/core/purchase/purchase_inquiry';
import type { PurchaseRequest } from '#/api/core/purchase/purchase_request';
import type { Supplier } from '#/api/core/purchase/supplier';

import {
  computed,
  nextTick,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  watch,
} from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Modal as AModal, Button, message, Tabs } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  createPurchaseInquiry,
  getRequestItemsInquiryStatus,
  updatePurchaseInquiry,
} from '#/api/core/purchase/purchase_inquiry';
import { getPurchaseRequestById } from '#/api/core/purchase/purchase_request';
import { getSupplierDetailApi } from '#/api/core/purchase/supplier';
import { useBasicSchema } from '#/views/purchaseManagement/purchase_inquiry/schema';

import FileUploader from '../../../../components/FileUploader/FileUploader.vue';

const emit = defineEmits(['success']);
const formData = ref<PurchaseInquiry>({
  supplier_id: null as unknown as number,
  total_amount: 0,
  supplier_description: '',
  items: [],
});
const readOnly = ref(false);
const supplierData = ref<null | Supplier>(null);
const requestData = ref<null | PurchaseRequest>(null);
const currentSupplierId = ref<null | number>(null);
const currentRequestId = ref<null | number>(null);
const fileUploaderRef = ref<InstanceType<typeof FileUploader> | null>(null);

const getTitle = computed(() => {
  if (readOnly.value) {
    return '查看采购询价';
  }
  return formData.value?.id ? '编辑采购询价' : '创建采购询价';
});

// 修改表单初始化部分，添加事件处理
const [BasicForm, basicFormApi] = useVbenForm({
  layout: 'vertical',
  schema: useBasicSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-x-8 gap-y-6',
});

// 去掉setInterval并添加事件处理函数
// 创建一个响应式的表单值对象
const formValues = ref<Record<string, any>>({});

// 每次获取表单值并更新到响应式对象
async function updateFormValues() {
  try {
    const values = await basicFormApi.getValues();
    if (values) {
      formValues.value = values;
    }
  } catch (error) {
    console.error('获取表单值失败:', error);
  }
}

// 添加表单onChange处理函数
function handleFormFieldChange() {
  setTimeout(updateFormValues, 100); // 延迟100ms以确保值已更新
}

// 强力禁止数字输入框滚轮事件的函数
function preventNumberInputWheel(event: Event) {
  // 多重阻止确保事件被完全拦截
  event.preventDefault();
  event.stopPropagation();
  event.stopImmediatePropagation();

  // 对于某些浏览器，还需要返回false
  return false;
}

// 为数字输入框添加滚轮事件监听器
function addWheelPreventionToNumberInputs() {
  // 查找所有数字输入框
  const numberInputs = document.querySelectorAll('input[type="number"]');

  numberInputs.forEach((input) => {
    // 移除可能已存在的监听器（避免重复添加）
    input.removeEventListener('wheel', preventNumberInputWheel);
    input.removeEventListener('mousewheel', preventNumberInputWheel);
    input.removeEventListener('DOMMouseScroll', preventNumberInputWheel);

    // 添加强制事件监听器，使用 capture 和 passive: false
    input.addEventListener('wheel', preventNumberInputWheel, {
      passive: false,
      capture: true,
    });
    input.addEventListener('mousewheel', preventNumberInputWheel, {
      passive: false,
      capture: true,
    });
    input.addEventListener('DOMMouseScroll', preventNumberInputWheel, {
      passive: false,
      capture: true,
    });
  });
}

// 监听DOM变化，为新添加的数字输入框添加事件监听器
function setupMutationObserver() {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;
            // 检查新添加的元素是否是数字输入框
            if (
              element.tagName === 'INPUT' &&
              element.getAttribute('type') === 'number'
            ) {
              const input = element as HTMLInputElement;
              input.addEventListener('wheel', preventNumberInputWheel, {
                passive: false,
                capture: true,
              });
              input.addEventListener('mousewheel', preventNumberInputWheel, {
                passive: false,
                capture: true,
              });
              input.addEventListener(
                'DOMMouseScroll',
                preventNumberInputWheel,
                {
                  passive: false,
                  capture: true,
                },
              );
            }
            // 检查新添加的元素内部是否包含数字输入框
            const numberInputs = element.querySelectorAll(
              'input[type="number"]',
            );
            numberInputs.forEach((input) => {
              input.addEventListener('wheel', preventNumberInputWheel, {
                passive: false,
                capture: true,
              });
              input.addEventListener('mousewheel', preventNumberInputWheel, {
                passive: false,
                capture: true,
              });
              input.addEventListener(
                'DOMMouseScroll',
                preventNumberInputWheel,
                {
                  passive: false,
                  capture: true,
                },
              );
            });
          }
        });
      }
    });
  });

  // 开始观察整个文档的变化
  observer.observe(document.body, {
    childList: true,
    subtree: true,
  });

  return observer;
}

// 在script部分顶部添加全局处理函数
onMounted(() => {
  // 注册全局处理函数
  window.handleRequestChange = (id: number) => {
    handleRequestChange(id);
  };

  window.handleSupplierChange = (id: number) => {
    handleSupplierChange(id);
  };

  // 初始加载完成后的处理
  nextTick(() => {
    updateFormValues();

    // 为所有数字输入框添加滚轮事件阻止
    addWheelPreventionToNumberInputs();

    // 设置DOM变化监听器
    setupMutationObserver();
  });
});

// 组件卸载时清除全局函数
onUnmounted(() => {
  window.handleRequestChange = undefined;
  window.handleSupplierChange = undefined;
});

// 添加防抖标志，避免重复请求
const isHandlingRequest = ref(false);
const isHandlingSupplier = ref(false);

// 监听表单值中的request_id变化
watch(
  () => formValues.value?.request_id,
  async (newVal, oldVal) => {
    if (newVal !== oldVal && newVal && !isHandlingRequest.value) {
      isHandlingRequest.value = true;
      try {
        await handleRequestChange(Number(newVal));
      } finally {
        isHandlingRequest.value = false;
      }
    }
  },
);

// 监听表单值中的supplier_id变化
watch(
  () => formValues.value?.supplier_id,
  async (newVal, oldVal) => {
    if (newVal !== oldVal && newVal && !isHandlingSupplier.value) {
      isHandlingSupplier.value = true;
      try {
        await handleSupplierChange(Number(newVal));
      } finally {
        isHandlingSupplier.value = false;
      }
    }
  },
);

// 当供应商ID改变时获取供应商详情
async function handleSupplierChange(supplierId: null | number) {
  if (!supplierId) {
    supplierData.value = null;
    return;
  }

  // 如果已经是当前供应商ID，避免重复处理
  if (currentSupplierId.value === supplierId) {
    return;
  }

  currentSupplierId.value = supplierId;

  try {
    supplierData.value = await getSupplierDetailApi(supplierId);
  } catch (error) {
    console.error('Failed to get supplier details:', error);
    supplierData.value = null;
  }
}

// 首先修改 handleRequestChange 函数，去掉自动切换到明细标签的代码
async function handleRequestChange(requestId: null | number) {
  if (!requestId) {
    requestData.value = null;
    itemList.length = 0;
    return;
  }

  // 如果已经是当前请求ID，避免重复处理
  if (currentRequestId.value === requestId) {
    return;
  }

  currentRequestId.value = requestId;

  try {
    // 同时获取采购申请详情和询价状态信息
    const [requestDetail, inquiryStatusResult] = await Promise.all([
      getPurchaseRequestById(requestId),
      getRequestItemsInquiryStatus(requestId),
    ]);

    // 确保数据已正确获取
    if (!requestDetail) {
      console.error('未获取到采购申请详情或返回数据格式不正确');
      return;
    }

    // 设置申请数据
    requestData.value = requestDetail;

    // 检查是否有明细项
    if (!requestDetail.items || requestDetail.items.length === 0) {
      console.warn('采购申请没有明细项');
      message.warning('该采购申请没有明细项，无法创建询价');
      return;
    }

    // 清空现有明细
    itemList.length = 0;

    // 创建一个映射存储每个明细项的询价状态
    const inquiryStatusMap = new Map();
    if (inquiryStatusResult && inquiryStatusResult.items) {
      inquiryStatusResult.items.forEach((item) => {
        inquiryStatusMap.set(item.request_item_id, item);
      });
    }

    // 处理每一个明细项
    requestDetail.items.forEach((requestItem, index) => {
      if (!requestItem || !requestItem.id) {
        console.warn(`第${index + 1}个明细项数据不完整或ID缺失`);
        return;
      }

      // 调整: 先定义接口，然后再使用
      // 修改interface定义
      interface RequestItemSimplified {
        id: number;
        material_type?: string;
        model?: string;
        brand?: string;
        pn?: string;
        spec?: string;
        unit?: string;
        quantity: number;
        remark?: string;
        inquired_quantity?: number; // 已询价数量
        uninquired_quantity?: number; // 未询价数量
        inquiry_progress?: number; // 询价进度
      }

      // 获取该明细的询价状态
      const inquiryStatus = inquiryStatusMap.get(requestItem.id);

      // 确保request_item包含所有必需字段
      const request_item: RequestItemSimplified = {
        id: requestItem.id,
        material_type: requestItem.material_type || '',
        model: requestItem.model || '',
        brand: requestItem.brand || '',
        pn: requestItem.pn || '',
        spec: requestItem.spec || '',
        unit: requestItem.unit || '',
        quantity: requestItem.quantity,
        remark: requestItem.remark || '',
        // 新增字段，从询价状态中获取
        inquired_quantity: inquiryStatus ? inquiryStatus.inquired_quantity : 0,
        uninquired_quantity: inquiryStatus
          ? inquiryStatus.uninquired_quantity
          : requestItem.quantity,
        inquiry_progress: inquiryStatus ? inquiryStatus.inquiry_progress : 0,
      };

      // 将明细添加到列表中
      itemList.push({
        request_item_id: requestItem.id,
        current_inquiry_quantity:
          inquiryStatus && inquiryStatus.uninquired_quantity > 0
            ? inquiryStatus.uninquired_quantity
            : requestItem.quantity,
        pn: requestItem.pn || '',
        budget_price: undefined,
        remark: '',
        request_item,
      });
    });

    // 如果有关联的项目，自动设置项目ID
    if (requestDetail.project_id) {
      basicFormApi.setFieldValue('project_id', requestDetail.project_id);
    }

    // 强制更新视图
    nextTick(() => {
      // 如果没有成功添加明细，显示警告
      if (itemList.length === 0) {
        message.warning('未能成功加载明细项，请检查采购申请数据');
      } else {
        message.success(`已成功加载${itemList.length}个明细项`);
      }
    });
  } catch (error) {
    console.error('获取采购申请详情失败:', error);
    message.error('获取采购申请详情失败，请检查网络连接');
    requestData.value = null;
  }
}

// 移除 watchEffect，避免与 watch 重复监听

// 修改interface定义
interface RequestItemSimplified {
  id: number;
  material_type?: string;
  model?: string;
  brand?: string;
  pn?: string;
  spec?: string;
  unit?: string;
  quantity: number;
  remark?: string;
  inquired_quantity?: number; // 已询价数量
  uninquired_quantity?: number; // 未询价数量
  inquiry_progress?: number; // 询价进度
}

interface InquiryItemFormData extends CreateInquiryItemParams {
  request_item?: RequestItemSimplified;
}

const itemList = reactive<InquiryItemFormData[]>([]);

// Add a function to delete an inquiry item by index
function deleteInquiryItem(index: number) {
  if (index >= 0 && index < itemList.length) {
    itemList.splice(index, 1);
    message.success('已删除询价明细项');

    // Check if the itemList is now empty
    if (itemList.length === 0) {
      message.warning('注意：询价单没有明细项，提交前请确保添加询价项目');
    }
  }
}

function resetForm() {
  basicFormApi.resetForm();
  basicFormApi.setValues(formData.value || {});
  itemList.length = 0;

  if (formData.value?.items) {
    formData.value.items.forEach((item) => {
      itemList.push({
        request_item_id: item.request_item_id,
        current_inquiry_quantity: item.current_inquiry_quantity,
        pn: item.pn || '',
        budget_price: item.budget_price,
        remark: item.remark || '',
        request_item: item.request_item,
      });
    });
  }

  // 重新获取关联数据
  if (formData.value?.supplier_id) {
    handleSupplierChange(Number(formData.value.supplier_id));
  }
  if (formData.value?.request_id) {
    handleRequestChange(Number(formData.value.request_id));
  }
}

const [Modal, modalApi] = useVbenModal({
  class: '!w-full',
  async onConfirm() {
    if (readOnly.value) {
      modalApi.close();
      return;
    }

    try {
      const { valid } = await basicFormApi.validate();
      if (!valid) {
        modalApi.lock(false);
        message.error('请检查必填项');
        return;
      }

      modalApi.lock();

      // 检查是否选择了采购申请和供应商
      const basicData = await basicFormApi.getValues();
      if (!basicData.request_id) {
        modalApi.lock(false);
        message.error('请选择关联的采购申请');
        return;
      }
      if (!basicData.supplier_id) {
        modalApi.lock(false);
        message.error('请选择目标供应商');
        return;
      }

      // 检查是否有询价明细
      if (itemList.length === 0) {
        modalApi.lock(false);
        message.error('请添加至少一个询价明细项');
        return;
      }

      // 检查明细项是否填写完整
      for (const item of itemList) {
        if (!item.request_item_id) {
          message.error('请选择关联的申请明细项');
          modalApi.lock(false);
          return;
        }
        if (item.current_inquiry_quantity <= 0) {
          message.error('本次询价数量必须大于0');
          modalApi.lock(false);
          return;
        }
        if (!item.budget_price) {
          message.error('请填写预算单价');
          modalApi.lock(false);
          return;
        }
      }

      try {
        // 构建提交数据
        const data: CreatePurchaseInquiryParams = {
          request_id: basicData.request_id
            ? Number(basicData.request_id)
            : undefined,
          project_id: requestData.value?.project_id,
          supplier_id: Number(basicData.supplier_id),
          supplier_description: basicData.supplier_description || '',
          request_items: itemList.map((item) => ({
            request_item_id: item.request_item_id,
            current_inquiry_quantity: item.current_inquiry_quantity,
            pn: item.pn || '',
            budget_price: item.budget_price,
            remark: item.remark || '',
          })),
        };

        // 显示确认弹窗
        showConfirmModal(data);
      } catch (error) {
        console.error('数据准备失败:', error);
        modalApi.lock(false);
      }
    } catch (error) {
      console.error('表单验证错误:', error);
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<PurchaseInquiry & { readOnly?: boolean }>();
      if (data) {
        readOnly.value = !!data.readOnly;

        if (data.readOnly === undefined) {
          formData.value = { ...data };
        } else {
          const { readOnly: _, ...restData } = data;
          formData.value = { ...restData };
        }

        basicFormApi.setValues(formData.value || {});

        // 初始化明细项
        itemList.length = 0;
        if (formData.value.items) {
          formData.value.items.forEach((item) => {
            const requestItemData: RequestItemSimplified =
              item.request_item || {
                id: item.request_item_id,
                material_type: '',
                model: '',
                brand: '',
                pn: item.pn || '',
                spec: '',
                unit: '',
                quantity: item.current_inquiry_quantity,
                remark: '',
              };

            itemList.push({
              request_item_id: item.request_item_id,
              current_inquiry_quantity: item.current_inquiry_quantity,
              pn: item.pn || '',
              budget_price: item.budget_price,
              remark: item.remark || '',
              request_item: requestItemData,
            });
          });
        }

        // 获取关联数据
        if (formData.value.supplier_id) {
          handleSupplierChange(Number(formData.value.supplier_id));
        }
        if (formData.value.request_id) {
          handleRequestChange(Number(formData.value.request_id));
        }
      } else {
        readOnly.value = false;
        formData.value = {
          supplier_id: null as unknown as number,
          total_amount: 0,
          supplier_description: '',
          items: [],
        };
        basicFormApi.setValues(formData.value);
        itemList.length = 0;
      }
    }
  },
});

const activeKey = ref('basic');

// 二次确认弹窗
const confirmModalVisible = ref(false);
const confirmLoading = ref(false);
const pendingData = ref<CreatePurchaseInquiryParams | null>(null);

// 显示确认弹窗
function showConfirmModal(data: CreatePurchaseInquiryParams) {
  pendingData.value = data;
  confirmModalVisible.value = true;
}

// 确认提交
async function handleConfirmSubmit() {
  if (!pendingData.value) return;

  confirmLoading.value = true;
  try {
    // 创建或更新询价
    const result = await (formData.value?.id
      ? updatePurchaseInquiry(formData.value.id, {
          id: formData.value.id,
          ...pendingData.value,
          updated_by: 1, // TODO: 从上下文获取当前用户ID
        })
      : createPurchaseInquiry(pendingData.value));

    // 上传相关文件（如果有）
    if (fileUploaderRef.value && result?.id) {
      try {
        await fileUploaderRef.value.uploadFilesToServer(result.id);
      } catch (error) {
        console.error('文件上传失败:', error);
      }
    }

    confirmModalVisible.value = false;
    modalApi.close();
    emit('success');
  } catch (error) {
    console.error('提交失败:', error);
  } finally {
    confirmLoading.value = false;
    modalApi.lock(false);
  }
}

// 取消确认
function handleCancelConfirm() {
  confirmModalVisible.value = false;
  modalApi.lock(false);
  pendingData.value = null;
}

// 添加日期格式化函数
function formatDate(dateString: null | string | undefined): string {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    if (Number.isNaN(date.getTime())) return dateString;

    // 格式化为 YYYY-MM-DD 格式
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error('日期格式化错误:', error);
    return dateString;
  }
}

// 添加辅助方法
function isQuantityExceeded(item: any) {
  if (!item) return false;
  const uninquiredQty =
    item.request_item?.uninquired_quantity || item.request_item?.quantity || 0;
  return item.current_inquiry_quantity > uninquiredQty;
}

function getMaxQuantity(item: any) {
  if (!item || !item.request_item) return 0;
  return (
    item.request_item.uninquired_quantity || item.request_item.quantity || 0
  );
}

function hasInquiredQuantity(item: any) {
  if (!item || !item.request_item) return false;
  return (item.request_item.inquired_quantity || 0) > 0;
}
</script>

<template>
  <Modal :title="getTitle" width="1000px">
    <Tabs v-model:active-key="activeKey" class="mx-4">
      <Tabs.TabPane key="basic" tab="基本信息">
        <!-- 关联采购申请信息展示 - 进一步美化 -->
        <div
          v-if="requestData"
          class="mx-4 mb-6 rounded-lg border border-blue-100 bg-blue-50 p-4 shadow-sm"
        >
          <div class="mb-3 flex items-center">
            <div class="mr-2 h-5 w-1 rounded bg-blue-500"></div>
            <div class="text-base font-medium text-blue-700">
              关联采购申请信息
            </div>
          </div>
          <div class="grid grid-cols-1 gap-x-8 gap-y-3 text-sm md:grid-cols-2">
            <div class="flex items-center">
              <span class="mr-2 w-24 flex-shrink-0 font-semibold text-blue-700"
                >申请单号:</span
              >
              <span class="font-medium text-gray-700">{{
                requestData.request_no
              }}</span>
            </div>
            <div class="flex items-center">
              <span class="mr-2 w-24 flex-shrink-0 font-semibold text-blue-700"
                >申请类型:</span
              >
              <span class="text-gray-700">
                {{
                  Array.isArray(requestData.request_type)
                    ? requestData.request_type
                        .join(', ')
                        .replace(/[\[\]"]/g, '')
                    : requestData.request_type
                }}
              </span>
            </div>
            <div v-if="requestData.project_name" class="flex items-center">
              <span class="mr-2 w-24 flex-shrink-0 font-semibold text-blue-700"
                >所属项目:</span
              >
              <span class="text-gray-700">{{ requestData.project_name }}</span>
            </div>

            <div
              v-if="requestData.expected_delivery_date"
              class="col-span-2 flex items-center"
            >
              <span class="mr-2 w-24 flex-shrink-0 font-semibold text-blue-700"
                >期望交付日期:</span
              >
              <span class="text-gray-700">
                {{ formatDate(requestData.expected_delivery_date) }}
              </span>
            </div>
            <div class="col-span-2 flex items-start">
              <span
                class="mr-2 mt-0.5 w-24 flex-shrink-0 font-semibold text-blue-700"
                >申请事由:</span
              >
              <span class="text-gray-700">{{ requestData.reason }}</span>
            </div>
          </div>
        </div>

        <!-- 提示信息 - 移到表单上方 -->
        <div
          v-if="!requestData && !readOnly"
          class="mx-4 mb-6 rounded-lg border border-yellow-100 bg-yellow-50 p-4"
        >
          <div class="flex items-center text-yellow-700">
            <span class="mr-2 text-lg">⚠️</span>
            <span class="font-medium"
              >请先选择关联采购申请单，系统会自动加载申请明细</span
            >
          </div>
        </div>

        <BasicForm
          :disabled="readOnly"
          class="mx-4 mb-4"
          @change="handleFormFieldChange"
        />

        <div class="mx-4 mb-6">
          <div class="mb-2 text-base font-medium">附件上传</div>
          <FileUploader
            ref="fileUploaderRef"
            file-description="询价附件"
            module-type="purchase_inquiries"
            :module-id="formData?.id"
            :disabled="readOnly"
            show-upload-list
            :max-count="5"
            multiple
            accept="*"
            list-type="text"
            compact
          >
            <Button v-if="!readOnly" type="primary" size="small">
              选择文件
            </Button>
          </FileUploader>
        </div>

        <!-- 移除供应商信息展示 -->

        <!-- 移除调试信息 -->
      </Tabs.TabPane>

      <Tabs.TabPane key="items" tab="询价明细" :disabled="!requestData">
        <div class="mx-4">
          <div
            v-if="itemList.length === 0"
            class="rounded border border-dashed border-gray-300 bg-gray-50 py-10 text-center text-gray-500"
          >
            <div class="mb-3">
              <div
                class="mb-3 inline-flex h-12 w-12 items-center justify-center rounded-full bg-gray-100"
              >
                <span class="text-xl text-gray-400">+</span>
              </div>
            </div>
            <div class="mb-1 text-base font-medium">暂无询价明细</div>
            <div class="text-sm text-gray-400">
              请先在基本信息页选择采购申请
            </div>
          </div>

          <div v-else>
            <div class="mb-4 flex items-center justify-between">
              <h3 class="text-lg font-medium">
                物料询价明细 ({{ itemList.length }}项)
              </h3>
              <div class="text-sm text-blue-600">已从采购申请自动加载</div>
            </div>

            <div
              v-for="(item, index) in itemList"
              :key="index"
              class="mb-4 rounded border border-gray-200 bg-white p-4 shadow-sm transition-all hover:border-gray-300"
            >
              <div
                class="mb-4 flex items-center justify-between border-b border-gray-200 pb-2"
              >
                <div class="flex items-center gap-2">
                  <div
                    class="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 text-xs font-bold text-blue-800"
                  >
                    {{ index + 1 }}
                  </div>
                  <h3 class="text-sm font-semibold text-gray-800">
                    {{ item.request_item?.material_type || '询价项目' }}
                    {{ index + 1 }}
                  </h3>
                  <div v-if="item.request_item" class="text-xs text-gray-500">
                    ({{ item.request_item.material_type }} -
                    {{ item.request_item.model }})
                  </div>
                </div>
                <Button
                  v-if="!readOnly"
                  type="text"
                  danger
                  size="small"
                  @click="deleteInquiryItem(index)"
                >
                  <span class="text-red-500">删除</span>
                </Button>
              </div>

              <!-- 关联申请明细信息 -->
              <div v-if="item.request_item" class="mb-4 rounded bg-gray-50 p-3">
                <div class="mb-2 text-xs font-medium text-gray-700">
                  物料信息
                </div>
                <div
                  class="grid grid-cols-2 gap-2 text-xs text-gray-600 md:grid-cols-4"
                >
                  <div>
                    <span class="font-medium">物料类型:</span>
                    {{ item.request_item.material_type }}
                  </div>
                  <div>
                    <span class="font-medium">型号:</span>
                    {{ item.request_item.model }}
                  </div>
                  <div>
                    <span class="font-medium">品牌:</span>
                    {{ item.request_item.brand }}
                  </div>
                  <div>
                    <span class="font-medium">需求数量:</span>
                    {{ item.request_item.quantity }}
                    {{ item.request_item.unit }}
                  </div>
                  <div v-if="item.request_item.spec" class="col-span-2">
                    <span class="font-medium">规格:</span>
                    {{ item.request_item.spec }}
                  </div>
                </div>

                <!-- 添加询价状态展示 -->
                <div class="mt-2 border-t border-gray-200 pt-2">
                  <div class="mb-1 text-xs font-medium text-gray-700">
                    询价状态
                  </div>
                  <div class="grid grid-cols-3 gap-2 text-xs">
                    <div class="text-blue-600">
                      <span class="font-medium">已询价数量:</span>
                      {{ item.request_item?.inquired_quantity || 0 }}
                      {{ item.request_item?.unit }}
                    </div>
                    <div class="text-green-600">
                      <span class="font-medium">未询价数量:</span>
                      {{
                        item.request_item?.uninquired_quantity ||
                        item.request_item?.quantity
                      }}
                      {{ item.request_item?.unit }}
                    </div>
                    <div class="text-purple-600">
                      <span class="font-medium">询价进度:</span>
                      {{
                        Math.round(item.request_item?.inquiry_progress || 0)
                      }}%
                    </div>
                  </div>
                  <div v-if="hasInquiredQuantity(item)" class="mt-2">
                    <div class="h-1.5 w-full rounded-full bg-gray-200">
                      <div
                        class="h-1.5 rounded-full bg-blue-500"
                        :style="{
                          width: `${item.request_item?.inquiry_progress || 0}%`,
                        }"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 询价信息 -->
              <div class="mb-4">
                <div
                  class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4"
                >
                  <div>
                    <div class="mb-1.5 text-xs font-medium text-gray-700">
                      本次询价数量 <span class="text-red-500">*</span>
                    </div>
                    <input
                      v-model.number="item.current_inquiry_quantity"
                      :disabled="readOnly"
                      class="w-full rounded border border-gray-300 px-2.5 py-2 text-sm transition-colors focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      placeholder="本次询价数量"
                      type="number"
                      min="1"
                      :max="getMaxQuantity(item)"
                    />
                    <div
                      v-if="isQuantityExceeded(item)"
                      class="mt-1 text-xs text-red-500"
                    >
                      数量超过可询价数量 ({{ getMaxQuantity(item) }}
                      {{ item.request_item?.unit }})
                    </div>
                  </div>

                  <div>
                    <div class="mb-1.5 text-xs font-medium text-gray-700">
                      PN号
                    </div>
                    <input
                      v-model="item.pn"
                      :disabled="readOnly"
                      class="w-full rounded border border-gray-300 px-2.5 py-2 text-sm transition-colors focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      placeholder="请输入PN号"
                    />
                  </div>

                  <div>
                    <div class="mb-1.5 text-xs font-medium text-gray-700">
                      预算单价 <span class="text-red-500">*</span>
                    </div>
                    <div class="relative">
                      <input
                        v-model.number="item.budget_price"
                        :disabled="readOnly"
                        class="w-full rounded border border-gray-300 px-2.5 py-2 pl-5 text-sm transition-colors focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                        placeholder="预算单价"
                        type="number"
                        min="0"
                        step="0.01"
                      />
                      <div
                        class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-2 text-gray-500"
                      >
                        ¥
                      </div>
                    </div>
                  </div>

                  <div>
                    <div class="mb-1.5 text-xs font-medium text-gray-700">
                      预算金额
                    </div>
                    <div class="relative">
                      <input
                        :value="
                          item.budget_price && item.current_inquiry_quantity
                            ? (
                                item.budget_price *
                                item.current_inquiry_quantity
                              ).toFixed(2)
                            : ''
                        "
                        disabled
                        class="w-full rounded border border-gray-300 bg-gray-50 px-2.5 py-2 pl-5 text-sm text-gray-500"
                        placeholder="自动计算"
                      />
                      <div
                        class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-2 text-gray-500"
                      >
                        ¥
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 备注信息 -->
              <div>
                <div class="mb-1.5 text-xs font-medium text-gray-700">
                  备注说明
                </div>
                <textarea
                  v-model="item.remark"
                  :disabled="readOnly"
                  class="w-full resize-none rounded border border-gray-300 px-2.5 py-2 text-sm transition-colors focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  placeholder="请输入备注说明（可选）"
                  rows="2"
                ></textarea>
              </div>
            </div>
          </div>
        </div>
      </Tabs.TabPane>
    </Tabs>

    <template #prepend-footer>
      <div v-if="!readOnly" class="flex-auto">
        <Button type="primary" danger @click="resetForm"> 重置 </Button>
      </div>
    </template>
  </Modal>

  <!-- 确认提交弹窗 -->
  <AModal
    v-model:open="confirmModalVisible"
    title="确认提交询价申请"
    width="700px"
    :confirm-loading="confirmLoading"
    @ok="handleConfirmSubmit"
    @cancel="handleCancelConfirm"
  >
    <div class="space-y-6">
      <!-- 采购申请信息 -->
      <div
        v-if="requestData"
        class="rounded border border-blue-200 bg-blue-50 p-4"
      >
        <div class="mb-3 flex items-center">
          <div class="mr-2 h-4 w-1 rounded bg-blue-500"></div>
          <h3 class="text-sm font-semibold text-blue-800">关联采购申请</h3>
        </div>
        <div class="grid grid-cols-2 gap-2 text-sm text-blue-700">
          <div>
            <span class="font-medium">申请单号:</span>
            {{ requestData.request_no }}
          </div>
          <div>
            <span class="font-medium">申请类型:</span>
            {{ requestData.request_type }}
          </div>
          <div v-if="requestData.project_name" class="col-span-2">
            <span class="font-medium">所属项目:</span>
            {{ requestData.project_name }}
          </div>
          <div class="col-span-2">
            <span class="font-medium">申请事由:</span>
            {{ requestData.reason }}
          </div>
        </div>
      </div>

      <!-- 供应商信息 -->
      <div
        v-if="supplierData"
        class="rounded border border-green-200 bg-green-50 p-4"
      >
        <div class="mb-3 flex items-center">
          <div class="mr-2 h-4 w-1 rounded bg-green-500"></div>
          <h3 class="text-sm font-semibold text-green-800">目标供应商</h3>
        </div>
        <div class="grid grid-cols-2 gap-2 text-sm text-green-700">
          <div>
            <span class="font-medium">供应商名称:</span>
            {{ supplierData.supplier_name }}
          </div>
          <div v-if="supplierData.contact_person">
            <span class="font-medium">联系人:</span>
            {{ supplierData.contact_person }}
          </div>
          <div v-if="supplierData.contact_phone">
            <span class="font-medium">联系电话:</span>
            {{ supplierData.contact_phone }}
          </div>
          <div v-if="supplierData.contact_email">
            <span class="font-medium">联系邮箱:</span>
            {{ supplierData.contact_email }}
          </div>
        </div>
      </div>

      <!-- 询价明细 -->
      <div>
        <div class="mb-3 flex items-center">
          <div class="mr-2 h-4 w-1 rounded bg-blue-500"></div>
          <h3 class="text-sm font-semibold text-gray-800">
            询价明细 ({{ itemList.length }}项)
          </h3>
        </div>
        <div class="space-y-3">
          <div
            v-for="(item, index) in itemList"
            :key="index"
            class="rounded border border-gray-200 bg-gray-50 p-3"
          >
            <div class="mb-2 flex items-center">
              <div
                class="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 text-xs font-bold text-blue-800"
              >
                {{ index + 1 }}
              </div>
              <span class="text-sm font-medium text-gray-800">
                {{ item.request_item?.material_type || '手动添加项目' }}
              </span>
            </div>
            <div class="ml-7 grid grid-cols-2 gap-2 text-xs text-gray-600">
              <div v-if="item.request_item?.model">
                <span class="font-medium">型号:</span>
                {{ item.request_item.model }}
              </div>
              <div v-if="item.request_item?.brand">
                <span class="font-medium">品牌:</span>
                {{ item.request_item.brand }}
              </div>
              <div>
                <span class="font-medium">本次询价数量:</span>
                {{ item.current_inquiry_quantity }}
                {{ item.request_item?.unit || '' }}
              </div>
              <div v-if="item.budget_price" class="col-span-2">
                <span class="font-medium">预算单价:</span> ¥{{
                  item.budget_price.toFixed(2)
                }}
              </div>
              <div v-if="item.pn" class="col-span-2">
                <span class="font-medium">PN号:</span> {{ item.pn }}
              </div>
              <div v-if="item.remark" class="col-span-2">
                <span class="font-medium">备注:</span> {{ item.remark }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="border-t pt-4 text-center text-sm text-gray-500">
        请确认以上信息无误后点击确定提交
      </div>
    </div>
  </AModal>
</template>

<style scoped>
/* 禁用数字输入框的滚轮行为 */
input[type='number'] {
  appearance: textfield;
}

input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  margin: 0;
  appearance: none;
}

/* 确保数字输入框在获得焦点时也不响应滚轮 */
input[type='number']:focus {
  appearance: textfield;
}
</style>
