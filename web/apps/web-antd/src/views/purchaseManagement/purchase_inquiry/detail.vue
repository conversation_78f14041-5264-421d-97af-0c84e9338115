<script lang="ts" setup>
import type {
  InquiryApprovalHistory,
  InquiryApprovalParams,
  InquiryRollbackParams,
  PurchaseInquiry,
} from '#/api/core/purchase/purchase_inquiry';
import type { ModuleFileListResult } from '#/api/core/upload';

import { computed, nextTick, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { AccessControl } from '@vben/access';
import { Page } from '@vben/common-ui';
import {
  ArrowLeft,
  Calendar,
  FileText,
  Tag as TagIcon,
  User,
} from '@vben/icons';

import {
  Button,
  Image,
  message,
  Modal,
  Spin,
  Step,
  Steps,
  Tag,
  Textarea,
  Timeline,
  TimelineItem,
} from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  approvePurchaseInquiry,
  getPurchaseInquiryById,
  getPurchaseInquiryHistory,
  rollbackPurchaseInquiry,
} from '#/api/core/purchase/purchase_inquiry';
import { getModuleFiles } from '#/api/core/upload';
import { handleDownloadFile, handlePreviewFile } from '#/utils/common/download';
import { formatToDateTime } from '#/utils/common/time';

import { ACTION_CONFIG, INQUIRY_STATUS_CONFIG } from './constants';
import { useItemColumns } from './data';

const route = useRoute();
const router = useRouter();

const loading = ref(false);
const workflowLoading = ref(false);
const purchaseInquiry = ref<PurchaseInquiry>();
const approvalHistory = ref<InquiryApprovalHistory[]>([]);
const approvalComments = ref('');
const rollbackComments = ref('');
const isApprovalModalVisible = ref(false);
const isRollbackModalVisible = ref(false);
const currentAction = ref<'approve' | 'reject'>('approve');

const [ItemGrid, itemGridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useItemColumns(),
    height: 'auto',
    pagerConfig: {
      enabled: false, // 禁用分页
    },
    proxyConfig: {
      autoLoad: false, // 禁用自动加载
      ajax: {
        query: async () => {
          // 直接返回询价单的items数据
          return {
            items: purchaseInquiry.value?.items || [],
            total: purchaseInquiry.value?.items?.length || 0,
          };
        },
      },
    },
    toolbarConfig: {
      enabled: false, // 禁用工具栏
    },
  },
});

// 附件相关变量
const attachmentsLoading = ref(false);
const attachments = ref<ModuleFileListResult[]>([]);
const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');

// 获取询价ID
const inquiryId = computed(() => {
  return Number(route.params.id);
});

// 工作流步骤
const workflowSteps = computed(() => {
  const status = purchaseInquiry.value?.status || 'draft';

  const steps = [
    { title: '创建询价', status: 'finish', description: '询价单已创建' },
    { title: '财务审批', status: 'wait', description: '财务负责人审批' },
    { title: '企业审批', status: 'wait', description: '企业负责人审批' },
    { title: '完成询价', status: 'wait', description: '询价流程完成' },
  ];

  const statusOrder = [
    'draft',
    'finance_review',
    'enterprise_review',
    'approved',
    'completed',
  ];
  const currentIndex = statusOrder.indexOf(status);

  steps.forEach((step, index) => {
    if (index < currentIndex) {
      step.status = 'finish';
    } else if (index === currentIndex) {
      step.status =
        status === 'completed' || status === 'approved' ? 'finish' : 'process';
    } else {
      step.status = 'wait';
    }

    if (['cancelled', 'rejected'].includes(status) && index <= currentIndex) {
      step.status = 'error';
    }
  });

  return steps;
});

// 权限检查
const canApprove = computed(() => {
  // 修改权限逻辑，使审批按钮可见
  return (
    purchaseInquiry.value?.status === 'finance_review' ||
    purchaseInquiry.value?.status === 'enterprise_review'
  );
});

const canRollback = computed(() => {
  // 修改权限逻辑，使回退按钮可见 - 财务审批阶段不允许回退
  return ['completed', 'enterprise_review'].includes(
    purchaseInquiry.value?.status || '',
  );
});

// 获取当前阶段的权限码
const getPermissionCodes = computed(() => {
  const status = purchaseInquiry.value?.status;

  switch (status) {
    case 'enterprise_review': {
      return {
        approve: ['Purchase:PurchaseInquiry:EnterpriseManagerReview'],
        reject: ['Purchase:PurchaseInquiry:EnterpriseManagerReview'],
        rollback: ['Purchase:PurchaseInquiry:EnterpriseManagerReview'],
      };
    }
    case 'finance_review': {
      return {
        approve: ['Purchase:PurchaseInquiry:FinanceManagerReview'],
        reject: ['Purchase:PurchaseInquiry:FinanceManagerReview'],
        rollback: ['Purchase:PurchaseInquiry:FinanceManagerReview'],
      };
    }
    default: {
      return {
        approve: [],
        reject: [],
        rollback: [],
      };
    }
  }
});

// 获取询价详情
async function fetchInquiryDetail() {
  try {
    loading.value = true;
    const response = await getPurchaseInquiryById(inquiryId.value);
    purchaseInquiry.value = response;

    // 加载数据后更新表格数据
    if (purchaseInquiry.value?.items) {
      await nextTick();
      if (itemGridApi && itemGridApi.grid) {
        await itemGridApi.grid.commitProxy('query');
      }
    }
  } catch {
    message.error('获取询价详情失败');
  } finally {
    loading.value = false;
  }
}

// 获取审批历史
async function fetchApprovalHistory() {
  try {
    workflowLoading.value = true;
    const response = await getPurchaseInquiryHistory(inquiryId.value);
    approvalHistory.value = Array.isArray(response) ? response : [];
  } catch {
    message.error('获取审批历史失败');
    approvalHistory.value = [];
  } finally {
    workflowLoading.value = false;
  }
}

// 获取附件列表
async function fetchAttachments() {
  if (!inquiryId.value) return;

  try {
    attachmentsLoading.value = true;
    const files = await getModuleFiles('purchase_inquiries', inquiryId.value);
    attachments.value = Array.isArray(files) ? files : [];
  } catch {
    message.error('获取附件列表失败');
    attachments.value = [];
  } finally {
    attachmentsLoading.value = false;
  }
}

// 处理文件预览
function handlePreview(file: ModuleFileListResult) {
  // 使用通用预览函数
  handlePreviewFile(file, {
    onImagePreview: (imageUrl, title) => {
      previewImage.value = imageUrl;
      previewTitle.value = title;
      previewVisible.value = true;
    },
  });
}

// 处理文件下载
async function handleDownload(file: ModuleFileListResult) {
  // 使用通用下载函数
  await handleDownloadFile(file);
}

// 处理审批
function handleApproval(action: 'approve' | 'reject') {
  currentAction.value = action;
  approvalComments.value = '';
  isApprovalModalVisible.value = true;
}

// 确认审批
async function confirmApproval() {
  if (!approvalComments.value.trim()) {
    message.error('请输入审批意见');
    return;
  }

  try {
    const params: InquiryApprovalParams = {
      inquiry_id: inquiryId.value,
      action: currentAction.value,
      comments: approvalComments.value,
      current_stage: purchaseInquiry.value?.status || '', // 添加当前阶段
    };

    await approvePurchaseInquiry(params);
    message.success(
      currentAction.value === 'approve' ? '审批通过' : '审批拒绝',
    );
    isApprovalModalVisible.value = false;

    // 添加延迟确保后端状态更新
    await new Promise((resolve) => setTimeout(resolve, 800));

    // 刷新数据，如果失败则重试
    let retries = 0;
    const maxRetries = 3;

    const refreshData = async () => {
      try {
        await Promise.all([fetchInquiryDetail(), fetchApprovalHistory()]);
      } catch {
        if (retries < maxRetries) {
          retries++;
          await new Promise((resolve) => setTimeout(resolve, 500));
          await refreshData();
        } else {
          message.warning('数据刷新失败，请手动刷新页面');
        }
      }
    };

    await refreshData();
  } catch {
    message.error('审批失败');
  }
}

// 处理回退
function handleRollback() {
  rollbackComments.value = '';
  isRollbackModalVisible.value = true;
}

// 确认回退
async function confirmRollback() {
  if (!rollbackComments.value.trim()) {
    message.error('请输入回退原因');
    return;
  }

  try {
    const params: InquiryRollbackParams = {
      inquiry_id: inquiryId.value,
      rollback_to: 'finance_review', // 修改为可回退的有效阶段
      current_stage: purchaseInquiry.value?.status || '',
      comments: rollbackComments.value,
    };

    await rollbackPurchaseInquiry(params);
    message.success('回退成功');
    isRollbackModalVisible.value = false;

    // 添加延迟确保后端状态更新
    await new Promise((resolve) => setTimeout(resolve, 800));

    // 刷新数据，如果失败则重试
    let retries = 0;
    const maxRetries = 3;

    const refreshData = async () => {
      try {
        await Promise.all([fetchInquiryDetail(), fetchApprovalHistory()]);
      } catch {
        if (retries < maxRetries) {
          retries++;
          await new Promise((resolve) => setTimeout(resolve, 500));
          await refreshData();
        } else {
          message.warning('数据刷新失败，请手动刷新页面');
        }
      }
    };

    await refreshData();
  } catch {
    message.error('回退失败');
  }
}

// 返回列表
function goBack() {
  router.push('/purchase-management/purchase-inquiry');
}

// 页面初始化
onMounted(async () => {
  await Promise.all([
    fetchInquiryDetail(),
    fetchApprovalHistory(),
    fetchAttachments(),
  ]);

  // 确保表格数据已加载
  if (itemGridApi && itemGridApi.grid && purchaseInquiry.value?.items) {
    await nextTick();
    await itemGridApi.grid.commitProxy('query');
  }
});
</script>

<template>
  <Page auto-content-height class="bg-gray-50/50">
    <div class="mx-auto max-w-full space-y-6 px-4 py-4">
      <!-- 头部操作区 -->
      <div
        class="relative overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm transition-shadow duration-300 hover:shadow-md"
      >
        <div
          class="absolute -right-20 -top-20 h-48 w-48 rounded-full bg-blue-500/5"
        ></div>
        <div
          class="absolute -right-10 -top-10 h-32 w-32 rounded-full bg-indigo-500/10"
        ></div>
        <div class="px-6 py-5">
          <div class="flex flex-wrap items-center justify-between gap-4">
            <div class="flex items-center space-x-6">
              <Button
                type="primary"
                size="large"
                class="flex items-center justify-center rounded-xl !bg-gradient-to-r !from-blue-500 !to-indigo-600 !px-5 !py-3 shadow-sm transition-all duration-300 hover:scale-105 hover:!from-blue-600 hover:!to-indigo-700 hover:shadow-md"
                @click="goBack"
              >
                <div class="flex items-center justify-center">
                  <ArrowLeft class="mr-2 size-5 text-white" />
                  <span class="font-medium text-white">返回列表</span>
                </div>
              </Button>
              <div class="border-l-2 border-indigo-100 pl-6">
                <div class="flex items-center">
                  <h1
                    class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-2xl font-bold text-transparent"
                  >
                    询价详情
                  </h1>
                  <Tag
                    v-if="purchaseInquiry?.status"
                    :color="
                      INQUIRY_STATUS_CONFIG[purchaseInquiry.status as string]
                        ?.color
                    "
                    class="ml-3 !rounded-full !px-3 !py-0.5 !text-xs !font-medium shadow-sm"
                  >
                    {{
                      INQUIRY_STATUS_CONFIG[purchaseInquiry.status as string]
                        ?.text || purchaseInquiry.status
                    }}
                  </Tag>
                </div>
                <div
                  v-if="purchaseInquiry"
                  class="mt-2 flex items-center gap-4"
                >
                  <div class="flex items-center">
                    <div class="mr-2 h-2 w-2 rounded-full bg-blue-500"></div>
                    <span class="text-sm font-medium text-gray-600"
                      >询价单号:
                    </span>
                    <span class="ml-1 text-sm font-bold text-gray-800">{{
                      purchaseInquiry.inquiry_no
                    }}</span>
                  </div>
                  <div
                    v-if="purchaseInquiry.request_no"
                    class="flex items-center"
                  >
                    <div class="mr-2 h-2 w-2 rounded-full bg-emerald-500"></div>
                    <span class="text-sm font-medium text-gray-600"
                      >关联申请单:
                    </span>
                    <span class="ml-1 text-sm font-bold text-gray-800">
                      {{ purchaseInquiry.request_no }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div class="flex space-x-3">
              <AccessControl
                v-if="canApprove"
                :codes="getPermissionCodes.approve"
                type="code"
              >
                <Button
                  type="primary"
                  size="large"
                  class="!h-auto !rounded-xl !border-0 !bg-gradient-to-r !from-green-500 !to-emerald-600 !px-8 !py-2.5 !font-medium shadow-md transition-all hover:scale-105 hover:!from-green-600 hover:!to-emerald-700 hover:shadow-lg"
                  @click="handleApproval('approve')"
                >
                  <span class="text-base text-white">审批通过</span>
                </Button>
              </AccessControl>
              <AccessControl
                v-if="canApprove"
                :codes="getPermissionCodes.reject"
                type="code"
              >
                <Button
                  danger
                  size="large"
                  class="!h-auto !rounded-xl !border-0 !bg-gradient-to-r !from-red-500 !to-rose-600 !px-8 !py-2.5 !font-medium shadow-md transition-all hover:scale-105 hover:!from-red-600 hover:!to-rose-700 hover:shadow-lg"
                  @click="handleApproval('reject')"
                >
                  <span class="text-base text-white">审批拒绝</span>
                </Button>
              </AccessControl>
              <AccessControl
                v-if="canRollback"
                :codes="getPermissionCodes.rollback"
                type="code"
              >
                <Button
                  size="large"
                  class="!h-auto !rounded-xl !border-0 !bg-gradient-to-r !from-amber-500 !to-orange-600 !px-8 !py-2.5 !font-medium shadow-md transition-all hover:scale-105 hover:!from-amber-600 hover:!to-orange-700 hover:shadow-lg"
                  @click="handleRollback"
                >
                  <span class="text-base text-white">回退</span>
                </Button>
              </AccessControl>
            </div>
          </div>
        </div>
      </div>

      <Spin :spinning="loading">
        <!-- 基本信息和询价进度并排 -->
        <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
          <!-- 基本信息 -->
          <div class="lg:col-span-2">
            <div
              class="overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm"
            >
              <div
                class="border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4"
              >
                <h2
                  class="flex items-center text-lg font-semibold text-gray-900"
                >
                  <div class="mr-3 h-6 w-1 rounded-full bg-blue-500"></div>
                  基本信息
                </h2>
              </div>
              <div class="p-6">
                <div
                  v-if="purchaseInquiry"
                  class="grid grid-cols-1 gap-6 md:grid-cols-3"
                >
                  <div
                    class="rounded-lg border border-blue-200/50 bg-gradient-to-br from-blue-50 to-blue-100/50 p-4"
                  >
                    <div class="flex items-center space-x-3">
                      <div
                        class="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-500"
                      >
                        <TagIcon class="size-5 text-white" />
                      </div>
                      <div>
                        <div class="mb-1 text-sm font-medium text-blue-900">
                          询价状态
                        </div>
                        <Tag
                          :color="
                            INQUIRY_STATUS_CONFIG[
                              purchaseInquiry.status as string
                            ]?.color
                          "
                          class="!rounded-md !font-medium"
                        >
                          {{
                            INQUIRY_STATUS_CONFIG[
                              purchaseInquiry.status as string
                            ]?.text || purchaseInquiry.status
                          }}
                        </Tag>
                      </div>
                    </div>
                  </div>

                  <div
                    class="rounded-lg border border-green-200/50 bg-gradient-to-br from-green-50 to-green-100/50 p-4"
                  >
                    <div class="flex items-center space-x-3">
                      <div
                        class="flex h-10 w-10 items-center justify-center rounded-lg bg-green-500"
                      >
                        <User class="size-5 text-white" />
                      </div>
                      <div>
                        <div class="mb-1 text-sm font-medium text-green-900">
                          目标供应商
                        </div>
                        <div class="text-sm font-semibold text-green-800">
                          {{ purchaseInquiry.supplier_name || '未指定' }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="rounded-lg border border-purple-200/50 bg-gradient-to-br from-purple-50 to-purple-100/50 p-4"
                  >
                    <div class="flex items-center space-x-3">
                      <div
                        class="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-500"
                      >
                        <FileText class="size-5 text-white" />
                      </div>
                      <div>
                        <div class="mb-1 text-sm font-medium text-purple-900">
                          关联申请单
                        </div>
                        <div class="text-sm font-semibold text-purple-800">
                          {{ purchaseInquiry.request_no || '无' }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="rounded-lg border border-orange-200/50 bg-gradient-to-br from-orange-50 to-orange-100/50 p-4"
                  >
                    <div class="flex items-center space-x-3">
                      <div
                        class="flex h-10 w-10 items-center justify-center rounded-lg bg-orange-500"
                      >
                        <TagIcon class="size-5 text-white" />
                      </div>
                      <div>
                        <div class="mb-1 text-sm font-medium text-orange-900">
                          预算金额
                        </div>
                        <div class="text-lg font-bold text-orange-800">
                          ¥{{
                            purchaseInquiry.total_amount?.toLocaleString() ||
                            '0'
                          }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="rounded-lg border border-gray-200/50 bg-gradient-to-br from-gray-50 to-gray-100/50 p-4"
                  >
                    <div class="flex items-center space-x-3">
                      <div
                        class="flex h-10 w-10 items-center justify-center rounded-lg bg-gray-500"
                      >
                        <User class="size-5 text-white" />
                      </div>
                      <div>
                        <div class="mb-1 text-sm font-medium text-gray-900">
                          创建人
                        </div>
                        <div class="text-sm font-semibold text-gray-800">
                          {{ purchaseInquiry.creator_name || '未知' }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="rounded-lg border border-slate-200/50 bg-gradient-to-br from-slate-50 to-slate-100/50 p-4"
                  >
                    <div class="flex items-center space-x-3">
                      <div
                        class="flex h-10 w-10 items-center justify-center rounded-lg bg-slate-500"
                      >
                        <Calendar class="size-5 text-white" />
                      </div>
                      <div>
                        <div class="mb-1 text-sm font-medium text-slate-900">
                          创建时间
                        </div>
                        <div class="text-sm font-semibold text-slate-800">
                          {{
                            purchaseInquiry?.created_at
                              ? formatToDateTime(purchaseInquiry.created_at)
                              : '-'
                          }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 询价进度 -->
          <div class="lg:col-span-1">
            <div
              class="h-full overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm"
            >
              <div
                class="border-b border-gray-100 bg-gradient-to-r from-indigo-50 to-blue-50 px-6 py-4"
              >
                <h2
                  class="flex items-center text-lg font-semibold text-gray-900"
                >
                  <div class="mr-3 h-6 w-1 rounded-full bg-indigo-500"></div>
                  询价进度
                </h2>
              </div>
              <div class="space-y-6 p-5">
                <Steps
                  direction="vertical"
                  size="small"
                  :current="
                    workflowSteps.findIndex((s) => s.status === 'process')
                  "
                  class="!pl-0"
                >
                  <Step
                    v-for="(step, index) in workflowSteps"
                    :key="index"
                    :title="step.title"
                    :description="step.description"
                    :status="step.status"
                  />
                </Steps>
              </div>
            </div>
          </div>
        </div>

        <!-- 询价明细单独占一行 -->
        <div
          class="mt-6 overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm"
        >
          <div
            class="border-b border-gray-100 bg-gradient-to-r from-emerald-50 to-teal-50 px-6 py-4"
          >
            <h2 class="flex items-center text-lg font-semibold text-gray-900">
              <div class="mr-3 h-6 w-1 rounded-full bg-emerald-500"></div>
              询价明细
            </h2>
          </div>
          <div class="p-6">
            <div
              v-if="purchaseInquiry?.items?.length"
              class="rounded-lg bg-gray-50/50 p-4"
            >
              <ItemGrid>
                <!-- 报价状态插槽 -->
                <template #quoted-status="{ row }">
                  <Tag
                    :color="row.is_quoted ? 'green' : 'orange'"
                    class="!rounded-md !font-medium"
                  >
                    {{ row.is_quoted ? '已报价' : '未报价' }}
                  </Tag>
                </template>
              </ItemGrid>
            </div>
            <div v-else class="py-12 text-center">
              <div class="flex flex-col items-center space-y-3">
                <div
                  class="flex h-16 w-16 items-center justify-center rounded-full bg-gray-100"
                >
                  <FileText class="size-8 text-gray-400" />
                </div>
                <p class="font-medium text-gray-500">暂无询价明细</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 附件和审批历史 -->
        <div class="mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2">
          <!-- 附件信息 -->
          <div
            class="overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm"
          >
            <div
              class="border-b border-gray-100 bg-gradient-to-r from-violet-50 to-purple-50 px-6 py-4"
            >
              <h2 class="flex items-center text-lg font-semibold text-gray-900">
                <div class="mr-3 h-6 w-1 rounded-full bg-violet-500"></div>
                相关附件
              </h2>
            </div>
            <div class="p-6">
              <Spin :spinning="attachmentsLoading">
                <div v-if="attachments.length === 0" class="py-12 text-center">
                  <div class="flex flex-col items-center space-y-3">
                    <div
                      class="flex h-16 w-16 items-center justify-center rounded-full bg-gray-100"
                    >
                      <FileText class="size-8 text-gray-400" />
                    </div>
                    <p class="font-medium text-gray-500">暂无附件</p>
                  </div>
                </div>
                <div v-else class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div
                    v-for="file in attachments"
                    :key="file.id"
                    class="group cursor-pointer rounded-xl border border-gray-200/60 bg-gradient-to-br from-gray-50 to-gray-100/80 p-4 transition-all duration-200 hover:border-violet-300/50 hover:shadow-md"
                  >
                    <div class="flex items-start space-x-3">
                      <div
                        class="flex h-12 w-12 items-center justify-center rounded-lg bg-violet-100 transition-colors group-hover:bg-violet-200"
                      >
                        <FileText class="size-6 text-violet-600" />
                      </div>
                      <div class="min-w-0 flex-1">
                        <div
                          class="mb-1 truncate text-sm font-semibold text-gray-900"
                        >
                          {{ file.file_name }}
                        </div>
                        <div class="mb-3 text-xs text-gray-500">
                          {{ (file.file_size / 1024).toFixed(1) }} KB
                        </div>
                        <div class="flex space-x-2">
                          <Button
                            size="small"
                            type="primary"
                            class="!rounded-md !px-3 !text-xs"
                            @click="handlePreview(file)"
                          >
                            预览
                          </Button>
                          <Button
                            size="small"
                            class="!rounded-md !px-3 !text-xs"
                            @click="handleDownload(file)"
                          >
                            下载
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Spin>
            </div>
          </div>

          <!-- 审批历史 -->
          <div
            class="overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm"
          >
            <div
              class="border-b border-gray-100 bg-gradient-to-r from-amber-50 to-yellow-50 px-6 py-4"
            >
              <h2 class="flex items-center text-lg font-semibold text-gray-900">
                <div class="mr-3 h-6 w-1 rounded-full bg-amber-500"></div>
                审批历史
              </h2>
            </div>
            <div class="p-6">
              <Spin :spinning="workflowLoading">
                <Timeline
                  v-if="approvalHistory.length > 0"
                  class="custom-timeline !pl-0"
                >
                  <TimelineItem
                    v-for="(history, index) in approvalHistory"
                    :key="index"
                    :color="
                      ACTION_CONFIG[history.action as string]?.color || 'blue'
                    "
                  >
                    <template #dot>
                      <div
                        class="flex h-8 w-8 items-center justify-center rounded-full font-semibold text-white shadow-lg"
                        :class="{
                          'bg-gradient-to-br from-green-500 to-green-600':
                            history.action === 'approve',
                          'bg-gradient-to-br from-red-500 to-red-600':
                            history.action === 'reject',
                          'bg-gradient-to-br from-orange-500 to-amber-600':
                            history.action === 'rollback',
                          'bg-gradient-to-br from-blue-500 to-blue-600':
                            history.action === 'create',
                          'bg-gradient-to-br from-gray-500 to-gray-600':
                            history.action === 'cancel',
                        }"
                      >
                        <span v-if="history.action === 'approve'">✓</span>
                        <span v-else-if="history.action === 'reject'">✗</span>
                        <span v-else-if="history.action === 'rollback'">↺</span>
                        <span v-else-if="history.action === 'create'">+</span>
                        <span v-else-if="history.action === 'cancel'">×</span>
                        <span v-else>•</span>
                      </div>
                    </template>

                    <div
                      class="rounded-lg border border-gray-100 bg-white p-4 shadow-sm transition-shadow duration-200 hover:shadow-md"
                    >
                      <div class="mb-3 flex items-center gap-3">
                        <div
                          class="flex h-8 w-8 items-center justify-center rounded-full font-semibold text-white"
                          :class="{
                            'bg-gradient-to-br from-green-500 to-green-600':
                              history.action === 'approve',
                            'bg-gradient-to-br from-red-500 to-red-600':
                              history.action === 'reject',
                            'bg-gradient-to-br from-orange-500 to-amber-600':
                              history.action === 'rollback',
                            'bg-gradient-to-br from-blue-500 to-blue-600':
                              history.action === 'create',
                            'bg-gradient-to-br from-gray-500 to-gray-600':
                              history.action === 'cancel',
                          }"
                        >
                          {{ String(history.operator_name || 'U').charAt(0) }}
                        </div>
                        <div class="flex-1">
                          <div class="font-medium text-gray-900">
                            {{ history.operator_name || '未知用户' }}
                          </div>
                          <div class="text-sm text-gray-500">
                            {{
                              formatToDateTime(history.operation_time) ||
                              '未知时间'
                            }}
                          </div>
                        </div>
                        <Tag
                          :color="
                            ACTION_CONFIG[history.action as string]?.color ||
                            'default'
                          "
                          class="rounded-full px-3 py-1 text-sm font-medium"
                        >
                          {{
                            ACTION_CONFIG[history.action as string]?.text ||
                            '其他操作'
                          }}
                        </Tag>
                      </div>

                      <div class="mb-2 text-sm text-gray-600">
                        <span v-if="history.action === 'rollback'">
                          {{
                            INQUIRY_STATUS_CONFIG[history.previous_status || '']
                              ?.text || '未知状态'
                          }}
                          回退到
                          {{
                            INQUIRY_STATUS_CONFIG[history.new_status || '']
                              ?.text || '未知状态'
                          }}
                        </span>
                        <span v-else-if="history.action === 'create'">
                          状态变更：{{
                            INQUIRY_STATUS_CONFIG[history.new_status || '']
                              ?.text || '初始状态'
                          }}
                        </span>
                        <span v-else-if="history.action === 'cancel'">
                          取消前状态：{{
                            INQUIRY_STATUS_CONFIG[history.previous_status || '']
                              ?.text || '未知状态'
                          }}
                        </span>
                        <span v-else>
                          {{
                            INQUIRY_STATUS_CONFIG[history.previous_status || '']
                              ?.text || '未知状态'
                          }}
                          到
                          {{
                            INQUIRY_STATUS_CONFIG[history.new_status || '']
                              ?.text || '未知状态'
                          }}
                        </span>
                      </div>

                      <div
                        v-if="history.comments"
                        class="rounded-lg border-l-4 bg-gray-50 p-3"
                        :class="{
                          'border-green-500': history.action === 'approve',
                          'border-red-500': history.action === 'reject',
                          'border-orange-500': history.action === 'rollback',
                          'border-blue-500': history.action === 'create',
                          'border-gray-500': history.action === 'cancel',
                        }"
                      >
                        <div
                          class="mb-1 text-xs font-semibold uppercase tracking-wide text-gray-500"
                        >
                          {{
                            {
                              approve: '审批意见',
                              reject: '拒绝原因',
                              rollback: '回退原因',
                              create: '创建说明',
                              cancel: '取消原因',
                            }[history.action as string] || '操作说明'
                          }}
                        </div>
                        <div class="text-sm leading-relaxed text-gray-800">
                          {{ history.comments }}
                        </div>
                      </div>
                    </div>
                  </TimelineItem>
                </Timeline>
                <div v-else class="py-12 text-center">
                  <div class="flex flex-col items-center space-y-3">
                    <div
                      class="flex h-16 w-16 items-center justify-center rounded-full bg-gray-100"
                    >
                      <User class="size-8 text-gray-400" />
                    </div>
                    <p class="font-medium text-gray-500">暂无审批记录</p>
                  </div>
                </div>
              </Spin>
            </div>
          </div>
        </div>
      </Spin>
    </div>

    <!-- 审批弹窗 -->
    <Modal
      v-model:open="isApprovalModalVisible"
      :title="currentAction === 'approve' ? '审批通过' : '审批拒绝'"
      @ok="confirmApproval"
      class="custom-modal !rounded-xl"
      :width="500"
    >
      <div class="space-y-4 py-4">
        <div class="rounded-lg border border-gray-200/60 bg-gray-50 p-4">
          <label class="mb-2 block text-sm font-semibold text-gray-700">
            审批意见
          </label>
          <Textarea
            v-model:value="approvalComments"
            :rows="4"
            placeholder="请输入审批意见"
            class="!rounded-lg !border-gray-300"
          />
        </div>
      </div>
    </Modal>

    <!-- 回退弹窗 -->
    <Modal
      v-model:open="isRollbackModalVisible"
      title="流程回退"
      @ok="confirmRollback"
      class="custom-modal !rounded-xl"
      :width="500"
    >
      <div class="space-y-4 py-4">
        <div class="rounded-lg border border-gray-200/60 bg-gray-50 p-4">
          <label class="mb-2 block text-sm font-semibold text-gray-700">
            回退原因
          </label>
          <Textarea
            v-model:value="rollbackComments"
            :rows="4"
            placeholder="请输入回退原因"
            class="!rounded-lg !border-gray-300"
          />
        </div>
      </div>
    </Modal>

    <!-- 图片预览弹窗 -->
    <Image
      :preview="{
        visible: previewVisible,
        onVisibleChange: (vis: boolean) => (previewVisible = vis),
      }"
      :src="previewImage"
      style="display: none"
    />
  </Page>
</template>

<style scoped>
/* Custom Timeline Styling */
:deep(.custom-timeline .ant-timeline-item-tail) {
  border-left: 2px solid #e5e7eb;
}

/* Custom Card Header Styling */
:deep(.ant-card-head) {
  background: linear-gradient(to right, #f9fafb, #f3f4f6);
  border-bottom: 1px solid #e5e7eb;
}

:deep(.ant-card-head-title) {
  font-weight: 600;
  color: #1f2937;
}

/* Modal Styling */
:deep(.custom-modal .ant-modal-header) {
  background: linear-gradient(to right, #eff6ff, #e0e7ff);
  border-bottom: 1px solid #e5e7eb;
}

:deep(.custom-modal .ant-modal-title) {
  font-weight: 600;
  color: #1f2937;
}

/* Transition Effects */
.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover Effects */
/* stylelint-disable selector-class-pattern */
.hover\:shadow-md:hover {
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 10%),
    0 2px 4px -1px rgb(0 0 0 / 6%);
}
</style>
