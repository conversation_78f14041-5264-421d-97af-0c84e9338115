<script lang="ts" setup>
import type {
  PurchaseInquiry,
  PurchaseInquiryQuery,
} from '#/api/core/purchase/purchase_inquiry';

import { nextTick, ref } from 'vue';
import { useRouter } from 'vue-router';

import { AccessControl } from '@vben/access';
import { Page, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { Button, Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  // cancelPurchaseInquiry,
  // deletePurchaseInquiry,
  getPurchaseInquiryList,
} from '#/api/core/purchase/purchase_inquiry';

import { INQUIRY_STATUS_CONFIG } from './constants';
import { useColumns, useItemColumns } from './data';
import Form from './modules/form.vue';
import { useGridFormSchema } from './schema';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

const router = useRouter();

// 询价明细模态框相关数据
const detailModalVisible = ref(false);
const currentItems = ref<any[]>([]);
const currentInquiryNo = ref('');

// 询价明细表格
const [DetailGrid, detailGridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useItemColumns(),
    height: 'auto',
    maxHeight: 500,
    pagerConfig: {
      enabled: false,
    },
    proxyConfig: {
      autoLoad: false,
      ajax: {
        query: async () => {
          return {
            items: currentItems.value,
            total: currentItems.value.length,
          };
        },
      },
    },
    toolbarConfig: {
      enabled: false,
    },
  },
});

/**
 * 显示询价明细
 */
async function showItemsDetail(row: PurchaseInquiry) {
  if (row.items && row.items.length > 0) {
    currentItems.value = row.items;
    currentInquiryNo.value = row.inquiry_no || '';
    detailModalVisible.value = true;

    // 等待模态框打开后加载数据
    await nextTick();
    if (detailGridApi && detailGridApi.grid) {
      await detailGridApi.grid.commitProxy('query');
    }
  }
}

/**
 * 创建新询价
 */
function onCreate() {
  formModalApi.setData(null).open();
}

/**
 * 查看询价详情
 */
function onView(row: PurchaseInquiry) {
  router.push(`/purchase-management/purchase-inquiry/detail/${row.id}`);
}

// /**
//  * 删除询价
//  */
// async function onDelete(row: PurchaseInquiry) {
//   try {
//     await deletePurchaseInquiry(row.id!);
//     refreshGrid();
//   } catch (error) {
//     console.error('删除询价失败', error);
//   }
// }

// /**
//  * 取消询价
//  */
// async function onCancel(row: PurchaseInquiry) {
//   try {
//     await cancelPurchaseInquiry(row.id!);
//     refreshGrid();
//   } catch (error) {
//     console.error('取消询价失败', error);
//   }
// }

// 定义分页参数类型
interface PageParams {
  currentPage: number;
  pageSize: number;
}

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['create_time', ['start_date', 'end_date']]],
    schema: useGridFormSchema(),
    submitOnChange: false,
    submitOnEnter: true,
  },
  gridOptions: {
    columns: useColumns(),
    height: 'auto',
    pagerConfig: {
      enabled: true,
    },
    proxyConfig: {
      ajax: {
        query: async (
          { page }: { page: PageParams },
          formValues: Record<string, any>,
        ) => {
          const params: PurchaseInquiryQuery = {
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          };

          const res = await getPurchaseInquiryList(params);

          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    toolbarConfig: {
      custom: true,
      export: false,
      refresh: true,
      refreshOptions: { code: 'query' },
      search: true,
      zoom: true,
      zoomOptions: {},
    },
  },
});

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid>
      <template #toolbar-tools>
        <AccessControl :codes="['Purchase:PurchaseInquiry:Create']" type="code">
          <Button type="primary" @click="onCreate">
            <Plus class="size-5" />
            创建询价
          </Button>
        </AccessControl>
      </template>

      <!-- 询价状态插槽 -->
      <template #status="{ row }">
        <Tag :color="INQUIRY_STATUS_CONFIG[row.status as string]?.color">
          {{ INQUIRY_STATUS_CONFIG[row.status as string]?.text || row.status }}
        </Tag>
      </template>

      <!-- 询价明细按钮插槽 -->
      <template #items_action="{ row }">
        <Button
          type="link"
          :disabled="!row.items || row.items.length === 0"
          @click="showItemsDetail(row)"
        >
          查看明细({{ row.items?.length || 0 }})
        </Button>
      </template>

      <!-- 操作列插槽 -->
      <template #operate="{ row }">
        <Button type="link" @click="onView(row)">查看</Button>
      </template>
    </Grid>

    <!-- 询价明细模态框 -->
    <Modal
      v-model:open="detailModalVisible"
      title="询价明细"
      :width="1400"
      :footer="null"
      :destroy-on-close="true"
      class="inquiry-detail-modal"
    >
      <div class="inquiry-detail-header">
        <div
          class="flex items-center justify-between rounded-lg bg-blue-50 p-4"
        >
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
              <span class="text-sm font-medium text-gray-600">询价单号:</span>
              <span class="text-sm font-semibold text-blue-600">{{
                currentInquiryNo
              }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm font-medium text-gray-600">明细数量:</span>
              <span class="text-sm font-semibold text-green-600"
                >{{ currentItems.length }} 项</span
              >
            </div>
          </div>
        </div>
      </div>

      <div class="inquiry-detail-table">
        <DetailGrid />
      </div>
    </Modal>
  </Page>
</template>

<style lang="less" scoped>
.inquiry-detail-modal {
  :deep(.ant-modal-content) {
    border-radius: 12px;
    overflow: hidden;
  }

  :deep(.ant-modal-header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: none;
    padding: 16px 24px;

    .ant-modal-title {
      color: white;
      font-weight: 600;
      font-size: 16px;
    }
  }

  :deep(.ant-modal-close) {
    color: white;

    &:hover {
      color: rgba(255, 255, 255, 0.8);
    }
  }

  :deep(.ant-modal-body) {
    padding: 0;
  }
}

.inquiry-detail-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.inquiry-detail-table {
  padding: 0 24px 24px;

  :deep(.vxe-table--body-wrapper) {
    max-height: 500px;
  }

  :deep(.vxe-table) {
    border-radius: 8px;
    overflow: hidden;
  }

  :deep(.vxe-table--header) {
    background: #f8fafc;
  }

  :deep(.vxe-table--header-wrapper) {
    .vxe-table--header {
      th {
        background: #f1f5f9;
        color: #475569;
        font-weight: 600;
        border-bottom: 2px solid #e2e8f0;
      }
    }
  }

  :deep(.vxe-table--body) {
    .vxe-table--body-wrapper {
      .vxe-body--row {
        &:hover {
          background-color: #f8fafc;
        }

        &:nth-child(even) {
          background-color: #fafbfc;
        }
      }
    }
  }
}
</style>
