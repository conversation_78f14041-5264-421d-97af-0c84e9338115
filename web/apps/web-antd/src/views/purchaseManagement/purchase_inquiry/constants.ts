/**
 * 采购询价常量定义
 */

// 定义标签配置接口
export interface TagConfig {
  color: string;
  text: string;
}

// 定义标签配置映射接口
export interface TagConfigMap {
  [key: string]: TagConfig;
}

// 询价状态配置
export const INQUIRY_STATUS_CONFIG: TagConfigMap = {
  draft: { color: '', text: '草稿' },
  submitted: { color: 'blue', text: '已提交' },
  finance_review: { color: 'purple', text: '财务负责人审批' },
  enterprise_review: { color: 'orange', text: '企业负责人审批' },
  in_progress: { color: 'orange', text: '询价中' },
  quoted: { color: 'cyan', text: '已报价' },
  completed: { color: 'green', text: '已完成' },
  approved: { color: 'green', text: '已批准' },
  cancelled: { color: 'red', text: '已取消' },
  rejected: { color: 'red', text: '已拒绝' },
};

// 询价状态选项
export const INQUIRY_STATUS_OPTIONS = [
  { label: '草稿', value: 'draft' },
  { label: '已提交', value: 'submitted' },
  { label: '财务负责人审批', value: 'finance_review' },
  { label: '企业负责人审批', value: 'enterprise_review' },
  { label: '询价中', value: 'in_progress' },
  { label: '已报价', value: 'quoted' },
  { label: '已完成', value: 'completed' },
  { label: '已批准', value: 'approved' },
  { label: '已取消', value: 'cancelled' },
  { label: '已拒绝', value: 'rejected' },
];

// 询价类型配置
export const INQUIRY_TYPE_CONFIG: TagConfigMap = {
  standard: { color: 'blue', text: '标准询价' },
  urgent: { color: 'orange', text: '紧急询价' },
  competitive: { color: 'green', text: '竞价询价' },
  technical: { color: 'purple', text: '技术询价' },
};

// 询价类型选项
export const INQUIRY_TYPE_OPTIONS = [
  { label: '标准询价', value: 'standard' },
  { label: '紧急询价', value: 'urgent' },
  { label: '竞价询价', value: 'competitive' },
  { label: '技术询价', value: 'technical' },
];

// 供应商响应状态配置
export const SUPPLIER_RESPONSE_CONFIG: TagConfigMap = {
  pending: { color: 'orange', text: '待响应' },
  responding: { color: 'blue', text: '响应中' },
  quoted: { color: 'green', text: '已报价' },
  declined: { color: 'red', text: '已拒绝' },
  timeout: { color: 'grey', text: '超时' },
};

// 供应商响应状态选项
export const SUPPLIER_RESPONSE_OPTIONS = [
  { label: '待响应', value: 'pending' },
  { label: '响应中', value: 'responding' },
  { label: '已报价', value: 'quoted' },
  { label: '已拒绝', value: 'declined' },
  { label: '超时', value: 'timeout' },
];

// 报价状态配置
export const QUOTE_STATUS_CONFIG: TagConfigMap = {
  not_quoted: { color: '', text: '未报价' },
  partial_quoted: { color: 'orange', text: '部分报价' },
  fully_quoted: { color: 'green', text: '完全报价' },
  expired: { color: 'red', text: '已过期' },
};

// 紧急程度配置（复用采购申请的配置）
export const URGENCY_CONFIG: TagConfigMap = {
  low: { color: '', text: '低' },
  medium: { color: 'blue', text: '中' },
  high: { color: 'orange', text: '高' },
  critical: { color: 'red', text: '紧急' },
  normal: { color: 'default', text: '一般' },
};

// 紧急程度选项
export const URGENCY_LEVEL_OPTIONS = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'critical' },
];

// 询价进度状态顺序(用于计算进度)
export const INQUIRY_STATUS_ORDER = [
  'draft',
  'submitted',
  'in_progress',
  'quoted',
  'completed',
];

// 操作类型映射
export const ACTION_CONFIG: TagConfigMap = {
  create: { text: '创建询价', color: 'blue' },
  submit: { text: '提交询价', color: 'green' },
  quote: { text: '供应商报价', color: 'cyan' },
  complete: { text: '完成询价', color: 'success' },
  cancel: { text: '取消询价', color: 'error' },
  approve: { text: '审批通过', color: 'success' },
  reject: { text: '审批拒绝', color: 'error' },
  approved: { text: '审批通过', color: 'success' },
  rejected: { text: '审批拒绝', color: 'error' },
  rollback: { text: '流程回退', color: 'warning' },
  update: { text: '更新询价', color: 'blue' },
};

// 物料类型选项（参考采购申请）
export const MATERIAL_TYPE_OPTIONS = [
  { label: '服务器', value: '服务器' },
  { label: '服务器配件', value: '服务器配件' },
  { label: '网络设备', value: '网络设备' },
  { label: '综合布线', value: '综合布线' },
  { label: 'IDC机柜', value: 'IDC机柜' },
  { label: '带宽', value: '带宽' },
  { label: '服务', value: '服务' },
  { label: '其他', value: '其他' },
];

// 单位选项
export const UNIT_OPTIONS = [
  { label: '台', value: '台' },
  { label: '个', value: '个' },
  { label: '套', value: '套' },
  { label: '片', value: '片' },
  { label: '块', value: '块' },
  { label: '条', value: '条' },
  { label: '根', value: '根' },
  { label: '米', value: '米' },
  { label: '公斤', value: '公斤' },
  { label: '批', value: '批' },
];

// 报价币种选项
export const CURRENCY_OPTIONS = [
  { label: '人民币(CNY)', value: 'CNY' },
  { label: '美元(USD)', value: 'USD' },
  { label: '欧元(EUR)', value: 'EUR' },
  { label: '日元(JPY)', value: 'JPY' },
];

// 询价方式选项
export const INQUIRY_METHOD_OPTIONS = [
  { label: '邮件询价', value: 'email' },
  { label: '电话询价', value: 'phone' },
  { label: '线下询价', value: 'offline' },
  { label: '平台询价', value: 'platform' },
];

// 比价方式选项
export const COMPARISON_METHOD_OPTIONS = [
  { label: '价格比较', value: 'price' },
  { label: '技术比较', value: 'technical' },
  { label: '综合比较', value: 'comprehensive' },
  { label: '服务比较', value: 'service' },
];
