import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { PurchaseInquiry } from '#/api/core/purchase/purchase_inquiry';

/**
 * 获取询价列表表格列配置
 */
export function useColumns(): VxeTableGridOptions<PurchaseInquiry>['columns'] {
  return [
    {
      field: 'inquiry_no',
      title: '询价单号',
      width: 200,
    },
    {
      field: 'status',
      title: '询价状态',
      width: 120,
      slots: { default: 'status' },
    },
    {
      field: 'supplier_name',
      title: '供应商',
      width: 150,
    },
    {
      field: 'request_no',
      title: '关联申请单',
      width: 200,
    },
    {
      field: 'project_name',
      title: '所属项目',
      width: 150,
    },
    {
      field: 'items',
      title: '询价明细',
      width: 120,
      slots: { default: 'items_action' },
    },
    {
      field: 'total_amount',
      title: '预算金额',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'creator_name',
      title: '创建人',
      width: 100,
    },
    {
      field: 'created_at',
      title: '创建时间',
      formatter: 'formatDateTime',
      width: 180,
    },
    {
      field: 'updated_at',
      title: '更新时间',
      formatter: 'formatDateTime',
      width: 180,
      visible: false,
    },
    {
      title: '操作',
      field: 'operation',
      fixed: 'right',
      width: 180,
      slots: { default: 'operate' },
    },
  ];
}

/**
 * 获取询价明细表格列配置
 */
export function useItemColumns(): VxeTableGridOptions<any>['columns'] {
  return [
    {
      field: 'material_type',
      title: '物料类型',
      width: 200,
      fixed: 'left',
    },
    {
      field: 'model',
      title: '型号',
      width: 150,
    },
    {
      field: 'brand',
      title: '品牌',
      width: 120,
    },
    {
      field: 'pn',
      title: 'PN号',
      width: 150,
    },
    {
      field: 'spec',
      title: '规格',
      minWidth: 200,
    },
    {
      field: 'unit',
      title: '单位',
      width: 80,
    },
    {
      field: 'request_quantity',
      title: '需求数量',
      width: 100,
    },
    {
      field: 'current_inquiry_quantity',
      title: '本次询价数量',
      width: 120,
    },
    {
      field: 'uninquired_quantity',
      title: '未询价数量',
      width: 100,
    },
    {
      field: 'budget_price',
      title: '预算单价',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'budget_amount',
      title: '预算金额',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'remark',
      title: '备注',
      minWidth: 150,
    },
  ];
}

/**
 * 获取供应商询价统计表格列配置
 */
export function useSupplierStatsColumns(): VxeTableGridOptions<any>['columns'] {
  return [
    {
      field: 'supplier_name',
      title: '供应商名称',
      width: 200,
    },
    {
      field: 'inquiry_count',
      title: '询价次数',
      width: 100,
    },
    {
      field: 'quoted_count',
      title: '已报价次数',
      width: 120,
    },
    {
      field: 'pending_count',
      title: '待报价次数',
      width: 120,
    },
    {
      field: 'quote_rate',
      title: '报价率',
      width: 100,
      formatter: ({ cellValue }) => `${(cellValue * 100).toFixed(1)}%`,
    },
  ];
}

/**
 * 获取物料询价统计表格列配置
 */
export function useItemStatsColumns(): VxeTableGridOptions<any>['columns'] {
  return [
    {
      field: 'product_name',
      title: '物料名称',
      width: 200,
    },
    {
      field: 'total_quantity',
      title: '总需求量',
      width: 100,
    },
    {
      field: 'inquired_quantity',
      title: '已询价量',
      width: 100,
    },
    {
      field: 'uninquired_quantity',
      title: '未询价量',
      width: 100,
    },
    {
      field: 'inquiry_progress',
      title: '询价进度',
      width: 120,
      formatter: ({ cellValue }) => `${(cellValue * 100).toFixed(1)}%`,
    },
    {
      field: 'inquiry_count',
      title: '询价次数',
      width: 100,
    },
  ];
}
