import type { VbenFormSchema } from '#/adapter/form';

import { z } from '#/adapter/form';
import { getProjectListApi } from '#/api/core/purchase/project';
import { getPurchaseRequestList } from '#/api/core/purchase/purchase_request';
import { getSupplierListApi } from '#/api/core/purchase/supplier';

import {
  COMPARISON_METHOD_OPTIONS,
  CURRENCY_OPTIONS,
  INQUIRY_STATUS_OPTIONS,
} from './constants';

// 定义一个全局处理函数类型，用于处理值变化
declare global {
  interface Window {
    handleRequestChange?: (id: number) => void;
    handleSupplierChange?: (id: number) => void;
  }
}

/**
 * 获取询价基本信息表单的字段配置
 */
export function useBasicSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        showSearch: true,
        filterOption: (
          input: string,
          option: { label: string; value: number },
        ) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
        api: async () => {
          const res = await getPurchaseRequestList({
            page: 1,
            pageSize: 100,
            status: 'approved',
          });

          return res.list.map((item) => ({
            label: `${item.request_no} - ${item.reason}`,
            value: item.id,
          }));
        },
        class: 'w-full',
        labelField: 'label',
        valueField: 'value',
        placeholder: '请选择关联的采购申请',
        onChange: (_value: number) => {
          // 移除直接调用，让 watch 监听器处理，避免重复请求
          // 使用 _value 前缀避免 TypeScript 未使用参数警告
        },
      },
      fieldName: 'request_id',
      label: '关联采购申请',
      rules: 'selectRequired',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: false,
        showSearch: true,
        filterOption: (
          input: string,
          option: { label: string; value: number },
        ) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
        api: async () => {
          const res = await getSupplierListApi({
            page: 1,
            pageSize: 100,
          });

          return res.list.map((item) => ({
            label: item.supplier_name,
            value: item.id,
          }));
        },
        class: 'w-full',
        labelField: 'label',
        valueField: 'value',
        placeholder: '请选择供应商',
        onChange: (value: number) => {
          // 直接使用全局函数
          if (window.handleSupplierChange && value) {
            window.handleSupplierChange(value);
          }
        },
      },
      fieldName: 'supplier_id',
      label: '目标供应商',
      rules: 'selectRequired',
    },
    {
      component: 'Textarea',
      componentProps: {
        maxLength: 500,
        placeholder: '请输入供应商选择说明',
        rows: 3,
        showCount: true,
        class: 'w-full',
      },
      fieldName: 'supplier_description',
      label: '供应商选择说明',
    },
  ];
}

/**
 * 搜索表单字段配置
 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'inquiry_no',
      label: '询价单号',
      componentProps: {
        placeholder: '请输入询价单号',
      },
    },
    {
      component: 'Input',
      fieldName: 'request_no',
      label: '采购申请单号',
      componentProps: {
        placeholder: '请输入采购申请单号',
      },
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        showSearch: true,
        filterOption: (
          input: string,
          option: { label: string; value: number },
        ) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
        api: async () => {
          const res = await getSupplierListApi({
            page: 1,
            pageSize: 100,
          });
          return res.list.map((item) => ({
            label: item.supplier_name,
            value: item.id,
          }));
        },
        labelField: 'label',
        valueField: 'value',
        placeholder: '请选择供应商',
      },
      fieldName: 'supplier_id',
      label: '供应商',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: INQUIRY_STATUS_OPTIONS,
      },
      fieldName: 'status',
      label: '询价状态',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        showSearch: true,
        filterOption: (
          input: string,
          option: { label: string; value: number },
        ) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
        api: async () => {
          const res = await getProjectListApi({
            page: 1,
            pageSize: 100,
          });
          return res.list.map((item) => ({
            label: item.project_name,
            value: item.id,
          }));
        },
        labelField: 'label',
        valueField: 'value',
        placeholder: '请选择项目',
      },
      fieldName: 'project_id',
      label: '所属项目',
    },

    {
      component: 'RangePicker',
      fieldName: 'create_time',
      label: '创建时间',
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DDT00:00:00Z',
      },
    },
  ];
}

/**
 * 获取询价明细项表单的字段配置
 */
export function useItemsSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入PN号',
        allowClear: true,
      },
      fieldName: 'pn',
      label: 'PN号',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 1,
        placeholder: '请输入本次询价数量',
      },
      fieldName: 'current_inquiry_quantity',
      label: '本次询价数量',
      rules: z.number().min(1, '本次询价数量必须大于0').nullable(),
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        placeholder: '请输入预算单价',
      },
      fieldName: 'budget_price',
      label: '预算单价',
      rules: z
        .number()
        .min(0, '预算单价不能为负数')
        .nullable()
        .refine((val) => val !== null && val !== undefined, {
          message: '请输入预算单价',
        }),
    },
    {
      component: 'Textarea',
      componentProps: {
        maxLength: 200,
        rows: 2,
        showCount: true,
        placeholder: '请输入备注信息',
      },
      fieldName: 'remark',
      label: '备注',
    },
  ];
}

/**
 * 获取供应商报价表单的字段配置
 */
export function useQuoteSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入供应商PN号',
        allowClear: true,
      },
      fieldName: 'pn',
      label: '供应商PN',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        placeholder: '请输入报价单价',
      },
      fieldName: 'quoted_price',
      label: '报价单价',
      rules: z.number().min(0, '报价单价不能为负数'),
    },
    {
      component: 'Select',
      componentProps: {
        options: CURRENCY_OPTIONS,
        placeholder: '请选择币种',
      },
      defaultValue: 'CNY',
      fieldName: 'currency',
      label: '报价币种',
    },
    {
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        placeholder: '请选择报价有效期',
        valueFormat: 'YYYY-MM-DDT00:00:00Z',
      },
      fieldName: 'quote_valid_date',
      label: '报价有效期',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 1,
        placeholder: '请输入交货周期(天)',
      },
      fieldName: 'delivery_period',
      label: '交货周期(天)',
    },
    {
      component: 'Textarea',
      componentProps: {
        maxLength: 300,
        rows: 3,
        showCount: true,
        placeholder: '请输入技术规格或其他说明',
      },
      fieldName: 'technical_specs',
      label: '技术规格',
    },
    {
      component: 'Textarea',
      componentProps: {
        maxLength: 200,
        rows: 2,
        showCount: true,
        placeholder: '请输入备注信息',
      },
      fieldName: 'remark',
      label: '备注',
    },
  ];
}

/**
 * 获取审批表单字段配置
 */
export function useApprovalSchema(): VbenFormSchema[] {
  return [
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '批准', value: 'approve' },
          { label: '拒绝', value: 'reject' },
        ],
        optionType: 'button',
      },
      fieldName: 'action',
      label: '审批决定',
      rules: z.string().min(1, '请选择审批决定'),
    },
    {
      component: 'Textarea',
      componentProps: {
        maxLength: 300,
        placeholder: '请输入审批意见',
        rows: 4,
        showCount: true,
      },
      fieldName: 'comments',
      label: '审批意见',
      rules: z.string().min(1, '请输入审批意见'),
    },
  ];
}

/**
 * 比价分析表单字段配置
 */
export function useComparisonSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Select',
      componentProps: {
        options: COMPARISON_METHOD_OPTIONS,
        mode: 'multiple',
        placeholder: '请选择比价维度',
      },
      fieldName: 'comparison_dimensions',
      label: '比价维度',
      rules: z.array(z.string()).min(1, '请选择至少一个比价维度'),
    },
    {
      component: 'Textarea',
      componentProps: {
        maxLength: 500,
        placeholder: '请输入比价分析结果',
        rows: 5,
        showCount: true,
      },
      fieldName: 'comparison_result',
      label: '比价分析',
      rules: z.string().min(10, '比价分析内容不能少于10个字符'),
    },
    {
      component: 'Textarea',
      componentProps: {
        maxLength: 300,
        placeholder: '请输入推荐意见',
        rows: 3,
        showCount: true,
      },
      fieldName: 'recommendation',
      label: '推荐意见',
    },
  ];
}
