<script lang="ts" setup>
import type { Arrival } from '#/api/core/purchase/arrival';
import type { ModuleFileListResult } from '#/api/core/upload';

import { computed, nextTick, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { AccessControl } from '@vben/access';
import { Page } from '@vben/common-ui';
import { ArrowLeft, FileText, Tag as TagIcon, User } from '@vben/icons';

import {
  Button,
  Image,
  message,
  Modal,
  Select,
  Spin,
  Tag,
  Textarea,
  Timeline,
  TimelineItem,
} from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  approveArrival,
  getArrivalById,
  getArrivalHistory,
  rollbackArrival,
} from '#/api/core/purchase/arrival';
import { getModuleFiles } from '#/api/core/upload';
import { handleDownloadFile, handlePreviewFile } from '#/utils/common/download';
import { formatToDateTime } from '#/utils/common/time';

import WorkflowChart from './components/WorkflowChart.vue';
import { ARRIVAL_COMPLETE_CONFIG, ARRIVAL_STATUS_CONFIG } from './constants';
import { useItemColumnsReadonly } from './data';

// 操作类型映射
const ACTION_CONFIG: Record<string, { color: string; text: string }> = {
  create: { text: '创建申请', color: 'blue' },
  submit: { text: '提交申请', color: 'green' },
  approve: { text: '审批通过', color: 'success' },
  reject: { text: '审批拒绝', color: 'error' },
  rollback: { text: '回退申请', color: 'orange' },
  cancel: { text: '取消申请', color: 'default' },
};

const route = useRoute();
const router = useRouter();

const loading = ref(false);
const workflowLoading = ref(false);
const arrival = ref<Arrival>();
const approvalHistory = ref<any[]>([]);

// 附件相关
const attachmentsLoading = ref(false);
const attachments = ref<ModuleFileListResult[]>([]);
const previewVisible = ref(false);
const previewImage = ref('');

// 按description分组的附件
const groupedAttachments = computed(() => {
  const grouped: Record<string, ModuleFileListResult[]> = {};

  attachments.value.forEach((file) => {
    const key = file.description || '其他附件';
    if (!grouped[key]) {
      grouped[key] = [];
    }
    grouped[key].push(file);
  });

  return grouped;
});

// 处理文件预览
function handlePreview(file: ModuleFileListResult) {
  // 使用通用预览函数
  handlePreviewFile(file, {
    onImagePreview: (imageUrl) => {
      previewImage.value = imageUrl;
      previewVisible.value = true;
    },
  });
}

// 处理文件下载
async function handleDownload(file: ModuleFileListResult) {
  // 使用通用下载函数
  await handleDownloadFile(file);
}

// 审批相关状态
const approvalComments = ref('');
const isApprovalModalVisible = ref(false);
const currentAction = ref<'approve' | 'reject'>('approve');
const isRollbackModalVisible = ref(false);
const rollbackComments = ref('');
const rollbackTarget = ref('');
const availableRollbackTargets = ref<any[]>([]);

const arrivalId = computed(() => Number(route.params.id));

// 权限判断
const canApprove = computed(() => {
  const status = arrival.value?.status;
  // 可以审批的状态：采购负责人审批中、服务器管理员审批中、网络管理员审批中、资产管理审批中
  const approvableStatuses = [
    'purchase_review',
    'server_admin_review',
    'network_admin_review',
    'asset_admin_review',
  ];
  return status && approvableStatuses.includes(status);
});

// 是否可以回退
const canRollback = computed(() => {
  const status = arrival.value?.status;

  // 不能回退的状态
  const nonRollbackStatuses = ['draft', 'completed', 'cancelled'];
  if (!status || nonRollbackStatuses.includes(status)) {
    return false;
  }

  // 采购负责人阶段不允许回退（第一个审批阶段）
  if (status === 'purchase_review') {
    return false;
  }

  return true;
});

// 获取当前阶段的权限码
const getPermissionCodes = computed(() => {
  const status = arrival.value?.status;

  switch (status) {
    case 'asset_admin_review': {
      return {
        approve: ['Purchase:ArrivalManagement:AssetAdminReview'],
        reject: ['Purchase:ArrivalManagement:AssetAdminReview'],
        rollback: ['Purchase:ArrivalManagement:AssetAdminReview'],
      };
    }
    case 'network_admin_review': {
      return {
        approve: ['Purchase:ArrivalManagement:NetworkAdminReview'],
        reject: ['Purchase:ArrivalManagement:NetworkAdminReview'],
        rollback: ['Purchase:ArrivalManagement:NetworkAdminReview'],
      };
    }
    case 'purchase_review': {
      return {
        approve: ['Purchase:ArrivalManagement:PurchaseManagerReview'],
        reject: ['Purchase:ArrivalManagement:PurchaseManagerReview'],
        rollback: [], // 采购审批阶段不能回退
      };
    }
    case 'server_admin_review': {
      return {
        approve: ['Purchase:ArrivalManagement:ServerAdminReview'],
        reject: ['Purchase:ArrivalManagement:ServerAdminReview'],
        rollback: ['Purchase:ArrivalManagement:ServerAdminReview'],
      };
    }
    default: {
      return {
        approve: [],
        reject: [],
        rollback: [],
      };
    }
  }
});

// 到货明细表格
const [ItemGrid, itemGridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useItemColumnsReadonly(),
    showOverflow: 'tooltip',
    pagerConfig: {
      enabled: false, // 禁用分页
    },
    proxyConfig: {
      autoLoad: false, // 禁用自动加载
      ajax: {
        query: async () => {
          if (!arrival.value?.items) return { items: [], total: 0 };

          return {
            items: arrival.value.items,
            total: arrival.value.items.length,
          };
        },
      },
    },
    toolbarConfig: {
      enabled: false, // 禁用工具栏
    },
  },
});

// 计算合计金额
const totalAmount = computed(() => {
  if (!arrival.value?.items) return 0;
  return arrival.value.items.reduce((sum, item) => {
    return sum + (item.current_arrival_amount || 0);
  }, 0);
});

// 计算合计数量
const totalQuantity = computed(() => {
  if (!arrival.value?.items) return 0;
  return arrival.value.items.reduce((sum, item) => {
    return sum + (item.current_arrival_quantity || 0);
  }, 0);
});

// 加载到货记录详情
async function loadArrivalDetail() {
  if (!arrivalId.value) return;

  try {
    loading.value = true;
    arrival.value = await getArrivalById(arrivalId.value);

    // 等待下一个tick确保grid已经挂载，然后更新明细表格数据
    await nextTick();

    if (
      itemGridApi?.grid &&
      typeof itemGridApi.grid.commitProxy === 'function'
    ) {
      try {
        await itemGridApi.query();
      } catch (gridError) {
        console.warn('表格数据更新失败:', gridError);

        if (itemGridApi.grid.loadData) {
          itemGridApi.grid.loadData(arrival.value?.items || []);
        }
      }
    }
  } catch (error) {
    console.error('获取到货记录详情失败:', error);
    message.error('获取到货记录详情失败');
  } finally {
    loading.value = false;
  }
}

// 获取审批历史
async function fetchApprovalHistory() {
  try {
    workflowLoading.value = true;
    const response = await getArrivalHistory(arrivalId.value);
    approvalHistory.value = Array.isArray(response) ? response : [];
  } catch (error) {
    console.error('获取审批历史失败:', error);
    message.error('获取审批历史失败');
    approvalHistory.value = [];
  } finally {
    workflowLoading.value = false;
  }
}

// 获取附件列表
async function fetchAttachments() {
  if (!arrivalId.value) return;

  try {
    attachmentsLoading.value = true;
    const files = await getModuleFiles('arrivals', arrivalId.value);
    attachments.value = Array.isArray(files) ? files : [];
  } catch (error) {
    console.error('获取附件失败:', error);
    message.error('获取附件列表失败');
    attachments.value = [];
  } finally {
    attachmentsLoading.value = false;
  }
}

// 返回列表
function goBack() {
  router.push('/purchase-management/arrival-management');
}

// 处理审批操作
function handleApproval(action: 'approve' | 'reject') {
  currentAction.value = action;
  approvalComments.value = '';
  isApprovalModalVisible.value = true;
}

// 确认审批
async function confirmApproval() {
  if (!approvalComments.value.trim()) {
    message.error('请输入审批意见');
    return;
  }

  try {
    workflowLoading.value = true;

    await approveArrival({
      id: arrivalId.value,
      current_stage: arrival.value?.status || '',
      action: currentAction.value,
      comments: approvalComments.value,
    });

    message.success(
      currentAction.value === 'approve' ? '审批通过' : '审批拒绝',
    );
    isApprovalModalVisible.value = false;

    // 延时后刷新数据
    setTimeout(async () => {
      await loadArrivalDetail();
      await fetchApprovalHistory();
    }, 500);
  } catch (error) {
    console.error('审批操作失败:', error);
    message.error('审批操作失败');
  } finally {
    workflowLoading.value = false;
  }
}

// 处理回退
function handleRollback() {
  rollbackComments.value = '';
  rollbackTarget.value = '';

  // 根据当前状态，确定可回退的目标环节
  const statusOrder = [
    'purchase_review', // 采购负责人审批
    'server_admin_review', // 服务器管理员审批
    'network_admin_review', // 网络管理员审批
    'asset_admin_review', // 资产管理审批
  ];

  const currentStatus = arrival.value?.status || '';
  const currentIndex = statusOrder.indexOf(currentStatus);

  // 重置可用目标
  availableRollbackTargets.value = [];
  rollbackTarget.value = '';

  // 找出当前状态之前的所有可回退状态
  const rollbackOptions = [
    { label: '采购负责人审批', value: 'purchase_review' },
    { label: '服务器管理员审批', value: 'server_admin_review' },
    { label: '网络管理员审批', value: 'network_admin_review' },
  ];

  rollbackOptions.forEach((option) => {
    const optionIndex = statusOrder.indexOf(option.value);
    // 只有在当前阶段之前的阶段才能作为回退目标
    if (optionIndex !== -1 && optionIndex < currentIndex) {
      availableRollbackTargets.value.push(option);
    }
  });

  // 如果有可回退的目标，默认选择最近的一个（最后一个）
  if (availableRollbackTargets.value.length > 0) {
    rollbackTarget.value =
      availableRollbackTargets.value[availableRollbackTargets.value.length - 1]
        ?.value || '';
  }

  isRollbackModalVisible.value = true;
}

// 确认回退
async function confirmRollback() {
  if (!rollbackComments.value.trim()) {
    message.error('请输入回退原因');
    return;
  }

  if (!rollbackTarget.value && availableRollbackTargets.value.length > 0) {
    message.error('请选择回退目标环节');
    return;
  }

  try {
    workflowLoading.value = true;

    await rollbackArrival({
      id: arrivalId.value,
      current_stage: arrival.value?.status || '',
      rollback_to: rollbackTarget.value,
      comments: rollbackComments.value,
    });

    message.success('回退成功');
    isRollbackModalVisible.value = false;

    // 延时后刷新数据
    setTimeout(async () => {
      await loadArrivalDetail();
      await fetchApprovalHistory();
    }, 500);
  } catch (error) {
    console.error('回退操作失败:', error);
    message.error('回退操作失败');
  } finally {
    workflowLoading.value = false;
  }
}

onMounted(() => {
  loadArrivalDetail();
  fetchApprovalHistory();
  fetchAttachments();
});
</script>

<template>
  <Page auto-content-height>
    <!-- 主容器 -->
    <div
      class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20"
    >
      <!-- 头部区域 -->
      <div class="relative overflow-hidden bg-white shadow-sm">
        <!-- 背景装饰 -->
        <div
          class="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-indigo-600/5 to-purple-600/5"
        ></div>
        <div
          class="absolute -left-40 -top-40 h-80 w-80 rounded-full bg-blue-400/10 blur-3xl"
        ></div>
        <div
          class="absolute -right-40 -top-40 h-80 w-80 rounded-full bg-indigo-400/10 blur-3xl"
        ></div>
        <div
          class="absolute -right-20 -top-20 h-48 w-48 rounded-full bg-blue-500/5"
        ></div>
        <div
          class="absolute -right-10 -top-10 h-32 w-32 rounded-full bg-indigo-500/10"
        ></div>

        <div class="px-6 py-5">
          <div class="flex flex-wrap items-center justify-between gap-4">
            <div class="flex items-center space-x-6">
              <Button
                type="primary"
                size="large"
                class="flex items-center justify-center rounded-xl !bg-gradient-to-r !from-blue-500 !to-indigo-600 !px-5 !py-3 shadow-sm transition-all duration-300 hover:scale-105 hover:!from-blue-600 hover:!to-indigo-700 hover:shadow-md"
                @click="goBack"
              >
                <div class="flex items-center justify-center">
                  <ArrowLeft class="mr-2 size-5 text-white" />
                  <span class="font-medium text-white">返回列表</span>
                </div>
              </Button>
              <div class="border-l-2 border-indigo-100 pl-6">
                <div class="flex items-center">
                  <h1
                    class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-2xl font-bold text-transparent"
                  >
                    到货记录详情
                  </h1>
                  <Tag
                    v-if="arrival?.status"
                    :color="
                      ARRIVAL_STATUS_CONFIG[arrival.status as string]?.color
                    "
                    class="ml-3 !rounded-full !px-3 !py-0.5 !text-xs !font-medium shadow-sm"
                  >
                    {{
                      ARRIVAL_STATUS_CONFIG[arrival.status as string]?.text ||
                      arrival.status
                    }}
                  </Tag>
                </div>
                <div v-if="arrival" class="mt-2 flex items-center gap-4">
                  <div class="flex items-center">
                    <div class="mr-2 h-2 w-2 rounded-full bg-blue-500"></div>
                    <span class="text-sm font-medium text-gray-600"
                      >到货通知单号:</span
                    >
                    <span class="ml-1 text-sm font-bold text-gray-800">{{
                      arrival.arrival_no || '未生成'
                    }}</span>
                  </div>
                  <div v-if="arrival.total_quantity" class="flex items-center">
                    <div class="mr-2 h-2 w-2 rounded-full bg-orange-500"></div>
                    <span class="text-sm font-medium text-gray-600"
                      >到货总数量:
                    </span>
                    <span class="ml-1 text-sm font-bold text-orange-600">
                      {{ arrival.total_quantity.toLocaleString() }}
                    </span>
                  </div>
                  <div v-if="arrival.total_amount" class="flex items-center">
                    <div class="mr-2 h-2 w-2 rounded-full bg-emerald-500"></div>
                    <span class="text-sm font-medium text-gray-600"
                      >到货总金额:</span
                    >
                    <span class="ml-1 text-sm font-bold text-red-600">
                      ¥{{ arrival.total_amount.toLocaleString() }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 审批操作按钮 -->
            <div v-if="arrival" class="flex space-x-3">
              <AccessControl
                v-if="canApprove"
                :codes="getPermissionCodes.approve"
                type="code"
              >
                <Button
                  type="primary"
                  size="large"
                  class="!h-auto !rounded-xl !border-0 !bg-gradient-to-r !from-green-500 !to-emerald-600 !px-8 !py-2.5 !font-medium shadow-md transition-all hover:scale-105 hover:!from-green-600 hover:!to-emerald-700 hover:shadow-lg"
                  @click="handleApproval('approve')"
                >
                  <span class="text-base text-white">审批通过</span>
                </Button>
              </AccessControl>
              <AccessControl
                v-if="canApprove"
                :codes="getPermissionCodes.reject"
                type="code"
              >
                <Button
                  danger
                  size="large"
                  class="!h-auto !rounded-xl !border-0 !bg-gradient-to-r !from-red-500 !to-rose-600 !px-8 !py-2.5 !font-medium shadow-md transition-all hover:scale-105 hover:!from-red-600 hover:!to-rose-700 hover:shadow-lg"
                  @click="handleApproval('reject')"
                >
                  <span class="text-base text-white">审批拒绝</span>
                </Button>
              </AccessControl>
              <AccessControl
                v-if="canRollback"
                :codes="getPermissionCodes.rollback"
                type="code"
              >
                <Button
                  size="large"
                  class="!h-auto !rounded-xl !border-2 !border-orange-300 !bg-gradient-to-r !from-orange-50 !to-amber-50 !px-8 !py-2.5 !font-medium shadow-md transition-all hover:scale-105 hover:!border-orange-400 hover:!from-orange-100 hover:!to-amber-100 hover:shadow-lg"
                  @click="handleRollback"
                >
                  <span class="text-base text-orange-600">流程回退</span>
                </Button>
              </AccessControl>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="px-4 py-6 sm:px-6 lg:px-8">
        <div class="space-y-6">
          <Spin :spinning="loading">
            <!-- 基本信息和流程图并排显示 -->
            <div class="mb-6 grid grid-cols-1 gap-6 lg:grid-cols-2">
              <!-- 基本信息 -->
              <div>
                <div
                  class="flex h-[700px] flex-col overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm"
                >
                  <div
                    class="border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4"
                  >
                    <h2
                      class="flex items-center text-lg font-semibold text-gray-900"
                    >
                      <div class="mr-3 h-6 w-1 rounded-full bg-blue-500"></div>
                      基本信息
                    </h2>
                  </div>
                  <div class="flex-1 overflow-y-auto p-6">
                    <!-- 基本信息卡片 -->
                    <div v-if="arrival" class="space-y-6">
                      <!-- 第一行：基本信息 -->
                      <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <!-- 到货通知单号 -->
                        <div
                          class="group relative overflow-hidden rounded-xl border border-blue-200/50 bg-gradient-to-br from-blue-50 to-blue-100/30 p-4 shadow-sm transition-all duration-300 hover:border-blue-300/70 hover:shadow-md"
                        >
                          <div
                            class="absolute -right-4 -top-4 h-12 w-12 rounded-full bg-blue-500/10"
                          ></div>
                          <div class="flex items-center gap-3">
                            <div
                              class="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 shadow-md"
                            >
                              <TagIcon class="size-5 text-white" />
                            </div>
                            <div class="flex-1">
                              <div
                                class="text-xs font-semibold uppercase tracking-wider text-blue-700 opacity-80"
                              >
                                到货通知单号
                              </div>
                              <div class="text-sm font-bold text-blue-800">
                                {{ arrival.arrival_no || '未生成' }}
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- 关联合同 -->
                        <div
                          class="group relative overflow-hidden rounded-xl border border-cyan-200/50 bg-gradient-to-br from-cyan-50 to-cyan-100/30 p-4 shadow-sm transition-all duration-300 hover:border-cyan-300/70 hover:shadow-md"
                        >
                          <div
                            class="absolute -right-4 -top-4 h-12 w-12 rounded-full bg-cyan-500/10"
                          ></div>
                          <div class="flex items-center gap-3">
                            <div
                              class="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-cyan-500 to-cyan-600 shadow-md"
                            >
                              <FileText class="size-5 text-white" />
                            </div>
                            <div class="flex-1">
                              <div
                                class="text-xs font-semibold uppercase tracking-wider text-cyan-700 opacity-80"
                              >
                                关联合同
                              </div>
                              <div
                                class="truncate text-sm font-bold text-cyan-800"
                              >
                                {{ arrival.contract_no || '-' }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 第二行：状态和供应商 -->
                      <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <!-- 到货状态 -->
                        <div
                          class="group relative overflow-hidden rounded-xl border border-purple-200/50 bg-gradient-to-br from-purple-50 to-purple-100/30 p-4 shadow-sm transition-all duration-300 hover:border-purple-300/70 hover:shadow-md"
                        >
                          <div
                            class="absolute -right-4 -top-4 h-12 w-12 rounded-full bg-purple-500/10"
                          ></div>
                          <div class="flex items-center gap-3">
                            <div
                              class="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-purple-500 to-purple-600 shadow-md"
                            >
                              <span class="text-lg font-bold text-white"
                                >📦</span
                              >
                            </div>
                            <div class="flex-1">
                              <div
                                class="text-xs font-semibold uppercase tracking-wider text-purple-700 opacity-80"
                              >
                                到货状态
                              </div>
                              <Tag
                                :color="
                                  ARRIVAL_STATUS_CONFIG[arrival.status]?.color
                                "
                                class="!rounded-md !px-2 !py-1 !text-xs !font-semibold shadow-sm"
                              >
                                {{
                                  ARRIVAL_STATUS_CONFIG[arrival.status]?.text ||
                                  arrival.status
                                }}
                              </Tag>
                            </div>
                          </div>
                        </div>

                        <!-- 供应商 -->
                        <div
                          class="group relative overflow-hidden rounded-xl border border-green-200/50 bg-gradient-to-br from-green-50 to-green-100/30 p-4 shadow-sm transition-all duration-300 hover:border-green-300/70 hover:shadow-md"
                        >
                          <div
                            class="absolute -right-4 -top-4 h-12 w-12 rounded-full bg-green-500/10"
                          ></div>
                          <div class="flex items-center gap-3">
                            <div
                              class="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-green-500 to-green-600 shadow-md"
                            >
                              <User class="size-5 text-white" />
                            </div>
                            <div class="flex-1">
                              <div
                                class="text-xs font-semibold uppercase tracking-wider text-green-700 opacity-80"
                              >
                                供应商
                              </div>
                              <div class="text-sm font-bold text-green-800">
                                {{ arrival.supplier_name || '未指定' }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 第三行：统计信息 -->
                      <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                        <!-- 到货总数量 -->
                        <div
                          class="group relative overflow-hidden rounded-xl border border-blue-200/50 bg-gradient-to-br from-blue-50 to-blue-100/30 p-4 shadow-sm transition-all duration-300 hover:border-blue-300/70 hover:shadow-md"
                        >
                          <div
                            class="absolute -right-4 -top-4 h-12 w-12 rounded-full bg-blue-500/10"
                          ></div>
                          <div class="flex items-center gap-3">
                            <div
                              class="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 shadow-md"
                            >
                              <span class="text-lg font-bold text-white"
                                >📦</span
                              >
                            </div>
                            <div class="flex-1">
                              <div
                                class="text-xs font-semibold uppercase tracking-wider text-blue-700 opacity-80"
                              >
                                到货总数量
                              </div>
                              <div class="text-lg font-bold text-blue-800">
                                {{
                                  arrival.total_quantity?.toLocaleString() ||
                                  '0'
                                }}
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- 到货总金额 -->
                        <div
                          class="group relative overflow-hidden rounded-xl border border-red-200/50 bg-gradient-to-br from-red-50 to-red-100/30 p-4 shadow-sm transition-all duration-300 hover:border-red-300/70 hover:shadow-md"
                        >
                          <div
                            class="absolute -right-4 -top-4 h-12 w-12 rounded-full bg-red-500/10"
                          ></div>
                          <div class="flex items-center gap-3">
                            <div
                              class="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-red-500 to-red-600 shadow-md"
                            >
                              <span class="text-lg font-bold text-white"
                                >💰</span
                              >
                            </div>
                            <div class="flex-1">
                              <div
                                class="text-xs font-semibold uppercase tracking-wider text-red-700 opacity-80"
                              >
                                到货总金额
                              </div>
                              <div class="text-lg font-bold text-red-800">
                                ¥{{
                                  arrival.total_amount?.toLocaleString() || '0'
                                }}
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- 完成状态 -->
                        <div
                          class="group relative overflow-hidden rounded-xl border border-indigo-200/50 bg-gradient-to-br from-indigo-50 to-indigo-100/30 p-4 shadow-sm transition-all duration-300 hover:border-indigo-300/70 hover:shadow-md"
                        >
                          <div
                            class="absolute -right-4 -top-4 h-12 w-12 rounded-full bg-indigo-500/10"
                          ></div>
                          <div class="flex items-center gap-3">
                            <div
                              class="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-indigo-500 to-indigo-600 shadow-md"
                            >
                              <span class="text-lg font-bold text-white"
                                >✅</span
                              >
                            </div>
                            <div class="flex-1">
                              <div
                                class="text-xs font-semibold uppercase tracking-wider text-indigo-700 opacity-80"
                              >
                                完成状态
                              </div>
                              <Tag
                                :color="
                                  ARRIVAL_COMPLETE_CONFIG[
                                    arrival.is_complete?.toString()
                                  ]?.color
                                "
                                class="!rounded-md !px-2 !py-1 !text-xs !font-semibold shadow-sm"
                              >
                                {{
                                  ARRIVAL_COMPLETE_CONFIG[
                                    arrival.is_complete?.toString()
                                  ]?.text || '未知'
                                }}
                              </Tag>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 第四行：详细信息 -->
                      <div class="space-y-4">
                        <!-- 第一行：预计到货日期 + 到货通知 -->
                        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                          <!-- 预计到货日期 -->
                          <div
                            class="group relative overflow-hidden rounded-xl border border-amber-200/50 bg-gradient-to-br from-amber-50 to-amber-100/30 p-4 shadow-sm transition-all duration-300 hover:border-amber-300/70 hover:shadow-md"
                          >
                            <div
                              class="absolute -right-4 -top-4 h-12 w-12 rounded-full bg-amber-500/10"
                            ></div>
                            <div class="flex items-start gap-3">
                              <div
                                class="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-amber-500 to-amber-600 shadow-md"
                              >
                                <span class="text-lg font-bold text-white"
                                  >📅</span
                                >
                              </div>
                              <div class="flex-1">
                                <div
                                  class="text-xs font-semibold uppercase tracking-wider text-amber-700 opacity-80"
                                >
                                  预计到货日期
                                </div>
                                <div class="text-sm font-bold text-amber-800">
                                  {{
                                    arrival.may_arrive_at?.split('T')[0] ||
                                    '未设定'
                                  }}
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- 到货通知 -->
                          <div
                            class="group relative overflow-hidden rounded-xl border border-violet-200/50 bg-gradient-to-br from-violet-50 to-violet-100/30 p-4 shadow-sm transition-all duration-300 hover:border-violet-300/70 hover:shadow-md"
                          >
                            <div
                              class="absolute -right-4 -top-4 h-12 w-12 rounded-full bg-violet-500/10"
                            ></div>
                            <div class="flex items-start gap-3">
                              <div
                                class="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-violet-500 to-violet-600 shadow-md"
                              >
                                <span class="text-lg font-bold text-white"
                                  >�</span
                                >
                              </div>
                              <div class="flex-1">
                                <div
                                  class="text-xs font-semibold uppercase tracking-wider text-violet-700 opacity-80"
                                >
                                  到货通知
                                </div>
                                <div
                                  class="text-sm font-bold leading-relaxed text-violet-800"
                                >
                                  {{ arrival.arrival_notice || '暂无通知' }}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- 第二行：到货地址 + 物流信息 -->
                        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                          <!-- 到货地址 -->
                          <div
                            class="group relative overflow-hidden rounded-xl border border-teal-200/50 bg-gradient-to-br from-teal-50 to-teal-100/30 p-4 shadow-sm transition-all duration-300 hover:border-teal-300/70 hover:shadow-md"
                          >
                            <div
                              class="absolute -right-4 -top-4 h-12 w-12 rounded-full bg-teal-500/10"
                            ></div>
                            <div class="flex items-start gap-3">
                              <div
                                class="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-teal-500 to-teal-600 shadow-md"
                              >
                                <span class="text-lg font-bold text-white"
                                  >�</span
                                >
                              </div>
                              <div class="flex-1">
                                <div
                                  class="text-xs font-semibold uppercase tracking-wider text-teal-700 opacity-80"
                                >
                                  到货地址
                                </div>
                                <div
                                  class="text-sm font-bold leading-relaxed text-teal-800"
                                >
                                  {{ arrival.arrival_address || '未填写' }}
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- 物流信息 -->
                          <div
                            class="group relative overflow-hidden rounded-xl border border-orange-200/50 bg-gradient-to-br from-orange-50 to-orange-100/30 p-4 shadow-sm transition-all duration-300 hover:border-orange-300/70 hover:shadow-md"
                          >
                            <div
                              class="absolute -right-4 -top-4 h-12 w-12 rounded-full bg-orange-500/10"
                            ></div>
                            <div class="flex items-start gap-3">
                              <div
                                class="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-orange-500 to-orange-600 shadow-md"
                              >
                                <span class="text-lg font-bold text-white"
                                  >�</span
                                >
                              </div>
                              <div class="flex-1">
                                <div
                                  class="text-xs font-semibold uppercase tracking-wider text-orange-700 opacity-80"
                                >
                                  物流信息
                                </div>
                                <div
                                  class="text-sm font-bold leading-relaxed text-orange-800"
                                >
                                  {{ arrival.tracking_info || '暂无物流信息' }}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- 第三行：备注信息 -->
                        <div
                          class="group relative overflow-hidden rounded-xl border border-slate-200/50 bg-gradient-to-br from-slate-50 to-slate-100/30 p-4 shadow-sm transition-all duration-300 hover:border-slate-300/70 hover:shadow-md"
                        >
                          <div
                            class="absolute -right-4 -top-4 h-12 w-12 rounded-full bg-slate-500/10"
                          ></div>
                          <div class="flex items-start gap-3">
                            <div
                              class="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-slate-500 to-slate-600 shadow-md"
                            >
                              <span class="text-lg font-bold text-white"
                                >📝</span
                              >
                            </div>
                            <div class="flex-1">
                              <div
                                class="text-xs font-semibold uppercase tracking-wider text-slate-700 opacity-80"
                              >
                                备注信息
                              </div>
                              <div
                                class="text-sm font-bold leading-relaxed text-slate-800"
                              >
                                {{ arrival.remark || '暂无备注' }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 流程图 -->
              <div>
                <div
                  class="flex h-[700px] flex-col overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm"
                >
                  <div
                    class="border-b border-gray-100 bg-gradient-to-r from-indigo-50 to-purple-50 px-6 py-4"
                  >
                    <h2
                      class="flex items-center text-lg font-semibold text-gray-900"
                    >
                      <div
                        class="mr-3 h-6 w-1 rounded-full bg-indigo-500"
                      ></div>
                      审批流程图
                    </h2>
                  </div>
                  <div class="flex-1 overflow-y-auto p-6">
                    <WorkflowChart
                      :current-status="arrival?.status || 'draft'"
                      :approval-history="approvalHistory"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- 到货明细 -->
            <div
              v-if="arrival?.items && arrival.items.length > 0"
              class="overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm"
            >
              <div
                class="border-b border-gray-100 bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4"
              >
                <div class="flex items-center justify-between">
                  <h2
                    class="flex items-center text-lg font-semibold text-gray-900"
                  >
                    <div class="mr-3 h-6 w-1 rounded-full bg-green-500"></div>
                    到货明细 ({{ arrival.items.length }}项)
                  </h2>
                  <div class="flex items-center gap-6">
                    <div class="text-right">
                      <div class="text-sm text-gray-600">合计数量</div>
                      <div class="text-lg font-bold text-orange-600">
                        {{ totalQuantity.toLocaleString() }}
                      </div>
                    </div>
                    <div class="text-right">
                      <div class="text-sm text-gray-600">合计金额</div>
                      <div class="text-lg font-bold text-red-600">
                        ¥{{ totalAmount.toLocaleString() }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="p-6">
                <ItemGrid />
              </div>
            </div>

            <!-- 附件和操作历史记录并排显示 -->
            <div class="mb-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
              <!-- 相关附件 -->
              <div
                class="overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm"
              >
                <div
                  class="border-b border-gray-100 bg-gradient-to-r from-violet-50 to-purple-50 px-6 py-4"
                >
                  <h2
                    class="flex items-center text-lg font-semibold text-gray-900"
                  >
                    <div class="mr-3 h-6 w-1 rounded-full bg-violet-500"></div>
                    相关附件
                  </h2>
                </div>
                <div class="p-6">
                  <Spin :spinning="attachmentsLoading">
                    <div
                      v-if="attachments.length === 0"
                      class="py-12 text-center"
                    >
                      <div class="flex flex-col items-center space-y-3">
                        <div
                          class="flex h-16 w-16 items-center justify-center rounded-full bg-gray-100"
                        >
                          <FileText class="size-8 text-gray-400" />
                        </div>
                        <p class="font-medium text-gray-500">暂无附件</p>
                      </div>
                    </div>
                    <div v-else class="space-y-6">
                      <div
                        v-for="(files, category) in groupedAttachments"
                        :key="category"
                        class="rounded-lg border border-gray-200 p-4"
                      >
                        <h3
                          class="mb-4 flex items-center text-base font-semibold text-gray-800"
                        >
                          <div
                            class="mr-2 h-4 w-4 rounded-full"
                            :class="{
                              'bg-green-500': category.includes('到货文件'),
                              'bg-blue-500': category.includes('验收文件'),
                              'bg-purple-500':
                                !category.includes('到货文件') &&
                                !category.includes('验收文件'),
                            }"
                          ></div>
                          {{ category }}
                          <span class="ml-2 text-xs text-gray-500"
                            >({{ files.length }}个文件)</span
                          >
                        </h3>
                        <div
                          class="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3"
                        >
                          <div
                            v-for="file in files"
                            :key="file.id"
                            class="group cursor-pointer rounded-xl border border-gray-200/60 bg-gradient-to-br from-gray-50 to-gray-100/80 p-3 transition-all duration-200 hover:border-violet-300/50 hover:shadow-md"
                          >
                            <div class="flex items-start space-x-3">
                              <div
                                class="flex h-10 w-10 items-center justify-center rounded-lg bg-violet-100 transition-colors group-hover:bg-violet-200"
                              >
                                <FileText class="size-5 text-violet-600" />
                              </div>
                              <div class="min-w-0 flex-1">
                                <div
                                  class="mb-1 truncate text-sm font-semibold text-gray-900"
                                >
                                  {{ file.original_name }}
                                </div>
                                <div class="mb-2 text-xs text-gray-500">
                                  {{ (file.file_size / 1024).toFixed(1) }} KB
                                </div>
                                <div class="flex space-x-2">
                                  <Button
                                    size="small"
                                    type="primary"
                                    class="!rounded-md !px-3 !text-xs"
                                    @click="handlePreview(file)"
                                  >
                                    预览
                                  </Button>
                                  <Button
                                    size="small"
                                    class="!rounded-md !px-3 !text-xs"
                                    @click="handleDownload(file)"
                                  >
                                    下载
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Spin>
                </div>
              </div>

              <!-- 操作历史记录 -->
              <div
                class="overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm"
              >
                <div
                  class="border-b border-gray-100 bg-gradient-to-r from-amber-50 to-yellow-50 px-6 py-4"
                >
                  <h2
                    class="flex items-center text-lg font-semibold text-gray-900"
                  >
                    <div class="mr-3 h-6 w-1 rounded-full bg-amber-500"></div>
                    操作历史记录
                  </h2>
                </div>
                <div class="p-6">
                  <Spin :spinning="workflowLoading">
                    <Timeline
                      v-if="approvalHistory.length > 0"
                      class="custom-timeline !pl-0"
                    >
                      <TimelineItem
                        v-for="(history, index) in approvalHistory"
                        :key="index"
                        :color="
                          ACTION_CONFIG[history.action as string]?.color ||
                          'blue'
                        "
                      >
                        <template #dot>
                          <div
                            class="flex h-8 w-8 items-center justify-center rounded-full font-semibold text-white shadow-lg"
                            :class="{
                              'bg-gradient-to-br from-green-500 to-green-600':
                                history.action === 'approve',
                              'bg-gradient-to-br from-red-500 to-red-600':
                                history.action === 'reject',
                              'bg-gradient-to-br from-orange-500 to-amber-600':
                                history.action === 'rollback',
                              'bg-gradient-to-br from-blue-500 to-blue-600':
                                history.action === 'create' ||
                                history.action === 'submit',
                              'bg-gradient-to-br from-gray-500 to-gray-600':
                                history.action === 'cancel',
                            }"
                          >
                            <span v-if="history.action === 'approve'">✓</span>
                            <span v-else-if="history.action === 'reject'"
                              >✗</span
                            >
                            <span v-else-if="history.action === 'rollback'"
                              >↺</span
                            >
                            <span
                              v-else-if="
                                history.action === 'create' ||
                                history.action === 'submit'
                              "
                              >+</span
                            >
                            <span v-else-if="history.action === 'cancel'"
                              >×</span
                            >
                            <span v-else>•</span>
                          </div>
                        </template>

                        <div
                          class="rounded-lg border border-gray-100 bg-white p-4 shadow-sm"
                        >
                          <div class="mb-3 flex items-center gap-3">
                            <div
                              class="flex h-8 w-8 items-center justify-center rounded-full text-white"
                              :class="{
                                'bg-gradient-to-br from-green-500 to-green-600':
                                  history.action === 'approve',
                                'bg-gradient-to-br from-red-500 to-red-600':
                                  history.action === 'reject',
                                'bg-gradient-to-br from-orange-500 to-amber-600':
                                  history.action === 'rollback',
                                'bg-gradient-to-br from-blue-500 to-blue-600':
                                  history.action === 'create' ||
                                  history.action === 'submit',
                                'bg-gradient-to-br from-gray-500 to-gray-600':
                                  history.action === 'cancel',
                              }"
                            >
                              {{
                                String(history.approver_name || 'U').charAt(0)
                              }}
                            </div>
                            <div class="flex-1">
                              <div class="font-medium text-gray-900">
                                {{ history.approver_name || '系统' }}
                              </div>
                              <div class="text-sm text-gray-500">
                                {{ formatToDateTime(history.created_at) }}
                              </div>
                            </div>
                            <Tag
                              :color="
                                ACTION_CONFIG[history.action as string]
                                  ?.color || 'blue'
                              "
                              class="!rounded-full !px-3 !py-1 !text-xs !font-medium shadow-sm"
                            >
                              {{
                                history.action_display ||
                                ACTION_CONFIG[history.action as string]?.text ||
                                history.action
                              }}
                            </Tag>
                          </div>

                          <!-- 状态变更信息 -->
                          <div
                            v-if="history.to_status"
                            class="mb-3 flex items-center gap-2 text-sm"
                          >
                            <span class="text-gray-500">状态变更:</span>
                            <Tag
                              color="orange"
                              size="small"
                              class="!rounded-md !text-xs"
                            >
                              {{
                                history.from_status_display ||
                                (history.from_status
                                  ? ARRIVAL_STATUS_CONFIG[history.from_status]
                                      ?.text
                                  : '初始状态') ||
                                history.from_status ||
                                '初始状态'
                              }}
                            </Tag>
                            <span class="text-gray-400">→</span>
                            <Tag
                              color="green"
                              size="small"
                              class="!rounded-md !text-xs"
                            >
                              {{
                                history.to_status_display ||
                                ARRIVAL_STATUS_CONFIG[history.to_status]
                                  ?.text ||
                                history.to_status
                              }}
                            </Tag>
                          </div>

                          <!-- 操作意见 -->
                          <div
                            v-if="history.comments"
                            class="rounded-lg border-l-4 bg-gray-50 p-3"
                            :class="{
                              'border-green-500': history.action === 'approve',
                              'border-red-500': history.action === 'reject',
                              'border-orange-500':
                                history.action === 'rollback',
                              'border-blue-500':
                                history.action === 'create' ||
                                history.action === 'submit',
                              'border-gray-500': history.action === 'cancel',
                            }"
                          >
                            <div
                              class="mb-1 text-xs font-semibold uppercase tracking-wide text-gray-500"
                            >
                              {{
                                {
                                  approve: '审批意见',
                                  reject: '拒绝原因',
                                  rollback: '回退原因',
                                  create: '创建说明',
                                  submit: '提交说明',
                                  cancel: '取消原因',
                                }[history.action as string] || '操作说明'
                              }}
                            </div>
                            <div class="text-sm leading-relaxed text-gray-700">
                              {{ history.comments }}
                            </div>
                          </div>
                        </div>
                      </TimelineItem>
                    </Timeline>
                    <div
                      v-else
                      class="flex flex-col items-center justify-center py-12 text-gray-500"
                    >
                      <div class="mb-4 text-6xl">📋</div>
                      <div class="text-lg font-medium">暂无操作记录</div>
                      <div class="text-sm">该到货记录暂无操作历史记录</div>
                    </div>
                  </Spin>
                </div>
              </div>
            </div>
          </Spin>
        </div>
      </div>
    </div>

    <!-- 审批弹窗 -->
    <Modal
      v-model:open="isApprovalModalVisible"
      :title="`${currentAction === 'approve' ? '审批通过' : '审批拒绝'}到货记录`"
      width="600px"
      :confirm-loading="workflowLoading"
      @ok="confirmApproval"
    >
      <div class="space-y-6">
        <!-- 申请信息摘要 -->
        <div
          class="rounded-lg border border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50 p-4"
        >
          <h3 class="mb-3 font-semibold text-gray-900">到货记录摘要</h3>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-gray-600">到货通知单号:</span>
              <span class="ml-2 font-medium">{{
                arrival?.arrival_no || '未生成'
              }}</span>
            </div>
            <div>
              <span class="text-gray-600">到货总金额:</span>
              <span class="ml-2 font-medium text-red-600">
                ¥{{ arrival?.total_amount?.toLocaleString() || '0' }}
              </span>
            </div>
            <div>
              <span class="text-gray-600">供应商:</span>
              <span class="ml-2 font-medium">{{
                arrival?.supplier?.name || arrival?.supplier_name || '-'
              }}</span>
            </div>
            <div>
              <span class="text-gray-600">关联合同:</span>
              <span class="ml-2 font-medium">{{
                arrival?.contract?.contract_no || '-'
              }}</span>
            </div>
          </div>
        </div>

        <!-- 审批意见 -->
        <div>
          <label class="mb-2 block text-sm font-medium text-gray-700">
            审批意见 <span class="text-red-500">*</span>
          </label>
          <Textarea
            v-model:value="approvalComments"
            :rows="4"
            placeholder="请输入审批意见..."
            class="!rounded-lg"
          />
          <div class="mt-2 text-xs text-gray-500">
            请详细说明{{ currentAction === 'approve' ? '通过' : '拒绝' }}的理由
          </div>
        </div>
      </div>
    </Modal>

    <!-- 回退弹窗 -->
    <Modal
      v-model:open="isRollbackModalVisible"
      title="流程回退"
      width="600px"
      :confirm-loading="workflowLoading"
      @ok="confirmRollback"
    >
      <div class="space-y-6">
        <!-- 回退信息摘要 -->
        <div
          class="rounded-lg border border-gray-200 bg-gradient-to-r from-orange-50 to-amber-50 p-4"
        >
          <h3 class="mb-3 font-semibold text-gray-900">回退信息</h3>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-gray-600">到货通知单号:</span>
              <span class="ml-2 font-medium">{{
                arrival?.arrival_no || '未生成'
              }}</span>
            </div>
            <div>
              <span class="text-gray-600">当前状态:</span>
              <span class="ml-2 font-medium">{{
                ARRIVAL_STATUS_CONFIG[arrival?.status as string]?.text ||
                arrival?.status
              }}</span>
            </div>
          </div>
        </div>

        <!-- 回退目标 -->
        <div v-if="availableRollbackTargets.length > 0">
          <label class="mb-2 block text-sm font-medium text-gray-700">
            回退目标 <span class="text-red-500">*</span>
          </label>
          <Select
            v-model:value="rollbackTarget"
            style="width: 100%"
            placeholder="请选择回退目标环节"
            :options="availableRollbackTargets"
            class="!rounded-lg"
          />
          <div class="mt-2 text-xs text-gray-500">请选择要回退到的审批环节</div>
        </div>
        <div
          v-else
          class="rounded-lg border border-yellow-200 bg-yellow-50 p-4"
        >
          <div class="text-sm text-yellow-700">当前状态无可回退的目标环节</div>
        </div>

        <!-- 回退原因 -->
        <div>
          <label class="mb-2 block text-sm font-medium text-gray-700">
            回退原因 <span class="text-red-500">*</span>
          </label>
          <Textarea
            v-model:value="rollbackComments"
            :rows="4"
            placeholder="请输入回退原因..."
            class="!rounded-lg"
          />
          <div class="mt-2 text-xs text-gray-500">
            请详细说明回退的原因，以便相关人员了解情况
          </div>
        </div>
      </div>
    </Modal>

    <!-- 图片预览弹窗 -->
    <Image
      :preview="{
        visible: previewVisible,
        onVisibleChange: (vis: boolean) => (previewVisible = vis),
      }"
      :src="previewImage"
      style="display: none"
    />
  </Page>
</template>

<style scoped>
/* Responsive Design */
@media (max-width: 768px) {
  :deep(.ant-card-body) {
    padding: 16px;
  }

  :deep(.ant-form-item) {
    margin-bottom: 16px;
  }
}

:deep(.custom-timeline .ant-timeline-item-tail) {
  border-left: 2px solid #e5e7eb;
}

/* Custom Card Header Styling */
:deep(.ant-card-head) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
}

:deep(.ant-card-head-title) {
  font-weight: 600;
  color: #1e293b;
}

/* Custom Button Styling */
:deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  box-shadow: 0 4px 12px rgb(59 130 246 / 30%);
}

:deep(.ant-btn-primary:hover) {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  box-shadow: 0 6px 16px rgb(59 130 246 / 40%);
  transform: translateY(-1px);
}

/* Custom Modal Styling */
:deep(.ant-modal-header) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
}

:deep(.ant-modal-title) {
  font-weight: 600;
  color: #1e293b;
}

/* Custom Form Styling */
:deep(.ant-form-item-label > label) {
  font-weight: 500;
  color: #374151;
}

/* Custom Table Styling */
:deep(.vxe-table .vxe-header--column) {
  font-weight: 600;
  color: #1e293b;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Custom Tag Styling */
:deep(.ant-tag) {
  font-weight: 500;
  border-radius: 6px;
}

/* Custom Spin Styling */
:deep(.ant-spin-dot-item) {
  background-color: #3b82f6;
}

/* Custom Timeline Item Styling */
:deep(.custom-timeline .ant-timeline-item-content) {
  margin-left: 20px;
}

:deep(.custom-timeline .ant-timeline-item-head) {
  background-color: transparent;
}

/* Custom Timeline Styling */
</style>

<style lang="less" scoped>
/* 自定义时间线样式 */
.custom-timeline {
  :deep(.ant-timeline-item-content) {
    margin-left: 0;
    padding-left: 24px;
  }

  :deep(.ant-timeline-item-tail) {
    border-left: 2px solid #e5e7eb;
  }

  :deep(.ant-timeline-item-head) {
    border: none;
    background: transparent;
    padding: 0;
  }
}

/* 按钮样式优化 */
:deep(.ant-btn) {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
}

:deep(.ant-btn:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 标签样式优化 */
:deep(.ant-tag) {
  border-radius: 6px;
  font-weight: 500;
  padding: 2px 8px;
}

/* 表格样式优化 */
:deep(.vxe-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.vxe-table .vxe-header--row) {
  background: #f8fafc;
  font-weight: 600;
}

:deep(.vxe-table .vxe-body--row:hover) {
  background: #f1f5f9;
}
</style>
