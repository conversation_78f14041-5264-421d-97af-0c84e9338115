import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { Arrival, ArrivalItem } from '#/api/core/purchase/arrival';

/**
 * 获取到货管理列表表格列配置
 */
export function useColumns(): VxeTableGridOptions<Arrival>['columns'] {
  return [
    {
      field: 'arrival_no',
      title: '到货通知单号',
      width: 180,
      fixed: 'left',
    },
    {
      field: 'status',
      title: '状态',
      width: 150,
      slots: { default: 'status' },
    },
    {
      field: 'contract_no',
      title: '关联合同',
      width: 160,
    },
    {
      field: 'items',
      title: '到货明细',
      width: 120,
      slots: { default: 'items_action' },
    },
    {
      field: 'our_company_name',
      title: '我方公司',
      width: 180,
    },
    {
      field: 'supplier_name',
      title: '对方公司名称',
      width: 200,
    },
    {
      field: 'total_quantity',
      title: '到货总数量',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return Number(cellValue).toLocaleString();
      },
    },
    {
      field: 'total_amount',
      title: '到货总金额',
      width: 140,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'is_complete',
      title: '是否全部到货',
      width: 120,
      slots: { default: 'is_complete' },
    },
    {
      field: 'may_arrive_at',
      title: '预计到货日期',
      width: 120,
      formatter: 'formatDate',
    },
    {
      field: 'arrival_notice',
      title: '到货通知',
      width: 200,
      showOverflow: 'tooltip',
    },
    {
      field: 'tracking_info',
      title: '物流信息',
      width: 200,
      showOverflow: 'tooltip',
    },
    {
      field: 'arrival_address',
      title: '到货地址',
      width: 200,
      showOverflow: 'tooltip',
    },
    {
      field: 'creator_name',
      title: '创建人',
      width: 100,
    },
    {
      field: 'created_at',
      title: '创建时间',
      formatter: 'formatDateTime',
      width: 180,
    },
    {
      field: 'updated_at',
      title: '更新时间',
      formatter: 'formatDateTime',
      width: 180,
      visible: false,
    },
    {
      title: '操作',
      field: 'operation',
      fixed: 'right',
      width: 200,
      slots: { default: 'action' },
    },
  ];
}

/**
 * 获取到货明细表格列配置（可编辑版本）
 */
export function useItemColumns(): VxeTableGridOptions<ArrivalItem>['columns'] {
  return [
    {
      field: 'contract_item.material_type',
      title: '物料类型',
      width: 120,
      fixed: 'left',
    },
    {
      field: 'contract_item.brand',
      title: '品牌',
      width: 120,
    },
    {
      field: 'contract_item.spec',
      title: '规格',
      width: 200,
      showOverflow: 'tooltip',
    },
    {
      field: 'contract_item.model',
      title: '型号',
      width: 150,
    },
    {
      field: 'contract_item.pn',
      title: '原厂PN',
      width: 150,
    },
    {
      field: 'contract_item.unit',
      title: '单位',
      width: 80,
    },
    {
      field: 'contract_quantity',
      title: '合同数量',
      width: 100,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return Number(cellValue).toLocaleString();
      },
    },
    {
      field: 'contract_item.contract_price',
      title: '合同单价',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'contract_amount',
      title: '合同总金额',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },

    {
      field: 'received_quantity',
      title: '已到货数量',
      width: 100,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return Number(cellValue).toLocaleString();
      },
    },
    {
      field: 'received_amount',
      title: '已经到货金额',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'unreceived_quantity',
      title: '未到货数量',
      width: 100,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return Number(cellValue).toLocaleString();
      },
    },
    {
      field: 'unreceived_amount',
      title: '未到货金额',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'paid_quantity',
      title: '已付款数量',
      width: 100,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return Number(cellValue).toLocaleString();
      },
    },
    {
      field: 'paid_amount',
      title: '已付款金额',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'unpaid_quantity',
      title: '未付款数量',
      width: 100,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return Number(cellValue).toLocaleString();
      },
    },
    {
      field: 'unpaid_amount',
      title: '未付款金额',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'current_arrival_quantity',
      title: '本次到货数量',
      width: 140,
      fixed: 'right',
      editRender: {
        name: 'input',
        props: {
          type: 'number',
          min: 0,
          placeholder: '请输入本次到货数量',
        },
        events: {
          input: ({ row, $grid }, event: Event) => {
            const target = event.target as HTMLInputElement;
            const newValue = Number(target.value);

            // 更新当前行的数量
            row.current_arrival_quantity = newValue;

            // 重新计算金额
            if (
              row.contract_amount &&
              row.contract_quantity &&
              row.contract_quantity > 0
            ) {
              const unitPrice = row.contract_amount / row.contract_quantity;
              row.current_arrival_amount = newValue * unitPrice;
            }

            // 强制更新表格显示
            if ($grid) {
              $grid.updateData();
            }
          },
        },
      },
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return Number(cellValue).toLocaleString();
      },
    },
    {
      field: 'current_arrival_amount',
      title: '本次到货金额',
      width: 140,
      fixed: 'right',
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'expected_arrival_date',
      title: '预计到货时间',
      width: 160,
      fixed: 'right',
      editRender: {
        name: 'input',
        props: {
          type: 'text',
          readonly: true,
        },
      },
      slots: {
        default: 'expected_arrival_date',
        edit: 'expected_arrival_date_edit',
      },
    },
    {
      field: 'actions',
      title: '操作',
      width: 80,
      fixed: 'right',
      slots: {
        default: 'actions',
      },
    },
  ];
}

/**
 * 获取到货历史记录表格列配置
 */
export function useHistoryColumns(): VxeTableGridOptions<any>['columns'] {
  return [
    {
      field: 'action',
      title: '操作类型',
      width: 120,
      slots: { default: 'action' },
    },
    {
      field: 'from_status',
      title: '原状态',
      width: 150,
      slots: { default: 'from_status' },
    },
    {
      field: 'to_status',
      title: '目标状态',
      width: 150,
      slots: { default: 'to_status' },
    },
    {
      field: 'approver_name',
      title: '操作人',
      width: 100,
    },
    {
      field: 'comments',
      title: '操作意见',
      minWidth: 200,
      showOverflow: 'tooltip',
    },
    {
      field: 'created_at',
      title: '操作时间',
      formatter: 'formatDateTime',
      width: 180,
    },
  ];
}

/**
 * 获取到货明细表格列配置（只读版本，用于详情页）
 */
export function useItemColumnsReadonly(): VxeTableGridOptions<ArrivalItem>['columns'] {
  return [
    {
      field: 'contract_item.material_type',
      title: '物料类型',
      width: 120,
      fixed: 'left',
    },
    {
      field: 'contract_item.brand',
      title: '品牌',
      width: 120,
    },
    {
      field: 'contract_item.spec',
      title: '规格',
      width: 200,
      showOverflow: 'tooltip',
    },
    {
      field: 'contract_item.model',
      title: '型号',
      width: 150,
    },
    {
      field: 'contract_item.pn',
      title: '原厂PN',
      width: 150,
    },
    {
      field: 'contract_item.unit',
      title: '单位',
      width: 80,
    },
    {
      field: 'expected_arrival_date',
      title: '预计到货时间',
      width: 150,
      formatter: 'formatDate',
    },
    {
      field: 'contract_item.contract_quantity',
      title: '合同数量',
      width: 100,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return Number(cellValue).toLocaleString();
      },
    },
    {
      field: 'contract_item.contract_price',
      title: '合同单价',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'contract_item.contract_amount',
      title: '合同总金额',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'paid_quantity',
      title: '已付款数量',
      width: 100,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return Number(cellValue).toLocaleString();
      },
    },
    {
      field: 'paid_amount',
      title: '已付款金额',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'unpaid_quantity',
      title: '未付款数量',
      width: 100,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return Number(cellValue).toLocaleString();
      },
    },
    {
      field: 'unpaid_amount',
      title: '未付款金额',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'received_quantity',
      title: '已到货数量',
      width: 100,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return Number(cellValue).toLocaleString();
      },
    },
    {
      field: 'received_amount',
      title: '已经到货金额',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'unreceived_quantity',
      title: '未到货数量',
      width: 100,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return Number(cellValue).toLocaleString();
      },
    },
    {
      field: 'unreceived_amount',
      title: '未到货金额',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'current_arrival_quantity',
      title: '本次到货数量',
      fixed: 'right',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return Number(cellValue).toLocaleString();
      },
    },
    {
      field: 'current_arrival_amount',
      title: '本次到货金额',
      fixed: 'right',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
  ];
}
