<script lang="ts" setup>
import { computed, ref, watch } from 'vue';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';

import { useItemFormSchema } from '../schema';

interface Props {
  data?: null | {
    arrivalId: number;
    contractId: number;
    item?: any;
  };
}

const props = defineProps<Props>();

const emit = defineEmits<{
  success: [];
}>();

const loading = ref(false);
const isEdit = computed(() => !!props.data?.item?.id);

const [Form, formApi] = useVbenForm({
  schema: useItemFormSchema(),
});

// 监听数据变化，设置表单值
watch(
  () => props.data,
  (data) => {
    if (data) {
      if (data.item) {
        formApi.setValues({
          contract_item_id: data.item.contract_item_id,
          current_arrival_quantity: data.item.current_arrival_quantity,
          current_arrival_amount: data.item.current_arrival_amount,
          expected_arrival_date: data.item.expected_arrival_date,
          serial_numbers: data.item.serial_number_list || [],
          remark: data.item.remark,
        });
      } else {
        formApi.resetForm();
      }
    }
  },
  { immediate: true },
);

// 提交表单
async function handleSubmit() {
  if (!props.data) return;

  try {
    loading.value = true;
    const { valid } = await formApi.validate();

    if (!valid) {
      return;
    }

    // TODO: 实现保存明细项的API调用
    if (isEdit.value) {
      // await updateArrivalItem(props.data.item.id, values);
      message.success('更新明细成功');
    } else {
      // await createArrivalItem({ ...values, arrival_id: props.data.arrivalId });
      message.success('添加明细成功');
    }

    emit('success');
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败');
  } finally {
    loading.value = false;
  }
}

defineExpose({
  handleSubmit,
});
</script>

<template>
  <Form />
</template>
