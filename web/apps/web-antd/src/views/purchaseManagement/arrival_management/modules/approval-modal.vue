<script lang="ts" setup>
import type { Arrival, ArrivalApprovalDTO } from '#/api/core/purchase/arrival';

import { ref, watch } from 'vue';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { approveArrival } from '#/api/core/purchase/arrival';

import { useApprovalFormSchema } from '../schema';

interface Props {
  data?: Arrival | null;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  success: [];
}>();

const loading = ref(false);

const [Form, formApi] = useVbenForm({
  schema: useApprovalFormSchema(),
});

// 监听数据变化，重置表单
watch(
  () => props.data,
  (data) => {
    if (data) {
      formApi.resetForm();
    }
  },
  { immediate: true },
);

// 提交审批
async function handleSubmit() {
  if (!props.data) return;

  try {
    loading.value = true;
    const { valid, values } = await formApi.validate();

    if (!valid) {
      return;
    }

    const approvalData: ArrivalApprovalDTO = {
      id: props.data.id,
      action: values?.action || 'approve',
      comments: values?.comments || '',
    };

    await approveArrival(approvalData);
    message.success('审批成功');
    emit('success');
  } catch (error) {
    console.error('审批失败:', error);
    message.error('审批失败');
  } finally {
    loading.value = false;
  }
}

defineExpose({
  handleSubmit,
});
</script>

<template>
  <Form />
</template>
