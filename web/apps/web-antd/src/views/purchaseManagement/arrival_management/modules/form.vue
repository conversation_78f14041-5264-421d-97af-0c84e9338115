<script lang="ts" setup>
import type { Arrival, ArrivalItem } from '#/api/core/purchase/arrival';

import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';

import {
  Modal as AModal,
  Button,
  Checkbox,
  DatePicker,
  message,
  Radio,
  Select,
  Switch,
  Tabs,
} from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { getWarehouseListApi } from '#/api/core/cmdb/asset/warehouse';
import {
  createArrival,
  getArrivalsByContractId,
  updateArrival,
} from '#/api/core/purchase/arrival';
import { getPaymentRequestsByContract } from '#/api/core/purchase/payment_request';
import { getPurchaseContractById } from '#/api/core/purchase/purchase_contract';
import {
  createInboundTicketApiV2,
  startWorkflowV2,
} from '#/api/core/ticket/asset-ticket';
import FileUploader from '#/components/FileUploader/FileUploader.vue';
import { formatDate } from '#/utils/common/time';

import { CONTRACT_TYPE_CONFIG } from '../../purchase_contract/constants';
import ContractItemArrivalStatus from '../components/ContractItemArrivalStatus.vue';
import { useItemColumns } from '../data';
import { useFormSchema } from '../schema';

interface Props {
  data?: Arrival | null;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  success: [];
}>();

// 表单数据和状态
const formData = ref<Arrival | null>(null);
const readOnly = ref(false);
const contractData = ref<any>(null);
const supplierData = ref<any>(null);

// 到货明细相关
const arrivalItems = ref<ArrivalItem[]>([]);
const showContractItemsModal = ref(false);
const selectAllChecked = ref(false);
const selectAllIndeterminate = ref(false);

// 附件上传相关
const arrivalDocumentUploaderRef = ref();

// 入库相关状态
const createInbound = ref(true); // 默认值改为true
const inboundType = ref<'device_inbound' | 'part_inbound'>('part_inbound'); // 默认配件入库
const warehouseId = ref<number>();
const needValidation = ref(true);
const warehouseList = ref<any[]>([]);

const activeKey = ref('basic');

// 二次确认弹窗
const confirmModalVisible = ref(false);
const confirmLoading = ref(false);
const pendingData = ref<any>(null);

const getTitle = computed(() => {
  if (readOnly.value) {
    return '查看到货记录';
  }
  return formData.value?.id ? '编辑到货记录' : '创建到货记录';
});

const [Modal, modalApi] = useVbenModal({
  class: '!w-full',
  onConfirm: handleSubmit,
  onOpenChange: (isOpen: boolean) => {
    if (!isOpen) {
      resetForm();
    }
  },
});

const [BasicForm, basicFormApi] = useVbenForm({
  layout: 'vertical',
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-2',
});

// 到货明细表格 - 使用最简单的方式
const [ItemGrid, itemGridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useItemColumns(),
    keepSource: true,
    pagerConfig: {
      enabled: false,
    },
    // height: 'auto',
    editConfig: {
      trigger: 'click',
      mode: 'cell',
    },
    editRules: {
      current_arrival_quantity: [
        { required: true, message: '请输入本次到货数量' },
        { type: 'number', min: 0, message: '本次到货数量不能小于0' },
      ],
      expected_arrival_date: [
        { required: true, message: '请选择预计到货时间' },
      ],
    },
  },
  gridEvents: {
    editClosed: ({ row, column }: any) => {
      // 当本次到货数量变化时，自动计算本次到货金额
      if (column.field === 'current_arrival_quantity') {
        const quantity = Number(row.current_arrival_quantity) || 0;
        const price = Number(row.contract_item?.contract_price) || 0;
        row.current_arrival_amount = quantity * price;
      }
    },
  },
});

// 监听到货明细数据变化，更新表格
watch(
  arrivalItems,
  async (newItems) => {
    // 等待DOM更新
    await nextTick();

    // 使用setState来更新表格数据
    if (itemGridApi && itemGridApi.setState) {
      try {
        itemGridApi.setState({
          gridOptions: {
            data: newItems,
          },
        });
      } catch (error) {
        console.error('更新表格数据失败:', error);
      }
    }
  },
  { deep: true, immediate: false },
);

const formValues = ref<Record<string, any>>({});

async function updateFormValues() {
  try {
    const values = await basicFormApi.getValues();
    if (values) {
      formValues.value = values;
    }
  } catch (error) {
    console.error('获取表单值失败:', error);
  }
}

// 监听合同ID变化
watch(
  () => formValues.value?.contract_id,
  (newVal, oldVal) => {
    if (newVal !== oldVal && newVal) {
      handleContractChange(Number(newVal));
    }
  },
);

// 定期更新表单值以确保实时性
let updateInterval: NodeJS.Timeout | null = null;

onMounted(() => {
  nextTick(() => {
    updateFormValues();
  });

  // 设置定时器定期更新表单值
  updateInterval = setInterval(() => {
    updateFormValues();
  }, 500); // 每500ms更新一次
});

onUnmounted(() => {
  // 清理定时器
  if (updateInterval) {
    clearInterval(updateInterval);
    updateInterval = null;
  }
});

/**
 * 检查合同明细是否已选择
 */
function isContractItemSelected(contractItemId: number | undefined): boolean {
  if (!contractItemId) return false;
  return arrivalItems.value.some(
    (item) => item.contract_item_id === contractItemId,
  );
}

/**
 * 获取合同明细项的付款信息
 */
async function getContractItemPaymentInfo(
  contractItemId: number,
): Promise<{ paidAmount: number; paidQuantity: number }> {
  try {
    if (!contractData.value?.id) {
      return { paidQuantity: 0, paidAmount: 0 };
    }

    // 获取该合同的所有付款申请
    const paymentRequests = await getPaymentRequestsByContract(
      contractData.value.id,
    );

    let totalPaidQuantity = 0;
    let totalPaidAmount = 0;

    // 遍历所有已完成的付款申请
    for (const payment of paymentRequests) {
      if (payment.status === 'completed' && payment.items) {
        // 查找该明细项的付款记录
        const paymentItem = payment.items.find(
          (item) => item.contract_item_id === contractItemId,
        );
        if (paymentItem) {
          totalPaidQuantity += paymentItem.current_payment_quantity || 0;
          totalPaidAmount += paymentItem.current_payment_amount || 0;
        }
      }
    }

    return {
      paidQuantity: totalPaidQuantity,
      paidAmount: totalPaidAmount,
    };
  } catch (error) {
    console.error('获取付款信息失败:', error);
    return { paidQuantity: 0, paidAmount: 0 };
  }
}

/**
 * 获取合同明细项的已到货统计信息
 */
async function getContractItemArrivalStats(
  contractItemId: number,
): Promise<{ receivedAmount: number; receivedQuantity: number }> {
  try {
    if (!contractData.value?.id) {
      return { receivedQuantity: 0, receivedAmount: 0 };
    }

    // 获取该合同的所有到货记录
    const arrivals = await getArrivalsByContractId(contractData.value.id);

    let totalReceivedQuantity = 0;
    let totalReceivedAmount = 0;

    // 遍历所有已审批完成的到货记录
    for (const arrival of arrivals) {
      if (arrival.status === 'completed' && arrival.items) {
        // 查找该明细项的到货记录
        const arrivalItem = arrival.items.find(
          (item) => item.contract_item_id === contractItemId,
        );
        if (arrivalItem) {
          totalReceivedQuantity += arrivalItem.current_arrival_quantity || 0;
          totalReceivedAmount += arrivalItem.current_arrival_amount || 0;
        }
      }
    }

    return {
      receivedQuantity: totalReceivedQuantity,
      receivedAmount: totalReceivedAmount,
    };
  } catch (error) {
    console.error('获取到货统计信息失败:', error);
    return { receivedQuantity: 0, receivedAmount: 0 };
  }
}

/**
 * 切换合同明细选择状态
 */
async function toggleContractItem(contractItem: any, event: Event) {
  const target = event.target as HTMLInputElement;
  const isChecked = target.checked;

  if (isChecked) {
    // 获取付款信息
    const paymentInfo = await getContractItemPaymentInfo(contractItem.id);

    // 获取已到货统计信息
    const arrivalStats = await getContractItemArrivalStats(contractItem.id);

    // 计算未付款数量和金额
    const unpaidQuantity =
      contractItem.contract_quantity - paymentInfo.paidQuantity;
    const unpaidAmount = contractItem.contract_amount - paymentInfo.paidAmount;

    // 计算未到货数量和金额
    const unreceivedQuantity =
      contractItem.contract_quantity - arrivalStats.receivedQuantity;
    const unreceivedAmount =
      contractItem.contract_amount - arrivalStats.receivedAmount;

    // 检查是否已全部到货
    if (unreceivedQuantity <= 0) {
      message.warning(
        `该合同明细项"${contractItem.material_type || ''}${contractItem.model ? ` - ${contractItem.model}` : ''}"已全部到货，无需再次创建到货记录！`,
      );
      // 取消选择
      target.checked = false;
      return;
    }

    // 添加到到货明细
    const arrivalItem: ArrivalItem = {
      id: undefined,
      arrival_id: undefined,
      contract_item_id: contractItem.id!,
      contract_quantity: contractItem.contract_quantity,
      contract_amount: contractItem.contract_amount,
      received_quantity: arrivalStats.receivedQuantity, // 从已审批完成的到货单统计
      received_amount: arrivalStats.receivedAmount, // 从已审批完成的到货单统计
      unreceived_quantity: Math.max(0, unreceivedQuantity), // 确保不为负数
      unreceived_amount: Math.max(0, unreceivedAmount), // 确保不为负数
      paid_quantity: paymentInfo.paidQuantity, // 从付款申请API获取
      paid_amount: paymentInfo.paidAmount, // 从付款申请API获取
      unpaid_quantity: Math.max(0, unpaidQuantity), // 确保不为负数
      unpaid_amount: Math.max(0, unpaidAmount), // 确保不为负数
      current_arrival_quantity: 0,
      current_arrival_amount: 0,
      expected_arrival_date: undefined, // 需要用户手动填写
      serial_numbers: undefined,
      remark: undefined,
      contract_item: {
        id: contractItem.id || 0,
        material_type: contractItem.material_type,
        model: contractItem.model || '',
        brand: contractItem.brand || '',
        pn: contractItem.pn || '',
        spec: contractItem.spec || '',
        unit: contractItem.unit || '',
        contract_quantity: contractItem.contract_quantity,
        contract_price: contractItem.contract_price,
      },
      serial_number_list: [],
    };
    arrivalItems.value.push(arrivalItem);
  } else {
    // 从到货明细中移除
    const index = arrivalItems.value.findIndex(
      (item) => item.contract_item_id === contractItem.id,
    );
    if (index !== -1) {
      arrivalItems.value.splice(index, 1);
    }
  }

  // 更新全选状态
  updateSelectAllStatus();
}

/**
 * 更新全选状态
 */
function updateSelectAllStatus() {
  if (!contractData.value?.items) {
    selectAllChecked.value = false;
    selectAllIndeterminate.value = false;
    return;
  }

  // 获取可选择的合同明细项（未全部到货的）
  const selectableItems = contractData.value.items.filter((_item: any) => {
    // 这里可以添加过滤逻辑，比如过滤掉已全部到货的项
    return true; // 暂时允许选择所有项
  });

  const selectedCount = selectableItems.filter((item: any) =>
    isContractItemSelected(item.id),
  ).length;

  if (selectedCount === 0) {
    selectAllChecked.value = false;
    selectAllIndeterminate.value = false;
  } else if (selectedCount === selectableItems.length) {
    selectAllChecked.value = true;
    selectAllIndeterminate.value = false;
  } else {
    selectAllChecked.value = false;
    selectAllIndeterminate.value = true;
  }
}

/**
 * 处理全选/取消全选
 */
async function handleSelectAll(event: any) {
  const isChecked = event.target?.checked ?? event;

  if (!contractData.value?.items) return;

  // 获取可选择的合同明细项
  const selectableItems = contractData.value.items.filter((_item: any) => {
    // 这里可以添加过滤逻辑，比如过滤掉已全部到货的项
    return true; // 暂时允许选择所有项
  });

  for (const item of selectableItems) {
    const isCurrentlySelected = isContractItemSelected(item.id);

    if (isChecked && !isCurrentlySelected) {
      // 需要选中但当前未选中
      const success = await toggleContractItemDirect(item, true);
      if (!success) {
        // 如果选择失败，跳过这个项目
        continue;
      }
    } else if (!isChecked && isCurrentlySelected) {
      // 需要取消选中但当前已选中
      await toggleContractItemDirect(item, false);
    }
  }

  updateSelectAllStatus();
}

/**
 * 直接切换合同明细选择状态（不依赖事件）
 * @returns {Promise<boolean>} 返回是否成功
 */
async function toggleContractItemDirect(
  contractItem: any,
  isChecked: boolean,
): Promise<boolean> {
  if (isChecked) {
    // 获取已到货统计信息
    const arrivalStats = await getContractItemArrivalStats(contractItem.id);

    // 计算未到货数量和金额
    const unarrivedQuantity =
      contractItem.contract_quantity - arrivalStats.receivedQuantity;
    const unarrivedAmount =
      contractItem.contract_amount - arrivalStats.receivedAmount;

    // 检查是否已全部到货
    if (unarrivedQuantity <= 0) {
      message.warning(
        `该合同明细项"${contractItem.material_type || ''}${contractItem.model ? ` - ${contractItem.model}` : ''}"已全部到货，无需再次创建到货记录！`,
      );
      return false; // 返回false表示选择失败
    }

    // 添加到到货明细
    const arrivalItem: ArrivalItem = {
      id: undefined,
      arrival_id: undefined,
      contract_item_id: contractItem.id!,
      contract_quantity: contractItem.contract_quantity,
      contract_amount: contractItem.contract_amount,
      received_quantity: arrivalStats.receivedQuantity,
      received_amount: arrivalStats.receivedAmount,
      unreceived_quantity: Math.max(0, unarrivedQuantity),
      unreceived_amount: Math.max(0, unarrivedAmount),
      paid_quantity: 0, // 这里需要从付款信息获取
      paid_amount: 0, // 这里需要从付款信息获取
      unpaid_quantity: contractItem.contract_quantity,
      unpaid_amount: contractItem.contract_amount,
      current_arrival_quantity: 0,
      current_arrival_amount: 0,
      remark: undefined,
      contract_item: {
        id: contractItem.id || 0,
        material_type: contractItem.material_type,
        model: contractItem.model || '',
        brand: contractItem.brand || '',
        pn: contractItem.pn || '',
        spec: contractItem.spec || '',
        unit: contractItem.unit || '',
        contract_quantity: contractItem.contract_quantity,
        contract_price: contractItem.contract_price,
      },
    };
    arrivalItems.value.push(arrivalItem);
    return true; // 成功添加
  } else {
    // 从到货明细中移除
    const index = arrivalItems.value.findIndex(
      (item) => item.contract_item_id === contractItem.id,
    );
    if (index !== -1) {
      arrivalItems.value.splice(index, 1);
    }
    return true; // 成功移除
  }
}

/**
 * 确认选择合同明细
 */
function confirmContractItems() {
  showContractItemsModal.value = false;
}

/**
 * 获取仓库列表
 */
async function fetchWarehouseList() {
  try {
    const res = await getWarehouseListApi({
      page: 1,
      pageSize: 100,
    });
    warehouseList.value = res.list.map((item) => ({
      label: item.name,
      value: item.id,
    }));
  } catch (error) {
    console.error('获取仓库列表失败:', error);
  }
}

/**
 * 删除行
 */
function handleDeleteRow(rowIndex: number) {
  if (arrivalItems.value.length <= 1) {
    message.warning('至少需要保留一行到货明细');
    return;
  }

  // 从数组中删除对应的行
  arrivalItems.value.splice(rowIndex, 1);

  // 更新表格数据
  if (itemGridApi && itemGridApi.setState) {
    try {
      itemGridApi.setState({
        gridOptions: {
          data: arrivalItems.value,
        },
      });
    } catch (error) {
      console.error('更新表格数据失败:', error);
    }
  }

  message.success('删除成功');
}

// 显示确认弹窗
function showConfirmModal(data: any) {
  pendingData.value = data;
  confirmModalVisible.value = true;
}

// 确认提交
async function handleConfirmSubmit() {
  if (!pendingData.value) return;

  confirmLoading.value = true;
  try {
    let result;
    let inboundNo = null;

    if (formData.value?.id) {
      // 更新到货记录
      result = await updateArrival(formData.value.id, pendingData.value);
      message.success('更新到货记录成功');
    } else {
      // 如果选择了创建入库工单，则先创建入库工单
      if (createInbound.value) {
        try {
          // 先创建入库工单，使用临时数据
          inboundNo = await createInboundTicket(pendingData.value);
          if (inboundNo) {
            message.success(`入库工单创建成功，工单号：${inboundNo}`);
          }
        } catch (error) {
          console.error('创建入库工单失败:', error);
          message.error('创建入库工单失败，到货单创建已取消');
          // 入库工单创建失败，直接抛出错误阻止到货单创建
          throw error;
        }
      }

      // 入库工单创建成功（或未选择创建），再创建到货记录
      result = await createArrival(pendingData.value);
      message.success('创建到货记录成功');
    }

    // 上传到货相关文件（如果有）
    if (arrivalDocumentUploaderRef.value && result?.id) {
      try {
        await arrivalDocumentUploaderRef.value.uploadFilesToServer(result.id);
      } catch (error) {
        console.error('到货文件上传失败:', error);
      }
    }

    confirmModalVisible.value = false;
    modalApi.close();
    emit('success');
  } catch (error) {
    console.error('提交失败:', error);
    message.error('提交失败');
  } finally {
    confirmLoading.value = false;
    modalApi.lock(false);
  }
}

// 取消确认
function handleCancelConfirm() {
  confirmModalVisible.value = false;
  modalApi.lock(false);
  pendingData.value = null;
}

// 根据合同明细ID获取合同明细信息
function getContractItemById(contractItemId: number) {
  return contractData.value?.items?.find(
    (item: any) => item.id === contractItemId,
  );
}

/**
 * 创建入库工单
 */
async function createInboundTicket(arrivalData: any) {
  if (!createInbound.value) {
    return null;
  }

  try {
    // 检查必填字段
    if (!warehouseId.value) {
      message.error('请选择仓库');
      throw new Error('仓库ID不能为空');
    }

    // 构建入库工单数据
    const inboundData = {
      project: contractData.value?.project_name || '',
      inbound_title: '新购采购入库单',
      inbound_type: inboundType.value,
      inbound_reason: 'new_purchase',
      info: arrivalData.items.map((item: any) => {
        // 通过contract_item_id从合同明细中找到对应的product_id
        const contractItem = contractData.value?.items?.find(
          (contractItem: any) => contractItem.id === item.contract_item_id,
        );
        return {
          product_id: contractItem?.product_id,
          amount: item.current_arrival_quantity || 0,
        };
      }),
      warehouse_id: warehouseId.value,
      tracking_info: arrivalData.tracking_info || '',
      may_arrive_at: arrivalData.may_arrive_at || new Date().toISOString(),
      valid: needValidation.value,
      purchase_no: contractData.value?.contract_no || '',
    };

    // 创建入库工单
    const createResult = await createInboundTicketApiV2(inboundData);

    // 启动工作流
    if (createResult.inbound_no) {
      await startWorkflowV2({
        inbound_no: createResult.inbound_no,
        inbound_type: inboundType.value,
        verify: needValidation.value,
      });
    }

    return createResult.inbound_no;
  } catch (error) {
    console.error('创建入库工单失败:', error);
    // 不在这里显示错误消息，让调用方处理
    throw error;
  }
}

// 重置表单
function resetForm() {
  formData.value = null;
  contractData.value = null;
  supplierData.value = null;
  arrivalItems.value = [];
  activeKey.value = 'basic';
  readOnly.value = false;
  basicFormApi.resetForm();
}

// 处理合同变化
async function handleContractChange(contractId: number) {
  if (!contractId) {
    contractData.value = null;
    supplierData.value = null;
    arrivalItems.value = [];
    return;
  }

  try {
    // 获取合同详情
    const contract = await getPurchaseContractById(contractId);
    contractData.value = contract;

    // 设置供应商信息（从合同的supplier_name获取）
    if (contract.supplier_name) {
      supplierData.value = {
        id: contract.supplier_id,
        name: contract.supplier_name,
      };
    }

    // 清空到货明细，让用户手动选择
    arrivalItems.value = [];
  } catch (error) {
    console.error('获取合同信息失败:', error);
    message.error('获取合同信息失败');
    arrivalItems.value = [];
  }
}

// 打开模态框
async function openModal(data?: Arrival, readonly = false) {
  resetForm();
  readOnly.value = readonly;

  if (data) {
    formData.value = data;
    await nextTick();
    basicFormApi.setValues(data);

    if (data.contract_id) {
      await handleContractChange(data.contract_id);
    }

    // 设置到货明细数据
    if (data.items && data.items.length > 0) {
      arrivalItems.value = data.items;
    }
  }

  modalApi.open();
}

// 提交表单
async function handleSubmit() {
  const { valid } = await basicFormApi.validate();
  if (!valid) {
    modalApi.lock(false);
    message.error('请检查必填项');
    return;
  }

  const basicData = await basicFormApi.getValues();

  // 验证入库选项
  if (createInbound.value && !warehouseId.value) {
    modalApi.lock(false);
    message.error('发起入库时，关联仓库为必填项');
    return;
  }

  // 验证到货明细
  if (!arrivalItems.value || arrivalItems.value.length === 0) {
    modalApi.lock(false);
    message.error('请选择到货明细项');
    return;
  }

  // 从表格获取最新数据并同步到原始数组
  if (itemGridApi && itemGridApi.grid) {
    try {
      const tableData = itemGridApi.grid.getTableData();
      if (tableData && tableData.fullData && tableData.fullData.length > 0) {
        // 同步表格数据到原始数组
        tableData.fullData.forEach((tableRow: any, index: number) => {
          if (arrivalItems.value[index]) {
            arrivalItems.value[index].current_arrival_quantity =
              tableRow.current_arrival_quantity;
            arrivalItems.value[index].current_arrival_amount =
              tableRow.current_arrival_amount;
            arrivalItems.value[index].expected_arrival_date =
              tableRow.expected_arrival_date;
          }
        });
      }
    } catch (error) {
      console.warn('同步表格数据失败:', error);
    }
  }

  // 验证到货明细中的必填项
  for (let i = 0; i < arrivalItems.value.length; i++) {
    const item = arrivalItems.value[i];
    if (!item) {
      modalApi.lock(false);
      message.error(`第${i + 1}项到货明细数据异常`);
      return;
    }

    // 转换为数字进行验证
    const quantity = Number(item.current_arrival_quantity);
    if (Number.isNaN(quantity) || quantity <= 0) {
      modalApi.lock(false);
      message.error(
        `第${i + 1}项到货明细的到货数量不能为空或小于等于0，当前值: ${item.current_arrival_quantity}`,
      );
      return;
    }

    // 验证预计到货时间
    if (!item.expected_arrival_date) {
      modalApi.lock(false);
      message.error(`第${i + 1}项到货明细的预计到货时间不能为空`);
      return;
    }
  }

  try {
    const data = formData.value?.id
      ? {
          ...basicData,
          items: arrivalItems.value.map((item) => ({
            id: item.id,
            contract_item_id: item.contract_item_id,
            current_arrival_quantity:
              Number(item.current_arrival_quantity) || 0,
            current_arrival_amount: Number(item.current_arrival_amount) || 0,
            expected_arrival_date: item.expected_arrival_date,
            serial_numbers: item.serial_number_list || [],
            remark: item.remark,
          })),
        }
      : {
          contract_id: basicData.contract_id,
          may_arrive_at: basicData.may_arrive_at,
          arrival_notice: basicData.arrival_notice || '',
          tracking_info: basicData.tracking_info || '',
          arrival_address: basicData.arrival_address || '',
          remark: basicData.remark || '',
          items: arrivalItems.value.map((item) => ({
            contract_item_id: item.contract_item_id,
            current_arrival_quantity:
              Number(item.current_arrival_quantity) || 0,
            current_arrival_amount: Number(item.current_arrival_amount) || 0,
            expected_arrival_date: item.expected_arrival_date,
            serial_numbers: item.serial_number_list || [],
            remark: item.remark,
          })),
        };

    // 显示确认弹窗
    showConfirmModal(data);
  } catch (error) {
    console.error('数据准备失败:', error);
    modalApi.lock(false);
  }
}

// 组件挂载时初始化
onMounted(() => {
  fetchWarehouseList();
});

// 监听props变化
watch(
  () => props.data,
  (data) => {
    if (data !== undefined) {
      openModal(data || undefined);
    }
  },
  { immediate: true },
);

// 监听modal显示状态，更新全选状态
watch(
  () => showContractItemsModal.value,
  (isVisible) => {
    if (isVisible) {
      updateSelectAllStatus();
    }
  },
);

// 监听到货明细变化，更新全选状态
watch(
  () => arrivalItems.value.length,
  () => {
    if (showContractItemsModal.value) {
      updateSelectAllStatus();
    }
  },
);

defineExpose({
  openModal,
});
</script>

<template>
  <Modal :title="getTitle" width="1000px">
    <Tabs v-model:active-key="activeKey" class="mx-4">
      <Tabs.TabPane key="basic" tab="基本信息">
        <!-- 提示信息 -->
        <div
          v-if="!contractData && !readOnly"
          class="mx-4 mb-4 rounded-lg border border-yellow-200 bg-yellow-50 p-4"
        >
          <div class="flex items-center text-yellow-700">
            <span class="mr-2 text-lg">💡</span>
            <span class="font-medium"
              >请先选择关联合同，系统将自动显示合同信息和对方公司信息</span
            >
          </div>
        </div>

        <!-- 合同关键信息展示 -->
        <div v-if="contractData" class="mx-4 mb-6">
          <!-- 合同标题和基本信息 -->
          <div
            class="mb-4 rounded-xl border border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 p-4 shadow-sm"
          >
            <div class="mb-3 flex items-center">
              <div
                class="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-blue-500 text-white"
              >
                <span class="text-lg">📋</span>
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-bold text-blue-900">
                  {{ contractData.contract_title || '合同标题' }}
                </h3>
                <p class="text-sm text-blue-600">
                  合同编号：{{ contractData.contract_no || '-' }}
                </p>
              </div>
              <div class="text-right">
                <div class="text-lg font-bold text-red-600">
                  ¥{{ Number(contractData.total_amount || 0).toLocaleString() }}
                </div>
                <div class="text-xs text-gray-500">合同总金额</div>
              </div>
            </div>
          </div>

          <!-- 关键信息单行展示 -->
          <div class="space-y-3">
            <!-- 第一行：我方公司、对方公司、项目信息、合同类型 -->
            <div class="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-4">
              <!-- 我方公司 -->
              <div
                class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm"
              >
                <div class="flex items-center">
                  <span class="mr-2 text-cyan-500">🏛️</span>
                  <span class="text-sm font-medium text-gray-600"
                    >我方公司：</span
                  >
                  <span class="ml-2 font-semibold text-gray-900">{{
                    contractData.our_company_name || '-'
                  }}</span>
                </div>
              </div>
              <!-- 对方公司 -->
              <div
                class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm"
              >
                <div class="flex items-center">
                  <span class="mr-2 text-blue-500">🏢</span>
                  <span class="text-sm font-medium text-gray-600"
                    >对方公司名称：</span
                  >
                  <span class="ml-2 font-semibold text-gray-900">{{
                    contractData.supplier_name || '-'
                  }}</span>
                </div>
              </div>
              <div
                class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm"
              >
                <div class="flex items-center">
                  <span class="mr-2 text-green-500">�</span>
                  <span class="text-sm font-medium text-gray-600"
                    >项目名称：</span
                  >
                  <span class="ml-2 font-semibold text-gray-900">{{
                    contractData.project_name || '-'
                  }}</span>
                </div>
              </div>
              <div
                class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm"
              >
                <div class="flex items-center">
                  <span class="mr-2 text-purple-500">📝</span>
                  <span class="text-sm font-medium text-gray-600"
                    >合同类型：</span
                  >
                  <span class="ml-2 font-semibold text-gray-900">{{
                    CONTRACT_TYPE_CONFIG[contractData.contract_type]?.text ||
                    contractData.contract_type ||
                    '-'
                  }}</span>
                </div>
              </div>
            </div>

            <!-- 第二行：签署日期、交付地址、申请人 -->
            <div class="grid grid-cols-1 gap-3 md:grid-cols-3">
              <div
                class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm"
              >
                <div class="flex items-center">
                  <span class="mr-2 text-orange-500">�</span>
                  <span class="text-sm font-medium text-gray-600"
                    >签署日期：</span
                  >
                  <span class="ml-2 font-semibold text-gray-900">
                    {{
                      contractData.signing_date
                        ? formatDate(contractData.signing_date)
                        : '-'
                    }}
                  </span>
                </div>
              </div>
              <div
                class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm"
              >
                <div class="flex items-center">
                  <span class="mr-2 text-red-500">📍</span>
                  <span class="text-sm font-medium text-gray-600"
                    >交付地址：</span
                  >
                  <span class="ml-2 font-semibold text-gray-900">{{
                    contractData.delivery_address || '-'
                  }}</span>
                </div>
              </div>
              <div
                class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm"
              >
                <div class="flex items-center">
                  <span class="mr-2 text-yellow-500">�</span>
                  <span class="text-sm font-medium text-gray-600"
                    >申请人：</span
                  >
                  <span class="ml-2 font-semibold text-gray-900">{{
                    contractData.creator_name || '-'
                  }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 基本信息表单 -->
        <div class="mx-4">
          <BasicForm />
        </div>

        <!-- 入库选项 -->
        <div v-if="!readOnly" class="mx-4 mt-6">
          <div class="mb-3 text-base font-medium text-gray-800">入库选项</div>
          <div class="rounded-lg border border-gray-200 bg-gray-50 p-4">
            <div class="space-y-4">
              <!-- 是否发起入库 -->
              <div class="flex items-center space-x-3">
                <Switch
                  v-model:checked="createInbound"
                  @change="fetchWarehouseList"
                />
                <span class="text-sm font-medium text-gray-700"
                  >是否发起入库</span
                >
              </div>

              <!-- 入库选项详情 -->
              <div
                v-if="createInbound"
                class="ml-6 space-y-4 border-l-2 border-blue-200 pl-4"
              >
                <!-- 入库类型 -->
                <div class="flex items-center space-x-3">
                  <span class="w-20 text-sm text-gray-600">入库类型：</span>
                  <Radio.Group v-model:value="inboundType">
                    <Radio value="part_inbound">配件入库</Radio>
                    <Radio value="device_inbound">设备入库</Radio>
                  </Radio.Group>
                </div>

                <!-- 关联仓库 -->
                <div class="flex items-center space-x-3">
                  <span class="w-20 text-sm text-gray-600">
                    <span class="text-red-500">*</span>关联仓库：
                  </span>
                  <Select
                    v-model:value="warehouseId"
                    placeholder="请选择仓库（必填）"
                    style="width: 200px"
                    :options="warehouseList"
                    show-search
                    :filter-option="
                      (input: string, option: any) =>
                        option.label.toLowerCase().includes(input.toLowerCase())
                    "
                  />
                </div>

                <!-- 是否需要工程师验证 -->
                <div class="flex items-center space-x-3">
                  <span class="w-20 text-sm text-gray-600">工程师验证：</span>
                  <Switch v-model:checked="needValidation" />
                  <span class="text-xs text-gray-500">
                    {{ needValidation ? '需要工程师验证' : '无需工程师验证' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 附件上传 -->
        <div class="mx-4 mt-6">
          <div class="mb-3 text-base font-medium text-gray-800">相关附件</div>
          <div class="rounded-lg border border-gray-200 bg-gray-50 p-4">
            <FileUploader
              ref="arrivalDocumentUploaderRef"
              file-description="到货文件"
              module-type="arrivals"
              :module-id="formData?.id"
              :disabled="readOnly"
              show-upload-list
              :max-count="10"
              multiple
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.xlsx,.xls,.csv"
              list-type="text"
              compact
            >
              <Button v-if="!readOnly" type="primary" size="small">
                <span class="mr-1">📎</span>
                选择附件
              </Button>
            </FileUploader>
            <div class="mt-2 text-xs text-gray-500">
              支持格式：PDF、Word文档、Excel表格、CSV文件、图片文件，最多上传10个文件
            </div>
          </div>
        </div>
      </Tabs.TabPane>

      <Tabs.TabPane key="items" tab="到货明细">
        <div class="mx-4">
          <!-- 提示信息 -->
          <div
            v-if="!contractData && !readOnly"
            class="mb-4 rounded-lg border border-yellow-200 bg-yellow-50 p-4"
          >
            <div class="flex items-center text-yellow-700">
              <span class="mr-2 text-lg">💡</span>
              <span class="font-medium"
                >请先在基本信息中选择关联合同，然后在此处选择需要到货的合同明细项</span
              >
            </div>
          </div>

          <!-- 到货明细表格 -->
          <div v-if="contractData" class="space-y-4">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold">到货明细</h3>
              <Button
                type="primary"
                @click="showContractItemsModal = true"
                :disabled="readOnly"
              >
                选择合同明细
              </Button>
            </div>

            <!-- 明细表格 -->
            <div v-if="arrivalItems.length > 0">
              <ItemGrid>
                <!-- 预计到货时间显示插槽 -->
                <template #expected_arrival_date="{ row }">
                  <span v-if="row.expected_arrival_date" class="text-sm">
                    {{ formatDate(row.expected_arrival_date) }}
                  </span>
                  <span v-else class="text-sm text-gray-400">未设置</span>
                </template>

                <!-- 预计到货时间编辑插槽 -->
                <template #expected_arrival_date_edit="{ row }">
                  <DatePicker
                    v-model:value="row.expected_arrival_date"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DDTHH:mm:ss[Z]"
                    placeholder="请选择预计到货时间"
                    size="small"
                    style="width: 100%"
                  />
                </template>

                <!-- 操作列插槽 -->
                <template #actions="{ rowIndex }">
                  <Button
                    type="text"
                    danger
                    size="small"
                    @click="handleDeleteRow(rowIndex)"
                    title="删除这行"
                  >
                    🗑️
                  </Button>
                </template>
              </ItemGrid>
            </div>

            <div v-else class="py-8 text-center text-gray-500">
              暂无到货明细，请点击"选择合同明细"添加
            </div>
          </div>

          <div v-else class="py-8 text-center text-gray-500">
            <div class="mb-2">暂无到货明细</div>
            <div class="text-xs text-gray-400">请先选择关联合同</div>
          </div>
        </div>
      </Tabs.TabPane>
    </Tabs>
  </Modal>

  <!-- 合同明细选择模态框 -->
  <AModal
    v-model:open="showContractItemsModal"
    title="选择合同明细"
    width="1400px"
    :footer="null"
  >
    <div v-if="contractData?.items" class="space-y-4">
      <div class="mb-4 text-sm text-gray-600">
        请选择需要到货的合同明细项，系统将自动计算已到货和未到货数量。可使用表头的"全选"按钮快速选择所有项目。
      </div>

      <div class="overflow-x-auto">
        <table class="w-full rounded-lg border border-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                <div class="flex items-center space-x-2">
                  <Checkbox
                    :checked="selectAllChecked"
                    :indeterminate="selectAllIndeterminate"
                    @change="handleSelectAll"
                  />
                  <span>全选</span>
                </div>
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                物料类型
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                品牌
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                规格
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                型号
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                原厂PN
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                单位
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                合同数量
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                合同单价
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                合同金额
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                到货状态
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="item in contractData.items"
              :key="item.id"
              class="hover:bg-gray-50"
            >
              <td class="border-b px-4 py-3">
                <input
                  type="checkbox"
                  :checked="isContractItemSelected(item.id)"
                  @change="toggleContractItem(item, $event)"
                  class="rounded border-gray-300"
                />
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                {{ item.material_type || '-' }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                {{ item.brand || '-' }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                {{ item.spec || '-' }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                {{ item.model || '-' }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                <span class="font-mono text-sm text-blue-600">
                  {{ item.pn || '-' }}
                </span>
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                {{ item.unit || '-' }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                {{ item.contract_quantity || 0 }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                ¥{{ Number(item.contract_price || 0).toLocaleString() }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                ¥{{ Number(item.contract_amount || 0).toLocaleString() }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                <ContractItemArrivalStatus
                  :contract-item="item"
                  :contract-id="contractData?.id"
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="flex justify-end space-x-2 pt-4">
        <Button @click="showContractItemsModal = false"> 取消 </Button>
        <Button type="primary" @click="confirmContractItems"> 确定 </Button>
      </div>
    </div>

    <div v-else class="py-8 text-center text-gray-500">暂无合同明细数据</div>
  </AModal>

  <!-- 确认提交弹窗 -->
  <AModal
    v-model:open="confirmModalVisible"
    title="确认提交到货记录"
    width="800px"
    :confirm-loading="confirmLoading"
    @ok="handleConfirmSubmit"
    @cancel="handleCancelConfirm"
  >
    <div class="space-y-6">
      <!-- 基本信息 -->
      <div class="rounded border border-green-200 bg-green-50 p-4">
        <div class="mb-3 flex items-center">
          <div class="mr-2 h-4 w-1 rounded bg-green-500"></div>
          <h3 class="text-sm font-semibold text-green-800">到货基本信息</h3>
        </div>
        <div class="grid grid-cols-2 gap-2 text-sm text-green-700">
          <div>
            <span class="font-medium">关联合同:</span>
            {{ contractData?.contract_no }}
          </div>
          <div>
            <span class="font-medium">预计到货时间:</span>
            {{
              pendingData?.may_arrive_at
                ? new Date(pendingData.may_arrive_at).toLocaleString()
                : ''
            }}
          </div>
          <div v-if="pendingData?.arrival_notice">
            <span class="font-medium">到货通知:</span>
            {{ pendingData.arrival_notice }}
          </div>
          <div v-if="pendingData?.tracking_info">
            <span class="font-medium">物流跟踪:</span>
            {{ pendingData.tracking_info }}
          </div>
          <div v-if="pendingData?.arrival_address" class="col-span-2">
            <span class="font-medium">到货地址:</span>
            {{ pendingData.arrival_address }}
          </div>
          <div v-if="pendingData?.remark" class="col-span-2">
            <span class="font-medium">备注:</span>
            {{ pendingData.remark }}
          </div>
        </div>
      </div>

      <!-- 到货明细 -->
      <div class="rounded border border-blue-200 bg-blue-50 p-4">
        <div class="mb-3 flex items-center">
          <div class="mr-2 h-4 w-1 rounded bg-blue-500"></div>
          <h3 class="text-sm font-semibold text-blue-800">到货明细</h3>
        </div>
        <div class="space-y-3">
          <div
            v-for="(item, index) in pendingData?.items"
            :key="index"
            class="rounded border border-blue-300/50 bg-white p-3"
          >
            <div class="grid grid-cols-2 gap-2 text-sm text-blue-700">
              <div>
                <span class="font-medium">物料类型:</span>
                {{ getContractItemById(item.contract_item_id)?.material_type }}
              </div>
              <div>
                <span class="font-medium">品牌:</span>
                {{ getContractItemById(item.contract_item_id)?.brand }}
              </div>
              <div>
                <span class="font-medium">规格:</span>
                {{ getContractItemById(item.contract_item_id)?.spec }}
              </div>
              <div>
                <span class="font-medium">型号:</span>
                {{ getContractItemById(item.contract_item_id)?.model }}
              </div>
              <div>
                <span class="font-medium">本次到货数量:</span>
                {{ item.current_arrival_quantity }}
                {{ getContractItemById(item.contract_item_id)?.unit }}
              </div>
              <div>
                <span class="font-medium">本次到货金额:</span>
                ¥{{ item.current_arrival_amount?.toFixed(2) }}
              </div>
              <div v-if="item.expected_arrival_date" class="col-span-2">
                <span class="font-medium">预计到货时间:</span>
                {{ new Date(item.expected_arrival_date).toLocaleString() }}
              </div>
              <div v-if="item.remark" class="col-span-2">
                <span class="font-medium">备注:</span>
                {{ item.remark }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 入库选项 -->
      <div
        v-if="createInbound"
        class="rounded border border-orange-200 bg-orange-50 p-4"
      >
        <div class="mb-3 flex items-center">
          <div class="mr-2 h-4 w-1 rounded bg-orange-500"></div>
          <h3 class="text-sm font-semibold text-orange-800">入库选项</h3>
        </div>
        <div class="grid grid-cols-2 gap-2 text-sm text-orange-700">
          <div>
            <span class="font-medium">入库类型:</span>
            {{ inboundType === 'part_inbound' ? '配件入库' : '设备入库' }}
          </div>
          <div>
            <span class="font-medium">关联仓库:</span>
            {{ warehouseList.find((w) => w.value === warehouseId)?.label }}
          </div>
          <div class="col-span-2">
            <span class="font-medium">工程师验证:</span>
            {{ needValidation ? '需要工程师验证' : '无需工程师验证' }}
          </div>
        </div>
      </div>

      <div class="border-t pt-4 text-center text-sm text-gray-500">
        请确认以上信息无误后点击确定提交
      </div>
    </div>
  </AModal>
</template>
