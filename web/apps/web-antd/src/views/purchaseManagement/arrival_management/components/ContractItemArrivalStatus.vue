<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';

import { Tag } from 'ant-design-vue';

import { getArrivalsByContractId } from '#/api/core/purchase/arrival';

interface Props {
  contractItem: {
    contract_amount: number;
    contract_quantity: number;
    id: number;
    material_type?: string;
    model?: string;
  };
  contractId?: number;
}

const props = defineProps<Props>();

const loading = ref(false);
const receivedQuantity = ref(0);
const receivedAmount = ref(0);

// 计算到货状态
const arrivalStatus = computed(() => {
  const totalQuantity = props.contractItem.contract_quantity || 0;
  const received = receivedQuantity.value;

  if (received <= 0) {
    return {
      type: 'default',
      text: '未到货',
      color: 'gray',
    };
  } else if (received >= totalQuantity) {
    return {
      type: 'success',
      text: '已全部到货',
      color: 'green',
    };
  } else {
    return {
      type: 'processing',
      text: '部分到货',
      color: 'blue',
    };
  }
});

// 计算到货进度文本
const progressText = computed(() => {
  const total = props.contractItem.contract_quantity || 0;
  const received = receivedQuantity.value;

  if (total <= 0) return '';

  const percentage = Math.round((received / total) * 100);
  return `${received}/${total} (${percentage}%)`;
});

// 获取合同明细项的已到货统计信息
async function getContractItemArrivalStats() {
  if (!props.contractItem.id || !props.contractId) return;

  try {
    loading.value = true;

    // 获取该合同的所有到货记录
    const arrivals = await getArrivalsByContractId(props.contractId);

    let totalReceivedQuantity = 0;
    let totalReceivedAmount = 0;

    // 遍历所有已审批完成的到货记录
    for (const arrival of arrivals) {
      if (arrival.status === 'completed' && arrival.items) {
        // 查找该明细项的到货记录
        const arrivalItem = arrival.items.find(
          (item) => item.contract_item_id === props.contractItem.id,
        );
        if (arrivalItem) {
          totalReceivedQuantity += arrivalItem.current_arrival_quantity || 0;
          totalReceivedAmount += arrivalItem.current_arrival_amount || 0;
        }
      }
    }

    receivedQuantity.value = totalReceivedQuantity;
    receivedAmount.value = totalReceivedAmount;
  } catch (error) {
    console.error('获取到货统计信息失败:', error);
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  getContractItemArrivalStats();
});
</script>

<template>
  <div class="flex flex-col space-y-1">
    <Tag :color="arrivalStatus.color" class="text-xs">
      {{ arrivalStatus.text }}
    </Tag>
    <div v-if="progressText" class="text-xs text-gray-500">
      {{ progressText }}
    </div>
  </div>
</template>
