<script lang="ts" setup>
import type {
  Arrival,
  ArrivalQuery,
  PageParams,
} from '#/api/core/purchase/arrival';

import { nextTick, ref } from 'vue';
import { useRouter } from 'vue-router';

import { AccessControl } from '@vben/access';
import { Page, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { Button, Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getArrivalById, getArrivalList } from '#/api/core/purchase/arrival';

import { ARRIVAL_COMPLETE_CONFIG, ARRIVAL_STATUS_CONFIG } from './constants';
import { useColumns, useItemColumnsReadonly } from './data';
import Form from './modules/form.vue';
import { useGridFormSchema } from './schema';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

const router = useRouter();

// 到货明细模态框相关数据
const detailModalVisible = ref(false);
const currentItems = ref<any[]>([]);
const currentArrivalNo = ref('');

// 到货明细表格
const [DetailGrid, detailGridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useItemColumnsReadonly(),
    height: 'auto',
    maxHeight: 500,
    pagerConfig: {
      enabled: false,
    },
    proxyConfig: {
      autoLoad: false,
      ajax: {
        query: async () => {
          return {
            items: currentItems.value,
            total: currentItems.value.length,
          };
        },
      },
    },
    toolbarConfig: {
      enabled: false,
    },
  },
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['create_time', ['start_date', 'end_date']]],
    schema: useGridFormSchema(),
    submitOnChange: false,
    submitOnEnter: true,
  },
  gridOptions: {
    columns: useColumns(),
    height: 'auto',
    pagerConfig: {
      enabled: true,
    },
    proxyConfig: {
      ajax: {
        query: async (
          { page }: { page: PageParams },
          formValues: Record<string, any>,
        ) => {
          const params: ArrivalQuery = {
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          };

          const res = await getArrivalList(params);

          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    toolbarConfig: {
      custom: true,
      export: true,
      refresh: true,
      refreshOptions: { code: 'query' },
      search: true,
      zoom: true,
      zoomOptions: {},
    },
  },
});

/**
 * 创建新到货记录
 */
function onCreate() {
  formModalApi.setData(null).open();
}

// /**
//  * 编辑到货记录
//  */
// function onEdit(row: Arrival) {
//   formModalApi.setData(row).open();
// }

// /**
//  * 查看到货记录
//  */
// function onView(row: Arrival) {
//   formModalApi.setData(row).open();
// }

/**
 * 查看到货记录详情
 */
function onDetail(row: Arrival) {
  router.push(`/purchase-management/arrival-management/detail/${row.id}`);
}

/**
 * 显示到货明细
 */
async function showItemsDetail(row: Arrival) {
  try {
    // 调用API获取完整的到货详情数据
    const arrivalDetail = await getArrivalById(row.id);

    if (arrivalDetail.items && arrivalDetail.items.length > 0) {
      currentItems.value = arrivalDetail.items;
      currentArrivalNo.value = arrivalDetail.arrival_no || '';
      detailModalVisible.value = true;

      // 等待模态框打开后加载数据
      await nextTick();
      if (detailGridApi && detailGridApi.grid) {
        await detailGridApi.grid.commitProxy('query');
      }
    } else {
      // 如果没有明细数据，显示提示
      console.warn('该到货记录没有明细数据');
    }
  } catch (error) {
    console.error('获取到货详情失败:', error);
    // 可以添加错误提示
  }
}

/**
//  * 提交到货记录
//  */
// async function onSubmit(row: Arrival) {
//   try {
//     await submitArrival(row.id);
//     message.success('提交成功');
//     refreshGrid();
//   } catch (error) {
//     console.error('提交失败:', error);
//     message.error('提交失败');
//   }
// }

// /**
//  * 取消到货记录
//  */
// async function onCancel(row: Arrival) {
//   try {
//     await cancelArrival(row.id);
//     message.success('取消成功');
//     refreshGrid();
//   } catch (error) {
//     console.error('取消失败:', error);
//     message.error('取消失败');
//   }
// }

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid>
      <template #toolbar-tools>
        <AccessControl :codes="['Purchase:Arrival:Create']" type="code">
          <Button type="primary" @click="onCreate">
            <Plus class="size-5" />
            新建到货记录
          </Button>
        </AccessControl>
      </template>

      <!-- 状态插槽 -->
      <template #status="{ row }">
        <Tag :color="ARRIVAL_STATUS_CONFIG[row.status as string]?.color">
          {{ ARRIVAL_STATUS_CONFIG[row.status as string]?.text || row.status }}
        </Tag>
      </template>

      <!-- 是否全部到货插槽 -->
      <template #is_complete="{ row }">
        <Tag
          :color="ARRIVAL_COMPLETE_CONFIG[row.is_complete.toString()]?.color"
        >
          {{
            ARRIVAL_COMPLETE_CONFIG[row.is_complete.toString()]?.text ||
            (row.is_complete ? '是' : '否')
          }}
        </Tag>
      </template>

      <!-- 到货明细按钮插槽 -->
      <template #items_action="{ row }">
        <Button
          type="link"
          :disabled="!row.items || row.items.length === 0"
          @click="showItemsDetail(row)"
        >
          查看明细({{ row.items?.length || 0 }})
        </Button>
      </template>

      <!-- 操作列插槽 -->
      <template #action="{ row }">
        <Button size="small" type="link" @click="onDetail(row)"> 查看 </Button>
      </template>
    </Grid>

    <!-- 到货明细模态框 -->
    <Modal
      v-model:open="detailModalVisible"
      title="到货明细"
      :width="1400"
      :footer="null"
      :destroy-on-close="true"
      class="arrival-detail-modal"
    >
      <div class="arrival-detail-header">
        <div
          class="flex items-center justify-between rounded-lg bg-green-50 p-4"
        >
          <div class="flex items-center space-x-3">
            <div
              class="flex h-10 w-10 items-center justify-center rounded-full bg-green-500"
            >
              <span class="font-semibold text-white">到</span>
            </div>
            <div>
              <div class="text-lg font-semibold text-green-900">
                到货通知单号: {{ currentArrivalNo }}
              </div>
              <div class="text-sm text-green-600">
                共 {{ currentItems.length }} 个明细项
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <span class="text-sm text-green-600">总计金额:</span>
            <span class="text-lg font-bold text-green-800">
              ¥{{
                currentItems
                  .reduce(
                    (sum, item) => sum + (item.current_arrival_amount || 0),
                    0,
                  )
                  .toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })
              }}
            </span>
          </div>
        </div>
      </div>

      <div class="arrival-detail-table">
        <DetailGrid />
      </div>
    </Modal>
  </Page>
</template>

<style lang="less" scoped>
:deep(.vxe-table) {
  .vxe-header--row {
    background: #f8fafc;

    .vxe-header--column {
      background: #f8fafc;
      border-bottom: 2px solid #e2e8f0;
      font-weight: 600;
      color: #374151;
      font-size: 13px;
    }
  }

  .vxe-body--row {
    &:nth-child(even) {
      background: #fdfdfd;
    }

    &:hover {
      background: #f8fafc !important;
    }

    .vxe-body--column {
      border-bottom: 1px solid #f1f5f9;
      vertical-align: middle;
    }
  }
}

// 操作按钮样式优化
:deep(.ant-btn-link) {
  padding: 0 4px;
  height: auto;
  line-height: 1.4;

  &:not(:last-child) {
    margin-right: 8px;
  }
}

// Tag 样式优化
:deep(.ant-tag) {
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  padding: 2px 8px;
}
</style>
