import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { PaymentRequest } from '#/api/core/purchase/payment_request';

/**
 * 获取付款申请列表表格列配置
 */
export function useColumns(): VxeTableGridOptions<PaymentRequest>['columns'] {
  return [
    {
      field: 'payment_no',
      title: '付款申请编号',
      width: 180,
      fixed: 'left',
    },
    {
      field: 'status',
      title: '状态',
      width: 180,
      slots: { default: 'status' },
    },
    {
      field: 'contract_no',
      title: '关联合同',
      width: 160,
    },
    {
      field: 'items',
      title: '付款明细',
      width: 120,
      slots: { default: 'items_action' },
    },
    {
      field: 'our_company_name',
      title: '我方公司',
      width: 180,
    },
    {
      field: 'supplier_name',
      title: '对方公司名称',
      width: 200,
    },
    {
      field: 'payment_type',
      title: '付款类型',
      width: 120,
      slots: { default: 'payment_type' },
    },
    {
      field: 'current_payment_amount',
      title: '本次申请付款金额',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'contract_total_amount',
      title: '合同总金额',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'bank_name',
      title: '开户银行',
      width: 150,
    },
    {
      field: 'bank_account',
      title: '银行账号',
      width: 150,
    },

    {
      field: 'creator_name',
      title: '申请人',
      width: 100,
    },
    {
      field: 'created_at',
      title: '创建时间',
      formatter: 'formatDateTime',
      width: 180,
    },
    {
      field: 'updated_at',
      title: '更新时间',
      formatter: 'formatDateTime',
      width: 180,
      visible: false,
    },
    {
      title: '操作',
      field: 'operation',
      fixed: 'right',
      width: 100,
      slots: { default: 'action' },
    },
  ];
}

/**
 * 获取付款明细表格列配置
 */
export function useItemColumns(): VxeTableGridOptions<any>['columns'] {
  return [
    {
      field: 'contract_item.material_type',
      title: '物料类型',
      width: 200,
      fixed: 'left',
    },
    {
      field: 'contract_item.model',
      title: '型号',
      width: 150,
    },
    {
      field: 'contract_item.brand',
      title: '品牌',
      width: 120,
    },
    {
      field: 'contract_item.pn',
      title: 'PN',
      width: 150,
    },
    {
      field: 'contract_item.spec',
      title: '规格',
      minWidth: 200,
    },
    {
      field: 'contract_item.unit',
      title: '单位',
      width: 80,
    },
    {
      field: 'contract_item.contract_quantity',
      title: '合同数量',
      width: 100,
    },
    {
      field: 'contract_item.contract_price',
      title: '合同单价',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'current_payment_quantity',
      title: '本次付款数量',
      width: 120,
    },
    {
      field: 'current_payment_amount',
      title: '本次付款金额',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'paid_quantity',
      title: '已付数量',
      width: 100,
    },
    {
      field: 'paid_amount',
      title: '已付金额',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'unpaid_quantity',
      title: '未付数量',
      width: 100,
    },
    {
      field: 'unpaid_amount',
      title: '未付金额',
      width: 120,
      formatter: ({ cellValue }) => {
        if (cellValue === null || cellValue === '') return '';
        return `¥${Number(cellValue).toLocaleString()}`;
      },
    },
    {
      field: 'remark',
      title: '备注',
      minWidth: 150,
    },
  ];
}
