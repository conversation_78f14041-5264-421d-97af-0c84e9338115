<script lang="ts" setup>
import type { Company } from '#/api/core/purchase/company';
import type {
  CreatePaymentRequestParams,
  PaymentRequest,
  PaymentRequestItem,
} from '#/api/core/purchase/payment_request';
import type {
  PurchaseContract,
  PurchaseContractItem,
} from '#/api/core/purchase/purchase_contract';
import type { Supplier } from '#/api/core/purchase/supplier';

import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { formatDate } from '@vben/utils';

import {
  Modal as AModal,
  Button,
  Checkbox,
  Input,
  InputNumber,
  message,
  Tabs,
} from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { getCompanyDetailApi } from '#/api/core/purchase/company';
import {
  createPaymentRequest,
  getPaymentRequestsByContract,
  updatePaymentRequest,
} from '#/api/core/purchase/payment_request';
import { getPurchaseContractById } from '#/api/core/purchase/purchase_contract';
import { getSupplierDetailApi } from '#/api/core/purchase/supplier';
import FileUploader from '#/components/FileUploader/FileUploader.vue';
import { PAYMENT_TYPE_CONFIG } from '#/views/purchaseManagement/payment_request/constants';
import { useBasicSchema } from '#/views/purchaseManagement/payment_request/schema';
import { CONTRACT_TYPE_CONFIG } from '#/views/purchaseManagement/purchase_contract/constants';

import ContractItemPaymentStatus from '../components/ContractItemPaymentStatus.vue';

declare global {
  interface Window {
    handleContractChange?: (id: number) => void;
  }
}

const emit = defineEmits(['success']);
const formData = ref<PaymentRequest>();
const readOnly = ref(false);
const contractData = ref<null | PurchaseContract>(null);
const supplierData = ref<null | Supplier>(null);
const companyData = ref<Company | null>(null);

// 付款明细相关
const paymentItems = ref<PaymentRequestItem[]>([]);
const showContractItemsModal = ref(false);
const selectAllChecked = ref(false);
const selectAllIndeterminate = ref(false);

const activeKey = ref('basic');
const confirmModalVisible = ref(false);
const confirmLoading = ref(false);
const pendingData = ref<CreatePaymentRequestParams | null>(null);

// 付款金额统计相关
const contractPaidAmount = ref(0); // 合同已付款金额

// 文件上传器引用
const acceptanceDocumentUploaderRef = ref<InstanceType<typeof FileUploader>>();
const preInvoiceUploaderRef = ref<InstanceType<typeof FileUploader>>();

const getTitle = computed(() => {
  if (readOnly.value) {
    return '查看付款申请';
  }
  return formData.value?.id ? '编辑付款申请' : '创建付款申请';
});

const [Modal, modalApi] = useVbenModal({
  class: 'w-full',
  onOpenChange: (isOpen: boolean) => {
    if (!isOpen) {
      resetForm();
    }
  },
});

const [BasicForm, basicFormApi] = useVbenForm({
  layout: 'vertical',
  schema: useBasicSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-4',
});

const formValues = ref<Record<string, any>>({});

async function updateFormValues() {
  try {
    const values = await basicFormApi.getValues();
    if (values) {
      formValues.value = values;
    }
  } catch (error) {
    console.error('获取表单值失败:', error);
  }
}

function handleFormFieldChange() {
  // 恢复表单值更新，但使用防抖避免频繁调用
  setTimeout(updateFormValues, 100);
}

// 添加防抖标志，避免重复请求
const isHandlingContract = ref(false);
const currentContractId = ref<null | number>(null);

onMounted(() => {
  window.handleContractChange = (id: number) => {
    handleContractChange(id);
  };

  nextTick(() => {
    updateFormValues();
  });

  // 使用较低频率的定时器确保表单值同步，但避免过于频繁
  updateInterval = setInterval(() => {
    updateFormValues();
  }, 2000); // 每2秒更新一次，降低频率
});

onUnmounted(() => {
  window.handleContractChange = undefined;

  // 清理定时器
  if (updateInterval) {
    clearInterval(updateInterval);
    updateInterval = null;
  }
});

watch(
  () => formValues.value?.contract_id,
  async (newVal, oldVal) => {
    if (newVal !== oldVal && newVal && !isHandlingContract.value) {
      isHandlingContract.value = true;
      try {
        await handleContractChange(Number(newVal));
      } finally {
        isHandlingContract.value = false;
      }
    }
  },
  { immediate: false }, // 确保不会在初始化时立即触发
);

// 定期更新表单值以确保实时性
let updateInterval: NodeJS.Timeout | null = null;

async function handleContractChange(contractId: number) {
  if (!contractId) {
    currentContractId.value = null;
    contractData.value = null;
    supplierData.value = null;
    companyData.value = null;
    return;
  }

  // 如果正在处理中，等待完成
  if (isHandlingContract.value) {
    return;
  }

  // 如果已经是当前合同ID且数据已存在，避免重复处理
  if (
    currentContractId.value === contractId &&
    contractData.value?.id === contractId &&
    supplierData.value &&
    companyData.value
  ) {
    return;
  }

  currentContractId.value = contractId;

  try {
    // 获取合同信息及关联的供应商和公司信息
    await fetchContractData(contractId);

    // 获取合同已付款金额
    await fetchContractPaidAmount(contractId);
  } catch (error) {
    console.error('处理合同变化失败:', error);
  }
}

function resetForm() {
  basicFormApi.resetForm();
  contractData.value = null;
  supplierData.value = null;
  companyData.value = null;
  formData.value = undefined;
  readOnly.value = false;
  activeKey.value = 'basic';
  confirmModalVisible.value = false;
  pendingData.value = null;
  // 重置付款明细
  paymentItems.value = [];
  showContractItemsModal.value = false;
  // 重置防抖状态
  currentContractId.value = null;
  isHandlingContract.value = false;
}

/**
 * 获取合同信息
 */
async function fetchContractData(contractId: number) {
  try {
    const contract = await getPurchaseContractById(contractId);
    contractData.value = contract;

    // 获取供应商信息
    if (contract.supplier_id) {
      await fetchSupplierData(contract.supplier_id);
    }

    // 获取我司公司信息
    if (contract.our_company_id) {
      await fetchCompanyData(contract.our_company_id);
    }

    return contract;
  } catch (error) {
    console.error('获取合同信息失败:', error);
    contractData.value = null;
    return null;
  }
}

/**
 * 获取供应商信息
 */
async function fetchSupplierData(supplierId: number) {
  try {
    const supplier = await getSupplierDetailApi(supplierId);
    supplierData.value = supplier;
    return supplier;
  } catch (error) {
    console.error('获取供应商信息失败:', error);
    supplierData.value = null;
    return null;
  }
}

/**
 * 获取公司信息
 */
async function fetchCompanyData(companyId: number) {
  try {
    const company = await getCompanyDetailApi(companyId);
    companyData.value = company;
    return company;
  } catch (error) {
    console.error('获取公司信息失败:', error);
    companyData.value = null;
    return null;
  }
}

/**
 * 获取合同已付款金额
 */
async function fetchContractPaidAmount(contractId: number) {
  try {
    // 获取该合同的所有付款申请
    const paymentRequests = await getPaymentRequestsByContract(contractId);

    // 计算已完成的付款申请总金额
    let totalPaidAmount = 0;
    for (const payment of paymentRequests) {
      // 只计算状态为 'completed' 的付款申请
      if (payment.status === 'completed') {
        totalPaidAmount += payment.current_payment_amount || 0;
      }
    }

    contractPaidAmount.value = totalPaidAmount;
    return totalPaidAmount;
  } catch (error) {
    console.error('获取合同已付款金额失败:', error);
    contractPaidAmount.value = 0;
    return 0;
  }
}

// 付款明细相关函数

/**
 * 更新明细项金额
 */
function updateItemAmount(index: number) {
  const item = paymentItems.value[index];
  if (item && item.contract_item) {
    item.current_payment_amount =
      item.current_payment_quantity * item.contract_item.contract_price;
  }
  // 更新总付款金额
  updatePaymentAmount();
}

/**
 * 删除付款明细项
 */
function removePaymentItem(index: number) {
  paymentItems.value.splice(index, 1);
  // 更新总付款金额
  updatePaymentAmount();
}

/**
 * 计算未付数量
 */
function unpaidQuantity(item: PaymentRequestItem): number {
  const contractQuantity = item.contract_item?.contract_quantity || 0;
  const paidQuantity = item.paid_quantity || 0;
  return Math.max(0, contractQuantity - paidQuantity);
}

/**
 * 计算未付款金额
 */
function unpaidAmount(item: PaymentRequestItem): number {
  const contractPrice = item.contract_item?.contract_price || 0;
  const unpaidQty = unpaidQuantity(item);
  return unpaidQty * contractPrice;
}

/**
 * 获取合同明细项的已付信息
 */
async function getContractItemPaidInfo(
  contractItemId: number,
): Promise<{ paidAmount: number; paidQuantity: number }> {
  try {
    if (!contractData.value?.id) {
      return { paidQuantity: 0, paidAmount: 0 };
    }

    // 获取该合同的所有付款申请
    const paymentRequests = await getPaymentRequestsByContract(
      contractData.value.id,
    );

    let totalPaidQuantity = 0;
    let totalPaidAmount = 0;

    // 遍历所有已完成的付款申请
    for (const payment of paymentRequests) {
      if (payment.status === 'completed' && payment.items) {
        // 查找该明细项的付款记录
        const paymentItem = payment.items.find(
          (item) => item.contract_item_id === contractItemId,
        );
        if (paymentItem) {
          totalPaidQuantity += paymentItem.current_payment_quantity || 0;
          totalPaidAmount += paymentItem.current_payment_amount || 0;
        }
      }
    }

    return { paidQuantity: totalPaidQuantity, paidAmount: totalPaidAmount };
  } catch (error) {
    console.error('获取合同明细项已付信息失败:', error);
    return { paidQuantity: 0, paidAmount: 0 };
  }
}

// 计算属性
const totalPaymentQuantity = computed(() => {
  return paymentItems.value.reduce((total, item) => {
    return total + (item.current_payment_quantity || 0);
  }, 0);
});

const totalPaymentAmount = computed(() => {
  return paymentItems.value.reduce((total, item) => {
    return total + (item.current_payment_amount || 0);
  }, 0);
});

// 本次申请付款金额（从表单获取）
const currentPaymentAmount = computed(() => {
  return Number(formValues.value.current_payment_amount || 0);
});

// 更新表单中的付款金额
function updatePaymentAmount() {
  if (basicFormApi && !readOnly.value) {
    try {
      basicFormApi.setFieldValue(
        'current_payment_amount',
        totalPaymentAmount.value,
      );
    } catch (error) {
      console.warn('设置付款金额失败:', error);
    }
  }
}

// 监听付款明细变化，自动更新表单中的付款金额
watch(
  totalPaymentAmount,
  () => {
    updatePaymentAmount();
  },
  { immediate: false },
); // 不立即执行，避免初始化时的问题

// 本次支付后累计付款金额
const cumulativePaidAfterPayment = computed(() => {
  return contractPaidAmount.value + currentPaymentAmount.value;
});

// 本次支付后合同结余未支付金额
const remainingUnpaidAfterPayment = computed(() => {
  const contractTotal = Number(contractData.value?.total_amount || 0);
  return contractTotal - cumulativePaidAfterPayment.value;
});

// 付款进度百分比
const paymentProgress = computed(() => {
  const contractTotal = Number(contractData.value?.total_amount || 0);
  if (contractTotal === 0) return 0;
  return (cumulativePaidAfterPayment.value / contractTotal) * 100;
});

/**
 * 检查合同明细是否已选择
 */
function isContractItemSelected(contractItemId: number | undefined): boolean {
  if (!contractItemId) return false;
  return paymentItems.value.some(
    (item) => item.contract_item_id === contractItemId,
  );
}

/**
 * 切换合同明细选择状态
 */
async function toggleContractItem(
  contractItem: PurchaseContractItem,
  event: Event,
) {
  const target = event.target as HTMLInputElement;
  const isChecked = target.checked;

  if (isChecked) {
    // 获取该明细项的已付信息
    const { paidQuantity, paidAmount } = await getContractItemPaidInfo(
      contractItem.id!,
    );

    // 计算未付数量和金额
    const unpaidQty = Math.max(
      0,
      (contractItem.contract_quantity || 0) - paidQuantity,
    );
    const unpaidAmt = unpaidQty * (contractItem.contract_price || 0);

    // 添加到付款明细
    const paymentItem: PaymentRequestItem = {
      id: undefined,
      payment_request_id: undefined,
      contract_item_id: contractItem.id!,
      contract_quantity: contractItem.contract_quantity,
      contract_amount: contractItem.contract_amount,
      paid_quantity: paidQuantity,
      paid_amount: paidAmount,
      unpaid_quantity: unpaidQty,
      unpaid_amount: unpaidAmt,
      current_payment_quantity: unpaidQty, // 默认本次付款数量为未付数量
      current_payment_amount: unpaidAmt, // 默认本次付款金额为未付金额
      remark: '',
      contract_item: contractItem,
    };
    paymentItems.value.push(paymentItem);
  } else {
    // 从付款明细中移除
    const index = paymentItems.value.findIndex(
      (item) => item.contract_item_id === contractItem.id,
    );
    if (index !== -1) {
      paymentItems.value.splice(index, 1);
    }
  }

  // 更新付款金额
  await nextTick();
  updatePaymentAmount();

  // 更新全选状态
  updateSelectAllStatus();
}

/**
 * 更新全选状态
 */
function updateSelectAllStatus() {
  if (!contractData.value?.items) {
    selectAllChecked.value = false;
    selectAllIndeterminate.value = false;
    return;
  }

  // 获取可选择的合同明细项
  const selectableItems = contractData.value.items.filter((_item: any) => {
    // 这里可以添加过滤逻辑
    return true; // 暂时允许选择所有项
  });

  const selectedCount = selectableItems.filter((item: any) =>
    isContractItemSelected(item.id),
  ).length;

  if (selectedCount === 0) {
    selectAllChecked.value = false;
    selectAllIndeterminate.value = false;
  } else if (selectedCount === selectableItems.length) {
    selectAllChecked.value = true;
    selectAllIndeterminate.value = false;
  } else {
    selectAllChecked.value = false;
    selectAllIndeterminate.value = true;
  }
}

/**
 * 处理全选/取消全选
 */
async function handleSelectAll(event: any) {
  const isChecked = event.target?.checked ?? event;

  if (!contractData.value?.items) return;

  // 获取可选择的合同明细项
  const selectableItems = contractData.value.items.filter((_item: any) => {
    // 这里可以添加过滤逻辑
    return true; // 暂时允许选择所有项
  });

  for (const item of selectableItems) {
    const isCurrentlySelected = isContractItemSelected(item.id);

    if (isChecked && !isCurrentlySelected) {
      // 需要选中但当前未选中
      await toggleContractItemDirect(item, true);
    } else if (!isChecked && isCurrentlySelected) {
      // 需要取消选中但当前已选中
      await toggleContractItemDirect(item, false);
    }
  }

  updateSelectAllStatus();
}

/**
 * 直接切换合同明细选择状态（不依赖事件）
 */
async function toggleContractItemDirect(contractItem: any, isChecked: boolean) {
  if (isChecked) {
    // 获取付款信息
    const paymentInfo = await getContractItemPaidInfo(contractItem.id);

    // 计算未付款数量和金额
    const unpaidQuantity =
      contractItem.contract_quantity - paymentInfo.paidQuantity;
    const unpaidAmount = contractItem.contract_amount - paymentInfo.paidAmount;

    // 检查是否已全部付款
    if (unpaidQuantity <= 0 || unpaidAmount <= 0) {
      message.warning(
        `该合同明细项"${contractItem.material_type || ''}${contractItem.model ? ` - ${contractItem.model}` : ''}"已全部付款，无需再次创建付款记录！`,
      );
      return false;
    }

    // 添加到付款明细
    const paymentItem: PaymentRequestItem = {
      id: undefined,
      payment_request_id: undefined,
      contract_item_id: contractItem.id!,
      contract_quantity: contractItem.contract_quantity,
      contract_amount: contractItem.contract_amount,
      paid_quantity: paymentInfo.paidQuantity,
      paid_amount: paymentInfo.paidAmount,
      unpaid_quantity: Math.max(0, unpaidQuantity),
      unpaid_amount: Math.max(0, unpaidAmount),
      current_payment_quantity: 0,
      current_payment_amount: 0,
      remark: undefined,
      contract_item: {
        id: contractItem.id || 0,
        material_type: contractItem.material_type,
        model: contractItem.model || '',
        brand: contractItem.brand || '',
        pn: contractItem.pn || '',
        spec: contractItem.spec || '',
        unit: contractItem.unit || '',
        contract_quantity: contractItem.contract_quantity,
        contract_price: contractItem.contract_price,
      },
    };
    paymentItems.value.push(paymentItem);
    return true;
  } else {
    // 从付款明细中移除
    const index = paymentItems.value.findIndex(
      (item) => item.contract_item_id === contractItem.id,
    );
    if (index !== -1) {
      paymentItems.value.splice(index, 1);
    }
    return true;
  }
}

/**
 * 确认选择合同明细
 */
function confirmContractItems() {
  showContractItemsModal.value = false;
}

async function openModal(data?: PaymentRequest, readonly = false) {
  resetForm();
  readOnly.value = readonly;

  if (data) {
    formData.value = data;
    await nextTick();
    basicFormApi.setValues(data);

    if (data.contract_id) {
      // 重置当前合同ID，强制重新加载数据
      currentContractId.value = null;
      await handleContractChange(data.contract_id);
    }

    // 设置付款明细数据
    if (data.items && data.items.length > 0) {
      paymentItems.value = data.items;
      // 更新付款金额
      await nextTick();
      updatePaymentAmount();
    }
  }

  modalApi.open();
}

async function handleSubmit() {
  const { valid } = await basicFormApi.validate();
  if (!valid) {
    modalApi.lock(false);
    message.error('请检查必填项');
    return;
  }

  const basicData = await basicFormApi.getValues();

  // 验证付款明细
  if (!paymentItems.value || paymentItems.value.length === 0) {
    modalApi.lock(false);
    message.error('请选择付款明细项');
    return;
  }

  // 验证付款明细中的必填项
  for (let i = 0; i < paymentItems.value.length; i++) {
    const item = paymentItems.value[i];
    if (!item) {
      modalApi.lock(false);
      message.error(`第${i + 1}项付款明细数据异常`);
      return;
    }
    if (!item.current_payment_quantity || item.current_payment_quantity <= 0) {
      modalApi.lock(false);
      message.error(`第${i + 1}项付款明细的付款数量不能为空或小于等于0`);
      return;
    }
    if (!item.current_payment_amount || item.current_payment_amount <= 0) {
      modalApi.lock(false);
      message.error(`第${i + 1}项付款明细的付款金额不能为空或小于等于0`);
      return;
    }
  }

  const data: CreatePaymentRequestParams = {
    contract_id: Number(basicData.contract_id),
    supplier_id: Number(
      basicData.supplier_id || contractData.value?.supplier_id,
    ),
    payment_type: basicData.payment_type,

    current_payment_amount: totalPaymentAmount.value,
    payment_reason: basicData.payment_reason || '',

    remark: basicData.remark || '',
    items: paymentItems.value.map((item) => ({
      contract_item_id: item.contract_item_id,

      current_payment_quantity: item.current_payment_quantity,
      current_payment_amount: item.current_payment_amount,
      remark: item.remark || '',
    })),
  };

  pendingData.value = data;
  confirmModalVisible.value = true;
  modalApi.lock(true);
}

async function handleConfirmSubmit() {
  if (!pendingData.value) return;

  confirmLoading.value = true;
  try {
    // 创建或更新付款申请
    const result = await (formData.value?.id
      ? updatePaymentRequest(formData.value.id, {
          id: formData.value.id,
          ...pendingData.value,
        })
      : createPaymentRequest(pendingData.value));

    // 上传验收单文件（如果有）
    if (acceptanceDocumentUploaderRef.value && result?.id) {
      try {
        await acceptanceDocumentUploaderRef.value.uploadFilesToServer(
          result.id,
        );
      } catch (error) {
        console.error('验收单文件上传失败:', error);
      }
    }

    // 上传预开发票文件（如果有）
    if (preInvoiceUploaderRef.value && result?.id) {
      try {
        await preInvoiceUploaderRef.value.uploadFilesToServer(result.id);
      } catch (error) {
        console.error('预开发票文件上传失败:', error);
      }
    }

    confirmModalVisible.value = false;
    modalApi.close();
    emit('success');
  } catch (error) {
    console.error('提交失败:', error);
  } finally {
    confirmLoading.value = false;
    modalApi.lock(false);
  }
}

function handleCancelConfirm() {
  confirmModalVisible.value = false;
  modalApi.lock(false);
}

// 获取付款类型文本
function getPaymentTypeText(paymentType?: string) {
  if (!paymentType) return '-';
  return PAYMENT_TYPE_CONFIG[paymentType]?.text || paymentType;
}

// 获取合同类型文本
function getContractTypeText(contractType?: string) {
  if (!contractType) return '-';
  return CONTRACT_TYPE_CONFIG[contractType]?.text || contractType;
}

// 监听modal显示状态，更新全选状态
watch(
  () => showContractItemsModal.value,
  (isVisible) => {
    if (isVisible) {
      updateSelectAllStatus();
    }
  },
);

// 监听付款明细变化，更新全选状态
watch(
  () => paymentItems.value.length,
  () => {
    if (showContractItemsModal.value) {
      updateSelectAllStatus();
    }
  },
);

defineExpose({
  openModal,
});
</script>

<template>
  <Modal :title="getTitle" width="1000px">
    <Tabs v-model:active-key="activeKey" class="mx-4">
      <Tabs.TabPane key="basic" tab="基本信息">
        <!-- 提示信息 -->
        <div
          v-if="!contractData && !readOnly"
          class="mx-4 mb-4 rounded-lg border border-yellow-200 bg-yellow-50 p-4"
        >
          <div class="flex items-center text-yellow-700">
            <span class="mr-2 text-lg">💡</span>
            <span class="font-medium"
              >请先选择关联合同，系统将自动显示合同信息、我司公司信息和供应商信息</span
            >
          </div>
        </div>

        <!-- 三个信息卡片横向排列 -->
        <div v-if="contractData" class="mx-4 mb-6">
          <div class="grid grid-cols-1 gap-4 lg:grid-cols-3">
            <!-- 关联合同信息 -->
            <div
              class="rounded-xl border border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100 p-4 shadow-sm"
            >
              <div class="mb-3 flex items-center">
                <div
                  class="mr-2 flex h-8 w-8 items-center justify-center rounded-full bg-blue-500 text-white"
                >
                  <span class="text-sm">📋</span>
                </div>
                <h3 class="text-base font-semibold text-blue-800">
                  关联合同信息
                </h3>
              </div>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="font-medium text-blue-700">合同编号:</span>
                  <span class="font-medium text-gray-800">{{
                    contractData.contract_no || '-'
                  }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-medium text-blue-700">供应商:</span>
                  <span class="ml-2 truncate text-gray-700">{{
                    contractData.supplier_name || '-'
                  }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-medium text-blue-700">合同金额:</span>
                  <span class="font-semibold text-gray-800"
                    >¥{{
                      Number(contractData.total_amount || 0).toLocaleString()
                    }}</span
                  >
                </div>
                <div class="flex justify-between">
                  <span class="font-medium text-blue-700">合同类型:</span>
                  <span class="text-gray-700">{{
                    getContractTypeText(contractData.contract_type)
                  }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-medium text-blue-700">签订日期:</span>
                  <span class="text-gray-700">{{
                    formatDate(contractData.signing_date) || '-'
                  }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-medium text-blue-700">付款条款:</span>
                  <span class="ml-2 truncate text-gray-700">{{
                    contractData.payment_terms || '-'
                  }}</span>
                </div>
              </div>
            </div>

            <!-- 我司公司信息 -->
            <div
              v-if="companyData"
              class="rounded-xl border border-green-200 bg-gradient-to-br from-green-50 to-emerald-100 p-4 shadow-sm"
            >
              <div class="mb-3 flex items-center">
                <div
                  class="mr-2 flex h-8 w-8 items-center justify-center rounded-full bg-green-500 text-white"
                >
                  <span class="text-sm">🏢</span>
                </div>
                <h3 class="text-base font-semibold text-green-800">
                  我司公司信息
                </h3>
              </div>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="font-medium text-green-700">公司名称:</span>
                  <span class="ml-2 truncate font-medium text-gray-800">{{
                    companyData.company_name || '-'
                  }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-medium text-green-700">注册地址:</span>
                  <span class="ml-2 truncate text-gray-700">{{
                    companyData.registered_address || '-'
                  }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-medium text-green-700">联系电话:</span>
                  <span class="text-gray-700">{{
                    companyData.contact_phone || '-'
                  }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-medium text-green-700">联系人:</span>
                  <span class="text-gray-700">{{
                    companyData.contact_person || '-'
                  }}</span>
                </div>
              </div>
            </div>

            <!-- 供应商信息 -->
            <div
              v-if="supplierData"
              class="rounded-xl border border-orange-200 bg-gradient-to-br from-orange-50 to-amber-100 p-4 shadow-sm"
            >
              <div class="mb-3 flex items-center">
                <div
                  class="mr-2 flex h-8 w-8 items-center justify-center rounded-full bg-orange-500 text-white"
                >
                  <span class="text-sm">🏭</span>
                </div>
                <h3 class="text-base font-semibold text-orange-800">
                  供应商信息
                </h3>
              </div>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="font-medium text-orange-700">供应商名称:</span>
                  <span class="ml-2 truncate font-medium text-gray-800">{{
                    supplierData.supplier_name || '-'
                  }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-medium text-orange-700">注册地址:</span>
                  <span class="ml-2 truncate text-gray-700">{{
                    supplierData.address || '-'
                  }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-medium text-orange-700">纳税人识别号:</span>
                  <span class="ml-2 truncate text-gray-700">{{
                    supplierData.tax_number || '-'
                  }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-medium text-orange-700">联系电话:</span>
                  <span class="text-gray-700">{{
                    supplierData.contact_phone || '-'
                  }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-medium text-orange-700">开户银行:</span>
                  <span class="ml-2 truncate text-gray-700">{{
                    supplierData.bank_name || '-'
                  }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-medium text-orange-700">银行账户:</span>
                  <span class="ml-2 truncate text-gray-700">{{
                    supplierData.bank_account || '-'
                  }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <BasicForm
          :disabled="readOnly"
          class="mx-4 mb-4"
          @change="handleFormFieldChange"
        />

        <!-- 付款金额统计信息 -->
        <div
          v-if="contractData"
          class="mx-4 mb-6 rounded-lg border border-indigo-200 bg-indigo-50 p-4 shadow-sm"
        >
          <div class="mb-3 flex items-center">
            <div class="mr-2 h-5 w-1 rounded bg-indigo-500"></div>
            <div class="text-base font-medium text-indigo-700">
              付款金额统计
            </div>
          </div>
          <div
            class="grid grid-cols-1 gap-x-6 gap-y-3 text-sm md:grid-cols-2 lg:grid-cols-4"
          >
            <div class="rounded-lg bg-white p-3 shadow-sm">
              <div class="mb-1 text-xs font-medium text-gray-500">
                合同总金额
              </div>
              <div class="text-lg font-bold text-gray-900">
                ¥{{ Number(contractData.total_amount || 0).toLocaleString() }}
              </div>
            </div>
            <div class="rounded-lg bg-white p-3 shadow-sm">
              <div class="mb-1 text-xs font-medium text-gray-500">
                已付款金额
              </div>
              <div class="text-lg font-bold text-green-600">
                ¥{{ contractPaidAmount.toLocaleString() }}
              </div>
            </div>
            <div class="rounded-lg bg-white p-3 shadow-sm">
              <div class="mb-1 text-xs font-medium text-gray-500">
                本次支付后累计付款金额
              </div>
              <div class="text-lg font-bold text-blue-600">
                ¥{{ cumulativePaidAfterPayment.toLocaleString() }}
              </div>
            </div>
            <div class="rounded-lg bg-white p-3 shadow-sm">
              <div class="mb-1 text-xs font-medium text-gray-500">
                本次支付后合同结余未支付金额
              </div>
              <div class="text-lg font-bold text-orange-600">
                ¥{{ remainingUnpaidAfterPayment.toLocaleString() }}
              </div>
            </div>
          </div>

          <!-- 进度条 -->
          <div class="mt-4">
            <div class="mb-1 flex justify-between text-xs text-gray-600">
              <span>付款进度</span>
              <span>{{ paymentProgress.toFixed(1) }}%</span>
            </div>
            <div class="h-2 w-full rounded-full bg-gray-200">
              <div
                class="h-2 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 transition-all duration-300"
                :style="{ width: `${Math.min(paymentProgress, 100)}%` }"
              ></div>
            </div>
          </div>
        </div>
      </Tabs.TabPane>

      <Tabs.TabPane key="items" tab="付款明细">
        <div class="mx-4">
          <!-- 合同明细选择提示 -->
          <div
            v-if="!contractData"
            class="mb-4 rounded-lg border border-yellow-200 bg-yellow-50 p-4"
          >
            <div class="flex items-center text-yellow-700">
              <span class="mr-2 text-lg">💡</span>
              <span class="font-medium"
                >请先在基本信息中选择关联合同，然后在此处选择需要付款的合同明细项</span
              >
            </div>
          </div>

          <!-- 付款明细表格 -->
          <div v-if="contractData" class="space-y-4">
            <!-- 操作按钮 -->
            <div class="flex items-center justify-between">
              <div class="text-lg font-medium text-gray-700">付款明细项</div>
              <Button
                type="primary"
                @click="showContractItemsModal = true"
                :disabled="readOnly"
              >
                选择合同明细
              </Button>
            </div>

            <!-- 明细表格 -->
            <div class="overflow-x-auto">
              <table class="w-full rounded-lg border border-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th
                      class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
                    >
                      物料类型
                    </th>
                    <th
                      class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
                    >
                      PN
                    </th>
                    <th
                      class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
                    >
                      型号/品牌
                    </th>
                    <th
                      class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
                    >
                      规格
                    </th>
                    <th
                      class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
                    >
                      单位
                    </th>
                    <th
                      class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
                    >
                      合同数量
                    </th>
                    <th
                      class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
                    >
                      合同单价
                    </th>
                    <th
                      class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
                    >
                      已付数量
                    </th>
                    <th
                      class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
                    >
                      已付款金额
                    </th>
                    <th
                      class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
                    >
                      未付数量
                    </th>
                    <th
                      class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
                    >
                      未付款金额
                    </th>
                    <th
                      class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
                    >
                      本次付款数量
                    </th>
                    <th
                      class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
                    >
                      本次付款金额
                    </th>
                    <th
                      class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
                    >
                      备注
                    </th>
                    <th
                      class="border-b px-4 py-3 text-center text-sm font-medium text-gray-700"
                    >
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="(item, index) in paymentItems"
                    :key="item.contract_item_id || index"
                    class="hover:bg-gray-50"
                  >
                    <td class="border-b px-4 py-3 text-sm text-gray-700">
                      {{ item.contract_item?.material_type || '-' }}
                    </td>
                    <td class="border-b px-4 py-3 text-sm text-gray-700">
                      <span class="font-mono text-sm text-blue-600">
                        {{ item.contract_item?.pn || '-' }}
                      </span>
                    </td>
                    <td class="border-b px-4 py-3 text-sm text-gray-700">
                      <div>{{ item.contract_item?.model || '-' }}</div>
                      <div class="text-xs text-gray-500">
                        {{ item.contract_item?.brand || '-' }}
                      </div>
                    </td>
                    <td class="border-b px-4 py-3 text-sm text-gray-700">
                      {{ item.contract_item?.spec || '-' }}
                    </td>
                    <td class="border-b px-4 py-3 text-sm text-gray-700">
                      {{ item.contract_item?.unit || '-' }}
                    </td>
                    <td class="border-b px-4 py-3 text-sm text-gray-700">
                      {{ item.contract_item?.contract_quantity || 0 }}
                    </td>
                    <td class="border-b px-4 py-3 text-sm text-gray-700">
                      ¥{{
                        Number(
                          item.contract_item?.contract_price || 0,
                        ).toLocaleString()
                      }}
                    </td>
                    <td class="border-b px-4 py-3 text-sm text-gray-700">
                      {{ item.paid_quantity || 0 }}
                    </td>
                    <td class="border-b px-4 py-3 text-sm text-gray-700">
                      <span class="font-medium text-green-600">
                        ¥{{ Number(item.paid_amount || 0).toLocaleString() }}
                      </span>
                    </td>
                    <td class="border-b px-4 py-3 text-sm text-gray-700">
                      {{ unpaidQuantity(item) }}
                    </td>
                    <td class="border-b px-4 py-3 text-sm text-gray-700">
                      <span class="font-medium text-orange-600">
                        ¥{{ unpaidAmount(item).toLocaleString() }}
                      </span>
                    </td>
                    <td class="border-b px-4 py-3">
                      <InputNumber
                        v-model:value="item.current_payment_quantity"
                        :min="0"
                        :max="unpaidQuantity(item)"
                        :disabled="readOnly"
                        class="w-20"
                        @change="updateItemAmount(index)"
                      />
                    </td>
                    <td class="border-b px-4 py-3">
                      <InputNumber
                        v-model:value="item.current_payment_amount"
                        :min="0"
                        :disabled="readOnly"
                        class="w-24"
                        :formatter="
                          (value) =>
                            `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                        "
                        :parser="(value) => value.replace(/¥\s?|(,*)/g, '')"
                      />
                    </td>
                    <td class="border-b px-4 py-3">
                      <Input
                        v-model:value="item.remark"
                        :disabled="readOnly"
                        placeholder="备注"
                        class="w-32"
                      />
                    </td>
                    <td class="border-b px-4 py-3 text-center">
                      <Button
                        type="link"
                        danger
                        size="small"
                        @click="removePaymentItem(index)"
                        :disabled="readOnly"
                      >
                        删除
                      </Button>
                    </td>
                  </tr>
                  <tr v-if="paymentItems.length === 0">
                    <td
                      colspan="12"
                      class="px-4 py-8 text-center text-gray-500"
                    >
                      暂无付款明细，请点击"选择合同明细"添加
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- 合计信息 -->
            <div class="rounded-lg bg-gray-50 p-4">
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div class="flex justify-between">
                  <span class="font-medium text-gray-700">本次付款总数量:</span>
                  <span class="font-medium text-blue-600">{{
                    totalPaymentQuantity
                  }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-medium text-gray-700">本次付款总金额:</span>
                  <span class="font-medium text-blue-600"
                    >¥{{ totalPaymentAmount.toLocaleString() }}</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </Tabs.TabPane>

      <Tabs.TabPane key="attachments" tab="相关附件">
        <div class="mx-4 space-y-6">
          <!-- 验收单上传 -->
          <div>
            <div class="mb-3 text-base font-medium text-gray-800">验收单</div>
            <div class="rounded-lg border border-gray-200 bg-gray-50 p-4">
              <FileUploader
                ref="acceptanceDocumentUploaderRef"
                file-description="验收单"
                module-type="payment_requests"
                :module-id="formData?.id"
                :disabled="readOnly"
                show-upload-list
                :max-count="5"
                multiple
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.xlsx,.xls,.csv"
                list-type="text"
                compact
              >
                <Button v-if="!readOnly" type="primary" size="small">
                  <span class="mr-1">📄</span>
                  选择验收单文件
                </Button>
              </FileUploader>
              <div class="mt-2 text-xs text-gray-500">
                支持格式：PDF、Word文档、Excel表格、CSV文件、图片文件，最多上传5个文件
              </div>
            </div>
          </div>

          <!-- 预开发票上传 -->
          <div>
            <div class="mb-3 text-base font-medium text-gray-800">预开发票</div>
            <div class="rounded-lg border border-gray-200 bg-gray-50 p-4">
              <FileUploader
                ref="preInvoiceUploaderRef"
                file-description="预开发票"
                module-type="payment_requests"
                :module-id="formData?.id"
                :disabled="readOnly"
                show-upload-list
                :max-count="5"
                multiple
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.xlsx,.xls,.csv"
                list-type="text"
                compact
              >
                <Button v-if="!readOnly" type="primary" size="small">
                  <span class="mr-1">🧾</span>
                  选择预开发票文件
                </Button>
              </FileUploader>
              <div class="mt-2 text-xs text-gray-500">
                支持格式：PDF、Word文档、Excel表格、CSV文件、图片文件，最多上传5个文件
              </div>
            </div>
          </div>

          <!-- 上传说明 -->
          <div class="rounded-lg border border-blue-100 bg-blue-50 p-4">
            <div class="flex items-start">
              <span class="mr-2 text-blue-500">💡</span>
              <div class="text-sm text-blue-700">
                <div class="mb-1 font-medium">上传说明：</div>
                <ul class="list-inside list-disc space-y-1 text-xs">
                  <li>验收单：用于证明货物或服务已按合同要求完成验收</li>
                  <li>预开发票：供应商提供的预开具发票，用于付款申请审批</li>
                  <li>文件将在付款申请提交时一并上传到系统</li>
                  <li>上传的文件可在付款申请详情页查看和下载</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </Tabs.TabPane>
    </Tabs>

    <template #footer>
      <div class="flex justify-end space-x-2">
        <Button @click="modalApi.close()">取消</Button>
        <Button v-if="!readOnly" type="primary" @click="handleSubmit">
          提交
        </Button>
      </div>
    </template>
  </Modal>

  <!-- 确认提交弹窗 -->
  <AModal
    v-model:open="confirmModalVisible"
    title="确认提交付款申请"
    width="700px"
    :confirm-loading="confirmLoading"
    @ok="handleConfirmSubmit"
    @cancel="handleCancelConfirm"
  >
    <div class="space-y-4">
      <!-- 基本信息 -->
      <div
        class="rounded-lg border border-blue-200 bg-gradient-to-r from-blue-50 to-blue-100 p-4"
      >
        <div class="mb-3 flex items-center">
          <div
            class="mr-2 flex h-6 w-6 items-center justify-center rounded-full bg-blue-500 text-xs text-white"
          >
            💰
          </div>
          <h3 class="text-base font-semibold text-blue-800">付款申请信息</h3>
        </div>
        <div class="grid grid-cols-2 gap-3 text-sm">
          <div class="flex justify-between">
            <span class="font-medium text-blue-700">付款类型:</span>
            <span class="font-medium text-gray-800">{{
              getPaymentTypeText(pendingData?.payment_type)
            }}</span>
          </div>
          <div class="flex justify-between">
            <span class="font-medium text-blue-700">申请金额:</span>
            <span class="text-base font-bold text-red-600"
              >¥{{
                Number(
                  pendingData?.current_payment_amount || 0,
                ).toLocaleString()
              }}</span
            >
          </div>
          <div class="flex justify-between">
            <span class="font-medium text-blue-700">付款事由:</span>
            <span class="ml-2 truncate text-gray-800">{{
              pendingData?.payment_reason || '-'
            }}</span>
          </div>
          <div class="flex justify-between">
            <span class="font-medium text-blue-700">付款明细数量:</span>
            <span class="text-gray-800"
              >{{ pendingData?.items?.length || 0 }}项</span
            >
          </div>
        </div>
      </div>

      <!-- 合同信息 -->
      <div
        v-if="contractData"
        class="rounded-lg border border-green-200 bg-gradient-to-r from-green-50 to-green-100 p-4"
      >
        <div class="mb-3 flex items-center">
          <div
            class="mr-2 flex h-6 w-6 items-center justify-center rounded-full bg-green-500 text-xs text-white"
          >
            📋
          </div>
          <h3 class="text-base font-semibold text-green-800">关联合同信息</h3>
        </div>
        <div class="grid grid-cols-2 gap-3 text-sm">
          <div class="flex justify-between">
            <span class="font-medium text-green-700">合同编号:</span>
            <span class="font-medium text-gray-800">{{
              contractData.contract_no
            }}</span>
          </div>
          <div class="flex justify-between">
            <span class="font-medium text-green-700">供应商:</span>
            <span class="text-gray-800">{{ contractData.supplier_name }}</span>
          </div>
          <div class="flex justify-between">
            <span class="font-medium text-green-700">合同总金额:</span>
            <span class="font-medium text-gray-800"
              >¥{{
                Number(contractData.total_amount || 0).toLocaleString()
              }}</span
            >
          </div>
          <div class="flex justify-between">
            <span class="font-medium text-green-700">已付款金额:</span>
            <span class="text-gray-800"
              >¥{{ contractPaidAmount.toLocaleString() }}</span
            >
          </div>
        </div>
      </div>

      <!-- 备注信息 -->
      <div
        v-if="pendingData?.remark"
        class="rounded-lg border border-orange-200 bg-gradient-to-r from-orange-50 to-orange-100 p-4"
      >
        <div class="mb-2 flex items-center">
          <div
            class="mr-2 flex h-6 w-6 items-center justify-center rounded-full bg-orange-500 text-xs text-white"
          >
            📝
          </div>
          <h3 class="text-base font-semibold text-orange-800">备注信息</h3>
        </div>
        <div class="rounded border bg-white p-2 text-sm text-gray-700">
          {{ pendingData.remark }}
        </div>
      </div>

      <!-- 提示信息 -->
      <div
        class="rounded-lg border border-yellow-200 bg-gradient-to-r from-yellow-50 to-yellow-100 p-4"
      >
        <div class="flex items-start">
          <div
            class="mr-2 flex h-6 w-6 items-center justify-center rounded-full bg-yellow-500 text-xs text-white"
          >
            ⚠️
          </div>
          <div class="flex-1">
            <h3 class="mb-2 text-base font-semibold text-yellow-800">
              提交确认
            </h3>
            <div class="space-y-1 text-sm text-yellow-700">
              <p>• 提交后将进入审批流程，无法直接修改</p>
              <p>• 请确认付款金额、日期等信息准确无误</p>
              <p>• 如有附件需要上传，请确保已完成上传</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AModal>

  <!-- 合同明细选择模态框 -->
  <AModal
    v-model:open="showContractItemsModal"
    title="选择合同明细"
    width="1400px"
    :footer="null"
  >
    <div v-if="contractData?.items" class="space-y-4">
      <div class="mb-4 text-sm text-gray-600">
        请选择需要付款的合同明细项，系统将自动计算已付款和未付款数量。可使用表头的"全选"按钮快速选择所有项目。
      </div>

      <div class="overflow-x-auto">
        <table class="w-full rounded-lg border border-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                <div class="flex items-center space-x-2">
                  <Checkbox
                    :checked="selectAllChecked"
                    :indeterminate="selectAllIndeterminate"
                    @change="handleSelectAll"
                  />
                  <span>全选</span>
                </div>
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                物料类型
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                品牌
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                规格
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                型号
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                原厂PN
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                单位
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                合同数量
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                合同单价
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                合同金额
              </th>
              <th
                class="border-b px-4 py-3 text-left text-sm font-medium text-gray-700"
              >
                付款状态
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="item in contractData.items"
              :key="item.id"
              class="hover:bg-gray-50"
            >
              <td class="border-b px-4 py-3">
                <input
                  type="checkbox"
                  :checked="isContractItemSelected(item.id)"
                  @change="toggleContractItem(item, $event)"
                  class="rounded border-gray-300"
                />
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                {{ item.material_type || '-' }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                {{ item.brand || '-' }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                {{ item.spec || '-' }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                {{ item.model || '-' }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                <span class="font-mono text-sm text-blue-600">
                  {{ item.pn || '-' }}
                </span>
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                {{ item.unit || '-' }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                {{ item.contract_quantity || 0 }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                ¥{{ Number(item.contract_price || 0).toLocaleString() }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                ¥{{ Number(item.contract_amount || 0).toLocaleString() }}
              </td>
              <td class="border-b px-4 py-3 text-sm text-gray-700">
                <ContractItemPaymentStatus
                  :contract-item="item"
                  :contract-id="contractData?.id"
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="flex justify-end space-x-2 pt-4">
        <Button @click="showContractItemsModal = false"> 取消 </Button>
        <Button type="primary" @click="confirmContractItems"> 确定 </Button>
      </div>
    </div>

    <div v-else class="py-8 text-center text-gray-500">暂无合同明细数据</div>
  </AModal>
</template>
