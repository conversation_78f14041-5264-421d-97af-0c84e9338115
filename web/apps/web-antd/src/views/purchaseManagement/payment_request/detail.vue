<script lang="ts" setup>
import type {
  PaymentApprovalParams,
  PaymentRequest,
  PaymentRequestRollbackParams,
} from '#/api/core/purchase/payment_request';
import type { ModuleFileListResult } from '#/api/core/upload';

import { computed, nextTick, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { AccessControl } from '@vben/access';
import { Page } from '@vben/common-ui';
import {
  ArrowLeft,
  Calendar,
  FileText,
  Tag as TagIcon,
  User,
} from '@vben/icons';

import {
  Button,
  Image,
  message,
  Modal,
  Select,
  Spin,
  Tag,
  Textarea,
  Timeline,
  TimelineItem,
} from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  approvePaymentRequest,
  getPaymentRequestById,
  getPaymentRequestHistory,
  rollbackPaymentRequest,
} from '#/api/core/purchase/payment_request';
import { getModuleFiles } from '#/api/core/upload';
import { handleDownloadFile, handlePreviewFile } from '#/utils/common/download';
import { formatToDateTime } from '#/utils/common/time';

import WorkflowChart from './components/WorkflowChart.vue';
import {
  ACTION_CONFIG,
  PAYMENT_STATUS_CONFIG,
  PAYMENT_TYPE_CONFIG,
} from './constants';

const route = useRoute();
const router = useRouter();

const loading = ref(false);
const workflowLoading = ref(false);
const paymentRequest = ref<PaymentRequest>();
const approvalHistory = ref<any[]>([]);
const approvalComments = ref('');
const isApprovalModalVisible = ref(false);
const currentAction = ref<'approve' | 'reject'>('approve');
// 附件相关变量
const attachmentsLoading = ref(false);
const attachments = ref<ModuleFileListResult[]>([]);
const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');

// 按description分组的附件
const groupedAttachments = computed(() => {
  const grouped: Record<string, ModuleFileListResult[]> = {};

  attachments.value.forEach((file) => {
    const key = file.description || '其他附件';
    if (!grouped[key]) {
      grouped[key] = [];
    }
    grouped[key].push(file);
  });

  return grouped;
});

// 兼容性变量（保留原有的变量名）
const fileList = ref<any[]>([]);
const acceptanceDocuments = ref<any[]>([]);
const preInvoices = ref<any[]>([]);

// 回退相关状态
const isRollbackModalVisible = ref(false);
const rollbackComments = ref('');
const rollbackTarget = ref('');
const availableRollbackTargets = ref<any[]>([]);

// 供应商详情相关状态
const isSupplierModalVisible = ref(false);
const supplierDetails = ref<any>(null);
const supplierLoading = ref(false);

const paymentId = computed(() => Number(route.params.id));

// 计算合计金额
const totalAmount = computed(() => {
  if (!paymentRequest.value?.items) return 0;
  return paymentRequest.value.items.reduce((sum, item) => {
    return sum + (item.current_payment_amount || 0);
  }, 0);
});

// 明细项表格配置
const [ItemGrid, itemGridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: [
      {
        field: 'contract_item.material_type',
        title: '物料类型',
        width: 120,
        cellRender: {
          name: 'VxeText',
          props: ({ row }: any) => ({
            content: row.contract_item?.material_type || '-',
          }),
        },
      },
      {
        field: 'contract_item.model',
        title: '型号',
        width: 120,
        cellRender: {
          name: 'VxeText',
          props: ({ row }: any) => ({
            content: row.contract_item?.model || '-',
          }),
        },
      },
      {
        field: 'contract_item.brand',
        title: '品牌',
        width: 100,
        cellRender: {
          name: 'VxeText',
          props: ({ row }: any) => ({
            content: row.contract_item?.brand || '-',
          }),
        },
      },
      {
        field: 'contract_item.pn',
        title: 'PN',
        width: 120,
        cellRender: {
          name: 'VxeText',
          props: ({ row }: any) => ({
            content: row.contract_item?.pn || '-',
          }),
        },
      },
      {
        field: 'contract_item.spec',
        title: '规格',
        width: 150,
        cellRender: {
          name: 'VxeText',
          props: ({ row }: any) => ({
            content: row.contract_item?.spec || '-',
          }),
        },
      },
      {
        field: 'contract_item.unit',
        title: '单位',
        width: 80,
        cellRender: {
          name: 'VxeText',
          props: ({ row }: any) => ({
            content: row.contract_item?.unit || '-',
          }),
        },
      },
      {
        field: 'contract_item.contract_quantity',
        title: '合同数量',
        width: 100,
        cellRender: {
          name: 'VxeNumber',
          props: ({ row }: any) => ({
            value: row.contract_item?.contract_quantity || 0,
            digits: 0,
          }),
        },
      },
      {
        field: 'contract_item.contract_price',
        title: '合同单价',
        width: 120,
        formatter: ({ cellValue }: any) => {
          if (cellValue === null || cellValue === '') return '';
          return `¥${Number(cellValue).toLocaleString()}`;
        },
      },
      {
        field: 'unpaid_quantity',
        title: '未付款数量',
        width: 120,
        cellRender: { name: 'VxeNumber', props: { digits: 0 } },
      },
      {
        field: 'unpaid_amount',
        title: '未付款金额',
        width: 140,
        formatter: ({ cellValue }: any) => {
          if (cellValue === null || cellValue === '') return '';
          return `¥${Number(cellValue).toLocaleString()}`;
        },
      },
      {
        field: 'current_payment_quantity',
        title: '本次付款数量',
        width: 120,
        cellRender: { name: 'VxeNumber', props: { digits: 0 } },
      },
      {
        field: 'current_payment_amount',
        title: '本次付款金额',
        width: 140,
        formatter: ({ cellValue }: any) => {
          if (cellValue === null || cellValue === '') return '';
          return `¥${Number(cellValue).toLocaleString()}`;
        },
      },
      { field: 'remark', title: '备注', minWidth: 150 },
    ],
    height: 'auto',
    pagerConfig: {
      enabled: false,
    },
    proxyConfig: {
      autoLoad: false,
      ajax: {
        query: async () => {
          const items = paymentRequest.value?.items || [];
          return {
            items,
            total: items.length,
          };
        },
      },
    },
    toolbarConfig: {
      enabled: false,
    },
  },
});

const canApprove = computed(() => {
  const status = paymentRequest.value?.status;

  // 不能审批的状态
  const nonApprovalStatuses = ['draft', 'completed', 'rejected', 'cancelled'];
  if (!status || nonApprovalStatuses.includes(status)) {
    return false;
  }

  // 判断状态（系统自动处理，不需要人工审批）
  const systemStatuses = [
    'company_entity_judgment_1',
    'company_entity_judgment_2',
  ];
  if (systemStatuses.includes(status)) {
    return false;
  }

  return true;
});

// 是否可以回退
const canRollback = computed(() => {
  const status = paymentRequest.value?.status;

  // 不能回退的状态
  const nonRollbackStatuses = ['draft', 'completed', 'rejected', 'cancelled'];
  if (!status || nonRollbackStatuses.includes(status)) {
    return false;
  }

  // 采购负责人阶段不允许回退（第一个审批阶段）
  if (status === 'purchase_review') {
    return false;
  }

  // 系统自动处理的状态不能回退
  const systemStatuses = [
    'company_entity_judgment_1',
    'company_entity_judgment_2',
  ];
  if (systemStatuses.includes(status)) {
    return false;
  }

  return true;
});

// 获取当前阶段的权限码
const getPermissionCodes = computed(() => {
  const status = paymentRequest.value?.status;

  switch (status) {
    case 'cyber_payment_handler': {
      return {
        approve: ['Purchase:PaymentRequest:CyberPaymentHandler'],
        reject: ['Purchase:PaymentRequest:CyberPaymentHandler'],
        rollback: ['Purchase:PaymentRequest:CyberPaymentHandler'],
      };
    }
    case 'cyber_payment_review': {
      return {
        approve: ['Purchase:PaymentRequest:CyberPaymentReview'],
        reject: ['Purchase:PaymentRequest:CyberPaymentReview'],
        rollback: ['Purchase:PaymentRequest:CyberPaymentReview'],
      };
    }
    case 'cyber_review_2': {
      return {
        approve: ['Purchase:PaymentRequest:CyberReview2'],
        reject: ['Purchase:PaymentRequest:CyberReview2'],
        rollback: ['Purchase:PaymentRequest:CyberReview2'],
      };
    }
    case 'enterprise_manager_review': {
      return {
        approve: ['Purchase:PaymentRequest:EnterpriseManagerReview'],
        reject: ['Purchase:PaymentRequest:EnterpriseManagerReview'],
        rollback: ['Purchase:PaymentRequest:EnterpriseManagerReview'],
      };
    }
    case 'final_review': {
      return {
        approve: ['Purchase:PaymentRequest:FinalReview'],
        reject: ['Purchase:PaymentRequest:FinalReview'],
        rollback: ['Purchase:PaymentRequest:FinalReview'],
      };
    }
    case 'finance_manager_review': {
      return {
        approve: ['Purchase:PaymentRequest:FinanceManagerReview'],
        reject: ['Purchase:PaymentRequest:FinanceManagerReview'],
        rollback: ['Purchase:PaymentRequest:FinanceManagerReview'],
      };
    }
    case 'fund_manager_review': {
      return {
        approve: ['Purchase:PaymentRequest:FundManagerReview'],
        reject: ['Purchase:PaymentRequest:FundManagerReview'],
        rollback: ['Purchase:PaymentRequest:FundManagerReview'],
      };
    }
    case 'other_entity_payment_handler': {
      return {
        approve: ['Purchase:PaymentRequest:OtherEntityPaymentHandler'],
        reject: ['Purchase:PaymentRequest:OtherEntityPaymentHandler'],
        rollback: ['Purchase:PaymentRequest:OtherEntityPaymentHandler'],
      };
    }
    case 'other_entity_payment_review': {
      return {
        approve: ['Purchase:PaymentRequest:OtherEntityPaymentReview'],
        reject: ['Purchase:PaymentRequest:OtherEntityPaymentReview'],
        rollback: ['Purchase:PaymentRequest:OtherEntityPaymentReview'],
      };
    }
    case 'purchase_review': {
      return {
        approve: ['Purchase:PaymentRequest:PurchaseManagerReview'],
        reject: ['Purchase:PaymentRequest:PurchaseManagerReview'],
        rollback: [], // 采购审批阶段不能回退
      };
    }
    default: {
      return {
        approve: [],
        reject: [],
        rollback: [],
      };
    }
  }
});

async function fetchPaymentRequestDetail() {
  loading.value = true;
  try {
    const data = await getPaymentRequestById(paymentId.value);
    paymentRequest.value = data;

    // 刷新明细项表格数据
    if (data?.items && data.items.length > 0) {
      await nextTick();
      itemGridApi.query();
    }
  } catch (error) {
    console.error('获取付款申请详情失败:', error);
  } finally {
    loading.value = false;
  }
}

async function fetchApprovalHistory() {
  try {
    workflowLoading.value = true;
    const response = await getPaymentRequestHistory(paymentId.value);
    approvalHistory.value = Array.isArray(response) ? response : [];
  } catch (error) {
    console.error('获取审批历史失败:', error);
    message.error('获取审批历史失败');
    approvalHistory.value = [];
  } finally {
    workflowLoading.value = false;
  }
}

// 获取附件列表
async function fetchAttachments() {
  if (!paymentId.value) return;

  try {
    attachmentsLoading.value = true;
    const files = await getModuleFiles('payment_requests', paymentId.value);
    attachments.value = Array.isArray(files) ? files : [];

    // 兼容性处理：将文件按描述分类
    const allFiles = files.map((file) => ({
      uid: file.id,
      name: file.original_name,
      status: 'done',
      url: file.file_path,
      size: file.file_size,
      type: file.file_type,
      description: file.description,
    }));

    // 分类文件
    acceptanceDocuments.value = allFiles.filter(
      (file) => file.description === '验收单',
    );
    preInvoices.value = allFiles.filter(
      (file) => file.description === '预开发票',
    );

    // 保持原有的 fileList 用于兼容性
    fileList.value = allFiles;
  } catch (error) {
    console.error('获取附件失败:', error);
    message.error('获取附件列表失败');
    attachments.value = [];
  } finally {
    attachmentsLoading.value = false;
  }
}

// 处理文件预览
function handlePreview(file: ModuleFileListResult) {
  // 使用通用预览函数
  handlePreviewFile(file, {
    onImagePreview: (imageUrl, title) => {
      previewImage.value = imageUrl;
      previewTitle.value = title;
      previewVisible.value = true;
    },
  });
}

// 处理文件下载
async function handleDownload(file: ModuleFileListResult) {
  // 使用通用下载函数
  await handleDownloadFile(file);
}

// 显示供应商详情
async function showSupplierDetails() {
  if (!paymentRequest.value?.supplier) {
    message.warning('供应商信息不完整');
    return;
  }

  try {
    supplierLoading.value = true;
    isSupplierModalVisible.value = true;

    // 使用API返回的供应商详细信息
    const supplier = paymentRequest.value.supplier;
    supplierDetails.value = {
      id: supplier.id,
      name: supplier.supplier_name,
      code: supplier.supplier_code,
      contact_person: supplier.contact_person || '未提供',
      contact_phone: supplier.contact_phone || '未提供',
      contact_email: supplier.contact_email || '未提供',
      address: supplier.address || '未提供',
      bank_name: supplier.bank_name || '未提供',
      bank_account: supplier.bank_account || '未提供',
      tax_number: supplier.tax_number || '未提供',
      business_license: supplier.business_license || '未提供',
      created_at: supplier.created_at
        ? new Date(supplier.created_at).toLocaleDateString()
        : '未知',
      status: supplier.status === 1 ? '正常' : '停用',
      short_name: supplier.short_name,
      credit_level: supplier.credit_level || 0,
      remark: supplier.remark || '无',
    };
  } catch (error) {
    console.error('获取供应商详情失败:', error);
    message.error('获取供应商详情失败');
  } finally {
    supplierLoading.value = false;
  }
}

function handleApproval(action: 'approve' | 'reject') {
  currentAction.value = action;
  approvalComments.value = '';
  isApprovalModalVisible.value = true;
}

async function confirmApproval() {
  if (!approvalComments.value.trim()) {
    message.error('请输入审批意见');
    return;
  }

  try {
    const params: PaymentApprovalParams = {
      payment_id: paymentId.value,
      action: currentAction.value,
      comments: approvalComments.value,
      stage: paymentRequest.value?.status || '',
    };

    await approvePaymentRequest(params);
    message.success(
      currentAction.value === 'approve' ? '审批通过' : '审批拒绝',
    );
    isApprovalModalVisible.value = false;

    setTimeout(async () => {
      await fetchPaymentRequestDetail();
      await fetchApprovalHistory();
    }, 500);
  } catch (error) {
    console.error('审批操作失败:', error);
  }
}

// 处理回退
function handleRollback() {
  rollbackComments.value = '';
  rollbackTarget.value = '';

  // 根据当前状态，确定可回退的目标环节（不包括草稿和完成状态）
  const statusOrder = [
    'purchase_review',
    'finance_manager_review',
    'enterprise_manager_review',
    'cyber_payment_review',
    'other_entity_payment_review',
    'fund_manager_review',
    'cyber_payment_handler',
    'other_entity_payment_handler',
    'cyber_review_2',
    'final_review',
  ];

  const currentStatus = paymentRequest.value?.status || '';
  const currentIndex = statusOrder.indexOf(currentStatus);

  // 重置可用目标
  availableRollbackTargets.value = [];
  rollbackTarget.value = '';

  // 找出当前状态之前的所有可回退状态（不包括草稿阶段）
  const rollbackOptions = [
    { label: '采购与供应链负责人审批', value: 'purchase_review' },
    { label: '财务负责人审批', value: 'finance_manager_review' },
    { label: '企业负责人审批', value: 'enterprise_manager_review' },
    { label: '赛博付款申请复核', value: 'cyber_payment_review' },
    { label: '其他主体付款申请复核', value: 'other_entity_payment_review' },
    { label: '资金负责人审批', value: 'fund_manager_review' },
    { label: '赛博付款经办', value: 'cyber_payment_handler' },
    { label: '其他主体付款经办', value: 'other_entity_payment_handler' },
    { label: '赛博复核2', value: 'cyber_review_2' },
    { label: '最终复核', value: 'final_review' },
  ];

  rollbackOptions.forEach((option) => {
    const optionIndex = statusOrder.indexOf(option.value);
    // 只有在当前阶段之前的阶段才能作为回退目标
    if (optionIndex !== -1 && optionIndex < currentIndex) {
      availableRollbackTargets.value.push(option);
    }
  });

  // 如果有可回退的目标，默认选择最近的一个（最后一个）
  if (availableRollbackTargets.value.length > 0) {
    rollbackTarget.value =
      availableRollbackTargets.value[availableRollbackTargets.value.length - 1]
        ?.value || '';
  }

  isRollbackModalVisible.value = true;
}

// 确认回退
async function confirmRollback() {
  if (!rollbackComments.value.trim()) {
    message.error('请输入回退原因');
    return;
  }

  if (!rollbackTarget.value && availableRollbackTargets.value.length > 0) {
    message.error('请选择回退目标环节');
    return;
  }

  try {
    const params: PaymentRequestRollbackParams = {
      payment_id: paymentId.value,
      rollback_to: rollbackTarget.value,
      comments: rollbackComments.value,
    };

    await rollbackPaymentRequest(params);
    message.success('回退成功');
    isRollbackModalVisible.value = false;

    // 添加延迟确保后端状态更新
    await new Promise((resolve) => setTimeout(resolve, 800));

    // 刷新数据
    let retries = 0;
    const maxRetries = 3;

    const refreshData = async () => {
      try {
        await Promise.all([
          fetchPaymentRequestDetail(),
          fetchApprovalHistory(),
        ]);
      } catch {
        if (retries < maxRetries) {
          retries++;
          await new Promise((resolve) => setTimeout(resolve, 500));
          await refreshData();
        } else {
          message.warning('数据刷新失败，请手动刷新页面');
        }
      }
    };

    await refreshData();
  } catch {
    message.error('回退失败');
  }
}

function goBack() {
  router.back();
}

onMounted(async () => {
  await Promise.all([
    fetchPaymentRequestDetail(),
    fetchApprovalHistory(),
    fetchAttachments(),
  ]);
});
</script>

<template>
  <div>
    <Page auto-content-height class="bg-gray-50/50">
      <div class="mx-auto max-w-full space-y-6 px-4 py-4">
        <!-- 头部操作区 -->
        <div
          class="relative overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm transition-shadow duration-300 hover:shadow-md"
        >
          <div
            class="absolute -right-20 -top-20 h-48 w-48 rounded-full bg-blue-500/5"
          ></div>
          <div
            class="absolute -right-10 -top-10 h-32 w-32 rounded-full bg-indigo-500/10"
          ></div>
          <div class="px-6 py-5">
            <div class="flex flex-wrap items-center justify-between gap-4">
              <div class="flex items-center space-x-6">
                <Button
                  type="primary"
                  size="large"
                  class="flex items-center justify-center rounded-xl !bg-gradient-to-r !from-blue-500 !to-indigo-600 !px-5 !py-3 shadow-sm transition-all duration-300 hover:scale-105 hover:!from-blue-600 hover:!to-indigo-700 hover:shadow-md"
                  @click="goBack"
                >
                  <div class="flex items-center justify-center">
                    <ArrowLeft class="mr-2 size-5 text-white" />
                    <span class="font-medium text-white">返回列表</span>
                  </div>
                </Button>
                <div class="border-l-2 border-indigo-100 pl-6">
                  <div class="flex items-center">
                    <h1
                      class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-2xl font-bold text-transparent"
                    >
                      付款申请详情
                    </h1>
                    <Tag
                      v-if="paymentRequest?.status"
                      :color="
                        PAYMENT_STATUS_CONFIG[paymentRequest.status as string]
                          ?.color
                      "
                      class="ml-3 !rounded-full !px-3 !py-0.5 !text-xs !font-medium shadow-sm"
                    >
                      {{
                        PAYMENT_STATUS_CONFIG[paymentRequest.status as string]
                          ?.text || paymentRequest.status
                      }}
                    </Tag>
                  </div>
                  <div
                    v-if="paymentRequest"
                    class="mt-2 flex items-center gap-4"
                  >
                    <div class="flex items-center">
                      <div class="mr-2 h-2 w-2 rounded-full bg-blue-500"></div>
                      <span class="text-sm font-medium text-gray-600"
                        >申请编号:
                      </span>
                      <span class="ml-1 text-sm font-bold text-gray-800">{{
                        paymentRequest.payment_no || '未生成'
                      }}</span>
                    </div>
                    <div
                      v-if="paymentRequest.current_payment_amount"
                      class="flex items-center"
                    >
                      <div
                        class="mr-2 h-2 w-2 rounded-full bg-emerald-500"
                      ></div>
                      <span class="text-sm font-medium text-gray-600"
                        >本次申请付款金额:
                      </span>
                      <span class="ml-1 text-sm font-bold text-red-600">
                        ¥{{
                          paymentRequest.current_payment_amount.toLocaleString()
                        }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="flex space-x-3">
                <AccessControl
                  v-if="canApprove"
                  :codes="getPermissionCodes.approve"
                  type="code"
                >
                  <Button
                    type="primary"
                    size="large"
                    class="!h-auto !rounded-xl !border-0 !bg-gradient-to-r !from-green-500 !to-emerald-600 !px-8 !py-2.5 !font-medium shadow-md transition-all hover:scale-105 hover:!from-green-600 hover:!to-emerald-700 hover:shadow-lg"
                    @click="handleApproval('approve')"
                  >
                    <span class="text-base text-white">审批通过</span>
                  </Button>
                </AccessControl>
                <AccessControl
                  v-if="canApprove"
                  :codes="getPermissionCodes.reject"
                  type="code"
                >
                  <Button
                    danger
                    size="large"
                    class="!h-auto !rounded-xl !border-0 !bg-gradient-to-r !from-red-500 !to-rose-600 !px-8 !py-2.5 !font-medium shadow-md transition-all hover:scale-105 hover:!from-red-600 hover:!to-rose-700 hover:shadow-lg"
                    @click="handleApproval('reject')"
                  >
                    <span class="text-base text-white">审批拒绝</span>
                  </Button>
                </AccessControl>
                <AccessControl
                  v-if="canRollback"
                  :codes="getPermissionCodes.rollback"
                  type="code"
                >
                  <Button
                    size="large"
                    class="!h-auto !rounded-xl !border-2 !border-orange-300 !bg-gradient-to-r !from-orange-50 !to-amber-50 !px-8 !py-2.5 !font-medium shadow-md transition-all hover:scale-105 hover:!border-orange-400 hover:!from-orange-100 hover:!to-amber-100 hover:shadow-lg"
                    @click="handleRollback"
                  >
                    <span class="text-base text-orange-600">流程回退</span>
                  </Button>
                </AccessControl>
              </div>
            </div>
          </div>
        </div>

        <Spin :spinning="loading">
          <!-- 基本信息和流程图并排显示 -->
          <div class="mb-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
            <!-- 基本信息 -->
            <div>
              <div
                class="overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm"
              >
                <div
                  class="border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4"
                >
                  <h2
                    class="flex items-center text-lg font-semibold text-gray-900"
                  >
                    <div class="mr-3 h-6 w-1 rounded-full bg-blue-500"></div>
                    基本信息
                  </h2>
                </div>
                <div class="p-6">
                  <!-- 基本信息卡片 -->
                  <div
                    v-if="paymentRequest"
                    class="grid grid-cols-1 gap-6 md:grid-cols-3"
                  >
                    <!-- 申请编号 -->
                    <div
                      class="group relative overflow-hidden rounded-xl border border-blue-200/50 bg-gradient-to-br from-blue-50 to-blue-100/30 p-5 shadow-sm transition-all duration-300 hover:border-blue-300/70 hover:shadow-md"
                    >
                      <div
                        class="absolute -right-6 -top-6 h-16 w-16 rounded-full bg-blue-500/10"
                      ></div>
                      <div
                        class="absolute -right-2 -top-2 h-8 w-8 rounded-full bg-blue-500/20"
                      ></div>
                      <div class="flex items-center gap-4">
                        <div
                          class="flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 p-3 shadow-md shadow-blue-200 transition-transform duration-300 group-hover:scale-110"
                        >
                          <TagIcon class="size-7 text-white" />
                        </div>
                        <div class="flex-1">
                          <div
                            class="mb-1.5 text-xs font-semibold uppercase tracking-wider text-blue-700 opacity-80"
                          >
                            申请编号
                          </div>
                          <div class="text-base font-bold text-blue-800">
                            {{ paymentRequest.payment_no || '未生成' }}
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 关联合同 -->
                    <div
                      class="group relative overflow-hidden rounded-xl border border-cyan-200/50 bg-gradient-to-br from-cyan-50 to-cyan-100/30 p-5 shadow-sm transition-all duration-300 hover:border-cyan-300/70 hover:shadow-md"
                    >
                      <div
                        class="absolute -right-6 -top-6 h-16 w-16 rounded-full bg-cyan-500/10"
                      ></div>
                      <div
                        class="absolute -right-2 -top-2 h-8 w-8 rounded-full bg-cyan-500/20"
                      ></div>
                      <div class="flex items-center gap-4">
                        <div
                          class="flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-cyan-500 to-cyan-600 p-3 shadow-md shadow-cyan-200 transition-transform duration-300 group-hover:scale-110"
                        >
                          <FileText class="size-7 text-white" />
                        </div>
                        <div class="flex-1">
                          <div
                            class="mb-1.5 text-xs font-semibold uppercase tracking-wider text-cyan-700 opacity-80"
                          >
                            关联合同
                          </div>
                          <div
                            class="truncate text-base font-bold text-cyan-800"
                          >
                            {{ paymentRequest.contract?.contract_no || '-' }}
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 付款类型 -->
                    <div
                      class="group relative overflow-hidden rounded-xl border border-purple-200/50 bg-gradient-to-br from-purple-50 to-purple-100/30 p-5 shadow-sm transition-all duration-300 hover:border-purple-300/70 hover:shadow-md"
                    >
                      <div
                        class="absolute -right-6 -top-6 h-16 w-16 rounded-full bg-purple-500/10"
                      ></div>
                      <div
                        class="absolute -right-2 -top-2 h-8 w-8 rounded-full bg-purple-500/20"
                      ></div>
                      <div class="flex items-center gap-4">
                        <div
                          class="flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 p-3 shadow-md shadow-purple-200 transition-transform duration-300 group-hover:scale-110"
                        >
                          <span class="text-xl font-bold text-white">💳</span>
                        </div>
                        <div class="flex-1">
                          <div
                            class="mb-1.5 text-xs font-semibold uppercase tracking-wider text-purple-700 opacity-80"
                          >
                            付款类型
                          </div>
                          <Tag
                            :color="
                              PAYMENT_TYPE_CONFIG[
                                paymentRequest.payment_type as string
                              ]?.color
                            "
                            class="!rounded-md !px-3 !py-1 !text-sm !font-semibold shadow-sm"
                          >
                            {{
                              PAYMENT_TYPE_CONFIG[
                                paymentRequest.payment_type as string
                              ]?.text || paymentRequest.payment_type
                            }}
                          </Tag>
                        </div>
                      </div>
                    </div>

                    <!-- 我司公司 -->
                    <div
                      class="group relative overflow-hidden rounded-xl border border-slate-200/50 bg-gradient-to-br from-slate-50 to-slate-100/30 p-5 shadow-sm transition-all duration-300 hover:border-slate-300/70 hover:shadow-md"
                    >
                      <div
                        class="absolute -right-6 -top-6 h-16 w-16 rounded-full bg-slate-500/10"
                      ></div>
                      <div
                        class="absolute -right-2 -top-2 h-8 w-8 rounded-full bg-slate-500/20"
                      ></div>
                      <div class="flex items-center gap-4">
                        <div
                          class="flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-slate-500 to-slate-600 p-3 shadow-md shadow-slate-200 transition-transform duration-300 group-hover:scale-110"
                        >
                          <span class="text-xl font-bold text-white">🏢</span>
                        </div>
                        <div class="flex-1">
                          <div
                            class="mb-1.5 text-xs font-semibold uppercase tracking-wider text-slate-700 opacity-80"
                          >
                            我司公司
                          </div>
                          <div class="text-base font-bold text-slate-800">
                            {{
                              paymentRequest.company?.company_name || '未指定'
                            }}
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 供应商 -->
                    <div
                      class="group relative cursor-pointer overflow-hidden rounded-xl border border-green-200/50 bg-gradient-to-br from-green-50 to-green-100/30 p-5 shadow-sm transition-all duration-300 hover:scale-[1.02] hover:border-green-300/70 hover:shadow-md"
                      @click="showSupplierDetails"
                    >
                      <div
                        class="absolute -right-6 -top-6 h-16 w-16 rounded-full bg-green-500/10"
                      ></div>
                      <div
                        class="absolute -right-2 -top-2 h-8 w-8 rounded-full bg-green-500/20"
                      ></div>
                      <div class="flex items-center gap-4">
                        <div
                          class="flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-green-500 to-green-600 p-3 shadow-md shadow-green-200 transition-transform duration-300 group-hover:scale-110"
                        >
                          <User class="size-7 text-white" />
                        </div>
                        <div class="flex-1">
                          <div
                            class="mb-1.5 text-xs font-semibold uppercase tracking-wider text-green-700 opacity-80"
                          >
                            供应商 (点击查看详情)
                          </div>
                          <div class="text-base font-bold text-green-800">
                            {{
                              paymentRequest.supplier?.supplier_name || '未指定'
                            }}
                          </div>
                        </div>
                        <div
                          class="text-green-600 opacity-60 transition-opacity group-hover:opacity-100"
                        >
                          <span class="text-sm">👁️</span>
                        </div>
                      </div>
                    </div>

                    <!-- 本次申请付款金额 -->
                    <div
                      class="group relative overflow-hidden rounded-xl border border-orange-200/50 bg-gradient-to-br from-orange-50 to-orange-100/30 p-5 shadow-sm transition-all duration-300 hover:border-orange-300/70 hover:shadow-md"
                    >
                      <div
                        class="absolute -right-6 -top-6 h-16 w-16 rounded-full bg-orange-500/10"
                      ></div>
                      <div
                        class="absolute -right-2 -top-2 h-8 w-8 rounded-full bg-orange-500/20"
                      ></div>
                      <div class="flex items-center gap-4">
                        <div
                          class="flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-orange-500 to-orange-600 p-3 shadow-md shadow-orange-200 transition-transform duration-300 group-hover:scale-110"
                        >
                          <span class="text-xl font-bold text-white">💰</span>
                        </div>
                        <div class="flex-1">
                          <div
                            class="mb-1.5 text-xs font-semibold uppercase tracking-wider text-orange-700 opacity-80"
                          >
                            本次申请付款金额
                          </div>
                          <div class="text-lg font-bold text-orange-800">
                            ¥{{
                              paymentRequest.current_payment_amount?.toLocaleString() ||
                              '0'
                            }}
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 申请人 -->
                    <div
                      class="group relative overflow-hidden rounded-xl border border-indigo-200/50 bg-gradient-to-br from-indigo-50 to-indigo-100/30 p-5 shadow-sm transition-all duration-300 hover:border-indigo-300/70 hover:shadow-md"
                    >
                      <div
                        class="absolute -right-6 -top-6 h-16 w-16 rounded-full bg-indigo-500/10"
                      ></div>
                      <div
                        class="absolute -right-2 -top-2 h-8 w-8 rounded-full bg-indigo-500/20"
                      ></div>
                      <div class="flex items-center gap-4">
                        <div
                          class="flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-indigo-500 to-indigo-600 p-3 shadow-md shadow-indigo-200 transition-transform duration-300 group-hover:scale-110"
                        >
                          <User class="size-7 text-white" />
                        </div>
                        <div class="flex-1">
                          <div
                            class="mb-1.5 text-xs font-semibold uppercase tracking-wider text-indigo-700 opacity-80"
                          >
                            申请人
                          </div>
                          <div
                            class="truncate text-base font-bold text-indigo-800"
                          >
                            {{ paymentRequest.creator_name || '未知' }}
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 创建时间 -->
                    <div
                      class="group relative overflow-hidden rounded-xl border border-emerald-200/50 bg-gradient-to-br from-emerald-50 to-emerald-100/30 p-5 shadow-sm transition-all duration-300 hover:border-emerald-300/70 hover:shadow-md"
                    >
                      <div
                        class="absolute -right-6 -top-6 h-16 w-16 rounded-full bg-emerald-500/10"
                      ></div>
                      <div
                        class="absolute -right-2 -top-2 h-8 w-8 rounded-full bg-emerald-500/20"
                      ></div>
                      <div class="flex items-center gap-4">
                        <div
                          class="flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-emerald-600 p-3 shadow-md shadow-emerald-200 transition-transform duration-300 group-hover:scale-110"
                        >
                          <Calendar class="size-7 text-white" />
                        </div>
                        <div class="flex-1">
                          <div
                            class="mb-1.5 text-xs font-semibold uppercase tracking-wider text-emerald-700 opacity-80"
                          >
                            创建时间
                          </div>
                          <div class="text-base font-bold text-emerald-800">
                            {{
                              paymentRequest.created_at
                                ? formatToDateTime(paymentRequest.created_at)
                                : '-'
                            }}
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 付款说明 -->
                    <div
                      v-if="paymentRequest.payment_reason"
                      class="group relative overflow-hidden rounded-xl border border-gray-200/50 bg-gradient-to-br from-gray-50 to-gray-100/30 p-5 shadow-sm transition-all duration-300 hover:border-gray-300/70 hover:shadow-md md:col-span-3"
                    >
                      <div
                        class="absolute -right-6 -top-6 h-16 w-16 rounded-full bg-gray-500/10"
                      ></div>
                      <div
                        class="absolute -right-2 -top-2 h-8 w-8 rounded-full bg-gray-500/20"
                      ></div>
                      <div class="flex items-start gap-4">
                        <div
                          class="flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-gray-500 to-gray-600 p-3 shadow-md shadow-gray-200 transition-transform duration-300 group-hover:scale-110"
                        >
                          <FileText class="size-7 text-white" />
                        </div>
                        <div class="flex-1">
                          <div
                            class="mb-1.5 text-xs font-semibold uppercase tracking-wider text-gray-700 opacity-80"
                          >
                            付款说明
                          </div>
                          <div class="text-base leading-relaxed text-gray-800">
                            {{ paymentRequest.payment_reason }}
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 备注 -->
                    <div
                      class="group relative overflow-hidden rounded-xl border border-rose-200/50 bg-gradient-to-br from-rose-50 to-rose-100/30 p-5 shadow-sm transition-all duration-300 hover:border-rose-300/70 hover:shadow-md md:col-span-3"
                    >
                      <div
                        class="absolute -right-6 -top-6 h-16 w-16 rounded-full bg-rose-500/10"
                      ></div>
                      <div
                        class="absolute -right-2 -top-2 h-8 w-8 rounded-full bg-rose-500/20"
                      ></div>
                      <div class="flex items-start gap-4">
                        <div
                          class="flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-rose-500 to-rose-600 p-3 shadow-md shadow-rose-200 transition-transform duration-300 group-hover:scale-110"
                        >
                          <span class="text-xl font-bold text-white">📝</span>
                        </div>
                        <div class="flex-1">
                          <div
                            class="mb-1.5 text-xs font-semibold uppercase tracking-wider text-rose-700 opacity-80"
                          >
                            备注
                          </div>
                          <div class="text-base leading-relaxed text-rose-800">
                            {{ paymentRequest.remark || '暂无备注' }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 流程图 -->
            <div>
              <div
                class="overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm"
              >
                <div
                  class="border-b border-gray-100 bg-gradient-to-r from-indigo-50 to-purple-50 px-6 py-4"
                >
                  <h2
                    class="flex items-center text-lg font-semibold text-gray-900"
                  >
                    <div class="mr-3 h-6 w-1 rounded-full bg-indigo-500"></div>
                    审批流程图
                  </h2>
                </div>
                <div class="p-6">
                  <WorkflowChart
                    :current-status="paymentRequest?.status || 'draft'"
                    :approval-history="approvalHistory"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 付款明细 -->
          <div
            v-if="paymentRequest?.items && paymentRequest.items.length > 0"
            class="overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm"
          >
            <div
              class="border-b border-gray-100 bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4"
            >
              <div class="flex items-center justify-between">
                <h2
                  class="flex items-center text-lg font-semibold text-gray-900"
                >
                  <div class="mr-3 h-6 w-1 rounded-full bg-green-500"></div>
                  付款明细 ({{ paymentRequest.items.length }}项)
                </h2>
                <div class="text-right">
                  <div class="text-sm text-gray-600">合计金额</div>
                  <div class="text-lg font-bold text-red-600">
                    ¥{{ totalAmount.toLocaleString() }}
                  </div>
                </div>
              </div>
            </div>
            <div class="p-6">
              <ItemGrid />
            </div>
          </div>

          <!-- 附件和操作历史记录并排显示 -->
          <div class="mb-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
            <!-- 相关附件 -->
            <div
              class="overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm"
            >
              <div
                class="border-b border-gray-100 bg-gradient-to-r from-orange-50 to-amber-50 px-6 py-4"
              >
                <h2
                  class="flex items-center text-lg font-semibold text-gray-900"
                >
                  <div class="mr-3 h-6 w-1 rounded-full bg-orange-500"></div>
                  相关附件
                </h2>
              </div>
              <div class="p-6">
                <div v-if="attachments.length === 0" class="py-12 text-center">
                  <div class="flex flex-col items-center space-y-3">
                    <div
                      class="flex h-16 w-16 items-center justify-center rounded-full bg-gray-100"
                    >
                      <FileText class="size-8 text-gray-400" />
                    </div>
                    <p class="font-medium text-gray-500">暂无附件</p>
                    <p class="text-sm text-gray-400">
                      该付款申请暂未上传任何附件
                    </p>
                  </div>
                </div>
                <div v-else class="space-y-6">
                  <!-- 按描述分组显示附件 -->
                  <div
                    v-for="(files, description) in groupedAttachments"
                    :key="description"
                    class="space-y-4"
                  >
                    <div class="flex items-center space-x-2">
                      <div class="h-4 w-1 rounded-full bg-orange-500"></div>
                      <h3 class="text-base font-semibold text-gray-900">
                        {{ description }}
                      </h3>
                      <span class="text-sm text-gray-500"
                        >({{ files.length }}个文件)</span
                      >
                    </div>
                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div
                        v-for="file in files"
                        :key="file.id"
                        class="group cursor-pointer rounded-xl border border-gray-200/60 bg-gradient-to-br from-gray-50 to-gray-100/80 p-3 transition-all duration-200 hover:border-orange-300/50 hover:shadow-md"
                      >
                        <div class="flex items-start space-x-3">
                          <div
                            class="flex h-10 w-10 items-center justify-center rounded-lg bg-orange-100 transition-colors group-hover:bg-orange-200"
                          >
                            <FileText class="size-5 text-orange-600" />
                          </div>
                          <div class="min-w-0 flex-1">
                            <div
                              class="mb-1 truncate text-sm font-semibold text-gray-900"
                            >
                              {{ file.file_name }}
                            </div>
                            <div class="mb-2 text-xs text-gray-500">
                              {{ (file.file_size / 1024).toFixed(1) }} KB
                            </div>
                            <div class="flex space-x-2">
                              <Button
                                size="small"
                                type="primary"
                                class="!rounded-md !px-3 !text-xs"
                                @click="handlePreview(file)"
                              >
                                预览
                              </Button>
                              <Button
                                size="small"
                                class="!rounded-md !px-3 !text-xs"
                                @click="handleDownload(file)"
                              >
                                下载
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 操作历史记录 -->
            <div
              class="overflow-hidden rounded-xl border border-gray-200/60 bg-white shadow-sm"
            >
              <div
                class="border-b border-gray-100 bg-gradient-to-r from-amber-50 to-yellow-50 px-6 py-4"
              >
                <h2
                  class="flex items-center text-lg font-semibold text-gray-900"
                >
                  <div class="mr-3 h-6 w-1 rounded-full bg-amber-500"></div>
                  操作历史记录
                </h2>
              </div>
              <div class="p-6">
                <Spin :spinning="workflowLoading">
                  <Timeline
                    v-if="approvalHistory.length > 0"
                    class="custom-timeline !pl-0"
                  >
                    <TimelineItem
                      v-for="(history, index) in approvalHistory"
                      :key="index"
                      :color="
                        ACTION_CONFIG[history.action as string]?.color || 'blue'
                      "
                    >
                      <template #dot>
                        <div
                          class="flex h-8 w-8 items-center justify-center rounded-full font-semibold text-white shadow-lg"
                          :class="{
                            'bg-gradient-to-br from-green-500 to-green-600':
                              history.action === 'approve',
                            'bg-gradient-to-br from-red-500 to-red-600':
                              history.action === 'reject',
                            'bg-gradient-to-br from-orange-500 to-amber-600':
                              history.action === 'rollback',
                            'bg-gradient-to-br from-blue-500 to-blue-600':
                              history.action === 'create' ||
                              history.action === 'submit',
                            'bg-gradient-to-br from-gray-500 to-gray-600':
                              history.action === 'cancel',
                          }"
                        >
                          <span v-if="history.action === 'approve'">✓</span>
                          <span v-else-if="history.action === 'reject'">✗</span>
                          <span v-else-if="history.action === 'rollback'"
                            >↺</span
                          >
                          <span
                            v-else-if="
                              history.action === 'create' ||
                              history.action === 'submit'
                            "
                            >+</span
                          >
                          <span v-else-if="history.action === 'cancel'">×</span>
                          <span v-else>•</span>
                        </div>
                      </template>

                      <div
                        class="rounded-lg border border-gray-100 bg-white p-4 shadow-sm transition-shadow duration-200 hover:shadow-md"
                      >
                        <div class="mb-3 flex items-center gap-3">
                          <div
                            class="flex h-8 w-8 items-center justify-center rounded-full text-white"
                            :class="{
                              'bg-gradient-to-br from-green-500 to-green-600':
                                history.action === 'approve',
                              'bg-gradient-to-br from-red-500 to-red-600':
                                history.action === 'reject',
                              'bg-gradient-to-br from-orange-500 to-amber-600':
                                history.action === 'rollback',
                              'bg-gradient-to-br from-blue-500 to-blue-600':
                                history.action === 'create' ||
                                history.action === 'submit',
                              'bg-gradient-to-br from-gray-500 to-gray-600':
                                history.action === 'cancel',
                            }"
                          >
                            {{ String(history.operator_name || 'U').charAt(0) }}
                          </div>
                          <div class="flex-1">
                            <div class="font-medium text-gray-900">
                              {{ history.operator_name || '系统' }}
                            </div>
                            <div class="text-sm text-gray-500">
                              {{
                                formatToDateTime(
                                  history.operation_time || history.created_at,
                                )
                              }}
                            </div>
                          </div>
                          <Tag
                            :color="
                              ACTION_CONFIG[history.action as string]?.color ||
                              'blue'
                            "
                            class="!rounded-full !px-3 !py-1 !text-xs !font-medium shadow-sm"
                          >
                            {{
                              history.action_display ||
                              ACTION_CONFIG[history.action as string]?.text ||
                              history.action
                            }}
                          </Tag>
                        </div>

                        <!-- 状态变更信息 -->
                        <div class="mb-3 flex items-center gap-2 text-sm">
                          <span class="text-gray-500">状态变更:</span>
                          <Tag
                            color="orange"
                            size="small"
                            class="!rounded-md !text-xs"
                          >
                            {{
                              history.previous_status_display ||
                              history.previous_status
                            }}
                          </Tag>
                          <span class="text-gray-400">→</span>
                          <Tag
                            color="green"
                            size="small"
                            class="!rounded-md !text-xs"
                          >
                            {{
                              history.new_status_display || history.new_status
                            }}
                          </Tag>
                        </div>

                        <!-- 操作意见 -->
                        <div
                          v-if="history.comments"
                          class="rounded-lg border-l-4 bg-gray-50 p-3"
                          :class="{
                            'border-green-500': history.action === 'approve',
                            'border-red-500': history.action === 'reject',
                            'border-orange-500': history.action === 'rollback',
                            'border-blue-500':
                              history.action === 'create' ||
                              history.action === 'submit',
                            'border-gray-500': history.action === 'cancel',
                          }"
                        >
                          <div
                            class="mb-1 text-xs font-semibold uppercase tracking-wide text-gray-500"
                          >
                            {{
                              {
                                approve: '审批意见',
                                reject: '拒绝原因',
                                rollback: '回退原因',
                                create: '创建说明',
                                submit: '提交说明',
                                cancel: '取消原因',
                              }[history.action as string] || '操作说明'
                            }}
                          </div>
                          <div class="text-sm leading-relaxed text-gray-700">
                            {{ history.comments }}
                          </div>
                        </div>
                      </div>
                    </TimelineItem>
                  </Timeline>
                  <div
                    v-else
                    class="flex flex-col items-center justify-center py-12 text-gray-500"
                  >
                    <div class="mb-4 text-6xl">📋</div>
                    <div class="text-lg font-medium">暂无操作记录</div>
                    <div class="text-sm">该付款申请暂无操作历史记录</div>
                  </div>
                </Spin>
              </div>
            </div>
          </div>
        </Spin>
      </div>
    </Page>

    <!-- 审批弹窗 -->
    <Modal
      v-model:open="isApprovalModalVisible"
      :title="`${currentAction === 'approve' ? '审批通过' : '审批拒绝'}付款申请`"
      width="600px"
      :confirm-loading="workflowLoading"
      @ok="confirmApproval"
    >
      <div class="space-y-6">
        <!-- 申请信息摘要 -->
        <div
          class="rounded-lg border border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50 p-4"
        >
          <h3 class="mb-3 font-semibold text-gray-900">申请信息摘要</h3>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-gray-600">申请编号:</span>
              <span class="ml-2 font-medium">{{
                paymentRequest?.payment_no || '未生成'
              }}</span>
            </div>
            <div>
              <span class="text-gray-600">本次申请付款金额:</span>
              <span class="ml-2 font-medium text-red-600">
                ¥{{
                  paymentRequest?.current_payment_amount?.toLocaleString() ||
                  '0'
                }}
              </span>
            </div>
            <div>
              <span class="text-gray-600">供应商:</span>
              <span class="ml-2 font-medium">{{
                paymentRequest?.supplier?.supplier_name || '-'
              }}</span>
            </div>
            <div>
              <span class="text-gray-600">关联合同:</span>
              <span class="ml-2 font-medium">{{
                paymentRequest?.contract?.contract_no || '-'
              }}</span>
            </div>
          </div>
        </div>

        <!-- 审批意见 -->
        <div>
          <label class="mb-2 block text-sm font-medium text-gray-700">
            审批意见 <span class="text-red-500">*</span>
          </label>
          <Textarea
            v-model:value="approvalComments"
            :rows="4"
            placeholder="请输入审批意见..."
            class="!rounded-lg"
          />
          <div class="mt-2 text-xs text-gray-500">
            请详细说明{{ currentAction === 'approve' ? '通过' : '拒绝' }}的理由
          </div>
        </div>
      </div>
    </Modal>

    <!-- 回退弹窗 -->
    <Modal
      v-model:open="isRollbackModalVisible"
      title="流程回退"
      @ok="confirmRollback"
      class="custom-modal !rounded-xl"
      :width="500"
    >
      <div class="space-y-4 py-4">
        <div class="rounded-lg border border-gray-200/60 bg-gray-50 p-4">
          <label class="mb-2 block text-sm font-semibold text-gray-700">
            回退目标
          </label>
          <Select
            v-model:value="rollbackTarget"
            style="width: 100%"
            placeholder="请选择回退目标环节"
            :options="availableRollbackTargets"
            :disabled="availableRollbackTargets.length === 0"
          />
          <div
            v-if="availableRollbackTargets.length === 0"
            class="mt-2 text-xs text-yellow-600"
          >
            没有可回退的目标环节
          </div>
        </div>
        <div class="rounded-lg border border-gray-200/60 bg-gray-50 p-4">
          <label class="mb-2 block text-sm font-semibold text-gray-700">
            回退原因
          </label>
          <Textarea
            v-model:value="rollbackComments"
            :rows="4"
            placeholder="请输入回退原因"
            class="!rounded-lg !border-gray-300"
          />
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end space-x-3">
          <Button @click="isRollbackModalVisible = false">取消</Button>
          <Button
            type="primary"
            @click="confirmRollback"
            class="bg-orange-500 hover:bg-orange-600"
          >
            确认回退
          </Button>
        </div>
      </template>
    </Modal>

    <!-- 图片预览弹窗 -->
    <Image
      :preview="{
        visible: previewVisible,
        onVisibleChange: (vis: boolean) => (previewVisible = vis),
      }"
      :src="previewImage"
      style="display: none"
    />

    <!-- 供应商详情弹窗 -->
    <Modal
      v-model:open="isSupplierModalVisible"
      title="供应商详细信息"
      :footer="null"
      width="800px"
      class="supplier-detail-modal"
    >
      <Spin :spinning="supplierLoading">
        <div v-if="supplierDetails" class="space-y-6">
          <!-- 基本信息 -->
          <div class="rounded-lg border border-gray-200 bg-gray-50 p-4">
            <h3
              class="mb-4 flex items-center text-lg font-semibold text-gray-900"
            >
              <User class="mr-2 size-5" />
              基本信息
            </h3>
            <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label class="block text-sm font-medium text-gray-700"
                  >供应商名称</label
                >
                <div class="mt-1 text-sm text-gray-900">
                  {{ supplierDetails.name || '-' }}
                </div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700"
                  >供应商编码</label
                >
                <div class="mt-1 text-sm text-gray-900">
                  {{ supplierDetails.code || '-' }}
                </div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700"
                  >简称</label
                >
                <div class="mt-1 text-sm text-gray-900">
                  {{ supplierDetails.short_name || '-' }}
                </div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700"
                  >信用等级</label
                >
                <div class="mt-1 text-sm text-gray-900">
                  {{ supplierDetails.credit_level || 0 }}级
                </div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700"
                  >联系人</label
                >
                <div class="mt-1 text-sm text-gray-900">
                  {{ supplierDetails.contact_person || '-' }}
                </div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700"
                  >联系电话</label
                >
                <div class="mt-1 text-sm text-gray-900">
                  {{ supplierDetails.contact_phone || '-' }}
                </div>
              </div>
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700"
                  >联系邮箱</label
                >
                <div class="mt-1 text-sm text-gray-900">
                  {{ supplierDetails.contact_email || '-' }}
                </div>
              </div>
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700"
                  >地址</label
                >
                <div class="mt-1 text-sm text-gray-900">
                  {{ supplierDetails.address || '-' }}
                </div>
              </div>
            </div>
          </div>

          <!-- 财务信息 -->
          <div class="rounded-lg border border-gray-200 bg-gray-50 p-4">
            <h3
              class="mb-4 flex items-center text-lg font-semibold text-gray-900"
            >
              <span class="mr-2 text-lg">💳</span>
              财务信息
            </h3>
            <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label class="block text-sm font-medium text-gray-700"
                  >开户银行</label
                >
                <div class="mt-1 text-sm text-gray-900">
                  {{ supplierDetails.bank_name || '-' }}
                </div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700"
                  >银行账号</label
                >
                <div class="mt-1 text-sm text-gray-900">
                  {{ supplierDetails.bank_account || '-' }}
                </div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700"
                  >税号</label
                >
                <div class="mt-1 text-sm text-gray-900">
                  {{ supplierDetails.tax_number || '-' }}
                </div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700"
                  >营业执照号</label
                >
                <div class="mt-1 text-sm text-gray-900">
                  {{ supplierDetails.business_license || '-' }}
                </div>
              </div>
            </div>
          </div>

          <!-- 其他信息 -->
          <div class="rounded-lg border border-gray-200 bg-gray-50 p-4">
            <h3
              class="mb-4 flex items-center text-lg font-semibold text-gray-900"
            >
              <span class="mr-2 text-lg">📋</span>
              其他信息
            </h3>
            <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label class="block text-sm font-medium text-gray-700"
                  >状态</label
                >
                <div class="mt-1">
                  <Tag
                    :color="supplierDetails.status === '正常' ? 'green' : 'red'"
                  >
                    {{ supplierDetails.status || '-' }}
                  </Tag>
                </div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700"
                  >创建时间</label
                >
                <div class="mt-1 text-sm text-gray-900">
                  {{ supplierDetails.created_at || '-' }}
                </div>
              </div>
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700"
                  >备注</label
                >
                <div class="mt-1 text-sm text-gray-900">
                  {{ supplierDetails.remark || '-' }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </Spin>
    </Modal>
  </div>
</template>

<style scoped>
/* Custom Timeline Styling */
:deep(.custom-timeline .ant-timeline-item-tail) {
  border-left: 2px solid #e5e7eb;
}

/* Custom Card Header Styling */
:deep(.ant-card-head) {
  background: linear-gradient(to right, #f9fafb, #f3f4f6);
  border-bottom: 1px solid #e5e7eb;
}

:deep(.ant-card-head-title) {
  font-weight: 600;
  color: #1f2937;
}

/* Modal Styling */
:deep(.custom-modal .ant-modal-header) {
  padding: 16px 24px;
  background: linear-gradient(to right, #eff6ff, #e0e7ff);
  border-bottom: 1px solid #e5e7eb;
  border-radius: 0.75rem 0.75rem 0 0;
}

:deep(.custom-modal .ant-modal-content) {
  overflow: hidden;
  border-radius: 0.75rem;
  box-shadow: 0 10px 25px rgb(0 0 0 / 10%);
}

:deep(.custom-modal .ant-modal-title) {
  font-weight: 600;
  color: #1f2937;
}

:deep(.custom-modal .ant-modal-body) {
  padding: 20px 24px;
}

:deep(.custom-modal .ant-modal-footer) {
  padding: 16px 24px;
  border-top: 1px solid #f3f4f6;
}

/* Transition Effects */
.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover Effects */
/* stylelint-disable selector-class-pattern */
.hover\:shadow-md:hover {
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 10%),
    0 2px 4px -1px rgb(0 0 0 / 6%);
}

.hover\:shadow-lg:hover {
  box-shadow:
    0 10px 15px -3px rgb(0 0 0 / 10%),
    0 4px 6px -2px rgb(0 0 0 / 5%);
}
</style>
