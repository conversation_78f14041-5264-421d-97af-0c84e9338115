<script lang="ts" setup>
import { computed } from 'vue';

import { Background } from '@vue-flow/background';
import { Controls } from '@vue-flow/controls';
import { useVueFlow, VueFlow } from '@vue-flow/core';
import { MiniMap } from '@vue-flow/minimap';

// 导入 Vue Flow 样式
import '@vue-flow/core/dist/style.css';
import '@vue-flow/core/dist/theme-default.css';
import '@vue-flow/controls/dist/style.css';
import '@vue-flow/minimap/dist/style.css';

interface Props {
  currentStatus: string;
  approvalHistory?: any[]; // 操作历史记录
}

const props = withDefaults(defineProps<Props>(), {
  currentStatus: 'draft',
  approvalHistory: () => [],
});

// 通过状态历史分析路径
const analyzePathFromStatusHistory = (statusHistory: string[]) => {
  // 检查历史记录是否更符合哪个路径
  let cyberScore = 0;
  let otherScore = 0;
  const analysisLog = [];

  // 方法1: 分析企业负责人审批之后的第一个状态
  const enterpriseIndex = statusHistory.indexOf('enterprise_manager_review');
  if (enterpriseIndex !== -1 && enterpriseIndex < statusHistory.length - 1) {
    const nextStatus = statusHistory[enterpriseIndex + 1];
    analysisLog.push(`企业负责人审批后的下一个状态: ${nextStatus}`);

    switch (nextStatus) {
      case 'cyber_payment_review': {
        cyberScore += 10;
        analysisLog.push('检测到赛博分支标志 (+10分)');

        break;
      }
      case 'fund_manager_review': {
        analysisLog.push('检测到直接跳转到资金负责人审批');

        // 方法2: 通过后续状态推断路径
        const fundIndex = statusHistory.indexOf('fund_manager_review');
        if (fundIndex !== -1 && fundIndex < statusHistory.length - 1) {
          const afterFundStatus = statusHistory[fundIndex + 1];
          analysisLog.push(`资金负责人审批后的状态: ${afterFundStatus}`);

          switch (afterFundStatus) {
            case 'completed':
            case 'final_review': {
              // 如果直接跳到最终复核或完成，需要其他方法判断
              analysisLog.push('直接跳转到最终阶段，需要其他方法判断');

              break;
            }
            case 'cyber_payment_handler': {
              cyberScore += 8;
              analysisLog.push('通过后续状态推断为赛博路径 (+8分)');

              break;
            }
            case 'other_entity_payment_handler': {
              otherScore += 8;
              analysisLog.push('通过后续状态推断为其他主体路径 (+8分)');

              break;
            }
            // No default
          }
        }

        break;
      }
      case 'other_entity_payment_review': {
        otherScore += 10;
        analysisLog.push('检测到其他主体分支标志 (+10分)');

        break;
      }
      // No default
    }
  } else {
    analysisLog.push('未找到企业负责人审批状态');
  }

  // 方法3: 检查整个状态序列中是否有任何分支特有状态
  const hasCyberHandler = statusHistory.includes('cyber_payment_handler');
  const hasOtherHandler = statusHistory.includes(
    'other_entity_payment_handler',
  );
  const hasCyberReview2 = statusHistory.includes('cyber_review_2');
  const hasCyberReview = statusHistory.includes('cyber_payment_review');
  const hasOtherReview = statusHistory.includes('other_entity_payment_review');

  if (hasCyberHandler || hasCyberReview2 || hasCyberReview) {
    cyberScore += 5;
    analysisLog.push(
      `通过整体状态序列推断为赛博路径 (+5分): ${[hasCyberHandler && 'cyber_handler', hasCyberReview2 && 'cyber_review_2', hasCyberReview && 'cyber_review'].filter(Boolean).join(', ')}`,
    );
  } else if (hasOtherHandler || hasOtherReview) {
    otherScore += 5;
    analysisLog.push(
      `通过整体状态序列推断为其他主体路径 (+5分): ${[hasOtherHandler && 'other_handler', hasOtherReview && 'other_review'].filter(Boolean).join(', ')}`,
    );
  }

  // 方法4: 如果还是无法判断，尝试通过状态跳转模式分析
  if (cyberScore === 0 && otherScore === 0) {
    // 检查是否有特殊的跳转模式
    const hasDirectJumpToFinal =
      statusHistory.includes('enterprise_manager_review') &&
      statusHistory.includes('final_review') &&
      !statusHistory.includes('fund_manager_review');

    if (hasDirectJumpToFinal) {
      analysisLog.push('检测到直接跳转到最终复核的模式');
      // 这种情况下可能需要其他信息来判断
    }
  }

  if (cyberScore > otherScore) {
    return 'cyber';
  }
  if (otherScore > cyberScore) {
    return 'other';
  }

  return null; // 无法判断
};

// 从操作历史记录中分析实际走过的路径
const getActualPathFromHistory = (history: any[]) => {
  if (!history || history.length === 0) {
    return null;
  }

  // 从历史记录中提取状态变化序列
  const statusHistory = history
    .map((item) => item.new_status)
    .filter(Boolean)
    .reverse(); // 按时间正序排列

  // 赛博路径特有的状态
  const cyberSpecificStatuses = [
    'cyber_payment_review',
    'cyber_payment_handler',
    'cyber_review_2',
  ];
  // 其他主体路径特有的状态
  const otherSpecificStatuses = [
    'other_entity_payment_review',
    'other_entity_payment_handler',
  ];

  // 检查是否走过赛博路径
  const hasCyberPath = cyberSpecificStatuses.some((status) =>
    statusHistory.includes(status),
  );
  // 检查是否走过其他主体路径
  const hasOtherPath = otherSpecificStatuses.some((status) =>
    statusHistory.includes(status),
  );

  // 如果已经走过分支特有状态，直接返回结果
  if (hasCyberPath) {
    return 'cyber';
  }
  if (hasOtherPath) {
    return 'other';
  }

  // 对于所有情况都尝试分析历史记录
  const pathFromHistory = analyzePathFromStatusHistory(statusHistory);
  if (pathFromHistory) {
    return pathFromHistory;
  }

  return null;
};

// 兜底路径判断函数
const getFallbackPath = (history: any[], _currentStatus: string) => {
  if (!history || history.length === 0) return null;

  // 简单的兜底逻辑：如果历史记录中有任何赛博相关的关键词，判断为赛博路径
  const historyText = history
    .map(
      (item) =>
        `${item.comments || ''} ${item.action || ''} ${item.new_status || ''}`,
    )
    .join(' ')
    .toLowerCase();

  // 检查是否包含赛博相关关键词
  const cyberKeywords = ['cyber', '赛博', 'huawei', '华为'];
  const otherKeywords = ['other', '其他', 'entity', '主体'];

  const hasCyberKeywords = cyberKeywords.some((keyword) =>
    historyText.includes(keyword),
  );
  const hasOtherKeywords = otherKeywords.some((keyword) =>
    historyText.includes(keyword),
  );

  if (hasCyberKeywords && !hasOtherKeywords) {
    return 'cyber';
  } else if (hasOtherKeywords && !hasCyberKeywords) {
    return 'other';
  }

  return null;
};

// 状态映射 - 定义每个状态对应的节点状态
const getNodeStatus = (nodeId: string, currentStatus: string) => {
  // 从历史记录中分析实际路径
  const actualPath = getActualPathFromHistory(props.approvalHistory);

  // 特殊状态处理
  if (currentStatus === 'rejected') {
    return nodeId === 'draft' ? 'completed' : 'rejected';
  }
  if (currentStatus === 'cancelled') {
    return nodeId === 'draft' ? 'completed' : 'cancelled';
  }

  // 定义流程步骤顺序
  const flowSteps = [
    'draft',
    'purchase_review',
    'finance_manager_review',
    'enterprise_manager_review',
  ];

  // 基础流程节点状态
  const baseStepIndex = flowSteps.indexOf(currentStatus);
  if (baseStepIndex !== -1) {
    const nodeIndex = flowSteps.indexOf(nodeId);
    if (nodeIndex !== -1) {
      if (nodeIndex < baseStepIndex) return 'completed';
      if (nodeIndex === baseStepIndex) return 'active';
      return 'pending';
    }
  }

  // 分支路径处理 - 使用与后端一致的状态值
  const cyberPath = [
    'cyber_payment_review', // 赛博付款申请复核
    'fund_manager_review', // 资金负责人审批
    'cyber_payment_handler', // 赛博付款经办
    'cyber_review_2', // 赛博复核2
    'final_review', // 最终复核
    'completed', // 已完成
  ];

  const otherPath = [
    'other_entity_payment_review', // 其他主体付款申请复核
    'fund_manager_review', // 资金负责人审批
    'other_entity_payment_handler', // 其他主体付款经办
    'final_review', // 最终复核
    'completed', // 已完成
  ];

  // 判断当前是哪个路径
  let isCyberPath = cyberPath.includes(currentStatus);
  let isOtherPath = otherPath.includes(currentStatus);

  // 关键修复：对于共同节点，使用历史记录判断实际路径
  const sharedNodes = new Set([
    'completed',
    'final_review',
    'fund_manager_review',
  ]);
  if (
    sharedNodes.has(currentStatus) ||
    currentStatus === 'rejected' ||
    currentStatus === 'cancelled'
  ) {
    if (actualPath === 'cyber') {
      isCyberPath = true;
      isOtherPath = false;
    } else if (actualPath === 'other') {
      isCyberPath = false;
      isOtherPath = true;
    } else {
      // 兜底方案：如果无法从历史记录判断路径，尝试其他方法
      console.warn('⚠️ 无法从历史记录判断实际路径，尝试兜底方案');

      // 临时兜底：检查当前状态前的状态来推断
      const fallbackPath = getFallbackPath(
        props.approvalHistory,
        currentStatus,
      );
      if (fallbackPath === 'cyber') {
        isCyberPath = true;
        isOtherPath = false;
      } else if (fallbackPath === 'other') {
        isCyberPath = false;
        isOtherPath = true;
      } else {
        // 最后的兜底：保持原有逻辑
        console.warn('⚠️ 所有方法都无法判断，保持原有逻辑');
      }
    }
  }

  if (isCyberPath) {
    // 赛博路径
    if (
      nodeId === 'draft' ||
      nodeId === 'purchase_review' ||
      nodeId === 'finance_manager_review' ||
      nodeId === 'enterprise_manager_review'
    ) {
      return 'completed';
    }

    // 如果流程已完成，显示完整的赛博路径
    if (currentStatus === 'completed') {
      if (cyberPath.includes(nodeId)) {
        return 'completed';
      }
      // 其他路径节点显示为待处理
      if (
        otherPath.includes(nodeId) &&
        nodeId !== 'fund_manager_review' &&
        nodeId !== 'final_review' &&
        nodeId !== 'completed'
      ) {
        return 'pending';
      }
    } else {
      // 流程进行中，根据当前位置显示状态
      const cyberIndex = cyberPath.indexOf(currentStatus);
      const nodeIndex = cyberPath.indexOf(nodeId);

      if (nodeIndex !== -1) {
        if (nodeIndex < cyberIndex) return 'completed';
        if (nodeIndex === cyberIndex) return 'active';
        return 'pending';
      }

      // 其他路径节点在赛博路径中显示为待处理
      if (
        otherPath.includes(nodeId) &&
        nodeId !== 'fund_manager_review' &&
        nodeId !== 'final_review' &&
        nodeId !== 'completed'
      ) {
        return 'pending';
      }
    }
  }

  if (isOtherPath) {
    // 其他主体路径
    if (
      nodeId === 'draft' ||
      nodeId === 'purchase_review' ||
      nodeId === 'finance_manager_review' ||
      nodeId === 'enterprise_manager_review'
    ) {
      return 'completed';
    }

    // 如果流程已完成，显示完整的其他主体路径
    if (currentStatus === 'completed') {
      if (otherPath.includes(nodeId)) {
        return 'completed';
      }
      // 赛博路径节点显示为待处理
      if (
        cyberPath.includes(nodeId) &&
        nodeId !== 'fund_manager_review' &&
        nodeId !== 'final_review' &&
        nodeId !== 'completed'
      ) {
        return 'pending';
      }
    } else {
      // 流程进行中，根据当前位置显示状态
      const otherIndex = otherPath.indexOf(currentStatus);
      const nodeIndex = otherPath.indexOf(nodeId);

      if (nodeIndex !== -1) {
        if (nodeIndex < otherIndex) return 'completed';
        if (nodeIndex === otherIndex) return 'active';
        return 'pending';
      }

      // 赛博路径节点在其他路径中显示为待处理
      if (
        cyberPath.includes(nodeId) &&
        nodeId !== 'fund_manager_review' &&
        nodeId !== 'final_review' &&
        nodeId !== 'completed'
      ) {
        return 'pending';
      }
    }
  }

  // 如果当前状态不在任何分支路径中，但节点在分支路径中
  // 根据基础流程的完成情况来判断分支节点状态
  if (!isCyberPath && !isOtherPath) {
    // 如果当前状态在基础流程中，且已经完成了企业负责人审批
    // 那么分支节点应该显示为待处理状态
    const baseStepIndex = flowSteps.indexOf(currentStatus);
    if (
      baseStepIndex >= 3 && // 企业负责人审批已完成
      // 分支节点显示为待处理
      (cyberPath.includes(nodeId) || otherPath.includes(nodeId))
    ) {
      return 'pending';
    }
  }

  return 'pending';
};

// 节点定义
const nodes = computed(() => [
  {
    id: 'draft',
    type: 'custom',
    position: { x: 300, y: 50 },
    data: {
      label: '📝 申请创建',
      description: '付款申请创建',
      status: getNodeStatus('draft', props.currentStatus),
      category: 'start',
    },
  },
  {
    id: 'purchase_review',
    type: 'custom',
    position: { x: 300, y: 150 },
    data: {
      label: '🛒 采购与供应链审批',
      description: '采购负责人审批',
      status: getNodeStatus('purchase_review', props.currentStatus),
      category: 'approval',
    },
  },
  {
    id: 'finance_manager_review',
    type: 'custom',
    position: { x: 300, y: 250 },
    data: {
      label: '💰 财务负责人审批',
      description: '财务负责人审批',
      status: getNodeStatus('finance_manager_review', props.currentStatus),
      category: 'approval',
    },
  },
  {
    id: 'enterprise_manager_review',
    type: 'custom',
    position: { x: 300, y: 350 },
    data: {
      label: '🏢 企业负责人审批',
      description: '企业负责人审批',
      status: getNodeStatus('enterprise_manager_review', props.currentStatus),
      category: 'approval',
    },
  },
  // 分支节点
  {
    id: 'cyber_payment_review',
    type: 'custom',
    position: { x: 100, y: 450 },
    data: {
      label: '🔍 赛博付款申请复核',
      description: '赛博路径付款申请复核',
      status: getNodeStatus('cyber_payment_review', props.currentStatus),
      category: 'cyber',
    },
  },
  {
    id: 'other_entity_payment_review',
    type: 'custom',
    position: { x: 500, y: 450 },
    data: {
      label: '👥 其他主体付款申请复核',
      description: '其他主体付款申请复核',
      status: getNodeStatus('other_entity_payment_review', props.currentStatus),
      category: 'other',
    },
  },
  // 汇聚节点
  {
    id: 'fund_manager_review',
    type: 'custom',
    position: { x: 300, y: 550 },
    data: {
      label: '💳 资金负责人审批',
      description: '资金负责人审批',
      status: getNodeStatus('fund_manager_review', props.currentStatus),
      category: 'approval',
    },
  },
  // 再次分支
  {
    id: 'cyber_payment_handler',
    type: 'custom',
    position: { x: 100, y: 650 },
    data: {
      label: '🏦 赛博付款经办',
      description: '赛博付款经办',
      status: getNodeStatus('cyber_payment_handler', props.currentStatus),
      category: 'cyber',
    },
  },
  {
    id: 'other_entity_payment_handler',
    type: 'custom',
    position: { x: 500, y: 650 },
    data: {
      label: '🏛️ 其他主体付款经办',
      description: '其他主体付款经办',
      status: getNodeStatus(
        'other_entity_payment_handler',
        props.currentStatus,
      ),
      category: 'other',
    },
  },
  // 赛博复核2（仅赛博路径）
  {
    id: 'cyber_review_2',
    type: 'custom',
    position: { x: 100, y: 750 },
    data: {
      label: '🔄 赛博复核2',
      description: '仅赛博路径复核',
      status: getNodeStatus('cyber_review_2', props.currentStatus),
      category: 'cyber',
    },
  },
  // 最终汇聚
  {
    id: 'final_review',
    type: 'custom',
    position: { x: 300, y: 850 },
    data: {
      label: '✅ 最终复核',
      description: '最终复核确认',
      status: getNodeStatus('final_review', props.currentStatus),
      category: 'approval',
    },
  },
  {
    id: 'completed',
    type: 'custom',
    position: { x: 300, y: 950 },
    data: {
      label: '🎉 流程完成',
      description: '审批流程完成',
      status: getNodeStatus('completed', props.currentStatus),
      category: 'end',
    },
  },
]);

// 边定义
const edges = computed(() => [
  { id: 'e1', source: 'draft', target: 'purchase_review', animated: true },
  {
    id: 'e2',
    source: 'purchase_review',
    target: 'finance_manager_review',
    animated: true,
  },
  {
    id: 'e3',
    source: 'finance_manager_review',
    target: 'enterprise_manager_review',
    animated: true,
  },

  // 分支
  {
    id: 'e4a',
    source: 'enterprise_manager_review',
    target: 'cyber_payment_review',
    animated: true,
  },
  {
    id: 'e4b',
    source: 'enterprise_manager_review',
    target: 'other_entity_payment_review',
    animated: true,
  },

  // 汇聚到资金负责人
  {
    id: 'e5a',
    source: 'cyber_payment_review',
    target: 'fund_manager_review',
    animated: true,
  },
  {
    id: 'e5b',
    source: 'other_entity_payment_review',
    target: 'fund_manager_review',
    animated: true,
  },

  // 再次分支
  {
    id: 'e6a',
    source: 'fund_manager_review',
    target: 'cyber_payment_handler',
    animated: true,
  },
  {
    id: 'e6b',
    source: 'fund_manager_review',
    target: 'other_entity_payment_handler',
    animated: true,
  },

  // 赛博复核2
  {
    id: 'e7',
    source: 'cyber_payment_handler',
    target: 'cyber_review_2',
    animated: true,
  },

  // 最终汇聚
  {
    id: 'e8a',
    source: 'cyber_review_2',
    target: 'final_review',
    animated: true,
  },
  {
    id: 'e8b',
    source: 'other_entity_payment_handler',
    target: 'final_review',
    animated: true,
  },

  // 完成
  { id: 'e9', source: 'final_review', target: 'completed', animated: true },
]);

const { onInit } = useVueFlow();

onInit((vueFlowInstance) => {
  vueFlowInstance.fitView();
});
</script>

<template>
  <div class="workflow-chart-container">
    <VueFlow
      :nodes="nodes"
      :edges="edges"
      :default-viewport="{ zoom: 0.8 }"
      :min-zoom="0.2"
      :max-zoom="2"
      fit-view-on-init
      class="workflow-chart"
    >
      <Background pattern-color="#e5e7eb" :gap="20" />
      <Controls />
      <MiniMap />

      <!-- 自定义节点模板 -->
      <template #node-custom="{ data }">
        <div
          class="custom-node"
          :class="[
            `node-${data.status}`,
            `category-${data.category}`,
            {
              'node-completed': data.status === 'completed',
              'node-active': data.status === 'active',
              'node-pending': data.status === 'pending',
              'node-rejected': data.status === 'rejected',
              'node-cancelled': data.status === 'cancelled',
            },
          ]"
        >
          <div class="node-content">
            <div class="node-label">{{ data.label }}</div>
            <div class="node-description">{{ data.description }}</div>
          </div>
          <div class="node-status-indicator" :class="`status-${data.status}`">
            <span v-if="data.status === 'completed'" class="status-icon"
              >✓</span
            >
            <span v-else-if="data.status === 'active'" class="status-icon"
              >⏳</span
            >
            <span v-else-if="data.status === 'rejected'" class="status-icon"
              >✗</span
            >
            <span v-else-if="data.status === 'cancelled'" class="status-icon"
              >⊘</span
            >
          </div>
        </div>
      </template>
    </VueFlow>
  </div>
</template>

<style scoped>
@keyframes pulse {
  0%,
  100% {
    box-shadow: 0 4px 12px rgb(59 130 246 / 30%);
  }

  50% {
    box-shadow: 0 4px 20px rgb(59 130 246 / 50%);
  }
}

@keyframes pulse-indicator {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.2);
  }
}

@keyframes dashdraw {
  from {
    stroke-dashoffset: 10;
  }

  to {
    stroke-dashoffset: 0;
  }
}

.workflow-chart-container {
  width: 100%;
  height: 600px;
  overflow: hidden;
  background: #f8fafc;
  border-radius: 12px;
}

.workflow-chart {
  width: 100%;
  height: 100%;
}

.custom-node {
  position: relative;
  min-width: 220px;
  padding: 18px;
  overflow: hidden;
  cursor: pointer;
  background: white;
  border: 3px solid;
  border-radius: 16px;
  box-shadow: 0 6px 20px rgb(0 0 0 / 8%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.custom-node::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 4px;
  content: '';
  background: linear-gradient(
    90deg,
    transparent,
    rgb(255 255 255 / 80%),
    transparent
  );
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.custom-node:hover {
  box-shadow: 0 12px 32px rgb(0 0 0 / 12%);
  transform: translateY(-4px) scale(1.02);
}

.custom-node:hover::before {
  transform: translateX(100%);
}

/* 状态样式 */
.node-completed {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  border-color: #10b981;
  box-shadow: 0 6px 20px rgb(16 185 129 / 15%);
}

.node-active {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-color: #3b82f6;
  box-shadow: 0 6px 20px rgb(59 130 246 / 20%);
  animation: pulse 2s infinite;
}

.node-pending {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  border-color: #d1d5db;
  opacity: 0.6;
}

.node-rejected {
  background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
  border-color: #ef4444;
  box-shadow: 0 6px 20px rgb(239 68 68 / 15%);
}

.node-cancelled {
  background: linear-gradient(135deg, #f9fafb 0%, #e5e7eb 100%);
  border-color: #6b7280;
  opacity: 0.5;
}

/* 类别样式 */
.category-cyber {
  border-left: 6px solid #3b82f6;
}

.category-other {
  border-left: 6px solid #8b5cf6;
}

.category-start {
  border-left: 6px solid #10b981;
}

.category-end {
  border-left: 6px solid #f59e0b;
}

.node-content {
  text-align: center;
}

.node-label {
  margin-bottom: 4px;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.node-description {
  font-size: 12px;
  color: #6b7280;
}

.node-status-indicator {
  position: absolute;
  top: -8px;
  right: -8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  font-size: 10px;
  font-weight: bold;
  border: 3px solid white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
}

.status-completed {
  color: white;
  background: linear-gradient(135deg, #10b981, #059669);
}

.status-active {
  color: white;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  animation: pulse-indicator 1.5s infinite;
}

.status-pending {
  color: white;
  background: linear-gradient(135deg, #9ca3af, #6b7280);
}

.status-rejected {
  color: white;
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.status-cancelled {
  color: white;
  background: linear-gradient(135deg, #6b7280, #4b5563);
}

.status-icon {
  font-size: 10px;
  line-height: 1;
}

/* Vue Flow 样式覆盖 */
:deep(.vue-flow__edge-path) {
  stroke: #6b7280;
  stroke-width: 2;
}

:deep(.vue-flow__edge.animated .vue-flow__edge-path) {
  stroke-dasharray: 5;
  animation: dashdraw 0.5s linear infinite;
}

:deep(.vue-flow__controls) {
  bottom: 20px;
  left: 20px;
}

:deep(.vue-flow__minimap) {
  right: 20px;
  bottom: 20px;
}
</style>
