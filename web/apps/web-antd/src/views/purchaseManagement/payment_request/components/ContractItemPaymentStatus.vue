<script lang="ts" setup>
import type { PurchaseContractItem } from '#/api/core/purchase/purchase_contract';

import { computed, onMounted, ref } from 'vue';

import { Progress, Tooltip } from 'ant-design-vue';

import { getPaymentRequestsByContract } from '#/api/core/purchase/payment_request';

interface Props {
  contractItem: PurchaseContractItem;
  contractId?: number;
}

const props = defineProps<Props>();

// 付款统计数据
const paymentStats = ref({
  paidQuantity: 0,
  paidAmount: 0,
  unpaidQuantity: 0,
  unpaidAmount: 0,
  paymentProgress: 0,
});

const loading = ref(false);

// 计算属性
const statusColor = computed(() => {
  const progress = paymentStats.value.paymentProgress;
  if (progress === 0) return 'gray';
  if (progress < 50) return 'red';
  if (progress < 100) return 'orange';
  return 'green';
});

const statusText = computed(() => {
  const progress = paymentStats.value.paymentProgress;
  if (progress === 0) return '未付款';
  if (progress < 100) return '部分付款';
  return '已完成';
});

const statusIcon = computed(() => {
  const progress = paymentStats.value.paymentProgress;
  if (progress === 0) return '⏳';
  if (progress < 100) return '🔄';
  return '✅';
});

/**
 * 获取合同明细项的付款统计信息
 */
async function fetchPaymentStats() {
  if (!props.contractId || !props.contractItem.id) {
    return;
  }

  loading.value = true;
  try {
    // 获取该合同的所有付款申请
    const paymentRequests = await getPaymentRequestsByContract(
      props.contractId,
    );

    let totalPaidQuantity = 0;
    let totalPaidAmount = 0;

    // 遍历所有已完成的付款申请
    for (const payment of paymentRequests) {
      if (payment.status === 'completed' && payment.items) {
        // 查找该明细项的付款记录
        const paymentItem = payment.items.find(
          (item) => item.contract_item_id === props.contractItem.id,
        );
        if (paymentItem) {
          totalPaidQuantity += paymentItem.current_payment_quantity || 0;
          totalPaidAmount += paymentItem.current_payment_amount || 0;
        }
      }
    }

    // 计算未付款数量和金额
    const contractQuantity = props.contractItem.contract_quantity || 0;
    const contractAmount = props.contractItem.contract_amount || 0;
    const unpaidQuantity = Math.max(0, contractQuantity - totalPaidQuantity);
    const unpaidAmount = Math.max(0, contractAmount - totalPaidAmount);

    // 计算付款进度
    const paymentProgress =
      contractAmount > 0 ? (totalPaidAmount / contractAmount) * 100 : 0;

    paymentStats.value = {
      paidQuantity: totalPaidQuantity,
      paidAmount: totalPaidAmount,
      unpaidQuantity,
      unpaidAmount,
      paymentProgress: Math.min(100, paymentProgress),
    };
  } catch (error) {
    console.error('获取付款统计信息失败:', error);
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  fetchPaymentStats();
});
</script>

<template>
  <div class="space-y-2">
    <!-- 状态标签 -->
    <div class="flex items-center space-x-2">
      <span class="text-sm">{{ statusIcon }}</span>
      <span
        class="rounded-full px-2 py-1 text-xs font-medium"
        :class="{
          'bg-gray-100 text-gray-600': statusColor === 'gray',
          'bg-red-100 text-red-600': statusColor === 'red',
          'bg-orange-100 text-orange-600': statusColor === 'orange',
          'bg-green-100 text-green-600': statusColor === 'green',
        }"
      >
        {{ statusText }}
      </span>
    </div>

    <!-- 进度条 -->
    <div class="w-full">
      <Tooltip>
        <template #title>
          <div class="space-y-1 text-xs">
            <div>
              已付款数量: {{ paymentStats.paidQuantity }}
              {{ contractItem.unit || '' }}
            </div>
            <div>
              已付款金额: ¥{{ paymentStats.paidAmount.toLocaleString() }}
            </div>
            <div>
              未付款数量: {{ paymentStats.unpaidQuantity }}
              {{ contractItem.unit || '' }}
            </div>
            <div>
              未付款金额: ¥{{ paymentStats.unpaidAmount.toLocaleString() }}
            </div>
            <div>付款进度: {{ paymentStats.paymentProgress.toFixed(1) }}%</div>
          </div>
        </template>
        <Progress
          :percent="paymentStats.paymentProgress"
          :stroke-color="{
            '0%':
              statusColor === 'gray'
                ? '#9CA3AF'
                : statusColor === 'red'
                  ? '#EF4444'
                  : statusColor === 'orange'
                    ? '#F59E0B'
                    : '#10B981',
            '100%':
              statusColor === 'gray'
                ? '#6B7280'
                : statusColor === 'red'
                  ? '#DC2626'
                  : statusColor === 'orange'
                    ? '#D97706'
                    : '#059669',
          }"
          :show-info="false"
          size="small"
          class="w-20"
        />
      </Tooltip>
    </div>

    <!-- 数量统计 -->
    <div class="text-xs text-gray-500">
      <div>
        已付: {{ paymentStats.paidQuantity }}/{{
          contractItem.contract_quantity || 0
        }}
      </div>
      <div>金额: ¥{{ paymentStats.paidAmount.toLocaleString() }}</div>
    </div>
  </div>
</template>
