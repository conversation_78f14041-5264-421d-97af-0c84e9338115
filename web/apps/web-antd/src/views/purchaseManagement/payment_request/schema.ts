import type { VbenFormSchema } from '#/adapter/form';

import { z } from '#/adapter/form';
import { getPurchaseContractList } from '#/api/core/purchase/purchase_contract';
import { getSupplierListApi } from '#/api/core/purchase/supplier';

import { PAYMENT_STATUS_OPTIONS, PAYMENT_TYPE_OPTIONS } from './constants';

// 定义全局窗口接口
declare global {
  interface Window {
    handleContractChange?: (id: number) => void;
  }
}

export function useBasicSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        showSearch: true,
        filterOption: (
          input: string,
          option: { label: string; value: number },
        ) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
        api: async () => {
          const res = await getPurchaseContractList({
            page: 1,
            pageSize: 100,
            status: 'completed',
          });
          return res.list.map((item) => ({
            label: `${item.contract_no} - ${item.contract_title}`,
            value: item.id,
          }));
        },
        class: 'w-full',
        labelField: 'label',
        valueField: 'value',
        placeholder: '请选择关联的合同',
        onChange: (value: number) => {
          if (window.handleContractChange && value) {
            window.handleContractChange(value);
          }
        },
      },

      fieldName: 'contract_id',
      label: '关联合同',
      rules: 'selectRequired',
    },
    {
      component: 'Select',
      componentProps: {
        options: PAYMENT_TYPE_OPTIONS,
        placeholder: '请选择付款类型',
        style: { width: '100%' },
      },
      fieldName: 'payment_type',
      label: '付款类型',
      rules: z.string().min(1, '请选择付款类型'),
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        placeholder: '由付款明细自动计算',
        style: { width: '100%' },
        disabled: true,
        formatter: (value: number) =>
          `¥ ${value}`.replaceAll(/\B(?=(?:\d{3})+(?!\d))/g, ','),
        parser: (value: string) => value.replaceAll(/¥\s?|,*/g, ''),
      },
      fieldName: 'current_payment_amount',
      label: '本次申请付款金额（自动计算）',
      rules: z.number().min(0.01, '本次申请付款金额不能为空'),
    },

    {
      component: 'Textarea',
      componentProps: {
        maxLength: 500,
        placeholder: '请输入付款说明',
        rows: 2,
        showCount: true,
        style: { width: '100%' },
      },
      fieldName: 'payment_reason',
      label: '付款说明',
      rules: z.string().min(1, '请输入付款说明'),
    },
    {
      component: 'Textarea',
      componentProps: {
        maxLength: 200,
        placeholder: '请输入备注信息',
        rows: 2,
        showCount: true,
        style: { width: '100%' },
      },
      fieldName: 'remark',
      label: '备注',
    },
  ];
}

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'payment_no',
      label: '付款申请编号',
      componentProps: {
        placeholder: '请输入付款申请编号',
      },
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入合同编号',
        allowClear: true,
      },
      fieldName: 'contract_no',
      label: '合同编号',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        showSearch: true,
        filterOption: true,
        api: async () => {
          const res = await getSupplierListApi({
            page: 1,
            pageSize: 100,
          });
          return res.list.map((item) => ({
            label: item.supplier_name,
            value: item.id,
          }));
        },
        placeholder: '请选择供应商',
      },
      fieldName: 'supplier_id',
      label: '供应商',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: PAYMENT_STATUS_OPTIONS,
      },
      fieldName: 'status',
      label: '状态',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: PAYMENT_TYPE_OPTIONS,
      },
      fieldName: 'payment_type',
      label: '付款类型',
    },
    {
      component: 'RangePicker',
      fieldName: 'create_time',
      label: '创建时间',
    },
  ];
}
