/**
 * 付款申请常量定义
 */

// 定义标签配置接口
export interface TagConfig {
  color: string;
  text: string;
}

// 定义标签配置映射接口
export interface TagConfigMap {
  [key: string]: TagConfig;
}

// 付款申请状态配置
export const PAYMENT_STATUS_CONFIG: TagConfigMap = {
  draft: { color: '', text: '草稿' },
  purchase_review: { color: 'blue', text: '采购负责人审批' },
  finance_manager_review: { color: 'purple', text: '财务负责人审批' },
  enterprise_manager_review: { color: 'orange', text: '企业负责人审批' },
  company_entity_judgment_1: { color: 'cyan', text: '公司主体判断（第一次）' },
  cyber_payment_review: { color: 'blue', text: '赛博付款申请复核' },
  other_entity_payment_review: {
    color: 'blue',
    text: '其他主体付款申请复核',
  },
  fund_manager_review: { color: 'purple', text: '资金负责人审批' },
  cyber_payment_handler: { color: 'orange', text: '赛博付款经办' },
  other_entity_payment_handler: { color: 'green', text: '其他主体付款经办' },
  cyber_review_2: { color: 'orange', text: '赛博复核2' },
  final_review: { color: 'purple', text: '最终复核' },
  completed: { color: 'green', text: '已完成' },
  rejected: { color: 'red', text: '已拒绝' },
  cancelled: { color: 'red', text: '已取消' },
};

// 付款申请状态选项
export const PAYMENT_STATUS_OPTIONS = [
  { label: '草稿', value: 'draft' },
  { label: '采购负责人审批', value: 'purchase_review' },
  { label: '财务负责人审批', value: 'finance_manager_review' },
  { label: '企业负责人审批', value: 'enterprise_manager_review' },
  { label: '公司主体判断（第一次）', value: 'company_entity_judgment_1' },
  { label: '赛博付款申请复核', value: 'cyber_payment_review' },
  { label: '其他主体付款申请复核', value: 'other_entity_payment_review' },
  { label: '资金负责人审批', value: 'fund_manager_review' },
  { label: '赛博付款经办', value: 'cyber_payment_handler' },
  { label: '其他主体付款经办', value: 'other_entity_payment_handler' },
  { label: '赛博复核2', value: 'cyber_review_2' },
  { label: '最终复核', value: 'final_review' },
  { label: '已完成', value: 'completed' },
  { label: '已拒绝', value: 'rejected' },
  { label: '已取消', value: 'cancelled' },
];

// 付款类型配置
export const PAYMENT_TYPE_CONFIG: TagConfigMap = {
  advance: { color: 'blue', text: '预付款' },
  cash_on_delivery: { color: 'green', text: '现货提款' },
  after_delivery_acceptance: { color: 'orange', text: '货到验收后付款' },
  account_period_due: { color: 'purple', text: '账期到期付款' },
  acceptance_delivery: {
    color: 'cyan',
    text: '验收到货（到货验收后尾款支付）',
  },
  after_acceptance: { color: 'geekblue', text: '验收后付款提货' },
};

// 付款类型选项
export const PAYMENT_TYPE_OPTIONS = [
  { label: '预付款', value: 'advance' },
  { label: '现货提款', value: 'cash_on_delivery' },
  { label: '货到验收后付款', value: 'after_delivery_acceptance' },
  { label: '账期到期付款', value: 'account_period_due' },
  { label: '验收到货（到货验收后尾款支付）', value: 'acceptance_delivery' },
  { label: '验收后付款提货', value: 'after_acceptance' },
];

// 公司主体类型配置
export const COMPANY_ENTITY_TYPE_CONFIG: TagConfigMap = {
  cyber: { color: 'blue', text: '赛博主体' },
  other: { color: 'green', text: '其他主体' },
};

// 公司主体类型选项
export const COMPANY_ENTITY_TYPE_OPTIONS = [
  { label: '赛博主体', value: 'cyber' },
  { label: '其他主体', value: 'other' },
];

// 操作类型映射
export const ACTION_CONFIG: TagConfigMap = {
  create: { text: '创建申请', color: 'blue' },
  submit: { text: '提交申请', color: 'green' },
  approve: { text: '审批通过', color: 'success' },
  reject: { text: '审批拒绝', color: 'error' },
  rollback: { text: '回退申请', color: 'orange' },
  withdraw: { text: '撤回申请', color: 'warning' },
  cancel: { text: '取消申请', color: 'error' },
  update: { text: '更新申请', color: 'blue' },
  delete: { text: '删除申请', color: 'error' },
  mark_paid: { text: '标记已付款', color: 'success' },
};

// 审批动作选项
export const APPROVAL_ACTION_OPTIONS = [
  { label: '批准', value: 'approve' },
  { label: '拒绝', value: 'reject' },
];
