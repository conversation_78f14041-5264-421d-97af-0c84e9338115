<script lang="ts" setup>
import { ref } from 'vue';

import WorkflowChart from './components/WorkflowChart.vue';

const currentStatus = ref('draft');

const statusOptions = [
  { value: 'draft', label: '草稿' },
  { value: 'purchase_review', label: '采购与供应链审批' },
  { value: 'finance_manager_review', label: '财务负责人审批' },
  { value: 'enterprise_manager_review', label: '企业负责人审批' },
  { value: 'cyber_specialist_review', label: '赛博专员复核' },
  { value: 'other_entity_specialist_review', label: '其他主体专员复核' },
  { value: 'fund_manager_review', label: '资金负责人审批' },
  { value: 'cyber_finance_review', label: '赛博财务经办' },
  { value: 'other_entity_finance_review', label: '其他主体财务经办' },
  { value: 'cyber_recheck_2', label: '赛博复核2' },
  { value: 'final_review', label: '最终复核' },
  { value: 'completed', label: '已完成' },
  { value: 'rejected', label: '已拒绝' },
  { value: 'cancelled', label: '已取消' },
];
</script>

<template>
  <div class="workflow-demo-page">
    <div class="page-header">
      <h1>付款申请流程图演示</h1>
      <p>这是一个交互式的流程图演示，展示了付款申请的完整审批流程。</p>
    </div>

    <div class="demo-container">
      <div class="controls">
        <label>当前状态:</label>
        <select v-model="currentStatus" class="status-select">
          <option
            v-for="option in statusOptions"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </option>
        </select>
      </div>

      <div class="chart-container">
        <WorkflowChart :current-status="currentStatus" />
      </div>

      <div class="legend">
        <h3>图例说明</h3>
        <div class="legend-items">
          <div class="legend-item">
            <div class="legend-color completed"></div>
            <span>已完成</span>
          </div>
          <div class="legend-item">
            <div class="legend-color active"></div>
            <span>进行中</span>
          </div>
          <div class="legend-item">
            <div class="legend-color pending"></div>
            <span>待处理</span>
          </div>
          <div class="legend-item">
            <div class="legend-color rejected"></div>
            <span>已拒绝</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.workflow-demo-page {
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.page-header {
  padding: 40px 20px;
  color: white;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 16px;
  font-size: 2.5rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgb(0 0 0 / 30%);
}

.page-header p {
  max-width: 600px;
  margin: 0;
  margin: 0 auto;
  font-size: 1.1rem;
  opacity: 0.9;
}

.demo-container {
  max-width: 1200px;
  padding: 24px;
  margin: 0 auto;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgb(0 0 0 / 10%);
}

.controls {
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 16px;
  margin-bottom: 24px;
  background: #f8fafc;
  border-radius: 12px;
}

.controls label {
  font-weight: 600;
  color: #374151;
}

.status-select {
  min-width: 200px;
  padding: 8px 12px;
  font-size: 14px;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
}

.status-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgb(59 130 246 / 10%);
}

.chart-container {
  margin-bottom: 24px;
  overflow: hidden;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
}

.legend {
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
}

.legend h3 {
  margin: 0 0 12px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.legend-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.legend-color {
  width: 16px;
  height: 16px;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
}

.legend-color.completed {
  background: linear-gradient(135deg, #10b981, #059669);
}

.legend-color.active {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.legend-color.pending {
  background: linear-gradient(135deg, #9ca3af, #6b7280);
}

.legend-color.rejected {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}
</style>
