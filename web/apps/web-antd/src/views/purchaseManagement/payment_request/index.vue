<script lang="ts" setup>
import type {
  PageParams,
  PaymentRequest,
  PaymentRequestQuery,
} from '#/api/core/purchase/payment_request';

import { nextTick, ref } from 'vue';
import { useRouter } from 'vue-router';

import { AccessControl } from '@vben/access';
import { Page, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { Button, Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getPaymentRequestList } from '#/api/core/purchase/payment_request';

import { PAYMENT_STATUS_CONFIG, PAYMENT_TYPE_CONFIG } from './constants';
import { useColumns, useItemColumns } from './data';
import Form from './modules/form.vue';
import { useGridFormSchema } from './schema';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

const router = useRouter();

// 付款明细模态框相关数据
const detailModalVisible = ref(false);
const currentItems = ref<any[]>([]);
const currentPaymentNo = ref('');

// 付款明细表格
const [DetailGrid, detailGridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useItemColumns(),
    height: 'auto',
    maxHeight: 500,
    pagerConfig: {
      enabled: false,
    },
    proxyConfig: {
      autoLoad: false,
      ajax: {
        query: async () => {
          return {
            items: currentItems.value,
            total: currentItems.value.length,
          };
        },
      },
    },
    toolbarConfig: {
      enabled: false,
    },
  },
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['create_time', ['start_date', 'end_date']]],
    schema: useGridFormSchema(),
    submitOnEnter: true,
    submitOnChange: false,
  },
  gridOptions: {
    columns: useColumns(),
    height: 'auto',
    pagerConfig: {
      enabled: true,
    },
    proxyConfig: {
      ajax: {
        query: async (
          { page }: { page: PageParams },
          formValues: Record<string, any>,
        ) => {
          const params: PaymentRequestQuery = {
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          };

          const res = await getPaymentRequestList(params);

          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    toolbarConfig: {
      custom: true,
      export: true,
      refresh: true,
      refreshOptions: { code: 'query' },
      search: true,
      zoom: true,
      zoomOptions: {},
    },
  },
});

/**
 * 创建新付款申请
 */
function onCreate() {
  formModalApi.setData(null).open();
}

/**
 * 查看付款申请详情
 */
function onDetail(row: PaymentRequest) {
  router.push(`/purchase-management/payment-request/detail/${row.id}`);
}

/**
 * 显示付款明细
 */
async function showItemsDetail(row: PaymentRequest) {
  if (row.items && row.items.length > 0) {
    currentItems.value = row.items;
    currentPaymentNo.value = row.payment_no || '';
    detailModalVisible.value = true;

    // 等待模态框打开后加载数据
    await nextTick();
    if (detailGridApi && detailGridApi.grid) {
      await detailGridApi.grid.commitProxy('query');
    }
  }
}

/**
 * 刷新表格
 */
function refreshGrid() {
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="refreshGrid" />
    <Grid>
      <template #toolbar-tools>
        <AccessControl :codes="['Purchase:PaymentRequest:Create']" type="code">
          <Button type="primary" @click="onCreate">
            <Plus class="size-5" />
            新建付款申请
          </Button>
        </AccessControl>
      </template>

      <!-- 付款状态插槽 -->
      <template #status="{ row }">
        <Tag :color="PAYMENT_STATUS_CONFIG[row.status as string]?.color">
          {{ PAYMENT_STATUS_CONFIG[row.status as string]?.text || row.status }}
        </Tag>
      </template>

      <!-- 付款类型插槽 -->
      <template #payment_type="{ row }">
        <Tag :color="PAYMENT_TYPE_CONFIG[row.payment_type as string]?.color">
          {{
            PAYMENT_TYPE_CONFIG[row.payment_type as string]?.text ||
            row.payment_type
          }}
        </Tag>
      </template>

      <!-- 付款明细按钮插槽 -->
      <template #items_action="{ row }">
        <Button
          type="link"
          :disabled="!row.items || row.items.length === 0"
          @click="showItemsDetail(row)"
        >
          查看明细({{ row.items?.length || 0 }})
        </Button>
      </template>

      <!-- 操作列插槽 -->
      <template #action="{ row }">
        <Button size="small" type="link" @click="onDetail(row)"> 查看 </Button>
      </template>
    </Grid>

    <!-- 付款明细模态框 -->
    <Modal
      v-model:open="detailModalVisible"
      title="付款明细"
      :width="1400"
      :footer="null"
      :destroy-on-close="true"
      class="payment-detail-modal"
    >
      <div class="payment-detail-header">
        <div
          class="flex items-center justify-between rounded-lg bg-orange-50 p-4"
        >
          <div class="flex items-center space-x-3">
            <div
              class="flex h-10 w-10 items-center justify-center rounded-full bg-orange-500"
            >
              <span class="font-semibold text-white">付</span>
            </div>
            <div>
              <div class="text-lg font-semibold text-orange-900">
                付款申请编号: {{ currentPaymentNo }}
              </div>
              <div class="text-sm text-orange-600">
                共 {{ currentItems.length }} 个明细项
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <span class="text-sm text-orange-600">总计金额:</span>
            <span class="text-lg font-bold text-orange-800">
              ¥{{
                currentItems
                  .reduce(
                    (sum, item) => sum + (item.current_payment_amount || 0),
                    0,
                  )
                  .toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })
              }}
            </span>
          </div>
        </div>
      </div>

      <div class="payment-detail-table">
        <DetailGrid />
      </div>
    </Modal>
  </Page>
</template>

<style lang="less" scoped>
:deep(.vxe-table) {
  .vxe-header--row {
    background: #f8fafc;

    .vxe-header--column {
      background: #f8fafc;
      border-bottom: 2px solid #e2e8f0;
      font-weight: 600;
      color: #374151;
      font-size: 13px;
    }
  }

  .vxe-body--row {
    &:nth-child(even) {
      background: #fdfdfd;
    }

    &:hover {
      background: #f8fafc !important;
    }

    .vxe-body--column {
      border-bottom: 1px solid #f1f5f9;
      vertical-align: middle;
    }
  }
}

// 操作按钮样式优化
:deep(.ant-btn-link) {
  padding: 0 4px;
  height: auto;
  line-height: 1.4;

  &:not(:last-child) {
    margin-right: 8px;
  }
}

// Tag 样式优化
:deep(.ant-tag) {
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  padding: 2px 8px;
}

/* 付款明细弹窗样式 */
.payment-detail-modal {
  :deep(.ant-modal-content) {
    border-radius: 12px;
    overflow: hidden;
  }

  :deep(.ant-modal-header) {
    background: linear-gradient(135deg, #ff7849 0%, #ff6b35 100%);
    border-bottom: none;
    padding: 16px 24px;
  }

  :deep(.ant-modal-title) {
    color: white;
    font-weight: 600;
    font-size: 16px;
  }

  :deep(.ant-modal-close) {
    color: white;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }

  :deep(.ant-modal-body) {
    padding: 0;
  }
}

.payment-detail-header {
  margin-bottom: 16px;
}

.payment-detail-table {
  padding: 0 24px 24px;
}
</style>
