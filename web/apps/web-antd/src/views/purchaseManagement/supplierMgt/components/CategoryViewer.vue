<script lang="ts">
import { computed, defineComponent } from 'vue';

export default defineComponent({
  name: 'CategoryViewer',
  props: {
    value: {
      type: [String, Object, Array],
      default: () => [],
    },
  },
  setup(props) {
    // 解析品类数据
    const categories = computed(() => {
      try {
        if (!props.value) return [];

        let categoryList: any[] = [];
        if (typeof props.value === 'string') {
          try {
            categoryList = JSON.parse(props.value);
          } catch {
            categoryList = [props.value]; // 如果解析失败，将字符串当作单个品类
          }
        } else if (Array.isArray(props.value)) {
          categoryList = props.value;
        } else if (typeof props.value === 'object') {
          categoryList = Object.values(props.value);
        } else {
          categoryList = [String(props.value)];
        }

        return categoryList.map(String);
      } catch (error) {
        console.error('解析品类数据失败', error);
        return [];
      }
    });

    // 根据品类名称获取对应的图标
    const getCategoryIcon = (category: string): string => {
      // 服务器类
      if (category.includes('服务器')) return '🖥️';

      // 处理器类
      if (category.includes('CPU') || category === 'cpu') return '⚙️';
      if (category.includes('GPU') || category === 'gpu') return '🔥';
      if (category.includes('GPU底板')) return '📟';

      // 存储类
      if (category.includes('硬盘')) return '💿';
      if (category.includes('SSD')) return '📀';
      if (category.includes('内存')) return '💾';
      if (category.includes('raid卡')) return '🗃️';

      // 网络类
      if (category.includes('网卡')) return '📶';
      if (category.includes('交换机') || category.includes('Switch板'))
        return '🔄';
      if (category.includes('路由器')) return '📡';
      if (category.includes('光模块')) return '💫';
      if (category.includes('AOC')) return '🔌';

      // 板类
      if (category.includes('主板')) return '🧩';
      if (category.includes('BMC板')) return '🎛️';
      if (category.includes('背板')) return '⛓️';

      // 机箱/电源/散热类
      if (category.includes('机箱')) return '📦';
      if (category.includes('电源模块')) return '🔋';
      if (category.includes('风扇') || category.includes('散热器')) return '❄️';

      // 连接类
      if (category.includes('线缆')) return '🧵';
      if (category.includes('跳线')) return '➿';

      // 服务类
      if (category.includes('维保')) return '🛡️';

      // 显示类
      if (category.includes('显卡')) return '📺';

      // 其他
      if (category.includes('其他')) return '📎';
      if (category.includes('测试')) return '🧪';

      // 默认图标
      return '🏷️';
    };

    // 根据品类名称获取对应的背景色
    const getCategoryColor = (category: string): string => {
      // 处理器类 - 蓝色系
      if (category.includes('CPU') || category === 'cpu') return '#f0f5ff';
      if (
        category.includes('GPU') ||
        category === 'gpu' ||
        category.includes('GPU底板')
      )
        return '#fff2e8';

      // 存储类 - 绿色系
      if (category.includes('硬盘') || category.includes('SSD'))
        return '#f6ffed';
      if (category.includes('内存')) return '#e6f7ff';
      if (category.includes('raid卡')) return '#f4ffb8';

      // 网络类 - 紫色系
      if (
        category.includes('网卡') ||
        category.includes('交换机') ||
        category.includes('Switch板') ||
        category.includes('路由器') ||
        category.includes('光模块') ||
        category.includes('AOC')
      )
        return '#f9f0ff';

      // 板类 - 黄色系
      if (
        category.includes('主板') ||
        category.includes('BMC板') ||
        category.includes('背板')
      )
        return '#fffbe6';

      // 机箱/电源/散热类 - 红色系
      if (
        category.includes('机箱') ||
        category.includes('电源模块') ||
        category.includes('风扇') ||
        category.includes('散热器')
      )
        return '#fff1f0';

      // 连接类 - 青色系
      if (category.includes('线缆') || category.includes('跳线'))
        return '#e6fffb';

      // 服务类 - 灰色系
      if (category.includes('维保')) return '#fafafa';

      // 显示类 - 橙色系
      if (category.includes('显卡')) return '#fff7e6';

      // 服务器 - 特殊蓝
      if (category.includes('服务器')) return '#e6f4ff';

      // 其他/测试 - 灰色系
      if (category.includes('其他') || category.includes('测试'))
        return '#f5f5f5';

      return '#f5f5f5'; // 默认灰色背景
    };

    // 根据品类名称获取对应的边框色
    const getCategoryBorderColor = (category: string): string => {
      // 处理器类 - 蓝色系
      if (category.includes('CPU') || category === 'cpu') return '#2f54eb';
      if (
        category.includes('GPU') ||
        category === 'gpu' ||
        category.includes('GPU底板')
      )
        return '#fa8c16';

      // 存储类 - 绿色系
      if (category.includes('硬盘') || category.includes('SSD'))
        return '#52c41a';
      if (category.includes('内存')) return '#1890ff';
      if (category.includes('raid卡')) return '#a0d911';

      // 网络类 - 紫色系
      if (
        category.includes('网卡') ||
        category.includes('交换机') ||
        category.includes('Switch板') ||
        category.includes('路由器') ||
        category.includes('光模块') ||
        category.includes('AOC')
      )
        return '#722ed1';

      // 板类 - 黄色系
      if (
        category.includes('主板') ||
        category.includes('BMC板') ||
        category.includes('背板')
      )
        return '#faad14';

      // 机箱/电源/散热类 - 红色系
      if (
        category.includes('机箱') ||
        category.includes('电源模块') ||
        category.includes('风扇') ||
        category.includes('散热器')
      )
        return '#f5222d';

      // 连接类 - 青色系
      if (category.includes('线缆') || category.includes('跳线'))
        return '#13c2c2';

      // 服务类 - 灰色系
      if (category.includes('维保')) return '#8c8c8c';

      // 显示类 - 橙色系
      if (category.includes('显卡')) return '#fa541c';

      // 服务器 - 特殊蓝
      if (category.includes('服务器')) return '#096dd9';

      // 其他/测试 - 灰色系
      if (category.includes('其他') || category.includes('测试'))
        return '#d9d9d9';

      return '#8c8c8c'; // 默认灰色边框
    };

    return {
      categories,
      getCategoryIcon,
      getCategoryColor,
      getCategoryBorderColor,
    };
  },
});
</script>

<template>
  <!-- 使用 Vue 组件安全显示内容，避免 v-html 的 XSS 风险 -->
  <div class="category-viewer">
    <!-- 空状态 -->
    <div v-if="categories.length === 0" class="empty-categories">
      暂无品类数据
    </div>

    <!-- 品类内容 -->
    <div v-else class="category-container">
      <!-- 品类汇总信息 -->
      <div class="category-summary">
        <span class="summary-icon">📂</span>
        <span class="summary-text"
          >共有 {{ categories.length }} 个供应品类</span
        >
      </div>

      <!-- 品类列表 -->
      <div
        v-for="(category, index) in categories"
        :key="index"
        class="category-item"
      >
        <div
          class="category-content"
          :style="{
            background: getCategoryColor(category),
            borderLeftColor: getCategoryBorderColor(category),
          }"
        >
          <div class="category-info">
            <span class="category-icon">{{ getCategoryIcon(category) }}</span>
            <span class="category-name">{{ category }}</span>
          </div>
          <div class="category-index">#{{ index + 1 }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.category-viewer {
  max-height: 350px;
  padding: 4px 0;
  overflow-y: auto;
}

.category-container {
  padding: 4px 0;
}

.category-summary {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: 500;
  background: #e6f7ff;
  border-left: 3px solid #1890ff;
  border-radius: 4px;
}

.summary-icon {
  margin-right: 4px;
}

.summary-text {
  color: #262626;
}

.category-item {
  margin-bottom: 6px;
  overflow: hidden;
  background: white;
  border: 1px solid #eee;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgb(0 0 0 / 3%);
}

.category-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 10px;
  border-left: 3px solid;
}

.category-info {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.category-icon {
  margin-right: 6px;
  font-size: 16px;
}

.category-name {
  color: #262626;
}

.category-index {
  padding: 1px 6px;
  font-size: 12px;
  color: #8c8c8c;
  background: rgb(0 0 0 / 3%);
  border-radius: 10px;
}

.empty-categories {
  padding: 8px;
  font-size: 13px;
  color: #999;
  text-align: center;
}
</style>
