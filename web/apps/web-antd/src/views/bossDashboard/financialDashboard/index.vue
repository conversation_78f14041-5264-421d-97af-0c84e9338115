<script lang="ts" setup>
import { ref } from 'vue';

const columns = [
  {
    title: '月份',
    dataIndex: 'month',
    key: 'month',
  },
  {
    title: '收入',
    dataIndex: 'income',
    key: 'income',
  },
  {
    title: '支出',
    dataIndex: 'expense',
    key: 'expense',
  },
  {
    title: '利润',
    dataIndex: 'profit',
    key: 'profit',
  },
  {
    title: '同比增长',
    dataIndex: 'growth',
    key: 'growth',
  },
];

const dataSource = ref([
  {
    key: '1',
    month: '2023-01',
    income: '¥2,100,000',
    expense: '¥1,050,000',
    profit: '¥1,050,000',
    growth: '+15%',
  },
  {
    key: '2',
    month: '2023-02',
    income: '¥2,300,000',
    expense: '¥1,150,000',
    profit: '¥1,150,000',
    growth: '+12%',
  },
  {
    key: '3',
    month: '2023-03',
    income: '¥2,400,000',
    expense: '¥1,200,000',
    profit: '¥1,200,000',
    growth: '+8%',
  },
  {
    key: '4',
    month: '2023-04',
    income: '¥2,500,000',
    expense: '¥1,250,000',
    profit: '¥1,250,000',
    growth: '+5%',
  },
  {
    key: '5',
    month: '2023-05',
    income: '¥2,560,000',
    expense: '¥1,280,000',
    profit: '¥1,280,000',
    growth: '+3%',
  },
]);
</script>

<template>
  <div class="financial-dashboard-container">
    <a-card title="财务看板" :bordered="false">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-statistic title="本月收入" value="¥2,560,000" />
        </a-col>
        <a-col :span="8">
          <a-statistic title="本月支出" value="¥1,280,000" />
        </a-col>
        <a-col :span="8">
          <a-statistic
            title="本月利润"
            value="¥1,280,000"
            :value-style="{ color: '#3f8600' }"
          />
        </a-col>
      </a-row>

      <a-divider />

      <h3>收入趋势</h3>
      <div class="chart-placeholder">
        <div class="chart-mock">
          <div class="bar" style="height: 60px"></div>
          <div class="bar" style="height: 80px"></div>
          <div class="bar" style="height: 40px"></div>
          <div class="bar" style="height: 70px"></div>
          <div class="bar" style="height: 90px"></div>
          <div class="bar" style="height: 100px"></div>
        </div>
        <div class="chart-label">
          图表区域 - 实际项目中应使用Echarts等图表库
        </div>
      </div>

      <a-divider />

      <h3>财务数据</h3>
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :pagination="{ pageSize: 5 }"
        :loading="false"
      />
    </a-card>
  </div>
</template>

<style lang="less" scoped>
.financial-dashboard-container {
  padding: 24px;

  .ant-card {
    margin-bottom: 24px;
  }

  .ant-statistic {
    margin-bottom: 16px;
  }

  h3 {
    margin: 16px 0;
  }

  .chart-placeholder {
    height: 200px;
    background-color: #f5f5f5;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;

    .chart-mock {
      display: flex;
      align-items: flex-end;
      height: 120px;
      width: 80%;

      .bar {
        flex: 1;
        background-color: #1890ff;
        margin: 0 8px;
        border-radius: 4px 4px 0 0;
      }
    }

    .chart-label {
      margin-top: 16px;
      color: #999;
    }
  }
}
</style>
