<script lang="ts" setup>
import type {
  BrandDistributionData,
  GpuModelDistributionData,
  RoomDistributionData,
} from '#/api/core/bossDashboard/fault-dashboard';
// 需要从API导入的类型
import type {
  ClusterStatsData,
  DailyImpactTrendData,
  DurationStatsData,
  EngineerStatsData,
  FaultDetailTypeStatsData,
  FaultDeviceBrandDistributionData,
  FaultTypeStatsData,
  GPUFaultRatioData,
  HardwareFaultTrendData,
  ProjectOption,
  ServerStatsData,
  SourceStatsData,
  TimeRangeParams,
  TotalFaultDevicesData,
} from '#/api/core/bossDashboard/sla-dashboard';

import { onBeforeUnmount, onMounted, reactive, ref } from 'vue';

import {
  Card,
  Col,
  DatePicker,
  message,
  Row,
  Select,
  Switch,
  Tooltip,
} from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  getBrandDistribution,
  getGpuModelDistribution,
  getRoomDistribution,
} from '#/api/core/bossDashboard/fault-dashboard';
// 导入API函数
import {
  getClusterStats,
  getDailyImpactTrend,
  getDurationStats,
  getEngineerStats,
  getFaultDetailTypeStats,
  getFaultDeviceBrandDistribution,
  getFaultTicketDetails,
  getFaultTypeStats,
  getGPUFaultRatio,
  getHardwareFaultTrend,
  getProjectList,
  getServerStats,
  getSourceStats,
  getTotalFaultDevices,
} from '#/api/core/bossDashboard/sla-dashboard';

// 导入新增的三个组件
import FaultDetailTypeChart from '../slaDashboard/components/fault-detail-type-chart.vue';
import FaultTypeChart from '../slaDashboard/components/fault-type-chart.vue';
import GpuFaultRatioCard from '../slaDashboard/components/gpu-fault-ratio-card.vue';
// 导入项目资源概览组件
import ProjectResourceCard from '../slaDashboard/components/project-resource-card.vue';
import SourceStatsChart from '../slaDashboard/components/source-stats-chart.vue';
// 导入新的统计图表组件
import BrandDistributionChart from './components/brand-distribution-chart.vue';
import DailyImpactTrendChart from './components/daily-impact-trend-chart.vue';
import FaultDeviceBrandChart from './components/fault-device-brand-chart.vue';
import FaultOverviewCard from './components/fault-overview-card.vue';
import GpuModelChart from './components/gpu-model-chart.vue';
import HardwareEngineerStatsChart from './components/hardware-engineer-stats-chart.vue';
import HardwareFaultTrendChart from './components/hardware-fault-trend-chart.vue';
import RepairTimeCard from './components/repair-time-card.vue';
// 导入新增的响应时长和修复时长组件
import ResponseTimeCard from './components/response-time-card.vue';
import RoomDistributionChart from './components/room-distribution-chart.vue';
import SoftwareEngineerStatsChart from './components/software-engineer-stats-chart.vue';

// 加载状态
const loading = reactive({
  server: false,
  cluster: false,
  projectList: false,
  brandDistribution: false,
  roomDistribution: false,
  gpuModel: false,
  totalDevices: false,
  durationStats: false,
  repairMethod: false,
  engineerStats: false,
  sourceStats: false,
  faultDetailType: false,
  gpuFaultRatio: false,
  faultType: false,
  faultDeviceBrand: false,
  dailyImpactTrend: false,
  hardwareFaultTrend: false,
});

// 图表数据
const serverStatsData = ref<ServerStatsData>({} as ServerStatsData);
const clusterStatsData = ref<ClusterStatsData>({} as ClusterStatsData);
const projectOptions = ref<ProjectOption[]>([]);

// 新添加的图表数据
const brandDistributionData = ref<BrandDistributionData>(
  {} as BrandDistributionData,
);
const roomDistributionData = ref<RoomDistributionData>(
  {} as RoomDistributionData,
);
const gpuModelData = ref<GpuModelDistributionData>(
  {} as GpuModelDistributionData,
);

// 添加故障总览相关数据
const totalDevicesData = ref<TotalFaultDevicesData>(
  {} as TotalFaultDevicesData,
);
const durationStatsData = ref<DurationStatsData>({} as DurationStatsData);
const engineerStatsData = ref<EngineerStatsData>({} as EngineerStatsData);

// 添加新组件所需的数据
const sourceStatsData = ref<SourceStatsData>({} as SourceStatsData);
const faultDetailTypeData = ref<FaultDetailTypeStatsData>(
  {} as FaultDetailTypeStatsData,
);
const gpuFaultRatioData = ref<GPUFaultRatioData>({} as GPUFaultRatioData);
const faultTypeData = ref<FaultTypeStatsData>({} as FaultTypeStatsData);
const faultDeviceBrandData = ref<FaultDeviceBrandDistributionData>(
  {} as FaultDeviceBrandDistributionData,
);
const dailyImpactTrendData = ref<DailyImpactTrendData>(
  {} as DailyImpactTrendData,
);
const hardwareFaultTrendData = ref<HardwareFaultTrendData>(
  {} as HardwareFaultTrendData,
);

// 添加维修方式统计数据
const repairMethodStats = ref({
  hardwareRepair: 0,
  softwareRepair: 0,
  total: 0,
});

// 图表组件引用
const brandChartRef = ref(null);
const roomChartRef = ref(null);
const gpuModelChartRef = ref(null);

// 当前选择的年月
const selectedYearMonth = ref(dayjs());

// 搜索参数
const searchParams = reactive<TimeRangeParams>({
  time_range: 'month',
  group_by: 'day',
  project: 'cloud17',
  count_in_sla: false, // 确保在所有查询中默认设为false
});

// 时间范围选项
const timeRangeOptions = [
  { label: '今日', value: 'today' },
  { label: '本周', value: 'week' },
  { label: '本月', value: 'month' },
  { label: '本年', value: 'year' },
  { label: '自定义', value: 'custom' },
];

// 分组方式选项
const groupByOptions = [
  { label: '按天', value: 'day' },
  { label: '按周', value: 'week' },
  { label: '按月', value: 'month' },
];

// 获取项目列表
async function fetchProjectList() {
  try {
    loading.projectList = true;
    const res = await getProjectList();

    // 将字符串数组转换为对象数组格式
    projectOptions.value = res.map((project: string) => ({
      label: project,
      value: project,
    }));
    // 添加一个全部选项
    projectOptions.value.unshift({ label: '全部', value: '' });
  } catch (error) {
    console.error('获取项目列表失败', error);
  } finally {
    loading.projectList = false;
  }
}

// 获取服务器统计数据
async function fetchServerStats() {
  try {
    loading.server = true;
    const res = await getServerStats(searchParams);
    serverStatsData.value = res;
  } catch (error) {
    console.error('获取服务器统计数据失败', error);
  } finally {
    loading.server = false;
  }
}

// 获取集群统计数据
async function fetchClusterStats() {
  try {
    loading.cluster = true;
    const res = await getClusterStats(searchParams);
    clusterStatsData.value = res;
  } catch (error) {
    console.error('获取集群统计数据失败', error);
  } finally {
    loading.cluster = false;
  }
}

// 获取品牌分布数据
async function fetchBrandDistribution() {
  try {
    loading.brandDistribution = true;
    const res = await getBrandDistribution(searchParams);
    brandDistributionData.value = res;
  } catch (error) {
    console.error('获取品牌分布数据失败', error);
  } finally {
    loading.brandDistribution = false;
  }
}

// 获取机房分布数据
async function fetchRoomDistribution() {
  try {
    loading.roomDistribution = true;
    const res = await getRoomDistribution(searchParams);
    roomDistributionData.value = res;
  } catch (error) {
    console.error('获取机房分布数据失败', error);
  } finally {
    loading.roomDistribution = false;
  }
}

// 获取GPU卡型号分布数据
async function fetchGpuModelDistribution() {
  try {
    loading.gpuModel = true;
    const res = await getGpuModelDistribution(searchParams);
    gpuModelData.value = res;
  } catch (error) {
    console.error('获取GPU卡型号分布数据失败', error);
  } finally {
    loading.gpuModel = false;
  }
}

// 获取故障总台数数据
async function fetchTotalFaultDevices() {
  try {
    loading.totalDevices = true;
    const res = await getTotalFaultDevices(searchParams);
    totalDevicesData.value = res;
  } catch (error) {
    console.error('获取故障总台数数据失败', error);
  } finally {
    loading.totalDevices = false;
  }
}

// 获取处理时长统计数据
async function fetchDurationStats() {
  try {
    loading.durationStats = true;
    const res = await getDurationStats(searchParams);
    durationStatsData.value = res;
  } catch (error) {
    console.error('获取处理时长统计数据失败', error);
  } finally {
    loading.durationStats = false;
  }
}

// 获取维修方式统计数据
async function fetchRepairMethodStats() {
  if (!searchParams.project) {
    return;
  }

  try {
    loading.repairMethod = true;
    const res = await getFaultTicketDetails(searchParams);

    // 统计维修方式
    let hardwareRepair = 0;
    let softwareRepair = 0;

    if (res.details && Array.isArray(res.details)) {
      res.details.forEach((ticket) => {
        if (ticket.repair_method === 'hardware_fix') {
          hardwareRepair++;
        } else {
          softwareRepair++;
        }
      });

      repairMethodStats.value = {
        hardwareRepair,
        softwareRepair,
        total: res.details.length,
      };
    }
  } catch (error) {
    console.error('获取维修方式统计失败', error);
  } finally {
    loading.repairMethod = false;
  }
}

// 获取工程师统计数据
async function fetchEngineerStats() {
  if (!searchParams.project) {
    return;
  }

  try {
    loading.engineerStats = true;
    const res = await getEngineerStats(searchParams);
    engineerStatsData.value = res;
  } catch (error) {
    console.error('获取工程师统计数据失败', error);
  } finally {
    loading.engineerStats = false;
  }
}

// 获取来源统计数据
async function fetchSourceStats() {
  try {
    loading.sourceStats = true;
    const res = await getSourceStats(searchParams);
    sourceStatsData.value = res;
  } catch (error) {
    console.error('获取故障来源统计数据失败', error);
  } finally {
    loading.sourceStats = false;
  }
}

// 获取故障详细类型统计数据
async function fetchFaultDetailTypeStats() {
  try {
    loading.faultDetailType = true;
    const res = await getFaultDetailTypeStats(searchParams);
    faultDetailTypeData.value = res;
  } catch (error) {
    console.error('获取故障详细类型统计数据失败', error);
  } finally {
    loading.faultDetailType = false;
  }
}

// 获取GPU故障比率数据
async function fetchGpuFaultRatio() {
  try {
    loading.gpuFaultRatio = true;
    const res = await getGPUFaultRatio(searchParams);
    gpuFaultRatioData.value = res;
  } catch (error) {
    console.error('获取GPU故障比率数据失败', error);
  } finally {
    loading.gpuFaultRatio = false;
  }
}

// 获取故障类型统计数据
async function fetchFaultTypeStats() {
  try {
    loading.faultType = true;
    const res = await getFaultTypeStats(searchParams);
    faultTypeData.value = res;
  } catch (error) {
    console.error('获取故障类型统计数据失败', error);
  } finally {
    loading.faultType = false;
  }
}

// 获取故障设备厂商分布数据
async function fetchFaultDeviceBrandDistribution() {
  try {
    loading.faultDeviceBrand = true;
    const res = await getFaultDeviceBrandDistribution(searchParams);
    faultDeviceBrandData.value = res;
  } catch (error) {
    console.error('获取故障设备厂商分布数据失败', error);
  } finally {
    loading.faultDeviceBrand = false;
  }
}

// 获取每天故障处理总时长趋势统计数据
async function fetchDailyImpactTrend() {
  try {
    loading.dailyImpactTrend = true;
    const res = await getDailyImpactTrend(searchParams);
    dailyImpactTrendData.value = res;
  } catch (error) {
    console.error('获取每天故障处理总时长趋势统计数据失败', error);
  } finally {
    loading.dailyImpactTrend = false;
  }
}

// 获取每天硬件故障次数趋势统计数据
async function fetchHardwareFaultTrend() {
  try {
    loading.hardwareFaultTrend = true;
    const res = await getHardwareFaultTrend(searchParams);
    hardwareFaultTrendData.value = res;
  } catch (error) {
    console.error('获取每天硬件故障次数趋势统计数据失败', error);
  } finally {
    loading.hardwareFaultTrend = false;
  }
}

// 处理年月选择变更
function handleYearMonthChange(
  date: dayjs.Dayjs | null | string,
  _dateString?: string,
): void {
  if (date) {
    selectedYearMonth.value = typeof date === 'string' ? dayjs(date) : date;

    // 设置月份的开始日期和结束日期
    const monthStart = selectedYearMonth.value
      .startOf('month')
      .format('YYYY-MM-DD');
    const monthEnd = selectedYearMonth.value
      .endOf('month')
      .format('YYYY-MM-DD');

    // 更新搜索参数
    searchParams.start_date = monthStart;
    searchParams.end_date = monthEnd;

    // 更改时间范围为自定义
    searchParams.time_range = 'custom';

    // 获取数据
    fetchAllData();
  }
}

// 搜索条件变更
function handleSearchParamsChange() {
  // 确保日期格式正确
  if (
    searchParams.time_range === 'custom' &&
    (!searchParams.start_date || !searchParams.end_date)
  ) {
    message.warning('请选择开始和结束日期');
    return;
  }
  fetchAllData();
}

// 日期变更处理函数
function handleDateChange(
  _date: any,
  dateString: string,
  type: 'end' | 'start',
): void {
  if (type === 'start') {
    searchParams.start_date = dateString;
  } else {
    searchParams.end_date = dateString;
  }

  if (searchParams.start_date && searchParams.end_date) {
    fetchAllData();
  }
}

// 获取所有数据
function fetchAllData() {
  fetchServerStats();
  fetchClusterStats();
  fetchBrandDistribution();
  fetchRoomDistribution();
  fetchGpuModelDistribution();
  fetchTotalFaultDevices();
  fetchDurationStats();
  fetchRepairMethodStats();
  fetchEngineerStats();
  fetchSourceStats();
  fetchFaultDetailTypeStats();
  fetchGpuFaultRatio();
  fetchFaultTypeStats();
  fetchFaultDeviceBrandDistribution();
  fetchDailyImpactTrend();
  fetchHardwareFaultTrend();
}

// 页面加载时获取数据
onMounted(() => {
  fetchProjectList();
  fetchAllData();
});

// 组件卸载时清理
onBeforeUnmount(() => {});
</script>

<template>
  <div class="fault-dashboard">
    <div class="filter-container">
      <Card title="搜索条件" :bordered="false">
        <Row :gutter="16">
          <Col :span="5">
            <div class="filter-item">
              <span class="label">时间范围：</span>
              <Select
                v-model:value="searchParams.time_range"
                style="width: 120px"
                @change="handleSearchParamsChange"
              >
                <Select.Option
                  v-for="item in timeRangeOptions"
                  :key="item.value"
                  :value="item.value"
                >
                  {{ item.label }}
                </Select.Option>
              </Select>
            </div>
          </Col>

          <Col :span="5" v-if="searchParams.time_range === 'custom'">
            <div class="filter-item">
              <span class="label">开始日期：</span>
              <DatePicker
                v-model:value="searchParams.start_date"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="选择开始日期"
                :disabled-date="(current) => current > dayjs().endOf('day')"
                @change="
                  (date, dateString) =>
                    handleDateChange(date, dateString, 'start')
                "
              />
            </div>
          </Col>

          <Col :span="5" v-if="searchParams.time_range === 'custom'">
            <div class="filter-item">
              <span class="label">结束日期：</span>
              <DatePicker
                v-model:value="searchParams.end_date"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="选择结束日期"
                :disabled-date="(current) => current > dayjs().endOf('day')"
                @change="
                  (date, dateString) =>
                    handleDateChange(date, dateString, 'end')
                "
              />
            </div>
          </Col>

          <Col :span="5">
            <div class="filter-item">
              <span class="label">分组方式：</span>
              <Select
                v-model:value="searchParams.group_by"
                style="width: 120px"
                @change="handleSearchParamsChange"
              >
                <Select.Option
                  v-for="item in groupByOptions"
                  :key="item.value"
                  :value="item.value"
                >
                  {{ item.label }}
                </Select.Option>
              </Select>
            </div>
          </Col>

          <Col :span="5">
            <div class="filter-item">
              <span class="label">项目：</span>
              <Select
                v-model:value="searchParams.project"
                style="width: 160px"
                :loading="loading.projectList"
                placeholder="请选择项目"
                @change="handleSearchParamsChange"
              >
                <Select.Option
                  v-for="item in projectOptions"
                  :key="item.value"
                  :value="item.value"
                >
                  {{ item.label }}
                </Select.Option>
              </Select>
            </div>
          </Col>

          <Col :span="4">
            <div class="filter-item">
              <span class="label">年月：</span>
              <DatePicker
                v-model:value="selectedYearMonth"
                format="YYYY-MM"
                value-format="YYYY-MM"
                placeholder="选择年月"
                picker="month"
                :disabled-date="(current) => current > dayjs().endOf('day')"
                @change="handleYearMonthChange"
              />
            </div>
          </Col>

          <Col :span="5">
            <div class="filter-item">
              <span class="label">计入SLA：</span>
              <Switch
                v-model:checked="searchParams.count_in_sla"
                @change="handleSearchParamsChange"
                checked-children="是"
                un-checked-children="否"
                :loading="false"
                class="sla-switch"
              />
              <Tooltip title="选择是否将数据计入SLA统计">
                <span class="help-icon">?</span>
              </Tooltip>
            </div>
          </Col>
        </Row>
      </Card>
    </div>

    <!-- 项目资源统计 -->
    <Row :gutter="24" class="dashboard-row" v-if="searchParams.project">
      <Col :span="24">
        <ProjectResourceCard
          :server-loading="loading.server"
          :cluster-loading="loading.cluster"
          :server-data="serverStatsData"
          :cluster-data="clusterStatsData"
          :project="searchParams.project"
        />
      </Col>
    </Row>

    <!-- GPU服务器分布统计 -->
    <Row :gutter="24" class="dashboard-row">
      <!-- 品牌分布 -->
      <Col :span="8">
        <Card title="GPU服务器品牌分布" :bordered="false" class="chart-card">
          <BrandDistributionChart
            ref="brandChartRef"
            :loading="loading.brandDistribution"
            :data="brandDistributionData"
            :key="`brand-${brandDistributionData?.chart_data?.length || 0}`"
          />
        </Card>
      </Col>

      <!-- 机房分布 -->
      <Col :span="8">
        <Card title="GPU服务器包间分布" :bordered="false" class="chart-card">
          <RoomDistributionChart
            ref="roomChartRef"
            :loading="loading.roomDistribution"
            :data="roomDistributionData"
            :key="`room-${roomDistributionData?.chart_data?.length || 0}`"
          />
        </Card>
      </Col>

      <!-- GPU卡型号分布 -->
      <Col :span="8">
        <Card title="GPU卡型号分布" :bordered="false" class="chart-card">
          <GpuModelChart
            ref="gpuModelChartRef"
            :loading="loading.gpuModel"
            :data="gpuModelData"
            :key="`gpu-${gpuModelData?.resource_gpu_model_counts?.length || 0}`"
          />
        </Card>
      </Col>
    </Row>

    <!-- 故障总览统计组件，放置在GPU分布下方 -->
    <Row :gutter="24" class="dashboard-row">
      <Col :span="24">
        <FaultOverviewCard
          :total-devices-data="totalDevicesData"
          :duration-data="durationStatsData"
          :repair-stats="repairMethodStats"
          :gpu-fault-ratio-data="gpuFaultRatioData"
          :loading="
            loading.totalDevices ||
            loading.durationStats ||
            loading.repairMethod ||
            loading.gpuFaultRatio
          "
        />
      </Col>
    </Row>

    <!-- 新增的响应时长和修复时长统计卡片 -->
    <Row :gutter="24" class="dashboard-row">
      <Col :span="12">
        <ResponseTimeCard
          :loading="loading.durationStats"
          :data="durationStatsData"
        />
      </Col>
      <Col :span="12">
        <RepairTimeCard
          :loading="loading.durationStats"
          :data="durationStatsData"
        />
      </Col>
    </Row>

    <!-- 添加工程师响应和修复时长统计图表 -->
    <Row :gutter="24" class="dashboard-row">
      <Col :span="12">
        <Card
          title="软件人均响应和修复时长统计"
          :bordered="false"
          class="chart-card"
        >
          <SoftwareEngineerStatsChart
            :loading="loading.engineerStats"
            :data="engineerStatsData"
            :key="`software-engineer-${JSON.stringify(engineerStatsData?.stats?.filter((item) => item.ticket_type === 'software') || [])}`"
          />
        </Card>
      </Col>
      <Col :span="12">
        <Card
          title="硬件人均响应和修复时长统计"
          :bordered="false"
          class="chart-card"
        >
          <HardwareEngineerStatsChart
            :loading="loading.engineerStats"
            :data="engineerStatsData"
            :key="`hardware-engineer-${JSON.stringify(engineerStatsData?.stats?.filter((item) => item.ticket_type === 'hardware') || [])}`"
          />
        </Card>
      </Col>
    </Row>

    <!-- 添加来源分布、故障具体类型和故障厂商分布放在一行 -->
    <Row :gutter="24" class="dashboard-row">
      <Col :span="8">
        <Card title="故障来源分布" :bordered="false" class="chart-card">
          <SourceStatsChart
            :loading="loading.sourceStats"
            :data="sourceStatsData"
          />
        </Card>
      </Col>
      <Col :span="8">
        <Card title="故障点类型分布" :bordered="false" class="chart-card">
          <FaultDetailTypeChart
            :loading="loading.faultDetailType"
            :data="faultDetailTypeData"
          />
        </Card>
      </Col>
      <Col :span="8">
        <Card title="故障设备厂商分布" :bordered="false" class="chart-card">
          <FaultDeviceBrandChart
            :loading="loading.faultDeviceBrand"
            :data="faultDeviceBrandData"
          />
        </Card>
      </Col>
    </Row>

    <!-- 添加故障类型分布和GPU故障比率组件 -->
    <Row :gutter="24" class="dashboard-row">
      <Col :span="12">
        <GpuFaultRatioCard
          :loading="loading.gpuFaultRatio"
          :data="gpuFaultRatioData"
          class="chart-card"
        />
      </Col>
      <Col :span="12">
        <Card title="故障类型分布" :bordered="false" class="chart-card">
          <FaultTypeChart :loading="loading.faultType" :data="faultTypeData" />
        </Card>
      </Col>
    </Row>

    <!-- 添加每天故障处理总时长趋势统计组件 -->
    <Row :gutter="24" class="dashboard-row">
      <Col :span="24">
        <Card
          title="故障处理总时长与报障单数量趋势"
          :bordered="false"
          class="chart-card"
        >
          <DailyImpactTrendChart
            :loading="loading.dailyImpactTrend"
            :data="dailyImpactTrendData"
          />
        </Card>
      </Col>
    </Row>

    <!-- 添加每天硬件故障次数趋势统计组件 -->
    <Row :gutter="24" class="dashboard-row">
      <Col :span="24">
        <Card title="硬件故障次数趋势" :bordered="false" class="chart-card">
          <HardwareFaultTrendChart
            :loading="loading.hardwareFaultTrend"
            :data="hardwareFaultTrendData"
          />
        </Card>
      </Col>
    </Row>
  </div>
</template>

<style lang="less" scoped>
.fault-dashboard {
  padding: 24px;

  .filter-container {
    margin-bottom: 24px;

    :deep(.ant-card-body) {
      padding: 20px;
    }

    .ant-row {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .filter-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .label {
        margin-right: 8px;
        white-space: nowrap;
      }

      .sla-switch {
        margin-right: 8px;
      }

      .help-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #e6f7ff;
        color: #1890ff;
        font-size: 12px;
        cursor: pointer;
      }
    }
  }

  .dashboard-row {
    margin-bottom: 24px;
  }

  .chart-card {
    height: 450px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.09);
    }

    :deep(.ant-card-head) {
      padding: 0 16px;
      min-height: 48px;
      border-bottom: 1px solid #f0f0f0;

      .ant-card-head-title {
        padding: 12px 0;
        font-size: 16px;
        font-weight: 500;
      }
    }

    :deep(.ant-card-body) {
      padding: 16px;
      height: calc(100% - 48px);
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
  }
}
</style>
