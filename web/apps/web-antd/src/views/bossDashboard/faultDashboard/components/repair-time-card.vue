<script lang="ts" setup>
import type { DurationStatsData } from '#/api/core/bossDashboard/sla-dashboard';

import { computed } from 'vue';

import { Card, Col, Row, Spin, Statistic } from 'ant-design-vue';

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object as () => DurationStatsData,
    default: () => ({
      phases: [],
      avgTimes: [],
      maxTimes: [],
      minTimes: [],
    }),
  },
});

// 计算软件处理时长和硬件维修时长索引
const softwareRepairIndex = computed(() => {
  return (
    props.data?.phases?.findIndex((phase) => phase === '软件处理时长') ?? -1
  );
});

const hardwareRepairIndex = computed(() => {
  return (
    props.data?.phases?.findIndex((phase) => phase === '硬件维修时长') ?? -1
  );
});

// 计算获取数据
const softwareRepairAvg = computed(() => {
  if (softwareRepairIndex.value >= 0 && props.data?.avgTimes) {
    return props.data.avgTimes[softwareRepairIndex.value]?.toFixed(2) || '0';
  }
  return '0';
});

const softwareRepairMax = computed(() => {
  if (softwareRepairIndex.value >= 0 && props.data?.maxTimes) {
    return props.data.maxTimes[softwareRepairIndex.value] || 0;
  }
  return 0;
});

const hardwareRepairAvg = computed(() => {
  if (hardwareRepairIndex.value >= 0 && props.data?.avgTimes) {
    return props.data.avgTimes[hardwareRepairIndex.value]?.toFixed(2) || '0';
  }
  return '0';
});

const hardwareRepairMax = computed(() => {
  if (hardwareRepairIndex.value >= 0 && props.data?.maxTimes) {
    return props.data.maxTimes[hardwareRepairIndex.value] || 0;
  }
  return 0;
});

// 格式化时间（分钟转换为小时和分钟）
const formatTime = (minutes: number) => {
  if (minutes <= 0) return '0分钟';

  const hours = Math.floor(minutes / 60);
  const mins = Math.floor(minutes % 60);

  if (hours > 0) {
    return `${hours}小时${mins > 0 ? `${mins}分钟` : ''}`;
  }
  return `${mins}分钟`;
};
</script>

<template>
  <Card title="修复时长统计" :bordered="false" :loading="loading">
    <Spin :spinning="loading">
      <Row :gutter="16">
        <Col :span="12">
          <Card class="inner-card">
            <div class="stat-title">软件平均修复时长</div>
            <Statistic
              :value="softwareRepairAvg"
              suffix="分钟"
              :precision="2"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <svg
                  class="stat-icon"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path
                    d="M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z"
                  />
                </svg>
              </template>
            </Statistic>
            <div class="stat-description">
              最长修复时间: {{ formatTime(softwareRepairMax) }}
            </div>
          </Card>
        </Col>
        <Col :span="12">
          <Card class="inner-card">
            <div class="stat-title">硬件平均维修时长</div>
            <Statistic
              :value="hardwareRepairAvg"
              suffix="分钟"
              :precision="2"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <svg
                  class="stat-icon"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path
                    d="M18 16h-2v-1h-2v1h-2v-1H8v1H6v-1H4v1H2v2h2v1h2v-1h2v1h2v-1h4v1h2v-1h2v1h2v-2h-2v-1zm-9-9H7V5H5v2H3v2h2v2h2V9h2V7zm7 4h-2v2h2v2h2v-2h2v-2h-2v-2h-2v2z"
                  />
                </svg>
              </template>
            </Statistic>
            <div class="stat-description">
              最长维修时间: {{ formatTime(hardwareRepairMax) }}
            </div>
          </Card>
        </Col>
      </Row>
    </Spin>
  </Card>
</template>

<style lang="less" scoped>
.inner-card {
  background-color: #f9f9f9;
  border-radius: 8px;

  :deep(.ant-card-body) {
    padding: 16px;
  }
}

.stat-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #333;
}

.stat-description {
  margin-top: 8px;
  color: #999;
  font-size: 12px;
}

.stat-icon {
  margin-right: 8px;
  font-size: 18px;
}
</style>
