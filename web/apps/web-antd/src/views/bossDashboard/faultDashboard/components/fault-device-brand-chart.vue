<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { FaultDeviceBrandDistributionData } from '#/api/core/bossDashboard/sla-dashboard';

import { computed, onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

const props = defineProps<{
  data?: FaultDeviceBrandDistributionData;
  loading?: boolean;
}>();

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

function renderChart() {
  if (
    !chartRef.value ||
    !props.data ||
    !props.data.legend ||
    !props.data.series
  )
    return;

  renderEcharts({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderColor: '#e6e6e6',
      borderWidth: 1,
      textStyle: {
        color: '#333',
      },
      padding: [8, 12],
    },
    legend: {
      type: 'scroll',
      orient: 'horizontal',
      bottom: 10,
      itemWidth: 12,
      itemHeight: 12,
      textStyle: {
        fontSize: 11,
      },
      pageTextStyle: {
        color: '#666',
      },
      data: props.data.legend,
    },
    series: props.data.series.map((item) => ({
      ...item,
      radius: ['40%', '70%'],
      center: ['50%', '42%'],
      avoidLabelOverlap: true,
      itemStyle: {
        borderRadius: 6,
        borderColor: '#fff',
        borderWidth: 2,
        shadowBlur: 5,
        shadowColor: 'rgba(0, 0, 0, 0.1)',
      },
      label: {
        show: true,
        formatter: '{b}: {c}',
        fontSize: 11,
        fontWeight: 'bold',
        lineHeight: 14,
        padding: [4, 2],
      },
      labelLine: {
        show: true,
        length: 15,
        length2: 10,
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.3)',
        },
        label: {
          fontSize: 12,
        },
      },
    })),
  } as any);
}

watch(
  () => props.data,
  () => {
    renderChart();
  },
  { deep: true },
);

watch(
  () => props.loading,
  () => {
    if (!props.loading) {
      renderChart();
    }
  },
);

onMounted(() => {
  renderChart();
});

const totalTickets = computed(() => {
  return props.data?.total_devices || 0;
});
</script>

<template>
  <div class="fault-device-brand-chart-container">
    <div class="chart-header">
      <div class="total-info">
        <div class="total-value">{{ totalTickets }}</div>
        <div class="total-label">总故障设备数</div>
      </div>
    </div>
    <EchartsUI ref="chartRef" :height="totalTickets ? '300px' : '320px'" />
  </div>
</template>

<style lang="less" scoped>
.fault-device-brand-chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: center;
  margin-bottom: 12px;
}

.total-info {
  text-align: center;
  background: #f9f9f9;
  border-radius: 20px;
  padding: 6px 16px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.total-value {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
  line-height: 1.2;
}

.total-label {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}
</style>
