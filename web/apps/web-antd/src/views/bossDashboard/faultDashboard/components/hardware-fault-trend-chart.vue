<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { HardwareFaultTrendData } from '#/api/core/bossDashboard/sla-dashboard';

import { computed, onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

const props = defineProps<{
  data?: HardwareFaultTrendData;
  loading?: boolean;
}>();

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

function renderChart() {
  if (!chartRef.value || !props.data || !props.data.series || !props.data.xAxis)
    return;

  // 获取最大故障次数用于计算Y轴刻度
  const faultData = props.data?.series?.[0]?.data;
  const maxFaultCount = faultData ? Math.max(...faultData) : 10;

  // 设置渐变色
  const gradientColor = {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      { offset: 0, color: 'rgba(250, 173, 20, 0.8)' },
      { offset: 0.8, color: 'rgba(250, 173, 20, 0.2)' },
    ],
  };

  renderEcharts({
    color: ['#faad14'],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#eee',
      borderWidth: 1,
      textStyle: {
        color: '#333',
      },
      padding: [8, 12],
      formatter: (params: any) => {
        const date = params[0].name;
        const value = params[0].value;
        return `<div style="font-weight:bold;margin-bottom:5px">${date}</div>
                <div style="display:flex;align-items:center">
                  <span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background-color:#faad14"></span>
                  <span style="margin-right:15px;flex:1">硬件故障次数:</span>
                  <span style="font-weight:bold">${value} 次</span>
                </div>`;
      },
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '12%',
      top: '14%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: props.data.xAxis,
      boundaryGap: false,
      axisLine: {
        lineStyle: {
          color: '#ddd',
        },
      },
      axisLabel: {
        rotate: 45,
        interval: 'auto',
        fontSize: 11,
        color: '#666',
      },
      axisTick: {
        alignWithLabel: true,
      },
    },
    yAxis: {
      type: 'value',
      name: '硬件故障次数',
      min: 0,
      max: maxFaultCount ? Math.max(Math.ceil(maxFaultCount * 1.2), 5) : 5,
      axisLabel: {
        formatter: '{value}',
        fontSize: 11,
        color: '#666',
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#eee',
        },
      },
    },
    series: props.data.series.map((item) => ({
      ...item,
      smooth: true,
      symbol: 'emptyCircle',
      symbolSize: 8,
      lineStyle: {
        width: 3,
        shadowColor: 'rgba(0,0,0,0.2)',
        shadowBlur: 10,
        shadowOffsetY: 8,
        color: '#faad14',
      },
      itemStyle: {
        borderWidth: 2,
        borderColor: '#faad14',
        color: '#fff',
      },
      emphasis: {
        itemStyle: {
          borderWidth: 4,
          shadowColor: 'rgba(250, 173, 20, 0.5)',
          shadowBlur: 10,
        },
      },
      areaStyle: {
        color: gradientColor,
        opacity: 1,
      },
      markLine: {
        data: [
          {
            type: 'average',
            name: '平均值',
            lineStyle: {
              color: '#ff7a45',
              type: 'dashed',
              width: 1,
            },
            label: {
              position: 'end',
              formatter: '平均: {c}',
              color: '#ff7a45',
              fontSize: 12,
            },
          },
        ],
      },
    })) as any,
  });
}

watch(
  () => props.data,
  () => {
    renderChart();
  },
  { deep: true },
);

watch(
  () => props.loading,
  () => {
    if (!props.loading) {
      renderChart();
    }
  },
);

onMounted(() => {
  renderChart();
});

// 计算总硬件故障数
const totalHardwareFaults = computed(() => {
  return props.data?.total_tickets || 0;
});
</script>

<template>
  <div class="hardware-fault-trend-chart-container">
    <div class="chart-header">
      <div class="stat-item">
        <div class="stat-value">{{ totalHardwareFaults }}</div>
        <div class="stat-label">硬件故障总数</div>
      </div>
    </div>
    <EchartsUI ref="chartRef" height="450px" />
  </div>
</template>

<style lang="less" scoped>
.hardware-fault-trend-chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  background: linear-gradient(135deg, #fff7e6 0%, #fff1d6 100%);
  border-radius: 10px;
  padding: 10px 24px;
  min-width: 180px;
  box-shadow: 0 4px 12px rgba(250, 173, 20, 0.15);
  transition: all 0.3s ease;
  border: 1px solid rgba(250, 173, 20, 0.1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(250, 173, 20, 0.2);
  }
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #faad14;
  line-height: 1.2;
}

.stat-label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  margin-top: 6px;
}
</style>
