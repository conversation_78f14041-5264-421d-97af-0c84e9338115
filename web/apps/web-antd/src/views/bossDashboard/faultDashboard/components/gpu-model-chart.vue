<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { GpuModelDistributionData } from '#/api/core/bossDashboard/fault-dashboard';

import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { Empty, Spin } from 'ant-design-vue';

const props = defineProps<{
  data: GpuModelDistributionData;
  loading: boolean;
}>();

const chartRef = ref<EchartsUIType>();
const { renderEcharts, resize } = useEcharts(chartRef);

// 图表数据 - 使用resource_gpu_model_counts
const chartData = computed(() => {
  if (!props.data || !props.data.resource_gpu_model_counts) {
    return [];
  }
  return props.data.resource_gpu_model_counts.map((item) => ({
    name: item.gpu_model,
    value: item.count,
  }));
});

// GPU型号列表
const modelList = computed(() => {
  if (!props.data || !props.data.resource_gpu_model_counts) {
    return [];
  }
  return props.data.resource_gpu_model_counts;
});

// 总GPU型号数
const totalModels = computed(() => {
  if (!props.data || !props.data.resource_gpu_model_counts) {
    return 0;
  }
  return props.data.resource_gpu_model_counts.length;
});

// 是否有数据
const hasData = computed(() => {
  return chartData.value.length > 0;
});

// 处理窗口大小变化
function handleResize() {
  resize();
}

// 渲染图表
function renderChart() {
  if (!chartRef.value || !hasData.value) {
    return;
  }

  const colors = [
    '#5470c6',
    '#91cc75',
    '#fac858',
    '#ee6666',
    '#73c0de',
    '#3ba272',
    '#fc8452',
    '#9a60b4',
    '#ea7ccc',
  ];

  // 确保容器可见且有尺寸
  if (chartRef.value.$el) {
    chartRef.value.$el.style.height = '280px';
    chartRef.value.$el.style.width = '100%';
  }

  renderEcharts({
    color: colors,
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: 5,
      top: 20,
      bottom: 20,
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        fontSize: 11,
      },
      pageTextStyle: {
        color: '#666',
      },
      data: modelList.value.map((item) => item.gpu_model),
      formatter: (name) => {
        const item = modelList.value.find((m) => m.gpu_model === name);
        return `${name} (${item ? item.count : 0})`;
      },
    },
    series: [
      {
        name: 'GPU卡型号分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 6,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: true,
          formatter: '{b}: {c}',
          position: 'outside',
          fontSize: 11,
          lineHeight: 14,
          padding: [2, 4],
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 12,
            fontWeight: 'bold',
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        labelLine: {
          show: true,
          length: 8,
          length2: 10,
        },
        data: chartData.value,
      },
    ],
  })
    .then(() => {
      // 渲染成功后调整大小
      handleResize();
    })
    .catch((error) => {
      console.error('GPU型号图表渲染失败:', error);
    });
}

// 监听数据变化
watch(
  () => props.data,
  () => {
    renderChart();
  },
  { deep: true },
);

// 监听加载状态变化
watch(
  () => props.loading,
  (val) => {
    if (!val) {
      renderChart();
    }
  },
);

onMounted(() => {
  // 延迟渲染，确保DOM已经完全渲染
  setTimeout(() => {
    renderChart();
  }, 300);
  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize, { passive: true });
});

onBeforeUnmount(() => {
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize);
});
</script>

<template>
  <div class="gpu-model-chart">
    <Spin :spinning="loading" tip="加载中...">
      <div v-if="hasData" class="chart-container">
        <div class="summary">
          <div class="summary-item">
            <div class="number">{{ totalModels }}</div>
            <div class="label">GPU型号总数</div>
          </div>
        </div>
        <div class="chart-wrapper">
          <EchartsUI ref="chartRef" height="280px" />
        </div>
      </div>
      <Empty v-else description="暂无数据" />
    </Spin>
  </div>
</template>

<style lang="less" scoped>
.gpu-model-chart {
  height: 100%;
  position: relative;

  .chart-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .summary {
      display: flex;
      justify-content: center;
      margin-bottom: 10px;

      .summary-item {
        text-align: center;
        margin: 0 20px;
        padding: 6px 12px;
        background-color: #f0f7ff;
        border-radius: 8px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .number {
          font-size: 20px;
          font-weight: 600;
          color: #1890ff;
          line-height: 1.2;
        }

        .label {
          font-size: 12px;
          color: #666;
          margin-top: 3px;
        }
      }
    }

    .chart-wrapper {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
