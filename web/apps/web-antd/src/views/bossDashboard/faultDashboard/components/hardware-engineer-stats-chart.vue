<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { EngineerStatsData } from '#/api/core/bossDashboard/sla-dashboard';

import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { Empty, Spin } from 'ant-design-vue';

const props = defineProps<{
  data: EngineerStatsData;
  loading: boolean;
}>();

const chartRef = ref<EchartsUIType>();
const { renderEcharts, resize } = useEcharts(chartRef);

// 硬件工程师数据
const hardwareEngineers = computed(() => {
  if (!props.data || !props.data.stats) {
    return [];
  }

  return props.data.stats.filter((item) => item.ticket_type === 'hardware');
});

// 图表数据
const engineerData = computed(() => {
  if (hardwareEngineers.value.length === 0) {
    return [];
  }

  // 取前20名工程师数据并按修复时长从高到低排序
  return [...hardwareEngineers.value]
    .sort((a, b) => b.avg_fix_time - a.avg_fix_time)
    .slice(0, 20)
    .map((engineer) => ({
      name: engineer.engineer_name,
      responseTime: engineer.avg_response_time,
      fixTime: engineer.avg_fix_time,
    }));
});

// 是否有数据
const hasData = computed(() => {
  return hardwareEngineers.value.length > 0;
});

// 处理窗口大小变化
function handleResize() {
  resize();
}

// 渲染图表
function renderChart() {
  if (!chartRef.value || !hasData.value) {
    return;
  }

  // 提取数据
  const engineers = engineerData.value;
  const names = engineers.map((item) => item.name);
  const responseData = engineers.map((item) => item.responseTime);
  const fixData = engineers.map((item) => item.fixTime);

  renderEcharts({
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter(params: any) {
        let result = `<div style="font-weight:bold;margin-bottom:5px;">${params[0].name}</div>`;
        params.forEach((param: any) => {
          const color = param.color;
          const value = param.value;
          const name = param.seriesName;
          result += `<div style="display:flex;align-items:center;margin:3px 0;">
            <span style="display:inline-block;width:10px;height:10px;background:${color};margin-right:6px;border-radius:50%;"></span>
            <span>${name}：${value} 分钟</span>
          </div>`;
        });
        return result;
      },
    },
    legend: {
      data: ['平均响应时长(分钟)', '平均修复时长(分钟)'],
      bottom: 5,
      icon: 'roundRect',
      itemWidth: 12,
      itemHeight: 12,
      textStyle: {
        fontSize: 12,
      },
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '8%',
      top: '5%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      name: '分钟',
      nameTextStyle: {
        fontSize: 12,
        padding: [0, 0, 0, 0],
        color: '#333333',
      },
      axisLabel: {
        formatter: '{value}',
        fontSize: 11,
        color: '#333333',
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#e0e0e0',
        },
      },
      axisLine: {
        lineStyle: {
          color: '#d9d9d9',
        },
      },
    },
    yAxis: {
      type: 'category',
      data: names,
      axisLabel: {
        fontSize: 11,
        width: 100,
        overflow: 'truncate',
        color: '#333333',
      },
      axisLine: {
        lineStyle: {
          color: '#d9d9d9',
        },
      },
      splitLine: {
        show: false,
      },
    },
    series: [
      {
        name: '平均响应时长(分钟)',
        type: 'bar',
        data: responseData,
        emphasis: {
          focus: 'series',
        },
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: '#ff9c6e' },
              { offset: 1, color: '#ff7a45' },
            ],
          },
          borderRadius: [0, 4, 4, 0],
        },
        barWidth: '40%',
        barGap: '20%',
      },
      {
        name: '平均修复时长(分钟)',
        type: 'bar',
        data: fixData,
        emphasis: {
          focus: 'series',
        },
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: '#ffd666' },
              { offset: 1, color: '#faad14' },
            ],
          },
          borderRadius: [0, 4, 4, 0],
        },
        barWidth: '40%',
      },
    ],
  });
}

// 监听数据变化
watch(
  () => props.data,
  () => {
    renderChart();
  },
  { deep: true },
);

// 监听加载状态变化
watch(
  () => props.loading,
  (val) => {
    if (!val) {
      renderChart();
    }
  },
);

// 初始化图表
onMounted(() => {
  renderChart();
  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize, { passive: true });
});

onBeforeUnmount(() => {
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize);
});
</script>

<template>
  <div class="hardware-engineer-stats-chart">
    <Spin :spinning="loading" tip="加载中...">
      <div v-if="hasData" class="chart-container">
        <div class="chart-wrapper">
          <EchartsUI ref="chartRef" height="350px" />
        </div>
      </div>
      <Empty v-else description="暂无数据" />
    </Spin>
  </div>
</template>

<style lang="less" scoped>
.hardware-engineer-stats-chart {
  height: 100%;
  position: relative;

  .chart-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .chart-wrapper {
      flex: 1;
      min-height: 350px;
    }
  }
}
</style>
