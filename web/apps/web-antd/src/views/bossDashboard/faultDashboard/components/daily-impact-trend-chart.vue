<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { DailyImpactTrendData } from '#/api/core/bossDashboard/sla-dashboard';

import { computed, onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

const props = defineProps<{
  data?: DailyImpactTrendData;
  loading?: boolean;
}>();

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

function renderChart() {
  if (!chartRef.value || !props.data || !props.data.series || !props.data.xAxis)
    return;

  // 获取最大影响时长用于计算Y轴刻度
  const impactData = props.data.series.find(
    (s) => s.name === '故障处理时长',
  )?.data;
  const maxImpactTime = impactData ? Math.max(...impactData) : 100;

  const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d'];

  renderEcharts({
    color: colors,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#eee',
      borderWidth: 1,
      textStyle: {
        color: '#333',
      },
      padding: [8, 12],
      formatter: (params: any) => {
        let result = `<div style="font-weight:bold;margin-bottom:5px">${params[0].name}</div>`;
        params.forEach((param: any) => {
          const color = param.color;
          const seriesName = param.seriesName;
          const value = param.value;

          result += `<div style="display:flex;align-items:center;margin:3px 0">`;
          result += `<span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background-color:${color};"></span>`;
          result +=
            seriesName === '故障处理时长'
              ? `<span style="margin-right:15px;flex:1">${seriesName}:</span> <span style="font-weight:bold">${value} 分钟</span>`
              : `<span style="margin-right:15px;flex:1">${seriesName}:</span> <span style="font-weight:bold">${value} 个</span>`;
          result += `</div>`;
        });

        return result;
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '12%',
      top: '14%',
      containLabel: true,
    },
    legend: {
      data: ['故障处理时长', '报障单数量'],
      bottom: 0,
      icon: 'roundRect',
      itemWidth: 12,
      itemHeight: 12,
      textStyle: {
        fontSize: 12,
      },
    },
    xAxis: [
      {
        type: 'category',
        data: props.data.xAxis,
        axisPointer: {
          type: 'shadow',
        },
        axisLabel: {
          rotate: 45,
          interval: 'auto',
          fontSize: 11,
          color: '#666',
        },
        axisTick: {
          alignWithLabel: true,
        },
        axisLine: {
          lineStyle: {
            color: '#ddd',
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '故障处理时长(分钟)',
        min: 0,
        max: maxImpactTime ? Math.ceil(maxImpactTime * 1.1) : 100,
        position: 'left',
        axisLabel: {
          formatter: '{value}',
          fontSize: 11,
          color: '#666',
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: colors[0],
          },
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#eee',
          },
        },
      },
      {
        type: 'value',
        name: '报障单数量',
        min: 0,
        position: 'right',
        axisLabel: {
          formatter: '{value}',
          fontSize: 11,
          color: '#666',
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: colors[1],
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    series: props.data.series.map((s) => {
      if (s.type === 'line') {
        return {
          name: s.name,
          type: 'line',
          data: s.data,
          yAxisIndex: s.yAxisIndex,
          symbol: 'emptyCircle',
          symbolSize: 6,
          smooth: true,
          lineStyle: {
            width: 3,
            shadowColor: 'rgba(0,0,0,0.2)',
            shadowBlur: 6,
            shadowOffsetY: 6,
          },
          itemStyle: {
            borderWidth: 2,
          },
          areaStyle: {
            opacity: 0.15,
          },
          emphasis: {
            itemStyle: {
              borderWidth: 3,
              shadowColor: 'rgba(0,0,0,0.3)',
              shadowBlur: 10,
            },
          },
        };
      }
      if (s.type === 'bar') {
        return {
          name: s.name,
          type: 'bar',
          data: s.data,
          yAxisIndex: s.yAxisIndex,
          barWidth: '50%',
          itemStyle: {
            borderRadius: [3, 3, 0, 0],
            color: colors[1],
          },
          emphasis: {
            itemStyle: {
              shadowColor: 'rgba(0,0,0,0.2)',
              shadowBlur: 6,
            },
          },
        };
      }
      return s;
    }) as any,
  });
}

watch(
  () => props.data,
  () => {
    renderChart();
  },
  { deep: true },
);

watch(
  () => props.loading,
  () => {
    if (!props.loading) {
      renderChart();
    }
  },
);

onMounted(() => {
  renderChart();
});

// 计算总处理时长
const totalImpactTime = computed(() => {
  return props.data?.total_impact_time || 0;
});

// 计算总工单数
const totalTickets = computed(() => {
  return props.data?.total_tickets || 0;
});
</script>

<template>
  <div class="daily-impact-trend-chart-container">
    <div class="chart-header">
      <div class="stat-item">
        <div class="stat-value">{{ totalImpactTime }} 分钟</div>
        <div class="stat-label">故障总处理时长</div>
      </div>
      <div class="stat-divider"></div>
      <div class="stat-item">
        <div class="stat-value">{{ totalTickets }}</div>
        <div class="stat-label">报障单总数</div>
      </div>
    </div>
    <EchartsUI ref="chartRef" height="450px" />
  </div>
</template>

<style lang="less" scoped>
.daily-impact-trend-chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}

.stat-divider {
  width: 1px;
  height: 40px;
  background: #eaeaea;
}

.stat-item {
  text-align: center;
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
  border-radius: 10px;
  padding: 10px 24px;
  min-width: 180px;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(24, 144, 255, 0.1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(24, 144, 255, 0.15);
  }
}

.stat-value {
  font-size: 22px;
  font-weight: bold;
  color: #1890ff;
  line-height: 1.2;
}

.stat-label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  margin-top: 6px;
}
</style>
