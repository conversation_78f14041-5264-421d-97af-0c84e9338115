<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { RoomDistributionData } from '#/api/core/bossDashboard/fault-dashboard';

import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { Empty, Spin } from 'ant-design-vue';

const props = defineProps<{
  data: RoomDistributionData;
  loading: boolean;
}>();

const chartRef = ref<EchartsUIType>();
const { renderEcharts, resize } = useEcharts(chartRef);

// 图表数据
const chartData = computed(() => {
  if (!props.data || !props.data.chart_data) {
    return [];
  }
  return props.data.chart_data;
});

// 机房列表
const roomList = computed(() => {
  if (!props.data || !props.data.room_counts) {
    return [];
  }
  return props.data.room_counts;
});

// 数据中心统计
const dataCenterStats = computed(() => {
  if (!props.data || !props.data.data_center_stats) {
    return [];
  }
  return props.data.data_center_stats;
});

// 是否有数据
const hasData = computed(() => {
  return chartData.value.length > 0;
});

// 获取总服务器数量
const totalServers = computed(() => {
  return roomList.value.reduce((acc, curr) => acc + curr.count, 0);
});

// 处理窗口大小变化
function handleResize() {
  resize();
}

// 渲染图表
function renderChart() {
  if (!chartRef.value || !hasData.value) {
    return;
  }

  const colors = [
    '#36cfc9',
    '#52c41a',
    '#1890ff',
    '#722ed1',
    '#eb2f96',
    '#faad14',
    '#a0d911',
    '#13c2c2',
    '#fa8c16',
    '#fa541c',
  ];

  // 确保容器可见且有尺寸
  if (chartRef.value.$el) {
    chartRef.value.$el.style.height = '280px';
    chartRef.value.$el.style.width = '100%';
  }

  renderEcharts({
    color: colors,
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      type: 'scroll',
      orient: 'horizontal',
      bottom: 5,
      textStyle: {
        fontSize: 11,
      },
      pageTextStyle: {
        color: '#666',
      },
      data: chartData.value.map((item) => item.name),
      formatter: (name) => {
        const item = chartData.value.find((d) => d.name === name);
        return `${name}${item ? `: ${item.value}` : ''}`;
      },
    },
    series: [
      {
        name: 'GPU服务器机房分布',
        type: 'pie',
        radius: ['35%', '65%'],
        center: ['50%', '50%'],
        roseType: 'radius',
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 6,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: true,
          formatter: '{b}: {c}',
          fontSize: 11,
          lineHeight: 14,
          padding: [2, 4],
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 12,
            fontWeight: 'bold',
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        labelLine: {
          show: true,
          length: 8,
          length2: 10,
        },
        data: chartData.value,
      },
    ],
  })
    .then(() => {
      // 渲染成功后调整大小
      handleResize();
    })
    .catch((error) => {
      console.error('机房分布图表渲染失败:', error);
    });
}

// 监听数据变化
watch(
  () => props.data,
  () => {
    renderChart();
  },
  { deep: true },
);

// 监听加载状态变化
watch(
  () => props.loading,
  (val) => {
    if (!val) {
      renderChart();
    }
  },
);

onMounted(() => {
  // 延迟渲染，确保DOM已经完全渲染
  setTimeout(() => {
    renderChart();
  }, 300);
  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize, { passive: true });
});

onBeforeUnmount(() => {
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize);
});
</script>

<template>
  <div class="room-distribution-chart">
    <Spin :spinning="loading" tip="加载中...">
      <div v-if="hasData" class="chart-container">
        <div class="summary">
          <div class="summary-item">
            <div class="number">{{ roomList.length }}</div>
            <div class="label">包间总数</div>
          </div>
          <div class="summary-item">
            <div class="number">{{ dataCenterStats.length }}</div>
            <div class="label">数据中心数</div>
          </div>
          <div class="summary-item">
            <div class="number">{{ totalServers }}</div>
            <div class="label">服务器总数</div>
          </div>
        </div>
        <div class="chart-wrapper">
          <EchartsUI ref="chartRef" height="280px" />
        </div>
      </div>
      <Empty v-else description="暂无数据" />
    </Spin>
  </div>
</template>

<style lang="less" scoped>
.room-distribution-chart {
  height: 100%;
  position: relative;

  .chart-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .summary {
      display: flex;
      justify-content: center;
      margin-bottom: 10px;

      .summary-item {
        text-align: center;
        margin: 0 10px;
        padding: 6px 12px;
        background-color: #e6f7ff;
        border-radius: 8px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .number {
          font-size: 20px;
          font-weight: 600;
          color: #36cfc9;
          line-height: 1.2;
        }

        .label {
          font-size: 12px;
          color: #666;
          margin-top: 3px;
        }
      }
    }

    .chart-wrapper {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
