<script lang="ts" setup>
import type {
  DurationStatsData,
  GPUFaultRatioData,
  TotalFaultDevicesData,
} from '#/api/core/bossDashboard/sla-dashboard';

import { computed } from 'vue';

import { Card, Col, Row, Spin, Statistic } from 'ant-design-vue';

const props = defineProps<{
  durationData?: DurationStatsData;
  gpuFaultRatioData?: GPUFaultRatioData;
  loading?: boolean;
  repairStats?: {
    hardwareRepair: number;
    softwareRepair: number;
    total: number;
  };
  totalDevicesData?: TotalFaultDevicesData;
}>();

// 获取响应时间和停机时间的索引
const responseTimeIndex = computed(() => {
  if (!props.durationData?.phases) return -1;
  return props.durationData.phases.indexOf('响应时长');
});

const impactTimeIndex = computed(() => {
  if (!props.durationData?.phases) return -1;
  return props.durationData.phases.indexOf('业务影响时长');
});

// 安全获取数据的辅助函数
const safeDevicesData = computed(() => {
  return (
    props.totalDevicesData || {
      total_devices: 0,
      total_tickets: 0,
      type_stats: {
        types: [],
        data: [],
      },
    }
  );
});

// 安全获取修复数据的辅助函数
const safeRepairStats = computed(() => {
  return (
    props.repairStats || {
      hardwareRepair: 0,
      softwareRepair: 0,
      total: 0,
    }
  );
});

// 计算平均响应时间（分钟）
const avgResponseTime = computed(() => {
  if (!props.durationData || responseTimeIndex.value === -1) return 0;
  return Math.round(props.durationData.avgTimes[responseTimeIndex.value] || 0);
});

// 计算平均故障时长（分钟）
const avgImpactTime = computed(() => {
  if (!props.durationData || impactTimeIndex.value === -1) return 0;
  return Math.round(props.durationData.avgTimes[impactTimeIndex.value] || 0);
});

// 计算故障总时长（分钟）
const totalImpactTime = computed(() => {
  if (
    !props.durationData ||
    impactTimeIndex.value === -1 ||
    !props.totalDevicesData
  )
    return 0;
  const avgTime = props.durationData.avgTimes[impactTimeIndex.value] || 0;
  const totalTickets = props.totalDevicesData.total_tickets || 0;
  return Math.round(avgTime * totalTickets);
});

// 计算GPU故障率
const gpuFaultRate = computed(() => {
  if (
    !props.gpuFaultRatioData ||
    !props.gpuFaultRatioData.fault_ratio_percent
  ) {
    return 0;
  }
  return Number.parseFloat(
    props.gpuFaultRatioData.fault_ratio_percent.toFixed(2),
  );
});

// 确定故障率级别颜色
const gpuFaultColor = computed(() => {
  if (gpuFaultRate.value < 1) {
    return '#52c41a'; // 绿色: < 1%
  } else if (gpuFaultRate.value < 3) {
    return '#faad14'; // 黄色: 1% - 3%
  } else {
    return '#f5222d'; // 红色: > 3%
  }
});
</script>

<template>
  <Card title="故障总览" :bordered="false" class="fault-overview-card">
    <Spin :spinning="loading || false">
      <!-- 第一行显示：总时长、总台数、平均响应、平均修复时间 -->
      <Row :gutter="24" class="stat-row">
        <Col :span="6">
          <div class="stat-item stat-item-total">
            <Statistic
              title="故障总时长(分钟)"
              :value="totalImpactTime"
              :precision="0"
            >
              <template #prefix>
                <svg
                  class="stat-icon"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path
                    d="M15.5 16.5v-5h-5v-5h-5v15h15v-5h-5zm0-10v5H21v10H5.5v-15H15.5z"
                  />
                </svg>
              </template>
            </Statistic>
          </div>
        </Col>
        <Col :span="6">
          <div class="stat-item stat-item-devices">
            <Statistic
              title="故障总台数"
              :value="safeDevicesData.total_devices"
              :precision="0"
            >
              <template #prefix>
                <svg
                  class="stat-icon"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path
                    d="M20 18c1.1 0 1.99-.9 1.99-2L22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2H0v2h24v-2h-4zM4 6h16v10H4V6z"
                  />
                </svg>
              </template>
            </Statistic>
          </div>
        </Col>
        <Col :span="6">
          <div class="stat-item stat-item-tickets">
            <Statistic
              title="故障总台次"
              :value="safeDevicesData.total_tickets"
              :precision="0"
            >
              <template #prefix>
                <svg
                  class="stat-icon"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path
                    d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"
                  />
                </svg>
              </template>
            </Statistic>
          </div>
        </Col>
        <Col :span="6">
          <div class="stat-item stat-item-gpu">
            <Statistic
              title="GPU卡故障率(%)"
              :value="gpuFaultRate"
              :precision="2"
            >
              <template #prefix>
                <svg
                  class="stat-icon"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                  :style="{ color: gpuFaultColor }"
                >
                  <path
                    d="M15 9H9v6h6V9zm-2 4h-2v-2h2v2zm8-2V9h-2V7c0-1.1-.9-2-2-2h-2V3h-2v2h-2V3H9v2H7c-1.1 0-2 .9-2 2v2H3v2h2v2H3v2h2v2c0 1.1.9 2 2 2h2v2h2v-2h2v2h2v-2h2c1.1 0 2-.9 2-2v-2h2v-2h-2v-2h2zm-4 6H7V7h10v10z"
                  />
                </svg>
              </template>
            </Statistic>
          </div>
        </Col>
      </Row>

      <!-- 第二行显示：总单量、软修复单量、硬修复单量 -->
      <Row :gutter="24" class="stat-row stat-row-second">
        <Col :span="6">
          <div class="stat-item stat-item-response">
            <Statistic
              title="平均响应时间(分钟)"
              :value="avgResponseTime"
              :precision="0"
            >
              <template #prefix>
                <svg
                  class="stat-icon"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path
                    d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z"
                  />
                </svg>
              </template>
            </Statistic>
          </div>
        </Col>
        <Col :span="6">
          <div class="stat-item stat-item-impact">
            <Statistic
              title="平均恢复时长(分钟)"
              :value="avgImpactTime"
              :precision="0"
            >
              <template #prefix>
                <svg
                  class="stat-icon"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path
                    d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
                  />
                  <path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z" />
                </svg>
              </template>
            </Statistic>
          </div>
        </Col>
        <Col :span="6">
          <div class="stat-item stat-item-soft">
            <Statistic
              title="软修复单量"
              :value="safeRepairStats.softwareRepair"
              :precision="0"
            >
              <template #prefix>
                <svg
                  class="stat-icon"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path
                    d="M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z"
                  />
                </svg>
              </template>
            </Statistic>
          </div>
        </Col>
        <Col :span="6">
          <div class="stat-item stat-item-hard">
            <Statistic
              title="硬修复单量"
              :value="safeRepairStats.hardwareRepair"
              :precision="0"
            >
              <template #prefix>
                <svg
                  class="stat-icon"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path
                    d="M18 16h-2v-1h-2v1h-2v-1H8v1H6v-1H4v1H2v2h2v1h2v-1h2v1h2v-1h4v1h2v-1h2v1h2v-2h-2v-1zm-9-9H7V5H5v2H3v2h2v2h2V9h2V7zm7 4h-2v2h2v2h2v-2h2v-2h-2v-2h-2v2z"
                  />
                </svg>
              </template>
            </Statistic>
          </div>
        </Col>
      </Row>
    </Spin>
  </Card>
</template>

<style lang="less" scoped>
.fault-overview-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;

  :deep(.ant-card-head) {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;

    .ant-card-head-title {
      font-size: 16px;
      font-weight: 600;
      color: #1f1f1f;
    }
  }

  :deep(.ant-card-body) {
    padding: 24px;
  }

  .stat-row {
    margin-top: 8px;

    &-second {
      margin-top: 24px;
    }
  }

  .stat-item {
    padding: 16px;
    border-radius: 6px;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }

    .stat-icon {
      margin-right: 8px;
      font-size: 18px;
    }

    &-total {
      background-color: rgba(114, 46, 209, 0.08);
      :deep(.ant-statistic-title) {
        color: #722ed1;
      }
      .stat-icon {
        color: #722ed1;
      }
    }

    &-devices {
      background-color: rgba(82, 196, 26, 0.08);
      :deep(.ant-statistic-title) {
        color: #52c41a;
      }
      .stat-icon {
        color: #52c41a;
      }
    }

    &-tickets {
      background-color: rgba(24, 144, 255, 0.08);
      :deep(.ant-statistic-title) {
        color: #1890ff;
      }
      .stat-icon {
        color: #1890ff;
      }
    }

    &-gpu {
      background-color: rgba(16, 142, 233, 0.08);
      :deep(.ant-statistic-title) {
        color: #108ee9;
      }
    }

    &-response {
      background-color: rgba(250, 173, 20, 0.08);
      :deep(.ant-statistic-title) {
        color: #faad14;
      }
      .stat-icon {
        color: #faad14;
      }
    }

    &-impact {
      background-color: rgba(245, 34, 45, 0.08);
      :deep(.ant-statistic-title) {
        color: #f5222d;
      }
      .stat-icon {
        color: #f5222d;
      }
    }

    &-soft {
      background-color: rgba(22, 119, 255, 0.08);
      :deep(.ant-statistic-title) {
        color: #1677ff;
      }
      .stat-icon {
        color: #1677ff;
      }
    }

    &-hard {
      background-color: rgba(255, 85, 0, 0.08);
      :deep(.ant-statistic-title) {
        color: #ff5500;
      }
      .stat-icon {
        color: #ff5500;
      }
    }
  }

  :deep(.ant-statistic) {
    text-align: center;
  }

  :deep(.ant-statistic-title) {
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 14px;
  }

  :deep(.ant-statistic-content) {
    font-size: 20px;
    font-weight: 600;
  }
}
</style>
