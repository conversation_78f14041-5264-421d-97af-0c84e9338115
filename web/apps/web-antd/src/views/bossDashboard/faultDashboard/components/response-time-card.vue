<script lang="ts" setup>
import type { DurationStatsData } from '#/api/core/bossDashboard/sla-dashboard';

import { computed } from 'vue';

import { Card, Col, Row, Spin, Statistic } from 'ant-design-vue';

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object as () => DurationStatsData,
    default: () => ({
      phases: [],
      avgTimes: [],
      maxTimes: [],
      minTimes: [],
    }),
  },
});

// 计算响应时长和硬件平均响应时长索引
const generalResponseIndex = computed(() => {
  return props.data?.phases?.findIndex((phase) => phase === '响应时长') ?? -1;
});

const hardwareResponseIndex = computed(() => {
  return (
    props.data?.phases?.findIndex((phase) => phase === '硬件平均响应时长') ?? -1
  );
});

// 计算获取数据
const generalResponseAvg = computed(() => {
  if (generalResponseIndex.value >= 0 && props.data?.avgTimes) {
    return props.data.avgTimes[generalResponseIndex.value]?.toFixed(2) || '0';
  }
  return '0';
});

const generalResponseMax = computed(() => {
  if (generalResponseIndex.value >= 0 && props.data?.maxTimes) {
    return props.data.maxTimes[generalResponseIndex.value] || 0;
  }
  return 0;
});

const hardwareResponseAvg = computed(() => {
  if (hardwareResponseIndex.value >= 0 && props.data?.avgTimes) {
    return props.data.avgTimes[hardwareResponseIndex.value]?.toFixed(2) || '0';
  }
  return '0';
});

const hardwareResponseMax = computed(() => {
  if (hardwareResponseIndex.value >= 0 && props.data?.maxTimes) {
    return props.data.maxTimes[hardwareResponseIndex.value] || 0;
  }
  return 0;
});

// 格式化时间（分钟转换为小时和分钟）
const formatTime = (minutes: number) => {
  if (minutes <= 0) return '0分钟';

  const hours = Math.floor(minutes / 60);
  const mins = Math.floor(minutes % 60);

  if (hours > 0) {
    return `${hours}小时${mins > 0 ? `${mins}分钟` : ''}`;
  }
  return `${mins}分钟`;
};
</script>

<template>
  <Card title="响应时长统计" :bordered="false" :loading="loading">
    <Spin :spinning="loading">
      <Row :gutter="16">
        <Col :span="12">
          <Card class="inner-card">
            <div class="stat-title">软件平均响应时长</div>
            <Statistic
              :value="generalResponseAvg"
              suffix="分钟"
              :precision="2"
              :value-style="{ color: '#3f8600' }"
            >
              <template #prefix>
                <svg
                  class="stat-icon"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path
                    d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
                  />
                  <path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z" />
                </svg>
              </template>
            </Statistic>
            <div class="stat-description">
              最长响应时间: {{ formatTime(generalResponseMax) }}
            </div>
          </Card>
        </Col>
        <Col :span="12">
          <Card class="inner-card">
            <div class="stat-title">硬件平均响应时长</div>
            <Statistic
              :value="hardwareResponseAvg"
              suffix="分钟"
              :precision="2"
              :value-style="{ color: '#cf1322' }"
            >
              <template #prefix>
                <svg
                  class="stat-icon"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path
                    d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
                  />
                  <path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z" />
                </svg>
              </template>
            </Statistic>
            <div class="stat-description">
              最长响应时间: {{ formatTime(hardwareResponseMax) }}
            </div>
          </Card>
        </Col>
      </Row>
    </Spin>
  </Card>
</template>

<style lang="less" scoped>
.inner-card {
  background-color: #f9f9f9;
  border-radius: 8px;

  :deep(.ant-card-body) {
    padding: 16px;
  }
}

.stat-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #333;
}

.stat-description {
  margin-top: 8px;
  color: #999;
  font-size: 12px;
}

.stat-icon {
  margin-right: 8px;
  font-size: 18px;
}
</style>
