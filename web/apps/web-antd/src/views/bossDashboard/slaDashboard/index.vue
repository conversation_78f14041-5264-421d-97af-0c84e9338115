<script lang="ts" setup>
import type {
  BusinessImpactData,
  ClusterStatsData,
  DurationStatsData,
  FaultDetailTypeStatsData,
  FaultTypeStatsData,
  // GPUFaultRatioData,
  ProjectOption,
  ServerStatsData,
  SLACalculateData,
  SLAStatsData,
  SourceStatsData,
  // StatusStatsData,
  // TicketTimeStatsData,
  TimeRangeParams,
} from '#/api/core/bossDashboard/sla-dashboard';

import { onMounted, reactive, ref } from 'vue';

import {
  Card,
  Col,
  DatePicker,
  message,
  Row,
  Select,
  Spin,
  Statistic,
  Tooltip,
} from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  // getStatusStats,
  // getTicketTimeStats,
  calculateSLA,
  getBusinessImpactTotalTime,
  getClusterStats,
  getDurationStats,
  getFaultDetailTypeStats,
  getFaultTicketDetails,
  getFaultTypeStats,
  // getGPUFaultRatio,
  getProjectList,
  getServerStats,
  getSLAStats,
  getSourceStats,
  getTotalFaultDevices,
} from '#/api/core/bossDashboard/sla-dashboard';

import ColdMigrationRatioCard from './components/cold-migration-ratio-card.vue';
import FaultDetectionRateCard from './components/fault-detection-rate-card.vue';
// import BusinessImpactCard from './components/business-impact-card.vue';
// import DurationStatsChart from './components/duration-stats-chart.vue';
// import FaultDetailTypeChart from './components/fault-detail-type-chart.vue';
import FaultTicket from './components/fault-ticket.vue';
// import FaultTypeChart from './components/fault-type-chart.vue';
// import GPUFaultRatioCard from './components/gpu-fault-ratio-card.vue';
import ProjectResourceCard from './components/project-resource-card.vue';
import ResourceChangeDetail from './components/resource-change-detail.vue';
import ResourceChangesCard from './components/resource-changes-card.vue';
// import RepairMethodChart from './components/repair-method-chart.vue';
// 导入图表组件
// import SLASingleChart from './components/sla-single-chart.vue'; //燃尽图
import SourceStatsChart from './components/source-stats-chart.vue';
// import StatusStatsChart from './components/status-stats-chart.vue';
// import TicketTimeStatsChart from './components/ticket-time-stats-chart.vue';

// 加载状态
const loading = reactive({
  server: false,
  cluster: false,
  sla: false,
  slaCalculate: false,
  ticketTime: false,
  faultType: false,
  faultDetailType: false,
  sourceStats: false,
  statusStats: false,
  durationStats: false,
  businessImpact: false,
  projectList: false,
  gpuFaultRatio: false,
  totalFaultDevices: false,
  faultDevicesDetails: false,
});

// 图表数据
const serverStatsData = ref<ServerStatsData>({} as ServerStatsData);
const clusterStatsData = ref<ClusterStatsData>({} as ClusterStatsData);
const slaStatsData = ref<SLAStatsData>({} as SLAStatsData);
const slaLevelsData = ref<{
  level1: null | SLACalculateData;
  level2: null | SLACalculateData;
  level3: null | SLACalculateData;
}>({
  level1: null,
  level2: null,
  level3: null,
});
// const ticketTimeStatsData = ref<TicketTimeStatsData>({} as TicketTimeStatsData);
const faultTypeData = ref<FaultTypeStatsData>({} as FaultTypeStatsData);
const faultDetailTypeData = ref<FaultDetailTypeStatsData>(
  {} as FaultDetailTypeStatsData,
);
const sourceStatsData = ref<SourceStatsData>({} as SourceStatsData);
// const statusStatsData = ref<StatusStatsData>({} as StatusStatsData);
const durationStatsData = ref<DurationStatsData>({} as DurationStatsData);
const businessImpactData = ref<BusinessImpactData>({} as BusinessImpactData);
const projectOptions = ref<ProjectOption[]>([]);
// const gpuFaultRatioData = ref<GPUFaultRatioData>({} as GPUFaultRatioData);
const totalFaultDevicesData = ref<any>({});
const faultDevicesDetailsData = ref<any>({
  details: [],
  total: 0,
});

// SLA图表引用
const slaChartRef = ref<any>(null);

// 当前选择的年月
const selectedYearMonth = ref(dayjs());

// 搜索参数
const searchParams = reactive<TimeRangeParams>({
  time_range: 'month',
  group_by: 'day',
  project: 'cloud17',
});

// 时间范围选项
const timeRangeOptions = [
  { label: '今日', value: 'today' },
  { label: '本周', value: 'week' },
  { label: '本月', value: 'month' },
  { label: '本年', value: 'year' },
  { label: '自定义', value: 'custom' },
];

// 分组方式选项
const groupByOptions = [
  { label: '按天', value: 'day' },
  { label: '按周', value: 'week' },
  { label: '按月', value: 'month' },
];

// SLA等级定义
const slaLevels = {
  level1: 99,
  level2: 99.5,
  level3: 99.9,
};

// 获取项目列表
async function fetchProjectList() {
  try {
    loading.projectList = true;
    const res = await getProjectList();

    // 将字符串数组转换为对象数组格式
    projectOptions.value = res.map((project: string) => ({
      label: project,
      value: project,
    }));
    // 添加一个全部选项
    projectOptions.value.unshift({ label: '全部', value: '' });
  } catch (error) {
    console.error('获取项目列表失败', error);
  } finally {
    loading.projectList = false;
  }
}

// 获取SLA计算数据（用于顶部三个等级的展示）
async function fetchSLACalculate() {
  try {
    loading.slaCalculate = true;
    const year = selectedYearMonth.value.year();
    const month = selectedYearMonth.value.month() + 1;
    const project = searchParams.project;

    // 并行请求三个不同算法的SLA数据
    const [res100, res95, res90] = await Promise.all([
      calculateSLA({ year, month, project, algorithm: '100' }),
      calculateSLA({ year, month, project, algorithm: '95' }),
      calculateSLA({ year, month, project, algorithm: '90' }),
    ]);

    slaLevelsData.value = {
      level1: res100,
      level2: res95,
      level3: res90,
    };
  } catch (error) {
    console.error('获取SLA计算数据失败', error);
  } finally {
    loading.slaCalculate = false;
  }
}

// 获取服务器统计数据
async function fetchServerStats() {
  try {
    loading.server = true;
    const res = await getServerStats(searchParams);
    serverStatsData.value = res;
  } catch (error) {
    console.error('获取服务器统计数据失败', error);
  } finally {
    loading.server = false;
  }
}

// 获取集群统计数据
async function fetchClusterStats() {
  try {
    loading.cluster = true;
    const res = await getClusterStats(searchParams);
    clusterStatsData.value = res;
  } catch (error) {
    console.error('获取集群统计数据失败', error);
  } finally {
    loading.cluster = false;
  }
}

// 获取SLA统计数据
async function fetchSLAStats() {
  try {
    loading.sla = true;
    const res = await getSLAStats(searchParams);
    slaStatsData.value = res as unknown as SLAStatsData;
  } catch (error) {
    console.error('获取SLA统计数据失败', error);
  } finally {
    loading.sla = false;
  }
}

// 获取工单时间分布
// async function fetchTicketTimeStats() {
//   try {
//     loading.ticketTime = true;
//     const res = await getTicketTimeStats(searchParams);
//     ticketTimeStatsData.value = res as unknown as TicketTimeStatsData;
//   } catch (error) {
//     console.error('获取工单时间分布数据失败', error);
//   } finally {
//     loading.ticketTime = false;
//   }
// }

// 获取故障类型分布
async function fetchFaultTypeStats() {
  try {
    loading.faultType = true;
    const res = await getFaultTypeStats(searchParams);
    faultTypeData.value = res as unknown as FaultTypeStatsData;
  } catch (error) {
    console.error('获取故障类型分布数据失败', error);
  } finally {
    loading.faultType = false;
  }
}

// 获取故障来源分布
async function fetchSourceStats() {
  try {
    loading.sourceStats = true;
    const res = await getSourceStats(searchParams);
    sourceStatsData.value = res as unknown as SourceStatsData;
  } catch (error) {
    console.error('获取故障来源分布数据失败', error);
  } finally {
    loading.sourceStats = false;
  }
}

// 获取具体故障类型分布
async function fetchFaultDetailTypeStats() {
  try {
    loading.faultDetailType = true;
    const res = await getFaultDetailTypeStats(searchParams);
    faultDetailTypeData.value = res as unknown as FaultDetailTypeStatsData;
  } catch (error) {
    console.error('获取具体故障类型分布数据失败', error);
  } finally {
    loading.faultDetailType = false;
  }
}

// 获取工单状态分布
// async function fetchStatusStats() {
//   try {
//     loading.statusStats = true;
//     const res = await getStatusStats(searchParams);
//     statusStatsData.value = res as unknown as StatusStatsData;
//   } catch (error) {
//     console.error('获取工单状态分布数据失败', error);
//   } finally {
//     loading.statusStats = false;
//   }
// }

// 获取处理时长统计
async function fetchDurationStats() {
  try {
    loading.durationStats = true;
    const res = await getDurationStats(searchParams);
    durationStatsData.value = res as unknown as DurationStatsData;
  } catch (error) {
    console.error('获取处理时长统计数据失败', error);
  } finally {
    loading.durationStats = false;
  }
}

// 获取业务影响总时长
async function fetchBusinessImpactTotalTime() {
  try {
    loading.businessImpact = true;
    const res = await getBusinessImpactTotalTime(searchParams);
    businessImpactData.value = res as unknown as BusinessImpactData;
  } catch (error) {
    console.error('获取业务影响总时长数据失败', error);
  } finally {
    loading.businessImpact = false;
  }
}

// 获取故障总台数和详情
async function fetchTotalFaultDevices() {
  try {
    loading.totalFaultDevices = true;
    loading.faultDevicesDetails = true;
    // 创建新的参数对象，并显式设置count_in_sla为true
    const params = {
      ...searchParams,
      count_in_sla: true, // 确保设置为true
    };

    // 获取故障台数数据
    const countRes = await getTotalFaultDevices(params);
    totalFaultDevicesData.value = countRes;

    // 仅当有选择项目时才获取故障单详情
    if (searchParams.project) {
      try {
        const detailsRes = await getFaultTicketDetails(params);
        faultDevicesDetailsData.value = detailsRes;
      } catch (detailsError) {
        console.error('获取故障单详情数据失败', detailsError);
        faultDevicesDetailsData.value = { details: [], total: 0 };
      }
    } else {
      faultDevicesDetailsData.value = { details: [], total: 0 };
    }
  } catch (error) {
    console.error('获取故障总台数数据失败', error);
    totalFaultDevicesData.value = {};
    faultDevicesDetailsData.value = { details: [], total: 0 };
  } finally {
    loading.totalFaultDevices = false;
    loading.faultDevicesDetails = false;
  }
}

// 获取GPU卡故障比
// async function fetchGPUFaultRatio() {
//   // 只有当选择了项目时才获取GPU故障比
//   if (!searchParams.project) return;

//   try {
//     loading.gpuFaultRatio = true;
//     const res = await getGPUFaultRatio(searchParams);
//     gpuFaultRatioData.value = res as unknown as GPUFaultRatioData;
//   } catch (error) {
//     console.error('获取GPU卡故障比数据失败', error);
//   } finally {
//     loading.gpuFaultRatio = false;
//   }
// }

// 处理年月选择变更
function handleYearMonthChange(
  date: dayjs.Dayjs | null | string,
  _dateString?: string,
): void {
  if (date) {
    selectedYearMonth.value = typeof date === 'string' ? dayjs(date) : date;

    // 设置月份的开始日期和结束日期
    const monthStart = selectedYearMonth.value
      .startOf('month')
      .format('YYYY-MM-DD');
    const monthEnd = selectedYearMonth.value
      .endOf('month')
      .format('YYYY-MM-DD');

    // 更新搜索参数
    searchParams.start_date = monthStart;
    searchParams.end_date = monthEnd;

    // 更改时间范围为自定义
    searchParams.time_range = 'custom';

    // 更新SLA图表
    if (slaChartRef.value && slaChartRef.value.setDate) {
      slaChartRef.value.setDate(selectedYearMonth.value);
    }

    // 更新SLA计算数据
    fetchSLACalculate();

    // 获取所有数据
    fetchAllData();
  }
}

// 搜索条件变更
function handleSearchParamsChange() {
  // 确保日期格式正确
  if (
    searchParams.time_range === 'custom' &&
    (!searchParams.start_date || !searchParams.end_date)
  ) {
    message.warning('请选择开始和结束日期');
    return;
  }
  fetchAllData();
}

// 日期变更处理函数
function handleDateChange(
  _date: any,
  dateString: string,
  type: 'end' | 'start',
): void {
  if (type === 'start') {
    searchParams.start_date = dateString;
  } else {
    searchParams.end_date = dateString;
  }

  if (searchParams.start_date && searchParams.end_date) {
    fetchAllData();
  }
}

// 获取所有数据
function fetchAllData() {
  fetchServerStats();
  fetchClusterStats();
  fetchSLAStats();
  fetchSLACalculate();
  // fetchTicketTimeStats();
  fetchFaultTypeStats();
  fetchSourceStats();
  fetchFaultDetailTypeStats();
  // fetchStatusStats();
  fetchDurationStats();
  fetchBusinessImpactTotalTime();
  // fetchGPUFaultRatio();
  fetchTotalFaultDevices();
}

// 页面加载时获取数据
onMounted(() => {
  fetchProjectList();
  fetchAllData();
});
</script>

<template>
  <div class="sla-dashboard">
    <div class="filter-container">
      <Card title="搜索条件" :bordered="false">
        <Row :gutter="16">
          <Col :span="5">
            <div class="filter-item">
              <span class="label">时间范围：</span>
              <Select
                v-model:value="searchParams.time_range"
                style="width: 120px"
                @change="handleSearchParamsChange"
              >
                <Select.Option
                  v-for="item in timeRangeOptions"
                  :key="item.value"
                  :value="item.value"
                >
                  {{ item.label }}
                </Select.Option>
              </Select>
            </div>
          </Col>

          <Col :span="5" v-if="searchParams.time_range === 'custom'">
            <div class="filter-item">
              <span class="label">开始日期：</span>
              <DatePicker
                v-model:value="searchParams.start_date"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="选择开始日期"
                :disabled-date="(current) => current > dayjs().endOf('day')"
                @change="
                  (date, dateString) =>
                    handleDateChange(date, dateString, 'start')
                "
              />
            </div>
          </Col>

          <Col :span="5" v-if="searchParams.time_range === 'custom'">
            <div class="filter-item">
              <span class="label">结束日期：</span>
              <DatePicker
                v-model:value="searchParams.end_date"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="选择结束日期"
                :disabled-date="(current) => current > dayjs().endOf('day')"
                @change="
                  (date, dateString) =>
                    handleDateChange(date, dateString, 'end')
                "
              />
            </div>
          </Col>

          <Col :span="5">
            <div class="filter-item">
              <span class="label">分组方式：</span>
              <Select
                v-model:value="searchParams.group_by"
                style="width: 120px"
                @change="handleSearchParamsChange"
              >
                <Select.Option
                  v-for="item in groupByOptions"
                  :key="item.value"
                  :value="item.value"
                >
                  {{ item.label }}
                </Select.Option>
              </Select>
            </div>
          </Col>

          <Col :span="5">
            <div class="filter-item">
              <span class="label">项目：</span>
              <Select
                v-model:value="searchParams.project"
                style="width: 160px"
                :loading="loading.projectList"
                placeholder="请选择项目"
                @change="handleSearchParamsChange"
              >
                <Select.Option
                  v-for="item in projectOptions"
                  :key="item.value"
                  :value="item.value"
                >
                  {{ item.label }}
                </Select.Option>
              </Select>
            </div>
          </Col>

          <Col :span="4">
            <div class="filter-item">
              <span class="label">年月：</span>
              <DatePicker
                v-model:value="selectedYearMonth"
                format="YYYY-MM"
                value-format="YYYY-MM"
                placeholder="选择年月"
                picker="month"
                :disabled-date="(current) => current > dayjs().endOf('day')"
                @change="handleYearMonthChange"
              />
            </div>
          </Col>
        </Row>
      </Card>
    </div>

    <!-- 顶部SLA展示 -->
    <Row :gutter="24" class="dashboard-row">
      <Col :span="24">
        <Card title="SLA" :bordered="false" class="fault-overview-card">
          <Spin :spinning="loading.slaCalculate || loading.businessImpact">
            <Row :gutter="24" class="stat-row">
              <Col :span="4">
                <Tooltip
                  title="SLA P90 ：一个月内90%算力云主机的服务可用性不低于99.9%"
                  placement="top"
                >
                  <div class="stat-item stat-item-level3">
                    <Statistic
                      title="P90"
                      :value="
                        slaLevelsData.level3?.sla_percentage || slaLevels.level3
                      "
                      suffix="%"
                      :precision="2"
                    >
                      <template #prefix>
                        <svg
                          class="stat-icon"
                          viewBox="0 0 24 24"
                          width="1em"
                          height="1em"
                          fill="currentColor"
                        >
                          <path
                            d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm-2 16l-4-4 1.41-1.41L10 14.17l6.59-6.59L18 9l-8 8z"
                          />
                        </svg>
                      </template>
                    </Statistic>
                  </div>
                </Tooltip>
              </Col>

              <Col :span="4">
                <Tooltip
                  title="SLA P95 ：一个月内95%算力云主机的服务可用性不低于99.5%"
                  placement="top"
                >
                  <div class="stat-item stat-item-level2">
                    <Statistic
                      title="P95"
                      :value="
                        slaLevelsData.level2?.sla_percentage || slaLevels.level2
                      "
                      suffix="%"
                      :precision="2"
                    >
                      <template #prefix>
                        <svg
                          class="stat-icon"
                          viewBox="0 0 24 24"
                          width="1em"
                          height="1em"
                          fill="currentColor"
                        >
                          <path
                            d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 14l-4-4 1.41-1.41L12 15.17l6.59-6.59L20 10l-8 7z"
                          />
                        </svg>
                      </template>
                    </Statistic>
                  </div>
                </Tooltip>
              </Col>
              <Col :span="4">
                <Tooltip
                  title="SLA P100 ：一个月内100%算力云主机的服务可用性不低于99.97%"
                  placement="top"
                >
                  <div class="stat-item stat-item-level1">
                    <Statistic
                      title="P100"
                      :value="
                        slaLevelsData.level1?.sla_percentage || slaLevels.level1
                      "
                      suffix="%"
                      :precision="2"
                    >
                      <template #prefix>
                        <svg
                          class="stat-icon"
                          viewBox="0 0 24 24"
                          width="1em"
                          height="1em"
                          fill="currentColor"
                        >
                          <path
                            d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z"
                          />
                        </svg>
                      </template>
                    </Statistic>
                  </div>
                </Tooltip>
              </Col>
              <Col :span="4">
                <div class="stat-item stat-item-total">
                  <Statistic
                    title="故障总时长(分钟)"
                    :value="businessImpactData.total_impact_time || 0"
                  >
                    <template #prefix>
                      <svg
                        class="stat-icon"
                        viewBox="0 0 24 24"
                        width="1em"
                        height="1em"
                        fill="currentColor"
                      >
                        <path
                          d="M15.5 16.5v-5h-5v-5h-5v15h15v-5h-5zm0-10v5H21v10H5.5v-15H15.5z"
                        />
                      </svg>
                    </template>
                  </Statistic>
                </div>
              </Col>
              <Col :span="4">
                <div class="stat-item stat-item-tickets">
                  <Statistic
                    title="故障总台次"
                    :value="businessImpactData.total_tickets || 0"
                  >
                    <template #prefix>
                      <svg
                        class="stat-icon"
                        viewBox="0 0 24 24"
                        width="1em"
                        height="1em"
                        fill="currentColor"
                      >
                        <path
                          d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"
                        />
                      </svg>
                    </template>
                  </Statistic>
                </div>
              </Col>
              <Col :span="4">
                <div class="stat-item stat-item-impact">
                  <Statistic
                    title="平均恢复时长(分钟)"
                    :value="businessImpactData.avg_impact_time || 0"
                    :precision="2"
                  >
                    <template #prefix>
                      <svg
                        class="stat-icon"
                        viewBox="0 0 24 24"
                        width="1em"
                        height="1em"
                        fill="currentColor"
                      >
                        <path
                          d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
                        />
                        <path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z" />
                      </svg>
                    </template>
                  </Statistic>
                </div>
              </Col>
            </Row>
          </Spin>
        </Card>
      </Col>
    </Row>

    <!-- SLA算法对比 -->
    <!-- <Row :gutter="24" class="dashboard-row">
      <Col :span="24">
        <SLASingleChart ref="slaChartRef" :project="searchParams.project" />
      </Col>
    </Row> -->

    <!-- 项目资源统计（移至燃尽图下面） -->
    <Row :gutter="24" class="dashboard-row" v-if="searchParams.project">
      <Col :span="24">
        <ProjectResourceCard
          :server-loading="loading.server"
          :cluster-loading="loading.cluster"
          :server-data="serverStatsData"
          :cluster-data="clusterStatsData"
          :project="searchParams.project"
        />
      </Col>
    </Row>

    <!-- 资源变更统计 -->
    <Row :gutter="24" class="dashboard-row" v-if="searchParams.project">
      <Col :span="24">
        <ResourceChangesCard
          :loading="loading.server || loading.cluster"
          :project="searchParams.project"
          :time-range="searchParams.time_range"
          :start-date="searchParams.start_date"
          :end-date="searchParams.end_date"
        />
      </Col>
    </Row>

    <!-- 故障统计信息（美化版卡片） -->
    <!-- <Row :gutter="24" class="dashboard-row">
      <Col :span="24">
        <Card title="故障" :bordered="false" class="fault-overview-card">
          <Spin
            :spinning="
              loading.businessImpact ||
              loading.durationStats ||
              loading.totalFaultDevices
            "
          >
            <Row :gutter="24" class="stat-row">
              <Col :span="4">
                <div class="stat-item stat-item-total">
                  <Statistic
                    title="总时长(分钟)"
                    :value="businessImpactData.total_impact_time || 0"
                  >
                    <template #prefix>
                      <svg
                        class="stat-icon"
                        viewBox="0 0 24 24"
                        width="1em"
                        height="1em"
                        fill="currentColor"
                      >
                        <path
                          d="M15.5 16.5v-5h-5v-5h-5v15h15v-5h-5zm0-10v5H21v10H5.5v-15H15.5z"
                        />
                      </svg>
                    </template>
                  </Statistic>
                </div>
              </Col>
              <Col :span="5">
                <div class="stat-item stat-item-devices">
                  <Statistic
                    title="总台数"
                    :value="totalFaultDevicesData.total_devices || 0"
                  >
                    <template #prefix>
                      <svg
                        class="stat-icon"
                        viewBox="0 0 24 24"
                        width="1em"
                        height="1em"
                        fill="currentColor"
                      >
                        <path
                          d="M20 18c1.1 0 1.99-.9 1.99-2L22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2H0v2h24v-2h-4zM4 6h16v10H4V6z"
                        />
                      </svg>
                    </template>
                  </Statistic>
                </div>
              </Col>
              <Col :span="5">
                <div class="stat-item stat-item-tickets">
                  <Statistic
                    title="总台次"
                    :value="businessImpactData.total_tickets || 0"
                  >
                    <template #prefix>
                      <svg
                        class="stat-icon"
                        viewBox="0 0 24 24"
                        width="1em"
                        height="1em"
                        fill="currentColor"
                      >
                        <path
                          d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"
                        />
                      </svg>
                    </template>
                  </Statistic>
                </div>
              </Col>
              <Col :span="5">
                <div class="stat-item stat-item-response">
                  <Statistic
                    title="平均响应时长(分钟)"
                    :value="durationStatsData.avgTimes?.[0] || 0"
                    :precision="2"
                  >
                    <template #prefix>
                      <svg
                        class="stat-icon"
                        viewBox="0 0 24 24"
                        width="1em"
                        height="1em"
                        fill="currentColor"
                      >
                        <path
                          d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
                        />
                      </svg>
                    </template>
                  </Statistic>
                </div>
              </Col>
              <Col :span="5">
                <div class="stat-item stat-item-impact">
                  <Statistic
                    title="平均恢复时长(分钟)"
                    :value="businessImpactData.avg_impact_time || 0"
                    :precision="2"
                  >
                    <template #prefix>
                      <svg
                        class="stat-icon"
                        viewBox="0 0 24 24"
                        width="1em"
                        height="1em"
                        fill="currentColor"
                      >
                        <path
                          d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
                        />
                        <path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z" />
                      </svg>
                    </template>
                  </Statistic>
                </div>
              </Col>
            </Row>
          </Spin>
        </Card>
      </Col>
    </Row> -->

    <!-- 故障单来源分布和故障分布（放在同一行） -->
    <Row :gutter="24" class="dashboard-row">
      <Col :span="8">
        <Card title="故障单来源分布" :bordered="false" class="chart-card">
          <SourceStatsChart
            :loading="loading.sourceStats"
            :data="sourceStatsData"
          />
        </Card>
      </Col>
      <Col :span="8">
        <Card title="提前客户故障发现率" :bordered="false" class="chart-card">
          <FaultDetectionRateCard
            :loading="loading.sourceStats"
            :project="searchParams.project"
          />
        </Card>
      </Col>
      <Col :span="8">
        <ColdMigrationRatioCard
          :loading="loading.faultDevicesDetails"
          :fault-data="faultDevicesDetailsData"
          :project="searchParams.project"
        />
      </Col>
    </Row>

    <!-- 维修方式和工单状态分布（新调整放在了一行） -->
    <!-- <Row :gutter="24" class="dashboard-row">
      <Col :span="12">
        <Card title="维修方式分布" :bordered="false" class="chart-card">
          <RepairMethodChart :search-params="searchParams" />
        </Card>
      </Col>
      <Col :span="12">
        <Card title="工单状态分布" :bordered="false" class="chart-card">
          <StatusStatsChart
            :loading="loading.statusStats"
            :data="statusStatsData"
          />
        </Card>
      </Col>
    </Row> -->

    <!-- GPU故障率 -->
    <!-- <Row :gutter="24" class="dashboard-row" v-if="searchParams.project">
      <Col :span="8">
        <GPUFaultRatioCard
          :loading="loading.gpuFaultRatio"
          :data="gpuFaultRatioData"
        />
      </Col> -->

    <!-- 故障分布 -->
    <!-- <Col :span="8">
        <Card title="故障分布" :bordered="false" class="chart-card">
          <FaultTypeChart :loading="loading.faultType" :data="faultTypeData" />
        </Card>
      </Col> -->

    <!-- 工单时间分布 -->
    <!-- <Col :span="8">
        <Card title="工单时间分布" :bordered="false" class="chart-card">
          <TicketTimeStatsChart
            :loading="loading.ticketTime"
            :data="ticketTimeStatsData"
          />
        </Card>
      </Col>
    </Row> -->

    <!-- <Row :gutter="24" class="dashboard-row"> -->
    <!-- 处理时长统计 -->
    <!-- <Col :span="24">
        <Card title="处理时长统计" :bordered="false" class="chart-card">
          <DurationStatsChart
            :loading="loading.durationStats"
            :data="durationStatsData"
          />
        </Card>
      </Col>
    </Row> -->

    <!-- 故障单详情列表 -->
    <Row :gutter="24" class="dashboard-row" v-if="searchParams.project">
      <Col :span="12">
        <Card title="故障单详情列表" :bordered="false" class="chart-card">
          <FaultTicket :search-params="searchParams" />
        </Card>
      </Col>
      <Col :span="12">
        <Card title="资源变更详情列表" :bordered="false" class="chart-card">
          <ResourceChangeDetail :search-params="searchParams" />
        </Card>
      </Col>
    </Row>
  </div>
</template>

<style lang="less" scoped>
.sla-dashboard {
  padding: 24px;

  .filter-container {
    margin-bottom: 24px;

    .filter-item {
      display: flex;
      align-items: center;

      .label {
        margin-right: 8px;
        white-space: nowrap;
      }
    }
  }

  .dashboard-row {
    margin-bottom: 24px;
  }

  .chart-card {
    height: 100%;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.09);
    }

    :deep(.ant-card-head) {
      padding: 0 16px;
      min-height: 48px;
      border-bottom: 1px solid #f0f0f0;

      .ant-card-head-title {
        padding: 12px 0;
        font-size: 16px;
        font-weight: 500;
      }
    }

    :deep(.ant-card-body) {
      padding: 16px;
    }
  }

  .fault-overview-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    margin-bottom: 20px;

    :deep(.ant-card-head) {
      border-bottom: 1px solid #f0f0f0;
      padding: 0 16px;
      min-height: 48px;

      .ant-card-head-title {
        padding: 12px 0;
        font-size: 16px;
        font-weight: 600;
        color: #1f1f1f;
      }
    }

    :deep(.ant-card-body) {
      padding: 24px;
    }

    .stat-row {
      margin-top: 8px;
    }

    .stat-item {
      padding: 16px;
      border-radius: 6px;
      transition: all 0.3s;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }

      .stat-icon {
        margin-right: 8px;
        font-size: 18px;
      }

      &-total {
        background-color: rgba(114, 46, 209, 0.08);
        :deep(.ant-statistic-title) {
          color: #722ed1;
        }
        .stat-icon {
          color: #722ed1;
        }
      }

      &-devices {
        background-color: rgba(82, 196, 26, 0.08);
        :deep(.ant-statistic-title) {
          color: #52c41a;
        }
        .stat-icon {
          color: #52c41a;
        }
      }

      &-tickets {
        background-color: rgba(24, 144, 255, 0.08);
        :deep(.ant-statistic-title) {
          color: #1890ff;
        }
        .stat-icon {
          color: #1890ff;
        }
      }

      &-response {
        background-color: rgba(250, 173, 20, 0.08);
        :deep(.ant-statistic-title) {
          color: #faad14;
        }
        .stat-icon {
          color: #faad14;
        }
      }

      &-impact {
        background-color: rgba(245, 34, 45, 0.08);
        :deep(.ant-statistic-title) {
          color: #f5222d;
        }
        .stat-icon {
          color: #f5222d;
        }
      }

      &-level1 {
        background-color: rgba(82, 196, 26, 0.08);
        :deep(.ant-statistic-title) {
          color: #52c41a;
        }
        .stat-icon {
          color: #52c41a;
        }
      }

      &-level2 {
        background-color: rgba(250, 173, 20, 0.08);
        :deep(.ant-statistic-title) {
          color: #faad14;
        }
        .stat-icon {
          color: #faad14;
        }
      }

      &-level3 {
        background-color: rgba(245, 34, 45, 0.08);
        :deep(.ant-statistic-title) {
          color: #cf1322;
        }
        .stat-icon {
          color: #cf1322;
        }
      }
    }

    :deep(.ant-statistic) {
      text-align: center;
    }

    :deep(.ant-statistic-title) {
      font-weight: 500;
      margin-bottom: 8px;
      font-size: 14px;
    }

    :deep(.ant-statistic-content) {
      font-size: 20px;
      font-weight: 600;
    }
  }
}
</style>
