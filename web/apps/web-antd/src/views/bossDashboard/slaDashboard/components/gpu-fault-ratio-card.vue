<script lang="ts" setup>
import type { GPUFaultRatioData } from '#/api/core/bossDashboard/sla-dashboard';

import { computed } from 'vue';

import { Card, Progress, Spin, Tooltip } from 'ant-design-vue';

// 定义组件属性
const props = defineProps<{
  data: GPUFaultRatioData;
  loading: boolean;
}>();

// 计算故障率百分比
const faultRatioPercent = computed(() => {
  if (!props.data || !props.data.fault_ratio_percent) {
    return 0;
  }
  return Number.parseFloat(props.data.fault_ratio_percent.toFixed(2));
});

// 确定故障率级别颜色
const progressColor = computed(() => {
  if (faultRatioPercent.value < 1) {
    return '#52c41a'; // 绿色: < 1%
  } else if (faultRatioPercent.value < 3) {
    return '#faad14'; // 黄色: 1% - 3%
  } else {
    return '#f5222d'; // 红色: > 3%
  }
});

// 确定故障率级别文本
const riskLevelText = computed(() => {
  if (faultRatioPercent.value < 1) {
    return '正常';
  } else if (faultRatioPercent.value < 3) {
    return '告警';
  } else {
    return '严重';
  }
});

// 检查是否有效数据
const hasData = computed(() => {
  return (
    props.data &&
    props.data.project &&
    typeof props.data.gpu_fault_ratio === 'number' &&
    props.data.time_range
  );
});

// 获取时间范围
const timeRange = computed(() => {
  return props.data?.time_range || { start: '-', end: '-' };
});

// 格式化大数字
const formatNumber = (num: number) => {
  return new Intl.NumberFormat().format(num);
};
</script>

<template>
  <Card :bordered="false" class="gpu-fault-card">
    <template #title>
      <div class="card-title">
        <span>GPU卡故障比</span>
        <Tooltip title="该项目中GPU故障工单数量与该项目GPU服务器数量*8的比值">
          <span class="question-icon">?</span>
        </Tooltip>
      </div>
    </template>
    <Spin :spinning="loading">
      <div v-if="hasData" class="gpu-fault-container">
        <!-- 中央仪表盘 -->
        <div class="progress-container">
          <div class="progress-wrapper">
            <Progress
              type="dashboard"
              :percent="faultRatioPercent"
              :stroke-color="progressColor"
              :format="() => `${faultRatioPercent}%`"
              :width="200"
              stroke-linecap="round"
              :stroke-width="8"
            />
            <div class="risk-level" :style="{ color: progressColor }">
              {{ riskLevelText }}
            </div>
          </div>
          <div class="progress-label">GPU卡故障率</div>
        </div>

        <!-- 简化的数字展示 -->
        <div class="simple-stats">
          <div class="stat-item">
            <div class="stat-label">故障卡数</div>
            <div class="stat-value">{{ data.gpu_fault_count }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">服务器数</div>
            <div class="stat-value">
              {{ formatNumber(data.gpu_server_count) }}
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-label">GPU卡总数</div>
            <div class="stat-value">
              {{ formatNumber(data.total_gpu_cards) }}
            </div>
          </div>
        </div>

        <div class="time-range">
          <div class="time-range-icon">📅</div>
          <div>统计时间范围: {{ timeRange.start }} 至 {{ timeRange.end }}</div>
        </div>
      </div>
      <div v-else-if="!loading" class="no-data">
        <div class="no-data-icon">📊</div>
        <p>暂无GPU故障比数据，请选择有GPU服务器的项目</p>
      </div>
    </Spin>
  </Card>
</template>

<style lang="less" scoped>
.gpu-fault-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  height: 100%;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.09);
  }

  .card-title {
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;

    .question-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 16px;
      height: 16px;
      line-height: 16px;
      text-align: center;
      background-color: #e6f7ff;
      color: #1890ff;
      border-radius: 50%;
      font-size: 12px;
      margin-left: 8px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background-color: #1890ff;
        color: #fff;
      }
    }
  }
}

.gpu-fault-container {
  padding: 24px 16px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .progress-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 32px;

    .progress-wrapper {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 16px;

      .risk-level {
        position: absolute;
        bottom: 6px;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .progress-label {
      margin-top: 12px;
      font-size: 16px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
  }

  .simple-stats {
    display: flex;
    justify-content: center;
    width: 100%;
    margin-bottom: 24px;

    .stat-item {
      text-align: center;
      padding: 0 24px;
      position: relative;

      &:not(:last-child):after {
        content: '';
        position: absolute;
        right: 0;
        top: 20%;
        height: 60%;
        width: 1px;
        background-color: #f0f0f0;
      }

      .stat-label {
        color: rgba(0, 0, 0, 0.45);
        font-size: 14px;
        margin-bottom: 4px;
      }

      .stat-value {
        font-size: 20px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }

  .time-range {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    background-color: #f5f5f5;
    border-radius: 20px;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    width: fit-content;

    .time-range-icon {
      margin-right: 8px;
    }
  }
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  color: rgba(0, 0, 0, 0.45);

  .no-data-icon {
    font-size: 32px;
    margin-bottom: 16px;
  }

  p {
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .simple-stats {
    flex-direction: column;
    gap: 16px;

    .stat-item {
      &:not(:last-child):after {
        display: none;
      }
    }
  }
}
</style>
