<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type {
  CalculateSLAParams,
  DailySLAData,
  SLACalculateData,
} from '#/api/core/bossDashboard/sla-dashboard';

import { computed, onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { message, Spin } from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  calculateSLA,
  getDailySLA,
} from '#/api/core/bossDashboard/sla-dashboard';

// 接收project参数
const props = defineProps<{
  project?: string;
}>();

// 加载状态
const loading = ref(false);
const loadingDaily = ref(false);

// 当前选择的年月和算法
const currentDate = ref(dayjs());
const currentAlgorithm = ref('100');

// SLA数据
const slaData = ref<Record<string, SLACalculateData | undefined>>({
  '100': undefined,
  '95': undefined,
  '90': undefined,
});

// 每日SLA数据
const dailySLAData = ref<DailySLAData | null>(null);

// 主图表引用
const mainChartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(mainChartRef);

// 计算当前选中算法的SLA数据
const currentSLAData = computed(() => {
  return slaData.value[currentAlgorithm.value];
});

// 计算SLA丢失分钟数
const lostMinutes = computed(() => {
  const data = currentSLAData.value;
  if (!data) return 0;
  return data.total_impact_minutes || 0;
});

// 计算格式化后的SLA百分比
const formattedSLA = computed(() => {
  const data = currentSLAData.value;
  if (!data) return '暂无数据';
  return `${data.sla_percentage.toFixed(4)}%`;
});

// 获取SLA计算结果
const fetchSLACalculate = async (algorithm: string) => {
  try {
    const params: CalculateSLAParams = {
      year: currentDate.value.year(),
      month: currentDate.value.month() + 1,
      algorithm,
      project: props.project,
    };

    const res = await calculateSLA(params);
    slaData.value[algorithm] = res as unknown as SLACalculateData;
    return true;
  } catch (error) {
    console.error(`获取${algorithm}算法SLA计算结果失败`, error);
    return false;
  }
};

// 获取每日SLA数据
const fetchDailySLA = async () => {
  try {
    loadingDaily.value = true;
    const params: CalculateSLAParams = {
      year: currentDate.value.year(),
      month: currentDate.value.month() + 1,
      project: props.project,
    };

    const res = await getDailySLA(params);
    dailySLAData.value = res as unknown as DailySLAData;
    return true;
  } catch (error) {
    console.error('获取每日SLA数据失败', error);
    return false;
  } finally {
    loadingDaily.value = false;
  }
};

// 渲染图表
const renderChart = () => {
  if (!mainChartRef.value || !dailySLAData.value) return;

  const data = dailySLAData.value;
  const dates = data.chart_data.dates;

  // 获取当前日期
  const today = dayjs().format('YYYY-MM-DD');

  // 找到当前日期在日期数组中的索引位置
  const todayIndex = dates.findIndex((date) => date >= today);

  // 创建包含实线和虚线部分的数据
  const createSegmentedData = (dataArray: number[] | undefined) => {
    if (!dataArray) return [];

    // 如果没有找到今天的日期或者今天是最后一天，则全部使用实线
    if (todayIndex === -1 || todayIndex >= dates.length - 1) {
      return [
        {
          type: 'solid',
          data: dataArray,
        },
      ];
    }

    // 将数据分为今天及之前(实线)和今天之后(虚线)两部分
    return [
      {
        type: 'solid',
        data: dataArray.slice(0, todayIndex + 1),
      },
      {
        type: 'dashed',
        data: [
          ...Array.from({ length: todayIndex + 1 }).fill(null),
          ...dataArray.slice(todayIndex + 1),
        ],
      },
    ];
  };

  // 分割三种算法的数据
  const segmented100 = createSegmentedData(data.chart_data.sla_100);
  const segmented95 = createSegmentedData(data.chart_data.sla_95);
  const segmented90 = createSegmentedData(data.chart_data.sla_90);

  // 折线图显示范围 - 从99.9开始，确保最后一位小数变化也能明显看出
  const minDisplay = 99.9;

  const chartOptions = {
    title: {
      text: '每日SLA趋势',
      left: 'center',
      top: 5,
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
      },
      formatter: (params: any) => {
        if (!params || params.length === 0) return '';

        let result = `${params[0].axisValue}<br>`;
        params.forEach((param: any) => {
          // 只显示有值的点的提示
          if (param.value !== null && param.value !== undefined) {
            const marker = `<span style="display:inline-block;margin-right:4px;border-radius:50%;width:10px;height:10px;background-color:${param.color};"></span>`;
            result += `${marker + param.seriesName}: ${param.value.toFixed(
              4,
            )}%<br>`;
          }
        });
        return result;
      },
      enterable: false, // 提示框鼠标不可进入，避免卡住
      confine: true, // 限制提示框在图表区域内
    },
    legend: {
      data: ['Level 3 (100%)', 'Level 2 (95%)', 'Level 1 (90%)'],
      bottom: 0,
      selectedMode: false, // 不允许通过图例切换显示状态
      textStyle: {
        fontSize: 12,
      },
      itemWidth: 15,
      itemHeight: 10,
    },
    grid: {
      left: '3%',
      right: '4%',
      top: '40px',
      bottom: '30px',
      containLabel: true,
    },
    xAxis: {
      type: 'category' as const,
      data: dates,
      axisLabel: {
        rotate: 45,
        interval: Math.max(1, Math.floor(dates.length / 8)), // 自动计算间隔，确保不会太拥挤
        formatter: (value: string) => {
          // 只显示日期部分，不显示年份
          return value.slice(5);
        },
        fontSize: 10,
      },
      boundaryGap: false,
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          opacity: 0.2,
        },
      },
    },
    yAxis: {
      type: 'value',
      min: minDisplay, // 从99.9%开始显示，确保最小值变化能明显看出
      max: 100,
      interval: 0.02, // 设置更小的间隔
      axisLabel: {
        formatter: '{value}%',
        fontSize: 10,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          opacity: 0.2,
        },
      },
    },
    series: [
      // 标准算法 - 实线部分(添加区域着色)
      ...(segmented100.length > 0 && segmented100[0]
        ? [
            {
              name: 'Level 3 (100%)',
              type: 'line',
              data: segmented100[0].data,
              lineStyle: {
                width: currentAlgorithm.value === '100' ? 3 : 2,
                color: '#00C170',
              },
              itemStyle: {
                color: '#00C170',
              },
              emphasis: {
                focus: 'series',
                lineStyle: {
                  width: 4,
                },
              },
              symbol:
                currentAlgorithm.value === '100' ? 'circle' : 'emptyCircle',
              symbolSize: currentAlgorithm.value === '100' ? 7 : 4,
              smooth: true,
              z: 3, // 确保标准算法线在最上层
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#00C17033', // 30% 透明度
                    },
                    {
                      offset: 1,
                      color: '#00C17001', // 几乎透明
                    },
                  ],
                },
              },
              connectNulls: true,
              showSymbol: false, // 仅在hover时显示标记点
              scale: true,
            },
          ]
        : []),
      // 标准算法 - 虚线部分
      ...(segmented100.length > 1 && segmented100[1]
        ? [
            {
              name: 'Level 3 (100%)',
              type: 'line',
              data: segmented100[1].data,
              lineStyle: {
                width: currentAlgorithm.value === '100' ? 3 : 2,
                color: '#00C170',
                type: 'dashed',
              },
              itemStyle: {
                color: '#00C170',
              },
              emphasis: {
                focus: 'series',
              },
              symbol:
                currentAlgorithm.value === '100' ? 'circle' : 'emptyCircle',
              symbolSize: currentAlgorithm.value === '100' ? 7 : 4,
              smooth: true,
              z: 3,
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#00C17022', // 较低透明度
                    },
                    {
                      offset: 1,
                      color: '#00C17001',
                    },
                  ],
                },
              },
              connectNulls: true,
              showSymbol: false,
              scale: true,
            },
          ]
        : []),

      // 容忍算法 - 实线部分
      ...(segmented95.length > 0 && segmented95[0]
        ? [
            {
              name: 'Level 2 (95%)',
              type: 'line',
              data: segmented95[0].data,
              lineStyle: {
                width: currentAlgorithm.value === '95' ? 3 : 2,
                color: '#91CC75',
              },
              itemStyle: {
                color: '#91CC75',
              },
              emphasis: {
                focus: 'series',
              },
              symbol:
                currentAlgorithm.value === '95' ? 'circle' : 'emptyCircle',
              symbolSize: currentAlgorithm.value === '95' ? 7 : 4,
              smooth: true,
              z: 2, // 中间层
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#91CC7533',
                    },
                    {
                      offset: 1,
                      color: '#91CC7501',
                    },
                  ],
                },
              },
              connectNulls: true,
              showSymbol: false,
              scale: true,
            },
          ]
        : []),
      // 容忍算法 - 虚线部分
      ...(segmented95.length > 1 && segmented95[1]
        ? [
            {
              name: 'Level 2 (95%)',
              type: 'line',
              data: segmented95[1].data,
              lineStyle: {
                width: currentAlgorithm.value === '95' ? 3 : 2,
                color: '#91CC75',
                type: 'dashed',
              },
              itemStyle: {
                color: '#91CC75',
              },
              emphasis: {
                focus: 'series',
              },
              symbol:
                currentAlgorithm.value === '95' ? 'circle' : 'emptyCircle',
              symbolSize: currentAlgorithm.value === '95' ? 7 : 4,
              smooth: true,
              z: 2,
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#91CC7522',
                    },
                    {
                      offset: 1,
                      color: '#91CC7501',
                    },
                  ],
                },
              },
              connectNulls: true,
              showSymbol: false,
              scale: true,
            },
          ]
        : []),

      // 弹性算法 - 实线部分
      ...(segmented90.length > 0 && segmented90[0]
        ? [
            {
              name: 'Level 1 (90%)',
              type: 'line',
              data: segmented90[0].data,
              lineStyle: {
                width: currentAlgorithm.value === '90' ? 3 : 2,
                color: '#FFB800',
              },
              itemStyle: {
                color: '#FFB800',
              },
              emphasis: {
                focus: 'series',
              },
              symbol:
                currentAlgorithm.value === '90' ? 'circle' : 'emptyCircle',
              symbolSize: currentAlgorithm.value === '90' ? 7 : 4,
              smooth: true,
              z: 1, // 最底层
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#FFB80033',
                    },
                    {
                      offset: 1,
                      color: '#FFB80001',
                    },
                  ],
                },
              },
              connectNulls: true,
              showSymbol: false,
              scale: true,
            },
          ]
        : []),
      // 弹性算法 - 虚线部分
      ...(segmented90.length > 1 && segmented90[1]
        ? [
            {
              name: 'Level 1 (90%)',
              type: 'line',
              data: segmented90[1].data,
              lineStyle: {
                width: currentAlgorithm.value === '90' ? 3 : 2,
                color: '#FFB800',
                type: 'dashed',
              },
              itemStyle: {
                color: '#FFB800',
              },
              emphasis: {
                focus: 'series',
              },
              symbol:
                currentAlgorithm.value === '90' ? 'circle' : 'emptyCircle',
              symbolSize: currentAlgorithm.value === '90' ? 7 : 4,
              smooth: true,
              z: 1,
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#FFB80022',
                    },
                    {
                      offset: 1,
                      color: '#FFB80001',
                    },
                  ],
                },
              },
              connectNulls: true,
              showSymbol: false,
              scale: true,
            },
          ]
        : []),
    ],
  };

  renderEcharts(chartOptions as any);
};

// 获取所有算法的SLA数据
const fetchAllData = async () => {
  loading.value = true;

  try {
    // 获取新数据
    await Promise.all([
      fetchSLACalculate('100'),
      fetchSLACalculate('95'),
      fetchSLACalculate('90'),
      fetchDailySLA(), // 同时获取每日数据
    ]);

    renderChart();
  } catch (error) {
    console.error('获取SLA数据时发生错误:', error);
    message.error({
      content: 'SLA数据加载失败，请刷新重试',
      key: 'slaLoading',
    });
  } finally {
    loading.value = false;
  }
};

// 获取算法名称
const getAlgorithmName = (algorithm: string) => {
  switch (algorithm) {
    case '90': {
      return 'Level 1 (90%)';
    }
    case '95': {
      return 'Level 2 (95%)';
    }
    case '100': {
      return 'Level 3 (100%)';
    }
    default: {
      return '未知算法';
    }
  }
};

// 获取算法对应的颜色
const getAlgorithmColor = (algorithm: string) => {
  switch (algorithm) {
    case '90': {
      return '#FFB800';
    }
    case '95': {
      return '#91CC75';
    }
    case '100': {
      return '#00C170';
    }
    default: {
      return '#999';
    }
  }
};

// 切换算法
const handleAlgorithmChange = (algorithm: string) => {
  currentAlgorithm.value = algorithm;
};

// 监听数据变化
watch(
  dailySLAData,
  () => {
    renderChart();
  },
  { deep: true },
);

// 监听算法变化
watch(
  () => currentAlgorithm.value,
  () => {
    renderChart();
  },
);

// 监听project变化
watch(
  () => props.project,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      fetchAllData();
    }
  },
  { immediate: true },
);

// 监听日期变化
watch(
  () => currentDate.value,
  () => {
    fetchAllData();
  },
);

// 组件挂载
onMounted(() => {
  fetchAllData();
});

// 向父组件暴露方法
defineExpose({
  setDate: (date: dayjs.Dayjs) => {
    currentDate.value = date;
  },
  setAlgorithm: (algorithm: string) => {
    currentAlgorithm.value = algorithm;
  },
});
</script>

<template>
  <div class="sla-chart-container">
    <Spin :spinning="loading || loadingDaily" tip="数据加载中...">
      <div class="sla-content-wrapper">
        <div class="chart-wrapper">
          <EchartsUI ref="mainChartRef" height="400px" class="chart-instance" />
        </div>

        <div class="stats-wrapper">
          <div class="stats-header">
            <div class="current-title">
              {{ currentDate.format('YYYY年MM月') }}SLA统计
            </div>
            <div class="algorithm-tabs">
              <div
                v-for="algorithm in ['100', '95', '90']"
                :key="algorithm"
                class="algorithm-tab"
                :class="{ active: currentAlgorithm === algorithm }"
                :style="{
                  borderColor:
                    currentAlgorithm === algorithm
                      ? getAlgorithmColor(algorithm)
                      : 'transparent',
                }"
                @click="handleAlgorithmChange(algorithm)"
              >
                <div
                  class="color-dot"
                  :style="{ backgroundColor: getAlgorithmColor(algorithm) }"
                ></div>
                {{
                  algorithm === '100'
                    ? 'Level 3'
                    : algorithm === '95'
                      ? 'Level 2'
                      : 'Level 1'
                }}
              </div>
            </div>
          </div>

          <div class="stats-content">
            <div class="stats-item highlight">
              <div class="item-label">
                {{ getAlgorithmName(currentAlgorithm) }}
              </div>
              <div
                class="item-value"
                :style="{ color: getAlgorithmColor(currentAlgorithm) }"
              >
                {{ formattedSLA }}
              </div>
            </div>

            <div class="stats-item" v-if="currentSLAData">
              <div class="item-label">总工单数</div>
              <div class="item-value">
                {{ currentSLAData.sla_tickets_count || 0 }}个
              </div>
            </div>

            <div class="stats-item" v-if="currentSLAData">
              <div class="item-label">排除工单</div>
              <div class="item-value">
                {{ currentSLAData.excluded_count || 0 }}个
              </div>
            </div>

            <div class="stats-item" v-if="currentSLAData">
              <div class="item-label">SLA丢失时间</div>
              <div class="item-value error">{{ lostMinutes }}分钟</div>
            </div>

            <div
              class="stats-item"
              v-if="currentSLAData && currentSLAData.excluded_minutes"
            >
              <div class="item-label">排除维护时间</div>
              <div class="item-value">
                {{ currentSLAData.excluded_minutes }}分钟
              </div>
            </div>

            <!-- <div class="stats-item" v-if="currentSLAData">
              <div class="item-label">总可用时间</div>
              <div class="item-value">{{ currentSLAData.total_minutes || 0 }}分钟</div>
            </div> -->
          </div>
        </div>
      </div>
    </Spin>
  </div>
</template>

<style lang="less" scoped>
.sla-chart-container {
  width: 100%;
  background-color: #fafafa;
  border-radius: 6px;
  padding: 8px;
  height: 420px;

  .sla-content-wrapper {
    display: flex;
    width: 100%;
    height: 100%;

    .chart-wrapper {
      flex: 1;
      min-width: 0;
      margin-right: 12px;

      .chart-instance {
        border-radius: 6px;
        overflow: hidden;
        width: 100%;
        background-color: #fff;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
      }
    }

    .stats-wrapper {
      width: 280px;
      background-color: #fff;
      border-radius: 6px;
      padding: 12px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
      display: flex;
      flex-direction: column;

      .stats-header {
        margin-bottom: 16px;

        .current-title {
          font-size: 16px;
          font-weight: bold;
          color: #333;
          text-align: center;
          margin-bottom: 10px;
        }

        .algorithm-tabs {
          display: flex;
          justify-content: space-between;
          margin-bottom: 6px;

          .algorithm-tab {
            flex: 1;
            text-align: center;
            padding: 4px 0;
            cursor: pointer;
            font-size: 13px;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover {
              background-color: #f8f8f8;
            }

            &.active {
              font-weight: bold;
            }

            .color-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              margin-right: 4px;
            }
          }
        }
      }

      .stats-content {
        flex: 1;
        display: flex;
        flex-direction: column;

        .stats-item {
          margin-bottom: 8px;

          &.highlight {
            padding: 8px;
            background-color: #f9f9f9;
            border-radius: 6px;
            margin: 0 0 12px;
          }

          .item-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 2px;
          }

          .item-value {
            font-size: 15px;
            font-weight: bold;
            color: #333;

            &.error {
              color: #ff4d4f;
            }
          }
        }
      }
    }
  }
}
</style>
