<script lang="ts" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';
import type { TimeRangeParams } from '#/api/core/bossDashboard/sla-dashboard';

import { onMounted, watch } from 'vue';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

const props = defineProps<{
  searchParams: TimeRangeParams;
}>();

// 表格配置
const gridOptions: VxeGridProps = {
  columns: [
    { title: '序号', type: 'seq', width: 60 },
    { field: 'change_no', title: '变更单号', width: 220 },
    { field: 'sn', title: 'SN', width: 180 },
    {
      field: 'change_type',
      title: '变更类型',
      width: 120,
      formatter: ({ cellValue }) => (cellValue === 'online' ? '上线' : '下线'),
    },
    {
      field: 'change_time',
      title: '变更时间',
      width: 180,
    },
  ],
  height: '500px',
  border: true,
  stripe: true,
  pagerConfig: {
    enabled: false,
    pageSize: 10,
    pageSizes: [10, 20, 50, 100],
  },
  sortConfig: {
    multiple: true,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!props.searchParams.project) {
          return { items: [], total: 0 };
        }

        try {
          // 模拟数据，需要替换为实际的API调用
          // const res = await getResourceChangeDetails({
          //   ...props.searchParams,
          //   page: page.currentPage,
          //   pageSize: page.pageSize,
          // });

          // 模拟数据
          const mockData = {
            details: [
              // {
              //   change_no: 'CHG20240601001',
              //   sn: 'SVR2024060101',
              //   change_type: 'online',
              //   change_time: '2024-06-01 10:30:00'
              // },
              // {
              //   change_no: 'CHG20240602002',
              //   sn: 'SVR2024060102',
              //   change_type: 'offline',
              //   change_time: '2024-06-02 15:45:00'
              // }
            ],
            total: 0,
          };

          return {
            items: mockData.details || [],
            total: mockData.total || 0,
          };
        } catch (error) {
          console.error('获取资源变更详情失败', error);
          message.error('获取资源变更详情失败');
          return { items: [], total: 0 };
        }
      },
    },
  },
};

const gridEvents: VxeGridListeners = {};

const [Grid, gridApi] = useVbenVxeGrid({ gridEvents, gridOptions });

// 监听搜索参数变化，重新加载表格数据
watch(
  () => props.searchParams,
  () => {
    if (gridApi) {
      gridApi.reload();
    }
  },
  { deep: true },
);

onMounted(() => {
  if (props.searchParams.project && gridApi) {
    gridApi.reload();
  }
});
</script>

<template>
  <div class="resource-change-table">
    <Grid>
      <template #empty>
        <span>{{
          props.searchParams.project ? '暂无数据' : '请先选择项目'
        }}</span>
      </template>
    </Grid>
  </div>
</template>

<style lang="less" scoped>
.resource-change-table {
  width: 100%;
}
</style>
