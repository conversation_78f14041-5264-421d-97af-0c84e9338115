<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { SLAStatsData } from '#/api/core/bossDashboard/sla-dashboard';

import { computed, onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

const props = defineProps<{
  data?: SLAStatsData;
  loading?: boolean;
}>();

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

// 计算SLA达标率
const complianceRate = computed(() => {
  if (!props.data) return 0;
  return props.data.complianceRate || 0;
});

function renderChart() {
  if (!chartRef.value || !props.data) return;

  // 使用SLA达标率展示一个单独的图表
  renderEcharts({
    title: {
      text: 'SLA达标情况',
      left: 'center',
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}%',
    },
    series: [
      {
        type: 'gauge',
        startAngle: 90,
        endAngle: -270,
        pointer: {
          show: false,
        },
        progress: {
          show: true,
          overlap: false,
          roundCap: true,
          clip: false,
          itemStyle: {
            color: '#67C23A', // 默认颜色
          },
        },
        axisLine: {
          lineStyle: {
            width: 20,
            color: [
              [0.95, '#F56C6C'], // 低于95%显示红色
              [0.99, '#E6A23C'], // 95%-99%显示黄色
              [1, '#67C23A'], // 99%以上显示绿色
            ],
          },
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        data: [
          {
            value: complianceRate.value,
            name: 'SLA达标率',
            title: {
              offsetCenter: ['0%', '-30%'],
            },
            detail: {
              valueAnimation: true,
              offsetCenter: ['0%', '0%'],
            },
          },
        ],
        title: {
          fontSize: 14,
        },
        detail: {
          width: 50,
          height: 14,
          fontSize: 26,
          color: 'auto',
          formatter: '{value}%',
        },
      },
    ],
  } as any);
}

watch(
  () => props.data,
  () => {
    renderChart();
  },
  { deep: true },
);

watch(
  () => props.loading,
  () => {
    if (!props.loading) {
      renderChart();
    }
  },
);

onMounted(() => {
  renderChart();
});
</script>

<template>
  <EchartsUI ref="chartRef" height="300px" />
</template>
