<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { SourceStatsData } from '#/api/core/bossDashboard/sla-dashboard';

import { computed, onMounted, onBeforeUnmount, ref, watch, nextTick } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

const props = defineProps<{
  data?: SourceStatsData;
  loading?: boolean;
}>();

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

function renderChart() {
  if (
    !chartRef.value ||
    !props.data ||
    !props.data.legend ||
    !props.data.series
  )
    return;

  nextTick(() => {
    const legendData = props.data?.legend || [];
    const seriesData = props.data?.series || [];
    
    renderEcharts({
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      legend: {
        orient: 'horizontal',
        bottom: 0,
        data: legendData,
      },
      series: seriesData.map((item) => ({
        ...item,
        radius: ['40%', '70%'],
        center: ['50%', '50%'],
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          formatter: '{b}: {c} ({d}%)',
        },
      })),
    } as any);
  });
}

watch(
  () => props.data,
  () => {
    renderChart();
  },
  { deep: true },
);

watch(
  () => props.loading,
  () => {
    if (!props.loading) {
      renderChart();
    }
  },
);

// 添加窗口大小变化的监听
const handleResize = () => {
  renderChart();
};

onMounted(() => {
  nextTick(() => {
    renderChart();
    window.addEventListener('resize', handleResize);
  });
});

// 组件卸载时移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
});

// 获取总数
const totalTickets = computed(() => {
  return props.data?.total || 0;
});
</script>

<template>
  <div class="source-chart-container">
    <div class="chart-header">
      <div class="total-info">
        <div class="total-value">{{ totalTickets }}</div>
        <div class="total-label">总工单数</div>
      </div>
    </div>
    <div class="chart-wrapper">
      <EchartsUI ref="chartRef" height="300px" style="width: 100%" />
    </div>
  </div>
</template>

<style scoped>
.source-chart-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.chart-header {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
}

.chart-wrapper {
  flex: 1;
  width: 100%;
  min-height: 300px;
  position: relative;
}

.total-info {
  text-align: center;
}

.total-value {
  font-size: 28px;
  font-weight: bold;
  color: #1890ff;
}

.total-label {
  font-size: 14px;
  color: rgb(0 0 0 / 45%);
}
</style>
