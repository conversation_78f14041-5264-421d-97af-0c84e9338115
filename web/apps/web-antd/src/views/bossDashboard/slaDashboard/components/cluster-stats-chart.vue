<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { ClusterStatsData } from '#/api/core/bossDashboard/sla-dashboard';

import { onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

const props = defineProps<{
  data?: ClusterStatsData;
  loading?: boolean;
}>();

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

function renderChart() {
  if (!chartRef.value || !props.data || !props.data.chart_data) return;

  const chartData = props.data.chart_data;
  if (chartData.length === 0) return;

  const legendData = chartData.map((item) => item.name);

  renderEcharts({
    title: {
      text: '集群类型统计',
      left: 'center',
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: legendData,
    },
    series: [
      {
        name: '集群类型',
        type: 'pie',
        radius: '55%',
        center: ['50%', '60%'],
        data: chartData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  });
}

watch(
  () => props.data,
  () => {
    renderChart();
  },
  { deep: true },
);

watch(
  () => props.loading,
  () => {
    if (!props.loading) {
      renderChart();
    }
  },
);

onMounted(() => {
  renderChart();
});
</script>

<template>
  <EchartsUI ref="chartRef" height="300px" />
</template>
