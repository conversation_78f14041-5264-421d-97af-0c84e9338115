<script lang="ts" setup>
import type { ResourceChangesData } from '#/api/core/bossDashboard/sla-dashboard';

import { computed, onMounted, ref, watch } from 'vue';

import { Card, Col, message, Progress, Row, Spin } from 'ant-design-vue';

import { getResourceChangesStats } from '#/api/core/bossDashboard/sla-dashboard';

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  project: {
    type: String,
    default: '',
  },
  timeRange: {
    type: String,
    default: 'last_7_days',
  },
  startDate: {
    type: String,
    default: '',
  },
  endDate: {
    type: String,
    default: '',
  },
});

// 数据状态
const data = ref<ResourceChangesData>({
  added: {
    total: 0,
    types: [],
    trend: 0,
  },
  removed: {
    total: 0,
    types: [],
    trend: 0,
  },
});

const internalLoading = ref(false);

// 获取资源变更统计数据
const fetchResourceChangesStats = async () => {
  try {
    internalLoading.value = true;

    const params: Record<string, any> = {
      time_range: props.timeRange,
    };

    if (props.project) {
      params.project = props.project;
    }

    if (props.timeRange === 'custom') {
      if (props.startDate) params.start_date = props.startDate;
      if (props.endDate) params.end_date = props.endDate;
    }

    const response = await getResourceChangesStats(params);
    data.value = response;
  } catch (error) {
    console.error('获取资源变更统计失败:', error);
    message.error('获取资源变更统计失败');
  } finally {
    internalLoading.value = false;
  }
};

// 监听props变化，重新获取数据
watch(
  () => [props.project, props.timeRange, props.startDate, props.endDate],
  () => {
    fetchResourceChangesStats();
  },
  { deep: true },
);

// 组件挂载时获取数据
onMounted(() => {
  fetchResourceChangesStats();
});

// 计算是否显示加载状态
const isLoading = computed(() => props.loading || internalLoading.value);
</script>

<template>
  <Card title="资源变更统计" :bordered="false" class="resource-changes-card">
    <Spin :spinning="isLoading">
      <Row :gutter="24" class="detail-row">
        <Col :span="12">
          <div class="change-section change-section-added">
            <div class="section-header">
              <div class="section-title">
                <svg
                  class="stat-icon"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                </svg>
                <span>新增资源</span>
              </div>
              <div class="section-total">
                <span>{{ data.added.total }} 台</span>
                <div class="trend-indicator positive">
                  <span>+{{ data.added.trend }}%</span>
                  <svg
                    class="trend-icon"
                    viewBox="0 0 24 24"
                    width="0.8em"
                    height="0.8em"
                    fill="currentColor"
                  >
                    <path
                      d="M4 12l1.41 1.41L11 7.83V20h2V7.83l5.59 5.58L20 12l-8-8-8 8z"
                    />
                  </svg>
                </div>
              </div>
            </div>
            <div class="resource-list">
              <div
                v-for="(item, index) in data.added.types"
                :key="`added-${index}`"
                class="resource-item"
              >
                <div class="resource-info">
                  <span class="resource-name">{{ item.name }}</span>
                  <span class="resource-count">{{ item.count }} 台</span>
                </div>
                <div class="progress-container">
                  <Progress
                    :percent="item.percent"
                    stroke-color="#52c41a"
                    :show-info="false"
                    :stroke-width="8"
                  />
                  <span class="progress-text">{{ item.percent }}%</span>
                </div>
              </div>
            </div>
          </div>
        </Col>
        <Col :span="12">
          <div class="change-section change-section-removed">
            <div class="section-header">
              <div class="section-title">
                <svg
                  class="stat-icon"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path d="M19 13H5v-2h14v2z" />
                </svg>
                <span>下线资源</span>
              </div>
              <div class="section-total">
                <span>{{ data.removed.total }} 台</span>
                <div class="trend-indicator negative">
                  <span>{{ data.removed.trend }}%</span>
                  <svg
                    class="trend-icon"
                    viewBox="0 0 24 24"
                    width="0.8em"
                    height="0.8em"
                    fill="currentColor"
                    style="transform: rotate(180deg)"
                  >
                    <path
                      d="M4 12l1.41 1.41L11 7.83V20h2V7.83l5.59 5.58L20 12l-8-8-8 8z"
                    />
                  </svg>
                </div>
              </div>
            </div>
            <div class="resource-list">
              <div
                v-for="(item, index) in data.removed.types"
                :key="`removed-${index}`"
                class="resource-item"
              >
                <div class="resource-info">
                  <span class="resource-name">{{ item.name }}</span>
                  <span class="resource-count">{{ item.count }} 台</span>
                </div>
                <div class="progress-container">
                  <Progress
                    :percent="item.percent"
                    stroke-color="#f5222d"
                    :show-info="false"
                    :stroke-width="8"
                  />
                  <span class="progress-text">{{ item.percent }}%</span>
                </div>
              </div>
            </div>
          </div>
        </Col>
      </Row>
    </Spin>
  </Card>
</template>

<style lang="less" scoped>
.resource-changes-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.09);
  }

  :deep(.ant-card-head) {
    padding: 0 16px;
    min-height: 48px;
    border-bottom: 1px solid #f0f0f0;

    .ant-card-head-title {
      padding: 12px 0;
      font-size: 16px;
      font-weight: 500;
    }
  }

  :deep(.ant-card-body) {
    padding: 16px;
  }

  .summary-section {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;

    .total-change {
      text-align: center;
      padding: 12px 24px;
      background-color: rgba(0, 0, 0, 0.02);
      border-radius: 8px;

      .total-label {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        margin-bottom: 4px;
      }

      .total-value {
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 4px;

        &.positive {
          color: #52c41a;
        }

        &.negative {
          color: #f5222d;
        }
      }

      .total-percent {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;

        .positive {
          color: #52c41a;
        }

        .negative {
          color: #f5222d;
        }

        .trend-icon {
          margin-left: 4px;
        }
      }
    }
  }

  .detail-row {
    margin-top: 10px;
  }

  .change-section {
    padding: 16px;
    border-radius: 8px;
    height: 100%;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &-added {
      background: linear-gradient(
        135deg,
        rgba(82, 196, 26, 0.05) 0%,
        rgba(82, 196, 26, 0.15) 100%
      );
      border: 1px solid rgba(82, 196, 26, 0.2);
    }

    &-removed {
      background: linear-gradient(
        135deg,
        rgba(245, 34, 45, 0.05) 0%,
        rgba(245, 34, 45, 0.15) 100%
      );
      border: 1px solid rgba(245, 34, 45, 0.2);
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .section-title {
        font-size: 16px;
        font-weight: 500;
        display: flex;
        align-items: center;

        .stat-icon {
          margin-right: 8px;
          font-size: 18px;
        }
      }

      .section-total {
        font-size: 18px;
        font-weight: 600;
        display: flex;
        flex-direction: column;
        align-items: flex-end;

        .trend-indicator {
          font-size: 12px;
          display: flex;
          align-items: center;
          margin-top: 4px;

          &.positive {
            color: #52c41a;
          }

          &.negative {
            color: #f5222d;
          }

          .trend-icon {
            margin-left: 2px;
          }
        }
      }
    }

    &-added {
      .section-title {
        color: #52c41a;

        .stat-icon {
          color: #52c41a;
        }
      }

      .section-total {
        color: #52c41a;
      }
    }

    &-removed {
      .section-title {
        color: #f5222d;

        .stat-icon {
          color: #f5222d;
        }
      }

      .section-total {
        color: #f5222d;
      }
    }

    .resource-list {
      margin-top: 12px;
    }

    .resource-item {
      display: flex;
      flex-direction: column;
      padding: 10px 0;
      border-bottom: 1px dashed rgba(0, 0, 0, 0.06);

      &:last-child {
        border-bottom: none;
      }

      .resource-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
      }

      .resource-name {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.75);
      }

      .resource-count {
        font-size: 14px;
        font-weight: 500;
      }

      .progress-container {
        display: flex;
        align-items: center;

        :deep(.ant-progress) {
          flex: 1;
          margin-right: 10px;
        }

        .progress-text {
          font-size: 12px;
          color: rgba(0, 0, 0, 0.65);
          width: 35px;
          text-align: right;
        }
      }
    }
  }
}
</style>
