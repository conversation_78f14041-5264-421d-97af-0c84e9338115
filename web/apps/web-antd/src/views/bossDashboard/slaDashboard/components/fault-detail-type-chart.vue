<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { FaultDetailTypeStatsData } from '#/api/core/bossDashboard/sla-dashboard';

import { computed, onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

const props = defineProps<{
  data?: FaultDetailTypeStatsData;
  loading?: boolean;
}>();

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

function renderChart() {
  if (
    !chartRef.value ||
    !props.data ||
    !props.data.legend ||
    !props.data.series
  )
    return;

  renderEcharts({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderColor: '#e6e6e6',
      borderWidth: 1,
      textStyle: {
        color: '#333',
      },
      padding: [8, 12],
    },
    legend: {
      type: 'scroll',
      orient: 'horizontal',
      bottom: 5,
      itemWidth: 12,
      itemHeight: 12,
      textStyle: {
        fontSize: 11,
      },
      pageTextStyle: {
        color: '#666',
      },
      data: props.data.legend,
    },
    grid: {
      top: 10,
      bottom: 60,
      left: 30,
      right: 30,
      containLabel: true,
    },
    series: props.data.series.map((item) => ({
      ...item,
      radius: ['40%', '65%'],
      center: ['50%', '42%'],
      avoidLabelOverlap: true,
      stillShowZeroSum: false,
      itemStyle: {
        borderRadius: 8,
        borderColor: '#fff',
        borderWidth: 2,
        shadowBlur: 5,
        shadowColor: 'rgba(0, 0, 0, 0.1)',
      },
      label: {
        show: true,
        formatter: (params: any) => {
          return `${params.name}:\n${params.value}(${params.percent}%)`;
        },
        fontSize: 11,
        fontWeight: 'bold',
        lineHeight: 14,
        padding: [4, 2],
        position: 'outside',
        alignTo: 'none',
        distanceToLabelLine: 3,
        overflow: 'truncate',
      },
      labelLine: {
        show: true,
        length: 18,
        length2: 15,
        smooth: 0.2,
        maxSurfaceAngle: 80,
        minTurnAngle: 80,
        lineStyle: {
          width: 1.2,
          type: 'solid',
        },
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.3)',
        },
        label: {
          fontSize: 12,
        },
      },
      labelLayout: {
        hideOverlap: true,
        draggable: true,
        moveOverlap: true,
      },
    })),
  } as any);
}

watch(
  () => props.data,
  () => {
    renderChart();
  },
  { deep: true },
);

watch(
  () => props.loading,
  () => {
    if (!props.loading) {
      renderChart();
    }
  },
);

onMounted(() => {
  renderChart();
});

const totalTickets = computed(() => {
  return props.data?.total || 0;
});
</script>

<template>
  <div class="fault-detail-chart-container">
    <div class="chart-header">
      <div class="total-info">
        <div class="total-value">{{ totalTickets }}</div>
        <div class="total-label">总工单数</div>
      </div>
    </div>
    <EchartsUI ref="chartRef" :height="totalTickets ? '350px' : '380px'" />
  </div>
</template>

<style lang="less" scoped>
.fault-detail-chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: center;
  margin-bottom: 12px;
}

.total-info {
  text-align: center;
  background: #f9f9f9;
  border-radius: 20px;
  padding: 6px 16px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.total-value {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
  line-height: 1.2;
}

.total-label {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}
</style>
