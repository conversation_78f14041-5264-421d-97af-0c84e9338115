<script lang="ts" setup>
import type {
  ClusterStatsData,
  ServerStatsData,
} from '#/api/core/bossDashboard/sla-dashboard';

import { computed, defineProps } from 'vue';

import { Card, Col, Row } from 'ant-design-vue';

// 定义组件属性
const props = defineProps({
  serverLoading: {
    type: Boolean,
    default: false,
  },
  clusterLoading: {
    type: Boolean,
    default: false,
  },
  serverData: {
    type: Object as () => ServerStatsData,
    default: () => ({}),
  },
  clusterData: {
    type: Object as () => ClusterStatsData,
    default: () => ({}),
  },
  project: {
    type: String,
    default: '',
  },
});

// 集群卡片颜色
const clusterColors = ['#FF9800', '#f5222d', '#52c41a', '#1890ff'];

// 计算属性：当前项目的GPU服务器数量
const gpuServerCount = computed(() => {
  if (!props.project || !props.serverData.project_asset_type_stats) {
    return 0;
  }

  // 处理数组格式的数据结构
  if (Array.isArray(props.serverData.project_asset_type_stats)) {
    const gpuServerStat = props.serverData.project_asset_type_stats.find(
      (stat) =>
        stat.project === props.project && stat.asset_type === 'gpu_server',
    );
    return gpuServerStat?.count || 0;
  }

  // 处理对象格式的数据结构（向后兼容）
  return (
    props.serverData.project_asset_type_stats[props.project]?.gpu_server || 0
  );
});

// 计算属性：当前项目的集群列表
const clusterList = computed(() => {
  if (!props.project || !props.clusterData.project_cluster_stats) {
    return [];
  }

  return props.clusterData.project_cluster_stats[props.project] || [];
});

// 计算属性：是否显示资源卡片
const shouldShowCard = computed(() => {
  return (
    !!props.project &&
    (!!props.serverData.project_asset_type_stats ||
      !!props.clusterData.project_cluster_stats)
  );
});

// 计算属性：集群卡片在右侧区域内的栅格宽度
const clusterInRowSpan = computed(() => {
  const count = clusterList.value.length;
  if (count === 0) return 24;
  if (count === 1) return 24;
  if (count === 2) return 12;
  return 12; // 如果有3个及以上，每行显示2个
});
</script>

<template>
  <div v-if="shouldShowCard" class="project-resource-card">
    <div class="card-title">{{ project }} 项目资源概览</div>

    <!-- 主要内容区域 -->
    <Row :gutter="[16, 16]">
      <!-- GPU服务器数量卡片 -->
      <Col :span="6">
        <Card class="gpu-card" :loading="serverLoading" :bordered="false">
          <div class="gpu-content">
            <div class="stat-title">GPU服务器数量</div>
            <div class="gpu-value">{{ gpuServerCount.toLocaleString() }}</div>
            <div class="gpu-icon">
              <svg
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M5 2C3.89543 2 3 2.89543 3 4V9C3 10.1046 3.89543 11 5 11H19C20.1046 11 21 10.1046 21 9V4C21 2.89543 20.1046 2 19 2H5Z"
                  fill="#1890ff"
                />
                <path
                  d="M5 13C3.89543 13 3 13.8954 3 15V20C3 21.1046 3.89543 22 5 22H19C20.1046 22 21 21.1046 21 20V15C21 13.8954 20.1046 13 19 13H5Z"
                  fill="#1890ff"
                />
                <path d="M8 5H6V8H8V5Z" fill="white" />
                <path d="M12 5H10V8H12V5Z" fill="white" />
                <path d="M16 5H14V8H16V5Z" fill="white" />
                <path d="M8 16H6V19H8V16Z" fill="white" />
                <path d="M12 16H10V19H12V16Z" fill="white" />
                <path d="M16 16H14V19H16V16Z" fill="white" />
              </svg>
            </div>
          </div>
        </Card>
      </Col>

      <!-- 资源规格分布区域 -->
      <Col :span="18">
        <div class="distribution-section">
          <!-- <div class="section-title">资源规格分布</div> -->
          <Row
            :gutter="[16, 16]"
            class="cluster-row"
            v-if="clusterList.length > 0"
          >
            <Col
              :span="clusterInRowSpan"
              v-for="(cluster, index) in clusterList"
              :key="cluster.cluster"
            >
              <Card
                class="cluster-card"
                :loading="clusterLoading"
                :class="`cluster-card-${index % 4}`"
                :bordered="false"
              >
                <div class="cluster-content">
                  <div class="cluster-info">
                    <div class="cluster-title">{{ cluster.cluster }}</div>
                    <div class="cluster-value">
                      {{ cluster.count.toLocaleString() }}
                    </div>
                  </div>
                  <div class="cluster-icon">
                    <svg
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 2L2 7L12 12L22 7L12 2Z"
                        :fill="clusterColors[index % 4]"
                      />
                      <path
                        d="M2 17L12 22L22 17M2 12L12 17L22 12"
                        :stroke="clusterColors[index % 4]"
                        stroke-width="2"
                      />
                    </svg>
                  </div>
                </div>
              </Card>
            </Col>
          </Row>

          <div v-if="clusterList.length === 0" class="empty-cluster-message">
            暂无集群数据
          </div>
        </div>
      </Col>
    </Row>
  </div>
</template>

<style lang="less" scoped>
.project-resource-card {
  margin-bottom: 16px;

  .card-title {
    font-size: 18px;
    font-weight: bold;
    color: #1890ff;
    margin-bottom: 16px;
  }

  :deep(.ant-row) {
    align-items: stretch;
  }
}

// GPU服务器卡片样式
.gpu-card {
  background: linear-gradient(135deg, #001529, #003a70);
  border-radius: 8px;
  height: 120px; // 固定高度
  overflow: hidden;

  :deep(.ant-card-body) {
    padding: 0;
    height: 100%;
  }

  .gpu-content {
    padding: 24px;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .stat-title {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.85);
    margin-bottom: 16px;
  }

  .gpu-value {
    font-size: 36px;
    font-weight: bold;
    color: #1890ff;
    line-height: 1;
    margin-bottom: 8px;
  }

  .gpu-icon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;

    svg {
      width: 100%;
      height: 100%;
    }
  }
}

// 资源规格分布区域样式
.distribution-section {
  height: 100%;
  display: flex;
  flex-direction: column;

  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 16px;
    position: relative;
    padding-left: 12px;

    &:before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background-color: #52c41a;
      border-radius: 2px;
    }
  }

  .cluster-row {
    display: flex;
    align-items: stretch;
  }

  .empty-cluster-message {
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 24px 0;
  }
}

// 集群卡片样式
.cluster-card {
  border-radius: 8px;
  overflow: hidden;
  height: 120px; // 固定高度，与GPU卡片保持一致

  :deep(.ant-card-body) {
    padding: 0;
    height: 100%;
  }

  .cluster-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px;
    height: 100%;
  }

  .cluster-info {
    flex: 1;
  }

  .cluster-title {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.85);
    margin-bottom: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .cluster-value {
    font-size: 32px;
    font-weight: bold;
    line-height: 1;
  }

  .cluster-icon {
    width: 40px;
    height: 40px;

    svg {
      width: 100%;
      height: 100%;
    }
  }

  &.cluster-card-0 {
    background: linear-gradient(135deg, #3e1a00, #613400);

    .cluster-value {
      color: #ff9800;
    }
  }

  &.cluster-card-1 {
    background: linear-gradient(135deg, #5c0011, #8c0d0d);

    .cluster-value {
      color: #f5222d;
    }
  }

  &.cluster-card-2 {
    background: linear-gradient(135deg, #092b00, #135200);

    .cluster-value {
      color: #52c41a;
    }
  }

  &.cluster-card-3 {
    background: linear-gradient(135deg, #003a70, #0050a0);

    .cluster-value {
      color: #1890ff;
    }
  }
}
</style>
