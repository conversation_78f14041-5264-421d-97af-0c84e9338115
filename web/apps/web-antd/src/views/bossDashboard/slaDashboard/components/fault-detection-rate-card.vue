<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import {
  computed,
  nextTick,
  onBeforeUnmount,
  onMounted,
  ref,
  watch,
} from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { Spin } from 'ant-design-vue';

// 定义props
defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  project: {
    type: String,
    default: '',
  },
});

// 图表DOM引用
const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

// 模拟数据
const data = ref({
  totalIncidents: 0,
  earlyDetected: 0,
  monthlyData: [
    { month: '1月', rate: 0 },
    { month: '2月', rate: 0 },
    { month: '3月', rate: 0 },
    { month: '4月', rate: 0 },
    { month: '5月', rate: 0 },
  ],
});

// 计算故障提前发现率
const earlyDetectionRate = computed(() => {
  if (!data.value.totalIncidents) return 0;
  return Math.round(
    (data.value.earlyDetected / data.value.totalIncidents) * 100,
  );
});

// 计算趋势变化
const trend = computed(() => {
  const monthlyData = data.value.monthlyData;
  if (!monthlyData || monthlyData.length < 2) return 0;

  const lastItem = monthlyData[monthlyData.length - 1];
  const prevItem = monthlyData[monthlyData.length - 2];

  if (!lastItem || !prevItem) return 0;

  return lastItem.rate - prevItem.rate;
});

// 渲染图表
const renderChart = () => {
  if (!chartRef.value) return;

  nextTick(() => {
    const months = data.value.monthlyData.map((item) => item.month);
    const rates = data.value.monthlyData.map((item) => item.rate);

    renderEcharts({
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: {c}%',
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '8%',
        top: '8%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: months,
        axisLine: {
          lineStyle: {
            color: '#E0E0E0',
          },
        },
        axisLabel: {
          color: '#666',
          fontSize: 10,
        },
      },
      yAxis: {
        type: 'value',
        name: '提前发现率 (%)',
        nameTextStyle: {
          color: '#666',
          fontSize: 10,
          align: 'left',
        },
        nameLocation: 'middle',
        nameGap: 40,
        min: 0,
        max: 100,
        interval: 20,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#E0E0E0',
          },
        },
        axisLabel: {
          show: true,
          color: '#666',
          fontSize: 10,
          formatter: '{value}%',
        },
        splitLine: {
          lineStyle: {
            color: '#E9E9E9',
            type: 'dashed',
          },
        },
      },
      series: [
        {
          name: '提前发现率',
          type: 'line',
          data: rates,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 3,
            color: '#1890FF',
          },
          itemStyle: {
            color: '#1890FF',
          },
          areaStyle: {
            color: 'rgba(24, 144, 255, 0.1)',
          },
        },
      ],
    } as any);
  });
};

// 监听数据变化
watch(
  () => data.value,
  () => {
    renderChart();
  },
  { deep: true },
);

// 添加窗口大小变化的监听
const handleResize = () => {
  renderChart();
};

// 生命周期钩子
onMounted(() => {
  nextTick(() => {
    renderChart();
    window.addEventListener('resize', handleResize);
  });
});

// 组件卸载时移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
});
</script>

<template>
  <div class="fault-detection-rate-card">
    <Spin :spinning="loading">
      <div class="chart-container">
        <div class="stats-header">
          <div class="stat-item">
            <div class="stat-value">{{ earlyDetectionRate }}%</div>
            <div class="stat-label">故障提前发现率</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ data.totalIncidents || 0 }}</div>
            <div class="stat-label">总故障数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ data.earlyDetected || 0 }}</div>
            <div class="stat-label">提前发现数</div>
          </div>
        </div>
        <div class="chart-wrapper">
          <EchartsUI ref="chartRef" height="220px" style="width: 100%" />
        </div>
        <div class="trend-info">
          <div class="trend-item">
            <div class="trend-title">较上月</div>
            <div class="trend-value" :class="[trend > 0 ? 'up' : 'down']">
              {{ trend > 0 ? '+' : '' }}{{ trend }}%
              <span class="trend-icon">
                <svg
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  :style="{
                    transform: trend > 0 ? 'rotate(0deg)' : 'rotate(180deg)',
                  }"
                >
                  <path
                    d="M4 12l1.41 1.41L11 7.83V20h2V7.83l5.59 5.58L20 12l-8-8-8 8z"
                    fill="currentColor"
                  />
                </svg>
              </span>
            </div>
          </div>
        </div>
      </div>
    </Spin>
  </div>
</template>

<style lang="less" scoped>
.fault-detection-rate-card {
  height: 100%;

  .chart-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
  }

  .chart-wrapper {
    position: relative;
    height: 220px;
    width: 100%;
  }

  .stats-header {
    display: flex;
    justify-content: space-around;
    margin-bottom: 16px;

    .stat-item {
      text-align: center;
      padding: 8px 12px;
      border-radius: 8px;
      background-color: rgba(24, 144, 255, 0.08);

      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: #1890ff;
      }

      .stat-label {
        font-size: 12px;
        color: #666;
        margin-top: 4px;
      }
    }
  }

  .trend-info {
    display: flex;
    justify-content: center;
    margin-top: 12px;

    .trend-item {
      display: flex;
      align-items: center;

      .trend-title {
        font-size: 12px;
        color: #999;
        margin-right: 8px;
      }

      .trend-value {
        font-size: 14px;
        font-weight: 500;
        display: flex;
        align-items: center;

        &.up {
          color: #52c41a;
        }

        &.down {
          color: #f5222d;
        }

        .trend-icon {
          margin-left: 4px;
          display: flex;
          align-items: center;
        }
      }
    }
  }
}
</style>
