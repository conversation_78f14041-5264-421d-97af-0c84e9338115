<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { StatusStatsData } from '#/api/core/bossDashboard/sla-dashboard';

import { onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

const props = defineProps<{
  data?: StatusStatsData;
  loading?: boolean;
}>();

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

function renderChart() {
  if (
    !chartRef.value ||
    !props.data ||
    !props.data.legend ||
    !props.data.series
  )
    return;

  // 将饼图转换为横向柱状图
  const series = props.data.series[0];
  if (!series || !series.data) return;

  // 按数量排序，并限制显示数量避免过多
  const sortedData = [...series.data].sort((a, b) => b.value - a.value);
  const displayData = sortedData.slice(0, 10); // 显示前10项

  renderEcharts({
    title: {
      text: '工单状态分布',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
    },
    yAxis: {
      type: 'category',
      data: displayData.map((item) => item.name),
      axisLabel: {
        interval: 0,
        width: 100,
        overflow: 'break',
      },
    },
    series: [
      {
        name: '工单数量',
        type: 'bar',
        data: displayData.map((item) => item.value),
      },
    ],
  });
}

watch(
  () => props.data,
  () => {
    renderChart();
  },
  { deep: true },
);

watch(
  () => props.loading,
  () => {
    if (!props.loading) {
      renderChart();
    }
  },
);

onMounted(() => {
  renderChart();
});
</script>

<template>
  <EchartsUI ref="chartRef" height="300px" />
</template>
