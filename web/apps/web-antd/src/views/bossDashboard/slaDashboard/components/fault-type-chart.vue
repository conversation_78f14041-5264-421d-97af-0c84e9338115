<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { FaultTypeStatsData } from '#/api/core/bossDashboard/sla-dashboard';

import { computed, onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

const props = defineProps<{
  data?: FaultTypeStatsData;
  loading?: boolean;
}>();

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

function renderChart() {
  if (
    !chartRef.value ||
    !props.data ||
    !props.data.legend ||
    !props.data.series
  )
    return;

  renderEcharts({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderColor: '#e6e6e6',
      borderWidth: 1,
      textStyle: {
        color: '#333',
      },
      padding: [8, 12],
    },
    legend: {
      orient: 'horizontal',
      left: 'center',
      bottom: '5%',
      itemWidth: 12,
      itemHeight: 12,
      textStyle: {
        fontSize: 12,
      },
      data: props.data.legend,
    },
    series: props.data.series.map((item) => ({
      ...item,
      radius: ['40%', '70%'],
      center: ['50%', '40%'],
      roseType: 'radius',
      itemStyle: {
        borderRadius: 6,
        borderColor: '#fff',
        borderWidth: 1,
        shadowBlur: 5,
        shadowColor: 'rgba(0, 0, 0, 0.1)',
      },
      label: {
        formatter: '{b}: {c} ({d}%)',
        fontSize: 12,
        fontWeight: 'bold',
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.3)',
        },
      },
    })),
  } as any);
}

watch(
  () => props.data,
  () => {
    renderChart();
  },
  { deep: true },
);

watch(
  () => props.loading,
  () => {
    if (!props.loading) {
      renderChart();
    }
  },
);

onMounted(() => {
  renderChart();
});

// 获取总数
const totalTickets = computed(() => {
  return props.data?.total || 0;
});
</script>

<template>
  <div class="fault-type-chart-container">
    <div class="chart-header">
      <div class="total-info">
        <div class="total-value">{{ totalTickets }}</div>
        <div class="total-label">总工单数</div>
      </div>
    </div>
    <EchartsUI ref="chartRef" :height="totalTickets ? '320px' : '350px'" />
  </div>
</template>

<style lang="less" scoped>
.fault-type-chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.total-info {
  text-align: center;
  background: #f9f9f9;
  border-radius: 20px;
  padding: 8px 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.total-value {
  font-size: 28px;
  font-weight: bold;
  color: #1890ff;
  line-height: 1.2;
}

.total-label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
}
</style>
