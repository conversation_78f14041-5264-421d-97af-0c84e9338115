<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { TimeRangeParams } from '#/api/core/bossDashboard/sla-dashboard';

import { computed, onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { message } from 'ant-design-vue';

import { getFaultTicketDetails } from '#/api/core/bossDashboard/sla-dashboard';

const props = defineProps<{
  loading?: boolean;
  searchParams: TimeRangeParams;
}>();

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);
const chartLoading = ref(false);
const repairStats = ref({
  hardwareRepair: 0,
  softwareRepair: 0,
  total: 0,
});

// 获取维修方式统计数据
async function fetchRepairMethodStats() {
  if (!props.searchParams.project) {
    return;
  }

  try {
    chartLoading.value = true;
    const res = await getFaultTicketDetails(props.searchParams);

    // 统计维修方式
    let hardwareRepair = 0;
    let softwareRepair = 0;

    if (res.details && Array.isArray(res.details)) {
      res.details.forEach((ticket) => {
        if (ticket.repair_method === 'hardware_fix') {
          hardwareRepair++;
        } else {
          softwareRepair++;
        }
      });

      repairStats.value = {
        hardwareRepair,
        softwareRepair,
        total: res.details.length,
      };
    }
  } catch (error) {
    console.error('获取维修方式统计失败', error);
    message.error('获取维修方式统计失败');
  } finally {
    chartLoading.value = false;
  }
}

// 准备图表数据
const chartData = computed(() => {
  const { hardwareRepair, softwareRepair } = repairStats.value;
  return {
    legend: ['硬修复', '软修复'],
    series: [
      {
        name: '维修方式',
        type: 'pie',
        data: [
          { name: '硬修复', value: hardwareRepair },
          { name: '软修复', value: softwareRepair },
        ],
      },
    ],
  };
});

// 渲染图表
function renderChart() {
  if (!chartRef.value || repairStats.value.total === 0) return;

  const data = chartData.value;

  renderEcharts({
    title: {
      text: '维修方式分布',
      left: 'center',
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: data.legend,
    },
    series: data.series.map((item) => ({
      ...item,
      radius: ['30%', '60%'],
      center: ['50%', '60%'],
      roseType: 'radius',
      itemStyle: {
        borderRadius: 5,
      },
      label: {
        formatter: '{b}: {c} ({d}%)',
      },
    })),
  } as any);
}

// 监听搜索参数变化
watch(
  () => props.searchParams,
  () => {
    fetchRepairMethodStats();
  },
  { deep: true },
);

// 监听统计数据变化，更新图表
watch(
  () => repairStats.value,
  () => {
    renderChart();
  },
  { deep: true },
);

// 监听加载状态
watch(
  () => chartLoading.value,
  () => {
    if (!chartLoading.value) {
      renderChart();
    }
  },
);

onMounted(() => {
  fetchRepairMethodStats();
});
</script>

<template>
  <div class="repair-method-chart">
    <div class="chart-summary" v-if="repairStats.total > 0">
      <div class="summary-item">
        <span class="label">总计工单：</span>
        <span class="value">{{ repairStats.total }}个</span>
      </div>
      <div class="summary-item">
        <span class="label">硬修复：</span>
        <span class="value">{{ repairStats.hardwareRepair }}个</span>
        <span class="ratio" v-if="repairStats.total > 0">
          ({{
            ((repairStats.hardwareRepair / repairStats.total) * 100).toFixed(2)
          }}%)
        </span>
      </div>
      <div class="summary-item">
        <span class="label">软修复：</span>
        <span class="value">{{ repairStats.softwareRepair }}个</span>
        <span class="ratio" v-if="repairStats.total > 0">
          ({{
            ((repairStats.softwareRepair / repairStats.total) * 100).toFixed(2)
          }}%)
        </span>
      </div>
    </div>
    <EchartsUI ref="chartRef" height="300px" :loading="chartLoading" />
  </div>
</template>

<style lang="less" scoped>
.repair-method-chart {
  .chart-summary {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-around;

    .summary-item {
      text-align: center;

      .label {
        font-weight: bold;
        margin-right: 8px;
      }

      .value {
        color: #1890ff;
        font-weight: bold;
      }

      .ratio {
        color: #666;
        margin-left: 4px;
      }
    }
  }
}
</style>
