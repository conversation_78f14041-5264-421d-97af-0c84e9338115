<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { ServerStatsData } from '#/api/core/bossDashboard/sla-dashboard';

import { computed, onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

const props = defineProps<{
  data?: ServerStatsData;
  loading?: boolean;
}>();

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

// 从服务器状态统计数据中提取饼图数据
const pieChartData = computed(() => {
  if (!props.data) return [];

  // 使用业务状态统计数据
  if (props.data.biz_status_stats && props.data.biz_status_stats.length > 0) {
    return props.data.biz_status_stats.map((item) => ({
      name: getStatusDisplayName(item.status),
      value: item.count,
    }));
  }

  // 如果没有状态统计数据，使用简单统计
  return [
    { name: '活跃', value: props.data.active_count || 0 },
    { name: '故障', value: props.data.outage_count || 0 },
    { name: '维护中', value: props.data.maintaining_count || 0 },
    { name: '备份', value: props.data.backup_count || 0 },
    { name: '已分配', value: props.data.allocated_count || 0 },
    { name: '未分配', value: props.data.unallocated_count || 0 },
  ].filter((item) => item.value > 0);
});

// 状态名称转换
function getStatusDisplayName(status: string): string {
  const statusMap: Record<string, string> = {
    active: '活跃',
    outage: '故障',
    maintaining: '维护中',
    backup: '备份',
    allocated: '已分配',
    unallocated: '未分配',
  };

  return statusMap[status] || status;
}

function renderChart() {
  if (!chartRef.value || !props.data) return;

  const chartData = pieChartData.value;
  if (chartData.length === 0) return;

  const legendData = chartData.map((item) => item.name);

  renderEcharts({
    title: {
      text: '服务器状态统计',
      left: 'center',
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: legendData,
    },
    series: [
      {
        name: '服务器状态',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: chartData,
      },
    ],
  });
}

watch(
  () => props.data,
  () => {
    renderChart();
  },
  { deep: true },
);

watch(
  () => props.loading,
  () => {
    if (!props.loading) {
      renderChart();
    }
  },
);

onMounted(() => {
  renderChart();
});
</script>

<template>
  <EchartsUI ref="chartRef" height="300px" />
</template>
