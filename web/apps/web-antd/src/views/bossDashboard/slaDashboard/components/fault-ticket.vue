<script lang="ts" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';
import type { TimeRangeParams } from '#/api/core/bossDashboard/sla-dashboard';

import { onMounted, watch } from 'vue';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getFaultTicketDetails } from '#/api/core/bossDashboard/sla-dashboard';

const props = defineProps<{
  searchParams: TimeRangeParams;
}>();

// 格式化业务影响时长，将分钟转换为 x天y小时z分钟
const formatDuration = (minutes: number): string => {
  if (minutes === 0) {
    return '0分钟';
  }

  const days = Math.floor(minutes / (24 * 60));
  const hours = Math.floor((minutes % (24 * 60)) / 60);
  const mins = minutes % 60;

  const parts: string[] = [];
  if (days > 0) {
    parts.push(`${days}天`);
  }
  if (hours > 0) {
    parts.push(`${hours}小时`);
  }
  if (mins > 0) {
    parts.push(`${mins}分钟`);
  }

  return parts.join('');
};

// 表格配置
const gridOptions: VxeGridProps = {
  columns: [
    { title: '序号', type: 'seq', width: 60 },
    { field: 'ticket_no', title: '工单号', width: 220 },
    { field: 'resource_identifier', title: '租户IP', width: 160 },
    { field: 'fault_detail_type', title: '故障原因', width: 180 },
    {
      field: 'business_impact_time',
      title: '故障时长',
      width: 160,
      formatter: ({ cellValue }) => formatDuration(cellValue),
    },
    { field: 'repair_method', title: '维修方法', width: 140, visible: false },
    {
      field: 'is_cold_migration',
      title: '是否冷迁移',
      width: 100,
      formatter: ({ cellValue }) => (cellValue ? '是' : '否'),
    },
  ],
  height: '500px',
  border: true,
  stripe: true,
  pagerConfig: {
    enabled: false,
    pageSize: 10,
    pageSizes: [10, 20, 50, 100],
  },
  sortConfig: {
    multiple: true,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }) => {
        if (!props.searchParams.project) {
          return { items: [], total: 0 };
        }

        try {
          const res = await getFaultTicketDetails({
            ...props.searchParams,
            page: page.currentPage,
            pageSize: page.pageSize,
          });

          return {
            items: res.details || [],
            total: res.total || 0,
          };
        } catch (error) {
          console.error('获取故障单详情失败', error);
          message.error('获取故障单详情失败');
          return { items: [], total: 0 };
        }
      },
    },
  },
};

const gridEvents: VxeGridListeners = {};

const [Grid, gridApi] = useVbenVxeGrid({ gridEvents, gridOptions });

// 监听搜索参数变化，重新加载表格数据
watch(
  () => props.searchParams,
  () => {
    if (gridApi) {
      gridApi.reload();
    }
  },
  { deep: true },
);

onMounted(() => {
  if (props.searchParams.project && gridApi) {
    gridApi.reload();
  }
});
</script>

<template>
  <div class="fault-ticket-table">
    <Grid>
      <template #empty>
        <span>{{
          props.searchParams.project ? '暂无数据' : '请先选择项目'
        }}</span>
      </template>
    </Grid>
  </div>
</template>

<style lang="less" scoped>
.fault-ticket-table {
  width: 100%;
}
</style>
