<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { DurationStatsData } from '#/api/core/bossDashboard/sla-dashboard';

import { onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

const props = defineProps<{
  data?: DurationStatsData;
  loading?: boolean;
}>();

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

function renderChart() {
  if (
    !chartRef.value ||
    !props.data ||
    !props.data.phases ||
    !props.data.avgTimes
  )
    return;

  // 直接使用原始分钟数据，不做转换
  const avgTimes = props.data.avgTimes;

  renderEcharts({
    title: {
      text: '处理时长统计',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter(params: any) {
        const index = params[0].dataIndex;
        const time = props.data?.avgTimes?.[index] || 0;
        return `${params[0].name}: ${time.toFixed(1)} 分钟`;
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: props.data.phases,
      axisLabel: {
        interval: 0,
        rotate: 30,
      },
    },
    yAxis: {
      type: 'value',
      name: '平均时长(分钟)',
      // 根据数据范围自动调整
      min: 0,
      max(value: { max: number }) {
        return Math.ceil(value.max * 1.1); // 为了美观，最大值增加10%的空间
      },
    },
    series: [
      {
        name: '平均处理时长',
        type: 'bar',
        data: avgTimes,
        markLine: {
          data: [{ type: 'average', name: '平均值' }],
        },
      },
    ],
  } as any);
}

watch(
  () => props.data,
  () => {
    renderChart();
  },
  { deep: true },
);

watch(
  () => props.loading,
  () => {
    if (!props.loading) {
      renderChart();
    }
  },
);

onMounted(() => {
  renderChart();
});
</script>

<template>
  <EchartsUI ref="chartRef" height="300px" />
</template>
