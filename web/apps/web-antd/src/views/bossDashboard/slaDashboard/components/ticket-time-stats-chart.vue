<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { TicketTimeStatsData } from '#/api/core/bossDashboard/sla-dashboard';

import { onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

const props = defineProps<{
  data?: TicketTimeStatsData;
  loading?: boolean;
}>();

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

function renderChart() {
  if (!chartRef.value || !props.data || !props.data.xAxis || !props.data.series)
    return;

  renderEcharts({
    title: {
      text: '工单时间分布',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: props.data.xAxis,
    },
    yAxis: {
      type: 'value',
    },
    series: props.data.series.map((item) => ({
      ...item,
      areaStyle: {},
      emphasis: {
        focus: 'series',
      },
    })),
  } as any);
}

watch(
  () => props.data,
  () => {
    renderChart();
  },
  { deep: true },
);

watch(
  () => props.loading,
  () => {
    if (!props.loading) {
      renderChart();
    }
  },
);

onMounted(() => {
  renderChart();
});
</script>

<template>
  <EchartsUI ref="chartRef" height="300px" />
</template>
