<script lang="ts" setup>
import type { BusinessImpactData } from '#/api/core/bossDashboard/sla-dashboard';

import { computed } from 'vue';

import { Card, Col, Row, Spin, Statistic } from 'ant-design-vue';

const props = defineProps<{
  data?: BusinessImpactData;
  loading?: boolean;
}>();

// 获取影响最严重的故障类型
const mostImpactType = computed(() => {
  if (
    !props.data ||
    !props.data.type_statistics ||
    props.data.type_statistics.length === 0
  ) {
    return '无';
  }

  // 找出影响时间最长的类型
  const sortedTypes = [...(props.data.type_statistics || [])].sort(
    (a, b) => b.time - a.time,
  );
  return sortedTypes[0]?.type || '未分类';
});

// 安全获取数据的辅助函数
const safeData = computed(() => {
  return (
    props.data || {
      impact_time_display: '0',
      total_tickets: 0,
      avg_impact_time: 0,
      type_statistics: [],
    }
  );
});

// 计算业务影响总时长（分钟）
const impactTimeInMinutes = computed(() => {
  if (!props.data) return 0;

  // 假设原始数据中有以分钟为单位的值，或需要从其他格式转换
  // 这里根据实际情况可能需要调整
  const rawMinutes = props.data.avg_impact_time * props.data.total_tickets;
  return Math.round(rawMinutes) || 0;
});
</script>

<template>
  <Card title="SLA统计信息" :bordered="false" class="sla-dashboard-card">
    <Spin :spinning="loading || false">
      <Row :gutter="24" class="stat-row">
        <Col :span="6">
          <div class="stat-item stat-item-time">
            <Statistic
              title="业务影响总时长(分钟)"
              :value="impactTimeInMinutes"
              :precision="0"
            >
              <template #prefix>
                <svg
                  class="stat-icon"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path
                    d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z"
                  />
                </svg>
              </template>
            </Statistic>
          </div>
        </Col>
        <Col :span="6">
          <div class="stat-item stat-item-tickets">
            <Statistic
              title="影响工单总数"
              :value="safeData.total_tickets || 0"
              :precision="0"
            >
              <template #prefix>
                <svg
                  class="stat-icon"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path
                    d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"
                  />
                </svg>
              </template>
            </Statistic>
          </div>
        </Col>
        <Col :span="6">
          <div class="stat-item stat-item-avg">
            <Statistic
              title="平均影响时长(分钟)"
              :value="safeData.avg_impact_time || 0"
              :precision="0"
            >
              <template #prefix>
                <svg
                  class="stat-icon"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path
                    d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z"
                  />
                </svg>
              </template>
            </Statistic>
          </div>
        </Col>
        <Col :span="6">
          <div class="stat-item stat-item-type">
            <Statistic title="影响最严重故障类型" :value="mostImpactType">
              <template #prefix>
                <svg
                  class="stat-icon"
                  viewBox="0 0 24 24"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                >
                  <path
                    d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"
                  />
                </svg>
              </template>
            </Statistic>
          </div>
        </Col>
      </Row>
    </Spin>
  </Card>
</template>

<style lang="less" scoped>
.sla-dashboard-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;

  :deep(.ant-card-head) {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;

    .ant-card-head-title {
      font-size: 16px;
      font-weight: 600;
      color: #1f1f1f;
    }
  }

  :deep(.ant-card-body) {
    padding: 24px;
  }

  .stat-row {
    margin-top: 8px;
  }

  .stat-item {
    padding: 16px;
    border-radius: 6px;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }

    .stat-icon {
      margin-right: 8px;
      font-size: 18px;
    }

    &-time {
      background-color: rgba(24, 144, 255, 0.08);
      :deep(.ant-statistic-title) {
        color: #1890ff;
      }
      .stat-icon {
        color: #1890ff;
      }
    }

    &-tickets {
      background-color: rgba(82, 196, 26, 0.08);
      :deep(.ant-statistic-title) {
        color: #52c41a;
      }
      .stat-icon {
        color: #52c41a;
      }
    }

    &-avg {
      background-color: rgba(250, 173, 20, 0.08);
      :deep(.ant-statistic-title) {
        color: #faad14;
      }
      .stat-icon {
        color: #faad14;
      }
    }

    &-type {
      background-color: rgba(255, 77, 79, 0.08);
      :deep(.ant-statistic-title) {
        color: #ff4d4f;
      }
      .stat-icon {
        color: #ff4d4f;
      }
    }
  }

  :deep(.ant-statistic) {
    text-align: center;
  }

  :deep(.ant-statistic-title) {
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 14px;
  }

  :deep(.ant-statistic-content) {
    font-size: 20px;
    font-weight: 600;
  }
}
</style>
