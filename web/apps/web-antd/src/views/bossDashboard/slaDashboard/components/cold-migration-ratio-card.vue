<script lang="ts" setup>
import { computed, ref, watch } from 'vue';

import { Card, Progress, Spin } from 'ant-design-vue';

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  faultData: {
    type: Object,
    default: () => ({
      details: [],
      total: 0,
    }),
  },
  project: {
    type: String,
    default: '',
  },
});

// 冷迁移次数和总次数
const coldMigrationCount = ref(0);
const totalCount = ref(0);
const averageImpactTime = ref<null | number>(null);

// 根据比例计算渐变颜色
const strokeColor = computed(() => {
  return {
    '0%': '#1890ff',
    '100%': '#13c2c2',
  };
});

// 计算冷迁移比率
const migrationRatio = computed(() => {
  if (totalCount.value === 0) return 0;
  return Math.round((coldMigrationCount.value / totalCount.value) * 100);
});

// 监听故障数据变化，计算冷迁移比例
watch(
  () => props.faultData,
  (newData) => {
    if (!newData || !Array.isArray(newData.details)) {
      coldMigrationCount.value = 0;
      totalCount.value = 0;
      averageImpactTime.value = null;
      return;
    }
    // 使用API提供的total字段
    totalCount.value = newData.total || newData.details.length;

    // 冷迁移数据
    const coldMigrationItems = newData.details.filter(
      (item) => item.repair_method === 'cold_migration',
    );

    coldMigrationCount.value = coldMigrationItems.length;

    // 计算冷迁移的平均业务影响时长
    if (coldMigrationItems.length > 0) {
      const totalImpactTime = coldMigrationItems.reduce(
        (sum, item) => sum + (Number(item.business_impact_time) || 0),
        0,
      );
      averageImpactTime.value = totalImpactTime / coldMigrationItems.length;
    } else {
      averageImpactTime.value = null;
    }
  },
  { immediate: true, deep: true },
);
</script>

<template>
  <Card title="冷迁移覆盖率" :bordered="false" class="ratio-card">
    <Spin :spinning="loading">
      <div class="ratio-content">
        <div class="ratio-chart">
          <Progress
            type="circle"
            :percent="migrationRatio"
            :format="(percent) => `${percent}%`"
            :width="120"
            :stroke-color="{
              '0%': '#1890ff',
              '50%': '#52c41a',
              '100%': '#13c2c2',
            }"
            :stroke-width="10"
          />
        </div>
        <div class="ratio-stats">
          <div class="stats-item">
            <div class="item-label">冷迁移单</div>
            <div class="item-value">{{ coldMigrationCount }} 次</div>
            <div class="item-desc">通过冷迁移处理的故障占比</div>
          </div>
          <div class="stats-item">
            <div class="item-label">总故障单数</div>
            <div class="item-value">{{ totalCount }} 次</div>
            <div class="item-progress">
              <Progress
                :percent="100"
                :stroke-color="strokeColor"
                :show-info="false"
                size="small"
              />
              <div class="progress-labels">
                <span>冷迁移: {{ migrationRatio }}%</span>
                <span>其他方式: {{ 100 - migrationRatio }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="impact-info" v-if="averageImpactTime !== null">
        <svg
          class="info-icon"
          viewBox="0 0 24 24"
          width="1em"
          height="1em"
          fill="currentColor"
        >
          <path
            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"
          />
        </svg>
        <span
          >冷迁移故障平均业务影响时长:
          <strong>{{ averageImpactTime.toFixed(2) }}</strong> 分钟</span
        >
      </div>
      <div class="empty-data" v-if="!totalCount">
        <svg
          class="empty-icon"
          viewBox="0 0 24 24"
          width="1em"
          height="1em"
          fill="currentColor"
        >
          <path
            d="M20 6h-8l-2-2H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm0 12H4V8h16v10z"
          />
        </svg>
        <span>暂无冷迁移数据</span>
      </div>
    </Spin>
  </Card>
</template>

<style lang="less" scoped>
.ratio-card {
  height: 100%;
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  overflow: hidden; /* 防止内容溢出 */

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.09);
    transform: translateY(-2px);
  }

  :deep(.ant-card-head) {
    padding: 0 16px;
    min-height: 48px;
    border-bottom: 1px solid #f0f0f0;

    .ant-card-head-title {
      padding: 12px 0;
      font-size: 16px;
      font-weight: 500;
    }
  }

  :deep(.ant-card-body) {
    padding: 16px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: calc(100% - 48px);
    width: 100%;
    min-height: 300px;
    overflow-y: auto; /* 内容过多时可滚动 */
  }

  .ratio-content {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px 0;
    flex-wrap: wrap;
    gap: 24px;
    width: 100%;
  }

  .ratio-chart {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 4px;
    min-width: 120px;

    :deep(.ant-progress-text) {
      font-size: 22px;
      font-weight: 700;
      color: #1890ff;
    }
  }

  .ratio-stats {
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex: 0 0 auto;
    min-width: 200px;
    width: 100%;
    max-width: 300px;
  }

  .stats-item {
    display: flex;
    flex-direction: column;
    width: 100%;

    .item-label {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      margin-bottom: 4px;
    }

    .item-value {
      font-size: 20px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.85);
      margin-bottom: 4px;
    }

    .item-desc {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.45);
    }

    .item-progress {
      margin-top: 8px;
      width: 100%;

      .progress-labels {
        display: flex;
        justify-content: space-between;
        margin-top: 4px;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }

  .impact-info {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 16px;
    padding: 8px;
    background-color: rgba(24, 144, 255, 0.05);
    border-radius: 4px;
    font-size: 13px;
    color: rgba(0, 0, 0, 0.65);
    width: 100%;

    .info-icon {
      color: #1890ff;
      margin-right: 6px;
    }

    strong {
      color: #1890ff;
      margin: 0 2px;
    }
  }

  .empty-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 16px 0;
    padding: 24px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
    text-align: center;
    width: 100%;

    .empty-icon {
      font-size: 32px;
      margin-bottom: 12px;
      color: rgba(0, 0, 0, 0.25);
    }
  }
}
</style>
