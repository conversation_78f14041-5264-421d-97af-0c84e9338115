<script lang="ts" setup>
import { ref } from 'vue';

const columns = [
  {
    title: '资产编号',
    dataIndex: 'id',
    key: 'id',
  },
  {
    title: '资产名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '资产类型',
    dataIndex: 'type',
    key: 'type',
  },
  {
    title: '变动类型',
    dataIndex: 'changeType',
    key: 'changeType',
  },
  {
    title: '变动时间',
    dataIndex: 'changeTime',
    key: 'changeTime',
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    key: 'operator',
  },
];

const dataSource = ref([
  {
    key: '1',
    id: 'AS-2023-001',
    name: 'Dell R740服务器',
    type: '服务器',
    changeType: '新增',
    changeTime: '2023-05-15',
    operator: '张三',
  },
  {
    key: '2',
    id: 'AS-2023-002',
    name: 'Cisco交换机',
    type: '网络设备',
    changeType: '新增',
    changeTime: '2023-05-12',
    operator: '李四',
  },
  {
    key: '3',
    id: 'AS-2022-156',
    name: 'HP存储设备',
    type: '存储设备',
    changeType: '维修',
    changeTime: '2023-05-10',
    operator: '王五',
  },
  {
    key: '4',
    id: 'AS-2022-089',
    name: '华为服务器',
    type: '服务器',
    changeType: '报废',
    changeTime: '2023-05-08',
    operator: '赵六',
  },
  {
    key: '5',
    id: 'AS-2023-003',
    name: '联想工作站',
    type: '工作站',
    changeType: '新增',
    changeTime: '2023-05-05',
    operator: '张三',
  },
]);
</script>

<template>
  <div class="asset-dashboard-container">
    <a-card title="资产看板" :bordered="false">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-statistic title="服务器总数" value="1,256" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="网络设备" value="328" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="存储设备" value="156" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="资产总值" value="¥85,600,000" />
        </a-col>
      </a-row>

      <a-divider />

      <h3>资产分布</h3>
      <div class="chart-placeholder">
        <div class="pie-chart-mock">
          <div
            class="pie-segment"
            style="background-color: #1890ff; transform: rotate(0deg)"
          ></div>
          <div
            class="pie-segment"
            style="background-color: #52c41a; transform: rotate(120deg)"
          ></div>
          <div
            class="pie-segment"
            style="background-color: #faad14; transform: rotate(240deg)"
          ></div>
          <div class="pie-center"></div>
        </div>
        <div class="chart-label">
          图表区域 - 实际项目中应使用Echarts等图表库
        </div>
      </div>

      <a-divider />

      <h3>资产状态</h3>
      <a-row :gutter="16">
        <a-col :span="8">
          <a-card class="mini-card">
            <div class="status-card">
              <div class="status-icon" style="background-color: #52c41a"></div>
              <div class="status-info">
                <div class="status-title">正常运行</div>
                <div class="status-value">1,568台</div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card class="mini-card">
            <div class="status-card">
              <div class="status-icon" style="background-color: #faad14"></div>
              <div class="status-info">
                <div class="status-title">需要维护</div>
                <div class="status-value">128台</div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card class="mini-card">
            <div class="status-card">
              <div class="status-icon" style="background-color: #f5222d"></div>
              <div class="status-info">
                <div class="status-title">故障设备</div>
                <div class="status-value">44台</div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <a-divider />

      <h3>最近资产变动</h3>
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :pagination="{ pageSize: 5 }"
        :loading="false"
      />
    </a-card>
  </div>
</template>

<style lang="less" scoped>
.asset-dashboard-container {
  padding: 24px;

  .ant-card {
    margin-bottom: 24px;
  }

  .ant-statistic {
    margin-bottom: 16px;
  }

  h3 {
    margin: 16px 0;
  }

  .chart-placeholder {
    height: 200px;
    background-color: #f5f5f5;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;

    .pie-chart-mock {
      position: relative;
      width: 120px;
      height: 120px;
      border-radius: 50%;
      overflow: hidden;

      .pie-segment {
        position: absolute;
        width: 100%;
        height: 100%;
        clip-path: polygon(
          50% 50%,
          50% 0%,
          100% 0%,
          100% 100%,
          0% 100%,
          0% 0%,
          50% 0%
        );
      }

      .pie-center {
        position: absolute;
        width: 40px;
        height: 40px;
        background-color: white;
        border-radius: 50%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }

    .chart-label {
      margin-top: 16px;
      color: #999;
    }
  }

  .mini-card {
    margin-bottom: 16px;
  }

  .status-card {
    display: flex;
    align-items: center;

    .status-icon {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      margin-right: 12px;
    }

    .status-info {
      flex: 1;

      .status-title {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
      }

      .status-value {
        font-size: 20px;
        font-weight: 500;
      }
    }
  }
}
</style>
