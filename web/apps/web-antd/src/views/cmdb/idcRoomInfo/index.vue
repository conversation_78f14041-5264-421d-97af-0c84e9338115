<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { Room } from '#/api/core/cmdb/location/room';

import { ref } from 'vue';

import { Tag } from 'ant-design-vue';

import { getDataCenterTableApi } from '#/api/core/cmdb/location/dataCenter';
import {
  addRoomApi,
  deleteRoomApi,
  editRoomApi,
  getRoomTableApi,
} from '#/api/core/cmdb/location/room';
import CommonDetail from '#/components/CommonDetail/index.vue';
import CommonList from '#/components/CommonList/index.vue';

const detailRef = ref();
const detailData = ref<null | Room>(null);
const commonListRef = ref();
const dataCenterOptions = ref<{ label: string; value: number }[]>([]);

// 状态映射
const statusMap: {
  [key: string]: { color: string; text: string };
} = {
  active: { text: '启用', color: 'success' },
  disabled: { text: '禁用', color: 'error' },
};

// 默认数据
const defaultData = {
  name: '',
  dataCenterID: undefined,
  floor: '',
  area: undefined,
  status: 'active',
  description: '',
};

// 获取机房选项
async function fetchDataCenterOptions() {
  try {
    const res = await getDataCenterTableApi({ page: 1, pageSize: 1000 });
    dataCenterOptions.value = res.list.map((item) => ({
      label: `${item.name}${item.az ? ` (${item.az.name})` : ''}`,
      value: item.id,
    }));
  } catch (error) {
    console.error('获取机房选项失败:', error);
  }
}

// 初始化时获取机房选项
fetchDataCenterOptions();

// 表格搜索表单
const formOptions: VbenFormProps = {
  collapsed: true,
  collapsedRows: 1,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入房间名称' },
      fieldName: 'query',
      label: '房间名称',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择机房',
        options: dataCenterOptions,
        allowClear: true,
      },
      fieldName: 'dataCenterId',
      label: '所属机房',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: true,
};

// 表格配置
const gridOptions: VxeGridProps<Room> = {
  checkboxConfig: {
    highlight: true,
  },
  exportConfig: {}, // 导出配置
  importConfig: {
    types: ['csv'],
  }, // 导入配置
  columns: [
    { align: 'center', type: 'checkbox', width: 40 },
    { field: 'id', title: 'ID', width: 80 },
    { field: 'name', title: '房间名称' },
    {
      field: 'dataCenter.name',
      title: '所属机房',
    },
    {
      field: 'dataCenter.az.name',
      title: '所属可用区',
    },
    {
      field: 'dataCenter.az.region.name',
      title: '所属区域',
    },
    {
      field: 'status',
      title: '状态',
      slots: { default: 'status' }, // 添加自定义插槽
    },
    { field: 'description', title: '描述信息' },
    {
      title: '操作',
      slots: { default: 'operate' },
      width: 200,
    },
  ],
  keepSource: true,
  height: 'auto',
  rowConfig: { isHover: true },
  pagerConfig: { pageSize: 10, pageSizes: [10, 20, 50, 100] },
  toolbarConfig: {
    custom: true,
    refresh: true,
    export: true,
    zoom: true,
    import: true,
    slots: {
      buttons: 'toolbar-buttons',
    },
  },
};

// 新增/编辑表单配置
const editFormOptions: VbenFormProps = {
  showDefaultActions: false,
  schema: [
    {
      fieldName: 'name',
      label: '房间名称',
      component: 'Input',
      componentProps: { placeholder: '请输入房间名称' },
      rules: 'required',
    },
    {
      fieldName: 'dataCenterID',
      label: '所属机房',
      component: 'Select',
      componentProps: {
        placeholder: '请选择所属机房',
        options: dataCenterOptions,
        allowClear: true,
        style: { width: '200px' },
      },
      rules: 'required',
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: [
          { label: '启用', value: 'active' },
          { label: '禁用', value: 'disabled' },
        ],
      },
      rules: 'required',
    },
    {
      fieldName: 'description',
      label: '描述信息',
      component: 'Input',
      componentProps: {
        placeholder: '请输入描述信息',
        rows: 4,
        type: 'textarea',
      },
    },
  ],
};

// 详情字段配置
const detailFields = [
  { label: 'ID', field: 'id' },
  { label: '房间名称', field: 'name' },
  {
    label: '所属机房',
    field: 'dataCenter.name',
    format: (value: any) => {
      if (value) return value;
      const data = detailData.value;
      if (data?.dataCenterID) {
        const dataCenter = dataCenterOptions.value.find(
          (opt) => opt.value === data.dataCenterID,
        );
        if (dataCenter?.label) {
          const parts = dataCenter.label.split(' (');
          return parts[0] || '暂无';
        }
      }
      return '暂无';
    },
  },
  {
    label: '所属可用区',
    field: 'dataCenter.az.name',
    format: (value: any) => {
      if (value) return value;

      // 从详情数据中获取
      const data = detailData.value;

      // 尝试从dataCenter对象中获取
      if (data?.dataCenter?.az?.name) {
        return data.dataCenter.az.name;
      }

      // 尝试从选项中获取
      if (data?.dataCenterID) {
        const dataCenter = dataCenterOptions.value.find(
          (opt) => opt.value === data.dataCenterID,
        );
        if (dataCenter?.label && dataCenter.label.includes('(')) {
          const match = dataCenter.label.match(/\((.*?)\)/);
          if (match && match[1]) {
            return match[1];
          }
        }
      }

      return '暂无';
    },
  },
  {
    label: '所属区域',
    field: 'dataCenter.az.region.name',
    format: (value: any) => {
      if (value) return value;

      // 从详情数据中获取
      const data = detailData.value;

      // 尝试从dataCenter对象中获取
      if (data?.dataCenter?.az?.region?.name) {
        return data.dataCenter.az.region.name;
      }

      // 如果有可用区信息但没有区域信息，可以尝试从其他地方获取
      // 这里需要根据实际数据结构进行调整

      return '暂无';
    },
  },
  { label: '状态', field: 'status', type: 'status' as const },
  { label: '描述信息', field: 'description' },
  { label: '创建时间', field: 'created_at' },
  { label: '更新时间', field: 'updated_at' },
];

// 处理详情事件
function handleDetail(row: Room) {
  detailData.value = row;
  detailRef.value?.drawerApi.open();
}

// 自定义权限配置
const permissions = {
  add: ['Cmdb:IdcRoomInfo:Create'],
  edit: ['Cmdb:IdcRoomInfo:Edit'],
  delete: ['Cmdb:IdcRoomInfo:Delete'],
  import: ['Cmdb:IdcRoomInfo:Import'],
};
</script>

<template>
  <div>
    <!-- 使用通用列表组件 -->
    <CommonList
      ref="commonListRef"
      :form-options="formOptions"
      :grid-options="gridOptions"
      :edit-form-options="editFormOptions"
      :api="{
        getList: getRoomTableApi,
        add: addRoomApi,
        edit: editRoomApi,
        delete: deleteRoomApi,
      }"
      :default-data="defaultData"
      :status-map="statusMap"
      @detail="handleDetail"
      :permissions="permissions"
      show-add-button
      show-edit-button
      show-delete-button
      show-detail-button
      import-model-type="room"
      :enable-confirm="true"
      :show-template-button="true"
    >
      <!-- 自定义状态插槽 -->
      <template #status="{ row }">
        <Tag :color="statusMap[row.status]?.color || 'default'">
          {{ statusMap[row.status]?.text || row.status || '未知状态' }}
        </Tag>
      </template>
    </CommonList>

    <!-- 使用通用详情组件 -->
    <CommonDetail
      ref="detailRef"
      :data="detailData"
      title="房间详情"
      :fields="detailFields"
      :status-map="statusMap"
      show-audit-log
      table-name="rooms"
    />
  </div>
</template>
