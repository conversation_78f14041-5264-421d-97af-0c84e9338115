<script lang="ts" setup>
import type { Region } from '#/api/core/cmdb/location/region';

import { ref, watch } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { Descriptions, Empty, Tabs, Tag } from 'ant-design-vue';

import AuditLog from '#/components/AuditLog/index.vue';

// 定义属性
const props = defineProps<{
  data: null | Region;
}>();

const activeTab = ref('basic');
const auditLogRef = ref();

// 状态映射
const statusMap: {
  [key: string]: { color: string; text: string };
} = {
  active: { text: '启用', color: 'success' },
  disabled: { text: '禁用', color: 'error' },
};

// 获取状态显示信息
function getStatusInfo(status: string) {
  return statusMap[status] || { text: status, color: 'default' };
}

// 创建抽屉
const [Drawer, drawerApi] = useVbenDrawer({
  title: '区域详情',
});

// 监听 data 变化，确保数据存在时才渲染内容
watch(
  () => props.data,
  (newData) => {
    if (newData) {
      // 确保数据存在时再打开抽屉
      activeTab.value = 'basic';
    }
  },
  { immediate: true },
);

// 暴露抽屉 API 给父组件
defineExpose({
  drawerApi,
});
</script>

<template>
  <Drawer>
    <template v-if="data">
      <Tabs v-model:active-key="activeTab">
        <Tabs.TabPane key="basic" tab="基本信息">
          <!-- 使用 Ant Design 的 Descriptions 组件美化基本信息 -->
          <Descriptions bordered :column="1" size="middle" class="mt-4">
            <Descriptions.Item label="ID">
              {{ data.id }}
            </Descriptions.Item>
            <Descriptions.Item label="区域名称">
              {{ data.name }}
            </Descriptions.Item>
            <Descriptions.Item label="状态">
              <Tag :color="getStatusInfo(data.status).color">
                {{ getStatusInfo(data.status).text }}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="描述信息">
              {{ data.description || '暂无描述' }}
            </Descriptions.Item>
          </Descriptions>
        </Tabs.TabPane>
        <Tabs.TabPane key="changelog" tab="变更记录">
          <AuditLog
            ref="auditLogRef"
            table-name="regions"
            :entity-id="data.id"
          />
        </Tabs.TabPane>
      </Tabs>
    </template>
    <template v-else>
      <Empty description="暂无数据" />
    </template>
  </Drawer>
</template>
