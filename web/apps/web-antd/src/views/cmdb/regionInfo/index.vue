<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { Region } from '#/api/core/cmdb/location/region';

import { ref } from 'vue';

import { AccessControl } from '@vben/access';
import { Page, useVbenDrawer } from '@vben/common-ui';

import { Button, message, Tag } from 'ant-design-vue';
import XEUtils from 'xe-utils';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addRegionApi,
  deleteRegionApi,
  editRegionApi,
  getRegionTableApi,
} from '#/api/core/cmdb/location/region';
import {
  downloadImportTemplate as downloadTemplate,
  importDataApi,
} from '#/api/core/import';

import RegionDetail from './detail.vue';
import { defaultData } from './type';

const selectRow = ref<null | Region>(null);
const detailRef = ref();
const detailData = ref<null | Region>(null);

// 详情抽屉

// 表格搜索表单
const formOptions: VbenFormProps = {
  collapsed: true,
  collapsedRows: 1,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入区域名称' },
      fieldName: 'name',
      label: '区域名称',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: true,
};

// 表格配置
const gridOptions: VxeGridProps<Region> = {
  checkboxConfig: {
    highlight: true,
  },
  exportConfig: { modes: ['current', 'selected', 'all'], type: 'csv' }, // 导出配置
  importConfig: {
    types: ['csv'],
    remote: true,
    importMethod: handleImportRegions,
    modes: ['covering', 'insertBottom', 'insertTop'],
  }, // 导入配置
  columns: [
    { align: 'center', type: 'checkbox', width: 40 },
    { field: 'id', title: 'ID', width: 80 },
    { field: 'name', title: '区域名称' },
    {
      field: 'status',
      title: '状态',
      slots: { default: 'status' }, // 添加自定义插槽
    },
    { field: 'description', title: '描述信息' },
    {
      title: '操作',
      slots: { default: 'operate' },
      width: 200,
    },
  ],
  keepSource: true,
  height: 'auto',
  rowConfig: { isHover: true },
  pagerConfig: { pageSize: 10, pageSizes: [10, 20, 50, 100] },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const res = await getRegionTableApi({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
        return {
          items: res.list,
          total: res.total,
        };
      },
      queryAll: async (params) => {
        // 全量查询不使用分页参数
        const res = await getRegionTableApi({
          page: 1,
          pageSize: 99_999, // 设置一个足够大的数值来获取全部数据
          ...params.options,
        });
        return {
          items: res.list,
          total: res.total,
        };
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    export: true,
    zoom: true,
    import: true,
    slots: {
      buttons: 'toolbar-buttons',
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });

// 新增/编辑表单配置
const editFormOptions: VbenFormProps = {
  showDefaultActions: false,
  schema: [
    {
      fieldName: 'name',
      label: '区域名称',
      component: 'Input',
      componentProps: { placeholder: '请输入区域名称' },
      rules: 'required',
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: [
          { label: '启用', value: 'active' },
          { label: '禁用', value: 'disabled' },
        ],
      },
      rules: 'required',
    },
    {
      fieldName: 'description',
      label: '描述信息',
      component: 'Input',
      componentProps: {
        placeholder: '请输入描述信息',
        rows: 4,
      },
    },
  ],
};
const [Form, formApi] = useVbenForm(editFormOptions);

// 状态映射
const statusMap: {
  [key: string]: { color: string; text: string };
} = {
  active: { text: '启用', color: 'success' },
  disabled: { text: '禁用', color: 'error' },
};

// 获取状态显示信息
function getStatusInfo(status: string) {
  return statusMap[status] || { text: status, color: 'default' };
}

// 新增/编辑抽屉
const [Drawer, drawerApi] = useVbenDrawer({
  onConfirm: () => {
    formApi.validate().then(async (valid) => {
      if (valid) {
        await submitEvent();
      }
    });
  },
});

// 新增事件
function addEvent() {
  selectRow.value = null;
  formApi.setValues(XEUtils.clone(defaultData, true));
  drawerApi.open();
}

// 编辑事件
function editEvent(row: Region) {
  selectRow.value = row;
  const newData = Object.assign(XEUtils.clone(defaultData, true), row);
  formApi.setValues(newData);
  drawerApi.open();
}

// 批量删除函数
async function batchDeleteEvent() {
  const selectedRows = gridApi.grid.getCheckboxRecords();
  if (selectedRows.length === 0) {
    message.warning('请选择要删除的记录');
    return;
  }
  try {
    // 可以选择一次删除一条或批量删除
    for (const row of selectedRows) {
      await deleteRegionApi(row.id);
    }
    message.success(`成功删除 ${selectedRows.length} 条记录`);
    gridApi.reload();
  } catch {
    message.error('删除失败');
  }
}

// 新增/编辑表单提交事件
async function submitEvent() {
  // 使用 getValues 获取完整表单数据，并作类型断言
  const values = await formApi.getValues();
  // 编辑时补充 ID 字段
  if (selectRow.value) {
    values.id = selectRow.value.id;
  }

  try {
    await (selectRow.value
      ? editRegionApi(values as Region)
      : addRegionApi(
          values as Omit<Region, 'created_at' | 'id' | 'updated_at'>,
        ));

    // 保存成功后关闭抽屉，并刷新表格数据
    drawerApi.close();
    message.success('保存成功');
    gridApi.reload();
  } catch {
    message.error('保存失败');
  }
}

function detailEvent(row: Region) {
  detailData.value = row;
  detailRef.value?.drawerApi.open();
}

// 处理区域数据导入 - 修复参数类型和错误处理
async function handleImportRegions({ file }: { file: File }) {
  try {
    message.loading({ content: '正在导入数据...', key: 'import' });

    // 调用导入API
    const result = await importDataApi(file, 'region');

    // 显示导入结果
    if (result.failCount === 0) {
      message.success({
        content: `成功导入${result.successCount}条数据`,
        key: 'import',
      });
    } else {
      message.warning({
        content: `成功导入${result.successCount}条数据，失败${result.failCount}条`,
        key: 'import',
      });
    }

    // 刷新表格数据
    gridApi.reload();

    return { status: true };
  } catch (error: any) {
    message.error({
      content: `导入失败: ${error.message || '未知错误'}`,
      key: 'import',
    });
    return { status: false };
  }
}

// 修改下载导入模板函数
async function downloadImportTemplate() {
  try {
    message.loading({ content: '正在下载模板...', key: 'downloadTemplate' });
    await downloadTemplate('region');
    message.success({ content: '模板下载成功', key: 'downloadTemplate' });
  } catch (error: any) {
    message.error({
      content: error.message || '下载模板失败',
      key: 'downloadTemplate',
    });
  }
}
</script>

<template>
  <Page auto-content-height>
    <!-- 表格区域 -->
    <Grid>
      <!-- 添加状态插槽 -->
      <template #status="{ row }">
        <Tag :color="getStatusInfo(row.status).color">
          {{ getStatusInfo(row.status).text }}
        </Tag>
      </template>
      <template #operate="{ row }">
        <AccessControl :codes="['Cmdb:RegionInfo:Edit']" type="code">
          <Button type="link" @click="editEvent(row)">编辑</Button>
        </AccessControl>
        <Button type="link" @click="detailEvent(row)">详情</Button>
      </template>
      <template #toolbar-buttons>
        <AccessControl :codes="['Cmdb:RegionInfo:Create']" type="code">
          <Button class="mr-2" type="primary" @click="addEvent">新增</Button>
        </AccessControl>
        <AccessControl :codes="['Cmdb:RegionInfo:Delete']" type="code">
          <Button class="mr-2" danger @click="batchDeleteEvent">删除</Button>
        </AccessControl>
      </template>
      <template #toolbar-tools>
        <AccessControl :codes="['Cmdb:RegionInfo:Import']" type="code">
          <Button class="mr-2" type="primary" @click="downloadImportTemplate">
            下载导入模板
          </Button>
        </AccessControl>
      </template>
    </Grid>

    <!-- 新增/编辑抽屉 -->
    <Drawer :title="selectRow ? '编辑区域' : '新增区域'">
      <Form />
    </Drawer>

    <!-- 详情抽屉 -->
    <RegionDetail class="w-1/2" ref="detailRef" :data="detailData" />
  </Page>
</template>
