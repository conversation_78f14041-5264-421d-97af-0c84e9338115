import {
  addDeviceResourceApi,
  deleteDeviceResourceApi,
  editDeviceResourceApi,
  getClustersApi,
  getDeviceResourceTableApi,
  getProjectsApi,
} from '#/api/core/cmdb/asset/device';
import { getCabinetTableApi } from '#/api/core/cmdb/location/cabinet';
import { getRoomTableApi } from '#/api/core/cmdb/location/room';
import { getTemplateListApi } from '#/api/core/cmdb/template/template';

// 资产状态映射
export const assetStatusMap: {
  [key: string]: { color: string; text: string };
} = {
  pending_in: { text: '待入库', color: 'purple' },
  in_stock: { text: '已入库', color: 'blue' },
  pending_out: { text: '待出库', color: 'orange' },
  out_stock: { text: '已出库', color: 'cyan' },
  idle: { text: '闲置中', color: 'lime' },
  in_use: { text: '使用中', color: 'green' },
  repairing: { text: '维修中', color: 'gold' },
  verify: { text: '已验收', color: 'blue' },
  on_rack: { text: '已上架', color: 'blue' },
  pending_scrap: { text: '待报废', color: 'volcano' },
  scrapped: { text: '已报废', color: 'red' },
  sold_out: { text: '已售卖', color: 'red' },
};

// 业务状态映射
export const bizStatusMap: {
  [key: string]: { color: string; text: string };
} = {
  active: { text: 'ACTIVE', color: 'green' },
  maintaining: { text: 'MAINTAINING', color: 'blue' },
  outage: { text: 'OUTAGE', color: 'red' },
};

// 硬件状态映射
export const hardwareStatusMap: {
  [key: string]: { color: string; text: string };
} = {
  normal: { text: '正常', color: 'green' },
  warning: { text: '警告', color: 'orange' },
  faulty: { text: '故障', color: 'red' },
};

// 资产类型选项
export const assetTypeOptions = [
  { label: '服务器', value: 'server' },
  { label: 'GPU服务器', value: 'gpu_server' },
  { label: '存储设备', value: 'storage' },
  { label: '其他', value: 'other' },
];

// 默认数据
export const defaultData = {
  purchaseOrder: '',
  sn: '',
  brand: '',
  model: '',
  purchaseDate: '',
  warrantyExpire: '',
  assetStatus: 'pending_storage',
  hardwareStatus: 'normal',
  price: 0,
  residualValue: 0,
  assetType: 'server',
  remark: '',
  templateID: undefined,
  resource: {
    sn: '',
    bizStatus: 'active',
    project: '',
    bmcIP: '',
    vpcIP: '',
    cabinetID: undefined,
    rackPosition: undefined,
    hostname: '',
    cluster: '',
  },
};

// 修改深拷贝方法
export function adaptDeviceData(data: any, templateOptions: any[]): any {
  if (!data) return data;

  // 创建深拷贝以避免修改原始数据
  const result = structuredClone(data);

  // 确保template字段正确显示
  if (result.templateID && !result.template) {
    result.template = {
      id: result.templateID,
      templateName:
        templateOptions.find((opt) => opt.value === result.templateID)?.label ||
        '未知模板',
    };
  }

  return result;
}

// 添加类型定义
interface DevicePlan {
  id: number;
  name: string;
  type: string;
  resourceConfig: string;
  created_at: string;
}

interface TemplatePart {
  id: number;
  name: string;
  model: string;
  serialNumber: string;
  status: string;
  description: string;
  component_type: string;
  quantity: number;
  slot: string;
}

// 处理套餐数据的公共函数
export function processPlanData(data: any): {
  devicePlans: DevicePlan[];
  templateParts: TemplatePart[];
} {
  try {
    const devicePlans: DevicePlan[] = [];
    let templateParts: TemplatePart[] = [];

    if (!data) {
      return { devicePlans, templateParts };
    }

    if (Array.isArray(data)) {
      devicePlans.push(
        ...data.map((item: any) => ({
          id: item.id,
          name: item.template_name || item.templateName || '-',
          type: item.template_category || item.templateCategory || '标准',
          resourceConfig: `CPU: ${item.cpu_model || item.cpuModel || '-'}, 内存: ${item.memory_capacity || item.memoryCapacity || '-'}GB`,
          created_at: item.created_at,
        })),
      );

      // 获取模板部件信息
      if (data[0] && data[0].components) {
        templateParts = data[0].components.map((comp: any) => ({
          id: comp.id,
          name: comp.product?.name || '未知组件',
          model: comp.product?.model || '-',
          serialNumber: comp.product?.sn || comp.sn || '-',
          status: 'template', // 标记为模板部件
          description: `${comp.slot ? `安装位置: ${comp.slot}` : ''} ${comp.quantity ? `数量: ${comp.quantity}` : ''}`,
          component_type:
            comp.product?.type || comp.product?.component_type || '未知类型',
          quantity: comp.quantity || 1,
          slot: comp.slot || '-',
        }));
      }
    } else if (data && data.list && Array.isArray(data.list)) {
      // 处理其他数据格式
      devicePlans.push(
        ...data.list.map((item: any) => ({
          id: item.id,
          name: item.template_name || item.templateName || '-',
          type: item.template_category || item.templateCategory || '标准',
          resourceConfig: `CPU: ${item.cpu_model || item.cpuModel || '-'}, 内存: ${item.memory_capacity || item.memoryCapacity || '-'}GB`,
          created_at: item.created_at,
        })),
      );

      // 获取模板部件信息
      if (data.list[0] && data.list[0].components) {
        templateParts = data.list[0].components.map((comp: any) => ({
          id: comp.id,
          name: comp.product?.name || '未知组件',
          model: comp.product?.model || '-',
          serialNumber: comp.product?.sn || comp.sn || '-',
          status: 'template', // 标记为模板部件
          description: `${comp.slot ? `安装位置: ${comp.slot}` : ''} ${comp.quantity ? `数量: ${comp.quantity}` : ''}`,
          component_type:
            comp.product?.type || comp.product?.component_type || '未知类型',
          quantity: comp.quantity || 1,
          slot: comp.slot || '-',
        }));
      }
    } else if (data && typeof data === 'object') {
      // 处理其他数据格式
      devicePlans.push({
        id: data.id,
        name: data.template_name || data.templateName || '-',
        type: data.template_category || data.templateCategory || '标准',
        resourceConfig: `CPU: ${data.cpu_model || data.cpuModel || '-'}, 内存: ${data.memory_capacity || data.memoryCapacity || '-'}GB`,
        created_at: data.created_at,
      });

      // 获取模板部件信息
      if (data.components && Array.isArray(data.components)) {
        templateParts = data.components.map((comp: any) => ({
          id: comp.id,
          name: comp.product?.name || '未知组件',
          model: comp.product?.model || '-',
          serialNumber: comp.product?.sn || comp.sn || '-',
          status: 'template', // 标记为模板部件
          description: `${comp.slot ? `安装位置: ${comp.slot}` : ''} ${comp.quantity ? `数量: ${comp.quantity}` : ''}`,
          component_type:
            comp.product?.type || comp.product?.component_type || '未知类型',
          quantity: comp.quantity || 1,
          slot: comp.slot || '-',
        }));
      }
    }

    return { devicePlans, templateParts };
  } catch (error) {
    console.warn('处理套餐数据时出错:', error);
    return { devicePlans: [], templateParts: [] };
  }
}

// 获取机柜选项
export async function fetchCabinetOptions() {
  try {
    const res = await getCabinetTableApi({ page: 1, pageSize: 1000 });
    const allCabinets = res.list.map((item) => ({
      label: `${item.name}${item.room ? ` (${item.room.name})` : ''}`,
      value: item.id,
      roomId: item.roomID || 0,
    }));

    const cabinetOptions = allCabinets.map(({ label, value }) => ({
      label,
      value,
    }));

    return { allCabinets, cabinetOptions };
  } catch (error) {
    console.error('获取机柜选项失败:', error);
    return { allCabinets: [], cabinetOptions: [] };
  }
}

// 获取房间选项
export async function fetchRoomOptions() {
  try {
    const res = await getRoomTableApi({ page: 1, pageSize: 1000 });
    return res.list.map((item) => ({
      label: `${item.name}${item.dataCenter ? ` (${item.dataCenter.name})` : ''}`,
      value: item.id,
    }));
  } catch (error) {
    console.error('获取房间选项失败:', error);
    return [];
  }
}

// 获取套餐模板选项
export async function fetchTemplateOptions() {
  try {
    const res = await getTemplateListApi({ page: 1, pageSize: 1000 });
    return res.list.map((item) => ({
      label: item.templateName,
      value: item.id || 0,
    }));
  } catch (error) {
    console.error('获取套餐模板选项失败:', error);
    return [];
  }
}

// 获取项目选项
export async function fetchProjectOptions() {
  try {
    const res = await getProjectsApi();
    return (res || []).map((item: string) => ({
      label: item,
      value: item,
    }));
  } catch (error) {
    console.error('获取项目选项失败:', error);
    return [];
  }
}

// 获取集群选项
export async function fetchClusterOptions() {
  try {
    const res = await getClustersApi();
    return (res || []).map((item: string) => ({
      label: item,
      value: item,
    }));
  } catch (error) {
    console.error('获取集群选项失败:', error);
    return [];
  }
}

// 创建包装的API函数
export function createWrappedApi(getTemplateOptions: () => any[]) {
  return {
    getList: async (params: any) => {
      try {
        // 创建新的请求参数对象，避免直接修改params
        const requestParams = { ...params };

        // 处理isBackup字段，将字符串转换为布尔值
        switch (requestParams.isBackup) {
          case '':
          case undefined: {
            // 如果为空或undefined，则删除该字段
            delete requestParams.isBackup;
            break;
          }
          case 'false': {
            requestParams.isBackup = false;
            break;
          }
          case 'true': {
            requestParams.isBackup = true;
            break;
          }
        }

        // 检查参数
        if (requestParams.hostname !== undefined) {
          // 如果hostname有值且query为空，将hostname的值赋给query
          if (
            requestParams.hostname &&
            (!requestParams.query || requestParams.query === '')
          ) {
            requestParams.query = requestParams.hostname;
          }
          // 始终删除hostname参数，因为后端API不接收这个参数
          delete requestParams.hostname;
        }

        // 调用API函数
        const response = await getDeviceResourceTableApi(requestParams);

        // 确保返回数据格式正确
        if (response && typeof response === 'object') {
          // 确保list是数组
          const list = Array.isArray(response.list) ? response.list : [];
          // 获取最新的templateOptions
          const currentTemplateOptions = getTemplateOptions();
          // 处理list中的每个item
          const processedList = list.map((item) =>
            adaptDeviceData(item, currentTemplateOptions),
          );

          return {
            list: processedList,
            total: response.total || 0,
          };
        }

        // 如果response格式异常，返回空结果
        return { list: [], total: 0 };
      } catch (error) {
        console.error('获取设备列表失败:', error);
        return { list: [], total: 0 };
      }
    },
    add: async (data: any) => {
      // 确保传递正确的templateID
      const formData = { ...data };
      if (formData.templateID === null || formData.templateID === '') {
        formData.templateID = undefined;
      }

      return await addDeviceResourceApi(formData);
    },
    edit: async (data: any) => {
      try {
        // 确保传递正确的templateID
        const formData = { ...data };
        if (formData.templateID === null || formData.templateID === '') {
          formData.templateID = undefined;
        }

        // 直接发送一个编辑请求，其他刷新操作在回调中静默处理
        const result = await editDeviceResourceApi(formData);

        return result;
      } catch (error) {
        console.error('编辑设备资源出错:', error);
        throw error; // 重新抛出以便UI显示错误
      }
    },
    delete: deleteDeviceResourceApi,
  };
}
