import type { Ref } from 'vue';

import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { Device } from '#/api/core/cmdb/asset/device';

import {
  assetStatusMap,
  assetTypeOptions,
  bizStatusMap,
  hardwareStatusMap,
} from './server-config';

// 表格搜索表单配置
export function createFormOptions(
  projectOptions: Ref<{ label: string; value: string }[]>,
  clusterOptions: Ref<{ label: string; value: string }[]>,
  templateOptions: Ref<{ label: string; value: number }[]>,
): VbenFormProps {
  return {
    collapsed: true,
    collapsedRows: 3,
    schema: [
      {
        component: 'Input',
        componentProps: { placeholder: '请输入SN/主机名' },
        fieldName: 'query',
        label: '关键词',
      },
      {
        component: 'Input',
        componentProps: { placeholder: '请输入VPC IP地址' },
        fieldName: 'vpcIP',
        label: 'VPC IP',
      },
      {
        component: 'Input',
        componentProps: { placeholder: '请输入BMC IP地址' },
        fieldName: 'bmcIP',
        label: 'BMC IP',
      },
      {
        component: 'Input',
        componentProps: { placeholder: '请输入租户IP地址' },
        fieldName: 'tenantIP',
        label: '租户IP',
      },
      {
        component: 'Select',
        componentProps: {
          placeholder: '是否备机',
          options: [
            { label: '是', value: 'true' },
            { label: '否', value: 'false' },
          ],
          allowClear: true,
        },
        fieldName: 'isBackup',
        label: '是否备机',
      },
      {
        component: 'Select',
        componentProps: {
          placeholder: '请选择项目',
          options: projectOptions,
          allowClear: true,
          showSearch: true,
          filterOption: (input: string, option: any) =>
            option.label.toLowerCase().includes(input.toLowerCase()),
        },
        fieldName: 'project',
        label: '项目',
      },
      {
        component: 'Select',
        componentProps: {
          placeholder: '请选择资产状态',
          options: Object.entries(assetStatusMap).map(([value, item]) => ({
            label: item.text,
            value,
          })),
          allowClear: true,
        },
        fieldName: 'assetStatus',
        label: '资产状态',
      },
      {
        component: 'Select',
        componentProps: {
          placeholder: '请选择业务状态',
          options: Object.entries(bizStatusMap).map(([value, item]) => ({
            label: item.text,
            value,
          })),
          allowClear: true,
        },
        fieldName: 'bizStatus',
        label: '业务状态',
      },
      {
        component: 'Select',
        componentProps: {
          placeholder: '请选择资产类型',
          options: assetTypeOptions,
          allowClear: true,
        },
        fieldName: 'assetType',
        label: '资产类型',
      },
      {
        fieldName: 'resource.hostname',
        label: '主机名',
        component: 'Input',
        componentProps: { placeholder: '请输入主机名' },
      },
      {
        fieldName: 'cluster',
        label: '集群',
        component: 'Select',
        componentProps: {
          placeholder: '请选择集群',
          options: clusterOptions,
          allowClear: true,
          showSearch: true,
          filterOption: (input: string, option: any) =>
            option.label.toLowerCase().includes(input.toLowerCase()),
        },
      },
      {
        fieldName: 'brand',
        label: '厂商',
        component: 'Input',
        componentProps: { placeholder: '请输入厂商' },
      },
      {
        component: 'Select',
        componentProps: {
          placeholder: '请选择套餐模板',
          options: templateOptions,
          allowClear: true,
        },
        fieldName: 'templateID',
        label: '套餐模板',
      },
      {
        component: 'Input',
        componentProps: { placeholder: '请输入型号' },
        fieldName: 'model',
        label: '型号',
      },
    ],
    submitButtonOptions: { content: '查询' },
    showCollapseButton: true,
    submitOnChange: false,
    submitOnEnter: true,
  };
}

// 新增/编辑表单配置
export function createEditFormOptions(
  roomOptions: Ref<{ label: string; value: number }[]>,
  cabinetOptions: Ref<{ label: string; value: number }[]>,
  templateOptions: Ref<{ label: string; value: number }[]>,
  projectOptions: Ref<{ label: string; value: string }[]>,
  clusterOptions: Ref<{ label: string; value: string }[]>,
  loadingRooms: Ref<boolean>,
  loadingCabinets: Ref<boolean>,
  fetchRooms: () => Promise<void>,
  fetchCabinetsByRoomId: (roomId: number) => Promise<void>,
): VbenFormProps {
  return {
    showDefaultActions: false,
    wrapperClass: 'grid grid-cols-2 gap-x-4 gap-y-2',
    schema: [
      // 基本信息标题
      {
        component: 'Divider',
        label: '基本信息',
        fieldName: 'basic_info_title',
        wrapperClass: 'col-span-2 !mt-6 !mb-4',
        formItemClass: 'col-span-2',
        componentProps: {
          orientation: 'left',
          style: 'font-weight: bold; font-size: 16px;',
        },
      },
      // 基本信息字段
      {
        fieldName: 'sn',
        label: '资产SN',
        component: 'Input',
        componentProps: { placeholder: '请输入资产SN' },
        rules: 'required',
      },
      {
        fieldName: 'assetType',
        label: '资产类型',
        component: 'Select',
        componentProps: {
          placeholder: '请选择资产类型',
          options: assetTypeOptions,
          style: { width: '100%' },
        },
        rules: 'required',
      },
      {
        fieldName: 'resource.hostname',
        label: '主机名',
        component: 'Input',
        componentProps: { placeholder: '请输入主机名' },
      },
      {
        fieldName: 'resource.cluster',
        label: '集群',
        component: 'Select',
        componentProps: {
          placeholder: '请选择集群',
          options: clusterOptions,
          allowClear: true,
          showSearch: true,
          filterOption: (input: string, option: any) =>
            option.label.toLowerCase().includes(input.toLowerCase()),
          style: { width: '100%' },
        },
      },
      {
        fieldName: 'brand',
        label: '厂商',
        component: 'Input',
        componentProps: { placeholder: '请输入厂商' },
      },
      {
        fieldName: 'model',
        label: '型号',
        component: 'Input',
        componentProps: { placeholder: '请输入型号' },
      },
      {
        fieldName: 'templateID',
        label: '套餐模板',
        component: 'Select',
        componentProps: {
          placeholder: '请选择套餐模板',
          options: templateOptions,
          showSearch: true,
          filterOption: (input: string, option: any) =>
            option.label.toLowerCase().includes(input.toLowerCase()),
          allowClear: true,
          style: { width: '100%' },
        },
        rules: 'required',
      },
      {
        fieldName: 'resource.project',
        label: '项目',
        component: 'Select',
        componentProps: {
          placeholder: '请选择项目',
          options: projectOptions,
          allowClear: true,
          showSearch: true,
          filterOption: (input: string, option: any) =>
            option.label.toLowerCase().includes(input.toLowerCase()),
          style: { width: '100%' },
        },
      },

      // 状态信息标题
      {
        component: 'Divider',
        label: '状态信息',
        fieldName: 'status_info_title',
        wrapperClass: 'col-span-2 !mt-8 !mb-4',
        formItemClass: 'col-span-2',
        componentProps: {
          orientation: 'left',
          style: 'font-weight: bold; font-size: 16px;',
        },
      },
      // 状态信息字段
      {
        fieldName: 'assetStatus',
        label: '资产状态',
        component: 'Select',
        componentProps: {
          placeholder: '请选择资产状态',
          options: Object.entries(assetStatusMap).map(([value, item]) => ({
            label: item.text,
            value,
          })),
          style: { width: '100%' },
        },
        rules: 'required',
      },
      {
        fieldName: 'hardwareStatus',
        label: '硬件状态',
        component: 'Select',
        componentProps: {
          placeholder: '请选择硬件状态',
          options: Object.entries(hardwareStatusMap).map(([value, item]) => ({
            label: item.text,
            value,
          })),
          style: { width: '100%' },
        },
        rules: 'required',
      },
      {
        fieldName: 'resource.bizStatus',
        label: '业务状态',
        component: 'Select',
        componentProps: {
          placeholder: '请选择业务状态',
          options: Object.entries(bizStatusMap).map(([value, item]) => ({
            label: item.text,
            value,
          })),
          style: { width: '100%' },
        },
      },
      {
        fieldName: 'resource.isBackup',
        label: '是否备机',
        component: 'Switch',
        componentProps: {
          checkedChildren: '是',
          unCheckedChildren: '否',
        },
      },

      // 位置信息标题
      {
        component: 'Divider',
        label: '位置信息',
        fieldName: 'location_info_title',
        wrapperClass: 'col-span-2 !mt-8 !mb-4',
        formItemClass: 'col-span-2',
        componentProps: {
          orientation: 'left',
          style: 'font-weight: bold; font-size: 16px;',
        },
      },
      // 位置信息字段
      {
        fieldName: 'resource.roomID',
        label: '房间',
        component: 'Select',
        componentProps: {
          placeholder: '请先选择房间',
          options: roomOptions,
          allowClear: true,
          showSearch: true,
          filterOption: (input: string, option: any) =>
            option.label.toLowerCase().includes(input.toLowerCase()),
          style: { width: '100%' },
          loading: loadingRooms,
          onDropdownVisibleChange: (open: boolean) => {
            if (open) {
              fetchRooms();
            }
          },
          onChange: (value: number) => {
            // 当选择房间后，清空并重新加载机柜选项

            // 清空机柜选项和选择值
            cabinetOptions.value = [];

            // 如果选择了房间，则加载该房间的机柜
            if (value) {
              // 直接调用加载机柜数据的方法
              fetchCabinetsByRoomId(value);
            }

            // 清空机柜选择
            try {
              const formElement = document.querySelector('form');
              if (
                formElement &&
                formElement.__vueParentComponent &&
                formElement.__vueParentComponent.ctx
              ) {
                const formModel =
                  formElement.__vueParentComponent.ctx.formModel;
                if (formModel && formModel.resource) {
                  formModel.resource.cabinetID = undefined;
                }
              }
            } catch (error) {
              console.error('清空机柜选择失败:', error);
            }
          },
        },
        rules: 'required',
      },
      {
        fieldName: 'resource.cabinetID',
        label: '机柜',
        component: 'Select',
        componentProps: {
          placeholder: '请先选择房间，再选择机柜',
          options: cabinetOptions,
          allowClear: true,
          showSearch: true,
          filterOption: (input: string, option: any) =>
            option.label.toLowerCase().includes(input.toLowerCase()),
          style: { width: '100%' },
          loading: loadingCabinets,
          disabled: loadingCabinets.value,
        },
        rules: 'required',
      },
      {
        fieldName: 'resource.rackPosition',
        label: '机架位',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入机架位',
          min: 0,
          style: { width: '100%' },
        },
      },

      // 网络信息标题
      {
        component: 'Divider',
        label: '网络信息',
        fieldName: 'network_info_title',
        wrapperClass: 'col-span-2 !mt-8 !mb-4',
        formItemClass: 'col-span-2',
        componentProps: {
          orientation: 'left',
          style: 'font-weight: bold; font-size: 16px;',
        },
      },
      // 网络信息字段

      {
        fieldName: 'resource.bmcIP',
        label: 'BMC IP',
        component: 'Input',
        componentProps: { placeholder: '请输入BMC IP' },
      },
      {
        fieldName: 'resource.vpcIP',
        label: 'VPC IP',
        component: 'Input',
        componentProps: { placeholder: '请输入VPC IP' },
      },

      // 财务信息标题
      {
        component: 'Divider',
        label: '财务信息',
        fieldName: 'financial_info_title',
        wrapperClass: 'col-span-2 !mt-8 !mb-4',
        formItemClass: 'col-span-2',
        componentProps: {
          orientation: 'left',
          style: 'font-weight: bold; font-size: 16px;',
        },
      },
      // 财务信息字段
      {
        fieldName: 'purchaseOrder',
        label: '采购合同',
        component: 'Input',
        componentProps: { placeholder: '请输入采购合同' },
      },
      {
        fieldName: 'price',
        label: '金额',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入金额',
          min: 0,
          precision: 2,
          style: { width: '100%' },
        },
      },
      {
        fieldName: 'residualValue',
        label: '残值',
        component: 'InputNumber',
        componentProps: {
          placeholder: '请输入残值',
          min: 0,
          precision: 2,
          style: { width: '100%' },
        },
      },

      // 时间信息标题
      {
        component: 'Divider',
        label: '时间信息',
        fieldName: 'time_info_title',
        wrapperClass: 'col-span-2 !mt-8 !mb-4',
        formItemClass: 'col-span-2',
        componentProps: {
          orientation: 'left',
          style: 'font-weight: bold; font-size: 16px;',
        },
      },
      // 时间信息字段
      {
        fieldName: 'purchaseDate',
        label: '购买时间',
        component: 'DatePicker',
        componentProps: {
          placeholder: '请选择购买时间',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        fieldName: 'warrantyExpire',
        label: '过保时间',
        component: 'DatePicker',
        componentProps: {
          placeholder: '请选择过保时间',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        fieldName: 'resource.rackingTime',
        label: '上架时间',
        component: 'DatePicker',
        componentProps: {
          placeholder: '请选择上架时间',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        fieldName: 'resource.deliveryTime',
        label: '交付时间',
        component: 'DatePicker',
        componentProps: {
          placeholder: '请选择交付时间',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },

      // 其他信息标题
      {
        component: 'Divider',
        label: '其他信息',
        fieldName: 'other_info_title',
        wrapperClass: 'col-span-2 !mt-8 !mb-4',
        formItemClass: 'col-span-2',
        componentProps: {
          orientation: 'left',
          style: 'font-weight: bold; font-size: 16px;',
        },
      },
      // 其他信息字段
      {
        fieldName: 'remark',
        label: '备注',
        component: 'Input',
        componentProps: {
          placeholder: '请输入备注',
          rows: 4,
          type: 'textarea',
        },
        wrapperClass: 'col-span-2',
      },
    ],
  };
}

// 详情字段配置 - 展示全部信息
export const detailFields = [
  // 基本信息组
  { label: 'ID', field: 'id', group: '基本信息' },
  { label: '资产SN', field: 'sn', group: '基本信息' },
  { label: '厂商', field: 'brand', group: '基本信息' },
  { label: '型号', field: 'model', group: '基本信息' },
  {
    label: '资产类型',
    field: 'assetType',
    format: (value: any) => {
      if (!value) return '暂无';
      const option = assetTypeOptions.find((opt) => opt.value === value);
      return option ? option.label : value;
    },
    group: '基本信息',
  },
  {
    label: '套餐模板',
    field: 'template.templateName',
    format: (value: any) => value || '暂无',
    group: '基本信息',
  },
  {
    label: '集群',
    field: 'resource.cluster',
    format: (value: any) => value || '暂无',
    group: '基本信息',
  },

  // 状态信息组
  {
    label: '资产状态',
    field: 'assetStatus',
    type: 'status' as const,
    statusMap: assetStatusMap,
    group: '状态信息',
  },
  {
    label: '硬件状态',
    field: 'hardwareStatus',
    type: 'status' as const,
    statusMap: hardwareStatusMap,
    group: '状态信息',
  },
  {
    label: '业务状态',
    field: 'resource.bizStatus',
    type: 'status' as const,
    statusMap: bizStatusMap,
    group: '状态信息',
  },
  { label: '最后状态变更时间', field: 'lastStatusChange', group: '状态信息' },

  // 位置信息组
  {
    label: '区域',
    field: 'resource.cabinet.room.dataCenter.az.region.name',
    format: (value: any) => value || '暂无',
    group: '位置信息',
  },
  {
    label: '可用区',
    field: 'resource.cabinet.room.dataCenter.az.name',
    format: (value: any) => value || '暂无',
    group: '位置信息',
  },
  {
    label: '机房',
    field: 'resource.cabinet.room.dataCenter.name',
    format: (value: any) => value || '暂无',
    group: '位置信息',
  },
  {
    label: '房间',
    field: 'resource.cabinet.room.name',
    format: (value: any) => value || '暂无',
    group: '位置信息',
  },
  {
    label: '机柜',
    field: 'resource.cabinet.name',
    format: (value: any) => value || '暂无',
    group: '位置信息',
  },
  {
    label: '高度',
    field: 'resource.cabinet.capacityUnits',
    format: (value: any) => value || '暂无',
    group: '位置信息',
  },
  {
    label: '机架位',
    field: 'resource.rackPosition',
    format: (value: any) => value || '暂无',
    group: '位置信息',
  },

  // 网络信息组
  {
    label: '资源SN',
    field: 'resource.sn',
    format: (value: any) => value || '暂无',
    group: '网络信息',
  },
  {
    label: 'BMC IP',
    field: 'resource.bmcIP',
    format: (value: any) => value || '暂无',
    group: '网络信息',
  },
  {
    label: 'VPC IP',
    field: 'resource.vpcIP',
    format: (value: any) => value || '暂无',
    group: '网络信息',
  },
  {
    label: '集群',
    field: 'resource.cluster',
    format: (value: any) => value || '暂无',
    group: '网络信息',
  },

  // 项目信息组
  {
    label: '所属项目',
    field: 'resource.project',
    format: (value: any) => value || '暂无',
    group: '项目信息',
  },
  {
    label: '是否备机',
    field: 'resource.isBackup',
    format: (value: any) => (value ? '是' : '否'),
    group: '项目信息',
  },

  // 时间信息组
  { label: '上架时间', field: 'resource.rackingTime', group: '时间信息' },
  { label: '交付时间', field: 'resource.deliveryTime', group: '时间信息' },
  { label: '购买时间', field: 'purchaseDate', group: '时间信息' },
  { label: '过保时间', field: 'warrantyExpire', group: '时间信息' },

  // 财务信息组
  { label: '采购合同', field: 'purchaseOrder', group: '财务信息' },
  { label: '金额', field: 'price', group: '财务信息' },
  { label: '残值', field: 'residualValue', group: '财务信息' },

  // 其他信息组
  { label: '备注', field: 'remark', group: '其他信息' },
  { label: '创建时间', field: 'created_at', group: '其他信息' },
  { label: '更新时间', field: 'updated_at', group: '其他信息' },
];

// 表格配置
export const gridOptions: VxeGridProps<Device> = {
  checkboxConfig: {
    highlight: true,
  },
  exportConfig: {}, // 导出配置
  importConfig: {
    types: ['csv'],
    remote: true,
  }, // 导入配置
  columns: [
    { align: 'center', fixed: 'left', type: 'checkbox', width: 40 },
    { field: 'id', title: 'ID', width: 80, fixed: 'left' },
    { field: 'sn', title: '资产SN', fixed: 'left', width: 180 },
    {
      field: 'assetType',
      title: '类型',
      width: 100,
      slots: { default: 'assetType' },
    },
    {
      field: 'resource.hostname',
      title: '主机名',
      width: 200,
    },

    {
      field: 'assetStatus',
      title: '资产状态',
      slots: { default: 'assetStatus' },
      width: 100,
    },
    {
      field: 'resource.bizStatus',
      title: '业务状态',
      slots: { default: 'bizStatus' },
      width: 100,
    },
    {
      field: 'resource.bmcIP',
      title: 'BMC IP',
      slots: { default: 'bmcIP' },
      width: 140,
    },
    {
      field: 'resource.vpcIP',
      title: 'VPC IP',
      slots: { default: 'vpcIP' },
      width: 140,
    },
    {
      field: 'resource.tenantIP',
      title: '租户IP',
      width: 140,
      visible: false,
    },
    {
      field: 'resource.project',
      title: '项目',
      slots: { default: 'project' },
      width: 140,
    },

    {
      field: 'resource.cabinet.room.dataCenter.name',
      title: '机房',
      slots: { default: 'dataCenter' },
      width: 120,
    },
    {
      field: 'resource.cabinet.name',
      title: '机柜',
      slots: { default: 'cabinet' },
      width: 100,
    },
    {
      field: 'resource.cabinet.capacityUnits',
      title: '高度',
      slots: { default: 'capacityUnits' },
      width: 100,
    },

    { field: 'brand', title: '厂商', width: 120, visible: false },
    { field: 'model', title: '型号', width: 140, visible: false },
    {
      field: 'template.templateName',
      title: '套餐模板',
      slots: { default: 'templateName' },
      width: 140,
    },
    { field: 'purchaseDate', title: '购买时间', width: 180, visible: false },
    { field: 'warrantyExpire', title: '过保时间', width: 180, visible: false },
    {
      field: 'hardwareStatus',
      title: '硬件状态',
      width: 100,
      slots: { default: 'hardwareStatus' },
      visible: true,
    },
    { field: 'price', title: '金额', width: 100, visible: false },
    { field: 'residualValue', title: '残值', width: 100, visible: false },
    { field: 'resource.sn', title: '资源SN', width: 140, visible: false },
    {
      field: 'resource.rackPosition',
      title: '机架位',
      width: 80,
      visible: false,
    },
    {
      field: 'resource.isBackup',
      title: '是否备机',
      width: 80,
      slots: { default: 'isBackup' },
      visible: true,
    },
    {
      field: 'resource.cluster',
      title: '集群',
      width: 180,
      visible: true,
    },
    {
      field: 'resource.rackingTime',
      title: '上架时间',
      width: 120,
      visible: false,
    },
    {
      field: 'resource.deliveryTime',
      title: '交付时间',
      width: 120,
      visible: false,
    },
    { field: 'remark', title: '备注', width: 200, visible: false },
    { field: 'created_at', title: '创建时间', width: 180, visible: false },
    { field: 'updated_at', title: '更新时间', width: 180, visible: false },
    {
      title: '操作',
      slots: { default: 'operate' },
      width: 200,
      fixed: 'right',
    },
  ],
  keepSource: true,
  height: 'auto',
  rowConfig: { isHover: true },
  pagerConfig: { pageSize: 10, pageSizes: [10, 20, 50, 100] },
  toolbarConfig: {
    custom: true,
    refresh: true,
    export: true,
    zoom: true,
    import: true,
    slots: {
      buttons: 'toolbar-buttons',
    },
  },
};
