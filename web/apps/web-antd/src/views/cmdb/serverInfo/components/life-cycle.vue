<script setup lang="ts">
import type {
  AssetStatusChangeLog,
  listLogParams,
} from '#/api/core/cmdb/asset/serverComponent';

import { computed, h, onMounted, ref } from 'vue';

import { createIconifyIcon } from '@vben/icons';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { getAssetStatusChangeHistoryApi } from '#/api/core/cmdb/asset/serverComponent';
import { router } from '#/router';
import { formatDateTime } from '#/utils/common/time';

import {
  assetStatusMap,
  bizStatusMap,
  hardwareStatusMap,
} from '../server-config';

const props = defineProps<{
  assetId: number;
}>();

const pageData = ref({
  source: 'new_purchase', // 默认选中采购
});

const operatorOptions = ref<{ label: string; value: number }[]>([]);

const originData = ref({
  assetStatusLogsHistory: [] as AssetStatusChangeLog[],
});
const sourceEnum = {
  new_purchase: '采购',
  inbound: '入库',
  outbound: '出库',
  on_rack: '上架',
  online: '部署',
  ops: '运维',
  offline: '下线',
};

const iconComponents: Record<string, ReturnType<typeof createIconifyIcon>> = {
  new_purchase: createIconifyIcon('mdi:cart-arrow-down'),
  inbound: createIconifyIcon('mdi:import'),
  outbound: createIconifyIcon('mdi:export'),
  on_rack: createIconifyIcon('mdi:warehouse'),
  online: createIconifyIcon('mdi:cloud'),
  ops: createIconifyIcon('tabler:tools'),
  offline: createIconifyIcon('mdi:power'),
};

// 提前构造稳定的 VNode，不要在 render 时频繁 h()
const iconVNodeMap: Record<string, ReturnType<typeof h>> = {};
Object.entries(iconComponents).forEach(([key, comp]) => {
  iconVNodeMap[key] = h(comp, {
    style: {
      fontSize: '20px',
      display: 'inline-block',
      verticalAlign: 'middle',
    },
  });
});

// 生成相应的步骤条
const lifeCycleItems = computed(() => {
  return Object.entries(sourceEnum).map(([key, value]) => ({
    title: value,
    icon: iconVNodeMap[key],
    description: '相关记录 {} 条'.replace(
      '{}',
      String(
        originData.value.assetStatusLogsHistory.filter(
          (log) => log.source === key,
        ).length,
      ),
    ),
  }));
});

const currentStep: Record<string, number> = {
  new_purchase: 0,
  inbound: 1,
  outbound: 2,
  on_rack: 3,
  online: 4,
  ops: 5,
  offline: 6,
};

const current = ref(currentStep[pageData.value.source]);

// 根据source字段对数据进行分类
const groupedBySource = computed(() => {
  const result: Record<string, AssetStatusChangeLog[]> = {};
  // 确保每一个来源都有对应的数组
  Object.keys(sourceEnum).forEach((value) => {
    result[value] = [];
  });

  originData.value.assetStatusLogsHistory.forEach((log) => {
    const source = log.source;
    // 双重检查：确保 source 在 sourceEnum 中定义且在 result 中存在
    if (source in sourceEnum && result[source]) {
      result[source]!.push(log);
    }
  });
  return result;
});

// 排序后的数据（不修改原数组，倒序排序）
const sortedData = computed(() => {
  const result: Record<string, AssetStatusChangeLog[]> = {};
  Object.keys(groupedBySource.value).forEach((key) => {
    result[key] = (groupedBySource.value[key] || []).sort((a, b) => {
      return (
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
    });
  });
  return result;
});

// 根据workflow_id对数据进行分组
const groupedByWorkflowId = computed(() => {
  const result: Record<string, Record<string, AssetStatusChangeLog[]>> = {};

  Object.keys(sortedData.value).forEach((sourceKey) => {
    result[sourceKey] = {};

    // 对每个source下的数据按workflow_id分组
    const sourceData = sortedData.value[sourceKey];
    if (sourceData) {
      sourceData.forEach((log) => {
        const workflowId = log.workflow_id || 'no_workflow_id';

        if (!result[sourceKey]![workflowId]) {
          result[sourceKey]![workflowId] = [];
        }
        result[sourceKey]![workflowId]?.push(log);
      });
    }
  });
  return result;
});

// 组装详情数据
// const lifeCycleList = computed(() => {
//   const ticket_ids: Record<string, string[]> = {};
//   const result: Record<string, Record<string, AssetStatusChangeLog[]>> = {};

//   // 获取报障单数据
//   Object.keys(groupedByWorkflowId.value).forEach((sourceKey) => {
//     result[sourceKey] = {};
//     // 获取每一个source下的所有id
//     const workflows = groupedByWorkflowId.value[sourceKey] ?? {};
//     Object.keys(workflows).forEach((workflowId) => {
//       const ticket_id = workflowId.split('-').pop()!;
//       ticket_ids[sourceKey] = ticket_ids[sourceKey] || [];
//       ticket_ids[sourceKey]?.push(ticket_id);
//     });

//     // 批量获取报障单详情
//   });
//   return result;
// });

// 获取当前选中source下的所有workflow组
const currentWorkflowGroups = computed(() => {
  const sourceKey = pageData.value.source;
  return groupedByWorkflowId.value[sourceKey] || {};
});

const getAssetStatusChangeHistory = async (params: listLogParams) => {
  const res = await getAssetStatusChangeHistoryApi(params);
  // 过滤掉不在 sourceEnum 中定义的 source 值
  originData.value.assetStatusLogsHistory = res.list.filter(
    (log) => log.source in sourceEnum,
  );
};

// 步骤条变化时的处理函数
const onChange = (stepIndex: number) => {
  current.value = stepIndex; // 更新步骤条
  const keys = Object.keys(currentStep);
  const selectedSource =
    keys.find((key) => currentStep[key] === stepIndex) || 'new_purchase';
  pageData.value.source = selectedSource;
};

const [BaseForm, BaseFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    componentProps: {
      class: 'w-full',
      allowClear: true,
    },
  },
  handleSubmit: async (values) => {
    console.warn('values', values);
    await getAssetStatusChangeHistory({
      assetId: props.assetId,
      page: 1,
      pageSize: 1000,
      startTime: values.rangePicker[0]?.toISOString(),
      endTime: values.rangePicker[1]?.toISOString(),
      operatorID: values.operator,
    });
    // 确保设置的 source 值在 sourceEnum 中定义
    const firstValidSource = originData.value.assetStatusLogsHistory[0]?.source;
    pageData.value.source =
      firstValidSource && firstValidSource in sourceEnum
        ? firstValidSource
        : 'new_purchase';
    current.value = currentStep[pageData.value.source];
    message.success('查询成功');
  },
  handleReset: async () => {
    await getAssetStatusChangeHistory({
      assetId: props.assetId,
      page: 1,
      pageSize: 1000,
    });
    BaseFormApi.resetForm();
    // 确保设置的 source 值在 sourceEnum 中定义
    const firstValidSource = originData.value.assetStatusLogsHistory[0]?.source;
    pageData.value.source =
      firstValidSource && firstValidSource in sourceEnum
        ? firstValidSource
        : 'new_purchase';
    current.value = currentStep[pageData.value.source];
    message.success('重置成功');
  },
  wrapperClass: 'grid-cols-4',
  collapsed: true,
  // 提交函数
  // handleSubmit: onSubmit,
  layout: 'horizontal',
  schema: [
    {
      component: 'RangePicker',
      fieldName: 'rangePicker',
      label: '时间',
      labelClass: 'w-10',
      formItemClass: 'col-span-2',
      defaultValue: [],
    },
    {
      component: 'Select',
      fieldName: 'operator',
      label: '操作人',
      componentProps: {
        options: operatorOptions,
      },
      labelClass: 'w-15 ml-10',
      formItemClass: 'col-span-2',
    },
  ],
});

const handleTicketClick = (workflowId: string, ticketNo: string) => {
  if (!workflowId) return;

  // 拆分 workflowId，例如 'fault_ticket_45'
  const parts = workflowId.split('_');
  if (parts.length < 2) {
    message.error(`跳转失败，未知类型: ${workflowId}`);
    return;
  }

  const right = parts.pop(); // 拿到最后一段 '45'
  const left = parts.join('_'); // 剩下前面部分 'fault_ticket'
  let path = '';

  switch (left) {
    case 'acceptance_order': {
      // 验收工单跳转
      path = `/hardware-maintenance/asset-acceptance/${right}`;
      break;
    }
    case 'device_inbound': {
      // 设备入库工单跳转
      path = `/asset-storage-detail/${ticketNo}`;
      break;
    }
    case 'device_outbound': {
      // 设备出库工单跳转
      path = `/asset-out-warehouse-detail/${right}`;
      break;
    }
    case 'fault_ticket': {
      // 故障工单跳转
      path = `/fault-report-management/detail/${right}`;
      break;
    }
    case 'launch_ticket': {
      // 上线工单跳转
      path = `/soft-maintenance/soft-launch-detail/${right}`;
      break;
    }
    case 'purchase_contract': {
      // 采购合同跳转
      path = `/purchase-management/purchase-contract/detail/${right}`;
      break;
    }
    case 'purchase_inquiry': {
      // 采购询价跳转
      path = `/purchase-management/purchase-inquiry/detail/${right}`;
      break;
    }
    case 'purchase_request': {
      // 采购请求跳转
      path = `/purchase-management/purchase-request/detail/${right}`;
      break;
    }
    case 'racking_order': {
      // 上架工单跳转
      path = `/hardware-maintenance/asset-racking-detail/${right}`;
      break;
    }
    default: {
      message.error(`跳转失败，未知类型: ${left}`);
      break;
    }
  }
  const route = router.resolve({
    path,
  });
  window.open(route.href, '_blank');
};

// 获取操作人列表并设置为下拉框选项
const getOperatorList = () => {
  // 从资产状态变更历史中提取操作人信息
  const operatorMap = new Map<number, string>();

  originData.value.assetStatusLogsHistory.forEach((log) => {
    if (log.operator_id && log.operator_name) {
      operatorMap.set(log.operator_id, log.operator_name);
    }
  });

  // 转换为下拉框选项格式
  const options = [...operatorMap.entries()].map(([id, name]) => ({
    label: name,
    value: id,
  }));
  // 更新操作人选项
  operatorOptions.value = options;
};

onMounted(async () => {
  await getAssetStatusChangeHistory({
    assetId: props.assetId,
    page: 1,
    pageSize: 1000,
  });

  // 获取操作人列表
  getOperatorList();

  // 确保初始化时显示默认选中的source对应的数据
  const firstValidSource = originData.value.assetStatusLogsHistory[0]?.source;
  const defaultSource =
    firstValidSource && firstValidSource in sourceEnum
      ? firstValidSource
      : 'new_purchase';
  const defaultStep = currentStep[defaultSource];

  // 如果有默认步骤，触发一次onChange来初始化视图
  if (defaultStep !== undefined) {
    onChange(defaultStep);
  }
});
</script>

<template>
  <div class="life-cycle-container">
    <!-- 表单区域 -->
    <div class="form-section rounded-md bg-white shadow-md">
      <BaseForm />
    </div>

    <div class="flex flex-col md:flex-row">
      <!-- 左侧步骤条 -->
      <div
        class="sticky-steps mb-4 w-full rounded-md bg-white p-4 shadow-sm md:mb-0 md:mr-4 md:w-1/4"
      >
        <h3 class="mb-4 text-lg font-medium">生命周期</h3>
        <a-steps
          v-model:current="current"
          direction="vertical"
          :items="lifeCycleItems"
          @change="onChange"
        />
      </div>

      <!-- 右侧列表 -->
      <div class="w-full rounded-md bg-white p-4 md:w-3/4">
        <!-- 标题显示当前选中的源类型 -->
        <h3 class="mb-4 border-b pb-2 text-lg font-medium">
          {{ sourceEnum[pageData.source as keyof typeof sourceEnum] }}记录
        </h3>

        <!-- 只显示当前选中的source对应的数据 -->
        <div
          v-if="
            sortedData[pageData.source as keyof typeof sortedData] &&
            sortedData[pageData.source as keyof typeof sortedData]!.length > 0
          "
        >
          <!-- 按workflow_id分组显示 -->
          <div
            v-for="(logs, workflowId) in currentWorkflowGroups"
            :key="workflowId"
            class="mb-4"
          >
            <a-card
              v-if="logs && logs.length > 0"
              class="w-full rounded-md bg-white"
              :bordered="true"
            >
              <template #title>
                <div class="flex items-center justify-between">
                  <span class="text-primary">
                    {{ formatDateTime(logs[0]?.created_at || '') }}
                  </span>
                  <!-- <span class="text-gray-500">
                    操作人: {{ logs[0]?.operator_name || '' }}
                  </span> -->
                </div>
              </template>

              <!-- 工单信息 -->
              <span
                v-if="logs[0]?.ticket_no"
                @click="handleTicketClick(workflowId, logs[0].ticket_no)"
                class="mb-2 block cursor-pointer text-gray-700 hover:text-blue-600 hover:underline"
              >
                工单单号: {{ logs[0].ticket_no }}
              </span>

              <!-- 状态变更记录列表 -->
              <div class="mt-4">
                <a-timeline>
                  <a-timeline-item
                    v-for="(item, index) in logs"
                    :key="index"
                    :color="
                      item?.new_asset_status
                        ? assetStatusMap[item.new_asset_status]?.color || 'blue'
                        : 'blue'
                    "
                  >
                    <div class="text-sm">
                      <div class="flex items-center">
                        <span class="mr-4 font-medium">
                          {{ logs.length - index }}、时间：
                          {{ formatDateTime(item?.created_at || '') }}
                        </span>
                        <span class="text-gray-500">
                          操作人: {{ item.operator_name || '' }}
                        </span>
                      </div>

                      <div v-if="item?.change_reason" class="mb-2 mt-2">
                        <span class="text-gray-500">备注：</span>
                        {{ item.change_reason }}
                      </div>
                      <div class="mt-2 grid grid-cols-2 flex-wrap items-center">
                        <div
                          v-if="item?.new_asset_status"
                          class="col-span-1 mb-2"
                        >
                          <span class="text-gray-500">资产状态：</span>
                          <a-tag
                            :color="
                              assetStatusMap[item.new_asset_status]?.color
                            "
                          >
                            {{ assetStatusMap[item.new_asset_status]?.text }}
                          </a-tag>
                        </div>
                        <div v-if="item?.new_biz_status" class="col-span-1">
                          <span class="text-gray-500">业务状态：</span>
                          <a-tag
                            :color="bizStatusMap[item.new_biz_status]?.color"
                          >
                            {{ bizStatusMap[item.new_biz_status]?.text }}
                          </a-tag>
                        </div>
                        <div
                          v-if="item?.new_hardware_status"
                          class="col-span-1"
                        >
                          <span class="text-gray-500">硬件状态：</span>
                          <a-tag
                            :color="
                              hardwareStatusMap[item.new_hardware_status]?.color
                            "
                          >
                            {{
                              hardwareStatusMap[item.new_hardware_status]?.text
                            }}
                          </a-tag>
                        </div>
                      </div>
                    </div>
                  </a-timeline-item>
                </a-timeline>
              </div>
            </a-card>
          </div>
        </div>
        <a-empty
          v-else
          description="暂无数据"
          class="my-4 rounded-md bg-gray-50 py-12"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.life-cycle-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-section {
  transition: all 0.3s ease;
}

.sticky-steps {
  position: sticky;
  top: 16px;
  max-height: calc(100vh - 32px);
  overflow-y: auto;
  transition: all 0.3s ease;
}

/* 当前选中步骤的字体颜色 */
:deep(.ant-steps-item-active .ant-steps-item-title) {
  color: #3b9f13 !important;
}

/* 确保当前选中步骤的描述文字也变成绿色 */
:deep(.ant-steps-item-active .ant-steps-item-description) {
  color: #3b9f13 !important;
}

/* 当前选中步骤的图标容器颜色 */
:deep(.ant-steps-item-active .ant-steps-item-icon) {
  color: #3b9f13 !important;
  border-color: #3b9f13 !important;
}

/* 当前选中步骤的SVG图标颜色 */
:deep(.ant-steps-item-active .ant-steps-item-icon svg) {
  color: #3b9f13 !important;
  fill: #3b9f13 !important;
}

/* 添加卡片悬停效果 */
:deep(.ant-card) {
  transition: all 0.3s ease;
}

:deep(.ant-card:hover) {
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  transform: translateY(-2px);
}
</style>
