<script lang="ts" setup>
import type { Device, ServerComponent } from '#/api/core/cmdb/asset/device';
import type {
  ComponentChangeInfo,
  ComponentChangeLog,
  ComponentRemoveInfo,
} from '#/api/core/cmdb/asset/serverComponent';
import type {
  AssetSpare,
  SpareListResponse,
} from '#/api/core/cmdb/asset/spare';

import { h, onMounted, ref } from 'vue';

import {
  Button,
  Divider,
  Empty,
  Form,
  Input,
  Modal,
  notification,
  Select,
  Spin,
  Table,
  Tabs,
  Tag,
} from 'ant-design-vue';

import {
  getDevicePartsApi,
  getDevicePlansApi,
  getDeviceResourceDetailApi,
} from '#/api/core/cmdb/asset/device';
import {
  getServerComponentChangeHistoryApi,
  removeComponentApi,
  replaceComponentApi,
} from '#/api/core/cmdb/asset/serverComponent';
import { getSparesByComponentTypeApi } from '#/api/core/cmdb/asset/spare';
import { getCabinetsByRoomIdApi } from '#/api/core/cmdb/location/cabinet';
import { getRoomTableApi } from '#/api/core/cmdb/location/room';
import CommonDetail from '#/components/CommonDetail/index.vue';
import CommonList from '#/components/CommonList/index.vue';
import LifeCycle from '#/views/cmdb/serverInfo/components/life-cycle.vue';

// 导入抽离的表单和表格配置
import {
  createEditFormOptions,
  createFormOptions,
  detailFields,
  gridOptions,
} from './form-grid-config';
// 导入抽离的配置和函数
import {
  assetStatusMap,
  assetTypeOptions,
  bizStatusMap,
  createWrappedApi,
  defaultData,
  fetchClusterOptions,
  fetchProjectOptions,
  fetchTemplateOptions,
  hardwareStatusMap,
  processPlanData,
} from './server-config';

const detailRef = ref();
const detailData = ref<Device | null>(null);
const commonListRef = ref();
const cabinetOptions = ref<{ label: string; value: number }[]>([]);
const roomOptions = ref<{ label: string; value: number }[]>([]);
const templateOptions = ref<{ label: string; value: number }[]>([]);
const projectOptions = ref<{ label: string; value: string }[]>([]);
const clusterOptions = ref<{ label: string; value: string }[]>([]);
const loadingRooms = ref(false);
const loadingCabinets = ref(false);

onMounted(async () => {
  // 先获取所有选项数据
  templateOptions.value = await fetchTemplateOptions();
  projectOptions.value = await fetchProjectOptions();
  clusterOptions.value = await fetchClusterOptions();
});

async function fetchRooms() {
  if (roomOptions.value.length > 0) return;

  loadingRooms.value = true;
  try {
    const res = await getRoomTableApi({ page: 1, pageSize: 1000 });
    roomOptions.value = res.list.map((item: any) => ({
      label: `${item.name}${item.dataCenter ? ` (${item.dataCenter.name})` : ''}`,
      value: item.id,
    }));
  } catch (error) {
    console.error('获取房间选项失败:', error);
  } finally {
    loadingRooms.value = false;
  }
}

async function fetchCabinetsByRoomId(roomId: number) {
  if (!roomId) {
    cabinetOptions.value = [];
    return;
  }

  loadingCabinets.value = true;
  try {
    const cabinets = await getCabinetsByRoomIdApi(roomId);
    cabinetOptions.value = cabinets.map((cabinet: any) => ({
      label: cabinet.name,
      value: cabinet.id,
    }));
  } catch (error) {
    console.error('根据房间ID获取机柜列表失败:', error);
    cabinetOptions.value = [];
  } finally {
    loadingCabinets.value = false;
  }
}

// 更新初始化编辑表单机柜数据的函数
async function initEditFormCabinets(row: any) {
  if (row && row.resource && row.resource.roomID) {
    // 先加载房间选项
    if (roomOptions.value.length === 0) {
      await fetchRooms();
    }

    // 记录当前房间ID和机柜ID
    const roomId = row.resource.roomID;
    const cabinetId = row.resource.cabinetID;

    // 根据房间ID加载机柜选项
    await fetchCabinetsByRoomId(roomId);

    // 检查加载后的机柜选项中是否包含当前机柜
    if (cabinetId) {
      const found = cabinetOptions.value.find((opt) => opt.value === cabinetId);
      if (!found) {
        console.warn(`警告: 未找到ID为${cabinetId}的机柜，可能数据不一致`);
      }
    }
  }
}

// 使用工厂函数创建表单配置
const formOptions = createFormOptions(
  projectOptions,
  clusterOptions,
  templateOptions,
);
const editFormOptions = createEditFormOptions(
  roomOptions,
  cabinetOptions,
  templateOptions,
  projectOptions,
  clusterOptions,
  loadingRooms,
  loadingCabinets,
  fetchRooms,
  fetchCabinetsByRoomId,
);

// 添加新的状态变量存储部件和套餐信息
const deviceParts = ref<ServerComponent[]>([]);
const devicePlans = ref<any[]>([]);
const loadingParts = ref(false);
const loadingPlans = ref(false);
const templateParts = ref<any[]>([]); // 存储模板部件信息
const activePartsTab = ref<string>('template'); // 默认显示模板部件

// 添加新的状态变量存储部件变更历史
const deviceChangeHistory = ref<ComponentChangeLog[]>([]);
const loadingChangeHistory = ref(false);
const changeHistoryPagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
});

const loadingAssetStatusLogsHistroy = ref(false);

// 替换组件对话框状态
const showReplaceComponentModal = ref(false);
const replacingComponentId = ref<null | number>(null);
const selectedSpareId = ref<number>(0);
const availableSpares = ref<any[]>([]);
const loadingSpares = ref(false);
const replaceForm = ref({
  reason: '',
  remarks: '',
});

// 移除组件对话框状态
const showRemoveComponentModal = ref(false);
const removingComponentId = ref<null | number>(null);
const removeForm = ref({
  reason: '',
  remarks: '',
});

// 修改控制台日志
function handleDetail(row: Device) {
  // 获取设备基本信息
  getDeviceResourceDetailApi(row.id)
    .then((response) => {
      // 确保套餐模板信息正确展示
      const deviceData = response as any;
      if (deviceData.templateID && !deviceData.template) {
        deviceData.template = {
          id: deviceData.templateID,
          templateName:
            templateOptions.value.find(
              (opt) => opt.value === deviceData.templateID,
            )?.label || '未知模板',
        };
      }
      detailData.value = deviceData;
      detailRef.value?.drawerApi.open();

      // 获取部件信息
      loadingParts.value = true;
      getDevicePartsApi(row.id)
        .then((data) => {
          deviceParts.value = data || [];
        })
        .catch((error) => {
          console.error('获取设备部件信息失败:', error);
        })
        .finally(() => {
          loadingParts.value = false;
        });

      // 获取套餐模版信息
      loadingPlans.value = true;
      getDevicePlansApi(row.id)
        .then((data) => {
          const result = processPlanData(data);
          devicePlans.value = result.devicePlans;
          templateParts.value = result.templateParts;
        })
        .catch((error) => {
          console.error('获取套餐模版信息失败:', error);
          devicePlans.value = [];
          templateParts.value = [];
        })
        .finally(() => {
          loadingPlans.value = false;
        });

      // 获取组件变更历史
      loadChangeHistory(row.id);
    })
    .catch((error) => {
      console.error('获取设备资源详情失败:', error);
    });
}

// 加载组件变更历史
function loadChangeHistory(serverId: number) {
  loadingChangeHistory.value = true;
  getServerComponentChangeHistoryApi(
    serverId,
    changeHistoryPagination.value.current,
    changeHistoryPagination.value.pageSize,
  )
    .then((response) => {
      deviceChangeHistory.value = response.list || [];
      changeHistoryPagination.value.total = response.total || 0;
    })
    .catch((error) => {
      console.error('获取组件变更历史失败:', error);
      deviceChangeHistory.value = [];
    })
    .finally(() => {
      loadingChangeHistory.value = false;
    });
}

// 变更历史分页变化
function onChangeHistoryPaginationChange(pagination: any) {
  changeHistoryPagination.value.current = pagination.current;
  changeHistoryPagination.value.pageSize = pagination.pageSize;
  if (detailData.value) {
    loadChangeHistory(detailData.value.id);
  }
}

// 显示替换组件对话框
function showReplaceComponent(component: ServerComponent) {
  replacingComponentId.value = component.id ?? null;
  showReplaceComponentModal.value = true;
  replaceForm.value = {
    reason: '',
    remarks: '',
  };

  // 加载可用备件列表
  loadingSpares.value = true;
  getSparesByComponentTypeApi(component.component_type || '', 1, 100)
    .then((response: SpareListResponse) => {
      availableSpares.value = (response.list || []).map(
        (spare: AssetSpare) => ({
          id: spare.id,
          label: `${spare.brand || ''} ${spare.model || ''} (SN: ${spare.sn || ''})`,
          value: spare.id,
        }),
      );
    })
    .catch((error: any) => {
      console.error('获取可用备件失败:', error);
      availableSpares.value = [];
    })
    .finally(() => {
      loadingSpares.value = false;
    });
}

// 执行替换组件操作
function handleReplaceComponent() {
  if (!replacingComponentId.value || !selectedSpareId.value) {
    notification.error({
      message: '参数错误',
      description: '请选择要替换的组件和备件',
    });
    return;
  }

  if (!replaceForm.value.reason) {
    notification.error({
      message: '参数错误',
      description: '请输入替换原因',
    });
    return;
  }

  const changeInfo: ComponentChangeInfo = {
    reason: replaceForm.value.reason,
    remarks: replaceForm.value.remarks,
  };

  replaceComponentApi(
    replacingComponentId.value,
    Number(selectedSpareId.value),
    changeInfo,
  )
    .then(() => {
      notification.success({
        message: '操作成功',
        description: '组件替换成功',
      });
      showReplaceComponentModal.value = false;
      // 刷新部件列表
      if (detailData.value) {
        getDevicePartsApi(detailData.value.id).then((data) => {
          deviceParts.value = data || [];
        });
        // 刷新变更历史
        loadChangeHistory(detailData.value.id);
      }
    })
    .catch((error) => {
      notification.error({
        message: '操作失败',
        description: `组件替换失败: ${error.message || '未知错误'}`,
      });
    });
}

// 显示移除组件对话框
function showRemoveComponent(component: ServerComponent) {
  removingComponentId.value = component.id ?? null;
  showRemoveComponentModal.value = true;
  removeForm.value = {
    reason: '',
    remarks: '',
  };
}

// 执行移除组件操作
function handleRemoveComponent() {
  if (!removingComponentId.value) {
    notification.error({
      message: '参数错误',
      description: '请选择要移除的组件',
    });
    return;
  }

  if (!removeForm.value.reason) {
    notification.error({
      message: '参数错误',
      description: '请输入移除原因',
    });
    return;
  }

  const removeInfo: ComponentRemoveInfo = {
    reason: removeForm.value.reason,
    remarks: removeForm.value.remarks,
  };

  removeComponentApi(removingComponentId.value, removeInfo)
    .then(() => {
      notification.success({
        message: '操作成功',
        description: '组件移除成功',
      });
      showRemoveComponentModal.value = false;
      // 刷新部件列表
      if (detailData.value) {
        getDevicePartsApi(detailData.value.id).then((data) => {
          deviceParts.value = data || [];
        });
        // 刷新变更历史
        loadChangeHistory(detailData.value.id);
      }
    })
    .catch((error) => {
      notification.error({
        message: '操作失败',
        description: `组件移除失败: ${error.message || '未知错误'}`,
      });
    });
}

// 自定义权限配置
const permissions = {
  add: ['Cmdb:ServerInfo:Create'],
  edit: ['Cmdb:ServerInfo:Edit'],
  delete: ['Cmdb:ServerInfo:Delete'],
  import: ['Cmdb:ServerInfo:Import'],
};

// 编辑后的回调函数
function handleEdited(row: Device) {
  try {
    // 强制刷新表格数据，确保模板名称正确显示
    if (commonListRef.value) {
      commonListRef.value.reload();
    }

    // 只有在查看该设备详情时才更新详情
    if (detailData.value && detailData.value.id === row.id) {
      // 如果当前正在查看该设备的详情，则更新详情
      try {
        getDeviceResourceDetailApi(row.id)
          .then((response) => {
            try {
              // 确保套餐模板信息正确展示
              const deviceData = response as any;
              if (deviceData.templateID && !deviceData.template) {
                deviceData.template = {
                  id: deviceData.templateID,
                  templateName:
                    templateOptions.value.find(
                      (opt) => opt.value === deviceData.templateID,
                    )?.label || '未知模板',
                };
              }
              detailData.value = deviceData;

              // 静默获取套餐模版信息
              loadingPlans.value = true;
              try {
                getDevicePlansApi(row.id)
                  .then((data) => {
                    try {
                      const result = processPlanData(data);
                      devicePlans.value = result.devicePlans;
                      templateParts.value = result.templateParts;
                    } catch (error) {
                      console.warn('处理套餐数据时出错:', error);
                      devicePlans.value = [];
                      templateParts.value = [];
                    }
                  })
                  .catch((error) => {
                    // 静默失败，不显示错误通知
                    console.warn('获取套餐模板失败，但不影响正常功能:', error);
                    devicePlans.value = [];
                    templateParts.value = [];
                  })
                  .finally(() => {
                    loadingPlans.value = false;
                  });
              } catch (error) {
                console.warn('加载套餐模板时出错:', error);
                loadingPlans.value = false;
              }
            } catch (error) {
              console.warn('处理设备详情数据时出错:', error);
            }
          })
          .catch((error) => {
            // 这里只保留一个关键错误通知
            console.warn('更新设备资源详情失败，但不影响保存结果:', error);
          });
      } catch (error) {
        console.warn('获取设备详情API调用时出错:', error);
      }
    }
  } catch (error) {
    console.warn('handleEdited函数执行时出错:', error);
  }
}

// 创建包装的API函数
const wrappedApi = createWrappedApi(() => templateOptions.value);

// 更新API包装器引用不再需要，因为我们现在每次获取最新的templateOptions
onMounted(async () => {
  // 先获取所有选项数据
  templateOptions.value = await fetchTemplateOptions();
  projectOptions.value = await fetchProjectOptions();
  clusterOptions.value = await fetchClusterOptions();
});
</script>

<template>
  <div>
    <!-- 使用通用列表组件 -->
    <CommonList
      ref="commonListRef"
      :form-options="formOptions"
      :grid-options="gridOptions"
      :edit-form-options="editFormOptions"
      :api="wrappedApi"
      :default-data="defaultData"
      @detail="handleDetail"
      @edit="(row) => initEditFormCabinets(row)"
      @add="fetchRooms"
      @edited="handleEdited"
      :permissions="permissions"
      show-add-button
      show-edit-button
      show-delete-button
      show-detail-button
      drawer-class="w-1/2"
      import-model-type="device-resource"
      :enable-confirm="true"
      :show-template-button="true"
    >
      <!-- 资产类型插槽 -->
      <template #assetType="{ row }">
        <span v-if="row && row.assetType">
          {{
            assetTypeOptions.find(
              (opt) =>
                opt.value === row.assetType || opt.label === row.assetType,
            )?.label || row.assetType
          }}
        </span>
        <span v-else>-</span>
      </template>

      <!-- 资产状态插槽 -->
      <template #assetStatus="{ row }">
        <Tag :color="assetStatusMap[row.assetStatus]?.color || 'default'">
          {{
            assetStatusMap[row.assetStatus]?.text ||
            row.assetStatus ||
            '未知状态'
          }}
        </Tag>
      </template>

      <!-- 业务状态插槽 -->
      <template #bizStatus="{ row }">
        <Tag
          v-if="row.resource?.bizStatus"
          :color="bizStatusMap[row.resource.bizStatus]?.color || 'default'"
        >
          {{
            bizStatusMap[row.resource.bizStatus]?.text ||
            row.resource.bizStatus ||
            '未知状态'
          }}
        </Tag>
        <span v-else>-</span>
      </template>

      <!-- 硬件状态插槽 -->
      <template #hardwareStatus="{ row }">
        <Tag :color="hardwareStatusMap[row.hardwareStatus]?.color || 'default'">
          {{
            hardwareStatusMap[row.hardwareStatus]?.text ||
            row.hardwareStatus ||
            '未知状态'
          }}
        </Tag>
      </template>

      <!-- 机房插槽 -->
      <template #dataCenter="{ row }">
        <span v-if="row.resource?.room?.dataCenter?.name">
          {{ row.resource.room.dataCenter.name }}
        </span>
        <span v-else-if="row.resource?.room?.name">
          {{ row.resource.room.name }} (机房信息不完整)
        </span>
        <span v-else>-</span>
      </template>

      <!-- 机柜插槽 -->
      <template #cabinet="{ row }">
        <span v-if="row.resource?.cabinet?.name">{{
          row.resource.cabinet.name
        }}</span>
        <span v-else>-</span>
      </template>

      <!-- 项目插槽 -->
      <template #project="{ row }">
        <span v-if="row.resource?.project">{{ row.resource.project }}</span>
        <span v-else>-</span>
      </template>

      <!-- BMC IP插槽 -->
      <template #bmcIP="{ row }">
        <span v-if="row.resource?.bmcIP">{{ row.resource.bmcIP }}</span>
        <span v-else>-</span>
      </template>

      <!-- VPC IP插槽 -->
      <template #vpcIP="{ row }">
        <span v-if="row.resource?.vpcIP">{{ row.resource.vpcIP }}</span>
        <span v-else>-</span>
      </template>

      <!-- 添加是否备机插槽 -->
      <template #isBackup="{ row }">
        <Tag
          v-if="row.resource?.isBackup !== undefined"
          :color="row.resource.isBackup ? 'green' : 'gray'"
        >
          {{ row.resource.isBackup ? '是' : '否' }}
        </Tag>
        <span v-else>-</span>
      </template>

      <!-- 添加套餐模板名称插槽 -->
      <template #templateName="{ row }">
        <span v-if="row.template?.templateName">
          {{ row.template.templateName }}
        </span>
        <span v-else>-</span>
      </template>

      <!-- 添加机柜容量单位插槽 -->
      <template #capacityUnits="{ row }">
        <span v-if="row.resource?.cabinet?.capacityUnits !== undefined">
          {{ row.resource.cabinet.capacityUnits }}
        </span>
        <span v-else>-</span>
      </template>
    </CommonList>

    <!-- 使用通用详情组件，添加自定义标签页 -->
    <CommonDetail
      ref="detailRef"
      :data="detailData"
      title="设备资源详情"
      :fields="detailFields"
      show-audit-log
      table-name="asset_device_resources"
      show-group
      class="w-1/2"
    >
      <!-- 添加自定义标签页 -->
      <template #tabs>
        <!-- 部件信息标签页 -->
        <Tabs.TabPane key="parts" tab="部件信息">
          <Spin :spinning="loadingParts || loadingPlans">
            <div>
              <Tabs
                v-model:active-key="activePartsTab"
                size="small"
                style="margin-bottom: 16px"
              >
                <Tabs.TabPane key="template" tab="模板部件">
                  <Empty
                    v-if="templateParts.length === 0"
                    description="暂无模板部件信息"
                  />
                  <Table
                    v-else
                    :data-source="templateParts"
                    :columns="[
                      { title: '名称', dataIndex: 'name', key: 'name' },
                      { title: '型号', dataIndex: 'model', key: 'model' },
                      { title: '安装位置', dataIndex: 'slot', key: 'slot' },
                      { title: '数量', dataIndex: 'quantity', key: 'quantity' },
                      {
                        title: '类型',
                        dataIndex: 'component_type',
                        key: 'component_type',
                      },
                      {
                        title: '描述',
                        dataIndex: 'description',
                        key: 'description',
                      },
                    ]"
                    row-key="id"
                    :pagination="{ pageSize: 5 }"
                  />
                </Tabs.TabPane>
                <Tabs.TabPane key="installed" tab="已安装部件">
                  <Empty
                    v-if="deviceParts.length === 0"
                    description="暂无已安装部件信息"
                  />
                  <Table
                    v-else
                    :data-source="deviceParts"
                    :columns="[
                      { title: '名称', dataIndex: 'name', key: 'name' },
                      { title: '型号', dataIndex: 'model', key: 'model' },
                      {
                        title: '序列号',
                        dataIndex: 'sn',
                        key: 'sn',
                      },
                      {
                        title: '状态',
                        dataIndex: 'status',
                        key: 'status',
                        customRender: ({ text }) => {
                          const statusMap: Record<
                            string,
                            { text: string; color: string }
                          > = {
                            normal: { text: '正常', color: 'green' },
                            warning: { text: '警告', color: 'orange' },
                            faulty: { text: '故障', color: 'red' },
                            removed: { text: '已移除', color: 'gray' },
                          };
                          const status =
                            text && typeof text === 'string' && statusMap[text]
                              ? statusMap[text]
                              : { text: String(text), color: 'default' };
                          return h(
                            Tag,
                            { color: status.color },
                            () => status.text,
                          );
                        },
                      },
                      {
                        title: '描述',
                        dataIndex: 'description',
                        key: 'description',
                      },
                      {
                        title: '操作',
                        key: 'operation',
                        width: 220,
                        customRender: ({ record }) => {
                          return h('div', [
                            h(
                              Button,
                              {
                                type: 'link',
                                size: 'small',
                                onClick: () => showReplaceComponent(record),
                                disabled: record.status === 'removed',
                              },
                              () => '替换',
                            ),
                            h(Divider, { type: 'vertical' }),
                            h(
                              Button,
                              {
                                type: 'link',
                                size: 'small',
                                danger: true,
                                onClick: () => showRemoveComponent(record),
                                disabled: record.status === 'removed',
                              },
                              () => '移除',
                            ),
                          ]);
                        },
                      },
                    ]"
                    row-key="id"
                    :pagination="{ pageSize: 5 }"
                  />
                </Tabs.TabPane>
              </Tabs>
            </div>
          </Spin>
        </Tabs.TabPane>

        <!-- 套餐模版信息标签页 -->
        <Tabs.TabPane key="plans" tab="套餐模版信息">
          <Spin :spinning="loadingPlans">
            <Empty
              v-if="devicePlans.length === 0"
              description="暂无套餐模版信息"
            />
            <Table
              v-else
              :data-source="devicePlans"
              :columns="[
                { title: '模版名称', dataIndex: 'name', key: 'name' },
                { title: '模版类型', dataIndex: 'type', key: 'type' },
                {
                  title: '资源配置详情',
                  dataIndex: 'resourceConfig',
                  key: 'resourceConfig',
                  ellipsis: true,
                  width: 300,
                },
                {
                  title: '创建时间',
                  dataIndex: 'created_at',
                  key: 'created_at',
                },
              ]"
              row-key="id"
              :pagination="{ pageSize: 5 }"
            />
          </Spin>
        </Tabs.TabPane>

        <!-- 组件变更历史标签页 -->
        <Tabs.TabPane key="changeHistory" tab="变更历史">
          <Spin :spinning="loadingChangeHistory">
            <Empty
              v-if="deviceChangeHistory.length === 0"
              description="暂无变更历史记录"
            />
            <Table
              v-else
              :data-source="deviceChangeHistory"
              :columns="[
                {
                  title: '变更类型',
                  dataIndex: 'change_type',
                  key: 'change_type',
                  customRender: ({ text }) => {
                    const typeMap: Record<
                      string,
                      { text: string; color: string }
                    > = {
                      install: { text: '安装', color: 'blue' },
                      remove: { text: '移除', color: 'orange' },
                      replace: { text: '替换', color: 'green' },
                      repair: { text: '维修', color: 'purple' },
                    };
                    const type =
                      text && typeof text === 'string' && typeMap[text]
                        ? typeMap[text]
                        : { text: String(text), color: 'default' };
                    return h(Tag, { color: type.color }, () => type.text);
                  },
                },
                { title: '变更原因', dataIndex: 'reason', key: 'reason' },
                {
                  title: '备件信息',
                  dataIndex: 'spare',
                  key: 'spare',
                  customRender: ({ record }) => {
                    return record.spare
                      ? `${record.spare.brand || ''} ${record.spare.model || ''} (SN: ${record.spare.sn || ''})`
                      : '-';
                  },
                },
                {
                  title: '操作人',
                  dataIndex: 'operator_name',
                  key: 'operator_name',
                },
                {
                  title: '变更时间',
                  dataIndex: 'change_time',
                  key: 'change_time',
                },
                { title: '备注', dataIndex: 'remark', key: 'remark' },
              ]"
              row-key="id"
              :pagination="{
                pageSize: changeHistoryPagination.pageSize,
                current: changeHistoryPagination.current,
                total: changeHistoryPagination.total,
                onChange: onChangeHistoryPaginationChange,
              }"
            />
          </Spin>
        </Tabs.TabPane>
        <Tabs.TabPane key="AssetStatusLog" tab="生命周期">
          <Spin :spinning="loadingAssetStatusLogsHistroy">
            <LifeCycle
              v-if="detailData"
              :asset-id="detailData?.id"
              :key="detailData?.id"
            />
            <Empty v-else description="暂无生命周期信息" />
          </Spin>
        </Tabs.TabPane>
      </template>
    </CommonDetail>

    <!-- 替换组件对话框 -->
    <Modal
      v-model:visible="showReplaceComponentModal"
      title="替换组件"
      @ok="handleReplaceComponent"
      @cancel="showReplaceComponentModal = false"
      :confirm-loading="loadingSpares"
    >
      <Form layout="vertical">
        <Form.Item label="选择备件" required>
          <Select
            v-model:value="selectedSpareId"
            placeholder="请选择替换用的备件"
            :loading="loadingSpares"
            :options="availableSpares"
            style="width: 100%"
          />
        </Form.Item>
        <Form.Item label="替换原因" required>
          <Input
            v-model:value="replaceForm.reason"
            placeholder="请输入替换原因"
          />
        </Form.Item>
        <Form.Item label="备注">
          <Input.TextArea
            v-model:value="replaceForm.remarks"
            placeholder="请输入备注信息"
            :rows="4"
          />
        </Form.Item>
      </Form>
    </Modal>

    <!-- 移除组件对话框 -->
    <Modal
      v-model:visible="showRemoveComponentModal"
      title="移除组件"
      @ok="handleRemoveComponent"
      @cancel="showRemoveComponentModal = false"
    >
      <Form layout="vertical">
        <Form.Item label="移除原因" required>
          <Input
            v-model:value="removeForm.reason"
            placeholder="请输入移除原因"
          />
        </Form.Item>
        <Form.Item label="备注">
          <Input.TextArea
            v-model:value="removeForm.remarks"
            placeholder="请输入备注信息"
            :rows="4"
          />
        </Form.Item>
      </Form>
    </Modal>
  </div>
</template>
