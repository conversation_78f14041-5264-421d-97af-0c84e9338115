<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { AZ } from '#/api/core/cmdb/location/az';

import { ref } from 'vue';

import {
  addAZApi,
  deleteAZApi,
  editAZApi,
  getAZTableApi,
} from '#/api/core/cmdb/location/az';
import { getRegionTableApi } from '#/api/core/cmdb/location/region';
import CommonDetail from '#/components/CommonDetail/index.vue';
import CommonList from '#/components/CommonList/index.vue';

const detailRef = ref();
const detailData = ref<AZ | null>(null);
const commonListRef = ref();
const regionOptions = ref<{ label: string; value: number }[]>([]);

// 状态映射
const statusMap: {
  [key: string]: { color: string; text: string };
} = {
  active: { text: '启用', color: 'success' },
  disabled: { text: '禁用', color: 'error' },
};

// 默认数据
const defaultData = {
  name: '',
  regionId: undefined,
  status: 'active',
  description: '',
};

// 获取区域选项
async function fetchRegionOptions() {
  try {
    const res = await getRegionTableApi({ page: 1, pageSize: 1000 });
    regionOptions.value = res.list.map((item) => ({
      label: item.name,
      value: item.id,
    }));
  } catch (error) {
    console.error('获取区域选项失败:', error);
  }
}

// 初始化时获取区域选项
fetchRegionOptions();

// 表格搜索表单
const formOptions: VbenFormProps = {
  collapsed: true,
  collapsedRows: 1,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入可用区名称' },
      fieldName: 'query',
      label: '可用区名称',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择区域',
        options: regionOptions,
        allowClear: true,
      },
      fieldName: 'regionId',
      label: '所属区域',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: true,
};

// 表格配置
const gridOptions: VxeGridProps<AZ> = {
  checkboxConfig: {
    highlight: true,
  },
  exportConfig: {}, // 导出配置
  importConfig: {
    types: ['csv'],
  }, // 导入配置
  columns: [
    { align: 'center', type: 'checkbox', width: 40 },
    { field: 'id', title: 'ID', width: 80 },
    { field: 'name', title: '可用区名称' },
    {
      field: 'region.name',
      title: '所属区域',
    },
    {
      field: 'status',
      title: '状态',
      slots: { default: 'status' }, // 添加自定义插槽
    },
    { field: 'description', title: '描述信息' },
    {
      title: '操作',
      slots: { default: 'operate' },
      width: 200,
    },
  ],
  keepSource: true,
  height: 'auto',
  rowConfig: { isHover: true },
  pagerConfig: { pageSize: 10, pageSizes: [10, 20, 50, 100] },
  toolbarConfig: {
    custom: true,
    refresh: true,
    export: true,
    zoom: true,
    import: true,
    slots: {
      buttons: 'toolbar-buttons',
    },
  },
};

// 新增/编辑表单配置
const editFormOptions: VbenFormProps = {
  showDefaultActions: false,
  schema: [
    {
      fieldName: 'name',
      label: '可用区名称',
      component: 'Input',
      componentProps: { placeholder: '请输入可用区名称' },
      rules: 'required',
    },
    {
      fieldName: 'regionId',
      label: '所属区域',
      component: 'Select',
      componentProps: {
        placeholder: '请选择所属区域',
        options: regionOptions,
      },
      rules: 'required',
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: [
          { label: '启用', value: 'active' },
          { label: '禁用', value: 'disabled' },
        ],
      },
      rules: 'required',
    },
    {
      fieldName: 'description',
      label: '描述信息',
      component: 'Input',
      componentProps: {
        placeholder: '请输入描述信息',
        rows: 4,
        type: 'textarea',
      },
    },
  ],
};

// 详情字段配置
const detailFields = [
  { label: 'ID', field: 'id' },
  { label: '可用区名称', field: 'name' },
  { label: '所属区域', field: 'region.name' },
  { label: '状态', field: 'status', type: 'status' as const },
  { label: '描述信息', field: 'description' },
  { label: '创建时间', field: 'created_at' },
  { label: '更新时间', field: 'updated_at' },
];

// 处理详情事件
function handleDetail(row: AZ) {
  detailData.value = row;
  detailRef.value?.drawerApi.open();
}

// 自定义权限配置
const permissions = {
  add: ['Cmdb:AzInfo:Create'],
  edit: ['Cmdb:AzInfo:Edit'],
  delete: ['Cmdb:AzInfo:Delete'],
};
</script>

<template>
  <div>
    <!-- 使用通用列表组件 -->
    <CommonList
      ref="commonListRef"
      :form-options="formOptions"
      :grid-options="gridOptions"
      :edit-form-options="editFormOptions"
      :api="{
        getList: getAZTableApi,
        add: addAZApi,
        edit: editAZApi,
        delete: deleteAZApi,
      }"
      :default-data="defaultData"
      :status-map="statusMap"
      @detail="handleDetail"
      :permissions="permissions"
      show-add-button
      show-edit-button
      show-delete-button
      show-detail-button
    />

    <!-- 使用通用详情组件 -->
    <CommonDetail
      ref="detailRef"
      :data="detailData"
      title="可用区详情"
      :fields="detailFields"
      :status-map="statusMap"
      show-audit-log
      table-name="azs"
    />
  </div>
</template>
