<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { Cabinet } from '#/api/core/cmdb/location/cabinet';

import { ref } from 'vue';

import { Tag } from 'ant-design-vue';

import {
  addCabinetApi,
  deleteCabinetApi,
  editCabinetApi,
  getCabinetTableApi,
} from '#/api/core/cmdb/location/cabinet';
import { getRoomTableApi } from '#/api/core/cmdb/location/room';
import CommonDetail from '#/components/CommonDetail/index.vue';
import CommonList from '#/components/CommonList/index.vue';

const detailRef = ref();
const detailData = ref<Cabinet | null>(null);
const commonListRef = ref();
const roomOptions = ref<{ label: string; value: number }[]>([]);

// 状态映射
const statusMap: {
  [key: string]: { color: string; text: string };
} = {
  active: { text: '启用', color: 'success' },
  disabled: { text: '禁用', color: 'error' },
};

// 机柜类型选项
const cabinetTypeOptions = [
  { label: 'GPU机柜', value: 'standard' },
  { label: '网络机柜', value: 'network' },
  { label: '高密度机柜', value: 'high-density' },
  { label: '其他', value: 'other' },
];

// 网络环境选项
const networkEnvironmentOptions = [
  { label: '生产环境', value: 'production' },
  { label: '测试环境', value: 'testing' },
  { label: '开发环境', value: 'development' },
  { label: '混合环境', value: 'mixed' },
];

// Bond类型选项
const bondTypeOptions = [
  { label: 'Bond0', value: 'bond0' },
  { label: 'Bond1', value: 'bond1' },
  { label: 'Bond2', value: 'bond2' },
  { label: '无', value: 'none' },
];

// 默认数据
const defaultData = {
  name: '',
  roomID: undefined,
  capacityUnits: 42,
  row: '',
  column: '',
  cabinetType: 'standard',
  networkEnvironment: 'production',
  bondType: 'none',
  totalPower: 0,
  status: 'active',
  description: '',
};

// 获取房间选项
async function fetchRoomOptions() {
  try {
    const res = await getRoomTableApi({ page: 1, pageSize: 1000 });
    roomOptions.value = res.list.map((item) => ({
      label: `${item.name}${item.dataCenter ? ` (${item.dataCenter.name})` : ''}`,
      value: item.id,
    }));
  } catch (error) {
    console.error('获取房间选项失败:', error);
  }
}

// 初始化时获取房间选项
fetchRoomOptions();

// 表格搜索表单
const formOptions: VbenFormProps = {
  collapsed: true,
  collapsedRows: 1,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入机柜名称' },
      fieldName: 'query',
      label: '机柜名称',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择房间',
        options: roomOptions,
        allowClear: true,
      },
      fieldName: 'roomID',
      label: '所属房间',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择机柜类型',
        options: cabinetTypeOptions,
        allowClear: true,
      },
      fieldName: 'cabinetType',
      label: '机柜类型',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择网络环境',
        options: networkEnvironmentOptions,
        allowClear: true,
      },
      fieldName: 'networkEnvironment',
      label: '网络环境',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: true,
};

// 表格配置
const gridOptions: VxeGridProps<Cabinet> = {
  checkboxConfig: {
    highlight: true,
  },
  exportConfig: {}, // 导出配置
  importConfig: {
    types: ['csv'],
  }, // 导入配置
  columns: [
    { align: 'center', type: 'checkbox', width: 40 },
    { field: 'id', title: 'ID', width: 80 },
    { field: 'name', title: '机柜名称' },
    { field: 'room.name', title: '所属房间' },
    { field: 'room.dataCenter.name', title: '所属机房' },
    { field: 'capacityUnits', title: '容量(U)' },
    { field: 'row', title: '机柜行' },
    { field: 'column', title: '机柜列' },
    { field: 'cabinetType', title: '机柜类型' },
    { field: 'networkEnvironment', title: '网络环境' },
    { field: 'bondType', title: 'Bond类型' },
    { field: 'totalPower', title: '总功耗(kW)' },
    {
      field: 'status',
      title: '状态',
      slots: { default: 'status' }, // 添加自定义插槽
    },
    { field: 'description', title: '描述信息' },
    {
      title: '操作',
      slots: { default: 'operate' },
      width: 200,
    },
  ],
  keepSource: true,
  height: 'auto',
  rowConfig: { isHover: true },
  pagerConfig: { pageSize: 10, pageSizes: [10, 20, 50, 100] },
  toolbarConfig: {
    custom: true,
    refresh: true,
    export: true,
    zoom: true,
    import: true,
    slots: {
      buttons: 'toolbar-buttons',
    },
  },
};

// 新增/编辑表单配置
const editFormOptions: VbenFormProps = {
  showDefaultActions: false,
  schema: [
    {
      fieldName: 'name',
      label: '机柜名称',
      component: 'Input',
      componentProps: { placeholder: '请输入机柜名称' },
      rules: 'required',
    },
    {
      fieldName: 'roomID',
      label: '所属房间',
      component: 'Select',
      componentProps: {
        placeholder: '请选择所属房间',
        options: roomOptions,
      },
      rules: 'required',
    },
    {
      fieldName: 'capacityUnits',
      label: '容量(U)',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入容量',
        min: 1,
        max: 100,
      },
      rules: 'required',
    },
    {
      fieldName: 'row',
      label: '机柜行',
      component: 'Input',
      componentProps: {
        placeholder: '请输入机柜行',
      },
    },
    {
      fieldName: 'column',
      label: '机柜列',
      component: 'Input',
      componentProps: {
        placeholder: '请输入机柜列',
      },
    },
    {
      fieldName: 'cabinetType',
      label: '机柜类型',
      component: 'Select',
      componentProps: {
        placeholder: '请选择机柜类型',
        options: cabinetTypeOptions,
      },
    },
    {
      fieldName: 'networkEnvironment',
      label: '网络环境',
      component: 'Select',
      componentProps: {
        placeholder: '请选择网络环境',
        options: networkEnvironmentOptions,
      },
    },
    {
      fieldName: 'bondType',
      label: 'Bond类型',
      component: 'Select',
      componentProps: {
        placeholder: '请选择Bond类型',
        options: bondTypeOptions,
      },
    },
    {
      fieldName: 'totalPower',
      label: '总功耗(kW)',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入总功耗',
        min: 0,
        precision: 2,
      },
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: [
          { label: '启用', value: 'active' },
          { label: '禁用', value: 'disabled' },
        ],
      },
      rules: 'required',
    },
    {
      fieldName: 'description',
      label: '描述信息',
      component: 'Input',
      componentProps: {
        placeholder: '请输入描述信息',
        rows: 4,
        type: 'textarea',
      },
    },
  ],
};

// 详情字段配置
const detailFields = [
  { label: 'ID', field: 'id' },
  { label: '机柜名称', field: 'name' },
  {
    label: '所属房间',
    field: 'room.name',
    format: (value: any) => {
      if (value) return value;
      const data = detailData.value;
      if (data?.roomID) {
        const room = roomOptions.value.find((opt) => opt.value === data.roomID);
        if (room?.label) {
          const parts = room.label.split(' (');
          return parts[0] || '暂无';
        }
      }
      return '暂无';
    },
  },
  {
    label: '所属机房',
    field: 'room.dataCenter.name',
    format: (value: any) => {
      if (value) return value;
      const data = detailData.value;
      if (data?.roomID) {
        const room = roomOptions.value.find((opt) => opt.value === data.roomID);
        if (room?.label && room.label.includes('(')) {
          const match = room.label.match(/\((.*?)\)/);
          if (match && match[1]) {
            return match[1];
          }
        }
      }
      return '暂无';
    },
  },
  {
    label: '所属可用区',
    field: 'room.dataCenter.az.name',
    format: (value: any) => {
      if (value) return value;

      // 从详情数据中获取
      const data = detailData.value;

      // 尝试从room.dataCenter对象中获取
      if (data?.room?.dataCenter?.az?.name) {
        return data.room.dataCenter.az.name;
      }

      return '暂无';
    },
  },
  {
    label: '所属区域',
    field: 'room.dataCenter.az.region.name',
    format: (value: any) => {
      if (value) return value;

      // 从详情数据中获取
      const data = detailData.value;

      // 尝试从room.dataCenter对象中获取
      if (data?.room?.dataCenter?.az?.region?.name) {
        return data.room.dataCenter.az.region.name;
      }

      return '暂无';
    },
  },
  { label: '容量(U)', field: 'capacityUnits' },
  { label: '机柜行', field: 'row' },
  { label: '机柜列', field: 'column' },
  {
    label: '机柜类型',
    field: 'cabinetType',
    format: (value: any) => {
      if (!value) return '暂无';
      const option = cabinetTypeOptions.find((opt) => opt.value === value);
      return option ? option.label : value;
    },
  },
  {
    label: '网络环境',
    field: 'networkEnvironment',
    format: (value: any) => {
      if (!value) return '暂无';
      const option = networkEnvironmentOptions.find(
        (opt) => opt.value === value,
      );
      return option ? option.label : value;
    },
  },
  {
    label: 'Bond类型',
    field: 'bondType',
    format: (value: any) => {
      if (!value) return '暂无';
      const option = bondTypeOptions.find((opt) => opt.value === value);
      return option ? option.label : value;
    },
  },
  { label: '总功耗(kW)', field: 'totalPower' },
  { label: '状态', field: 'status', type: 'status' as const },
  { label: '描述信息', field: 'description' },
  { label: '创建时间', field: 'created_at' },
  { label: '更新时间', field: 'updated_at' },
];

// 处理详情事件
function handleDetail(row: Cabinet) {
  detailData.value = row;
  detailRef.value?.drawerApi.open();
}

// 自定义权限配置
const permissions = {
  add: ['Cmdb:CabinetInfo:Create'],
  edit: ['Cmdb:CabinetInfo:Edit'],
  delete: ['Cmdb:CabinetInfo:Delete'],
  import: ['Cmdb:CabinetInfo:Import'],
};
</script>

<template>
  <div>
    <!-- 使用通用列表组件 -->
    <CommonList
      ref="commonListRef"
      :form-options="formOptions"
      :grid-options="gridOptions"
      :edit-form-options="editFormOptions"
      :api="{
        getList: getCabinetTableApi,
        add: addCabinetApi,
        edit: editCabinetApi,
        delete: deleteCabinetApi,
      }"
      :default-data="defaultData"
      :status-map="statusMap"
      @detail="handleDetail"
      :permissions="permissions"
      show-add-button
      show-edit-button
      show-delete-button
      show-detail-button
      :enable-confirm="true"
      :show-template-button="true"
      import-model-type="cabinet"
    >
      <!-- 自定义状态插槽 -->
      <template #status="{ row }">
        <Tag :color="statusMap[row.status]?.color || 'default'">
          {{ statusMap[row.status]?.text || row.status || '未知状态' }}
        </Tag>
      </template>
    </CommonList>

    <!-- 使用通用详情组件 -->
    <CommonDetail
      ref="detailRef"
      :data="detailData"
      title="机柜详情"
      :fields="detailFields"
      :status-map="statusMap"
      show-audit-log
      table-name="cabinets"
    />
  </div>
</template>
