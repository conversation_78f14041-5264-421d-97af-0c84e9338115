<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type {
  NetworkDevice,
  NetworkDeviceWithDeviceInfo,
} from '#/api/core/cmdb/asset/networkDevice';

import { onMounted, ref, watch } from 'vue';

import { Button, Descriptions, message, Tabs, Tag } from 'ant-design-vue';

import { getProjectsApi } from '#/api/core/cmdb/asset/device';
import {
  createNetworkDeviceWithDeviceApi,
  deleteNetworkDeviceApi,
  getNetworkDeviceDetailApi,
  getNetworkDeviceListApi,
  updateNetworkDeviceWithDeviceApi,
} from '#/api/core/cmdb/asset/networkDevice';
import CommonDetail from '#/components/CommonDetail/index.vue';
import CommonList from '#/components/CommonList/index.vue';

// 导入配置和函数
import {
  assetStatusMap,
  assetTypeOptions,
  bizStatusMap,
  createWrappedApi,
  defaultData,
  defaultTemplateMap,
  fetchCabinetsByRoomId as fetchCabinetsByRoomIdApi,
  fetchRoomOptions as fetchRoomOptionsApi,
  fetchTemplateOptions,
  getAssetTypeColor,
  getAssetTypeLabel,
  getTemplateIdByName,
  getTemplateNameById,
  handleBeforeEdit,
  handleBeforeSubmit,
  hardwareStatusMap,
  roleOptions,
} from './net-device-config';

const detailRef = ref();
const detailData = ref<NetworkDeviceWithDeviceInfo | null>(null);
const commonListRef = ref();
const cabinetOptions = ref<{ label: string; value: number }[]>([]);
const roomOptions = ref<{ label: string; value: number }[]>([]);
const selectedRole = ref('');
const selectedTemplate = ref('');
// 用于存储从API获取的模板选项
const templateOptions = ref<{ id: number; label: string; value: string }[]>([]);
// 角色与模板的关联映射
const roleTemplateMap = ref<Record<string, string>>({});
// 项目选项
const projectOptions = ref<{ label: string; value: string }[]>([]);
// 添加加载状态
const loadingRooms = ref(false);
const loadingCabinets = ref(false);

// 监听角色变化，自动选择套餐模板
watch(selectedRole, (newValue) => {
  if (newValue && roleTemplateMap.value[newValue]) {
    selectedTemplate.value = roleTemplateMap.value[newValue];
  } else if (newValue && defaultTemplateMap[newValue]) {
    selectedTemplate.value = defaultTemplateMap[newValue];
  } else {
    selectedTemplate.value = '';
  }
});

// 初始化函数
async function init() {
  try {
    // 获取模板选项
    const { templateOptions: templates, roleTemplateMap: templateMap } =
      await fetchTemplateOptions(
        message,
        selectedTemplate.value,
        selectedRole.value,
        roleTemplateMap.value,
      );
    templateOptions.value = templates;
    roleTemplateMap.value = templateMap;

    // 初始化时加载项目数据
    await fetchProjects();
  } catch (error) {
    console.error('初始化数据失败:', error);
  }
}

// 在组件挂载时执行初始化
onMounted(init);

// 获取房间选项
async function fetchRooms() {
  if (roomOptions.value.length > 0) return;

  loadingRooms.value = true;
  try {
    const rooms = await fetchRoomOptionsApi();
    roomOptions.value = rooms;
  } catch (error) {
    console.error('获取房间选项失败:', error);
    roomOptions.value = [];
  } finally {
    loadingRooms.value = false;
  }
}

// 根据房间ID获取机柜选项
async function fetchCabinets(roomId: number) {
  if (!roomId) {
    cabinetOptions.value = [];
    return;
  }

  loadingCabinets.value = true;
  try {
    const cabinets = await fetchCabinetsByRoomIdApi(roomId);
    cabinetOptions.value = cabinets;
  } catch (error) {
    console.error('根据房间ID获取机柜列表失败:', error);
    cabinetOptions.value = [];
  } finally {
    loadingCabinets.value = false;
  }
}

// 获取项目选项
async function fetchProjects() {
  try {
    const projectNames = await getProjectsApi();

    // 处理API返回的项目数组格式
    const projects = (projectNames || []).map((name: string) => ({
      label: name,
      value: name,
    }));

    projectOptions.value = projects;
  } catch (error) {
    console.error('获取项目数据失败:', error);
    message.error('获取项目数据失败');
  }
}

// 初始化编辑表单机柜数据的函数
async function initEditFormCabinets(row: any) {
  if (row && row.roomID) {
    // 先加载房间选项
    if (roomOptions.value.length === 0) {
      await fetchRooms();
    }

    // 记录当前房间ID和机柜ID
    const roomId = row.roomID;
    const cabinetId = row.cabinetID;

    // 根据房间ID加载机柜选项
    await fetchCabinets(roomId);

    // 检查加载后的机柜选项中是否包含当前机柜
    if (cabinetId) {
      const found = cabinetOptions.value.find((opt) => opt.value === cabinetId);
      if (!found) {
        console.warn(`警告: 未找到ID为${cabinetId}的机柜，可能数据不一致`);
      }
    }
  }
}

// 表单打开时刷新数据
async function handleFormOpen(formData?: any) {
  // formData存在表示编辑模式，不存在表示新增模式
  if (formData) {
    // 编辑模式，初始化机柜选项
    await initEditFormCabinets(formData);
  } else {
    // 只在新增模式下刷新数据
    try {
      // 加载房间选项
      await fetchRooms();
      // 刷新项目数据
      await fetchProjects();
    } catch (error) {
      console.error('刷新表单数据失败:', error);
    }
  }
}

// 表格搜索表单
const formOptions: VbenFormProps = {
  collapsed: true,
  collapsedRows: 2,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入SN/型号/品牌/IP' },
      fieldName: 'query',
      label: '关键词',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择设备角色',
        options: roleOptions,
        allowClear: true,
      },
      fieldName: 'role',
      label: '设备角色',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择资产类型',
        options: assetTypeOptions,
        allowClear: true,
      },
      fieldName: 'assetType',
      label: '资产类型',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择资产状态',
        options: Object.entries(assetStatusMap).map(([value, item]) => ({
          label: item.text,
          value,
        })),
        allowClear: true,
      },
      fieldName: 'assetStatus',
      label: '资产状态',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择业务状态',
        options: Object.entries(bizStatusMap).map(([value, item]) => ({
          label: item.text,
          value,
        })),
        allowClear: true,
      },
      fieldName: 'bizStatus',
      label: '业务状态',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入项目名称' },
      fieldName: 'project',
      label: '项目',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入IP地址' },
      fieldName: 'ip',
      label: 'IP地址',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: true,
};

// 表格配置
const gridOptions: VxeGridProps<NetworkDevice> = {
  checkboxConfig: {
    highlight: true,
  },
  exportConfig: {}, // 导出配置
  importConfig: {
    types: ['csv'],
    remote: true,
  }, // 导入配置
  columns: [
    { align: 'center', fixed: 'left', type: 'checkbox', width: 40 },
    { field: 'id', title: 'ID', width: 80, fixed: 'left' },
    { field: 'sn', title: '资产SN', fixed: 'left', width: 140 },
    { field: 'role', title: '设备角色', width: 120 },
    {
      field: 'assetType',
      title: '资产类型',
      slots: { default: 'assetType' },
      width: 100,
    },
    {
      field: 'assetStatus',
      title: '资产状态',
      slots: { default: 'assetStatus' },
      width: 100,
    },
    {
      field: 'bizStatus',
      title: '业务状态',
      slots: { default: 'bizStatus' },
      width: 100,
    },
    {
      field: 'dataCenterName',
      title: '数据中心',
      width: 120,
    },
    {
      field: 'project',
      title: '项目',
      width: 140,
    },
    {
      field: 'roomName',
      title: '机房',
      width: 120,
    },
    {
      field: 'cabinetName',
      title: '机柜',
      width: 100,
    },
    { field: 'rackPosition', title: '机架位', width: 80 },
    { field: 'managementAddress', title: '管理IP', width: 130 },
    { field: 'loopbackAddress', title: 'Loopback IP', width: 130 },
    { field: 'brand', title: '厂商', width: 120 },
    { field: 'model', title: '型号', width: 140 },
    {
      field: 'hardwareStatus',
      title: '硬件状态',
      slots: { default: 'hardwareStatus' },
      width: 100,
    },
    {
      field: 'stackSupport',
      title: '支持堆叠',
      width: 100,
      slots: { default: 'stackSupport' },
    },
    { field: 'ports', title: '端口数', width: 80 },
    { field: 'portSpeed', title: '端口速率', width: 100 },
    { field: 'layer', title: '网络层级', width: 100 },
    { field: 'rackingTime', title: '上架时间', width: 120 },
    { field: 'deliveryTime', title: '交付时间', width: 120 },
    { field: 'purchaseDate', title: '购买时间', width: 120 },
    { field: 'warrantyExpire', title: '过保时间', width: 120 },
    { field: 'remark', title: '备注', width: 200 },
    {
      title: '操作',
      slots: { default: 'operate' },
      width: 200,
      fixed: 'right',
    },
  ],
  keepSource: true,
  height: 'auto',
  rowConfig: { isHover: true },
  pagerConfig: { pageSize: 10, pageSizes: [10, 20, 50, 100] },
  toolbarConfig: {
    custom: true,
    refresh: true,
    export: true,
    zoom: true,
    import: true,
    slots: {
      buttons: 'toolbar-buttons',
    },
  },
};

// 新增/编辑表单配置
const editFormOptions: VbenFormProps = {
  showDefaultActions: false,
  wrapperClass: 'grid grid-cols-2 gap-x-4 gap-y-2',
  schema: [
    // 基本信息标题
    {
      component: 'Divider',
      label: '基本信息',
      fieldName: 'basic_info_title',
      wrapperClass: 'col-span-2 !mt-6 !mb-4',
      formItemClass: 'col-span-2',
      componentProps: {
        orientation: 'left',
        style: 'font-weight: bold; font-size: 16px;',
      },
    },
    // 基本信息字段
    {
      fieldName: 'assetType',
      label: '资产类型',
      component: 'Select',
      componentProps: {
        placeholder: '请选择资产类型',
        options: assetTypeOptions,
      },
      rules: 'required',
    },
    {
      fieldName: 'sn',
      label: '资产SN',
      component: 'Input',
      componentProps: { placeholder: '请输入资产SN' },
      rules: 'required',
    },
    {
      fieldName: 'brand',
      label: '厂商',
      component: 'Input',
      componentProps: { placeholder: '请输入厂商' },
    },
    {
      fieldName: 'model',
      label: '型号',
      component: 'Input',
      componentProps: { placeholder: '请输入型号' },
    },

    // 网络设备特性标题
    {
      component: 'Divider',
      label: '网络设备特性',
      fieldName: 'device_feature_title',
      wrapperClass: 'col-span-2 !mt-8 !mb-4',
      formItemClass: 'col-span-2',
      componentProps: {
        orientation: 'left',
        style: 'font-weight: bold; font-size: 16px;',
      },
    },
    // 网络设备特性字段
    {
      fieldName: 'role',
      label: '设备角色',
      component: 'Select',
      componentProps: {
        placeholder: '请选择设备角色',
        style: { width: '60%' },
        options: roleOptions,
        onChange: async (value: string) => {
          selectedRole.value = value;

          // 当角色改变时，根据映射自动选择套餐模板
          if (value && roleTemplateMap.value[value]) {
            const roleMappedTemplate = roleTemplateMap.value[value];
            selectedTemplate.value = roleMappedTemplate;

            // 查找模板ID
            const foundTemplate = templateOptions.value.find(
              (t) => t.value === roleMappedTemplate,
            );
            if (foundTemplate && foundTemplate.id) {
              // 已找到模板ID，确保当前选中的ID正确
              selectedTemplate.value = roleMappedTemplate;
            } else {
              // 如果在本地未找到，尝试从API获取
              try {
                const templateId = await getTemplateIdByName(
                  roleMappedTemplate,
                  templateOptions.value,
                  message,
                );
                if (templateId) {
                  // 更新当前选择的模板
                  selectedTemplate.value = roleMappedTemplate;
                }
              } catch {
                // 获取模板ID失败，清空选择
                selectedTemplate.value = '';
              }
            }
          } else {
            selectedTemplate.value = '';
          }
        },
      },
      rules: 'required',
    },
    {
      fieldName: 'templateName',
      label: '套餐模板',
      component: 'Select',
      componentProps: {
        placeholder: '请选择套餐模板',
        style: { width: '60%' },
        options: templateOptions,
        modelValue: selectedTemplate,
        'onUpdate:modelValue': (value: string) => {
          selectedTemplate.value = value;
        },
        showSearch: true,
        allowClear: true,
        mode: 'combobox',
        filterOption: (input: string, option: any) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
      },
    },
    {
      fieldName: 'firmwareVersion',
      label: '固件版本',
      component: 'Input',
      componentProps: { placeholder: '请输入固件版本' },
    },

    // 网络配置标题
    {
      component: 'Divider',
      label: '网络配置',
      fieldName: 'network_config_title',
      wrapperClass: 'col-span-2 !mt-8 !mb-4',
      formItemClass: 'col-span-2',
      componentProps: {
        orientation: 'left',
        style: 'font-weight: bold; font-size: 16px;',
      },
    },
    // 网络配置字段
    {
      fieldName: 'managementAddress',
      label: '管理地址',
      component: 'Input',
      componentProps: { placeholder: '请输入管理IP地址' },
    },
    {
      fieldName: 'loopbackAddress',
      label: 'Loopback地址',
      component: 'Input',
      componentProps: { placeholder: '请输入Loopback地址' },
    },
    {
      fieldName: 'ports',
      label: '端口数量',
      component: 'InputNumber',
      componentProps: { placeholder: '请输入端口数量' },
    },
    {
      fieldName: 'portSpeed',
      label: '端口速率',
      component: 'Input',
      componentProps: { placeholder: '请输入端口速率，如10G/25G/100G' },
    },
    {
      fieldName: 'layer',
      label: '网络层级',
      component: 'Select',
      componentProps: {
        placeholder: '请选择网络层级',
        options: [
          { label: '二层', value: 2 },
          { label: '三层', value: 3 },
        ],
      },
    },
    {
      fieldName: 'routingProtocols',
      label: '路由协议',
      component: 'Input',
      componentProps: {
        placeholder: '请输入支持的路由协议，如OSPF/BGP',
      },
    },

    // 堆叠信息标题
    {
      component: 'Divider',
      label: '堆叠信息',
      fieldName: 'stack_info_title',
      wrapperClass: 'col-span-2 !mt-8 !mb-4',
      formItemClass: 'col-span-2',
      componentProps: {
        orientation: 'left',
        style: 'font-weight: bold; font-size: 16px;',
      },
    },
    // 堆叠信息字段
    {
      fieldName: 'stackSupport',
      label: '支持堆叠',
      component: 'Switch',
      componentProps: {
        checkedChildren: '是',
        unCheckedChildren: '否',
      },
    },
    {
      fieldName: 'stackID',
      label: '堆叠ID',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入堆叠ID',
        style: { width: '60%' },
      },
    },
    {
      fieldName: 'stackRole',
      label: '堆叠角色',
      component: 'Select',
      componentProps: {
        placeholder: '请选择堆叠角色',
        style: { width: '60%' },
        options: [
          { label: '主机', value: 'master' },
          { label: '备机', value: 'standby' },
          { label: '成员', value: 'member' },
        ],
      },
    },

    // 状态信息标题
    {
      component: 'Divider',
      label: '状态信息',
      fieldName: 'status_info_title',
      wrapperClass: 'col-span-2 !mt-8 !mb-4',
      formItemClass: 'col-span-2',
      componentProps: {
        orientation: 'left',
        style: 'font-weight: bold; font-size: 16px;',
      },
    },
    // 状态信息字段
    {
      fieldName: 'assetStatus',
      label: '资产状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择资产状态',
        options: Object.entries(assetStatusMap).map(([value, item]) => ({
          label: item.text,
          value,
        })),
      },
      rules: 'required',
    },
    {
      fieldName: 'hardwareStatus',
      label: '硬件状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择硬件状态',
        options: Object.entries(hardwareStatusMap).map(([value, item]) => ({
          label: item.text,
          value,
        })),
      },
      rules: 'required',
    },
    {
      fieldName: 'bizStatus',
      label: '业务状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择业务状态',
        options: Object.entries(bizStatusMap).map(([value, item]) => ({
          label: item.text,
          value,
        })),
      },
    },

    // 位置信息标题
    {
      component: 'Divider',
      label: '位置信息',
      fieldName: 'location_info_title',
      wrapperClass: 'col-span-2 !mt-8 !mb-4',
      formItemClass: 'col-span-2',
      componentProps: {
        orientation: 'left',
        style: 'font-weight: bold; font-size: 16px;',
      },
    },
    // 位置信息字段
    {
      fieldName: 'roomID',
      label: '机房',
      component: 'Select',
      componentProps: {
        placeholder: '请选择机房',
        options: roomOptions,
        allowClear: true,
        showSearch: true,
        filterOption: (input: string, option: any) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
        style: { width: '100%' },
        loading: loadingRooms,
        onChange: (value: number) => {
          // 选择机房后清空并重新加载机柜选项
          cabinetOptions.value = [];
          if (value) {
            fetchCabinets(value);
          }
        },
      },
    },
    {
      fieldName: 'cabinetID',
      label: '机柜',
      component: 'Select',
      componentProps: {
        placeholder: '请选择机柜',
        options: cabinetOptions,
        allowClear: true,
        showSearch: true,
        filterOption: (input: string, option: any) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
        style: { width: '100%' },
        loading: loadingCabinets,
      },
    },
    {
      fieldName: 'rackPosition',
      label: '机架位',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入机架位',
        min: 0,
        style: { width: '100%' },
      },
    },
    {
      fieldName: 'project',
      label: '项目',
      component: 'Select',
      componentProps: {
        placeholder: '请选择项目',
        options: projectOptions,
        allowClear: true,
        showSearch: true,
        filterOption: (input: string, option: any) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
        style: { width: '100%' },
      },
    },

    // 时间信息标题
    {
      component: 'Divider',
      label: '时间信息',
      fieldName: 'time_info_title',
      wrapperClass: 'col-span-2 !mt-8 !mb-4',
      formItemClass: 'col-span-2',
      componentProps: {
        orientation: 'left',
        style: 'font-weight: bold; font-size: 16px;',
      },
    },
    // 时间信息字段
    {
      fieldName: 'purchaseDate',
      label: '购买时间',
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择购买时间',
        valueFormat: 'YYYY-MM-DD',
        style: { width: '100%' },
      },
    },
    {
      fieldName: 'warrantyExpire',
      label: '过保时间',
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择过保时间',
        valueFormat: 'YYYY-MM-DD',
        style: { width: '100%' },
      },
    },
    {
      fieldName: 'rackingTime',
      label: '上架时间',
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择上架时间',
        valueFormat: 'YYYY-MM-DD',
        style: { width: '100%' },
      },
    },
    {
      fieldName: 'deliveryTime',
      label: '交付时间',
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择交付时间',
        valueFormat: 'YYYY-MM-DD',
        style: { width: '100%' },
      },
    },

    // 财务信息标题
    {
      component: 'Divider',
      label: '财务信息',
      fieldName: 'financial_info_title',
      wrapperClass: 'col-span-2 !mt-8 !mb-4',
      formItemClass: 'col-span-2',
      componentProps: {
        orientation: 'left',
        style: 'font-weight: bold; font-size: 16px;',
      },
    },
    // 财务信息字段
    {
      fieldName: 'purchaseOrder',
      label: '采购合同',
      component: 'Input',
      componentProps: { placeholder: '请输入采购合同' },
    },
    {
      fieldName: 'price',
      label: '金额',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入金额',
        min: 0,
        precision: 2,
        style: { width: '100%' },
      },
    },
    {
      fieldName: 'residualValue',
      label: '残值',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入残值',
        min: 0,
        precision: 2,
        style: { width: '100%' },
      },
    },

    // 其他信息标题
    {
      component: 'Divider',
      label: '其他信息',
      fieldName: 'other_info_title',
      wrapperClass: 'col-span-2 !mt-8 !mb-4',
      formItemClass: 'col-span-2',
      componentProps: {
        orientation: 'left',
        style: 'font-weight: bold; font-size: 16px;',
      },
    },
    // 其他信息字段
    {
      fieldName: 'remark',
      label: '备注',
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入备注',
        rows: 4,
      },
      wrapperClass: 'col-span-2',
    },
  ],
};

// 详情字段配置 - 分组显示
const detailFields = [
  // 基本信息组
  { label: 'ID', field: 'id', group: '基本信息' },
  { label: '资产SN', field: 'sn', group: '基本信息' },
  { label: '厂商', field: 'brand', group: '基本信息' },
  { label: '型号', field: 'model', group: '基本信息' },
  {
    label: '资产类型',
    field: 'assetType',
    group: '基本信息',
    format: (value: any) => {
      const option = assetTypeOptions.find(
        (opt: { label: string; value: string }) => opt.value === value,
      );
      return option ? option.label : value || '未知';
    },
  },

  // 网络设备信息组
  { label: '设备角色', field: 'role', group: '网络设备信息' },
  { label: '套餐模板', field: 'templateName', group: '网络设备信息' },
  { label: '固件版本', field: 'firmwareVersion', group: '网络设备信息' },
  { label: '管理地址', field: 'managementAddress', group: '网络设备信息' },
  { label: 'Loopback地址', field: 'loopbackAddress', group: '网络设备信息' },
  { label: '端口数量', field: 'ports', group: '网络设备信息' },
  { label: '端口速率', field: 'portSpeed', group: '网络设备信息' },
  {
    label: '支持堆叠',
    field: 'stackSupport',
    group: '网络设备信息',
    format: (value: any) => (value ? '是' : '否'),
  },
  { label: '堆叠ID', field: 'stackID', group: '网络设备信息' },
  { label: '堆叠角色', field: 'stackRole', group: '网络设备信息' },
  { label: '网络层级', field: 'layer', group: '网络设备信息' },
  { label: '路由协议', field: 'routingProtocols', group: '网络设备信息' },

  // 状态信息组
  {
    label: '资产状态',
    field: 'assetStatus',
    type: 'status' as const,
    statusMap: assetStatusMap,
    group: '状态信息',
  },
  {
    label: '硬件状态',
    field: 'hardwareStatus',
    type: 'status' as const,
    statusMap: hardwareStatusMap,
    group: '状态信息',
  },
  {
    label: '业务状态',
    field: 'bizStatus',
    type: 'status' as const,
    statusMap: bizStatusMap,
    group: '状态信息',
  },
  {
    label: '资源状态',
    field: 'resStatus',
    type: 'status' as const,
    statusMap: {
      allocated: { text: '已分配', color: 'green' },
      unallocated: { text: '未分配', color: 'orange' },
    },
    group: '状态信息',
  },

  // 位置信息组
  {
    label: '区域',
    field: 'regionName',
    format: (value: any) => value || '暂无',
    group: '位置信息',
  },
  {
    label: '可用区',
    field: 'azName',
    format: (value: any) => value || '暂无',
    group: '位置信息',
  },
  {
    label: '机房',
    field: 'roomName',
    format: (value: any) => value || '暂无',
    group: '位置信息',
  },
  {
    label: '机柜',
    field: 'cabinetName',
    format: (value: any) => value || '暂无',
    group: '位置信息',
  },
  {
    label: '机架位',
    field: 'rackPosition',
    format: (value: any) => value || '暂无',
    group: '位置信息',
  },
  {
    label: '机柜行',
    field: 'cabinetRow',
    format: (value: any) => value || '暂无',
    group: '位置信息',
  },
  {
    label: '机柜列',
    field: 'cabinetColumn',
    format: (value: any) => value || '暂无',
    group: '位置信息',
  },

  // 时间信息组
  { label: '上架时间', field: 'rackingTime', group: '时间信息' },
  { label: '交付时间', field: 'deliveryTime', group: '时间信息' },
  { label: '购买时间', field: 'purchaseDate', group: '时间信息' },
  { label: '过保时间', field: 'warrantyExpire', group: '时间信息' },
  { label: '创建时间', field: 'createdAt', group: '时间信息' },
  { label: '更新时间', field: 'updatedAt', group: '时间信息' },

  // 财务信息组
  { label: '采购单号', field: 'purchaseOrder', group: '财务信息' },
  { label: '采购价格', field: 'price', group: '财务信息' },
  { label: '残值', field: 'residualValue', group: '财务信息' },

  // 其他信息组
  { label: '备注', field: 'remark', group: '其他信息' },
  { label: '资源备注', field: 'resourceRemark', group: '其他信息' },
];

// 处理详情事件
async function handleDetail(row: NetworkDevice) {
  try {
    const res = await getNetworkDeviceDetailApi(row.id);
    detailData.value = res;
    detailRef.value?.drawerApi.open();
  } catch {
    message.error('获取网络设备详情失败');
  }
}

// 表单重置处理 - 用于表单取消或关闭时清理状态
function handleFormReset() {
  // 清空选择的角色和模板
  selectedRole.value = '';
  selectedTemplate.value = '';
}

// 自定义权限配置
const permissions = {
  add: ['Cmdb:NetDeviceInfo:Create'],
  edit: ['Cmdb:NetDeviceInfo:Edit'],
  delete: ['Cmdb:NetDeviceInfo:Delete'],
  import: ['Cmdb:NetDeviceInfo:Import'],
};

// 刷新模板选项的函数
async function refreshTemplateOptions() {
  const { templateOptions: templates, roleTemplateMap: templateMap } =
    await fetchTemplateOptions(
      message,
      selectedTemplate.value,
      selectedRole.value,
      roleTemplateMap.value,
    );
  templateOptions.value = templates;
  roleTemplateMap.value = templateMap;
  return true;
}

// API包装
const wrappedApi = createWrappedApi(
  message,
  templateOptions.value,
  getNetworkDeviceDetailApi,
  createNetworkDeviceWithDeviceApi,
  updateNetworkDeviceWithDeviceApi,
  deleteNetworkDeviceApi,
  getNetworkDeviceListApi,
  refreshTemplateOptions,
);

// 封装beforeSubmit钩子
function beforeSubmitWrapper(formData: any) {
  return handleBeforeSubmit(
    formData,
    message,
    getTemplateIdByName,
    templateOptions.value,
  );
}

// 封装beforeEdit钩子
function beforeEditWrapper(record: any) {
  return handleBeforeEdit(record, message, getNetworkDeviceDetailApi);
}

// 处理编辑事件
function handleEditNetworkDevice(row: any) {
  try {
    // 首先加载选项数据
    handleFormOpen().then(() => {
      // 获取详细信息
      getNetworkDeviceDetailApi(row.id)
        .then((detail) => {
          const detailData = detail as any;

          // 角色处理
          const role = detailData?.role || row?.role || '';

          // 模板名称处理
          let templateName = '';
          if (detailData?.templateName) {
            templateName = detailData.templateName;
          } else if (row?.templateName) {
            templateName = row.templateName;
          } else if (role && defaultTemplateMap[role]) {
            templateName = defaultTemplateMap[role];
          }

          // 设置角色和模板选项
          selectedRole.value = role;
          selectedTemplate.value = templateName;

          // 构建展平的数据对象供表单使用
          const flattenedData = {
            // 网络设备基本信息
            id: detailData?.id || row?.id,
            deviceID: detailData?.deviceID || row?.deviceID,
            name: detailData?.name || row?.name || '',
            role,
            description: detailData?.description || row?.description || '',
            managementAddress:
              detailData?.managementAddress || row?.managementAddress || '',
            loopbackAddress:
              detailData?.loopbackAddress || row?.loopbackAddress || '',
            firmwareVersion:
              detailData?.firmwareVersion || row?.firmwareVersion || '',
            maintenancePackage:
              detailData?.maintenancePackage || row?.maintenancePackage || '',
            rackingTime: detailData?.rackingTime || row?.rackingTime,
            deliveryTime: detailData?.deliveryTime || row?.deliveryTime,

            // 模板相关信息
            templateName,
            templateID: detailData?.templateID || row?.templateID,

            // 设备基本信息
            sn: detailData?.sn || row?.sn || '',
            brand: detailData?.brand || row?.brand || '',
            model: detailData?.model || row?.model || '',
            assetType: detailData?.assetType || row?.assetType || 'network',
            assetStatus:
              detailData?.assetStatus || row?.assetStatus || 'in_use',
            hardwareStatus:
              detailData?.hardwareStatus || row?.hardwareStatus || 'normal',
            purchaseOrder:
              detailData?.purchaseOrder || row?.purchaseOrder || '',
            purchaseDate: detailData?.purchaseDate || row?.purchaseDate,
            warrantyExpire: detailData?.warrantyExpire || row?.warrantyExpire,
            price: detailData?.price || row?.price,
            residualValue: detailData?.residualValue || row?.residualValue,
            remark: detailData?.remark || row?.remark || '',

            // 网络设备特有字段
            bizStatus: detailData?.bizStatus || row?.bizStatus || 'active',
            ports: detailData?.ports || row?.ports,
            portSpeed: detailData?.portSpeed || row?.portSpeed || '',
            stackSupport:
              detailData?.stackSupport || row?.stackSupport || false,
            stackID: detailData?.stackID || row?.stackID,
            stackRole: detailData?.stackRole || row?.stackRole || '',
            layer: detailData?.layer || row?.layer,
            routingProtocols:
              detailData?.routingProtocols || row?.routingProtocols || '',

            // 机柜位置信息
            roomID: detailData?.roomID || row?.roomID,
            cabinetID: detailData?.cabinetID || row?.cabinetID,
            rackPosition: detailData?.rackPosition || row?.rackPosition,
            project: detailData?.project || row?.project || '',
          };

          // 直接调用CommonList的编辑方法
          if (commonListRef.value) {
            commonListRef.value.editEvent(flattenedData);
          } else {
            message.error('表单实例未找到，无法打开编辑窗口');
          }
        })
        .catch((error) => {
          console.error('获取网络设备详情失败:', error);
          message.error('获取网络设备详情失败，使用基本信息');

          // 获取详情失败，使用行数据处理
          if (commonListRef.value) {
            commonListRef.value.editEvent(row);
          }
        });
    });
  } catch (error) {
    console.error('编辑网络设备处理错误:', error);
    message.error('编辑处理错误，请刷新页面重试');
  }

  return true; // 表示已处理
}

// 封装getTemplateNameById函数，传递正确的值
function getTemplateNameForId(id: null | number | undefined) {
  return getTemplateNameById(id, templateOptions.value);
}
</script>

<template>
  <div>
    <!-- 使用通用列表组件 -->
    <CommonList
      ref="commonListRef"
      :form-options="formOptions"
      :grid-options="gridOptions"
      :edit-form-options="editFormOptions"
      :api="wrappedApi"
      :default-data="defaultData"
      @detail="handleDetail"
      @edit="initEditFormCabinets"
      @form-cancel="handleFormReset"
      @form-open="handleFormOpen"
      :permissions="permissions"
      :before-edit-hook="beforeEditWrapper"
      :before-submit-hook="beforeSubmitWrapper"
      show-add-button
      show-edit-button
      show-delete-button
      show-detail-button
      drawer-class="w-1/2"
      import-model-type="network-device"
      :enable-confirm="true"
      :show-template-button="true"
    >
      <!-- 资产类型插槽 -->
      <template #assetType="{ row }">
        <Tag :color="getAssetTypeColor(row.assetType)">
          {{ getAssetTypeLabel(row.assetType) }}
        </Tag>
      </template>

      <!-- 资产状态插槽 -->
      <template #assetStatus="{ row }">
        <Tag :color="assetStatusMap[row.assetStatus]?.color || 'default'">
          {{
            assetStatusMap[row.assetStatus]?.text ||
            row.assetStatus ||
            '未知状态'
          }}
        </Tag>
      </template>

      <!-- 业务状态插槽 -->
      <template #bizStatus="{ row }">
        <Tag
          v-if="row.bizStatus"
          :color="bizStatusMap[row.bizStatus]?.color || 'default'"
        >
          {{ bizStatusMap[row.bizStatus]?.text || row.bizStatus || '未知状态' }}
        </Tag>
        <Tag v-else color="green"> 正常 </Tag>
      </template>

      <!-- 硬件状态插槽 -->
      <template #hardwareStatus="{ row }">
        <Tag :color="hardwareStatusMap[row.hardwareStatus]?.color || 'default'">
          {{
            hardwareStatusMap[row.hardwareStatus]?.text ||
            row.hardwareStatus ||
            '未知状态'
          }}
        </Tag>
      </template>

      <!-- 堆叠支持插槽 -->
      <template #stackSupport="{ row }">
        <Tag :color="row.stackSupport ? 'green' : 'gray'">
          {{ row.stackSupport ? '支持' : '不支持' }}
        </Tag>
      </template>

      <!-- 模板名称插槽 -->
      <template #templateName="{ row }">
        <span v-if="row.templateName">{{ row.templateName }}</span>
        <span v-else-if="row.templateID">
          {{ getTemplateNameForId(row.templateID) }}
        </span>
        <span v-else>-</span>
      </template>

      <!-- 操作列插槽 -->
      <template #operate="{ row }">
        <div class="space-x-1">
          <Button
            type="link"
            size="small"
            @click="handleEditNetworkDevice(row)"
          >
            编辑
          </Button>
          <Button type="link" size="small" @click="handleDetail(row)">
            详情
          </Button>
        </div>
      </template>
    </CommonList>

    <!-- 使用通用详情组件 -->
    <CommonDetail
      ref="detailRef"
      :data="detailData"
      title="网络设备详情"
      :fields="detailFields"
      :status-map="assetStatusMap"
      show-audit-log
      table-name="network_devices"
      show-group
      class="w-1/2"
    >
      <!-- 添加网络设备配置标签页 -->
      <template #tabs>
        <!-- 配置信息标签页 -->
        <Tabs.TabPane key="config" tab="详细配置">
          <Descriptions bordered :column="1">
            <Descriptions.Item label="资产类型">
              {{
                detailData?.assetType === 'network'
                  ? '网络设备'
                  : detailData?.assetType === 'firewall'
                    ? '防火墙'
                    : detailData?.assetType === 'router'
                      ? '路由器'
                      : detailData?.assetType === 'loadbalancer'
                        ? '负载均衡'
                        : detailData?.assetType || '-'
              }}
            </Descriptions.Item>
            <Descriptions.Item label="设备角色">
              {{ detailData?.role || '-' }}
            </Descriptions.Item>
            <Descriptions.Item label="套餐模板">
              {{ detailData?.templateName || '-' }}
            </Descriptions.Item>
            <Descriptions.Item label="固件版本">
              {{ detailData?.firmwareVersion || '-' }}
            </Descriptions.Item>
            <Descriptions.Item label="管理地址">
              {{ detailData?.managementAddress || '-' }}
            </Descriptions.Item>
            <Descriptions.Item label="Loopback地址">
              {{ detailData?.loopbackAddress || '-' }}
            </Descriptions.Item>
            <Descriptions.Item label="端口数量">
              {{ detailData && 'ports' in detailData ? detailData.ports : '-' }}
            </Descriptions.Item>
            <Descriptions.Item label="端口速率">
              {{
                detailData && 'portSpeed' in detailData
                  ? detailData.portSpeed
                  : '-'
              }}
            </Descriptions.Item>
            <Descriptions.Item label="支持堆叠">
              <Tag
                :color="
                  detailData &&
                  'stackSupport' in detailData &&
                  detailData.stackSupport
                    ? 'green'
                    : 'gray'
                "
              >
                {{
                  detailData &&
                  'stackSupport' in detailData &&
                  detailData.stackSupport
                    ? '是'
                    : '否'
                }}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item
              v-if="
                detailData &&
                'stackSupport' in detailData &&
                detailData.stackSupport
              "
              label="堆叠ID"
            >
              {{
                detailData && 'stackID' in detailData ? detailData.stackID : '-'
              }}
            </Descriptions.Item>
            <Descriptions.Item
              v-if="
                detailData &&
                'stackSupport' in detailData &&
                detailData.stackSupport
              "
              label="堆叠角色"
            >
              {{
                detailData && 'stackRole' in detailData
                  ? detailData.stackRole
                  : '-'
              }}
            </Descriptions.Item>
            <Descriptions.Item label="网络层级">
              {{
                detailData && 'layer' in detailData
                  ? detailData.layer === 2
                    ? '二层'
                    : detailData.layer === 3
                      ? '三层'
                      : '-'
                  : '-'
              }}
            </Descriptions.Item>
            <Descriptions.Item label="路由协议">
              {{
                detailData && 'routingProtocols' in detailData
                  ? detailData.routingProtocols
                  : '-'
              }}
            </Descriptions.Item>
            <Descriptions.Item label="业务状态">
              <Tag
                v-if="detailData?.bizStatus"
                :color="bizStatusMap[detailData.bizStatus]?.color || 'default'"
              >
                {{
                  bizStatusMap[detailData.bizStatus]?.text ||
                  detailData.bizStatus
                }}
              </Tag>
              <span v-else>-</span>
            </Descriptions.Item>
          </Descriptions>
        </Tabs.TabPane>
      </template>
    </CommonDetail>
  </div>
</template>
