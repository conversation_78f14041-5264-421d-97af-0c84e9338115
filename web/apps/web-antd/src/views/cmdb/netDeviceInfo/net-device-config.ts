// 网络设备角色选项
export const roleOptions = [
  { label: '核心交换机(S0)', value: 'S0' },
  { label: '核心交换机(S1)', value: 'S1' },
  { label: '汇聚交换机(GS0)', value: 'GS0' },
  { label: '汇聚交换机(GS1)', value: 'GS1' },
  { label: '路由网关(RGW)', value: 'RGW' },
  { label: '路由交换机(RSW)', value: 'RSW' },
  { label: '直连存储交换机(DAS)', value: 'DAS' },
  { label: '管理交换机(MS0)', value: 'MS0' },
  { label: '管理交换机(MS1)', value: 'MS1' },
  { label: '边界交换机(BS0)', value: 'BS0' },
  { label: '边界交换机(BS1)', value: 'BS1' },
  { label: '数据中心互联(DCI)', value: 'DCI' },
];

// 资产类型选项
export const assetTypeOptions = [
  { label: '网络设备', value: 'network' },
  { label: '交换机', value: 'switch' },
  { label: '防火墙', value: 'firewall' },
  { label: '路由器', value: 'router' },
  { label: '负载均衡', value: 'loadbalancer' },
  { label: '其他', value: 'other' },
];

// 初始的套餐模板映射
export const defaultTemplateMap: { [key: string]: string } = {
  S0: '8Q-TOR-T3-B',
  S1: '32Q-TOR-T3-B',
  GS0: 'COREB-TH5',
  GS1: 'COREB-TH5',
  RGW: 'TOR-P4-6.4T',
  RSW: 'COREB-TH3',
  DAS: 'COREC-Macsec-2',
  MS0: 'TOR-1G',
  MS1: 'TOR-1G',
  BS0: 'TOR-1G',
  BS1: 'TOR-1G',
  DCI: 'COREC-Macsec-1',
};

// 资产状态映射
export const assetStatusMap: {
  [key: string]: { color: string; text: string };
} = {
  pending_in: { text: '待入库', color: 'purple' },
  in_stock: { text: '已入库', color: 'blue' },
  pending_out: { text: '待出库', color: 'orange' },
  out_stock: { text: '已出库', color: 'cyan' },
  idle: { text: '闲置中', color: 'lime' },
  in_use: { text: '使用中', color: 'green' },
  repairing: { text: '维修中', color: 'gold' },
  pending_scrap: { text: '待报废', color: 'volcano' },
  scrapped: { text: '已报废', color: 'red' },
  sold_out: { text: '已售卖', color: 'red' },
};

// 业务状态映射
export const bizStatusMap: {
  [key: string]: { color: string; text: string };
} = {
  active: { text: 'ACTIVE', color: 'green' },
  maintaining: { text: 'MAINTAINING', color: 'blue' },
  outage: { text: 'OUTAGE', color: 'red' },
};

// 硬件状态映射
export const hardwareStatusMap: {
  [key: string]: { color: string; text: string };
} = {
  normal: { text: '正常', color: 'green' },
  warning: { text: '警告', color: 'orange' },
  faulty: { text: '故障', color: 'red' },
};

// 资产类型映射
export const assetTypeMap: Record<string, { color: string; label: string }> = {
  network: { label: '网络设备', color: 'blue' },
  server: { label: '服务器', color: 'green' },
  switch: { label: '交换机', color: 'purple' },
  firewall: { label: '防火墙', color: 'orange' },
  router: { label: '路由器', color: 'green' },
  loadbalancer: { label: '负载均衡', color: 'cyan' },
  other: { label: '其他', color: 'default' },
};

// 默认数据
export const defaultData = {
  role: '',
  templateID: undefined,
  assetType: 'network',
  firmwareVersion: '',
  loopbackAddress: '',
  managementAddress: '',
  ports: undefined,
  portSpeed: '',
  stackSupport: false,
  stackID: undefined,
  stackRole: '',
  layer: undefined,
  routingProtocols: '',

  // 一站式创建时设备基本信息
  sn: '',
  brand: '',
  model: '',
  assetStatus: 'in_use',
  hardwareStatus: 'normal',
  purchaseOrder: '',
  purchaseDate: '',
  warrantyExpire: '',
  price: 0,
  residualValue: 0,
  remark: '',

  // 资源信息
  project: '',
  cabinetID: undefined,
  rackPosition: undefined,
  bizStatus: 'active',
  rackingTime: '',
  deliveryTime: '',
};

// 获取房间选项
export const fetchRoomOptions = async () => {
  try {
    const { getRoomTableApi } = await import('#/api/core/cmdb/location/room');
    const res = await getRoomTableApi({ page: 1, pageSize: 1000 });
    return res.list.map((item: any) => ({
      label: `${item.name}${item.dataCenter ? ` (${item.dataCenter.name})` : ''}`,
      value: item.id,
    }));
  } catch (error) {
    console.error('获取房间选项失败:', error);
    return [];
  }
};

// 根据房间ID获取机柜选项
export const fetchCabinetsByRoomId = async (roomId: number) => {
  if (!roomId) {
    return [];
  }

  try {
    const { getCabinetsByRoomIdApi } = await import(
      '#/api/core/cmdb/location/cabinet'
    );
    const cabinets = await getCabinetsByRoomIdApi(roomId);
    return cabinets.map((cabinet: any) => ({
      label: cabinet.name,
      value: cabinet.id,
    }));
  } catch (error) {
    console.error('根据房间ID获取机柜列表失败:', error);
    return [];
  }
};

// 获取机柜选项
export const fetchCabinetOptions = async (message: any) => {
  try {
    const { getCabinetTableApi } = await import(
      '#/api/core/cmdb/location/cabinet'
    );
    const res = await getCabinetTableApi({ page: 1, pageSize: 1000 });

    return res.list.map((item) => ({
      label: `${item.name}${item.room ? ` (${item.room.name})` : ''}`,
      value: item.id,
    }));
  } catch {
    message.error('获取机柜选项失败');
    return [];
  }
};

// 获取模板列表
export const fetchTemplateOptions = async (
  message: any,
  _selectedTemplate = '',
  _selectedRole = '',
  _roleTemplateMap: Record<string, string> = {},
) => {
  try {
    const { getTemplateListApi } = await import(
      '#/api/core/cmdb/template/template'
    );
    const response = await getTemplateListApi({
      page: 1,
      pageSize: 100,
    });

    // 转换为选项格式
    const templateOptions = response.list.map((template) => ({
      label: template.templateName,
      value: template.templateName,
      id: template.id as number,
    }));

    // 根据已有的默认映射关系，建立角色与模板的关联
    const tempMap: Record<string, string> = {};
    Object.entries(defaultTemplateMap).forEach(([role, templateName]) => {
      // 检查API返回的模板中是否存在该模板名称
      const found = templateOptions.some((t) => t.value === templateName);
      if (found) {
        tempMap[role] = templateName;
      }
    });

    return {
      templateOptions,
      roleTemplateMap: tempMap,
    };
  } catch {
    // 获取模板列表失败，使用默认映射
    const tempMap: Record<string, string> = {};
    Object.entries(defaultTemplateMap).forEach(([role, templateName]) => {
      tempMap[role] = templateName;
    });

    // 显示错误通知
    message.error('获取模板列表失败，已使用默认配置');

    return {
      templateOptions: [],
      roleTemplateMap: tempMap,
    };
  }
};

// 根据模板名称查询模板ID
export const getTemplateIdByName = async (
  templateName: string,
  templateOptions: any[] = [],
  message: any,
): Promise<null | number> => {
  if (!templateName) {
    return null;
  }

  // 优先从特殊映射中查找
  const specialTemplateMap: Record<string, number> = {
    '8Q-TOR-T3-B': 2,
    '32Q-TOR-T3-B': 3,
    'COREB-TH5': 4,
    'TOR-P4-6.4T': 5,
    'COREB-TH3': 6,
    'TOR-1G': 7,
    'COREC-Macsec-1': 8,
    'COREC-Macsec-2': 9,
    默认服务器模板: 1,
  };

  if (specialTemplateMap[templateName]) {
    const id = specialTemplateMap[templateName];

    return id;
  }

  // 从已加载的模板选项中查找
  const foundTemplate = templateOptions.find(
    (t) => t.value === templateName || t.label === templateName,
  );
  if (foundTemplate && foundTemplate.id) {
    return foundTemplate.id;
  }

  // 如果本地没有找到，尝试从API获取
  try {
    const { getTemplateListApi } = await import(
      '#/api/core/cmdb/template/template'
    );
    const response = await getTemplateListApi({
      page: 1,
      pageSize: 100,
      query: templateName,
    });

    // 先精确匹配模板名称
    const exactMatch = response.list.find(
      (t) => t.templateName === templateName,
    );
    if (exactMatch && exactMatch.id) {
      return exactMatch.id;
    }

    // 尝试模糊匹配
    const fuzzyMatch = response.list.find(
      (t) =>
        t.templateName.includes(templateName) ||
        templateName.includes(t.templateName),
    );
    if (fuzzyMatch && fuzzyMatch.id) {
      return fuzzyMatch.id;
    }

    message.warning(
      `未找到名为 "${templateName}" 的模板，设备模板ID将不会更新`,
    );
    return null;
  } catch (error) {
    console.error('查询模板API失败:', error);
    message.error(`查询模板失败: ${error}`);
    return null;
  }
};

// 根据资产类型获取标签文本
export const getAssetTypeLabel = (type: string | undefined): string => {
  if (!type) return '未知';
  return assetTypeMap[type]?.label || type;
};

// 根据资产类型获取标签颜色
export const getAssetTypeColor = (type: string | undefined): string => {
  if (!type) return 'default';
  return assetTypeMap[type]?.color || 'default';
};

// 根据模板ID获取模板名称
export const getTemplateNameById = (
  templateId: null | number | undefined,
  templateOptions: any[] = [],
): string => {
  if (!templateId) return '-';

  // 先从已加载的模板选项中查找
  const foundTemplate = templateOptions.find((t) => t.id === templateId);
  if (foundTemplate) {
    return foundTemplate.label;
  }

  // 对于默认映射的模板，尝试反向查找
  for (const [_role, templateName] of Object.entries(defaultTemplateMap)) {
    const template = templateOptions.find((t) => t.value === templateName);
    if (template && template.id === templateId) {
      return templateName;
    }
  }

  // 处理特殊情况的固定映射
  const specialTemplateMap: Record<number, string> = {
    1: '默认服务器模板',
    2: '8Q-TOR-T3-B', // 添加ID为2的特殊模板映射
    3: '32Q-TOR-T3-B', // 添加其他常见模板的ID映射
    4: 'COREB-TH5',
    5: 'TOR-P4-6.4T',
    6: 'COREB-TH3',
    7: 'TOR-1G',
    8: 'COREC-Macsec-1',
    9: 'COREC-Macsec-2',
  };

  if (specialTemplateMap[templateId]) {
    return specialTemplateMap[templateId];
  }

  // 如果是默认角色对应的模板ID，返回默认模板名称
  const defaultRoleForId = Object.entries(defaultTemplateMap).find(
    ([_, templateName]) => {
      const template = templateOptions.find((t) => t.value === templateName);
      return template && template.id === templateId;
    },
  );

  if (defaultRoleForId) {
    return defaultRoleForId[1]; // 返回模板名称
  }

  // 如果找不到，返回ID
  return `模板#${templateId}`;
};

// 创建API包装器
export const createWrappedApi = (
  message: any,
  _templateOptions: any[] = [],
  _getNetworkDeviceDetailApi: any,
  createNetworkDeviceWithDeviceApi: any,
  updateNetworkDeviceWithDeviceApi: any,
  deleteNetworkDeviceApi: any,
  getNetworkDeviceListApi: any,
  fetchTemplateOptionsFunc: any,
) => {
  return {
    // 获取列表
    getList: getNetworkDeviceListApi,

    // 添加网络设备
    async add(values: any) {
      try {
        const result = await createNetworkDeviceWithDeviceApi(values);
        message.success('添加网络设备成功');
        return result;
      } catch (error) {
        message.error(`添加网络设备失败: ${error}`);
        throw error;
      }
    },

    // 编辑网络设备
    async edit(values: any) {
      try {
        // 设备ID处理
        if (!values.deviceID && values.device?.id) {
          values.deviceID = values.device.id;
        }

        // 从detail字段获取设备ID (如果存在)
        if (!values.deviceID && values.detail?.deviceID) {
          values.deviceID = values.detail.deviceID;
        }

        if (!values.device) {
          values.device = { id: values.deviceID };
        } else if (!values.device.id && values.deviceID) {
          values.device.id = values.deviceID;
        }

        // 最后检查设备ID
        if (!values.deviceID) {
          // 尝试从详情API中重新获取设备ID
          try {
            const detailData = await _getNetworkDeviceDetailApi(values.id);
            if (detailData && detailData.deviceID) {
              values.deviceID = detailData.deviceID;
              if (values.device) {
                values.device.id = detailData.deviceID;
              } else {
                values.device = { id: detailData.deviceID };
              }
            } else {
              message.error('找不到关联的设备ID，无法更新网络设备');
              throw new Error('设备ID不能为空');
            }
          } catch {
            message.error('获取设备ID失败，无法更新网络设备');
            throw new Error('设备ID不能为空');
          }
        }

        // 模板ID处理
        if (values.templateID) {
          if (values.device) {
            values.device.templateID = values.templateID;
          }
        } else {
          // 如果没有有效的模板ID，确保删除相关字段
          if (values.device) {
            delete values.device.template_id;
            delete values.device.templateID;
          }
        }

        // 角色处理
        if (!values.role && values.device?.role) {
          values.role = values.device.role;
        }

        // 资源信息处理
        if (
          values.cabinetID ||
          values.rackPosition ||
          values.project ||
          values.bizStatus ||
          values.resStatus ||
          values.resourceID
        ) {
          values.resource = values.resource || {};

          // 数据映射
          const resourceFields = [
            'id',
            'cabinetID',
            'rackPosition',
            'project',
            'bizStatus',
            'resStatus',
            'vpcIP',
            'bmcIP',
            'cluster',
            'isBackup',
            'resourceRemark',
          ];

          // 将顶层字段复制到resource对象
          resourceFields.forEach((field) => {
            const sourceField = field === 'id' ? 'resourceID' : field;
            if (values[sourceField] !== undefined) {
              values.resource[field] = values[sourceField];
            }
          });
        }

        // 确保使用正确的ID进行更新
        // 网络设备ID是用于URL路径参数的id，确保它是正确的网络设备ID
        const networkDeviceId = values.id;

        // 确保device.id是正确的设备ID
        if (values.device) {
          values.device.id = values.deviceID;
        }

        // 发送请求到后端 - 使用一站式更新API
        const result = await updateNetworkDeviceWithDeviceApi(
          networkDeviceId,
          values,
        );
        message.success('更新网络设备成功');
        return result;
      } catch (error) {
        message.error(`更新网络设备失败: ${error}`);
        throw error;
      }
    },

    // 删除网络设备
    async delete(id: number) {
      try {
        const result = await deleteNetworkDeviceApi(id);
        message.success('删除网络设备成功');
        return result;
      } catch (error) {
        message.error(`删除网络设备失败: ${error}`);
        throw error;
      }
    },

    // 刷新视图
    async refreshView() {
      await fetchTemplateOptionsFunc();
      return true;
    },
  };
};

// 表单提交前处理
export const handleBeforeSubmit = async (
  formData: any,
  message: any,
  getTemplateIdByNameFunc: any,
  templateOptions: any[] = [],
) => {
  // 深拷贝数据，避免修改原始数据
  const processedData = structuredClone(formData);

  // 移除时间戳字段
  const timestampFields = [
    'created_at',
    'updated_at',
    'createdAt',
    'updatedAt',
  ];
  timestampFields.forEach((field) => {
    processedData[field] = undefined;
  });

  // 确保设备ID存在
  if (!processedData.deviceID) {
    message.error('设备ID不能为空');
    throw new Error('设备ID不能为空');
  }

  // 处理模板信息
  const role = processedData.role || '';
  let templateName = processedData.templateName || '';

  // 根据角色获取模板名称
  if (!templateName && role && defaultTemplateMap[role]) {
    templateName = defaultTemplateMap[role];
    processedData.templateName = templateName;
  }

  // 获取模板ID
  const templateId = templateName
    ? await getTemplateIdByNameFunc(templateName, templateOptions, message)
    : null;
  if (templateId) {
    processedData.templateID = templateId;
  }

  // 更新网络设备
  if (processedData.id) {
    // 确保deviceID字段一直存在
    if (!processedData.deviceID) {
      message.error('设备ID不能为空');
      throw new Error('设备ID不能为空');
    }

    // 防止网络设备ID和设备ID混淆
    if (processedData.id === processedData.deviceID && processedData.detail) {
      // 如果ID相同且有详情数据，使用详情中的网络设备ID

      processedData.id = processedData.detail.id;
    }

    // 构建device对象
    if (!processedData.device) {
      const deviceFields = [
        'sn',
        'brand',
        'model',
        'assetType',
        'assetStatus',
        'hardwareStatus',
        'purchaseOrder',
        'purchaseDate',
        'warrantyExpire',
        'price',
        'residualValue',
        'remark',
      ];

      processedData.device = {
        id: processedData.deviceID,
      };

      // 复制属性到device对象
      deviceFields.forEach((field) => {
        if (processedData[field] !== undefined) {
          processedData.device[field] = processedData[field];
        }
      });

      // 设置默认值
      processedData.device.assetType =
        processedData.device.assetType || 'network';
      processedData.device.assetStatus =
        processedData.device.assetStatus || 'in_use';
      processedData.device.hardwareStatus =
        processedData.device.hardwareStatus || 'normal';
    } else if (!processedData.device.id) {
      processedData.device.id = processedData.deviceID;
    }

    // 设置模板ID - 只有当找到有效模板ID时才设置
    if (templateId && processedData.device) {
      processedData.device.templateID = templateId;
    } else {
      // 确保不传递模板ID字段，避免外键约束错误
      if (processedData.device) {
        delete processedData.device.templateID;
      }
      delete processedData.templateID;
    }

    // 构建resource对象
    const resourceRelatedFields = [
      'cabinetID',
      'rackPosition',
      'project',
      'bizStatus',
      'resStatus',
      'vpcIP',
      'bmcIP',
      'cluster',
      'isBackup',
      'resourceRemark',
      'rackingTime',
      'deliveryTime',
    ];

    const hasResourceFields = resourceRelatedFields.some(
      (field) =>
        processedData[field] !== undefined && processedData[field] !== null,
    );

    if (hasResourceFields || processedData.resourceID) {
      processedData.resource = processedData.resource || {};

      // 设置资源ID
      if (processedData.resourceID) {
        processedData.resource.id = processedData.resourceID;
      }

      // 复制资源字段
      resourceRelatedFields.forEach((field) => {
        if (processedData[field] !== undefined) {
          processedData.resource[field] = processedData[field];
        }
      });
    }

    // 删除已复制的顶层字段，但保留deviceID
    const fieldsToDelete = [
      'sn',
      'brand',
      'model',
      'assetType',
      'assetStatus',
      'hardwareStatus',
      'purchaseOrder',
      'purchaseDate',
      'warrantyExpire',
      'price',
      'residualValue',
      'remark',
      'templateID',
      'template_id',
    ];

    fieldsToDelete.forEach((field) => {
      processedData[field] = undefined;
    });

    // 确保deviceID不被删除
    if (!processedData.deviceID && processedData.device?.id) {
      processedData.deviceID = processedData.device.id;
    }
  }

  // 处理日期字段 - 统一格式化逻辑
  const formatDates = (obj: any) => {
    if (!obj) return;

    const dateFields = [
      'purchaseDate',
      'warrantyExpire',
      'rackingTime',
      'deliveryTime',
    ];
    dateFields.forEach((field) => {
      if (obj[field] === '' || obj[field] === '0000-00-00') {
        obj[field] = undefined;
      }
    });
  };

  // 格式化device和顶层对象的日期
  formatDates(processedData);
  formatDates(processedData.device);
  formatDates(processedData.resource);

  // 移除时间戳字段
  if (processedData.device) {
    timestampFields.forEach((field) => {
      processedData.device[field] = undefined;
    });
  }

  return processedData;
};

// 编辑前处理
export const handleBeforeEdit = async (
  record: any,
  message: any,
  getNetworkDeviceDetailApi: any,
) => {
  try {
    // 首先获取详细信息
    const detail = await getNetworkDeviceDetailApi(record.id);
    const detailData = detail as any;

    const device = detailData?.device || {};

    // 确保设备ID正确
    const deviceID =
      detailData?.deviceID || record?.deviceID || record?.device?.id;
    if (!deviceID) {
      message.error('找不到关联的设备ID，无法编辑');
      return null;
    }

    // 获取角色和模板信息
    const role = detailData?.role || record?.role || '';
    let templateName = detailData?.templateName || record?.templateName || '';

    // 根据角色获取模板名称
    if (!templateName && role && defaultTemplateMap[role]) {
      templateName = defaultTemplateMap[role];
    }

    // 获取必要的标识符
    const resourceID = detailData?.resourceID || record?.resourceID;

    // 确保网络设备ID和设备ID正确设置
    const networkDeviceId = detailData?.id || record?.id; // 这是网络设备的ID
    const deviceId = deviceID; // 这是关联设备的ID

    // 构建表单数据对象 - 合并所有来源的数据
    const formData = {
      // 基本标识符
      id: networkDeviceId, // 确保这是网络设备ID
      deviceID: deviceId, // 确保这是设备ID
      resourceID,

      // 保存详情数据的引用，以便后续处理
      detail: detailData,

      // 网络设备信息
      role,
      templateName,
      templateID: detailData?.templateID || device?.templateID,
      firmwareVersion:
        detailData?.firmwareVersion || record?.firmwareVersion || '',
      loopbackAddress:
        detailData?.loopbackAddress || record?.loopbackAddress || '',
      managementAddress:
        detailData?.managementAddress || record?.managementAddress || '',
      ports: detailData?.ports || record?.ports,
      portSpeed: detailData?.portSpeed || record?.portSpeed || '',
      stackSupport: detailData?.stackSupport || record?.stackSupport || false,
      stackID: detailData?.stackID || record?.stackID,
      stackRole: detailData?.stackRole || record?.stackRole || '',
      layer: detailData?.layer || record?.layer,
      routingProtocols:
        detailData?.routingProtocols || record?.routingProtocols || '',

      // 设备基本信息
      sn: device?.sn || detailData?.sn || record?.sn || '',
      brand: device?.brand || detailData?.brand || record?.brand || '',
      model: device?.model || detailData?.model || record?.model || '',
      assetType:
        device?.assetType ||
        detailData?.assetType ||
        record?.assetType ||
        'network',
      assetStatus:
        device?.assetStatus ||
        detailData?.assetStatus ||
        record?.assetStatus ||
        'in_use',
      hardwareStatus:
        device?.hardwareStatus ||
        detailData?.hardwareStatus ||
        record?.hardwareStatus ||
        'normal',

      // 财务信息
      purchaseOrder:
        device?.purchaseOrder ||
        detailData?.purchaseOrder ||
        record?.purchaseOrder ||
        '',
      purchaseDate:
        device?.purchaseDate ||
        detailData?.purchaseDate ||
        record?.purchaseDate,
      warrantyExpire:
        device?.warrantyExpire ||
        detailData?.warrantyExpire ||
        record?.warrantyExpire,
      price: device?.price || detailData?.price || record?.price,
      residualValue:
        device?.residualValue ||
        detailData?.residualValue ||
        record?.residualValue,

      // 资源和位置信息
      cabinetID:
        detailData?.cabinetID || record?.cabinetID || record?.cabinet?.id,
      rackPosition: detailData?.rackPosition || record?.rackPosition,
      project: detailData?.project || record?.project || '',
      bizStatus: detailData?.bizStatus || record?.bizStatus || 'active',
      resStatus: detailData?.resStatus || record?.resStatus || '',
      rackingTime: detailData?.rackingTime || record?.rackingTime,
      deliveryTime: detailData?.deliveryTime || record?.deliveryTime,

      // 其他资源信息
      vpcIP: detailData?.vpcIP || record?.vpcIP || '',
      bmcIP: detailData?.bmcIP || record?.bmcIP || '',
      cluster: detailData?.cluster || record?.cluster || '',
      isBackup: detailData?.isBackup || record?.isBackup || false,
      resourceRemark:
        detailData?.resourceRemark || record?.resourceRemark || '',
      remark: device?.remark || detailData?.remark || record?.remark || '',

      // 确保device对象存在，并包含必要的ID
      device: {
        id: deviceId, // 使用设备ID，而不是网络设备ID
        ...device,
      },
    };

    return formData;
  } catch (error) {
    console.error('获取网络设备详情失败:', error);
    message.error('获取网络设备详情失败，请刷新重试');
    return {
      ...record,
      deviceID: record?.deviceID || record?.device?.id,
    };
  }
};
