<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { DataCenter } from '#/api/core/cmdb/location/dataCenter';

import { ref } from 'vue';

import { Tag } from 'ant-design-vue';

import { getAZTableApi } from '#/api/core/cmdb/location/az';
import {
  addDataCenterApi,
  deleteDataCenterApi,
  editDataCenterApi,
  getDataCenterTableApi,
} from '#/api/core/cmdb/location/dataCenter';
import CommonDetail from '#/components/CommonDetail/index.vue';
import CommonList from '#/components/CommonList/index.vue';

const detailRef = ref();
const detailData = ref<DataCenter | null>(null);
const commonListRef = ref();
const azOptions = ref<{ label: string; value: number }[]>([]);

// 状态映射
const statusMap: {
  [key: string]: { color: string; text: string };
} = {
  active: { text: '启用', color: 'success' },
  disabled: { text: '禁用', color: 'error' },
};

// 默认数据
const defaultData = {
  name: '',
  azId: undefined,
  address: '',
  status: 'active',
  description: '',
};

// 获取可用区选项
async function fetchAZOptions() {
  try {
    const res = await getAZTableApi({ page: 1, pageSize: 1000 });
    azOptions.value = res.list.map((item) => ({
      label: `${item.name}${item.region ? ` (${item.region.name})` : ''}`,
      value: item.id,
    }));
  } catch (error) {
    console.error('获取可用区选项失败:', error);
  }
}

// 初始化时获取可用区选项
fetchAZOptions();

// 表格搜索表单
const formOptions: VbenFormProps = {
  collapsed: true,
  collapsedRows: 1,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入机房名称' },
      fieldName: 'query',
      label: '机房名称',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择可用区',
        options: azOptions,
        allowClear: true,
      },
      fieldName: 'azId',
      label: '所属可用区',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: true,
};

// 表格配置
const gridOptions: VxeGridProps<DataCenter> = {
  checkboxConfig: {
    highlight: true,
  },
  exportConfig: {}, // 导出配置
  importConfig: {
    types: ['csv'],
  }, // 导入配置
  columns: [
    { align: 'center', type: 'checkbox', width: 40 },
    { field: 'id', title: 'ID', width: 80 },
    { field: 'name', title: '机房名称' },
    {
      field: 'az.name',
      title: '所属可用区',
    },
    {
      field: 'az.region.name',
      title: '所属区域',
    },
    {
      field: 'address',
      title: '地址',
    },
    {
      field: 'status',
      title: '状态',
      slots: { default: 'status' }, // 添加自定义插槽
    },
    { field: 'description', title: '描述信息' },
    {
      title: '操作',
      slots: { default: 'operate' },
      width: 200,
    },
  ],
  keepSource: true,
  height: 'auto',
  rowConfig: { isHover: true },
  pagerConfig: { pageSize: 10, pageSizes: [10, 20, 50, 100] },
  toolbarConfig: {
    custom: true,
    refresh: true,
    export: true,
    zoom: true,
    import: true,
    slots: {
      buttons: 'toolbar-buttons',
    },
  },
};

// 新增/编辑表单配置
const editFormOptions: VbenFormProps = {
  showDefaultActions: false,
  schema: [
    {
      fieldName: 'name',
      label: '机房名称',
      component: 'Input',
      componentProps: { placeholder: '请输入机房名称' },
      rules: 'required',
    },
    {
      fieldName: 'azId',
      label: '所属可用区',
      component: 'Select',
      componentProps: {
        placeholder: '请选择所属可用区',
        options: azOptions,
      },
      rules: 'required',
    },
    {
      fieldName: 'address',
      label: '地址',
      component: 'Input',
      componentProps: {
        placeholder: '请输入地址',
      },
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: [
          { label: '启用', value: 'active' },
          { label: '禁用', value: 'disabled' },
        ],
      },
      rules: 'required',
    },
    {
      fieldName: 'description',
      label: '描述信息',
      component: 'Input',
      componentProps: {
        placeholder: '请输入描述信息',
        rows: 4,
        type: 'textarea',
      },
    },
  ],
};

// 详情字段配置
const detailFields = [
  { label: 'ID', field: 'id' },
  { label: '机房名称', field: 'name' },
  {
    label: '所属可用区',
    field: 'az.name',
    format: (value: any) => {
      if (value) return value;
      const data = detailData.value;
      if (data?.azId) {
        const az = azOptions.value.find((opt) => opt.value === data.azId);
        if (az?.label) {
          const parts = az.label.split(' (');
          return parts[0] || '暂无';
        }
      }
      return '暂无';
    },
  },
  {
    label: '所属区域',
    field: 'az.region.name',
    format: (value: any) => {
      if (value) return value;
      const data = detailData.value;
      if (data?.azId) {
        const az = azOptions.value.find((opt) => opt.value === data.azId);
        if (az?.label && az?.label.includes('(')) {
          const parts = az?.label.split('(');
          // 使用可选链和空值合并操作符
          return (parts[1] ?? '').replace(')', '').trim() || '暂无';
        }
      }
      return '暂无';
    },
  },
  { label: '地址', field: 'address' },
  { label: '状态', field: 'status', type: 'status' as const },
  { label: '描述信息', field: 'description' },
  { label: '创建时间', field: 'created_at' },
  { label: '更新时间', field: 'updated_at' },
];

// 处理详情事件
function handleDetail(row: DataCenter) {
  detailData.value = row;
  detailRef.value?.drawerApi.open();
}

// 自定义权限配置
const permissions = {
  add: ['Cmdb:IdcInfo:Create'],
  edit: ['Cmdb:IdcInfo:Edit'],
  delete: ['Cmdb:IdcInfo:Delete'],
};
</script>

<template>
    <div>

    <!-- 使用通用列表组件 -->
    <CommonList
      ref="commonListRef"
      :form-options="formOptions"
      :grid-options="gridOptions"
      :edit-form-options="editFormOptions"
      :api="{
        getList: getDataCenterTableApi,
        add: addDataCenterApi,
        edit: editDataCenterApi,
        delete: deleteDataCenterApi,
      }"
      :default-data="defaultData"
      :status-map="statusMap"
      @detail="handleDetail"
      :permissions="permissions"
      show-add-button
      show-edit-button
      show-delete-button
      show-detail-button
    >
      <!-- 自定义状态插槽 -->
      <template #status="{ row }">
        <Tag :color="statusMap[row.status]?.color || 'default'">
          {{ statusMap[row.status]?.text || row.status || '未知状态' }}
        </Tag>
      </template>
    </CommonList>

    <!-- 使用通用详情组件 -->
    <CommonDetail
      ref="detailRef"
      :data="detailData"
      title="机房详情"
      :fields="detailFields"
      :status-map="statusMap"
      show-audit-log
      table-name="data_centers"
    />
  </div>
</template>
