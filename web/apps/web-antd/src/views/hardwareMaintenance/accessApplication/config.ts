import type { ModalApiOptions } from '@vben/common-ui';

import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { EntryPersonRes, EntryTicketRes } from '#/api/core/ticket/types';

import { listEntryTicketApi } from '#/api/core/ticket/fault-ticket';

export const APPROVAL_STATUS_LABEL: Record<string, string> = {
  approved: '已审批',
  rejected: '已拒绝',
  waiting_approval: '待审批',
  waiting_second_approval: '待负责人审批',
};

export const statusColors: Record<string, string> = {
  approved: 'green',
  rejected: 'red',
};

/** 入室申请表格表单配置 */
export const accessRequestListForm: VbenFormProps = {
  collapsed: true,
  collapsedRows: 3,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入工单编号' },
      fieldName: 'ticketNo',
      label: '工单编号',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择审批状态',
        allowClear: true,
        options: Object.entries(APPROVAL_STATUS_LABEL).map(([key, value]) => {
          return {
            label: value,
            value: key,
          };
        }),
      },
      fieldName: 'status',
      label: '审批状态',
    },
    {
      component: 'RangePicker',
      componentProps: {
        placeholder: ['开始时间', '结束时间'],
      },
      fieldName: 'dateRange',
      label: '申请时间',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: false,
  submitOnChange: false,
  submitOnEnter: true,
  wrapperClass: 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4 grid',
};

/** 入室申请列表 */
export const accessRequestListGrid: VxeGridProps<EntryTicketRes> = {
  columns: [
    {
      field: 'ticketNo',
      title: '工单编号',
    },
    {
      field: 'status',
      title: '审批状态',
      slots: {
        default: 'status',
      },
    },
    {
      field: 'entryStartTime',
      title: '入室开始时间',
      formatter: 'formatDateTime',
    },
    {
      field: 'entryEndTime',
      title: '入室结束时间',
      formatter: 'formatDateTime',
    },
    { field: 'applicantName', title: '申请人' },
    { field: 'creationTime', title: '申请日期', formatter: 'formatDateTime' },
    {
      title: '操作',
      slots: {
        default: 'action',
      },
    },
  ],
  data: [],
  proxyConfig: {
    response: {
      result: 'list',
      total: 'total',
    },
    ajax: {
      async query({ page }, form) {
        const dateRange = form.dateRange;
        const result = await listEntryTicketApi({
          page: page.currentPage,
          pageSize: page.pageSize,
          query: form.ticketNo,
          status: form.status || '',
          creationTimeStart: dateRange ? dateRange[0].toISOString() : '',
          creationTimeEnd: dateRange ? dateRange[1].toISOString() : '',
        });
        return result;
      },
    },
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10,
  },
  sortConfig: {
    multiple: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar-buttons',
    },
  },
};

/** 入室人员列表 */
export const visitorsGrid: VxeGridProps<EntryPersonRes> = {
  columns: [
    { slots: { default: 'name' }, field: 'name', title: '入室人员姓名' },
    {
      slots: { default: 'identification_number' },
      field: 'identification_number',
      title: '身份证号',
    },
    { slots: { default: 'telephone' }, field: 'telephone', title: '手机号' },
    {
      slots: { default: 'person_type' },
      field: 'person_type',
      title: '人员类型',
    },
    { slots: { default: 'company' }, field: 'company', title: '公司' },
    { slots: { default: 'action' }, title: '操作' },
  ],
  // editConfig: {
  //   mode: 'row',
  //   trigger: 'click',
  // },
  data: [],
  pagerConfig: {
    enabled: false,
  },
  showOverflow: true,
};

export const entryPersonsGrid: VxeGridProps<EntryPersonRes> = {
  columns: [
    { field: 'name', title: '入室人员姓名' },
    {
      field: 'identification_number',
      title: '身份证号',
    },
    { field: 'telephone', title: '手机号' },
    { field: 'person_type', title: '人员类型' },
    { field: 'company', title: '公司' },
  ],
  data: [],
  pagerConfig: {
    enabled: false,
  },
  showOverflow: true,
};

export const approvalModal: ModalApiOptions = {
  confirmText: '通过',
};
