<script setup lang="ts">
import type { VbenFormProps } from '#/adapter/form';

import { shallowReactive, shallowRef } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { IconAdd } from '@vben/icons';
import { useUserStore } from '@vben/stores';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getDataCenterTableApi } from '#/api/core/cmdb/location/dataCenter';
import { getRoomTableApi } from '#/api/core/cmdb/location/room';
import { addEntryTicketApi } from '#/api/core/ticket/fault-ticket';

import { visitorsGrid } from '../config';

const expectRef = shallowRef<{
  reject: () => void;
  resolve: () => void;
}>();

const initDetail = () => ({
  isCarryEquipment: false,
  equipment: '',
  dataCenterName: '',
  roomName: '',
});
const detail = shallowReactive(initDetail());

const dataCenterOptions = shallowRef<{ label: string; value: any }[]>([]);
const roomOptions = shallowRef<{ label: string; value: any }[]>([]);

const loadMachineRooms = async (dataCenterId: number) => {
  try {
    const result = await getRoomTableApi({
      dataCenterId,
      page: 1,
      pageSize: 1000,
    });
    roomOptions.value = result.list.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    });
  } catch (error) {
    console.error('获取房间选项失败:', error);
  }
};

/** 入室申请详情表单 */
const detailForm: VbenFormProps = {
  submitButtonOptions: {
    show: false,
  },
  resetButtonOptions: {
    show: false,
  },
  layout: 'horizontal',
  schema: [
    {
      component: 'Input',
      componentProps: {
        disabled: true,
      },
      fieldName: 'applicantName',
      label: '申请人',
    },
    {
      component: 'RangePicker',
      fieldName: 'datetime',
      label: '入室时间',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择目标机房',
        options: dataCenterOptions,
        onChange(value: any, option: any) {
          detail.dataCenterName = option.label;
          loadMachineRooms(value);
        },
      },
      fieldName: 'dataCenterId',
      label: '目标机房',
      rules: 'selectRequired',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择房间',
        options: roomOptions,
        onChange(_value: any, option: any) {
          detail.roomName = option.label;
        },
      },
      fieldName: 'roomId',
      label: '房间',
      rules: 'selectRequired',
      dependencies: {
        triggerFields: ['dataCenterId'],
        show(values) {
          return !!values.dataCenterId;
        },
        trigger(values) {
          values.roomId = void 0;
        },
      },
    },
    {
      component: 'Input',
      fieldName: 'visitors',
      label: '入室人员列表',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'carryEquipment',
      label: '携带设备',
    },
    {
      component: 'Checkbox',
      fieldName: 'isOutSource',
      label: '包含外派员工',
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入原因',
      },
      fieldName: 'reason',
      label: '入室原因',
      rules: 'required',
    },
  ],
  wrapperClass: 'grid-cols-1',
};

const [Drawer, drawerApi] = useVbenDrawer();
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: visitorsGrid,
});
const [BaseForm, formApi] = useVbenForm(detailForm);
const { userInfo } = useUserStore();

// 获取机房选项
async function loadDataCenters() {
  try {
    const res = await getDataCenterTableApi({ page: 1, pageSize: 1000 });
    dataCenterOptions.value = res.list.map((item) => ({
      label: `${item.name}${item.az ? ` (${item.az.name})` : ''}`,
      value: item.id,
    }));
  } catch (error) {
    console.error('获取机房选项失败:', error);
  }
}

drawerApi.onOpened = async () => {
  formApi.setFieldValue('applicantName', userInfo!.username);
  loadDataCenters();
};

drawerApi.onClosed = () => {
  Object.assign(detail, initDetail());
};
drawerApi.onConfirm = async () => {
  drawerApi.setState({
    confirmLoading: true,
  });
  try {
    const visitors = gridApi.grid.getFullData();
    if (visitors.length > 0) {
      formApi.setValues({ visitors });
    }
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const values = await formApi.getValues();
    await addEntryTicketApi({
      persons: visitors,
      entry_start_time: values.datetime[0].toISOString(),
      entry_end_time: values.datetime[1].toISOString(),
      reason: values.reason,
      carryEquipment: values.carryEquipment,
      isOutSource: values.isOutSource,
      dataCenterName: detail.dataCenterName,
      roomName: detail.roomName,
    });
  } finally {
    drawerApi.setState({
      confirmLoading: false,
    });
  }
  message.success('提交成功');
  drawerApi.close();

  expectRef.value!.resolve();
};

const addVisitor = async () => {
  const row = {
    name: '',
    identification_number: '',
    telephone: '',
    person_type: '',
    company: '',
  };
  await gridApi.grid.insert(row);
  await gridApi.grid.setEditRow(gridApi.grid.getFullData()[0]);
};

const changeCarryEquipmentCheckbox = (e: any) => {
  formApi.setFieldValue(
    'carryEquipment',
    e.target.checked ? detail.equipment : '',
  );
};
const deleteVisitor = (row: any) => {
  gridApi.grid.remove(row);
};

defineExpose({
  open(): Promise<void> {
    drawerApi.open();
    return new Promise((resolve, reject) => {
      expectRef.value = {
        resolve,
        reject,
      };
    });
  },
});
</script>

<template>
  <Drawer class="w-[1000px]" title="入室申请">
    <Page class="w-full">
      <BaseForm>
        <template #visitors>
          <div class="flex w-full flex-col">
            <a-button
              text
              type="primary"
              class="mb-2 w-fit"
              @click="addVisitor()"
            >
              添加人员
              <template #icon>
                <IconAdd class="ml-1 size-3 cursor-pointer" />
              </template>
            </a-button>
            <div class="visitor-grid">
              <Grid>
                <template #name="{ row }">
                  <a-input
                    v-model:value="row.name"
                    placeholder="请输入入室人员姓名"
                  />
                </template>
                <template #identification_number="{ row }">
                  <a-input
                    v-model:value="row.identification_number"
                    placeholder="请输入身份证号"
                  />
                </template>
                <template #telephone="{ row }">
                  <a-input
                    v-model:value="row.telephone"
                    placeholder="请输入手机号"
                  />
                </template>
                <template #person_type="{ row }">
                  <a-input
                    v-model:value="row.person_type"
                    placeholder="请输入人员类型"
                  />
                </template>
                <template #company="{ row }">
                  <a-input
                    v-model:value="row.company"
                    placeholder="请输入公司"
                  />
                </template>
                <template #action="{ row }">
                  <a-button type="link" @click="deleteVisitor(row)">
                    删除
                  </a-button>
                </template>
              </Grid>
            </div>
          </div>
        </template>
        <template #carryEquipment>
          <a-checkbox
            v-model:checked="detail.isCarryEquipment"
            @change="changeCarryEquipmentCheckbox"
          />
          <a-input
            v-model="detail.equipment"
            class="ml-2"
            placeholder="请输入设备名称"
            :disabled="!detail.isCarryEquipment"
          />
        </template>
      </BaseForm>
    </Page>
  </Drawer>
</template>

<style scoped>
.visitor-grid {
  margin: -0.5rem;
}
</style>
