<script lang="ts" setup>
import type { APPROVE_STATUS } from '#/api/core/ticket/types';

import { shallowReactive, shallowRef } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { approveEntryTicketApi } from '#/api/core/ticket/fault-ticket';

import { approvalModal } from '../config';

const expectRef = shallowRef<{
  reject: () => void;
  resolve: () => void;
}>();

const initApproveInfo = () => ({
  comment: '',
});
const approveInfo = shallowReactive(initApproveInfo());

const [ApprovalModal, approvalApi] = useVbenModal(approvalModal);

const resetApproveInfo = () => {
  Object.assign(approveInfo, initApproveInfo());
};
const approval = async (status: APPROVE_STATUS) => {
  const text = approveInfo.comment;
  const { id, status: currentStatus } = approvalApi.getData();
  let stage = 'customer_approval';

  if (currentStatus === 'waiting_second_approval') {
    stage = 'second_approval';
  }

  await approveEntryTicketApi(id, {
    stage,
    status: currentStatus,
    comments: text || '',
    data: {
      status,
    },
  });
  approvalApi.close();
  message.success('提交成功');

  resetApproveInfo();
  setTimeout(() => {
    expectRef.value!.resolve();
  }, 200);
};
approvalApi.onClosed = () => {
  resetApproveInfo();
};
approvalApi.onConfirm = async () => {
  approval('approved');
};

defineExpose({
  open(config: any): Promise<void> {
    approvalApi.setData(config);
    approvalApi.open();
    return new Promise((resolve, reject) => {
      expectRef.value = {
        resolve,
        reject,
      };
    });
  },
});
</script>

<template>
  <ApprovalModal title="入室审批">
    <template #append-footer>
      <a-button type="primary" danger @click="approval('rejected')">
        拒绝
      </a-button>
    </template>
    <div class="m-4 min-h-[30px]">
      <div class="flex flex-col gap-2">
        <p>审批意见:</p>
        <a-textarea
          v-model:value="approveInfo.comment"
          placeholder="请输入审批意见"
        />
      </div>
    </div>
  </ApprovalModal>
</template>
