<script lang="ts" setup>
import type {
  EntryTicketRes,
  HistoryEntryTicketsRes,
} from '#/api/core/ticket/types';

import { onMounted, shallowRef, useTemplateRef } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  getEntryTicketApi,
  historyEntryTicketsApi,
} from '#/api/core/ticket/fault-ticket';
import { formatToDateTime } from '#/utils/common/time';

import {
  APPROVAL_STATUS_LABEL,
  entryPersonsGrid,
  statusColors,
} from '../accessApplication/config';
import ApprovalModal from './components/approval.vue';

const historyData = shallowRef([] as HistoryEntryTicketsRes[]);
const entryTicket = shallowRef({} as EntryTicketRes);
const approvalDialog = useTemplateRef('approvalDialog');

const router = useRouter();
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: entryPersonsGrid,
});

const route = useRoute();

const onLoad = async () => {
  const id = route.params.id as string;
  getEntryTicketApi(id).then((res) => {
    entryTicket.value = res;
    let { entryPersons } = res;
    if (typeof entryPersons === 'string') {
      entryPersons = JSON.parse(entryPersons);
    }
    gridApi.setGridOptions(
      Object.assign({}, entryPersonsGrid, {
        data: entryPersons,
      }),
    );
  });

  historyEntryTicketsApi(id).then((res) => {
    historyData.value = res;
  });
};

const goBack = () => {
  router.push('/access-application');
};

const handleApprove = () => {
  const value = entryTicket.value;
  approvalDialog
    .value!.open({
      id: value.id,
      status: value.status,
    })
    .then(onLoad);
};

const remarkFileter = (remark: string) => {
  Object.keys(APPROVAL_STATUS_LABEL).forEach((key: string) => {
    remark = remark.replace(key, APPROVAL_STATUS_LABEL[key] || key);
  });
  return remark;
};

const historyTimelineItemColor = (status: string) => {
  return statusColors[status] || 'blue';
};

onMounted(onLoad);
</script>

<template>
  <div class="min-h-screen bg-gray-50 pb-4 dark:bg-gray-900">
    <!-- 顶部标题和状态栏 -->
    <div
      class="mb-4 border-b border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800"
    >
      <div
        class="mx-auto flex max-w-full items-center justify-between px-4 py-3"
      >
        <div class="flex items-center">
          <a-button
            type="primary"
            @click="goBack"
            class="mr-4 flex items-center overflow-hidden whitespace-nowrap rounded-md border border-blue-600 border-transparent bg-blue-500 px-3 py-1 font-medium text-white shadow-sm transition-all duration-300 hover:bg-blue-600 hover:shadow-md"
          >
            <template #icon><span class="anticon">←</span></template>
            返回列表
          </a-button>
          <!-- <Title :level="5" class="m-0 text-gray-800 dark:text-gray-200">
            {{ APPROVAL_STATUS_LABEL[entryTicket.status] }}
          </Title> -->
          <a-tag color="green">
            {{ APPROVAL_STATUS_LABEL[entryTicket.status] }}
          </a-tag>
        </div>
        <div
          v-if="
            entryTicket.status === 'waiting_approval' ||
            entryTicket.status === 'waiting_second_approval'
          "
          class="flex items-center"
        >
          <a-space>
            <a-button type="primary" @click="handleApprove()"> 审批 </a-button>
          </a-space>
        </div>
      </div>
    </div>

    <div class="mx-auto max-w-full px-4">
      <div class="mb-4">
        <a-card
          class="h-full overflow-hidden rounded-lg border border-gray-200 transition-shadow duration-300 hover:shadow-md dark:border-gray-700"
        >
          <template #title>
            <div class="flex items-center font-medium">
              <a-badge status="processing" text="入室申请信息" />
            </div>
          </template>
          <div class="pt-1">
            <a-row :gutter="24" class="mb-4">
              <a-col :span="8">
                <div class="flex py-1">
                  <a-typography-text
                    type="secondary"
                    class="w-[100px] font-medium"
                  >
                    申请人
                  </a-typography-text>
                  <a-typography-text class="text-gray-800 dark:text-gray-200">
                    {{ entryTicket.applicantName }}
                  </a-typography-text>
                </div>
              </a-col>

              <a-col :span="8">
                <div class="flex py-1">
                  <a-typography-text
                    type="secondary"
                    class="w-[100px] font-medium"
                  >
                    申请时间
                  </a-typography-text>
                  <a-typography-text class="text-gray-800 dark:text-gray-200">
                    {{ entryTicket.creationTime }}
                  </a-typography-text>
                </div>
              </a-col>

              <a-col :span="8">
                <div class="flex py-1">
                  <a-typography-text
                    type="secondary"
                    class="w-[100px] font-medium"
                  >
                    入室开始时间
                  </a-typography-text>
                  <a-typography-text class="text-gray-800 dark:text-gray-200">
                    {{ formatToDateTime(entryTicket.entryStartTime) }}
                  </a-typography-text>
                </div>
              </a-col>

              <a-col :span="8">
                <div class="flex py-1">
                  <a-typography-text
                    type="secondary"
                    class="w-[100px] font-medium"
                  >
                    入室结束时间
                  </a-typography-text>
                  <a-typography-text class="text-gray-800 dark:text-gray-200">
                    {{ formatToDateTime(entryTicket.entryEndTime) }}
                  </a-typography-text>
                </div>
              </a-col>

              <a-col :span="8">
                <div class="flex py-1">
                  <a-typography-text
                    type="secondary"
                    class="w-[100px] font-medium"
                  >
                    目标机房
                  </a-typography-text>
                  <a-typography-text class="text-gray-800 dark:text-gray-200">
                    {{ entryTicket.dataCenterName }} -
                    {{ entryTicket.roomName }}
                  </a-typography-text>
                </div>
              </a-col>

              <a-col :span="8">
                <div class="flex py-1">
                  <a-typography-text
                    type="secondary"
                    class="w-[100px] font-medium"
                  >
                    携带设备
                  </a-typography-text>
                  <a-typography-text class="text-gray-800 dark:text-gray-200">
                    {{ entryTicket.isCarryEquipment ? '是' : '否' }}
                  </a-typography-text>
                </div>
              </a-col>

              <a-col :span="8">
                <div class="flex py-1">
                  <a-typography-text
                    type="secondary"
                    class="w-[100px] font-medium"
                  >
                    包含外派员工
                  </a-typography-text>
                  <a-typography-text class="text-gray-800 dark:text-gray-200">
                    {{ entryTicket.isOutSource ? '是' : '否' }}
                  </a-typography-text>
                </div>
              </a-col>

              <a-col :span="8">
                <div class="flex py-1">
                  <a-typography-text
                    type="secondary"
                    class="w-[100px] font-medium"
                  >
                    入室原因
                  </a-typography-text>
                  <a-typography-text class="text-gray-800 dark:text-gray-200">
                    {{ entryTicket.reason }}
                  </a-typography-text>
                </div>
              </a-col>
            </a-row>
          </div>

          <Grid />
        </a-card>
      </div>

      <div
        class="mb-4 rounded-lg border border-gray-200 bg-white pl-4 pr-4 shadow-sm dark:border-gray-700 dark:bg-gray-800"
      >
        <a-tabs>
          <a-tab-pane key="history" tab="处理历史">
            <a-timeline v-if="historyData.length > 0" class="px-4">
              <a-timeline-item
                v-for="(item, index) in historyData"
                :key="index"
                :color="historyTimelineItemColor(item.new_status)"
              >
                <div
                  class="mb-4 rounded-lg border border-gray-100 bg-white p-4 shadow-sm transition-shadow duration-300 hover:shadow-md dark:border-gray-700 dark:bg-gray-800"
                >
                  <div
                    class="mb-2 flex items-center justify-between border-b border-gray-100 pb-2 dark:border-gray-700"
                  >
                    <div>
                      <a-typography-text
                        strong
                        class="text-gray-800 dark:text-gray-200"
                      >
                        {{
                          APPROVAL_STATUS_LABEL[item.new_status] ||
                          item.new_status
                        }}
                      </a-typography-text>
                      <a-typography-text type="secondary" class="ml-2 text-sm">
                        {{ formatToDateTime(item.operation_time) }}
                      </a-typography-text>
                    </div>
                    <a-badge
                      :color="historyTimelineItemColor(item.new_status)"
                    />
                  </div>
                  <div class="pl-2">
                    <div class="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <a-typography-text type="secondary" class="font-medium">
                          操作人：
                        </a-typography-text>
                        <a-typography-text>
                          {{ item.operator_name || '-' }}
                        </a-typography-text>
                      </div>
                      <div>
                        <a-typography-text type="secondary" class="font-medium">
                          活动类型：
                        </a-typography-text>
                        <a-typography-text>
                          {{ item.activity_category || '-' }}
                        </a-typography-text>
                      </div>
                      <div class="col-span-2">
                        <a-typography-text type="secondary" class="font-medium">
                          备注：
                        </a-typography-text>
                        <a-typography-text>
                          {{ remarkFileter(item.remarks) }}
                        </a-typography-text>
                      </div>
                    </div>
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
            <a-empty v-else description="暂无状态历史" />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>

    <ApprovalModal
      v-if="
        entryTicket.status === 'waiting_approval' ||
        entryTicket.status === 'waiting_second_approval'
      "
      ref="approvalDialog"
      @success="onLoad()"
    />
  </div>
</template>
#/utils/common/time
