<script setup lang="ts">
import type { EntryTicketRes } from '#/api/core/ticket/types';

import { onMounted, useTemplateRef } from 'vue';
import { useRouter } from 'vue-router';

import { AccessControl } from '@vben/access';
import { Page } from '@vben/common-ui';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

import AccessRequest from './components/access-request.vue';
import ApprovalModal from './components/approval.vue';
import {
  accessRequestListForm,
  accessRequestListGrid,
  APPROVAL_STATUS_LABEL,
  statusColors,
} from './config';

const detail = useTemplateRef('detail');
const approvalDialog = useTemplateRef('approvalDialog');
const router = useRouter();
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: accessRequestListForm,
  gridOptions: accessRequestListGrid,
});

const handleDetail = (row: EntryTicketRes) => {
  router.push(`/access-application/${row.id}`);
};

const onLoad = () => {
  gridApi.query();
};
const open = () => {
  detail.value!.open().then(onLoad);
};

const handleApprove = (row: EntryTicketRes) => {
  approvalDialog
    .value!.open({
      id: row.id,
      status: row.status,
    })
    .then(onLoad);
};

onMounted(onLoad);
</script>
<template>
  <div class="w-full p-4">
    <Page>
      <Grid>
        <template #toolbar-buttons>
          <AccessControl
            :codes="['System:EntryRoomApplication:Create']"
            type="code"
          >
            <a-button class="mr-2" type="primary" @click="open()">
              新增
            </a-button>
          </AccessControl>
        </template>

        <template #status="{ row }">
          <a-tag :color="statusColors[row.status] || 'blue'">
            {{ APPROVAL_STATUS_LABEL[row.status] }}
          </a-tag>
        </template>
        <template #action="{ row }">
          <AccessControl :codes="['EntryRoom:Handle']" type="code">
            <a-button
              v-if="
                row.status === 'waiting_approval' ||
                row.status === 'waiting_second_approval'
              "
              type="link"
              size="small"
              @click="handleApprove(row)"
            >
              审批
            </a-button>
          </AccessControl>
          <a-button type="link" size="small" @click="handleDetail(row)">
            详情
          </a-button>
        </template>
      </Grid>
    </Page>

    <AccessRequest ref="detail" />
    <ApprovalModal ref="approvalDialog" />
  </div>
</template>
