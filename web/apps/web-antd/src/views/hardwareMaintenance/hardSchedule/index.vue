<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { HardSchedule, HardScheduleParams } from '#/api/core/schedule/hardSchedule';

import { onMounted, ref, watch } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { Badge, Calendar, message, Modal, Divider } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';

import { useVbenForm } from '#/adapter/form';
import {
  createSchedule,
  getSchedulesByMonth,
  updateSchedule,
} from '#/api/core/schedule/hardSchedule';
import { getUserListApi } from '#/api/core/user';

const userOptions = ref<{ label: string; value: string }[]>([]);

// 加载用户列表数据
const loadUserOptions = async () => {
  try {
    const response = await getUserListApi({
      page: 1,
      pageSize: 100, // 获取足够多的用户
    });
    userOptions.value = response.list.map((user) => ({
      label: user.realName,
      value: user.realName,
    }));
  } catch (error) {
    console.error('获取用户列表失败:', error);
    message.error('获取用户列表失败');
  }
};

const dayShiftFormOptions: VbenFormProps = {
  showDefaultActions: false,
  schema: [
    {
      fieldName: 'day_date',
      label: '日期',
      component: 'Input',
      componentProps: {
        bordered: false,
        disabled: true,
        readonly: true,
        style: {
          color: '#1890ff',
          fontWeight: 'bold',
        },
      },
    },
    {
      fieldName: 'day_primary_username',
      label: '值班人',
      component: 'Select',
      componentProps: {
        placeholder: '请选择白班值班人',
        options: userOptions,
        showSearch : true,
        filterOption: (input:string, option:any) => {
          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        style: {
          width: '50%',
        },
      },
    },
    {
      fieldName: 'day_second_username',
      label: '',
      component: 'Select',
      componentProps: {
        placeholder: '请选择白班值班人',
        options: userOptions,
        showSearch : true,
        filterOption: (input:string, option:any) => {
          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        style: {
          width: '50%',
        },
      },
    },
  ],
};

const nightShiftFormOptions: VbenFormProps = {
  showDefaultActions: false,
  schema: [
    {
      fieldName: 'night_date',
      label: '日期',
      component: 'Input',
      componentProps: {
        bordered: false,
        disabled: true,
        readonly: true,
        style: {
          color: '#1890ff',
          fontWeight: 'bold',
        },
      },
    },
    {
      fieldName: 'night_primary_username',
      label: '值班人',
      component: 'Select',
      componentProps: {
        placeholder: '请选择夜班值班人',
        options: userOptions,
        style: {
          width: '50%',
        },
      },
    },
    {
      fieldName: 'night_second_username',
      label: '',
      component: 'Select',
      componentProps: {
        placeholder: '请选择夜班值班人',
        options: userOptions,
        style: {
          width: '50%',
        },
      },
    },
  ],
};

const dayShiftData = ref<HardSchedule | null>(null);
const nightShiftData = ref<HardSchedule | null>(null);
const value = ref<Dayjs>(dayjs());
const dailyScheduleData = ref<any[]>([]);
const [DayForm, dayFormApi] = useVbenForm(dayShiftFormOptions);
const [NightForm, nightFormApi] = useVbenForm(nightShiftFormOptions);

const [Drawer, drawerApi] = useVbenDrawer({
  title: '值班详情',
  onConfirm: () => {
    Promise.all([dayFormApi.validate(), nightFormApi.validate()]).then(
      async ([dayValid, nightValid]) => {
        if (dayValid && nightValid) {
          Modal.confirm({
            title: '确认操作',
            content: '确定要保存此值班安排吗？',
            okText: '确认',
            cancelText: '取消',
            onOk: async () => {
              await submitEvent();
            },
          });
        }
      }
    );
  },
});

const loadMonthData = async (date: string) => {
  try {
    const response = await getSchedulesByMonth(date);
    dailyScheduleData.value = response.list || [];
  } catch (error) {
    console.error('获取月度值班数据失败:', error);
    message.error('获取值班数据失败');
  }
};

onMounted(async () => {
  await loadUserOptions();
  const currentDate = dayjs(value.value || new Date());
  const date = currentDate.format('YYYY-MM');
  await loadMonthData(date);
});

watch(value, (newValue) => {
  const date = dayjs(newValue).format('YYYY-MM');
  loadMonthData(date);
});

const onPanelChange = (date: Dayjs | string, _mode: string) => {
  const dateObj = typeof date === 'string' ? dayjs(date) : date;
  const monthDate = dateObj.format('YYYY-MM');
  loadMonthData(monthDate);
};

const mapType = (type: string) => {
  switch (type) {
    case 'day':
      return 'success';
    case 'night':
      return 'warning';
    default:
      return undefined;
  }
};

const getListData = (current: Dayjs) => {
  if (!dailyScheduleData.value || !Array.isArray(dailyScheduleData.value)) {
    return [];
  }
  const dateStr = current.format('YYYY-MM-DD');
  const dayData = dailyScheduleData.value.find((item) => {
    if (!item || !item.date) return false;
    const dbDate = dayjs(item.date).format('YYYY-MM-DD');
    const dbTime = dayjs(item.date).format('HH:mm:ss');
    return dbDate === dateStr && dbTime === '09:00:00';
  });
  const nightData = dailyScheduleData.value.find((item) => {
    if (!item || !item.date) return false;
    const dbDate = dayjs(item.date).format('YYYY-MM-DD');
    const dbTime = dayjs(item.date).format('HH:mm:ss');
    return dbDate === dateStr && dbTime === '21:00:00';
  });
  const result = [];
  if (dayData && Array.isArray(dayData.username) && dayData.username.length > 0) {
    result.push({ type: 'day', content: `白班: ${dayData.username.join('、')}`, indent: false });
  }
  if (nightData && Array.isArray(nightData.username) && nightData.username.length > 0) {
    result.push({ type: 'night', content: `夜班: ${nightData.username.join('、')}`, indent: false });
  }
  return result;
};

const openDrawer = (current: Dayjs) => {
  const dateStr = current.format('YYYY-MM-DD');
  dayShiftData.value = dailyScheduleData.value && Array.isArray(dailyScheduleData.value)
    ? dailyScheduleData.value.find((item) => {
        if (!item || !item.date) return false;
        const dbDate = dayjs(item.date).format('YYYY-MM-DD');
        const dbTime = dayjs(item.date).format('HH:mm:ss');
        return dbDate === dateStr && dbTime === '09:00:00';
      })
    : null;
  nightShiftData.value = dailyScheduleData.value && Array.isArray(dailyScheduleData.value)
    ? dailyScheduleData.value.find((item) => {
        if (!item || !item.date) return false;
        const dbDate = dayjs(item.date).format('YYYY-MM-DD');
        const dbTime = dayjs(item.date).format('HH:mm:ss');
        return dbDate === dateStr && dbTime === '21:00:00';
      })
    : null;
  drawerApi.open();
  setTimeout(() => {
    const dayDateStr = `${dateStr} 09:00:00`;
    dayFormApi.setFieldValue('day_date', dayDateStr);
    if (dayShiftData.value && Array.isArray(dayShiftData.value.username)) {
      dayFormApi.setFieldValue('day_primary_username', dayShiftData.value.username[0] || undefined);
      dayFormApi.setFieldValue('day_second_username', dayShiftData.value.username[1] || undefined);
    } else {
      dayFormApi.setFieldValue('day_primary_username', undefined);
      dayFormApi.setFieldValue('day_second_username', undefined);
    }
    const nightDateStr = `${dateStr} 21:00:00`;
    nightFormApi.setFieldValue('night_date', nightDateStr);
    if (nightShiftData.value && Array.isArray(nightShiftData.value.username)) {
      nightFormApi.setFieldValue('night_primary_username', nightShiftData.value.username[0] || undefined);
      nightFormApi.setFieldValue('night_second_username', nightShiftData.value.username[1] || undefined);
    } else {
      nightFormApi.setFieldValue('night_primary_username', undefined);
      nightFormApi.setFieldValue('night_second_username', undefined);
    }
  }, 100);
};

async function submitEvent() {
  const dayValues = await dayFormApi.getValues();
  const nightValues = await nightFormApi.getValues();
  try {
    const dayDateStr = dayValues.day_date?.split(' ')[0];
    const dayTimeStr = '09:00:00';
    const dayFormattedDate = `${dayDateStr}T${dayTimeStr}+08:00`;
    const dayUserArr = [];
    if (dayValues.day_primary_username) dayUserArr.push(dayValues.day_primary_username);
    if (dayValues.day_second_username) dayUserArr.push(dayValues.day_second_username);
    const dayFormattedData: HardScheduleParams = {
      date: dayFormattedDate,
      username: dayUserArr,
    };
    const nightDateStr = nightValues.night_date?.split(' ')[0];
    const nightTimeStr = '21:00:00';
    const nightFormattedDate = `${nightDateStr}T${nightTimeStr}+08:00`;
    const nightUserArr = [];
    if (nightValues.night_primary_username) nightUserArr.push(nightValues.night_primary_username);
    if (nightValues.night_second_username) nightUserArr.push(nightValues.night_second_username);
    const nightFormattedData: HardScheduleParams = {
      date: nightFormattedDate,
      username: nightUserArr,
    };
    if (dayShiftData.value) {
        await updateSchedule({ ...dayFormattedData, ID: dayShiftData.value.ID });
      } else {
        await createSchedule(dayFormattedData);
      }
    if (nightShiftData.value) {
        await updateSchedule({ ...nightFormattedData, ID: nightShiftData.value.ID });
      } else {
        await createSchedule(nightFormattedData);
      }
    message.success('保存成功');
    drawerApi.close();
    const refreshDate = dayjs(value.value || new Date()).format('YYYY-MM');
    await loadMonthData(refreshDate);
  } catch (error) {
    console.error('保存失败:', error);
    message.error(
      `保存失败: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}
</script>

<template>
  <div>
    <Calendar v-model:value="value" @panel-change="onPanelChange">
      <template #dateCellRender="{ current }">
        <div class="date-cell" @click="openDrawer(current)">
          <ul class="events">
            <li v-for="item in getListData(current)" :key="item.content" 
                :class="{ 'indented': item.indent }">
              <Badge :status="mapType(item.type)" :text="item.content" />
            </li>
          </ul>
        </div>
      </template>
    </Calendar>
    <Drawer title="硬件值班详情">
      <div class="shift-section">
        <h3 class="shift-title">白班排班</h3>
        <DayForm />
      </div>
      <Divider />
      <div class="shift-section">
        <h3 class="shift-title">夜班排班</h3>
        <NightForm />
      </div>
    </Drawer>
  </div>
</template>

<style scoped>
.events {
  padding: 0;
  margin: 0;
  list-style: none;
}
.events .ant-badge-status {
  width: 100%;
  overflow: hidden;
  font-size: 12px;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.events li.indented .ant-badge-status-text {
  padding-left: 0;
}
.shift-section {
  margin-bottom: 16px;
}
.shift-section h3 {
  margin-bottom: 16px;
  color: #1890ff;
  font-weight: 500;
}
.shift-title {
  color: #000000 !important;
  font-size: 16px;
  font-weight: normal;
  margin-bottom: 20px;
}
.date-cell {
  height: 100%;
  padding: 4px;
  cursor: pointer;
}
.date-cell:hover {
  background-color: #f5f5f5;
}
</style> 
