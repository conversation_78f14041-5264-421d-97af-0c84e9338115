import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { EntryPersonRes } from '#/api/core/ticket/types';

import { listEntryTicketsPersonsApi } from '#/api/core/ticket/fault-ticket';

/** 入室人员查询表单配置 */
export const queryEntryPersonForm: VbenFormProps = {
  collapsed: true,
  collapsedRows: 3,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入姓名' },
      fieldName: 'name',
      label: '姓名',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入身份证' },
      fieldName: 'identification_number',
      label: '身份证',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入手机号码' },
      fieldName: 'telephone',
      label: '手机号码',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入人员类型' },
      fieldName: 'type',
      label: '人员类型',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入人员类型' },
      fieldName: 'company',
      label: '公司信息',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: '禁用', value: 'disabled' },
          { label: '启用', value: 'enabled' },
        ],
      },
      fieldName: 'status',
      label: '禁止入室',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: false,
  submitOnChange: false,
  submitOnEnter: true,
  wrapperClass: 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4 grid',
};

/** 入室人员表格列配置 */
export const entryPersonsGrid: VxeGridProps<EntryPersonRes> = {
  columns: [
    { field: 'name', title: '姓名' },
    { field: 'identification_number', title: '身份证号' },
    { field: 'telephone', title: '手机号' },
    { field: 'person_type', title: '人员类型' },
    { field: 'company', title: '公司信息' },
    { field: 'status', title: '禁止入室', slots: { default: 'status' } },
  ],
  data: [],
  proxyConfig: {
    response: {
      result: 'list',
      total: 'total',
    },
    ajax: {
      async query({ page }) {
        const result = listEntryTicketsPersonsApi({
          page: page.currentPage,
          pageSize: page.pageSize,
        });
        return result;
      },
    },
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10,
  },
  sortConfig: {
    multiple: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar-buttons',
    },
    custom: true,
    // export: true,
    // import: true,
    refresh: true,
    zoom: true,
  },
};
