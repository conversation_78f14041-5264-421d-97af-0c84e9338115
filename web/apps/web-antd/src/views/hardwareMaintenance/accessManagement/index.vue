<script setup lang="ts">
import { onMounted, useTemplateRef } from 'vue';

import { AccessControl } from '@vben/access';
import { Page } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { updateEntryTicketPersonsApi } from '#/api/core/ticket/fault-ticket';

import AccessRequest from './components/entry-person.vue';
import { entryPersonsGrid } from './config';

const [Grid, gridApi] = useVbenVxeGrid({
  // formOptions: queryEntryPersonForm,
  gridOptions: entryPersonsGrid,
});
const detail = useTemplateRef('detail');

// 禁止入室人员
const handleStatusChange = async (row: any, disabled: any) => {
  const newStatus = disabled ? 'disabled' : 'enabled';
  try {
    await updateEntryTicketPersonsApi(row.id, { status: newStatus });
    row.status = newStatus; // 成功后再更新本地状态
    message.success('操作成功');
    gridApi.query(); // 重新加载数据
  } catch (error) {
    message.error('更新状态失败，请重试');
    console.error('更新失败:', error);
  }
};
const open = () => {
  detail.value!.open().then(() => {
    gridApi.query();
  });
};
onMounted(() => {
  gridApi.query();
});
</script>
<template>
  <div class="w-full">
    <Page>
      <Grid>
        <template #toolbar-buttons>
          <AccessControl :codes="['System:EntryRoomManage:Create']" type="code">
            <a-button class="mr-2" type="primary" @click="open()">
              新增
            </a-button>
          </AccessControl>
        </template>
        <template #status="{ row }">
          <a-switch
            size="small"
            checked-children="是"
            un-checked-children="否"
            :checked="row.status === 'disabled'"
            @update:checked="handleStatusChange(row, $event)"
          />
        </template>
      </Grid>
    </Page>

    <AccessRequest ref="detail" />
  </div>
</template>
