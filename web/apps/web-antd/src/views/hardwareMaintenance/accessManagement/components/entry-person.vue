<script setup lang="ts">
import type { VbenFormProps } from '#/adapter/form';

import { shallowReactive, shallowRef } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { getDataCenterTableApi } from '#/api/core/cmdb/location/dataCenter';
import { getRoomTableApi } from '#/api/core/cmdb/location/room';
import { createEntryTicketPersonsApi } from '#/api/core/ticket/fault-ticket';

const expectRef = shallowRef<{
  reject: () => void;
  resolve: () => void;
}>();

const initDetail = () => ({
  isCarryEquipment: false,
  equipment: '',
  dataCenterName: '',
  roomName: '',
});
const detail = shallowReactive(initDetail());

const dataCenterOptions = shallowRef<{ label: string; value: any }[]>([]);
const roomOptions = shallowRef<{ label: string; value: any }[]>([]);

const loadMachineRooms = async (dataCenterId: number) => {
  try {
    const result = await getRoomTableApi({
      dataCenterId,
      page: 1,
      pageSize: 1000,
    });
    roomOptions.value = result.list.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    });
  } catch (error) {
    console.error('获取房间选项失败:', error);
  }
};

/** 新增入室人员查询表单配置 */
const postEntryPersonForm: VbenFormProps = {
  collapsed: true,
  collapsedRows: 3,
  schema: [
    {
      component: 'RangePicker',
      fieldName: 'datetime',
      label: '入室时间',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择目标机房',
        options: dataCenterOptions,
        onChange(value: any, option: any) {
          detail.dataCenterName = option.label;
          loadMachineRooms(value);
        },
      },
      fieldName: 'dataCenterId',
      label: '目标机房',
      rules: 'selectRequired',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择房间',
        options: roomOptions,
        onChange(_value: any, option: any) {
          detail.roomName = option.label;
        },
      },
      fieldName: 'roomId',
      label: '房间',
      rules: 'selectRequired',
      dependencies: {
        triggerFields: ['dataCenterId'],
        show(values) {
          return !!values.dataCenterId;
        },
        trigger(values) {
          values.roomId = void 0;
        },
      },
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入姓名' },
      fieldName: 'name',
      label: '姓名',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入身份证' },
      fieldName: 'identification_number',
      label: '身份证',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入手机号码' },
      fieldName: 'telephone',
      label: '手机号码',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入人员类型' },
      fieldName: 'person_type',
      label: '人员类型',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入公司信息' },
      fieldName: 'company',
      label: '公司信息',
      rules: 'required',
    },
    {
      component: 'Switch',
      componentProps: {
        options: [
          {
            label: '是',
            value: 'disabled',
          },
          {
            label: '否',
            value: 'enabled',
          },
        ],
      },
      defaultValue: 'enabled',
      fieldName: 'status',
      label: '禁止入室',
    },
  ],
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  submitButtonOptions: {
    show: false,
  },
  resetButtonOptions: {
    show: false,
  },
  layout: 'horizontal',
  wrapperClass: 'grid-cols-1',
};

const loadDataCenters = async () => {
  try {
    const res = await getDataCenterTableApi({ page: 1, pageSize: 1000 });
    dataCenterOptions.value = res.list.map((item) => ({
      label: `${item.name}${item.az ? ` (${item.az.name})` : ''}`,
      value: item.id,
    }));
  } catch (error) {
    console.error('获取机房选项失败:', error);
  }
};

const [BaseForm, formApi] = useVbenForm(postEntryPersonForm);
const [Drawer, drawerApi] = useVbenDrawer({
  onOpened: async () => {
    loadDataCenters();
  },
  onConfirm: async () => {
    drawerApi.setState({
      confirmLoading: true,
    });
    try {
      const { valid } = await formApi.validate();
      if (!valid) {
        return;
      }
      const values = await formApi.getValues();
      // console.log(values);
      await createEntryTicketPersonsApi([
        {
          name: values.name,
          identification_number: values.identification_number,
          telephone: values.telephone,
          person_type: values.person_type,
          company: values.company,
          status: values.status,
          entry_start_time: values.datetime[0].toISOString(),
          entry_end_time: values.datetime[1].toISOString(),
          dataCenterName: detail.dataCenterName,
          roomName: detail.roomName,
        },
      ]);
    } finally {
      drawerApi.setState({
        confirmLoading: false,
      });
    }
    message.success('提交成功');
    drawerApi.close();
    expectRef.value!.resolve();
  },
});

const handleStatusChange = (disabled: any) => {
  formApi.setFieldValue('status', disabled ? 'disabled' : 'enabled');
};

defineExpose({
  open(): Promise<void> {
    drawerApi.open();
    return new Promise((resolve, reject) => {
      expectRef.value = {
        resolve,
        reject,
      };
    });
  },
});
</script>

<template>
  <Drawer class="w-[600px]" title="入室人员">
    <Page class="w-full">
      <BaseForm>
        <template #status="{ value }">
          <a-switch
            size="small"
            :checked="value === 'disabled'"
            checked-children="是"
            un-checked-children="否"
            @update:checked="handleStatusChange($event)"
          />
        </template>
      </BaseForm>
    </Page>
  </Drawer>
</template>
