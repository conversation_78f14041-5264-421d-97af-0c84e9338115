import type { RepairType } from '#/api/core/ticket/repair-ticket';

// 格式化持续时间（分钟转为小时和分钟）
// 导入类型映射
import { repairTypeMap } from './type';

export function formatDuration(minutes: number) {
  if (!minutes) return '-';

  const hours = Math.floor(minutes / 60);
  const mins = Math.floor(minutes % 60);

  return hours > 0
    ? `${hours}小时${mins > 0 ? ` ${mins}分钟` : ''}`
    : `${mins}分钟`;
}

// 格式化日期时间
export function formatDateTime(dateTime: null | string | undefined) {
  if (!dateTime) return '-';
  return new Date(dateTime)
    .toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    })
    .replaceAll('/', '-');
}

// 转换图片为base64
export const getBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const FR = typeof FileReader === 'undefined' ? null : FileReader;
    if (!FR) {
      reject(new Error('FileReader not supported'));
      return;
    }
    const reader = new FR();
    reader.addEventListener('load', () => resolve(reader.result as string));
    reader.addEventListener('error', (error) => reject(error));
    reader.readAsDataURL(file);
  });
};

// 安全地获取维修类型信息
export function getSafeRepairTypeInfo(
  type: string | undefined,
  property: 'color' | 'text',
): string {
  if (!type) return property === 'color' ? 'blue' : '未知';

  // 检查是否为有效的RepairType
  const isValidType = Object.keys(repairTypeMap).includes(type);
  if (isValidType) {
    // 使用类型断言将字符串转换为RepairType
    return repairTypeMap[type as RepairType]?.[property] || type;
  }

  return property === 'color' ? 'blue' : type;
}

// 获取图标名称
export function getStatusIcon(status: string | undefined) {
  if (!status) return '';

  if (status === 'completed') return 'check-circle';
  if (status === 'failed' || status === 'cancelled') return 'close-circle';
  if (status.includes('hardware_replace') || status === 'in_progress')
    return 'setting';
  if (status.includes('waiting')) return 'clock-circle';
  if (status.includes('restart')) return 'reload';

  return '';
}
