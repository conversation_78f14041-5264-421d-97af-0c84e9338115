import type { RepairTicket } from '#/api/core/ticket/repair-ticket';

import { ref } from 'vue';

import { message } from 'ant-design-vue';

import { uploadFile } from '#/api/core/upload';

// 添加维修模板配置
export const repairTemplates = {
  memory: {
    name: '内存故障',
    templates: [
      {
        solution: '插拔或更换内存条',
        steps: '1. 确定故障内存槽位并更换。\n2. 确认更换后内存正常。',
      },
      {
        solution: '插拔或更换内存条',
        steps: '1. 插拔对调报错内存条。\n2. 确认内存正常。',
      },
    ],
  },
  network: {
    name: '网卡故障',
    templates: [
      {
        solution: '更换故障网卡',
        steps:
          '1. 确定故障网卡位置并更换。\n2. 更改网卡模式为Ethernet模式（rdma网卡）。\n3. 确认更换后网卡状态正常。',
      },
      {
        solution: '清洁网口',
        steps: '1. 插拔并清洁网口。',
      },
      {
        solution: '更换网卡pci板卡',
        steps:
          '1. 对调和更换降速网卡后该位置仍然降速。\n2. 更换网卡pci板卡后速率正常。\n3. 更改网卡模式为Ethernet模式。\n4. 确认更换后网卡状态正常。',
      },
    ],
  },
  disk: {
    name: '硬盘故障',
    templates: [
      {
        solution: '重启机器',
        steps: '1. 重启后硬盘正常识别。',
      },
      {
        solution: '更换故障硬盘',
        steps: '1. 确定故障硬盘槽位并更换。\n2. 确认更换后的硬盘状态正常。',
      },
      {
        solution: '插拔或更换线缆',
        steps: '1. 重新插拔硬盘插槽线路恢复。\n2. 重新更换硬盘插槽线路恢复。',
      },
    ],
  },
  fan: {
    name: '风扇故障',
    templates: [
      {
        solution: '更换故障风扇',
        steps: '1. 查找报错风扇对应位置。\n2. 更换故障风扇并确认状态正常。',
      },
      {
        solution: '插拔电源、风扇及交换板',
        steps:
          '1. 插拔和对调电源及风扇后仍然报错。\n2. 插拔交换板风扇恢复正常。',
      },
      {
        solution: '更换交换板',
        steps:
          '1. 插拔对调及更换电源和风扇后仍然报错。\n2. 插拔交换板后仍然报错。\n3. 更换交换板后风扇恢复正常。',
      },
    ],
  },
  power: {
    name: '电源故障',
    templates: [
      {
        solution: '插拔电源',
        steps: '1. 插拔报错电源后正常。',
      },
      {
        solution: '更换故障电源',
        steps: '1. 插拔电源后仍然故障。\n2. 更换故障电源后正常。',
      },
    ],
  },
  cpu: {
    name: 'CPU故障',
    templates: [
      {
        solution: '断电重启',
        steps: '1. 收集日志后断电重启。\n2. 确认重启后CPU恢复正常。',
      },
      {
        solution: '更换故障CPU',
        steps:
          '1. 收集日志后断电重启仍然故障。\n2. CPU1和CPU2对调后仍然故障。\n3. 更换报错CPU恢复正常。',
      },
    ],
  },
  motherboard: {
    name: '主板故障',
    templates: [
      {
        solution: '更改BIOS配置',
        steps: '1. 更改BIOS相关配置。\n2. 确认更换BIOS配置后正常。',
      },
      {
        solution: '主板放电',
        steps: '1. 扣掉主板电池放电。\n2. 确认故障消失。',
      },
      {
        solution: '更换主板',
        steps:
          '1. 扣掉主板电池放电后仍然故障。\n2. 更换主板后恢复正常。\n3. 修改SN与主机标签一致。\n4. 修改BMC相关配置。',
      },
    ],
  },
  gpu: {
    name: 'GPU卡故障',
    templates: [
      {
        solution: '重启压测',
        steps: '1. 重启后GPU正常识别。\n2. 压测通过。',
      },
      {
        solution: '压测失败后更换故障GPU',
        steps:
          '1. 重启后GPU正常识别。\n2. 压测失败，查找失败原因。\n3. 更换故障GPU。\n4. 压测通过。',
      },
      {
        solution: '更换报错GPU',
        steps: '1. 查找出报错GPU。\n2. 更换故障GPU。\n3. 压测通过。',
      },
      {
        solution: '对调GPU后压测',
        steps: '1. 对调掉卡GPU槽位后识别。\n2. 压测通过。',
      },
      {
        solution: '对调不识别后更换故障GPU',
        steps:
          '1. 对调掉卡GPU槽位后该GPU仍不识别。\n2. 更换故障GPU。\n3. 压测通过。',
      },
      {
        solution: '更换GPU底座',
        steps:
          '1. 对调掉卡GPU后原槽位GPU不识别。\n2. 更换GPU底板后GPU正常识别。',
      },
    ],
  },
  other: {
    name: '其它故障',
    templates: [
      {
        solution: '其它',
        steps: '1. 其它。',
      },
    ],
  },
};

// 模态框状态管理
export function useModalState() {
  const hardwareReplaceModalVisible = ref(false);
  const completeRepairModalVisible = ref(false);
  const hardwareReplaceFailedModalVisible = ref(false);
  const transferTicketModalVisible = ref(false);
  const repairSelectionModalVisible = ref(false);
  const verifyRepairModalVisible = ref(false);
  const currentRepairTicket = ref<null | RepairTicket>(null);

  // 设置当前处理的工单
  const setCurrentTicket = (ticket: RepairTicket) => {
    currentRepairTicket.value = ticket;
  };

  // 显示硬件更换模态框
  const showHardwareReplaceModal = (
    ticket: RepairTicket,
    hardwareReplaceForm: any,
  ) => {
    setCurrentTicket(ticket);
    // 重置表单数据
    hardwareReplaceForm.spare_id = undefined;
    hardwareReplaceForm.new_part_sn = '';
    hardwareReplaceForm.old_part_sn = '';
    hardwareReplaceModalVisible.value = true;
  };

  // 显示完成维修模态框
  const showCompleteRepairModal = (
    ticket: RepairTicket,
    completeRepairForm: any,
  ) => {
    setCurrentTicket(ticket);
    // 重置表单数据
    completeRepairForm.repair_result = 'success';
    completeRepairForm.solution = '';
    completeRepairForm.repair_steps = '';
    completeRepairForm.images = [];

    completeRepairModalVisible.value = true;

    // 验证表单状态
    setTimeout(() => {}, 100);
  };

  // 显示硬件更换失败模态框
  const showHardwareReplaceFailedModal = (
    ticket: RepairTicket,
    hardwareReplaceFailedForm: any,
  ) => {
    setCurrentTicket(ticket);
    // 重置表单数据
    hardwareReplaceFailedForm.reason = '';
    hardwareReplaceFailedModalVisible.value = true;
  };

  // 显示转单模态框
  const showTransferTicketModal = (ticket: RepairTicket, transferForm: any) => {
    setCurrentTicket(ticket);
    // 重置表单数据
    transferForm.engineer_id = undefined;
    transferTicketModalVisible.value = true;
  };

  // 显示故障说明模态框
  const showRepairSelectionModal = () => {
    repairSelectionModalVisible.value = true;
  };

  // 显示验证维修模态框
  const showVerifyRepairModal = (ticket: RepairTicket, verifyForm: any) => {
    setCurrentTicket(ticket);
    // 重置表单数据
    verifyForm.success = true;
    verifyForm.comments = '';
    verifyRepairModalVisible.value = true;
  };

  return {
    hardwareReplaceModalVisible,
    completeRepairModalVisible,
    hardwareReplaceFailedModalVisible,
    transferTicketModalVisible,
    repairSelectionModalVisible,
    verifyRepairModalVisible,
    currentRepairTicket,
    setCurrentTicket,
    showHardwareReplaceModal,
    showCompleteRepairModal,
    showHardwareReplaceFailedModal,
    showTransferTicketModal,
    showRepairSelectionModal,
    showVerifyRepairModal,
  };
}

// 处理图片上传
export async function handleImageUpload(
  images: any[],
  moduleType: string,
  moduleId: number,
) {
  if (!images || images.length === 0) return [];

  // 检查是否有图片需要上传
  const pendingUploads = images.filter(
    (file) => !file.response && file.originFileObj,
  );

  if (pendingUploads.length > 0) {
    message.loading('正在上传图片，请稍候...', 0);

    try {
      // 使用新的上传API，添加模块类型和ID
      const uploadPromises = pendingUploads.map((file) => {
        return uploadFile(
          file.originFileObj,
          moduleType,
          moduleId,
          '维修记录图片',
        ).then((result) => {
          file.status = 'done';
          file.response = result;
          return result;
        });
      });

      // 等待所有上传完成
      await Promise.all(uploadPromises);
      message.destroy(); // 关闭加载提示

      // 返回所有图片URL
      return images
        .map((file) => {
          // 处理不同格式的响应结构
          if (file.response?.url) {
            return file.response.url;
          } else if (file.response?.data?.url) {
            return file.response.data.url;
          } else if (file.url) {
            return file.url;
          }
          return '';
        })
        .filter(Boolean);
    } catch (error) {
      message.destroy(); // 关闭加载提示
      console.error('图片上传失败', error);
      message.error('部分图片上传失败，请重试');
      return [];
    }
  }

  // 没有需要上传的图片，返回已有URL
  return images.map((file) => file.url || '').filter(Boolean);
}

// 处理图片粘贴事件
export function setupPasteEventHandler(
  targetElement: HTMLElement | Window = window,
  callback: (file: any) => void,
) {
  const FR = typeof FileReader === 'undefined' ? null : FileReader;

  // 如果不支持FileReader，直接返回空函数
  if (!FR) {
    console.warn('当前环境不支持FileReader，无法粘贴图片');
    return () => {};
  }

  const handlePaste = (e: ClipboardEvent) => {
    try {
      const items = e.clipboardData?.items;
      if (!items) return;

      // 遍历粘贴的数据
      for (const item of items) {
        // 判断是否为图片
        if (item && item.kind === 'file' && item.type.includes('image')) {
          const file = item.getAsFile();
          if (!file) continue;

          // 检查文件大小
          const isLt20M = file.size / 1024 / 1024 < 20;
          if (!isLt20M) {
            message.error('图片大小不能超过 20MB!');
            continue;
          }

          // 创建本地预览
          const reader = new FR();
          reader.addEventListener('load', (e) => {
            try {
              const result = e.target?.result as string;
              // 创建自定义上传对象
              const uploadFile = {
                uid: `paste-${Date.now()}`,
                name: `paste-${Date.now()}.png`,
                status: 'done', // 标记为已完成，但实际上还未上传
                originFileObj: file,
                url: result, // 本地预览URL
                preview: result, // 添加preview属性以支持预览
              } as any;

              // 回调函数处理图片
              callback(uploadFile);
            } catch (error) {
              console.error('处理粘贴图片预览失败', error);
              message.error('无法处理粘贴的图片');
            }
          });

          reader.addEventListener('error', () => {
            message.error('读取粘贴的图片失败');
          });

          reader.readAsDataURL(file);

          // 只处理第一个图片
          break;
        }
      }
    } catch (error) {
      console.error('粘贴处理失败', error);
    }
  };

  // 添加粘贴事件监听器
  targetElement.addEventListener('paste', handlePaste as EventListener);

  // 返回清理函数
  return () => {
    targetElement.removeEventListener('paste', handlePaste as EventListener);
  };
}
