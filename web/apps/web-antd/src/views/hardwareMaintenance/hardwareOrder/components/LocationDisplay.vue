<script lang="ts" setup>
import type { PropType } from 'vue';

import type { Cabinet } from '#/api/core/cmdb/location/cabinet';

import { ref, watch } from 'vue';

import { Alert, Card, Spin, Typography } from 'ant-design-vue';

import { getCabinetDetailApi } from '#/api/core/cmdb/location/cabinet';

const props = defineProps({
  cabinetId: {
    type: Number as PropType<null | number>,
    default: null,
  },
});

const { Text, Title } = Typography;

const loading = ref(false);
const cabinetData = ref<Cabinet | null>(null);
const error = ref<null | string>(null);

// 监听cabinetId变化
watch(
  () => props.cabinetId,
  (newVal) => {
    if (newVal) {
      const cabinetIdNumber = Number(newVal);
      if (Number.isNaN(cabinetIdNumber)) {
        console.error('机柜ID不能转换为数字:', newVal);
        error.value = '无效的机柜ID';
      } else {
        loadCabinetData(cabinetIdNumber);
      }
    }
  },
  { immediate: true },
);

// 格式化位置信息为字符串
function formatLocationInfo(cabinet: Cabinet | null): string {
  if (!cabinet) return '';

  const parts: string[] = [];

  if (cabinet.room?.dataCenter?.az?.region?.name) {
    parts.push(cabinet.room.dataCenter.az.region.name);
  }

  if (cabinet.room?.dataCenter?.az?.name) {
    parts.push(cabinet.room.dataCenter.az.name);
  }

  if (cabinet.room?.dataCenter?.name) {
    parts.push(cabinet.room.dataCenter.name);
  }

  if (cabinet.room?.name) {
    parts.push(cabinet.room.name);
  }

  if (cabinet.name) {
    parts.push(cabinet.name);
  }

  if (cabinet.row && cabinet.column) {
    parts.push(`${cabinet.row}排${cabinet.column}列`);
  }

  return parts.join(' / ');
}

async function loadCabinetData(cabinetId: number) {
  loading.value = true;
  error.value = null;

  try {
    const response = await getCabinetDetailApi(cabinetId);

    // 根据实际API返回格式处理数据
    if (response) {
      // 尝试使用类型断言处理不同的响应格式
      const apiResponse = response as any; // 使用any类型暂时绕过类型检查

      // 检查是否直接返回了Cabinet对象
      if (apiResponse.id && apiResponse.name) {
        cabinetData.value = apiResponse as Cabinet;
      }
      // 检查response.data是否是Cabinet对象
      else if (apiResponse.data && apiResponse.data.id) {
        cabinetData.value = apiResponse.data as Cabinet;
      } else {
        console.warn('API返回数据格式无法解析:', apiResponse);
        error.value = '获取机柜数据格式异常';
      }
    } else {
      console.warn('API未返回数据');
      error.value = '获取机柜数据失败';
    }
  } catch (error_) {
    console.error('加载机柜信息失败:', error_);
    error.value = '加载机柜信息失败';
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <Spin :spinning="loading">
    <Card
      v-if="props.cabinetId"
      class="mb-4 rounded-lg shadow-md transition-shadow duration-300 hover:shadow-lg"
    >
      <template #title>
        <div class="flex items-center">
          <span class="anticon mr-2 text-blue-500"
            ><i class="anticon-environment"></i
          ></span>
          <Title :level="5" class="m-0 text-gray-800 dark:text-gray-200">
            位置信息
          </Title>
        </div>
      </template>

      <Alert v-if="error" type="error" :message="error" class="mb-4" />

      <div v-if="!error && cabinetData" class="location-info p-1">
        <!-- 完整位置显示 -->
        <div class="mb-3 rounded-lg bg-gray-50 p-3 dark:bg-gray-800">
          <Text strong class="text-gray-700 dark:text-gray-300">
            完整位置：
          </Text>
          <Text class="text-gray-900 dark:text-gray-100">
            {{ formatLocationInfo(cabinetData) }}
          </Text>
        </div>

        <!-- 更紧凑的布局 - 使用两列显示 -->
        <div class="grid grid-cols-2 gap-x-6 gap-y-3 p-2">
          <div
            v-if="cabinetData?.room?.dataCenter?.az?.region?.name"
            class="flex items-center"
          >
            <Text type="secondary" class="min-w-24">区域：</Text>
            <Text class="font-medium">
              {{ cabinetData.room.dataCenter.az.region.name }}
            </Text>
          </div>

          <div
            v-if="cabinetData?.room?.dataCenter?.az?.name"
            class="flex items-center"
          >
            <Text type="secondary" class="min-w-24">可用区：</Text>
            <Text class="font-medium">
              {{ cabinetData.room.dataCenter.az.name }}
            </Text>
          </div>

          <div
            v-if="cabinetData?.room?.dataCenter?.name"
            class="flex items-center"
          >
            <Text type="secondary" class="min-w-24">机房：</Text>
            <Text class="font-medium">
              {{ cabinetData.room.dataCenter.name }}
            </Text>
          </div>

          <div v-if="cabinetData?.room?.name" class="flex items-center">
            <Text type="secondary" class="min-w-24">包间：</Text>
            <Text class="font-medium">{{ cabinetData.room.name }}</Text>
          </div>

          <div v-if="cabinetData?.name" class="flex items-center">
            <Text type="secondary" class="min-w-24">机柜：</Text>
            <Text class="font-medium">{{ cabinetData.name }}</Text>
          </div>

          <div
            v-if="cabinetData?.row || cabinetData?.column"
            class="flex items-center"
          >
            <Text type="secondary" class="min-w-24">位置：</Text>
            <Text class="font-medium">
              {{ cabinetData.row ? `${cabinetData.row}行` : '' }}
              {{ cabinetData.column ? `${cabinetData.column}列` : '' }}
            </Text>
          </div>
        </div>
      </div>

      <div v-else-if="!error && !loading">
        <Alert message="暂无位置信息" type="info" />
      </div>
    </Card>

    <div v-else-if="!props.cabinetId && !loading">
      <Alert message="未提供机柜ID，无法获取位置信息" type="warning" />
    </div>
  </Spin>
</template>

<style scoped>
.location-info {
  @apply rounded-lg;
}
</style>
