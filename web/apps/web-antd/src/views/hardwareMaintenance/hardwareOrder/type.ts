import type {
  RepairTicketStatus,
  RepairType,
} from '#/api/core/ticket/repair-ticket';

// 状态映射
export const statusMap: Record<
  RepairTicketStatus,
  { color: string; text: string }
> = {
  waiting_authorization: { text: '待授权', color: 'gray' },
  waiting_accept: { text: '待受理', color: 'blue' },
  assigned: { text: '已受理', color: 'blue' },
  in_progress: { text: '维修中', color: 'processing' },
  replacing_hardware: { text: '硬件更换中', color: 'processing' },
  hardware_replace_completed: { text: '硬件维修完成', color: 'processing' },
  hardware_replace_failed: { text: '硬件维修失败', color: 'error' },
  restarting: { text: '重启中', color: 'processing' },
  restart_completed: { text: '重启完成', color: 'processing' },
  restart_failed: { text: '重启失败', color: 'error' },
  waiting_verification: { text: '维修验证', color: 'warning' },
  completed: { text: '已完成', color: 'success' },
  failed: { text: '失败', color: 'error' },
  cancelled: { text: '已取消', color: 'default' },
};

// 维修类型映射
export const repairTypeMap: Record<
  RepairType,
  { color: string; text: string }
> = {
  restart: { text: '重启', color: 'cyan' },
  hardware_fix: { text: '硬件维修', color: 'orange' },
};

// 维修类型选项
export const repairTypeOptions = [
  { label: '重启', value: 'restart' },
  { label: '硬件维修', value: 'hardware_fix' },
];

// 资产类型选项
export const assetTypeOptions = [
  { label: '算力服务器', value: 'server' },
  { label: '计算服务器', value: 'gpu_server' },
  { label: '存储设备', value: 'storage' },
  { label: '其他', value: 'other' },
];
