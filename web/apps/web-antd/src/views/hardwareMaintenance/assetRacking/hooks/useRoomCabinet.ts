import { reactive, ref } from 'vue';

import { message } from 'ant-design-vue';

import {
  getCabinetDetailApi,
  getCabinetTableApi,
} from '#/api/core/cmdb/location/cabinet';
import { getRoomTableApi } from '#/api/core/cmdb/location/room';

/**
 * 机房和机柜管理钩子
 */
export const useRoomCabinet = () => {
  const roomOptions = ref<{ label: string; value: number }[]>([]);
  const cabinetOptions = ref<{ label: string; value: number }[]>([]);
  const loadingRooms = ref(false);
  const loadingCabinets = ref(false);
  const roomCabinetMap = reactive(
    new Map<number, { height?: number; label: string; value: number }[]>(),
  );

  /**
   * 获取所有机房
   */
  async function fetchRooms() {
    if (roomOptions.value.length > 0) return;

    loadingRooms.value = true;
    try {
      const res = await getRoomTableApi({ page: 1, pageSize: 1000 });

      // 根据服务器页面的数据结构来处理
      if (res && res.list && Array.isArray(res.list)) {
        roomOptions.value = res.list.map((item: any) => ({
          label: `${item.name}${item.dataCenter ? ` (${item.dataCenter.name})` : ''}`,
          value: item.id,
        }));
      } else {
        console.error('机房API返回格式异常:', res);
        roomOptions.value = [];
      }
    } catch (error: any) {
      console.error('获取机房数据失败:', error);
      message.error(`获取机房数据失败: ${error.message || '未知错误'}`);
      roomOptions.value = [];
    } finally {
      loadingRooms.value = false;
    }
  }

  /**
   * 根据机房ID获取机柜
   */
  async function fetchCabinetsByRoomId(roomId: number) {
    if (!roomId) return;

    // 如果已经加载过这个机房的机柜数据，则直接使用缓存
    if (roomCabinetMap.has(roomId)) {
      cabinetOptions.value = roomCabinetMap.get(roomId) || [];
      return;
    }

    loadingCabinets.value = true;
    try {
      // 导入机柜API
      const params = { page: 1, pageSize: 100, roomID: roomId };
      const res = await getCabinetTableApi(params);

      // 根据返回数据结构处理
      let cabinets: any[] = [];
      if (res && res.list && Array.isArray(res.list)) {
        cabinets = res.list.map((item: any) => ({
          label: item.name,
          value: item.id,
          height: item.capacityUnits,
        }));
      } else {
        console.error('机柜API返回格式异常:', res);
      }

      // 缓存机柜数据
      roomCabinetMap.set(roomId, cabinets);
      cabinetOptions.value = cabinets;
    } catch (error: any) {
      console.error('获取机柜数据失败:', error);
      message.error(`获取机柜数据失败: ${error.message || '未知错误'}`);
      cabinetOptions.value = [];
    } finally {
      loadingCabinets.value = false;
    }
  }

  /**
   * 获取机柜详情并设置设备高度
   */
  async function getCabinetDetailAndSetHeight(
    cabinetId: number,
    editForm: { items: any[] },
    rowIndex: number,
  ) {
    if (!cabinetId) return;

    try {
      const res = await getCabinetDetailApi(cabinetId);
      const cabinet = res.data || {};
      if (cabinet && cabinet.capacityUnits && editForm.items[rowIndex]) {
        editForm.items[rowIndex].rackPosition = cabinet.capacityUnits;
      }
    } catch (error: any) {
      console.error(`获取机柜详情失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 处理机房选择变化
   */
  function handleRoomChange(
    value: any,
    editForm: { items: any[] },
    index?: number,
  ) {
    if (!value) return;

    // 如果没有提供索引，尝试查找
    if (index === undefined) {
      // 获取当前编辑的索引
      const currentIndex = editForm.items.findIndex(
        (item: any) => item.roomID === value,
      );
      if (currentIndex === -1) return;
      index = currentIndex;
    }

    // 清空该行的机柜选择
    editForm.items[index].cabinetID = undefined;

    // 加载该机房的机柜列表
    fetchCabinetsByRoomId(value);
  }

  /**
   * 处理机柜选择变化
   */
  function handleCabinetChange(
    value: any,
    roomId: number,
    editForm: { items: any[] },
    rowIndex: number,
  ) {
    if (!value) return;

    // 根据机柜ID查找对应机柜数据
    const cabinets = roomCabinetMap.get(roomId) || [];
    const cabinet = cabinets.find((cab) => cab.value === value);

    // 如果找到机柜数据且有高度信息，则自动填充机架位置
    if (cabinet && cabinet.height && editForm.items[rowIndex]) {
      editForm.items[rowIndex].rackPosition = cabinet.height;
    } else {
      // 如果缓存中没有高度信息，则通过API获取
      getCabinetDetailAndSetHeight(value, editForm, rowIndex);
    }
  }

  return {
    roomOptions,
    cabinetOptions,
    loadingRooms,
    loadingCabinets,
    roomCabinetMap,
    fetchRooms,
    fetchCabinetsByRoomId,
    getCabinetDetailAndSetHeight,
    handleRoomChange,
    handleCabinetChange,
  };
};
