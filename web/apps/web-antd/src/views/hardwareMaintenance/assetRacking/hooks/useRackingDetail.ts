import type { ButtonType } from 'ant-design-vue/es/button';

import { computed, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  getRackingTicketDetailApi,
  getRackingTicketHistoryApi,
  transitionRackingTicketApi,
} from '#/api/core/hardWareMaint/racking';

import { createDeviceGridConfig } from '../config/gridConfig';
import { STATUS_MAP } from '../constants/statusMaps';

/**
 * 资产上架详情页面逻辑钩子
 */
export const useRackingDetail = (ticketId: number) => {
  const router = useRouter();
  const loading = ref(false);
  const ticket = ref<any>(null);

  // 控制卡片展开/折叠状态
  type CardVisibilityKeys = 'baseInfo' | 'devices' | 'history' | 'operations';
  const cardVisible = ref({
    baseInfo: true,
    devices: true,
    history: true,
    operations: true,
  });

  // 确认对话框状态
  const confirmDialogVisible = ref(false);
  const confirmDialogTitle = ref('');
  const confirmDialogContent = ref('');
  const confirmDialogLoading = ref(false);
  const pendingAction = ref<
    ((status: string, comment: string) => Promise<void>) | null
  >(null);
  const pendingStatus = ref('');
  const pendingComment = ref('');

  // 编辑模式相关状态
  const isEditMode = ref(false);
  const tableEditMode = ref(false);

  // 编辑表单
  const editForm = reactive({
    items: [] as any[],
  });

  // 机房和机柜相关状态
  const roomOptions = ref<{ label: string; value: number }[]>([]);
  const cabinetOptions = ref<{ label: string; value: number }[]>([]);
  const loadingRooms = ref(false);
  const loadingCabinets = ref(false);
  const roomCabinetMap = reactive(
    new Map<number, { height?: number; label: string; value: number }[]>(),
  );

  // 设备表格
  const deviceGridOptions = createDeviceGridConfig();
  const [DeviceGrid, deviceGridApi] = useVbenVxeGrid({
    gridOptions: deviceGridOptions,
  });

  // 根据当前状态计算步骤
  const currentStep = computed(() => {
    if (!ticket.value) return 0;
    switch (ticket.value.status) {
      case 'APPROVED': {
        return 1;
      }
      case 'COMPLETED': {
        return 3;
      }
      case 'IN_PROGRESS': {
        return 2;
      }
      case 'PENDING': {
        return 0;
      }
      default: {
        return 0;
      }
    }
  });

  // 计算可以进行的下一步操作
  const possibleNextActions = computed(() => {
    if (!ticket.value) return [];

    switch (ticket.value.status) {
      case 'APPROVED': {
        return [
          {
            status: 'IN_PROGRESS',
            label: '开始上架',
            type: 'primary' as ButtonType,
          },
        ];
      }
      case 'IN_PROGRESS': {
        return [
          {
            status: 'COMPLETED',
            label: '完成上架',
            type: 'default' as ButtonType,
          },
        ];
      }
      case 'PENDING': {
        return [
          {
            status: 'APPROVED',
            label: '审核通过',
            type: 'primary' as ButtonType,
          },
          { status: 'REJECTED', label: '拒绝', type: 'default' as ButtonType },
        ];
      }
      default: {
        return [];
      }
    }
  });

  // 操作历史相关
  const historyList = ref<any[]>([]);
  const historyLoading = ref(false);

  // 获取操作历史
  async function fetchHistory() {
    if (!ticketId) {
      message.error('无效的工单ID');
      historyList.value = [];
      return;
    }
    historyLoading.value = true;
    try {
      const data = await getRackingTicketHistoryApi(ticketId);
      historyList.value = data;
    } catch (error: any) {
      message.error(`获取操作历史失败: ${error.message || '未知错误'}`);
      historyList.value = [];
    } finally {
      historyLoading.value = false;
    }
  }

  // 获取工单详情
  async function getTicketDetail() {
    if (!ticketId) {
      message.error('无效的工单ID');
      router.push('/hardware-maintenance/asset-racking');
      return;
    }

    loading.value = true;
    try {
      const data = await getRackingTicketDetailApi(ticketId);
      ticket.value = data;

      // 更新设备表格数据
      if (deviceGridApi && data.items) {
        deviceGridApi.setGridOptions({
          data: data.items || [],
        });
      }
    } catch (error: any) {
      message.error(`获取工单详情失败: ${error.message || '未知错误'}`);
      router.push('/hardware-maintenance/asset-racking');
    } finally {
      loading.value = false;
    }
  }

  // 切换卡片展开状态
  const toggleCardVisibility = (key: CardVisibilityKeys) => {
    cardVisible.value[key] = !cardVisible.value[key];
  };

  // 显示确认对话框
  function showConfirmDialog(status: string, actionLabel: string) {
    confirmDialogTitle.value = actionLabel;
    confirmDialogContent.value = `确定要将工单状态更新为${STATUS_MAP[status] || status}吗？`;
    confirmDialogVisible.value = true;
    pendingStatus.value = status;
    pendingComment.value = '';
    pendingAction.value = transitionStatus;

    // 如果是完成上架，进入编辑模式
    if (status === 'COMPLETED' && ticket.value?.items) {
      isEditMode.value = true;
      // 准备编辑表单数据
      editForm.items = ticket.value.items.map((item: any) => ({
        id: item.assetID,
        sn: item.device?.sn || '',
        brand: item.device?.brand || '',
        assetType: item.device?.assetType || '',
        status: item.status || '',
        roomID: item.roomId || 0,
        cabinetID: item.cabinetId || 0,
        rackPosition: item.rackPosition || 0,
        vpcMAC: item.vpcMac || '',
        bmcMAC: item.bmcMac || '',
        unitHeight: item.unitHeight || 0,
      }));
    } else {
      isEditMode.value = false;
    }
  }

  // 取消确认对话框
  function cancelConfirmDialog() {
    confirmDialogVisible.value = false;
    confirmDialogLoading.value = false;
    isEditMode.value = false;
  }

  // 确认对话框确认
  async function confirmDialog() {
    if (pendingAction.value && pendingStatus.value) {
      confirmDialogLoading.value = true;

      // 如果是完成操作并且有编辑设备数据，使用编辑后的数据
      if (
        pendingStatus.value === 'COMPLETED' &&
        isEditMode.value &&
        editForm.items.length > 0
      ) {
        const params: any = {
          status: pendingStatus.value,
          comment: pendingComment.value,
          rackItems: editForm.items.map((item) => ({
            assetID: item.id,
            status: item.status,
            roomID: item.roomID,
            cabinetID: item.cabinetID,
            rackPosition: item.rackPosition,
            vpcMAC: item.vpcMAC || '',
            bmcMAC: item.bmcMAC || '',
          })),
        };

        try {
          await transitionRackingTicketApi(ticketId, params);
          message.success('状态更新成功');
          isEditMode.value = false;
          confirmDialogVisible.value = false;
          confirmDialogLoading.value = false;
          getTicketDetail();
        } catch (error: any) {
          message.error(`状态更新失败: ${error.message || '未知错误'}`);
          confirmDialogLoading.value = false;
        }
      } else {
        // 其他状态变更使用原来的方法
        await pendingAction.value(pendingStatus.value, pendingComment.value);
        confirmDialogVisible.value = false;
        confirmDialogLoading.value = false;
      }
    }
  }

  // 状态转换
  async function transitionStatus(status: string, comment: string) {
    if (!ticketId) return;

    try {
      // 构建请求参数
      const params: any = {
        status,
        comment,
      };

      // 如果是完成上架，需要提供设备上架信息
      if (status === 'COMPLETED') {
        params.rackItems = editForm.items.map((item) => ({
          assetID: item.id,
          status: item.status,
          roomID: item.roomID,
          cabinetID: item.cabinetID,
          rackPosition: item.rackPosition,
          vpcMAC: item.vpcMAC || '',
          bmcMAC: item.bmcMAC || '',
        }));
      }

      await transitionRackingTicketApi(ticketId, params);
      message.success('状态更新成功');
      isEditMode.value = false; // 退出编辑模式
      tableEditMode.value = false; // 退出表格编辑模式
      getTicketDetail();
    } catch (error: any) {
      message.error(`状态更新失败: ${error.message || '未知错误'}`);
    }
  }

  // 返回列表
  function backToList() {
    router.push('/hardware-maintenance/asset-racking');
  }

  return {
    loading,
    ticket,
    cardVisible,
    confirmDialogVisible,
    confirmDialogTitle,
    confirmDialogContent,
    confirmDialogLoading,
    pendingStatus,
    pendingComment,
    isEditMode,
    tableEditMode,
    editForm,
    roomOptions,
    cabinetOptions,
    loadingRooms,
    loadingCabinets,
    roomCabinetMap,
    currentStep,
    possibleNextActions,
    DeviceGrid,
    deviceGridApi,
    getTicketDetail,
    toggleCardVisibility,
    showConfirmDialog,
    cancelConfirmDialog,
    confirmDialog,
    transitionStatus,
    backToList,
    historyList,
    historyLoading,
    fetchHistory,
  };
};
