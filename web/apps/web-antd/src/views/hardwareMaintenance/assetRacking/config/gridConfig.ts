import type { VxeGridProps } from '#/adapter/vxe-table';

/**
 * 资产上架表格配置
 */
export const createRackingGridConfig = (): VxeGridProps => ({
  height: 'auto',
  columns: [
    { field: 'ticketNo', title: '工单编号' },
    { field: 'title', title: '工单标题' },
    { field: 'quantity', title: '设备数量' },
    { field: 'project', title: '项目' },
    {
      field: 'status',
      title: '工单状态',
      slots: { default: 'status_column' },
      width: 120,
    },
    { field: 'applicantName', title: '申请人' },
    { field: 'handlerName', title: '处理人' },
    {
      field: 'createdAt',
      title: '创建时间',
      width: 180,
      formatter: 'formatDateTime',
    },
    {
      title: '操作',
      fixed: 'right',
      slots: { default: 'action' },
      width: 120,
    },
  ],
  proxyConfig: {
    response: {
      result: 'list',
    },
    ajax: {
      query: ({ page }, form: Record<string, any>) => {
        // 处理日期范围
        let endTime: string | undefined;
        let startTime: string | undefined;
        if (
          form.dateRange &&
          Array.isArray(form.dateRange) &&
          form.dateRange.length === 2
        ) {
          startTime = form.dateRange[0];
          endTime = form.dateRange[1];
        }

        // 调用API获取数据
        return import('#/api/core/hardWareMaint/racking').then(
          ({ listRackingTicketsApi }) => {
            return listRackingTicketsApi({
              page: page.currentPage,
              pageSize: page.pageSize,
              ticketNo: form.ticketNo,
              status: form.status,
              title: form.title,
              project: form.project,
              applicant: form.applicant,
              startTime,
              endTime,
            });
          },
        );
      },
    },
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10,
  },
  sortConfig: {
    multiple: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar-buttons',
    },
    custom: true,
    refresh: true,
    zoom: true,
  },
});

/**
 * 设备表格配置
 */
export const createDeviceGridConfig = (): VxeGridProps => ({
  columns: [
    { field: 'device.sn', title: '设备SN' },
    { field: 'device.brand', title: '厂商' },
    { field: 'device.model', title: '型号' },
    {
      field: 'device.assetType',
      title: '设备类型',
      slots: { default: 'assetType' },
    },
    {
      field: 'status',
      title: '上架前资产状态',
      slots: { default: 'assetStatus' },
    },
    { field: 'roomId', title: '机房ID', visible: false },
    { field: 'cabinetId', title: '机柜', visible: false },
    { field: 'roomName', title: '机房', slots: { default: 'roomName' } },
    {
      field: 'cabinetName',
      title: '机柜',
      slots: { default: 'cabinetName' },
    },
    { field: 'rackPosition', title: '机架位置' },
    { field: 'vpcMac', title: 'VPC MAC' },
    { field: 'bmcMac', title: 'BMC MAC' },
  ],
  showOverflow: true,
  rowConfig: { isHover: true },
  emptyText: '暂无设备数据',
  exportConfig: {},
  data: [],
});
