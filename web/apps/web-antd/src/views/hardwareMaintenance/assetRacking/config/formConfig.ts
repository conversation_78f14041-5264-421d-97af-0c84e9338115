import type { SelectOption } from '@vben/types';

import type { VbenFormProps } from '#/adapter/form';

import { STATUS_MAP } from '../constants/statusMaps';

/**
 * 创建资产上架查询表单配置
 */
export const createRackingFormConfig = (
  projectOptions: SelectOption[],
): VbenFormProps => ({
  collapsed: true,
  collapsedRows: 3,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入工单编号' },
      fieldName: 'ticketNo',
      label: '工单编号',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择项目',
        options: projectOptions,
      },
      fieldName: 'project',
      label: '项目',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择工单状态',
        options: Object.entries(STATUS_MAP).map(([key, value]) => {
          return {
            label: value,
            value: key,
          };
        }),
      },
      fieldName: 'status',
      label: '工单状态',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入工单标题' },
      fieldName: 'title',
      label: '工单标题',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入申请人' },
      fieldName: 'applicant',
      label: '申请人',
    },
    {
      component: 'RangePicker',
      componentProps: {
        placeholder: ['开始时间', '结束时间'],
        showTime: true,
      },
      fieldName: 'dateRange',
      label: '创建时间',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: true,
  wrapperClass: 'grid-cols-3 grid',
});
