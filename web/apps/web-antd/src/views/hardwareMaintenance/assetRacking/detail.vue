<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';

import {
  Button,
  Card,
  Divider,
  Modal,
  Spin,
  Steps,
  Tag,
  Typography,
} from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  assetStatusMap,
  assetTypeMap,
  STATUS_MAP,
} from './constants/statusMaps';
import { useRackingDetail } from './hooks/useRackingDetail';
import { useRoomCabinet } from './hooks/useRoomCabinet';

const { Title } = Typography;
const route = useRoute();

// 计算工单ID
const ticketId = computed<number>(() => {
  const id = route.params.id;
  return typeof id === 'string' ? Number.parseInt(id) : 0;
});

// 使用工单详情钩子
const {
  loading,
  ticket,
  cardVisible,
  confirmDialogVisible,
  confirmDialogTitle,
  confirmDialogContent,
  confirmDialogLoading,

  pendingComment,
  isEditMode,
  tableEditMode,
  editForm,
  currentStep,
  possibleNextActions,
  DeviceGrid,
  getTicketDetail,
  toggleCardVisibility,
  showConfirmDialog,
  cancelConfirmDialog,
  confirmDialog,
  backToList,
  historyList,
  historyLoading,
  fetchHistory,
} = useRackingDetail(ticketId.value);

// 使用机房和机柜管理钩子
const {
  roomOptions,
  loadingRooms,
  loadingCabinets,
  roomCabinetMap,
  fetchRooms,
  fetchCabinetsByRoomId,
  handleRoomChange: handleRoomChangeBase,
  handleCabinetChange: handleCabinetChangeBase,
} = useRoomCabinet();

// 机房选择变更处理函数的包装器
const handleRoomChange = (value: any, index?: number) => {
  handleRoomChangeBase(value, editForm, index);
};

// 机柜选择变更处理函数的包装器
const handleCabinetChange = (value: any, roomId: number, rowIndex: number) => {
  handleCabinetChangeBase(value, roomId, editForm, rowIndex);
};

// 英文操作类型到中文的映射
const activityCategoryMap: Record<string, string> = {
  Create: '创建',
  Approve: '审核通过',
  Handle: '开始上架',
  Complete: '完成',
  Reject: '拒绝',
  // 可根据实际后端返回类型继续补充
};

const statusMap: Record<string, string> = {
  PENDING: '待审核',
  APPROVED: '已审核',
  IN_PROGRESS: '上架中',
  COMPLETED: '已完成',
  REJECTED: '已拒绝',
  // 根据实际后端返回继续补充
};

onMounted(() => {
  getTicketDetail();
  fetchRooms();
  fetchHistory();
});
</script>

<template>
  <div class="min-h-screen pb-8">
    <!-- 顶部标题栏 -->
    <div
      class="mb-6 border-b border-gray-200 bg-white shadow-md dark:border-gray-700 dark:bg-gray-800"
    >
      <div
        class="mx-auto flex max-w-full items-center justify-between px-6 py-4"
      >
        <div class="flex items-center">
          <Button
            type="primary"
            @click="backToList"
            class="mr-5 flex items-center overflow-hidden whitespace-nowrap rounded-md px-4 py-1.5 font-medium text-white shadow-sm transition-all duration-300 hover:shadow-md"
          >
            <template #icon><span class="anticon mr-1">←</span></template>
            返回
          </Button>
          <Title :level="3" class="m-0 text-gray-800 dark:text-gray-200">
            资产上架详情
          </Title>
        </div>
        <div class="flex items-center space-x-3">
          <!-- 操作按钮区域，根据状态显示不同按钮 -->
          <div
            v-if="
              ticket &&
              ticket.status !== 'COMPLETED' &&
              ticket.status !== 'REJECTED'
            "
          >
            <Button
              v-for="action in possibleNextActions"
              :key="action.status"
              :type="action.status === 'COMPLETED' ? 'primary' : action.type"
              class="ml-2"
              :danger="action.status === 'REJECTED'"
              :style="
                action.status === 'COMPLETED'
                  ? 'background-color: #52c41a; border-color: #52c41a;'
                  : ''
              "
              @click="showConfirmDialog(action.status, action.label)"
            >
              {{ action.label }}
            </Button>
          </div>

          <!-- 工单状态标签 -->
          <div v-if="ticket" class="flex items-center">
            <Tag
              :color="
                ticket.status === 'COMPLETED'
                  ? 'success'
                  : ticket.status === 'REJECTED'
                    ? 'error'
                    : 'processing'
              "
              class="rounded-full px-3 py-1 text-sm font-medium"
            >
              {{ STATUS_MAP[ticket.status] || ticket.status }}
            </Tag>
          </div>
        </div>
      </div>
    </div>

    <div class="mx-auto max-w-full px-6">
      <!-- 加载中状态 -->
      <div
        v-if="loading"
        class="flex h-80 items-center justify-center rounded-xl bg-white p-8 shadow-md dark:bg-gray-800"
      >
        <div class="text-center">
          <Spin size="large" />
          <div
            class="mt-4 text-xl font-medium text-gray-700 dark:text-gray-300"
          >
            加载中，请稍候...
          </div>
        </div>
      </div>

      <div v-else-if="ticket" class="space-y-6">
        <!-- 处理进度和基本信息放在同一行 -->
        <div class="flex space-x-6">
          <!-- 处理进度 -->
          <Card
            class="flex-1 overflow-hidden rounded-xl border-0 shadow-md transition-all duration-300 hover:shadow-lg"
          >
            <template #title>
              <div
                @click="toggleCardVisibility('operations')"
                class="flex cursor-pointer items-center justify-between"
              >
                <div class="flex items-center text-lg font-medium">
                  <span class="mr-2 text-green-500">⚙️</span>
                  处理进度
                </div>
                <span class="text-gray-400 hover:text-blue-500">
                  {{ cardVisible.operations ? '▼' : '►' }}
                </span>
              </div>
            </template>
            <div
              v-show="cardVisible.operations"
              class="transform transition-all duration-500"
            >
              <div class="px-6 py-2">
                <Steps :current="currentStep" class="py-3" progress-dot>
                  <Steps.Step title="待审核" />
                  <Steps.Step title="已审核" />
                  <Steps.Step title="上架中" />
                  <Steps.Step title="已完成" />
                </Steps>
              </div>
            </div>
          </Card>

          <!-- 基本信息 -->
          <Card
            class="flex-1 overflow-hidden rounded-xl border-0 shadow-md transition-all duration-300 hover:shadow-lg"
          >
            <template #title>
              <div
                @click="toggleCardVisibility('baseInfo')"
                class="flex cursor-pointer items-center justify-between"
              >
                <div class="flex items-center text-lg font-medium">
                  <span class="mr-2 text-blue-500">📋</span>
                  工单信息
                </div>
                <span class="text-gray-400 hover:text-blue-500">
                  {{ cardVisible.baseInfo ? '▼' : '►' }}
                </span>
              </div>
            </template>
            <div
              v-show="cardVisible.baseInfo"
              class="transform transition-all duration-500"
            >
              <div class="px-4 py-3">
                <!-- 紧凑型工单信息布局 -->
                <div
                  class="rounded-lg border border-gray-100 bg-white dark:border-gray-700 dark:bg-gray-800"
                >
                  <!-- 工单编号、状态 -->
                  <div
                    class="flex flex-wrap items-center border-b border-gray-100 p-3 dark:border-gray-700"
                  >
                    <div class="mr-6 flex items-center">
                      <div class="mr-2 text-xs font-medium text-gray-500">
                        工单编号:
                      </div>
                      <div class="font-medium text-gray-800 dark:text-gray-200">
                        {{ ticket.ticketNo }}
                      </div>
                    </div>
                    <div class="mr-6 flex items-center">
                      <div class="mr-2 text-xs font-medium text-gray-500">
                        状态:
                      </div>
                      <Tag
                        :color="
                          ticket.status === 'COMPLETED'
                            ? 'success'
                            : ticket.status === 'REJECTED'
                              ? 'error'
                              : 'processing'
                        "
                        class="text-xs"
                      >
                        {{ STATUS_MAP[ticket.status] || ticket.status }}
                      </Tag>
                    </div>
                    <div class="flex items-center">
                      <div class="mr-2 text-xs font-medium text-gray-500">
                        项目:
                      </div>
                      <div class="font-medium text-gray-800 dark:text-gray-200">
                        {{ ticket.project || '-' }}
                      </div>
                    </div>
                  </div>

                  <!-- 申请人、处理人、设备数量 -->
                  <div
                    class="flex flex-wrap items-center border-b border-gray-100 p-3 dark:border-gray-700"
                  >
                    <div class="mr-6 flex items-center">
                      <span class="mr-1 text-sm text-green-500">👤</span>
                      <div class="mr-1 text-xs font-medium text-gray-500">
                        申请人:
                      </div>
                      <div class="font-medium text-gray-800 dark:text-gray-200">
                        {{ ticket.applicantName }}
                      </div>
                    </div>
                    <div class="mr-6 flex items-center">
                      <span class="mr-1 text-sm text-purple-500">👤</span>
                      <div class="mr-1 text-xs font-medium text-gray-500">
                        处理人:
                      </div>
                      <div class="font-medium text-gray-800 dark:text-gray-200">
                        {{ ticket.handlerName || '-' }}
                      </div>
                    </div>
                    <div class="mr-6 flex items-center">
                      <span class="mr-1 text-sm text-blue-500">🔢</span>
                      <div class="mr-1 text-xs font-medium text-gray-500">
                        设备数:
                      </div>
                      <div class="font-medium text-gray-800 dark:text-gray-200">
                        {{ ticket.quantity }}
                      </div>
                    </div>
                  </div>

                  <!-- 审核人、时间信息 -->
                  <div
                    class="flex flex-wrap items-center border-b border-gray-100 p-3 dark:border-gray-700"
                  >
                    <div
                      v-if="ticket.approverName"
                      class="mr-6 flex items-center"
                    >
                      <span class="mr-1 text-sm text-blue-500">👤</span>
                      <div class="mr-1 text-xs font-medium text-gray-500">
                        审核人:
                      </div>
                      <div class="font-medium text-gray-800 dark:text-gray-200">
                        {{ ticket.approverName }}
                      </div>
                    </div>
                    <div class="mr-6 flex items-center">
                      <span class="mr-1 text-sm text-purple-500">🕒</span>
                      <div class="mr-1 text-xs font-medium text-gray-500">
                        创建时间:
                      </div>
                      <div class="font-medium text-gray-800 dark:text-gray-200">
                        {{ ticket.CreatedAt }}
                      </div>
                    </div>
                    <div
                      v-if="ticket.plannedRackingTime"
                      class="flex items-center"
                    >
                      <span class="mr-1 text-sm text-orange-500">📅</span>
                      <div class="mr-1 text-xs font-medium text-gray-500">
                        预计上架时间:
                      </div>
                      <div class="font-medium text-gray-800 dark:text-gray-200">
                        {{ ticket.plannedRackingTime }}
                      </div>
                    </div>
                  </div>

                  <!-- 备注 -->
                  <div
                    v-if="ticket.remark"
                    class="border-b border-gray-100 p-3 dark:border-gray-700"
                  >
                    <div class="mb-1 text-xs font-medium text-gray-500">
                      描述:
                    </div>
                    <div class="text-sm text-gray-700 dark:text-gray-300">
                      {{ ticket.remark }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>

        <!-- 设备列表 -->
        <Card
          class="overflow-hidden rounded-xl border-0 shadow-md transition-all duration-300 hover:shadow-lg"
        >
          <template #title>
            <div
              @click="toggleCardVisibility('devices')"
              class="flex cursor-pointer items-center justify-between"
            >
              <div class="flex items-center text-lg font-medium">
                <span class="mr-2 text-amber-500">📱</span>
                设备列表
              </div>
              <span class="text-gray-400 hover:text-blue-500">
                {{ cardVisible.devices ? '▼' : '►' }}
              </span>
            </div>
          </template>
          <div
            v-show="cardVisible.devices"
            class="transform transition-all duration-500"
          >
            <Divider orientation="left">
              <span class="text-sm text-gray-500"
                >共 {{ ticket.quantity || 0 }} 个设备</span
              >
            </Divider>
            <div class="px-6 py-4">
              <div
                v-if="
                  ticket.status === 'IN_PROGRESS' ||
                  ticket.status === 'APPROVED'
                "
                class="mb-4 flex justify-end"
              ></div>

              <DeviceGrid v-if="!tableEditMode" class="rounded-lg shadow-sm">
                <template #assetStatus="{ row }">
                  <Tag
                    v-if="row.status && assetStatusMap[row.status]"
                    :color="assetStatusMap[row.status]?.color"
                  >
                    {{ assetStatusMap[row.status]?.text }}
                  </Tag>
                  <span v-else>{{ row.status || '-' }}</span>
                </template>
                <template #assetType="{ row }">
                  {{
                    row.device && row.device.assetType
                      ? assetTypeMap[row.device.assetType] ||
                        row.device.assetType
                      : '-'
                  }}
                </template>
                <template #roomName="{ row }">
                  {{
                    (row.roomId &&
                      roomOptions.find((option) => option.value === row.roomId)
                        ?.label) ||
                    '-'
                  }}
                </template>
                <template #cabinetName="{ row }">
                  {{
                    (() => {
                      if (!roomCabinetMap.has(row.roomId)) {
                        fetchCabinetsByRoomId(row.roomId);
                      }
                      return (
                        (row.cabinetId &&
                          roomCabinetMap
                            .get(row.roomId)
                            ?.find((cabinet) => cabinet.value === row.cabinetId)
                            ?.label) ||
                        '-'
                      );
                    })()
                  }}
                </template>
              </DeviceGrid>

              <!-- 编辑模式表格 -->
              <div v-else class="overflow-x-auto rounded-lg shadow-sm">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th
                        class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                      >
                        设备SN
                      </th>
                      <th
                        class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                      >
                        厂商
                      </th>
                      <th
                        class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                      >
                        设备类型
                      </th>
                      <th
                        class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                      >
                        资产状态
                      </th>
                      <th
                        class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                      >
                        机房
                      </th>
                      <th
                        class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                      >
                        机柜
                      </th>
                      <th
                        class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                      >
                        机架位置
                      </th>
                      <th
                        class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                      >
                        VPC MAC
                      </th>
                      <th
                        class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                      >
                        BMC MAC
                      </th>
                    </tr>
                  </thead>
                  <tbody class="divide-y divide-gray-200 bg-white">
                    <tr v-for="(item, index) in editForm.items" :key="item.id">
                      <td class="whitespace-nowrap px-4 py-3 text-sm">
                        {{ item.sn }}
                      </td>
                      <td class="whitespace-nowrap px-4 py-3 text-sm">
                        {{ item.brand }}
                      </td>
                      <td class="whitespace-nowrap px-4 py-3 text-sm">
                        {{
                          item.assetType
                            ? assetTypeMap[item.assetType] || item.assetType
                            : '-'
                        }}
                      </td>
                      <td class="whitespace-nowrap px-4 py-3 text-sm">
                        <Tag
                          v-if="item.status && assetStatusMap[item.status]"
                          :color="assetStatusMap[item.status]?.color"
                        >
                          {{ assetStatusMap[item.status]?.text }}
                        </Tag>
                        <span v-else>{{ item.status || '-' }}</span>
                      </td>
                      <td class="whitespace-nowrap px-4 py-3">
                        <a-select
                          v-model:value="editForm.items[index].roomID"
                          style="width: 120px"
                          placeholder="选择机房"
                          :options="roomOptions"
                          :loading="loadingRooms"
                          @change="
                            (value: any) => handleRoomChange(value, index)
                          "
                          show-search
                          option-filter-prop="label"
                        >
                          <template #notFoundContent>
                            <span v-if="loadingRooms">加载中...</span>
                            <span v-else>暂无数据</span>
                          </template>
                        </a-select>
                      </td>
                      <td class="whitespace-nowrap px-4 py-3">
                        <a-select
                          v-model:value="editForm.items[index].cabinetID"
                          style="width: 120px"
                          placeholder="选择机柜"
                          :options="
                            roomCabinetMap.get(editForm.items[index].roomID) ||
                            []
                          "
                          :disabled="!editForm.items[index].roomID"
                          :loading="loadingCabinets"
                          @change="
                            (value: any) =>
                              handleCabinetChange(
                                value,
                                editForm.items[index].roomID,
                                index,
                              )
                          "
                          show-search
                          option-filter-prop="label"
                        >
                          <template #notFoundContent>
                            <span v-if="loadingCabinets">加载中...</span>
                            <span v-else-if="!editForm.items[index].roomID"
                              >请先选择机房</span
                            >
                            <span v-else>暂无机柜数据</span>
                          </template>
                        </a-select>
                      </td>
                      <td class="whitespace-nowrap px-4 py-3">
                        <a-input-number
                          v-model:value="editForm.items[index].rackPosition"
                          :min="0"
                          style="width: 80px"
                        />
                      </td>
                      <td class="whitespace-nowrap px-4 py-3">
                        <a-input
                          v-model:value="editForm.items[index].vpcMAC"
                          placeholder="VPC MAC"
                          style="width: 140px"
                        />
                      </td>
                      <td class="whitespace-nowrap px-4 py-3">
                        <a-input
                          v-model:value="editForm.items[index].bmcMAC"
                          placeholder="BMC MAC"
                          style="width: 140px"
                        />
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </Card>

        <!-- 操作历史 -->
        <Card
          class="overflow-hidden rounded-xl border-0 shadow-md transition-all duration-300 hover:shadow-lg"
        >
          <template #title>
            <div
              @click="toggleCardVisibility('history')"
              class="flex cursor-pointer items-center justify-between"
            >
              <div class="flex items-center text-lg font-medium">
                <span class="mr-2 text-gray-500">📝</span>
                操作历史
              </div>
              <span class="text-gray-400 hover:text-blue-500">
                {{ cardVisible.history ? '▼' : '►' }}
              </span>
            </div>
          </template>
          <div
            v-show="cardVisible.history"
            class="transform transition-all duration-500"
          >
            <div class="px-4 py-3">
              <div v-if="historyLoading" class="text-gray-400">加载中...</div>
              <div v-else-if="historyList.length > 0">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th
                        class="px-4 py-2 text-left text-xs font-medium text-gray-500"
                      >
                        操作人
                      </th>
                      <th
                        class="px-4 py-2 text-left text-xs font-medium text-gray-500"
                      >
                        操作
                      </th>
                      <th
                        class="px-4 py-2 text-left text-xs font-medium text-gray-500"
                      >
                        备注
                      </th>
                      <th
                        class="px-4 py-2 text-left text-xs font-medium text-gray-500"
                      >
                        时间
                      </th>
                    </tr>
                  </thead>
                  <tbody class="divide-y divide-gray-200 bg-white">
                    <tr v-for="item in historyList" :key="item.id">
                      <td class="px-4 py-2 text-sm">
                        {{ item.operator_name }}
                      </td>
                      <td class="px-4 py-2 text-sm">
                        {{
                          activityCategoryMap[item.activity_category] ||
                          item.activity_category
                        }}
                        <span
                          v-if="item.previous_status || item.new_status"
                          class="text-xs text-gray-400"
                        >
                          <br />{{
                            statusMap[item.previous_status] ||
                            item.previous_status
                          }}
                          →
                          {{ statusMap[item.new_status] || item.new_status }}
                        </span>
                      </td>
                      <td class="px-4 py-2 text-sm">
                        {{ item.remarks || '-' }}
                      </td>
                      <td class="px-4 py-2 text-sm">
                        {{
                          dayjs(item.operation_time).format(
                            'YYYY-MM-DD HH:mm:ss',
                          )
                        }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div v-else class="text-gray-400">暂无操作历史</div>
            </div>
          </div>
        </Card>
      </div>

      <div
        v-else
        class="flex h-80 items-center justify-center rounded-xl bg-white p-8 shadow-md dark:bg-gray-800"
      >
        <div class="text-center">
          <div class="mb-4 text-gray-400">
            <svg
              class="mx-auto h-20 w-20"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <div class="text-xl font-medium text-gray-700 dark:text-gray-300">
            暂无数据
          </div>
          <p class="mt-2 text-gray-500 dark:text-gray-400">
            请检查工单ID是否正确
          </p>
        </div>
      </div>
    </div>

    <!-- 确认对话框 -->
    <Modal
      v-model:visible="confirmDialogVisible"
      :title="confirmDialogTitle"
      :confirm-loading="confirmDialogLoading"
      ok-text="确定"
      cancel-text="取消"
      @ok="confirmDialog"
      @cancel="cancelConfirmDialog"
      :width="isEditMode ? 900 : 520"
      class="dark:bg-gray-800 dark:text-gray-100"
    >
      <div class="py-4">
        <div class="flex items-center">
          <span class="mr-2 text-lg text-blue-500">ℹ️</span>
          <p class="text-base">{{ confirmDialogContent }}</p>
        </div>

        <!-- 设备编辑表格 -->
        <div v-if="isEditMode" class="mt-6">
          <div class="mb-3 text-base font-medium">请填写设备上架信息：</div>

          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th
                    class="px-3 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    设备SN
                  </th>
                  <th
                    class="px-3 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    厂商
                  </th>
                  <th
                    class="px-3 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    机房
                  </th>
                  <th
                    class="px-3 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    机柜
                  </th>
                  <th
                    class="px-3 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    机架位置
                  </th>
                  <th
                    class="px-3 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    VPC MAC
                  </th>
                  <th
                    class="px-3 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    BMC MAC
                  </th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 bg-white">
                <tr v-for="(item, index) in editForm.items" :key="item.id">
                  <td class="whitespace-nowrap px-3 py-2 text-sm">
                    {{ item.sn }}
                  </td>
                  <td class="whitespace-nowrap px-3 py-2 text-sm">
                    {{ item.brand }}
                  </td>
                  <td class="whitespace-nowrap px-3 py-2">
                    <a-select
                      v-model:value="editForm.items[index].roomID"
                      style="width: 120px"
                      placeholder="选择机房"
                      :options="roomOptions"
                      :loading="loadingRooms"
                      @change="(value: any) => handleRoomChange(value, index)"
                      show-search
                      option-filter-prop="label"
                    >
                      <template #notFoundContent>
                        <span v-if="loadingRooms">加载中...</span>
                        <span v-else>暂无数据</span>
                      </template>
                    </a-select>
                  </td>
                  <td class="whitespace-nowrap px-3 py-2">
                    <a-select
                      v-model:value="editForm.items[index].cabinetID"
                      style="width: 120px"
                      placeholder="选择机柜"
                      :options="
                        roomCabinetMap.get(editForm.items[index].roomID) || []
                      "
                      :disabled="!editForm.items[index].roomID"
                      :loading="loadingCabinets"
                      @change="
                        (value: any) =>
                          handleCabinetChange(
                            value,
                            editForm.items[index].roomID,
                            index,
                          )
                      "
                      show-search
                      option-filter-prop="label"
                    >
                      <template #notFoundContent>
                        <span v-if="loadingCabinets">加载中...</span>
                        <span v-else-if="!editForm.items[index].roomID"
                          >请先选择机房</span
                        >
                        <span v-else>暂无机柜数据</span>
                      </template>
                    </a-select>
                  </td>
                  <td class="whitespace-nowrap px-3 py-2">
                    <a-input-number
                      v-model:value="editForm.items[index].rackPosition"
                      :min="0"
                      style="width: 80px"
                    />
                  </td>
                  <td class="whitespace-nowrap px-3 py-2">
                    <a-input
                      v-model:value="editForm.items[index].vpcMAC"
                      placeholder="VPC MAC"
                      style="width: 140px"
                    />
                  </td>
                  <td class="whitespace-nowrap px-3 py-2">
                    <a-input
                      v-model:value="editForm.items[index].bmcMAC"
                      placeholder="BMC MAC"
                      style="width: 140px"
                    />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="mt-4">
          <div class="mb-1 font-medium">备注:</div>
          <a-textarea
            v-model:value="pendingComment"
            :rows="3"
            placeholder="请输入备注信息（可选）"
          />
        </div>
      </div>
    </Modal>
  </div>
</template>

<style scoped>
@keyframes pulse-ring {
  0% {
    box-shadow: 0 0 0 0 rgb(64 150 255 / 70%);
  }

  30% {
    box-shadow: 0 0 0 15px rgb(64 150 255 / 0%);
  }

  60% {
    box-shadow: 0 0 0 0 rgb(64 150 255 / 70%);
  }

  100% {
    box-shadow: 0 0 0 0 rgb(64 150 255 / 0%);
  }
}
</style>
