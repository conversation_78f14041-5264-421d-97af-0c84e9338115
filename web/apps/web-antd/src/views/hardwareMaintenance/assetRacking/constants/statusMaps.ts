/**
 * 工单状态映射
 */
export const STATUS_MAP: Record<string, string> = {
  PENDING: '待审核',
  APPROVED: '已审核',
  IN_PROGRESS: '上架中',
  COMPLETED: '已完成',
  REJECTED: '已拒绝',
};

/**
 * 资产状态映射
 */
export const assetStatusMap: {
  [key: string]: { color: string; text: string };
} = {
  pending_storage: { text: '待入库', color: 'purple' },
  in_storage: { text: '已入库', color: 'blue' },
  pending_outbound: { text: '待出库', color: 'orange' },
  outbound: { text: '已出库', color: 'cyan' },
  idle: { text: '闲置中', color: 'lime' },
  in_use: { text: '使用中', color: 'green' },
  maintaining: { text: '维修中', color: 'gold' },
  pending_scrap: { text: '待报废', color: 'volcano' },
  scrapped: { text: '已报废', color: 'red' },
};

/**
 * 资产类型映射
 */
export const assetTypeMap: { [key: string]: string } = {
  server: '服务器',
  gpu_server: 'GPU服务器',
  switch: '交换机',
  router: '路由器',
  storage: '存储',
  firewall: '防火墙',
  other: '其他',
};

/**
 * 根据工单状态返回对应的标签颜色
 */
export const getStatusTagColor = (status: string) => {
  switch (status) {
    case 'APPROVED': {
      return 'blue';
    }
    case 'COMPLETED': {
      return 'success';
    }
    case 'IN_PROGRESS': {
      return 'processing';
    }
    case 'PENDING': {
      return 'warning';
    }
    case 'REJECTED': {
      return 'error';
    }
    default: {
      return 'default';
    }
  }
};
