<script setup lang="ts">
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { onUnmounted, reactive, ref, watch } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { Button, message, Modal, Tag } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  getDeviceResourceTableApi,
  getDevicesByMultiSNApi,
} from '#/api/core/cmdb/asset/device';
import { getRegionTableApi } from '#/api/core/cmdb/location/region';
import { createRackingTicketApi } from '#/api/core/hardWareMaint/racking';
import { downloadImportTemplate } from '#/api/core/import';

// 定义事件
const emit = defineEmits(['success']);

const loading = ref(false);
const modalVisible = ref(false);
const searchLoading = ref(false);
const importLoading = ref(false);
const csvFileInputRef = ref<HTMLInputElement>();
const projectOptions = ref<{ label: string; value: string }[]>([]);

// 表单数据
const formState = reactive({
  title: '',
  project: '',
  description: '',
  plannedRackTime: null,
  items: [] as Array<{
    assetID: number;
    bmc_ip?: string;
    cabinetID: number;
    device_name?: string;
    device_sn?: string;
    device_type?: string;
    key: string;
    model?: string;
    rackPosition: number;
    roomID: number;
    status: string;
    vendor?: string;
    vpc_ip?: string;
  }>,
});

// 设备选择模态框相关数据
const selectedRowKeys = ref<(number | string)[]>([]);

// 已选设备表格配置
const selectedDevicesGridOptions: VxeGridProps = {
  columns: [
    { field: 'device_sn', title: '设备SN' },
    { field: 'device_name', title: '主机名' },
    { field: 'device_type', title: '设备类型' },
    { field: 'vendor', title: '厂商' },
    { field: 'model', title: '型号' },
    { field: 'vpc_ip', title: 'VPC IP' },
    { field: 'bmc_ip', title: 'BMC IP' },
    {
      title: '操作',
      slots: { default: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  showOverflow: true,
  rowConfig: { isHover: true },
  emptyText: '请添加设备',
  // 直接绑定数据源
  data: [],
};

// 已选设备表格
const [SelectedDevicesGrid, selectedDevicesGridApi] = useVbenVxeGrid({
  gridOptions: selectedDevicesGridOptions,
});

// 更新已选设备列表
function updateSelectedDevicesList() {
  if (selectedDevicesGridApi) {
    selectedDevicesGridApi.setGridOptions({
      data: formState.items,
    });
  }
}

// 监听formState.items变化，更新表格数据
const unwatchItems = watch(
  () => formState.items,
  () => {
    updateSelectedDevicesList();
  },
  { deep: true, immediate: true },
);

// 在组件卸载时移除监听
onUnmounted(() => {
  unwatchItems();
});

// 抽屉组件
const [Drawer, drawerApi] = useVbenDrawer({
  onClosed: () => {
    resetForm();
  },
});

// 表单配置
const formOptions: VbenFormProps = {
  showDefaultActions: false,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入工单标题' },
      fieldName: 'title',
      label: '工单标题',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择项目',
        options: projectOptions,
        allowClear: true,
      },
      fieldName: 'project',
      label: '项目',
      rules: 'required',
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择预计上架时间',
        showTime: true,
        style: { width: '100%' },
        format: 'YYYY-MM-DD HH:mm:ss',
      },
      fieldName: 'plannedRackTime',
      label: '预计上架时间',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入工单描述（可选）',
        type: 'textarea',
        rows: 4,
      },
      fieldName: 'description',
      label: '工单描述',
    },
  ],
};

const [Form, formApi] = useVbenForm(formOptions);

// 设备搜索表单配置
const deviceSearchFormOptions: VbenFormProps = {
  collapsed: false,
  collapsedRows: 2,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入SN/主机名' },
      fieldName: 'query',
      label: 'SN/主机名',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入厂商' },
      fieldName: 'brand',
      label: '厂商',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入型号' },
      fieldName: 'model',
      label: '型号',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入VPC IP' },
      fieldName: 'vpcIP',
      label: 'VPC IP',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入BMC IP' },
      fieldName: 'bmcIP',
      label: 'BMC IP',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: true,
};

// 设备搜索表格配置
const deviceSearchGridOptions: VxeGridProps = {
  columns: [
    { align: 'center', type: 'checkbox', width: 40 },
    { field: 'sn', title: '设备SN', width: 180 },
    { field: 'brand', title: '厂商', width: 120 },
    {
      field: 'assetStatus',
      title: '资产状态',
      width: 120,
      slots: { default: 'assetStatus' },
    },
    { field: 'model', title: '型号', width: 140 },
    {
      field: 'resource.hostname',
      title: '主机名',
      width: 200,
      slots: { default: 'hostname' },
    },
    {
      field: 'resource.vpcIP',
      title: 'VPC IP',
      width: 140,
      slots: { default: 'vpcIP' },
    },
    {
      field: 'resource.bmcIP',
      title: 'BMC IP',
      width: 140,
      slots: { default: 'bmcIP' },
    },
  ],
  showOverflow: true,
  height: 'auto',
  rowConfig: { isHover: true },
  checkboxConfig: {
    highlight: true,
  },
  keepSource: true,
  pagerConfig: {
    enabled: true,
    pageSize: 10,
    pageSizes: [10, 20, 50, 100],
  },
  toolbarConfig: {
    refresh: true,
    zoom: true,
    custom: true,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        searchLoading.value = true;
        try {
          const { list, total } = await getDeviceResourceTableApi({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return {
            items: list || [],
            total: total || 0,
          };
        } catch (error: any) {
          message.error(`搜索设备失败: ${error.message || '未知错误'}`);
          return { items: [], total: 0 };
        } finally {
          searchLoading.value = false;
        }
      },
    },
  },
};

// 搜索设备
const [DeviceSearchGrid, deviceSearchGridApi] = useVbenVxeGrid({
  gridOptions: deviceSearchGridOptions,
  formOptions: deviceSearchFormOptions,
});

// 打开抽屉
function open() {
  loadProjects();
  drawerApi.open();
  return Promise.resolve(true);
}

// 加载项目列表
async function loadProjects() {
  try {
    const res = await getRegionTableApi({
      page: 1,
      pageSize: 1000,
    });

    projectOptions.value = res.list.map((item) => ({
      label: item.name,
      value: item.name,
    }));
  } catch (error: any) {
    console.error('加载项目失败:', error);
    message.error('加载项目列表失败');
  }
}

// 重置表单
function resetForm() {
  formApi.setValues({
    title: '',
    project: '',
    description: '',
    plannedRackTime: null,
  });
  formState.title = '';
  formState.project = '';
  formState.description = '';
  formState.plannedRackTime = null;
  formState.items = [];
  selectedRowKeys.value = [];
}

// 打开设备选择模态框
function openDeviceSelectModal() {
  modalVisible.value = true;
  // 重置选择
  selectedRowKeys.value = [];
  // 初始加载数据
  setTimeout(() => {
    if (deviceSearchGridApi && deviceSearchGridApi.grid) {
      try {
        deviceSearchGridApi.reload();
      } catch (error) {
        console.error('加载设备列表失败:', error);
        message.error('加载设备列表失败，请重试');
      }
    }
  }, 100);
}

// 添加选中的设备
function addSelectedDevices() {
  if (deviceSearchGridApi && deviceSearchGridApi.grid) {
    try {
      // 获取选中的行数据
      const records = deviceSearchGridApi.grid.getCheckboxRecords();

      // 首先检查是否有选择设备
      if (!records || records.length === 0) {
        message.warning('请选择至少一个设备');
        return;
      }

      // 检查重复设备
      const existingSNs = new Set<string>();
      formState.items.forEach((item) => {
        if (item.device_sn) {
          existingSNs.add(item.device_sn);
        }
      });

      let duplicateCount = 0;
      let addedCount = 0;

      records.forEach((device: any) => {
        if (existingSNs.has(device.sn)) {
          duplicateCount++;
          return;
        }

        formState.items.push({
          key: Date.now() + device.sn,
          assetID: device.id || 0,
          status: device.assetStatus,
          roomID: device.resource?.cabinet?.room?.id || 0,
          cabinetID: device.resource?.cabinetID || 0,
          rackPosition: device.resource?.cabinet?.capacityUnits || 0,
          device_sn: device.sn,
          device_name: device.resource?.hostname,
          device_type: device.assetType,
          vendor: device.brand,
          model: device.model,
          vpc_ip: device.resource?.vpcIP,
          bmc_ip: device.resource?.bmcIP,
        });

        existingSNs.add(device.sn);
        addedCount++;
      });

      // 更新已选设备列表
      updateSelectedDevicesList();

      if (duplicateCount > 0) {
        message.warning(`${duplicateCount}个设备已存在，已跳过`);
      }

      if (addedCount > 0) {
        message.success(`成功添加${addedCount}个设备`);
      }

      // 关闭模态框
      modalVisible.value = false;
    } catch (error) {
      console.error('添加设备失败:', error);
      message.error('添加设备失败，请重试');
    }
  } else {
    message.error('获取选中设备失败');
  }
}

// 删除已选择的设备
function removeDevice(key: string) {
  const index = formState.items.findIndex((device) => device.key === key);
  if (index !== -1) {
    formState.items.splice(index, 1);
    // 更新表格数据
    updateSelectedDevicesList();
    message.success('已删除该设备');
  }
}

// 验证设备列表数据
function validateDevices(): { message: string; valid: boolean } {
  // 验证设备列表不能为空
  if (formState.items.length === 0) {
    return { valid: false, message: '请至少添加一个设备' };
  }

  // 验证每个设备的必填字段
  for (let i = 0; i < formState.items.length; i++) {
    const item = formState.items[i] as {
      assetID: number;
      device_sn?: string;
      device_type?: string;
    };

    // 手动添加的设备才需要检查SN和类型
    // 对于从资产库选择的设备，这些字段应该已经存在
    if (!item.assetID && (!item.device_sn || item.device_sn.trim() === '')) {
      return { valid: false, message: `第${i + 1}行设备SN不能为空` };
    }
    if (
      !item.assetID &&
      (!item.device_type || item.device_type.trim() === '')
    ) {
      return { valid: false, message: `第${i + 1}行设备类型不能为空` };
    }
  }

  return { valid: true, message: '' };
}

// 提交表单
async function handleSubmit() {
  try {
    // 先验证表单有效性
    const { valid } = await formApi.validate();
    if (!valid) {
      message.error('请完成必填项');
      return;
    }

    // 验证设备列表
    const deviceValidation = validateDevices();
    if (!deviceValidation.valid) {
      message.error(deviceValidation.message);
      return;
    }

    const values = await formApi.getValues();

    formState.title = values.title;
    formState.project = values.project;
    formState.description = values.description || '';
    formState.plannedRackTime = values.plannedRackTime;

    loading.value = true;

    // 提交创建请求
    await createRackingTicketApi({
      title: formState.title,
      project: formState.project,
      remark: formState.description,
      PlannedRackTime: formState.plannedRackTime,
      rackItems: formState.items.map((item: any) => ({
        assetID: item.assetID,
        status: item.status,
        roomID: item.roomID,
        cabinetID: item.cabinetID,
        rackPosition: item.rackPosition,
      })),
    });

    message.success('资产上架工单创建成功');

    // 关闭抽屉并触发成功事件
    drawerApi.close();
    emit('success');
  } catch (error: any) {
    message.error(`创建失败: ${error.message || '未知错误'}`);
    console.error('表单提交失败:', error);
  } finally {
    loading.value = false;
  }
}

// 向父组件暴露方法
defineExpose({
  open,
});

// 资产状态映射
const assetStatusMap: {
  [key: string]: { color: string; text: string };
} = {
  pending_storage: { text: '待入库', color: 'purple' },
  in_storage: { text: '已入库', color: 'blue' },
  pending_outbound: { text: '待出库', color: 'orange' },
  outbound: { text: '已出库', color: 'cyan' },
  idle: { text: '闲置中', color: 'lime' },
  in_use: { text: '使用中', color: 'green' },
  maintaining: { text: '维修中', color: 'gold' },
  pending_scrap: { text: '待报废', color: 'volcano' },
  scrapped: { text: '已报废', color: 'red' },
};

// 触发文件选择
function triggerFileSelect() {
  csvFileInputRef.value?.click();
}

// 下载导入模板
async function handleDownloadTemplate() {
  try {
    await downloadImportTemplate('racking_device_list');
    message.success('模板下载成功');
  } catch (error) {
    console.error('下载模板失败:', error);
    message.error('下载模板失败');
  }
}

// 处理CSV文件导入
async function handleCSVImport(event: Event) {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];

  if (!file) {
    return;
  }

  // 检查文件类型
  if (!file.name.toLowerCase().endsWith('.csv')) {
    message.error('请选择CSV文件');
    target.value = ''; // 清空文件选择
    return;
  }

  try {
    importLoading.value = true;

    // 读取文件内容
    const fileContent = await readFileAsText(file);

    // 解析CSV内容
    const deviceSNs = parseCSVContent(fileContent);

    if (deviceSNs.length === 0) {
      message.warning('CSV文件中未找到有效的设备SN');
      return;
    }

    // 显示解析到的SN数量供用户确认
    message.info(`解析到 ${deviceSNs.length} 个设备SN，正在获取设备信息...`);

    // 调用现有的设备获取逻辑
    await fetchDevicesBySNs(deviceSNs);
  } catch (error) {
    console.error('导入CSV文件失败:', error);
    message.error('导入CSV文件失败，请检查文件格式');
  } finally {
    importLoading.value = false;
    target.value = ''; // 清空文件选择
  }
}

// 读取文件内容
function readFileAsText(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.addEventListener('load', (e) => {
      resolve(e.target?.result as string);
    });
    reader.addEventListener('error', () => {
      reject(new Error('读取文件失败'));
    });
    reader.readAsText(file, 'utf8');
  });
}

// 解析CSV内容，提取设备SN
function parseCSVContent(content: string): string[] {
  const lines = content.split(/\r?\n/).filter((line) => line.trim());
  const deviceSNs: string[] = [];

  lines.forEach((line, index) => {
    // 跳过可能的表头（如果第一行包含中文或"SN"等字样）
    if (
      index === 0 &&
      (line.includes('设备') ||
        line.includes('SN') ||
        line.includes('序列号') ||
        line.includes('编号'))
    ) {
      return;
    }

    // 处理CSV格式，分割每一列
    const columns = line
      .split(',')
      .map((col) => col.replaceAll(/['"]/g, '').trim());

    // 尝试从不同的列中找到SN
    // 优先使用第一列，如果第一列为空则尝试第二列
    let sn = '';
    for (const col of columns) {
      if (col && col.length > 0) {
        sn = col;
        break;
      }
    }

    if (sn && !deviceSNs.includes(sn)) {
      deviceSNs.push(sn);
    }
  });

  return deviceSNs;
}

// 根据SN列表获取设备信息并添加到设备列表
async function fetchDevicesBySNs(sns: string[]) {
  if (sns.length === 0) {
    message.error('设备SN列表为空');
    return;
  }

  try {
    searchLoading.value = true;

    // 检查是否有重复设备
    const existingSNs = new Set<string>();
    formState.items.forEach((item) => {
      if (item.device_sn) {
        existingSNs.add(item.device_sn);
      }
    });

    let duplicateCount = 0;
    let addedCount = 0;
    let notFoundCount = 0;

    // 尝试使用专门的SN查询API
    try {
      const devices = await getDevicesByMultiSNApi(sns.join(','));

      if (devices && devices.length > 0) {
        // 如果通过SN API找到设备，尝试获取对应的资源信息
        for (const device of devices) {
          if (existingSNs.has(device.sn)) {
            duplicateCount++;
            continue;
          }

          // 尝试通过设备资源API获取完整的资源信息
          try {
            const resourceResponse = await getDeviceResourceTableApi({
              page: 1,
              pageSize: 10,
              query: device.sn,
            });

            let resourceData = null;
            if (resourceResponse && resourceResponse.list) {
              resourceData = resourceResponse.list.find(
                (d: any) => d.sn === device.sn,
              );
            }

            formState.items.push({
              key: Date.now() + device.sn + Math.random(),
              assetID: device.id || 0,
              status: device.assetStatus || '',
              roomID: resourceData?.resource?.cabinet?.room?.id || 0,
              cabinetID: resourceData?.resource?.cabinetID || 0,
              rackPosition: resourceData?.resource?.cabinet?.capacityUnits || 0,
              device_sn: device.sn,
              device_name: resourceData?.resource?.hostname || '',
              device_type: device.assetType || '',
              vendor: device.brand || '',
              model: device.model || '',
              vpc_ip: resourceData?.resource?.vpcIP || '',
              bmc_ip: resourceData?.resource?.bmcIP || '',
            });

            existingSNs.add(device.sn);
            addedCount++;
          } catch (resourceError) {
            console.warn(
              `获取设备 ${device.sn} 的资源信息失败，使用基本信息:`,
              resourceError,
            );

            // 如果无法获取资源信息，使用基本设备信息
            formState.items.push({
              key: Date.now() + device.sn + Math.random(),
              assetID: device.id || 0,
              status: device.assetStatus || '',
              roomID: 0,
              cabinetID: 0,
              rackPosition: 0,
              device_sn: device.sn,
              device_name: '',
              device_type: device.assetType || '',
              vendor: device.brand || '',
              model: device.model || '',
              vpc_ip: '',
              bmc_ip: '',
            });

            existingSNs.add(device.sn);
            addedCount++;
          }
        }

        // 计算未找到的设备数量
        notFoundCount = sns.length - devices.length - duplicateCount;
      } else {
        // 如果SN API没有找到任何设备
        notFoundCount = sns.length - duplicateCount;
      }
    } catch (snApiError) {
      console.error('SN查询API失败，回退到资源API:', snApiError);

      // 如果SN API失败，回退到原来的逐个查询方式
      for (const sn of sns) {
        if (existingSNs.has(sn)) {
          duplicateCount++;
          continue;
        }

        try {
          const response = await getDeviceResourceTableApi({
            page: 1,
            pageSize: 10,
            query: sn,
          });

          if (!response || !response.list) {
            console.warn(`API返回数据为空，SN: ${sn}`);
            notFoundCount++;
            continue;
          }

          const device = response.list.find((d: any) => d.sn === sn);

          if (device) {
            formState.items.push({
              key: Date.now() + device.sn + Math.random(),
              assetID: device.id || 0,
              status: device.assetStatus,
              roomID: device.resource?.cabinet?.room?.id || 0,
              cabinetID: device.resource?.cabinetID || 0,
              rackPosition: device.resource?.cabinet?.capacityUnits || 0,
              device_sn: device.sn,
              device_name: device.resource?.hostname,
              device_type: device.assetType,
              vendor: device.brand,
              model: device.model,
              vpc_ip: device.resource?.vpcIP,
              bmc_ip: device.resource?.bmcIP,
            });

            existingSNs.add(device.sn);
            addedCount++;
          } else {
            notFoundCount++;
            console.warn(`未找到匹配的设备SN: ${sn}`);
          }
        } catch (error) {
          console.error(`查询设备 ${sn} 失败:`, error);
          notFoundCount++;
        }
      }
    }

    // 更新已选设备列表
    updateSelectedDevicesList();

    // 显示结果消息
    const messages = [];
    if (addedCount > 0) {
      messages.push(`成功导入 ${addedCount} 个设备`);
    }
    if (duplicateCount > 0) {
      messages.push(`${duplicateCount} 个设备已存在，已跳过`);
    }
    if (notFoundCount > 0) {
      messages.push(`${notFoundCount} 个设备未找到`);
    }

    if (addedCount > 0) {
      message.success(messages.join('，'));
    } else if (notFoundCount > 0 && duplicateCount === 0) {
      message.warning('未能添加任何设备，请检查SN是否正确');
    } else if (duplicateCount > 0 && addedCount === 0) {
      message.warning('所有设备都已存在');
    }
  } catch (error) {
    console.error('批量获取设备信息失败:', error);

    let errorMessage = '获取设备信息失败';
    if (error instanceof Error) {
      errorMessage = error.message;
    }

    message.error(`获取设备信息失败: ${errorMessage}`);
  } finally {
    searchLoading.value = false;
  }
}
</script>

<template>
  <Drawer title="创建资产上架工单" :loading="loading" class="w-1/2">
    <template #default>
      <Form />

      <div class="mt-6">
        <h3 class="mb-2 font-medium">设备列表</h3>

        <div class="mb-4 flex gap-2">
          <Button type="primary" @click="openDeviceSelectModal">
            从资产库选择设备
          </Button>
          <Button
            type="primary"
            :loading="importLoading"
            @click="triggerFileSelect"
          >
            导入CSV
          </Button>
          <Button @click="handleDownloadTemplate"> 下载模板 </Button>
          <input
            ref="csvFileInputRef"
            type="file"
            accept=".csv"
            style="display: none"
            @change="handleCSVImport"
          />
        </div>

        <SelectedDevicesGrid>
          <!-- <template #bodyCell="{ column, record }"> -->
          <!-- <template v-if="column.field === 'device_sn'">
              <a-input v-model:value="record.device_sn" placeholder="请输入SN" />
            </template>
            <template v-else-if="column.field === 'device_name'">
              <a-input v-model:value="record.device_name" placeholder="请输入设备名称" />
            </template>
            <template v-else-if="column.field === 'device_type'">
              <a-select v-model:value="record.device_type" placeholder="设备类型" style="width: 100%">
                <a-select-option value="服务器">服务器</a-select-option>
                <a-select-option value="交换机">交换机</a-select-option>
                <a-select-option value="路由器">路由器</a-select-option>
              </a-select>
            </template>
            <template v-else-if="column.field === 'vendor'">
              <a-input v-model:value="record.vendor" placeholder="厂商" />
            </template>
            <template v-else-if="column.field === 'model'">
              <a-input v-model:value="record.model" placeholder="型号" />
            </template>
          </template> -->
          <template #action="{ row }">
            <Button type="link" danger @click="() => removeDevice(row.key)">
              删除
            </Button>
          </template>
        </SelectedDevicesGrid>
      </div>

      <Modal
        v-model:open="modalVisible"
        title="选择设备"
        width="900px"
        :body-style="{ height: '650px', overflow: 'auto' }"
        @ok="addSelectedDevices"
        @cancel="modalVisible = false"
        :destroy-on-close="true"
      >
        <DeviceSearchGrid>
          <template #hostname="{ row }">
            {{ row.resource?.hostname || '-' }}
          </template>
          <template #vpcIP="{ row }">
            {{ row.resource?.vpcIP || '-' }}
          </template>
          <template #bmcIP="{ row }">
            {{ row.resource?.bmcIP || '-' }}
          </template>
          <template #assetStatus="{ row }">
            <Tag
              v-if="row.assetStatus && assetStatusMap[row.assetStatus]"
              :color="assetStatusMap[row.assetStatus]?.color"
            >
              {{ assetStatusMap[row.assetStatus]?.text }}
            </Tag>
            <span v-else>{{ row.assetStatus || '-' }}</span>
          </template>
          <template #empty>
            <div class="py-4 text-center text-gray-500">
              {{ searchLoading ? '加载中...' : '暂无数据' }}
            </div>
          </template>
        </DeviceSearchGrid>
      </Modal>
    </template>

    <template #footer>
      <div class="flex justify-end">
        <Button class="mr-2" @click="drawerApi.close">取消</Button>
        <Button :loading="loading" type="primary" @click="handleSubmit">
          提交
        </Button>
      </div>
    </template>
  </Drawer>
</template>
