<script setup lang="ts">
import type { SelectOption } from '@vben/types';

import { onMounted, ref, shallowRef } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import { Button, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getRegionTableApi } from '#/api/core/cmdb/location/region';

import RackingRequest from './components/racking-request.vue';
import { createRackingFormConfig } from './config/formConfig';
import { createRackingGridConfig } from './config/gridConfig';
import { getStatusTagColor, STATUS_MAP } from './constants/statusMaps';

const projectOptions = shallowRef<SelectOption[]>([]);

/** 资产上架查询条件 */
const rackingForm = createRackingFormConfig(projectOptions.value);

/** 资产上架表格配置 */
const rackingGrid = createRackingGridConfig();

const rackingRequestRef = ref();
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: rackingForm,
  gridOptions: rackingGrid,
});
const router = useRouter();

const handleDetail = (row: any) => {
  router.push(`/hardware-maintenance/asset-racking-detail/${row.ID}`);
};

const add = () => {
  rackingRequestRef.value?.open().then(() => {
    gridApi.query();
  });
};

// 处理表单提交成功
function handleFormSubmitSuccess() {
  gridApi.reload();
}

const onLoad = async () => {
  getRegionTableApi({
    page: 1,
    pageSize: 1000,
  }).then((res) => {
    projectOptions.value = res.list.map((item) => ({
      label: item.name,
      value: item.name,
    }));
  });
};

onMounted(() => {
  onLoad();
});
</script>

<template>
  <div class="w-full">
    <Page auto-content-height>
      <Grid>
        <template #toolbar-buttons>
          <Button class="mr-2" type="primary" @click="add()"> 新增 </Button>
        </template>
        <template #action="{ row }">
          <Button type="link" size="small" @click="handleDetail(row)">
            详情
          </Button>
        </template>

        <!-- 自定义状态列 -->
        <template #status_column="{ row }">
          <Tag :color="getStatusTagColor(row.status)">
            {{ STATUS_MAP[row.status] || row.status }}
          </Tag>
        </template>
      </Grid>
    </Page>

    <RackingRequest
      ref="rackingRequestRef"
      @success="handleFormSubmitSuccess"
    />
  </div>
</template>
