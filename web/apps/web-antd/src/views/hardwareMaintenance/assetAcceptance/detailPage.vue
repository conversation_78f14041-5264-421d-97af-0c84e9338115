<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import {
  Button,
  Card,
  Descriptions,
  Divider,
  message,
  Modal,
  Steps,
  Tag,
  Typography,
} from 'ant-design-vue';
import dayjs from 'dayjs';

import BatchFailReasonModel from './components/BatchFailReasonModel.vue';
import ConfirmDialog from './components/ConfirmDialog.vue';
import DeviceComponentDetailModal from './components/DeviceComponentDetailModal.vue';
import ModelPNMappingModal from './components/ModelPNMappingModal.vue';
import useAcceptanceDetail from './hooks/useAcceptanceDetail';
import { getStatusText } from './utils';

const { Title } = Typography;
const route = useRoute();
const {
  loading,
  actionLoading,
  uploadLoading,
  fileInputRef,
  orderDetail,
  currentStep,
  isResultEditable,
  Grid,
  gridApi,
  historyList,
  historyLoading,
  init,
  goBack,
  handleAuditOrder,
  handleUploadClick,
  handleFileChange,
  confirmUploadFiles,
  cancelUpload,
  removeSelectedFile,
  formatFileSize,
  handleAddMoreFiles,
  setAllPass,
  openBatchFailModal,
  handleFillComponentPN,
  handleCompleteOrder,
  fetchHistory,
  deviceDetailVisible,
  showDeviceDetail,
  currentDevice,
  selectedFiles,
  fileUploadVisible,
  showFailedOnly,
  isOrderCompleted,
  toggleFailedFilter,
  exportFailedSNVisible,
  failedDeviceSNs,
  exportFailedDeviceSNs,
  copyFailedSNsToClipboard,
  completeErrorVisible,
  completeErrorData,
  batchFailModalVisible,
  confirmBatchFail,
} = useAcceptanceDetail();

// 额外文件输入的ref
const additionalFileInputRef = ref<HTMLInputElement | null>(null);

// 添加更多文件的处理函数
const handleAddMoreClick = () => {
  if (additionalFileInputRef.value) {
    additionalFileInputRef.value.click();
  }
};

// 定义卡片可见性状态的类型
type CardVisibilityKeys = 'baseInfo' | 'deviceList' | 'operations';

// 控制卡片展开/折叠状态
const cardVisible = ref({
  baseInfo: true,
  operations: true,
  deviceList: true,
});

// 控制确认框显示状态
const confirmDialogState = ref({
  approve: {
    visible: false,
    title: '审核确认',
    content: '确定要通过该验收工单吗？',
    loading: false,
  },
  reject: {
    visible: false,
    title: '拒绝确认',
    content: '确定要拒绝该验收工单吗？',
    confirmType: 'danger' as 'danger',
    loading: false,
  },
  upload: {
    visible: false,
    title: '上传确认',
    content: '确定要上传检测结果吗？',
    extraContent: '注意：只能上传JSON格式文件',
    extraContentType: 'warning' as 'warning',
    loading: false,
  },
  complete: {
    visible: false,
    title: '完成确认',
    content: '确定要完成该验收工单吗？系统将检查所有设备是否都已设置验收结果。',
    loading: false,
  },
  fillPN: {
    visible: false,
    title: '自动填入PN确认',
    content:
      '确定要自动填入所有设备的组件PN吗？系统将根据型号PN映射关系为CPU、GPU、主板、内存、硬盘、网卡等组件进行自动填入。',
    extraContent: '注意：此操作将覆盖已有的PN值',
    extraContentType: 'warning' as 'warning',
    loading: false,
  },
});

// 设备列表引用和高亮状态
const deviceListRef = ref<HTMLElement | null>(null);
const deviceListHighlight = ref(false);
const pulseAnimationClass = ref('');

// 型号PN映射弹窗状态
const modelPNMappingVisible = ref(false);

// 切换卡片展开状态
const toggleCardVisibility = (key: CardVisibilityKeys) => {
  cardVisible.value[key] = !cardVisible.value[key];
};

// 显示确认框
const showConfirmDialog = (
  type: 'approve' | 'complete' | 'fillPN' | 'reject' | 'upload',
) => {
  confirmDialogState.value[type].visible = true;
  confirmDialogState.value[type].loading = false;
};

// 取消确认
const handleCancel = (
  type: 'approve' | 'complete' | 'fillPN' | 'reject' | 'upload',
) => {
  confirmDialogState.value[type].visible = false;
};

// 显示设备列表高亮效果，并在一定时间后自动移除
const highlightDeviceList = () => {
  deviceListHighlight.value = true;
  // 移除旧的动画类(如果有)
  pulseAnimationClass.value = '';
  // 强制重绘后添加新的动画类
  setTimeout(() => {
    pulseAnimationClass.value = 'pulse-animation';
    // 动画结束后移除高亮
    setTimeout(() => {
      deviceListHighlight.value = false;
      pulseAnimationClass.value = '';
    }, 3000);
  }, 10);
};

// 检查所有设备是否都有验收结果
const checkAllDevicesHaveResult = () => {
  if (!gridApi || !gridApi.grid) {
    return false;
  }

  try {
    const tableData = gridApi.grid.getData() || [];

    if (tableData.length === 0) {
      return true; // 没有设备，不需要验证
    }

    // 检查是否有未设置验收结果的设备
    const hasUndecided = tableData.some(
      (device: any) => device.is_pass === null || device.is_pass === undefined,
    );

    return !hasUndecided;
  } catch (error) {
    console.error('获取设备数据失败:', error);
    return false;
  }
};

// 处理审核通过确认
const confirmApprove = async () => {
  confirmDialogState.value.approve.loading = true;
  await handleAuditOrder(true);
  confirmDialogState.value.approve.visible = false;
  confirmDialogState.value.approve.loading = false;
};

// 处理审核拒绝确认
const confirmReject = async () => {
  confirmDialogState.value.reject.loading = true;
  await handleAuditOrder(false);
  confirmDialogState.value.reject.visible = false;
  confirmDialogState.value.reject.loading = false;
};

// 处理上传确认
const confirmUpload = () => {
  confirmDialogState.value.upload.visible = false;
  handleUploadClick();
};

// 处理完成确认
const confirmComplete = async () => {
  if (!checkAllDevicesHaveResult()) {
    // 关闭确认框
    confirmDialogState.value.complete.visible = false;

    // 确保设备列表卡片是展开的
    cardVisible.value.deviceList = true;

    // 高亮并滚动到设备列表
    highlightDeviceList();

    // 显示提示信息
    message.error('存在处于验收中状态的设备，请先完成所有设备的验收结果设置');

    // 滚动到设备列表位置
    setTimeout(() => {
      if (deviceListRef.value) {
        deviceListRef.value.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });
      }
    }, 100);

    return;
  }

  confirmDialogState.value.complete.loading = true;
  await handleCompleteOrder();
  confirmDialogState.value.complete.visible = false;
  confirmDialogState.value.complete.loading = false;
};

// 处理自动填入PN确认
const confirmFillPN = async () => {
  confirmDialogState.value.fillPN.loading = true;
  await handleFillComponentPN();
  confirmDialogState.value.fillPN.visible = false;
  confirmDialogState.value.fillPN.loading = false;
};

// 英文操作类型到中文的映射
const activityCategoryMap: Record<string, string> = {
  Create: '创建',
  Approve: '审核通过',
  Handle: '上架',
  Complete: '完成',
  Reject: '拒绝',
  Collect: '上传结果',
  // 可根据实际后端返回类型继续补充
};

// 英文状态到中文的映射
const statusMap: Record<string, string> = {
  PENDING: '待审核',
  APPROVED: '已审核',
  COLLECTED: '已上传',
  COMPLETED: '已完成',
  REJECTED: '已拒绝',
  CANCELLED: '已取消',
  // 根据实际后端返回继续补充
};

// 打开型号PN映射管理弹窗
const openModelPNMapping = () => {
  modelPNMappingVisible.value = true;
};

// 复制完结错误信息到剪贴板
const copyCompleteErrorInfo = async () => {
  try {
    const errorInfo = completeErrorData.value;
    let content = '完结工单失败，详细错误信息：\n\n';

    if (errorInfo.invalidDeviceSNs.length > 0) {
      content += `无效设备SN（${errorInfo.invalidDeviceSNs.length}个）：\n`;
      errorInfo.invalidDeviceSNs.forEach((sn, index) => {
        content += `${index + 1}. ${sn}\n`;
      });
      content += '说明：这些设备在检测结果中不存在，已自动设置为验收不通过\n\n';
    }

    if (errorInfo.missingPNModels.length > 0) {
      content += `缺少PN信息的型号（${errorInfo.missingPNModels.length}个）：\n`;
      errorInfo.missingPNModels.forEach((model, index) => {
        content += `${index + 1}. ${model}\n`;
      });
      content += '说明：这些型号的设备组件PN需要补充\n\n';
    }

    if (errorInfo.needCMDBPNs.length > 0) {
      content += `需要录入CMDB的PN（${errorInfo.needCMDBPNs.length}个）：\n`;
      errorInfo.needCMDBPNs.forEach((pn, index) => {
        content += `${index + 1}. ${pn}\n`;
      });
      content += '说明：这些组件PN信息需要录入到CMDB中\n\n';
    }

    if (errorInfo.dbExistingComponentSNs.length > 0) {
      content += `CMDB中已存在的组件SN（${errorInfo.dbExistingComponentSNs.length}个）：\n`;
      errorInfo.dbExistingComponentSNs.forEach((sn, index) => {
        content += `${index + 1}. ${sn}\n`;
      });
      content +=
        '说明：这些组件SN已存在于CMDB中，无法重复插入，请联系管理员处理\n';
    }

    await navigator.clipboard.writeText(content);
    message.success('错误信息已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    message.error('复制失败，请手动复制');
  }
};

onMounted(async () => {
  const id = route.params.id;
  if (id) {
    // 调用初始化函数，传入ID
    await init(Number(id));
    // 手动刷新表格数据，但只执行一次
    if (gridApi && gridApi.reload) {
      gridApi.reload();
    }
    // 加载历史数据
    await fetchHistory(Number(id));
  }
});
</script>

<template>
  <div class="min-h-screen bg-gray-50 pb-8 dark:bg-gray-900">
    <!-- 顶部标题栏 -->
    <div
      class="mb-6 border-b border-gray-200 bg-white shadow-md dark:border-gray-700 dark:bg-gray-800"
    >
      <div
        class="mx-auto flex max-w-full items-center justify-between px-6 py-4"
      >
        <div class="flex items-center">
          <Button
            type="primary"
            @click="goBack"
            class="mr-5 flex items-center overflow-hidden whitespace-nowrap rounded-md bg-blue-600 px-4 py-1.5 font-medium text-white shadow-sm transition-all duration-300 hover:bg-blue-700 hover:shadow-md"
          >
            <template #icon><span class="anticon mr-1">←</span></template>
            返回
          </Button>
          <Title :level="3" class="m-0 text-gray-800 dark:text-gray-200">
            验收工单详情
          </Title>
        </div>
        <div class="flex items-center space-x-3">
          <!-- 操作按钮区域，根据状态显示不同按钮 -->
          <div
            v-if="
              orderDetail &&
              orderDetail.Status !== 'COMPLETED' &&
              orderDetail.Status !== 'REJECTED' &&
              orderDetail.Status !== 'CANCELLED'
            "
          >
            <!-- 待审核状态显示审核按钮 -->
            <template v-if="orderDetail.Status === 'PENDING'">
              <div class="flex space-x-2">
                <button
                  class="flex transform items-center rounded-lg bg-gradient-to-r from-green-500 to-green-600 px-4 py-1.5 font-medium text-white shadow transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg focus:outline-none"
                  :class="{ 'cursor-not-allowed opacity-70': actionLoading }"
                  @click="showConfirmDialog('approve')"
                  :disabled="actionLoading"
                >
                  <span class="mr-1">✓</span>
                  通过
                </button>
                <button
                  class="flex transform items-center rounded-lg bg-gradient-to-r from-red-500 to-red-600 px-4 py-1.5 font-medium text-white shadow transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg focus:outline-none"
                  :class="{ 'cursor-not-allowed opacity-70': actionLoading }"
                  @click="showConfirmDialog('reject')"
                  :disabled="actionLoading"
                >
                  <span class="mr-1">✗</span>
                  拒绝
                </button>
              </div>
            </template>

            <!-- 已通过状态显示上传检测结果按钮 -->
            <template v-if="orderDetail.Status === 'APPROVED'">
              <button
                class="flex transform items-center rounded-lg bg-gradient-to-r from-blue-500 to-indigo-600 px-4 py-1.5 font-medium text-white shadow transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg focus:outline-none"
                :class="{ 'cursor-not-allowed opacity-70': uploadLoading }"
                @click="showConfirmDialog('upload')"
                :disabled="uploadLoading"
              >
                <span class="mr-1">↑</span>
                上传结果
              </button>
              <!-- 隐藏的文件输入 -->
              <input
                ref="fileInputRef"
                type="file"
                multiple
                style="display: none"
                accept=".json,application/json"
                @change="handleFileChange"
              />
              <!-- 隐藏的额外文件输入，用于添加更多文件 -->
              <input
                ref="additionalFileInputRef"
                type="file"
                multiple
                style="display: none"
                accept=".json,application/json"
                @change="handleAddMoreFiles"
              />
            </template>

            <!-- 进行中状态显示完成按钮 -->
            <template v-if="orderDetail.Status === 'COLLECTED'">
              <button
                class="flex transform items-center rounded-lg bg-gradient-to-r from-purple-500 to-purple-600 px-4 py-1.5 font-medium text-white shadow transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg focus:outline-none"
                :class="{ 'cursor-not-allowed opacity-70': actionLoading }"
                @click="showConfirmDialog('complete')"
                :disabled="actionLoading"
              >
                <span class="mr-1">✓</span>
                完成
              </button>
            </template>
          </div>

          <!-- 工单状态标签 -->
          <div v-if="orderDetail" class="flex items-center">
            <Tag
              :color="
                orderDetail.Status === 'COMPLETED'
                  ? 'success'
                  : orderDetail.Status === 'REJECTED'
                    ? 'error'
                    : orderDetail.Status === 'CANCELLED'
                      ? 'warning'
                      : 'processing'
              "
              class="rounded-full px-3 py-1 text-sm font-medium"
            >
              {{ getStatusText(orderDetail.Status) }}
            </Tag>
          </div>
        </div>
      </div>
    </div>

    <div class="mx-auto max-w-full px-6">
      <!-- 加载中状态 -->
      <div
        v-if="loading"
        class="flex h-80 items-center justify-center rounded-xl bg-white p-8 shadow-md dark:bg-gray-800"
      >
        <div class="text-center">
          <div class="mb-4 text-blue-500 dark:text-blue-400">
            <svg
              class="mx-auto h-16 w-16 animate-spin"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              />
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
          </div>
          <div class="text-xl font-medium text-gray-700 dark:text-gray-300">
            加载中，请稍候...
          </div>
        </div>
      </div>

      <div v-else-if="orderDetail" class="space-y-6">
        <!-- 基本信息 -->
        <Card
          class="overflow-hidden rounded-xl border-0 shadow-md transition-all duration-300 hover:shadow-lg"
        >
          <template #title>
            <div
              @click="toggleCardVisibility('baseInfo')"
              class="flex cursor-pointer items-center justify-between"
            >
              <div class="flex items-center text-lg font-medium">
                <span class="mr-2 text-blue-500">📋</span>
                基本信息
              </div>
              <span class="text-gray-400 hover:text-blue-500">
                {{ cardVisible.baseInfo ? '▼' : '►' }}
              </span>
            </div>
          </template>
          <div
            v-show="cardVisible.baseInfo"
            class="transform transition-all duration-500"
          >
            <div class="px-6 py-4">
              <Descriptions
                bordered
                :column="{ xxl: 3, xl: 3, lg: 2, md: 2, sm: 1, xs: 1 }"
                class="bg-transparent"
              >
                <Descriptions.Item label="工单号" class="dark:border-gray-700">
                  <span class="font-medium text-gray-800 dark:text-gray-200">{{
                    orderDetail.OrderNo
                  }}</span>
                </Descriptions.Item>
                <Descriptions.Item
                  label="工单标题"
                  class="dark:border-gray-700"
                >
                  <span class="font-medium text-gray-800 dark:text-gray-200">{{
                    orderDetail.Title
                  }}</span>
                </Descriptions.Item>
                <Descriptions.Item label="状态" class="dark:border-gray-700">
                  <Tag
                    :color="
                      orderDetail.Status === 'COMPLETED'
                        ? 'success'
                        : orderDetail.Status === 'REJECTED'
                          ? 'error'
                          : orderDetail.Status === 'CANCELLED'
                            ? 'warning'
                            : 'processing'
                    "
                    class="rounded-full px-3 py-1"
                  >
                    {{ getStatusText(orderDetail.Status) }}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item
                  label="设备数量"
                  class="dark:border-gray-700"
                >
                  <span
                    class="flex items-center font-medium text-gray-800 dark:text-gray-200"
                  >
                    <span class="mr-1 text-blue-500">🔢</span>
                    {{ orderDetail.DeviceCount }}
                  </span>
                </Descriptions.Item>
                <Descriptions.Item label="发起人" class="dark:border-gray-700">
                  <span
                    class="flex items-center font-medium text-gray-800 dark:text-gray-200"
                  >
                    <span class="mr-1 text-green-500">👤</span>
                    {{ orderDetail.InitiatorName }}
                  </span>
                </Descriptions.Item>
                <Descriptions.Item
                  label="创建时间"
                  class="dark:border-gray-700"
                >
                  <span
                    class="flex items-center font-medium text-gray-800 dark:text-gray-200"
                  >
                    <span class="mr-1 text-purple-500">🕒</span>
                    {{ orderDetail.CreatedAt }}
                  </span>
                </Descriptions.Item>
                <Descriptions.Item
                  label="描述"
                  :span="3"
                  class="dark:border-gray-700"
                >
                  <div
                    class="whitespace-pre-line rounded-md bg-gray-50 p-3 text-gray-700 dark:bg-gray-800/50 dark:text-gray-300"
                  >
                    {{ orderDetail.Description || '无描述信息' }}
                  </div>
                </Descriptions.Item>
              </Descriptions>
            </div>
          </div>
        </Card>

        <!-- 工单流程步骤 -->
        <Card
          class="overflow-hidden rounded-xl border-0 shadow-md transition-all duration-300 hover:shadow-lg"
        >
          <template #title>
            <div
              @click="toggleCardVisibility('operations')"
              class="flex cursor-pointer items-center justify-between"
            >
              <div class="flex items-center text-lg font-medium">
                <span class="mr-2 text-green-500">⚙️</span>
                处理进度
              </div>
              <span class="text-gray-400 hover:text-blue-500">
                {{ cardVisible.operations ? '▼' : '►' }}
              </span>
            </div>
          </template>
          <div
            v-show="cardVisible.operations"
            class="transform transition-all duration-500"
          >
            <div class="px-6 py-2">
              <Steps
                v-if="currentStep >= 0"
                :current="currentStep"
                class="py-3"
                progress-dot
              >
                <Steps.Step title="提交申请" />
                <Steps.Step title="审批中" />
                <Steps.Step title="上传检测结果" />
                <Steps.Step title="验收结果" />
                <Steps.Step title="完成" />
              </Steps>

              <!-- 如果工单被拒绝或取消，显示特殊状态 -->
              <div
                v-if="currentStep < 0"
                class="mt-4 rounded-lg bg-red-50 p-4 text-red-500 dark:bg-red-900/30"
              >
                <div class="flex items-center">
                  <span class="mr-3 text-2xl">⚠️</span>
                  <div>
                    <div class="text-lg font-medium">
                      {{
                        orderDetail.Status === 'REJECTED'
                          ? '工单已被拒绝'
                          : '工单已取消'
                      }}
                    </div>
                    <div class="mt-1 text-sm text-red-400">
                      {{
                        orderDetail.Status === 'REJECTED'
                          ? '请检查申请信息并重新提交'
                          : '工单已被取消，无需进一步处理'
                      }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>

        <!-- 设备列表 -->
        <Card
          ref="deviceListRef"
          class="overflow-hidden rounded-xl border-0 shadow-md transition-all duration-300 hover:shadow-lg"
          :class="[
            { 'highlight-border': deviceListHighlight },
            pulseAnimationClass,
          ]"
        >
          <template #title>
            <div
              @click="toggleCardVisibility('deviceList')"
              class="flex cursor-pointer items-center justify-between"
            >
              <div class="flex items-center text-lg font-medium">
                <span class="mr-2 text-amber-500">📱</span>
                设备列表
              </div>
              <span class="text-gray-400 hover:text-blue-500">
                {{ cardVisible.deviceList ? '▼' : '►' }}
              </span>
            </div>
          </template>

          <!-- 批量操作按钮，只在验收结果阶段显示 -->
          <template #extra v-if="isResultEditable">
            <div class="flex gap-3">
              <Button
                @click="setAllPass"
                class="flex items-center rounded-full border-2 border-green-500 bg-green-50 px-4 py-1.5 font-medium text-green-600 transition-colors duration-300 hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-800/40"
              >
                <span class="mr-1">✓</span>
                全部通过
              </Button>
              <Button
                @click="openBatchFailModal"
                class="flex items-center rounded-full border-2 border-red-500 bg-red-50 px-4 py-1.5 font-medium text-red-600 transition-colors duration-300 hover:bg-red-100 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-800/40"
              >
                <span class="mr-1">✗</span>
                全部不通过
              </Button>
              <Button
                @click="showConfirmDialog('fillPN')"
                :loading="actionLoading"
                class="flex items-center rounded-full border-2 border-orange-500 bg-orange-50 px-4 py-1.5 font-medium text-orange-600 transition-colors duration-300 hover:bg-orange-100 dark:bg-orange-900/30 dark:text-orange-400 dark:hover:bg-orange-800/40"
              >
                <span class="mr-1">⚡</span>
                自动填入PN
              </Button>
              <Button
                @click="openModelPNMapping"
                class="flex items-center rounded-full border-2 border-blue-500 bg-blue-50 px-4 py-1.5 font-medium text-blue-600 transition-colors duration-300 hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-800/40"
              >
                <span class="mr-1">🔧</span>
                型号PN映射
              </Button>
            </div>
          </template>

          <!-- 完成后的筛选按钮，只在工单完成后显示 -->
          <template #extra v-else-if="isOrderCompleted">
            <div class="flex gap-3">
              <Button
                @click="toggleFailedFilter"
                class="flex items-center rounded-full border-2 px-4 py-1.5 font-medium transition-colors duration-300"
                :class="[
                  showFailedOnly
                    ? 'border-red-500 bg-red-50 text-red-600 hover:bg-red-100 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-800/40'
                    : 'border-gray-500 bg-gray-50 text-gray-600 hover:bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400 dark:hover:bg-gray-800/40',
                ]"
              >
                <span class="mr-1">{{ showFailedOnly ? '🔍' : '📋' }}</span>
                {{ showFailedOnly ? '查看全部设备' : '只看验收不通过' }}
              </Button>
              <Button
                @click="exportFailedDeviceSNs"
                class="flex items-center rounded-full border-2 border-purple-500 bg-purple-50 px-4 py-1.5 font-medium text-purple-600 transition-colors duration-300 hover:bg-purple-100 dark:bg-purple-900/30 dark:text-purple-400 dark:hover:bg-purple-800/40"
              >
                <span class="mr-1">📤</span>
                导出不通过设备SN
              </Button>
            </div>
          </template>

          <div
            v-show="cardVisible.deviceList"
            class="transform transition-all duration-500"
          >
            <Divider v-if="isResultEditable" orientation="left">
              <span class="text-sm text-gray-500"
                >请为每个设备设置验收结果</span
              >
            </Divider>

            <div class="px-6 py-4">
              <Grid class="rounded-lg shadow-sm">
                <template #is_pass="{ row }">
                  <Tag
                    v-if="row.is_pass === true"
                    color="success"
                    class="rounded-full px-3 py-1"
                  >
                    <span class="flex items-center">
                      <span class="mr-1">✓</span>
                      通过
                    </span>
                  </Tag>
                  <Tag
                    v-else-if="row.is_pass === false"
                    color="error"
                    class="rounded-full px-3 py-1"
                  >
                    <span class="flex items-center">
                      <span class="mr-1">✗</span>
                      不通过
                    </span>
                  </Tag>
                  <Tag v-else color="default" class="rounded-full px-3 py-1">
                    <span class="flex items-center">
                      <span class="mr-1">⏳</span>
                      待验收
                    </span>
                  </Tag>
                </template>
                <template #is_pass_edit="{ row }">
                  <a-select
                    v-model:value="row.is_pass"
                    style="width: 100%"
                    class="rounded-md"
                    placeholder="请选择验收结果"
                  >
                    <a-select-option :value="true">
                      <span class="flex items-center">
                        <span class="mr-1 text-green-500">✓</span>
                        通过
                      </span>
                    </a-select-option>
                    <a-select-option :value="false">
                      <span class="flex items-center">
                        <span class="mr-1 text-red-500">✗</span>
                        不通过
                      </span>
                    </a-select-option>
                    <a-select-option :value="null">
                      <span class="flex items-center">
                        <span class="mr-1 text-gray-500">↺</span>
                        清除结果
                      </span>
                    </a-select-option>
                  </a-select>
                </template>
                <template #device_detail="{ row }">
                  <Button type="link" @click="showDeviceDetail(row)">
                    详情
                  </Button>
                </template>
              </Grid>
            </div>
          </div>
        </Card>

        <!-- 操作历史卡片 -->
        <Card
          class="overflow-hidden rounded-xl border-0 shadow-md transition-all duration-300 hover:shadow-lg"
        >
          <template #title>
            <div class="flex cursor-pointer items-center justify-between">
              <div class="flex items-center text-lg font-medium">
                <span class="mr-2 text-gray-500">📝</span>
                操作历史
              </div>
            </div>
          </template>
          <div class="px-4 py-3">
            <div v-if="historyLoading" class="text-gray-400">加载中...</div>
            <div v-else-if="historyList && historyList.length > 0">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th
                      class="px-4 py-2 text-left text-xs font-medium text-gray-500"
                    >
                      操作人
                    </th>
                    <th
                      class="px-4 py-2 text-left text-xs font-medium text-gray-500"
                    >
                      操作
                    </th>
                    <th
                      class="px-4 py-2 text-left text-xs font-medium text-gray-500"
                    >
                      备注
                    </th>
                    <th
                      class="px-4 py-2 text-left text-xs font-medium text-gray-500"
                    >
                      时间
                    </th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                  <tr v-for="item in historyList" :key="item.id">
                    <td class="px-4 py-2 text-sm">{{ item.operator_name }}</td>
                    <td class="px-4 py-2 text-sm">
                      {{
                        activityCategoryMap[item.activity_category] ||
                        item.activity_category
                      }}
                      <span
                        v-if="item.previous_status || item.new_status"
                        class="text-xs text-gray-400"
                      >
                        <br />{{
                          statusMap[item.previous_status] ||
                          item.previous_status
                        }}
                        → {{ statusMap[item.new_status] || item.new_status }}
                      </span>
                    </td>
                    <td class="px-4 py-2 text-sm">{{ item.remarks || '-' }}</td>
                    <td class="px-4 py-2 text-sm">
                      {{
                        dayjs(item.operation_time).format('YYYY-MM-DD HH:mm:ss')
                      }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div v-else class="text-gray-400">暂无操作历史</div>
          </div>
        </Card>
      </div>

      <div
        v-else
        class="flex h-80 items-center justify-center rounded-xl bg-white p-8 shadow-md dark:bg-gray-800"
      >
        <div class="text-center">
          <div class="mb-4 text-gray-400">
            <svg
              class="mx-auto h-20 w-20"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <div class="text-xl font-medium text-gray-700 dark:text-gray-300">
            暂无数据
          </div>
          <p class="mt-2 text-gray-500 dark:text-gray-400">
            请检查工单ID是否正确
          </p>
        </div>
      </div>
    </div>

    <!-- 使用新的确认对话框组件 -->
    <!-- 审核通过确认框 -->
    <ConfirmDialog
      v-model:visible="confirmDialogState.approve.visible"
      :title="confirmDialogState.approve.title"
      :content="confirmDialogState.approve.content"
      :loading="confirmDialogState.approve.loading || actionLoading"
      @confirm="confirmApprove"
      @cancel="handleCancel('approve')"
    />

    <!-- 审核拒绝确认框 -->
    <ConfirmDialog
      v-model:visible="confirmDialogState.reject.visible"
      :title="confirmDialogState.reject.title"
      :content="confirmDialogState.reject.content"
      :confirm-type="confirmDialogState.reject.confirmType"
      :loading="confirmDialogState.reject.loading || actionLoading"
      @confirm="confirmReject"
      @cancel="handleCancel('reject')"
    />

    <!-- 上传确认框 -->
    <ConfirmDialog
      v-model:visible="confirmDialogState.upload.visible"
      :title="confirmDialogState.upload.title"
      :content="confirmDialogState.upload.content"
      :extra-content="confirmDialogState.upload.extraContent"
      :extra-content-type="confirmDialogState.upload.extraContentType"
      :loading="uploadLoading"
      @confirm="confirmUpload"
      @cancel="handleCancel('upload')"
    />

    <!-- 完成确认框 -->
    <ConfirmDialog
      v-model:visible="confirmDialogState.complete.visible"
      :title="confirmDialogState.complete.title"
      :content="confirmDialogState.complete.content"
      :loading="confirmDialogState.complete.loading || actionLoading"
      @confirm="confirmComplete"
      @cancel="handleCancel('complete')"
    />

    <!-- 自动填入PN确认框 -->
    <ConfirmDialog
      v-model:visible="confirmDialogState.fillPN.visible"
      :title="confirmDialogState.fillPN.title"
      :content="confirmDialogState.fillPN.content"
      :extra-content="confirmDialogState.fillPN.extraContent"
      :extra-content-type="confirmDialogState.fillPN.extraContentType"
      :loading="confirmDialogState.fillPN.loading || actionLoading"
      @confirm="confirmFillPN"
      @cancel="handleCancel('fillPN')"
    />

    <!-- 在页面底部添加设备组件详情弹窗 -->
    <DeviceComponentDetailModal
      v-model:visible="deviceDetailVisible"
      :item-id="currentDevice?.id"
      :order-id="orderDetail?.ID"
      :order-status="orderDetail?.Status"
    />

    <!-- 型号PN映射管理弹窗 -->
    <ModelPNMappingModal v-model:visible="modelPNMappingVisible" />

    <!-- 导出不通过设备SN弹窗 -->
    <Modal
      v-model:open="exportFailedSNVisible"
      title="验收不通过设备SN列表"
      :width="700"
      :footer="null"
    >
      <div class="space-y-4">
        <div class="rounded-lg border border-yellow-200 bg-yellow-50 p-4">
          <div class="flex items-start">
            <svg
              class="mr-2 mt-0.5 h-5 w-5 flex-shrink-0 text-yellow-600"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                clip-rule="evenodd"
              />
            </svg>
            <div class="text-sm text-yellow-700">
              <p class="font-medium">以下设备验收未通过：</p>
              <p class="mt-1">
                共计
                {{ failedDeviceSNs ? failedDeviceSNs.split(',').length : 0 }}
                台设备
              </p>
            </div>
          </div>
        </div>

        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            设备SN列表（逗号分隔）：
          </label>
          <div class="relative">
            <textarea
              :value="failedDeviceSNs"
              readonly
              class="w-full resize-none rounded-md border border-gray-300 bg-gray-50 p-3 font-mono text-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-200"
              rows="6"
              placeholder="暂无验收不通过的设备"
            ></textarea>
            <Button
              v-if="failedDeviceSNs"
              @click="copyFailedSNsToClipboard"
              type="primary"
              size="small"
              class="absolute right-2 top-2"
            >
              复制
            </Button>
          </div>
        </div>

        <div class="rounded-lg border border-blue-200 bg-blue-50 p-3">
          <div class="flex items-start">
            <svg
              class="mr-2 mt-0.5 h-5 w-5 flex-shrink-0 text-blue-400"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clip-rule="evenodd"
              />
            </svg>
            <div class="text-sm text-blue-700">
              <p class="font-medium">使用说明：</p>
              <ul class="mt-1 list-inside list-disc space-y-1">
                <li>点击复制按钮可将SN列表复制到剪贴板</li>
                <li>SN之间使用逗号分隔，便于导入其他系统</li>
                <li>此列表仅包含验收结果为"不通过"的设备</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3">
          <Button @click="exportFailedSNVisible = false"> 关闭 </Button>
          <Button
            v-if="failedDeviceSNs"
            type="primary"
            @click="copyFailedSNsToClipboard"
          >
            复制SN列表
          </Button>
        </div>
      </div>
    </Modal>

    <!-- 文件上传确认弹窗 -->
    <Modal
      v-model:open="fileUploadVisible"
      title="确认上传文件"
      :width="600"
      @ok="confirmUploadFiles"
      @cancel="cancelUpload"
      :confirm-loading="uploadLoading"
      ok-text="确认上传"
      cancel-text="取消"
    >
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <div class="text-gray-600">
            <p>
              已选择
              <span class="font-semibold text-blue-600">{{
                selectedFiles.length
              }}</span>
              个文件：
            </p>
          </div>
          <Button
            type="dashed"
            @click="handleAddMoreClick"
            :disabled="uploadLoading"
            class="flex items-center"
          >
            <span class="mr-1">➕</span>
            添加更多文件
          </Button>
        </div>

        <div class="max-h-60 space-y-2 overflow-y-auto">
          <div
            v-for="(file, index) in selectedFiles"
            :key="index"
            class="flex items-center justify-between rounded-lg border bg-gray-50 p-3"
          >
            <div class="flex flex-1 items-center space-x-3">
              <div class="flex-shrink-0">
                <svg
                  class="h-8 w-8 text-blue-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
              <div class="min-w-0 flex-1">
                <p class="truncate text-sm font-medium text-gray-900">
                  {{ file.name }}
                </p>
                <p class="text-sm text-gray-500">
                  {{ formatFileSize(file.size) }}
                </p>
              </div>
            </div>
            <Button
              type="text"
              danger
              size="small"
              @click="removeSelectedFile(index)"
              :disabled="uploadLoading"
              class="ml-2 flex-shrink-0"
            >
              删除
            </Button>
          </div>
        </div>

        <div class="rounded-lg border border-blue-200 bg-blue-50 p-3">
          <div class="flex items-start">
            <svg
              class="mr-2 mt-0.5 h-5 w-5 flex-shrink-0 text-blue-400"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clip-rule="evenodd"
              />
            </svg>
            <div class="text-sm text-blue-700">
              <p class="font-medium">上传说明：</p>
              <ul class="mt-1 list-inside list-disc space-y-1">
                <li>只支持 JSON 格式文件</li>
                <li>单个文件大小不能超过 100MB</li>
                <li>可以同时上传多个文件</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </Modal>

    <!-- 完结工单错误信息弹窗 -->
    <Modal
      v-model:open="completeErrorVisible"
      title="完结工单失败"
      :width="1000"
      :footer="null"
    >
      <div class="space-y-4">
        <div class="rounded-lg border border-red-200 bg-red-50 p-4">
          <div class="flex items-start">
            <svg
              class="mr-2 mt-0.5 h-5 w-5 flex-shrink-0 text-red-600"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 001.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clip-rule="evenodd"
              />
            </svg>
            <div class="text-sm text-red-700">
              <p class="font-medium">
                工单完结失败，请处理以下问题后重新提交：
              </p>
            </div>
          </div>
        </div>

        <!-- 无效设备SN -->
        <div
          v-if="completeErrorData.invalidDeviceSNs.length > 0"
          class="space-y-2"
        >
          <div class="flex items-center">
            <svg
              class="mr-2 h-5 w-5 text-red-500"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clip-rule="evenodd"
              />
            </svg>
            <h4 class="font-medium text-gray-900">
              无效设备SN（{{ completeErrorData.invalidDeviceSNs.length }}个）
            </h4>
          </div>
          <div class="rounded-md border border-gray-300 bg-gray-50 p-3">
            <div class="space-y-1">
              <div
                v-for="(sn, index) in completeErrorData.invalidDeviceSNs"
                :key="index"
                class="font-mono text-sm text-gray-700"
              >
                {{ index + 1 }}. {{ sn }}
              </div>
            </div>
            <div class="mt-2 text-xs text-gray-500">
              说明：这些设备在检测结果中不存在，已自动设置为验收不通过
            </div>
          </div>
        </div>

        <!-- 缺少PN信息的型号 -->
        <div
          v-if="completeErrorData.missingPNModels.length > 0"
          class="space-y-2"
        >
          <div class="flex items-center">
            <svg
              class="mr-2 h-5 w-5 text-orange-500"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                clip-rule="evenodd"
              />
            </svg>
            <h4 class="font-medium text-gray-900">
              缺少PN信息的型号（{{
                completeErrorData.missingPNModels.length
              }}个）
            </h4>
          </div>
          <div class="rounded-md border border-gray-300 bg-gray-50 p-3">
            <div class="space-y-1">
              <div
                v-for="(model, index) in completeErrorData.missingPNModels"
                :key="index"
                class="font-mono text-sm text-gray-700"
              >
                {{ index + 1 }}. {{ model }}
              </div>
            </div>
            <div class="mt-2 text-xs text-gray-500">
              说明：这些型号的设备组件PN需要补充
            </div>
          </div>
        </div>

        <!-- 需要录入CMDB的PN -->
        <div v-if="completeErrorData.needCMDBPNs.length > 0" class="space-y-2">
          <div class="flex items-center">
            <svg
              class="mr-2 h-5 w-5 text-blue-500"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clip-rule="evenodd"
              />
            </svg>
            <h4 class="font-medium text-gray-900">
              需要录入CMDB的PN（{{ completeErrorData.needCMDBPNs.length }}个）
            </h4>
          </div>
          <div class="rounded-md border border-gray-300 bg-gray-50 p-3">
            <div class="space-y-1">
              <div
                v-for="(pn, index) in completeErrorData.needCMDBPNs"
                :key="index"
                class="font-mono text-sm text-gray-700"
              >
                {{ index + 1 }}. {{ pn }}
              </div>
            </div>
            <div class="mt-2 text-xs text-gray-500">
              说明：这些组件PN信息需要录入到CMDB中
            </div>
          </div>
        </div>

        <!-- CMDB中已存在的组件SN -->
        <div
          v-if="completeErrorData.dbExistingComponentSNs.length > 0"
          class="space-y-2"
        >
          <div class="flex items-center">
            <svg
              class="mr-2 h-5 w-5 text-purple-500"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clip-rule="evenodd"
              />
            </svg>
            <h4 class="font-medium text-gray-900">
              CMDB中已存在的组件SN（{{
                completeErrorData.dbExistingComponentSNs.length
              }}个）
            </h4>
          </div>
          <div class="rounded-md border border-gray-300 bg-gray-50 p-3">
            <div class="space-y-1">
              <div
                v-for="(sn, index) in completeErrorData.dbExistingComponentSNs"
                :key="index"
                class="font-mono text-sm text-gray-700"
              >
                {{ index + 1 }}. {{ sn }}
              </div>
            </div>
            <div class="mt-2 text-xs text-gray-500">
              说明：这些组件SN已存在于CMDB中，无法重复插入
            </div>
          </div>
        </div>

        <div class="rounded-lg border border-blue-200 bg-blue-50 p-3">
          <div class="flex items-start">
            <svg
              class="mr-2 mt-0.5 h-5 w-5 flex-shrink-0 text-blue-400"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clip-rule="evenodd"
              />
            </svg>
            <div class="text-sm text-blue-700">
              <p class="font-medium">处理建议：</p>
              <ul class="mt-1 list-inside list-disc space-y-1">
                <li>对于无效设备SN：请确认检测结果文件是否完整</li>
                <li>对于缺少PN信息：请使用"型号PN映射"功能补充相关信息</li>
                <li>对于需要录入CMDB的PN：请联系管理员录入CMDB</li>
                <li>对于CMDB中已存在的组件SN：请联系管理员处理重复SN问题</li>
                <li>处理完成后可重新点击"完成"按钮</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3">
          <Button @click="completeErrorVisible = false"> 关闭 </Button>
          <Button type="primary" @click="copyCompleteErrorInfo">
            复制错误信息
          </Button>
        </div>
      </div>
    </Modal>

    <!-- 全部不通过原因弹窗 -->
    <BatchFailReasonModel
      :visible="batchFailModalVisible"
      @update:visible="(val) => (batchFailModalVisible = val)"
      @confirm="confirmBatchFail"
    />
  </div>
</template>

<style scoped>
.highlight-border {
  border: 2px solid #ef4444;
}

.pulse-animation {
  animation: pulse-ring 3s cubic-bezier(0.455, 0.03, 0.515, 0.955) forwards;
}

@keyframes pulse-ring {
  0% {
    box-shadow: 0 0 0 0 rgb(239 68 68 / 70%);
  }

  30% {
    box-shadow: 0 0 0 15px rgb(239 68 68 / 0%);
  }

  60% {
    box-shadow: 0 0 0 0 rgb(239 68 68 / 70%);
  }

  100% {
    box-shadow: 0 0 0 0 rgb(239 68 68 / 0%);
  }
}
</style>
