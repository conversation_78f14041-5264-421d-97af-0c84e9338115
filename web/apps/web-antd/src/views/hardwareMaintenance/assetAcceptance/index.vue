<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type {
  AcceptanceOrder,
  AcceptanceOrderParams,
} from '#/api/core/hardWareMaint/acceptance';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

// import { AccessControl } from '@vben/access';
import { Button, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getAcceptanceListApi } from '#/api/core/hardWareMaint/acceptance';

import CreateAcceptanceForm from './components/CreateAcceptanceForm.vue';

const router = useRouter();

// 组件引用
const createFormRef = ref();

// 表格搜索表单
const formOptions: VbenFormProps = {
  collapsed: true,
  collapsedRows: 1,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入工单号' },
      fieldName: 'orderNo',
      label: '工单号',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入工单标题' },
      fieldName: 'title',
      label: '工单标题',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择工单状态',
        options: [
          { label: '待审核', value: 'PENDING' },
          { label: '已通过', value: 'APPROVED' },
          { label: '已拒绝', value: 'REJECTED' },
          { label: '验收中', value: 'IN_PROGRESS' },
          { label: '已完成', value: 'COMPLETED' },
          { label: '已取消', value: 'CANCELLED' },
        ],
      },
      fieldName: 'status',
      label: '工单状态',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入发起人' },
      fieldName: 'initiatorName',
      label: '发起人',
    },
    {
      component: 'RangePicker',
      componentProps: { placeholder: ['开始日期', '结束日期'] },
      fieldName: 'dateRange',
      label: '创建日期',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: true,
};

// 状态映射
const statusMap: {
  [key: string]: { color: string; text: string };
} = {
  PENDING: { text: '待审核', color: 'warning' },
  APPROVED: { text: '已通过', color: 'success' },
  REJECTED: { text: '已拒绝', color: 'error' },
  COLLECTED: { text: '验证结果', color: 'processing' },
  COMPLETED: { text: '已完成', color: 'success' },
  CANCELLED: { text: '已取消', color: 'default' },
};

// 获取状态显示信息
function getStatusInfo(status: string) {
  return statusMap[status] || { text: status, color: 'default' };
}

// 表格配置
const gridOptions: VxeGridProps<AcceptanceOrder> = {
  checkboxConfig: {
    highlight: true,
  },
  columns: [
    { align: 'center', type: 'checkbox', width: 40 },
    { field: 'ID', title: 'ID', width: 80 },
    { field: 'OrderNo', title: '工单号', width: 150 },
    { field: 'Title', title: '工单标题' },
    {
      field: 'Status',
      title: '状态',
      slots: { default: 'status' },
    },
    { field: 'DeviceCount', title: '设备数量', width: 100 },
    { field: 'InitiatorName', title: '发起人' },
    { field: 'CreatedAt', title: '创建时间', width: 180, visible: false },
    { field: 'UpdatedAt', title: '更新时间', width: 180, visible: false },
    {
      title: '操作',
      slots: { default: 'operate' },
      width: 100,
    },
  ],
  keepSource: true,
  height: 'auto',
  rowConfig: { isHover: true },
  pagerConfig: { pageSize: 10, pageSizes: [10, 20, 50, 100] },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        // 处理日期范围
        const params: AcceptanceOrderParams = {
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        };

        if (formValues.dateRange && formValues.dateRange.length === 2) {
          params.startDate = formValues.dateRange[0];
          params.endDate = formValues.dateRange[1];
          delete params.dateRange;
        }

        const res = await getAcceptanceListApi(params);

        // 后端返回的是包含list和total的结构
        return {
          items: res.list || [],
          total: res.total || 0,
        };
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    zoom: true,
    slots: {
      buttons: 'toolbar-buttons',
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });

// 查看详情事件
function detailEvent(row: AcceptanceOrder) {
  router.push(`/hardware-maintenance/asset-acceptance/${row.ID}`);
}

// 创建工单事件
function createOrderEvent() {
  createFormRef.value?.open();
}

// 处理表单提交成功
function handleFormSubmitSuccess() {
  gridApi.reload();
}
</script>

<template>
  <Page auto-content-height>
    <!-- 表格区域 -->
    <Grid>
      <!-- 添加状态插槽 -->
      <template #status="{ row }">
        <Tag :color="getStatusInfo(row.Status).color">
          {{ getStatusInfo(row.Status).text }}
        </Tag>
      </template>
      <template #operate="{ row }">
        <Button type="link" @click="detailEvent(row)">详情</Button>
      </template>
      <template #toolbar-buttons>
        <!-- <AccessControl :codes="['AssetAcceptance:Create']" type="code"> -->
        <Button class="mr-2" type="primary" @click="createOrderEvent">
          创建验收工单
        </Button>
        <!-- </AccessControl> -->
      </template>
    </Grid>

    <!-- 创建工单表单 -->
    <CreateAcceptanceForm
      ref="createFormRef"
      @success="handleFormSubmitSuccess"
    />
  </Page>
</template>
