// 定义状态项类型
export interface StatusItem {
  text: string;
  color?: string;
}

// 验收工单状态映射
export const STATUS_MAP: Record<string, StatusItem> = {
  PENDING: { text: '待审核', color: 'warning' },
  APPROVED: { text: '已通过', color: 'success' },
  REJECTED: { text: '已拒绝', color: 'error' },
  COLLECTED: { text: '验证结果', color: 'processing' },
  IN_PROGRESS: { text: '验收中', color: 'processing' },
  COMPLETED: { text: '已完成', color: 'success' },
  CANCELLED: { text: '已取消', color: 'default' },
};

// 验收结果选项
export const PASS_OPTIONS = [
  { value: true, label: '通过' },
  { value: false, label: '不通过' },
];

// 工单步骤映射
export const STEP_MAP: Record<string, number> = {
  PENDING: 1, // 审批中
  APPROVED: 2, // 已审批，待上传验收文件
  REJECTED: -1, // 已拒绝，特殊处理
  CANCELLED: -1, // 已取消，特殊处理
  COLLECTED: 3, // 已上传验收文件，验收结果阶段
  COMPLETED: 4, // 已完成
};
