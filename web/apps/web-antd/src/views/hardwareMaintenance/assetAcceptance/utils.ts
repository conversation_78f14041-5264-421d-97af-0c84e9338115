import type { StatusItem } from './constants';

import type { AcceptanceItem } from '#/api/core/hardWareMaint/acceptance';

import { STATUS_MAP, STEP_MAP } from './constants';

/**
 * 获取订单状态信息
 * @param status 订单状态
 * @returns 状态显示信息
 */
export function getStatusInfo(status: string): StatusItem {
  return STATUS_MAP[status] || { text: status, color: 'default' };
}

/**
 * 获取订单状态文本
 * @param status 订单状态
 * @returns 状态显示文本
 */
export function getStatusText(status: string): string {
  return STATUS_MAP[status]?.text || status;
}

/**
 * 获取工单当前步骤
 * @param status 订单状态
 * @returns 当前步骤索引
 */
export function getCurrentStep(status: string): number {
  return STEP_MAP[status] ?? 0;
}

/**
 * 判断是否处于验收结果阶段（可编辑状态）
 * @param status 订单状态
 * @returns 是否可编辑
 */
export function isResultEditable(status: string): boolean {
  return status === 'COLLECTED';
}

/**
 * 设置所有设备为通过
 * @param items 设备列表数据
 * @returns 修改后的设备列表
 */
export function setAllItemsPass(items: AcceptanceItem[]): AcceptanceItem[] {
  const result = items.map((item) => {
    const newItem = {
      ...item,
      is_pass: true,
    };
    return newItem;
  });

  return result;
}

/**
 * 设置所有设备为不通过
 * @param items 设备列表数据
 * @returns 修改后的设备列表
 */
export function setAllItemsFail(items: AcceptanceItem[]): AcceptanceItem[] {
  return items.map((item) => ({
    ...item,
    is_pass: false,
    reason: item.reason || '批量设置不通过',
  }));
}

/**
 * 验证设备验收结果是否完整
 * @param items 设备列表数据
 * @returns 验证结果对象 { valid: 是否有效, message: 错误信息 }
 */
export function validateAcceptanceResults(items: AcceptanceItem[]): {
  message: string;
  valid: boolean;
} {
  // 验证所有设备都已设置验收结果（不能是null或undefined）
  const unprocessedItems = items.filter(
    (item) => item.is_pass === null || item.is_pass === undefined,
  );
  if (unprocessedItems.length > 0) {
    return {
      valid: false,
      message: `还有 ${unprocessedItems.length} 个设备处于验收中状态，请先完成所有设备的验收结果设置`,
    };
  }

  // 验证不通过的设备都有填写原因
  const failItemsWithoutReason = items.filter(
    (item) =>
      item.is_pass === false && (!item.reason || item.reason.trim() === ''),
  );
  if (failItemsWithoutReason.length > 0) {
    return {
      valid: false,
      message: `有 ${failItemsWithoutReason.length} 个不通过的设备未填写原因`,
    };
  }

  return { valid: true, message: '' };
}
