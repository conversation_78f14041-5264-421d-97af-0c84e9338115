<script lang="ts" setup>
import { onUnmounted, ref, watch } from 'vue';

import {
  Button,
  Empty,
  Input,
  message,
  Modal,
  Popconfirm,
  Select,
  Spin,
  Table,
} from 'ant-design-vue';

import {
  createComponentModelPNMappingApi,
  deleteComponentModelPNMappingApi,
  getComponentModelPNMappingListApi,
  updateComponentModelPNMappingApi,
} from '#/api/core/hardWareMaint/acceptance';

const props = defineProps<{
  visible: boolean;
}>();

const emit = defineEmits(['update:visible']);

// 支持的组件类型选项
const componentTypeOptions = [
  { label: 'CPU', value: 'CPU' },
  { label: 'GPU', value: 'GPU' },
  { label: '主板', value: 'MOTHERBOARD' },
  { label: '内存', value: 'MEMORY' },
  { label: '硬盘', value: 'DISK' },
  { label: 'SSD', value: 'SSD' },
  { label: 'HDD', value: 'HDD' },
  { label: 'NVME', value: 'NVME' },
  { label: '网卡', value: 'NETWORK' },
  { label: 'RAID卡', value: 'RAID' },
  { label: '电源', value: 'PSU' },
  { label: '风扇', value: 'FAN' },
];

const loading = ref(false);
const saving = ref(false);
const mappingList = ref<ComponentModelPNMapping[]>([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const editingKey = ref<null | number>(null);
const searchTimeout = ref<null | number>(null);

const newMapping = ref<CreateComponentModelPNMappingRequest>({
  component_type: 'CPU',
  model: '',
  pn: '',
  description: '',
  status: 'ACTIVE',
});

// 搜索过滤器
const searchFilter = ref<ComponentModelPNMappingFilter>({
  component_type: undefined,
  model: undefined,
  pn: undefined,
  status: undefined,
  page: 1,
  pageSize: 10,
});

// 表格列配置
const columns = [
  {
    title: '类型',
    dataIndex: 'component_type',
    key: 'component_type',
    width: 80,
  },
  {
    title: '型号',
    dataIndex: 'model',
    key: 'model',
    width: 200,
    ellipsis: true,
  },
  {
    title: 'PN',
    dataIndex: 'pn',
    key: 'pn',
    width: 150,
    ellipsis: true,
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    width: 150,
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 70,
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 120,
    ellipsis: true,
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right' as const,
  },
];

// 获取映射列表
const fetchMappingList = async () => {
  loading.value = true;
  try {
    const params: ComponentModelPNMappingFilter = {
      ...searchFilter.value,
      page: currentPage.value,
      pageSize: pageSize.value,
    };

    const res = await getComponentModelPNMappingListApi(params);
    mappingList.value = res.list || [];
    total.value = res.total || 0;
  } catch (error) {
    console.error('获取型号PN映射失败:', error);
    message.error('获取型号PN映射失败');
    mappingList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 添加新映射
const addMapping = async () => {
  if (!newMapping.value.model.trim() || !newMapping.value.pn.trim()) {
    message.error('请填写型号和PN');
    return;
  }

  // 检查是否已存在相同的型号
  const exists = mappingList.value.some(
    (item) =>
      item.component_type === newMapping.value.component_type &&
      item.model === newMapping.value.model.trim(),
  );

  if (exists) {
    message.error('该型号已存在映射');
    return;
  }

  saving.value = true;
  try {
    const data: CreateComponentModelPNMappingRequest = {
      ...newMapping.value,
      model: newMapping.value.model.trim(),
      pn: newMapping.value.pn.trim(),
      description: newMapping.value.description?.trim() || '',
    };

    await createComponentModelPNMappingApi(data);

    // 重置表单
    newMapping.value = {
      component_type: 'CPU',
      model: '',
      pn: '',
      description: '',
      status: 'ACTIVE',
    };

    message.success('添加成功');
    await fetchMappingList(); // 重新获取列表
  } catch (error) {
    console.error('添加映射失败:', error);
    message.error('添加映射失败');
  } finally {
    saving.value = false;
  }
};

// 删除映射
const deleteMapping = async (id: number) => {
  saving.value = true;
  try {
    await deleteComponentModelPNMappingApi(id);
    message.success('删除成功');
    await fetchMappingList(); // 重新获取列表
  } catch (error) {
    console.error('删除映射失败:', error);
    message.error('删除映射失败');
  } finally {
    saving.value = false;
  }
};

// 编辑映射
const editMapping = (record: ComponentModelPNMapping) => {
  editingKey.value = record.id;
};

// 保存编辑
const saveEdit = async (record: ComponentModelPNMapping) => {
  if (!record.model.trim() || !record.pn.trim()) {
    message.error('请填写型号和PN');
    return;
  }

  saving.value = true;
  try {
    const data: UpdateComponentModelPNMappingRequest = {
      component_type: record.component_type,
      model: record.model.trim(),
      pn: record.pn.trim(),
      description: record.description?.trim() || '',
      status: record.status,
    };

    await updateComponentModelPNMappingApi(record.id, data);

    editingKey.value = null;
    message.success('保存成功');
    await fetchMappingList(); // 重新获取列表
  } catch (error) {
    console.error('保存映射失败:', error);
    message.error('保存映射失败');
  } finally {
    saving.value = false;
  }
};

// 取消编辑
const cancelEdit = () => {
  editingKey.value = null;
  // 重新获取数据以恢复原始值
  fetchMappingList();
};

// 处理分页变化
const handleTableChange = (pagination: any) => {
  currentPage.value = pagination.current;
  pageSize.value = pagination.pageSize;
  fetchMappingList();
};

// 处理弹窗关闭
const handleClose = () => {
  emit('update:visible', false);
  editingKey.value = null;
  // 清理搜索防抖定时器
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
    searchTimeout.value = null;
  }
};

// 监听弹窗显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      currentPage.value = 1;
      fetchMappingList();
    }
  },
  { immediate: true },
);

// 是否正在编辑
const isEditing = (record: ComponentModelPNMapping) => {
  return editingKey.value === record.id;
};

// 格式化状态显示
const getStatusText = (status: string) => {
  return status === 'ACTIVE' ? '启用' : '禁用';
};

// 格式化状态颜色
const getStatusColor = (status: string) => {
  return status === 'ACTIVE' ? 'success' : 'default';
};

// 格式化时间显示
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-';
  const date = new Date(dateStr);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
  });
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
  fetchMappingList();
};

// 处理重置
const handleReset = () => {
  searchFilter.value = {
    component_type: undefined,
    model: undefined,
    pn: undefined,
    status: undefined,
    page: 1,
    pageSize: 10,
  };
  currentPage.value = 1;
  fetchMappingList();
};

// 处理搜索（带防抖）
const handleSearchDebounced = () => {
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }
  searchTimeout.value = window.setTimeout(() => {
    handleSearch();
  }, 300);
};

// 获取组件类型颜色
const getComponentTypeColor = (type: string) => {
  const colorMap: Record<string, { background: string; color: string }> = {
    CPU: { color: '#1e40af', background: '#dbeafe' },
    GPU: { color: '#7c3aed', background: '#ede9fe' },
    MOTHERBOARD: { color: '#059669', background: '#d1fae5' },
    MEMORY: { color: '#dc2626', background: '#fee2e2' },
    DISK: { color: '#ea580c', background: '#fed7aa' },
    SSD: { color: '#0284c7', background: '#bae6fd' },
    HDD: { color: '#7c2d12', background: '#fef3c7' },
    NVME: { color: '#4338ca', background: '#e0e7ff' },
    NETWORK: { color: '#0284c7', background: '#bae6fd' },
    RAID: { color: '#7c2d12', background: '#fef3c7' },
    PSU: { color: '#4338ca', background: '#e0e7ff' },
    FAN: { color: '#374151', background: '#f3f4f6' },
  };
  return colorMap[type] || { color: '#6b7280', background: '#f9fafb' };
};

// 组件卸载时清理定时器
onUnmounted(() => {
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }
});
</script>

<template>
  <Modal
    :open="props.visible"
    title="设备型号PN映射管理"
    width="1200px"
    :footer="null"
    @cancel="handleClose"
    @update:open="(val) => emit('update:visible', val)"
  >
    <div style="max-height: 70vh; overflow-y: auto">
      <!-- 筛选区域 -->
      <div
        style="
          padding: 12px;
          margin-bottom: 16px;
          background: #fafafa;
          border: 1px solid #d9d9d9;
          border-radius: 6px;
        "
      >
        <div
          style="display: flex; flex-wrap: wrap; gap: 12px; align-items: end"
        >
          <div style="min-width: 120px">
            <div style="margin-bottom: 4px; font-size: 12px; color: #6b7280">
              组件类型
            </div>
            <Select
              v-model:value="searchFilter.component_type"
              style="width: 120px"
              placeholder="全部类型"
              allow-clear
              :options="componentTypeOptions"
              @change="handleSearch"
            />
          </div>
          <div style="flex: 1; min-width: 180px">
            <div style="margin-bottom: 4px; font-size: 12px; color: #6b7280">
              型号筛选
            </div>
            <Input
              v-model:value="searchFilter.model"
              placeholder="输入型号关键词筛选"
              allow-clear
              @change="handleSearchDebounced"
              @press-enter="handleSearch"
            />
          </div>
          <div style="min-width: 100px">
            <div style="margin-bottom: 4px; font-size: 12px; color: #6b7280">
              状态
            </div>
            <Select
              v-model:value="searchFilter.status"
              style="width: 100px"
              placeholder="全部状态"
              allow-clear
              :options="[
                { label: '启用', value: 'ACTIVE' },
                { label: '禁用', value: 'INACTIVE' },
              ]"
              @change="handleSearch"
            />
          </div>
          <Button type="primary" @click="handleSearch" :loading="loading">
            搜索
          </Button>
          <Button @click="handleReset"> 重置 </Button>
        </div>
      </div>

      <!-- 添加新映射区域 -->
      <div
        style="
          padding: 12px;
          margin-bottom: 16px;
          background: #f8fafc;
          border: 1px solid #e2e8f0;
          border-radius: 6px;
        "
      >
        <h4 style="margin: 0 0 12px; font-size: 14px; color: #1f2937">
          添加新映射
        </h4>
        <div
          style="display: flex; flex-wrap: wrap; gap: 10px; align-items: end"
        >
          <div style="min-width: 100px">
            <div style="margin-bottom: 4px; font-size: 12px; color: #6b7280">
              组件类型
            </div>
            <Select
              v-model:value="newMapping.component_type"
              style="width: 100px"
              :options="componentTypeOptions"
            />
          </div>
          <div style="flex: 1; min-width: 160px">
            <div style="margin-bottom: 4px; font-size: 12px; color: #6b7280">
              型号
            </div>
            <Input
              v-model:value="newMapping.model"
              placeholder="请输入型号"
              @press-enter="addMapping"
            />
          </div>
          <div style="flex: 1; min-width: 140px">
            <div style="margin-bottom: 4px; font-size: 12px; color: #6b7280">
              PN
            </div>
            <Input
              v-model:value="newMapping.pn"
              placeholder="请输入PN"
              @press-enter="addMapping"
            />
          </div>
          <div style="flex: 1; min-width: 120px">
            <div style="margin-bottom: 4px; font-size: 12px; color: #6b7280">
              描述
            </div>
            <Input
              v-model:value="newMapping.description"
              placeholder="请输入描述信息"
              @press-enter="addMapping"
            />
          </div>
          <div style="min-width: 80px">
            <div style="margin-bottom: 4px; font-size: 12px; color: #6b7280">
              状态
            </div>
            <Select
              v-model:value="newMapping.status"
              style="width: 80px"
              :options="[
                { label: '启用', value: 'ACTIVE' },
                { label: '禁用', value: 'INACTIVE' },
              ]"
            />
          </div>
          <Button
            type="primary"
            :loading="saving"
            @click="addMapping"
            style="margin-bottom: 0"
          >
            添加
          </Button>
        </div>
      </div>

      <!-- 映射列表 -->
      <Spin :spinning="loading">
        <Table
          :columns="columns"
          :data-source="mappingList"
          :pagination="{
            current: currentPage,
            pageSize,
            total,
            showSizeChanger: true,
            pageSizeOptions: ['10', '20', '50'],
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            size: 'small',
          }"
          row-key="id"
          size="small"
          :scroll="{ x: 850 }"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <!-- 组件类型列 -->
            <template v-if="column.key === 'component_type'">
              <div style="display: flex; align-items: center">
                <span
                  :style="{
                    padding: '2px 6px',
                    fontSize: '11px',
                    color: getComponentTypeColor(record.component_type).color,
                    background: getComponentTypeColor(record.component_type)
                      .background,
                    borderRadius: '3px',
                  }"
                >
                  {{ record.component_type }}
                </span>
              </div>
            </template>

            <!-- 型号列 -->
            <template v-if="column.key === 'model'">
              <Input
                v-if="isEditing(record as ComponentModelPNMapping)"
                v-model:value="record.model"
                style="margin: -5px 0"
                @press-enter="saveEdit(record as ComponentModelPNMapping)"
              />
              <span v-else style="font-family: monospace; font-size: 12px">{{
                record.model
              }}</span>
            </template>

            <!-- PN列 -->
            <template v-if="column.key === 'pn'">
              <Input
                v-if="isEditing(record as ComponentModelPNMapping)"
                v-model:value="record.pn"
                style="margin: -5px 0"
                @press-enter="saveEdit(record as ComponentModelPNMapping)"
              />
              <span v-else style="font-family: monospace; font-size: 12px">{{
                record.pn
              }}</span>
            </template>

            <!-- 描述列 -->
            <template v-if="column.key === 'description'">
              <Input
                v-if="isEditing(record as ComponentModelPNMapping)"
                v-model:value="record.description"
                style="margin: -5px 0"
                placeholder="请输入描述"
                @press-enter="saveEdit(record as ComponentModelPNMapping)"
              />
              <span v-else style="font-size: 12px; color: #6b7280">{{
                record.description || '-'
              }}</span>
            </template>

            <!-- 状态列 -->
            <template v-if="column.key === 'status'">
              <Select
                v-if="isEditing(record as ComponentModelPNMapping)"
                v-model:value="record.status"
                style="width: 70px; margin: -5px 0"
                :options="[
                  { label: '启用', value: 'ACTIVE' },
                  { label: '禁用', value: 'INACTIVE' },
                ]"
              />
              <div v-else style="display: flex; align-items: center">
                <span
                  :style="{
                    padding: '2px 6px',
                    fontSize: '11px',
                    color:
                      getStatusColor(record.status) === 'success'
                        ? '#16a34a'
                        : '#6b7280',
                    background:
                      getStatusColor(record.status) === 'success'
                        ? '#d1fae5'
                        : '#f3f4f6',
                    borderRadius: '3px',
                  }"
                >
                  {{ getStatusText(record.status) }}
                </span>
              </div>
            </template>

            <!-- 创建时间列 -->
            <template v-if="column.key === 'created_at'">
              <span style="font-size: 11px; color: #6b7280">
                {{ formatDate(record.created_at) }}
              </span>
            </template>

            <!-- 操作列 -->
            <template v-if="column.key === 'action'">
              <div
                style="display: flex; gap: 4px"
                v-if="isEditing(record as ComponentModelPNMapping)"
              >
                <Button
                  size="small"
                  type="primary"
                  @click="saveEdit(record as ComponentModelPNMapping)"
                  :loading="saving"
                >
                  保存
                </Button>
                <Button size="small" @click="cancelEdit"> 取消 </Button>
              </div>
              <div style="display: flex; gap: 4px" v-else>
                <Button
                  size="small"
                  type="link"
                  @click="editMapping(record as ComponentModelPNMapping)"
                >
                  编辑
                </Button>
                <Popconfirm
                  title="确定要删除这个映射吗？"
                  @confirm="deleteMapping(record.id)"
                  ok-text="确定"
                  cancel-text="取消"
                >
                  <Button size="small" type="link" danger :loading="saving">
                    删除
                  </Button>
                </Popconfirm>
              </div>
            </template>
          </template>

          <template #emptyText>
            <Empty description="暂无映射数据">
              <template #extra>
                <Button
                  type="primary"
                  @click="
                    () => {
                      newMapping.model = '';
                      newMapping.pn = '';
                      newMapping.description = '';
                    }
                  "
                  size="small"
                >
                  立即添加
                </Button>
              </template>
            </Empty>
          </template>
        </Table>
      </Spin>

      <!-- 底部说明 -->
      <div
        style="
          padding: 8px 12px;
          margin-top: 12px;
          background: #f0f9ff;
          border: 1px solid #0ea5e9;
          border-radius: 4px;
        "
      >
        <div style="font-size: 11px; color: #0369a1">
          <div style="margin-bottom: 3px; font-weight: 600">💡 使用说明：</div>
          <div style="line-height: 1.4">
            • 配置映射关系用于设备组件详情中的快速PN填入 •
            支持CPU、GPU、主板、内存、硬盘、网卡等各种组件的PN映射管理<br />
            • 每个组件类型和型号的组合只能配置一个PN映射 •
            状态：ACTIVE=启用，INACTIVE=禁用 • 可添加描述信息方便管理 •
            修改后实时生效
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>

<style scoped>
:deep(.ant-table-cell) {
  padding: 8px 12px !important;
}

:deep(.ant-table-thead > tr > th) {
  font-weight: 600;
  background: #fafafa;
}
</style>
