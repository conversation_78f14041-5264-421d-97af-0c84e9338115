<script setup lang="ts">
import { ref, watch } from 'vue';

import { Input, message, Modal } from 'ant-design-vue';

const props = defineProps<{
  visible: boolean;
}>();
const emit = defineEmits(['update:visible', 'confirm']);

const reason = ref('');

watch(
  () => props.visible,
  (val) => {
    if (val) reason.value = '';
  },
);

function handleOk() {
  if (!reason.value.trim()) {
    message.error('请填写不通过原因');
    return;
  }
  emit('confirm', reason.value);
  emit('update:visible', false);
}
function handleCancel() {
  emit('update:visible', false);
}
</script>

<template>
  <Modal
    :visible="visible"
    title="批量设置不通过原因"
    @ok="handleOk"
    @cancel="handleCancel"
    destroy-on-close
  >
    <Input.TextArea
      v-model:value="reason"
      placeholder="请输入所有设备不通过的原因"
      :rows="4"
    />
  </Modal>
</template>
