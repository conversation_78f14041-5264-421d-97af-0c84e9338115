<script lang="ts" setup>
import { computed, ref, watch } from 'vue';

import {
  Button,
  Empty,
  Input,
  message,
  Modal,
  Spin,
  Tag,
} from 'ant-design-vue';

import {
  loadAcceptanceItemComponents,
  updateInspectingComponentInfo,
} from '../services';
import { isResultEditable } from '../utils';

const props = defineProps<{
  itemId: null | number | undefined;
  orderId: null | number | undefined;
  orderStatus?: string;
  visible: boolean;
}>();
const emit = defineEmits(['update:visible']);

const loading = ref(false);
const saving = ref(false);
const componentList = ref<any[]>([]);
const expandedGroups = ref<Set<string>>(new Set());
const expandedModels = ref<Set<string>>(new Set());
const editingComponents = ref<Set<number>>(new Set());
const editingValues = ref<Record<number, string>>({});
// 用于存储前端修改的PN值，key为component.id
const modifiedPNs = ref<Record<number, string>>({});

// 加载数据
const fetchData = async () => {
  if (!props.itemId || loading.value) {
    return;
  }

  loading.value = true;
  try {
    const res = await loadAcceptanceItemComponents(props.itemId);
    componentList.value = Array.isArray(res) ? res : [];

    // 清空之前的展开状态
    expandedGroups.value.clear();
    expandedModels.value.clear();

    if (componentList.value.length > 0) {
      // 如果是可编辑状态（待完成阶段），自动展开所有可编辑组件的分组及其型号子分组
      if (canEdit.value) {
        const editableTypes = new Set<string>();
        const editableModels = new Set<string>();

        componentList.value.forEach((component) => {
          const type = component.component_type || '其他';
          // 检查是否是可编辑的组件类型
          if (isComponentPNEditable(type)) {
            editableTypes.add(type);
            const model = component.model || '未知型号';
            editableModels.add(`${type}-${model}`);
          }
        });

        // 展开所有可编辑组件的分组
        editableTypes.forEach((type) => {
          expandedGroups.value.add(type);
        });

        // 展开所有可编辑组件的型号子分组
        editableModels.forEach((modelKey) => {
          expandedModels.value.add(modelKey);
        });

        // 如果没有可编辑组件，则展开第一个分组
        if (editableTypes.size === 0) {
          const firstType = componentList.value[0]?.component_type || '其他';
          expandedGroups.value.add(firstType);
        }
      } else {
        // 非可编辑状态，默认展开第一个分组
        const firstType = componentList.value[0]?.component_type || '其他';
        expandedGroups.value.add(firstType);
      }
    }

    // 清空编辑状态
    editingComponents.value.clear();
    editingValues.value = {};
    modifiedPNs.value = {};
  } catch (error) {
    console.error('获取组件信息失败:', error);
    componentList.value = [];
  } finally {
    loading.value = false;
  }
};

// 统一的watch监听器，避免重复请求
watch(
  () => [props.visible, props.itemId] as const,
  ([visible, itemId]) => {
    if (visible && itemId) {
      fetchData();
    } else if (!visible) {
      // 弹窗关闭时清空数据
      componentList.value = [];
      expandedGroups.value.clear();
      expandedModels.value.clear();
      editingComponents.value.clear();
      editingValues.value = {};
      modifiedPNs.value = {};
    }
  },
  { immediate: true },
);

const handleClose = () => {
  emit('update:visible', false);
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'error': {
      return 'error';
    }
    case 'normal': {
      return 'success';
    }
    default: {
      return 'default';
    }
  }
};

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'error': {
      return '异常';
    }
    case 'normal': {
      return '正常';
    }
    default: {
      return status;
    }
  }
};

// 获取组件显示的PN值（优先显示修改后的值）
const getDisplayPN = (component: any) => {
  return modifiedPNs.value[component.id] === undefined
    ? component.pn || ''
    : modifiedPNs.value[component.id];
};

// 按组件类型分组数据并统计型号
const groupedComponents = computed(() => {
  const groups: Record<
    string,
    {
      components: any[];
      modelGroups: Record<string, any[]>;
      modelStats: Record<string, number>;
      totalCount: number;
    }
  > = {};

  componentList.value.forEach((component) => {
    const type = component.component_type || '其他';
    const model = component.model || '未知型号';

    if (!groups[type]) {
      groups[type] = {
        components: [],
        modelStats: {},
        totalCount: 0,
        modelGroups: {},
      };
    }

    groups[type].components.push(component);
    groups[type].modelStats[model] = (groups[type].modelStats[model] || 0) + 1;
    groups[type].totalCount++;

    // 按型号分组
    if (!groups[type].modelGroups[model]) {
      groups[type].modelGroups[model] = [];
    }
    groups[type].modelGroups[model].push(component);
  });

  return groups;
});

// 切换分组展开状态
const toggleGroup = (groupType: string) => {
  if (expandedGroups.value.has(groupType)) {
    expandedGroups.value.delete(groupType);
  } else {
    expandedGroups.value.add(groupType);
  }
};

// 检查分组是否展开
const isGroupExpanded = (groupType: string) => {
  return expandedGroups.value.has(groupType);
};

// 切换型号子分组展开状态
const toggleModel = (groupType: string, model: string) => {
  const modelKey = `${groupType}-${model}`;
  if (expandedModels.value.has(modelKey)) {
    expandedModels.value.delete(modelKey);
  } else {
    expandedModels.value.add(modelKey);
  }
};

// 检查型号子分组是否展开
const isModelExpanded = (groupType: string, model: string) => {
  const modelKey = `${groupType}-${model}`;
  return expandedModels.value.has(modelKey);
};

// 检查组件类型是否可编辑PN（支持所有组件类型的PN编辑）
const isComponentPNEditable = (componentType: string) => {
  // 支持所有组件类型的PN编辑：CPU、GPU、主板、内存、硬盘、网卡等
  return (
    [
      'CPU',
      'DISK',
      'FAN',
      'GPU',
      'HDD',
      'MEMORY',
      'MOTHERBOARD',
      'NETWORK',
      'NVME',
      'PSU',
      'RAID',
      'SAS',
      'SATA',
      'SSD',
      '主板',
      '内存',
      '电源',
      '硬盘',
      '网卡',
      '风扇',
    ].includes(componentType?.toUpperCase()) || componentType
  ); // 如果不在预定义列表中，默认也允许编辑
};

// 开始编辑PN
const startEditPN = (component: any) => {
  editingComponents.value.add(component.id);
  editingValues.value[component.id] = getDisplayPN(component);
};

// 取消编辑PN
const cancelEditPN = (componentId: number) => {
  editingComponents.value.delete(componentId);
  delete editingValues.value[componentId];
};

// 确认编辑PN（前端修改，不请求后端）
const confirmEditPN = (component: any) => {
  const newPN = (editingValues.value[component.id] || '').trim();
  modifiedPNs.value[component.id] = newPN;
  cancelEditPN(component.id);

  if (newPN !== (component.pn || '')) {
    message.success('PN已修改，请点击"保存所有更改"提交');
  }
};

// 批量填入相同型号的PN
const batchFillSameModelPN = (component: any) => {
  const newPN = (editingValues.value[component.id] || '').trim();
  const componentType = component.component_type;
  const model = component.model;

  if (!newPN) {
    message.warning('请先输入PN值');
    return;
  }

  // 找到相同型号的所有组件
  const sameModelComponents = componentList.value.filter(
    (c) =>
      c.component_type === componentType &&
      c.model === model &&
      c.id !== component.id,
  );

  if (sameModelComponents.length === 0) {
    message.info('没有找到相同型号的其他组件');
    return;
  }

  // 批量填入PN
  sameModelComponents.forEach((c) => {
    modifiedPNs.value[c.id] = newPN;
  });

  // 当前组件也设置
  modifiedPNs.value[component.id] = newPN;
  cancelEditPN(component.id);

  message.success(
    `已批量填入 ${sameModelComponents.length + 1} 个 ${model} 组件的PN`,
  );
};

// 处理PN输入时的自动去空格
const handlePNInput = (componentId: number, value: string) => {
  // 自动去除首尾空格
  editingValues.value[componentId] = value.trim();
};

// 保存所有更改到后端
const saveAllChanges = async () => {
  if (!props.itemId || !props.orderId) {
    message.error('缺少必要的参数信息');
    return;
  }

  const componentInfos = [];

  // 收集所有修改的组件信息
  for (const [componentId, newPN] of Object.entries(modifiedPNs.value)) {
    const component = componentList.value.find(
      (c) => c.id === Number(componentId),
    );
    if (component) {
      componentInfos.push({
        item_id: props.itemId,
        pn: newPN.trim(),
        model: component.model || '',
        component_id: Number(componentId),
        status: component.status || '',
        description: component.description || '',
      });
    }
  }

  if (componentInfos.length === 0) {
    message.warning('没有需要保存的更改');
    return;
  }

  saving.value = true;
  try {
    const success = await updateInspectingComponentInfo(
      props.itemId,
      props.orderId,
      componentInfos,
    );

    if (success) {
      // 更新原始数据
      componentInfos.forEach((info) => {
        const component = componentList.value.find(
          (c) => c.id === info.component_id,
        );
        if (component) {
          component.pn = info.pn;
        }
      });

      // 清空修改记录
      modifiedPNs.value = {};
      message.success(`成功保存 ${componentInfos.length} 个组件的更改`);
    }
  } catch (error) {
    console.error('保存失败:', error);
  } finally {
    saving.value = false;
  }
};

// 检查是否有未保存的更改
const hasUnsavedChanges = computed(() => {
  return Object.keys(modifiedPNs.value).length > 0;
});

// 检查组件是否被修改过
const isComponentModified = (componentId: number) => {
  return modifiedPNs.value[componentId] !== undefined;
};

// 检查是否允许编辑（基于工单状态）
const canEdit = computed(() => {
  return props.orderStatus ? isResultEditable(props.orderStatus) : false;
});
</script>

<template>
  <Modal
    title="设备组件详情"
    :open="props.visible"
    @cancel="handleClose"
    @update:open="(val) => emit('update:visible', val)"
    :footer="null"
    width="1400px"
    destroy-on-close
    :body-style="{ padding: '20px' }"
  >
    <Spin :spinning="loading">
      <template v-if="componentList.length > 0">
        <div
          style="
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
          "
        >
          <span style="font-size: 14px; color: #666">
            共找到 {{ componentList.length }} 个组件
            <span
              v-if="hasUnsavedChanges"
              style="margin-left: 8px; color: #f59e0b"
            >
              (有 {{ Object.keys(modifiedPNs).length }} 个未保存的更改)
            </span>
          </span>

          <!-- 保存所有更改按钮 -->
          <Button
            v-if="hasUnsavedChanges && canEdit"
            type="primary"
            :loading="saving"
            @click="saveAllChanges"
            style="margin-left: auto"
          >
            保存所有更改 ({{ Object.keys(modifiedPNs).length }})
          </Button>
        </div>

        <!-- 按组件类型分组显示 -->
        <div
          v-for="(groupData, type) in groupedComponents"
          :key="type"
          style="margin-bottom: 16px"
        >
          <!-- 分组头部 - 可点击展开/收起 -->
          <div
            @click="toggleGroup(type)"
            style="
              display: flex;
              align-items: center;
              padding: 12px 16px;
              margin-bottom: 8px;
              cursor: pointer;
              background: #f8fafc;
              border: 1px solid #e2e8f0;
              border-radius: 8px;
              transition: all 0.2s;
            "
            :style="{
              background: isGroupExpanded(type) ? '#e0f2fe' : '#f8fafc',
              'border-color': isGroupExpanded(type) ? '#0891b2' : '#e2e8f0',
            }"
          >
            <!-- 展开/收起图标 -->
            <span
              style="
                display: inline-block;
                margin-right: 8px;
                font-size: 12px;
                color: #64748b;
                transition: transform 0.2s;
              "
              :style="{
                transform: isGroupExpanded(type)
                  ? 'rotate(90deg)'
                  : 'rotate(0deg)',
              }"
            >
              ▶
            </span>
            <h4 style="flex: 1; margin: 0; font-weight: 600; color: #1e293b">
              {{ type }} ({{ groupData.totalCount }}个)
              <span
                v-if="isComponentPNEditable(type) && canEdit"
                style="margin-left: 8px; font-size: 12px; color: #10b981"
              >
                可编辑PN
              </span>
            </h4>

            <!-- 型号统计信息 -->
            <div style="display: flex; flex-wrap: wrap; gap: 8px">
              <span
                v-for="(count, model) in groupData.modelStats"
                :key="model"
                style="
                  padding: 4px 8px;
                  font-size: 12px;
                  color: #475569;
                  white-space: nowrap;
                  background: #fff;
                  border: 1px solid #e2e8f0;
                  border-radius: 4px;
                "
              >
                {{ model }}: {{ count }}个
              </span>
            </div>
          </div>

          <!-- 展开的详细信息 -->
          <div v-if="isGroupExpanded(type)">
            <!-- 如果是可编辑的组件类型，按型号进一步分组 -->
            <template v-if="isComponentPNEditable(type)">
              <div
                v-for="(modelComponents, model) in groupData.modelGroups"
                :key="model"
                style="margin-bottom: 12px"
              >
                <!-- 型号子分组头部 -->
                <div
                  @click="toggleModel(type, model)"
                  style="
                    display: flex;
                    align-items: center;
                    padding: 8px 12px;
                    margin-bottom: 4px;
                    margin-left: 20px;
                    cursor: pointer;
                    background: #f1f5f9;
                    border: 1px solid #cbd5e1;
                    border-radius: 6px;
                    transition: all 0.2s;
                  "
                  :style="{
                    background: isModelExpanded(type, model)
                      ? '#dbeafe'
                      : '#f1f5f9',
                    'border-color': isModelExpanded(type, model)
                      ? '#3b82f6'
                      : '#cbd5e1',
                  }"
                >
                  <span
                    style="
                      display: inline-block;
                      margin-right: 8px;
                      font-size: 10px;
                      color: #64748b;
                      transition: transform 0.2s;
                    "
                    :style="{
                      transform: isModelExpanded(type, model)
                        ? 'rotate(90deg)'
                        : 'rotate(0deg)',
                    }"
                  >
                    ▶
                  </span>
                  <span style="flex: 1; font-weight: 500; color: #334155">
                    {{ model }} ({{ modelComponents.length }}个)
                  </span>
                </div>

                <!-- 型号详细表格 -->
                <div
                  v-if="isModelExpanded(type, model)"
                  style="
                    margin-left: 20px;
                    overflow-x: auto;
                    border: 1px solid #e2e8f0;
                    border-radius: 8px;
                  "
                >
                  <table style="width: 100%; border-collapse: collapse">
                    <thead>
                      <tr style="background-color: #f8fafc">
                        <th
                          style="
                            width: 100px;
                            padding: 8px 12px;
                            font-weight: 600;
                            text-align: left;
                            border-bottom: 1px solid #e2e8f0;
                          "
                        >
                          组件类型
                        </th>
                        <th
                          style="
                            min-width: 200px;
                            padding: 8px 12px;
                            font-weight: 600;
                            text-align: left;
                            border-bottom: 1px solid #e2e8f0;
                          "
                        >
                          型号
                        </th>
                        <th
                          style="
                            min-width: 150px;
                            padding: 8px 12px;
                            font-weight: 600;
                            text-align: left;
                            border-bottom: 1px solid #e2e8f0;
                          "
                        >
                          SN
                        </th>
                        <th
                          style="
                            min-width: 200px;
                            padding: 8px 12px;
                            font-weight: 600;
                            text-align: left;
                            border-bottom: 1px solid #e2e8f0;
                          "
                        >
                          PN
                        </th>
                        <th
                          style="
                            min-width: 130px;
                            padding: 8px 12px;
                            font-weight: 600;
                            text-align: left;
                            border-bottom: 1px solid #e2e8f0;
                          "
                        >
                          固件版本
                        </th>
                        <th
                          style="
                            width: 80px;
                            padding: 8px 12px;
                            font-weight: 600;
                            text-align: left;
                            border-bottom: 1px solid #e2e8f0;
                          "
                        >
                          状态
                        </th>
                        <th
                          style="
                            min-width: 120px;
                            padding: 8px 12px;
                            font-weight: 600;
                            text-align: left;
                            border-bottom: 1px solid #e2e8f0;
                          "
                        >
                          描述
                        </th>
                        <th
                          v-if="canEdit"
                          style="
                            width: 120px;
                            padding: 8px 12px;
                            font-weight: 600;
                            text-align: left;
                            border-bottom: 1px solid #e2e8f0;
                          "
                        >
                          操作
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        v-for="(component, index) in modelComponents"
                        :key="component.id"
                        :style="{
                          'background-color':
                            index % 2 === 0 ? '#ffffff' : '#f8fafc',
                        }"
                      >
                        <td
                          style="
                            padding: 8px 12px;
                            border-bottom: 1px solid #f1f5f9;
                          "
                        >
                          <span
                            style="
                              padding: 2px 6px;
                              font-size: 12px;
                              color: #1e40af;
                              background: #dbeafe;
                              border-radius: 4px;
                            "
                          >
                            {{ component.component_type || '-' }}
                          </span>
                        </td>
                        <td
                          style="
                            padding: 8px 12px;
                            word-break: break-all;
                            border-bottom: 1px solid #f1f5f9;
                          "
                        >
                          {{ component.model || '-' }}
                        </td>
                        <td
                          style="
                            padding: 8px 12px;
                            font-family: monospace;
                            word-break: break-all;
                            border-bottom: 1px solid #f1f5f9;
                          "
                        >
                          {{ component.sn || '-' }}
                        </td>
                        <td
                          style="
                            padding: 8px 12px;
                            border-bottom: 1px solid #f1f5f9;
                          "
                        >
                          <!-- PN编辑区域 -->
                          <div
                            v-if="editingComponents.has(component.id)"
                            style="
                              display: flex;
                              flex-direction: column;
                              gap: 4px;
                            "
                          >
                            <div
                              style="
                                display: flex;
                                gap: 4px;
                                align-items: center;
                              "
                            >
                              <Input
                                v-model:value="editingValues[component.id]"
                                size="small"
                                style="min-width: 120px; font-family: monospace"
                                placeholder="输入PN"
                                @input="
                                  (e) =>
                                    handlePNInput(
                                      component.id,
                                      e.target?.value || '',
                                    )
                                "
                                @press-enter="confirmEditPN(component)"
                              />
                            </div>
                            <div
                              style="display: flex; flex-wrap: wrap; gap: 4px"
                            >
                              <Button
                                size="small"
                                type="primary"
                                @click="confirmEditPN(component)"
                              >
                                确认
                              </Button>
                              <Button
                                size="small"
                                type="default"
                                @click="batchFillSameModelPN(component)"
                              >
                                批量填入PN
                              </Button>
                              <Button
                                size="small"
                                @click="cancelEditPN(component.id)"
                              >
                                取消
                              </Button>
                            </div>
                          </div>
                          <div
                            v-else
                            style="display: flex; gap: 8px; align-items: center"
                          >
                            <span
                              style="
                                flex: 1;
                                font-family: monospace;
                                word-break: break-all;
                              "
                              :style="{
                                color: isComponentModified(component.id)
                                  ? '#10b981'
                                  : 'inherit',
                                fontWeight: isComponentModified(component.id)
                                  ? '600'
                                  : 'normal',
                              }"
                            >
                              {{ getDisplayPN(component) || '-' }}
                            </span>
                            <span
                              v-if="isComponentModified(component.id)"
                              style="
                                padding: 1px 4px;
                                font-size: 10px;
                                color: white;
                                background: #10b981;
                                border-radius: 2px;
                              "
                            >
                              已修改
                            </span>
                          </div>
                        </td>
                        <td
                          style="
                            padding: 8px 12px;
                            font-family: monospace;
                            border-bottom: 1px solid #f1f5f9;
                          "
                        >
                          {{ component.firmware || '-' }}
                        </td>
                        <td
                          style="
                            padding: 8px 12px;
                            border-bottom: 1px solid #f1f5f9;
                          "
                        >
                          <Tag :color="getStatusColor(component.status)">
                            {{ getStatusText(component.status) }}
                          </Tag>
                        </td>
                        <td
                          style="
                            padding: 8px 12px;
                            word-break: break-all;
                            border-bottom: 1px solid #f1f5f9;
                          "
                        >
                          {{ component.description || '-' }}
                        </td>
                        <td
                          v-if="canEdit"
                          style="
                            padding: 8px 12px;
                            border-bottom: 1px solid #f1f5f9;
                          "
                        >
                          <!-- 操作按钮 -->
                          <Button
                            v-if="!editingComponents.has(component.id)"
                            size="small"
                            type="link"
                            @click="startEditPN(component)"
                          >
                            编辑PN
                          </Button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </template>

            <!-- 非可编辑组件的原始表格显示 -->
            <template v-else>
              <div
                style="
                  overflow-x: auto;
                  border: 1px solid #e2e8f0;
                  border-radius: 8px;
                "
              >
                <table style="width: 100%; border-collapse: collapse">
                  <thead>
                    <tr style="background-color: #f1f5f9">
                      <th
                        style="
                          width: 100px;
                          padding: 12px;
                          font-weight: 600;
                          text-align: left;
                          border-bottom: 1px solid #e2e8f0;
                        "
                      >
                        组件类型
                      </th>
                      <th
                        style="
                          min-width: 200px;
                          padding: 12px;
                          font-weight: 600;
                          text-align: left;
                          border-bottom: 1px solid #e2e8f0;
                        "
                      >
                        型号
                      </th>
                      <th
                        style="
                          min-width: 150px;
                          padding: 12px;
                          font-weight: 600;
                          text-align: left;
                          border-bottom: 1px solid #e2e8f0;
                        "
                      >
                        SN
                      </th>
                      <th
                        style="
                          min-width: 150px;
                          padding: 12px;
                          font-weight: 600;
                          text-align: left;
                          border-bottom: 1px solid #e2e8f0;
                        "
                      >
                        PN
                      </th>
                      <th
                        style="
                          min-width: 130px;
                          padding: 12px;
                          font-weight: 600;
                          text-align: left;
                          border-bottom: 1px solid #e2e8f0;
                        "
                      >
                        固件版本
                      </th>
                      <th
                        style="
                          width: 80px;
                          padding: 12px;
                          font-weight: 600;
                          text-align: left;
                          border-bottom: 1px solid #e2e8f0;
                        "
                      >
                        状态
                      </th>
                      <th
                        style="
                          min-width: 120px;
                          padding: 12px;
                          font-weight: 600;
                          text-align: left;
                          border-bottom: 1px solid #e2e8f0;
                        "
                      >
                        描述
                      </th>
                      <th
                        v-if="canEdit"
                        style="
                          width: 120px;
                          padding: 8px 12px;
                          font-weight: 600;
                          text-align: left;
                          border-bottom: 1px solid #e2e8f0;
                        "
                      >
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="(component, index) in groupData.components"
                      :key="component.id"
                      :style="{
                        'background-color':
                          index % 2 === 0 ? '#ffffff' : '#f8fafc',
                      }"
                    >
                      <td
                        style="padding: 12px; border-bottom: 1px solid #f1f5f9"
                      >
                        <span
                          style="
                            padding: 2px 6px;
                            font-size: 12px;
                            color: #1e40af;
                            background: #dbeafe;
                            border-radius: 4px;
                          "
                        >
                          {{ component.component_type || '-' }}
                        </span>
                      </td>
                      <td
                        style="
                          padding: 12px;
                          word-break: break-all;
                          border-bottom: 1px solid #f1f5f9;
                        "
                      >
                        {{ component.model || '-' }}
                      </td>
                      <td
                        style="
                          padding: 12px;
                          font-family: monospace;
                          word-break: break-all;
                          border-bottom: 1px solid #f1f5f9;
                        "
                      >
                        {{ component.sn || '-' }}
                      </td>
                      <td
                        style="
                          padding: 12px;
                          font-family: monospace;
                          word-break: break-all;
                          border-bottom: 1px solid #f1f5f9;
                        "
                      >
                        {{ component.pn || '-' }}
                      </td>
                      <td
                        style="
                          padding: 12px;
                          font-family: monospace;
                          border-bottom: 1px solid #f1f5f9;
                        "
                      >
                        {{ component.firmware || '-' }}
                      </td>
                      <td
                        style="padding: 12px; border-bottom: 1px solid #f1f5f9"
                      >
                        <Tag :color="getStatusColor(component.status)">
                          {{ getStatusText(component.status) }}
                        </Tag>
                      </td>
                      <td
                        style="
                          padding: 12px;
                          word-break: break-all;
                          border-bottom: 1px solid #f1f5f9;
                        "
                      >
                        {{ component.description || '-' }}
                      </td>
                      <td
                        v-if="canEdit"
                        style="
                          padding: 8px 12px;
                          border-bottom: 1px solid #f1f5f9;
                        "
                      >
                        <!-- 操作按钮 -->
                        <Button
                          v-if="!editingComponents.has(component.id)"
                          size="small"
                          type="link"
                          @click="startEditPN(component)"
                        >
                          编辑PN
                        </Button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </template>
          </div>
        </div>
      </template>
      <template v-else>
        <Empty description="暂无组件数据" />
      </template>
    </Spin>
  </Modal>
</template>
