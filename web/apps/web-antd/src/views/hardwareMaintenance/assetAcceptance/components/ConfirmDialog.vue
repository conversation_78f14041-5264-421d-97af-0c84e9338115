<script lang="ts" setup>
import { But<PERSON>, Modal } from 'ant-design-vue';

interface Props {
  visible: boolean;
  title: string;
  content: string;
  confirmText?: string;
  cancelText?: string;
  confirmType?: 'danger' | 'default' | 'primary';
  loading?: boolean;
  extraContent?: string;
  extraContentType?: 'default' | 'error' | 'info' | 'success' | 'warning';
}

defineProps<Props>();
const emits = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'confirm'): void;
  (e: 'cancel'): void;
}>();

const handleCancel = () => {
  emits('cancel');
  emits('update:visible', false);
};

const handleConfirm = () => {
  emits('confirm');
};
</script>

<template>
  <Modal
    :visible="visible"
    :title="title"
    @cancel="handleCancel"
    :mask-closable="false"
    :footer="null"
    :body-style="{ padding: '24px' }"
    :style="{ top: '20px' }"
    class="confirm-dialog-modal"
  >
    <div class="p-2">
      <p class="mb-5 text-base leading-relaxed text-gray-700">{{ content }}</p>
      <p
        v-if="extraContent"
        class="mb-5 rounded-md px-4 py-2 text-sm"
        :class="[
          {
            'border-l-4 border-amber-400 bg-amber-50 text-amber-600':
              extraContentType === 'warning',
            'border-l-4 border-blue-400 bg-blue-50 text-blue-600':
              extraContentType === 'info',
            'border-l-4 border-red-400 bg-red-50 text-red-600':
              extraContentType === 'error',
            'border-l-4 border-green-400 bg-green-50 text-green-600':
              extraContentType === 'success',
            'border-l-4 border-gray-300 bg-gray-50 text-gray-600':
              extraContentType === 'default' || !extraContentType,
          },
        ]"
      >
        {{ extraContent }}
      </p>

      <div class="mt-6 flex justify-end gap-3">
        <Button
          type="default"
          @click="handleCancel"
          class="cancel-btn min-w-[80px]"
        >
          {{ cancelText || '取消' }}
        </Button>
        <Button
          :type="
            confirmType === 'default'
              ? 'default'
              : confirmType === 'danger'
                ? 'primary'
                : 'primary'
          "
          :danger="confirmType === 'danger'"
          :loading="loading"
          @click="handleConfirm"
          class="confirm-btn min-w-[80px]"
        >
          {{ confirmText || '确定' }}
        </Button>
      </div>
    </div>
  </Modal>
</template>
