<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { CreateAcceptanceOrderRequest } from '#/api/core/hardWareMaint/acceptance';

import { nextTick, reactive, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { Button, Input, message, Modal } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getDevicesByMultiSNApi } from '#/api/core/cmdb/asset/device';
import { createAcceptanceOrderApi } from '#/api/core/hardWareMaint/acceptance';
import { downloadImportTemplate } from '#/api/core/import';

// 定义事件
const emit = defineEmits(['success']);

const loading = ref(false);
const fetchingDevices = ref(false);
const modalVisible = ref(false);
const deviceSNInput = ref('');
const importLoading = ref(false);
const csvFileInputRef = ref<HTMLInputElement>();

// 表单数据
const formState = reactive<CreateAcceptanceOrderRequest>({
  title: '',
  description: '',
  items: [],
});

// 抽屉组件
const [Drawer, drawerApi] = useVbenDrawer({
  onClosed: () => {
    resetForm();
  },
});

// 表单配置
const formOptions: VbenFormProps = {
  showDefaultActions: false,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入工单标题' },
      fieldName: 'title',
      label: '工单标题',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入描述信息',
        type: 'textarea',
        rows: 4,
      },
      fieldName: 'description',
      label: '描述',
    },
  ],
};

const [Form, formApi] = useVbenForm(formOptions);

// 设备表格配置
const gridOptions: VxeGridProps = {
  columns: [
    { field: 'device_sn', title: '设备SN', width: 150 },
    { field: 'vendor', title: '厂商', width: 120 },
    { field: 'model', title: '型号', width: 120 },
    { field: 'template_name', title: '模板名称', minWidth: 150 },
    {
      title: '操作',
      slots: { default: 'operate' },
      width: 120,
      fixed: 'right',
    },
  ],
  data: [],
  showOverflow: true,
  height: 600,
  border: true,
  stripe: true,
  rowConfig: { isHover: true },
  pagerConfig: {
    enabled: true,
    pageSize: 10,
    pageSizes: [5, 10, 20, 50],
    background: true,
    perfect: false, // 禁用完美分页模式，使用简单分页
  },
  keepSource: true,
  // 使用proxyConfig来处理本地数据分页
  proxyConfig: {
    enabled: true,
    autoLoad: false, // 不自动加载
    ajax: {
      query: ({ page }) => {
        const startIndex = (page.currentPage - 1) * page.pageSize;
        const endIndex = startIndex + page.pageSize;
        const items = formState.items.slice(startIndex, endIndex);

        return Promise.resolve({
          items,
          total: formState.items.length,
        });
      },
    },
    response: {
      result: 'items',
      total: 'total',
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

// 初始化表格
async function initGrid() {
  await nextTick();
  if (gridApi && gridApi.grid) {
    await gridApi.grid.commitProxy('query');
  }
}

// 安全刷新表格数据的辅助函数
async function refreshGrid() {
  await nextTick();
  try {
    if (gridApi && gridApi.grid) {
      // 使用代理模式重新查询数据
      await gridApi.grid.commitProxy('query');
    }
  } catch (error) {
    console.warn('刷新表格失败:', error);
  }
}

// 打开抽屉
async function open() {
  // 先重置表单数据，确保每次打开都是清空状态
  await resetForm();
  drawerApi.open();
  // 初始化表格
  await initGrid();
}

// 重置表单
async function resetForm() {
  formApi.setValues({
    title: '',
    description: '',
  });
  formState.title = '';
  formState.description = '';
  formState.items = [];
  deviceSNInput.value = '';

  // 确保表格数据被清空
  await refreshGrid();
}

// 打开添加设备的Modal
function openAddDeviceModal() {
  deviceSNInput.value = '';
  modalVisible.value = true;
}

// 触发文件选择
function triggerFileSelect() {
  csvFileInputRef.value?.click();
}

// 下载导入模板
async function handleDownloadTemplate() {
  try {
    await downloadImportTemplate('acceptance_device_list');
    message.success('模板下载成功');
  } catch (error) {
    console.error('下载模板失败:', error);
    message.error('下载模板失败');
  }
}

// 处理CSV文件导入
async function handleCSVImport(event: Event) {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];

  if (!file) {
    return;
  }

  // 检查文件类型
  if (!file.name.toLowerCase().endsWith('.csv')) {
    message.error('请选择CSV文件');
    target.value = ''; // 清空文件选择
    return;
  }

  try {
    importLoading.value = true;

    // 读取文件内容
    const fileContent = await readFileAsText(file);

    // 解析CSV内容
    const deviceSNs = parseCSVContent(fileContent);

    if (deviceSNs.length === 0) {
      message.warning('CSV文件中未找到有效的设备SN');
      return;
    }

    // 显示解析到的SN数量供用户确认
    message.info(`解析到 ${deviceSNs.length} 个设备SN，正在获取设备信息...`);

    // 调用现有的设备获取逻辑
    await fetchDevicesBySNs(deviceSNs.join(','));
  } catch (error) {
    console.error('导入CSV文件失败:', error);
    message.error('导入CSV文件失败，请检查文件格式');
  } finally {
    importLoading.value = false;
    target.value = ''; // 清空文件选择
  }
}

// 读取文件内容
function readFileAsText(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.addEventListener('load', (e) => {
      resolve(e.target?.result as string);
    });
    reader.addEventListener('error', () => {
      reject(new Error('读取文件失败'));
    });
    reader.readAsText(file, 'utf8');
  });
}

// 解析CSV内容，提取设备SN
function parseCSVContent(content: string): string[] {
  const lines = content.split(/\r?\n/).filter((line) => line.trim());
  const deviceSNs: string[] = [];

  lines.forEach((line, index) => {
    // 跳过可能的表头（如果第一行包含中文或"SN"等字样）
    if (
      index === 0 &&
      (line.includes('设备') || line.includes('SN') || line.includes('序列号'))
    ) {
      return;
    }

    // 处理CSV格式，移除引号并取第一列
    const sn = line.split(',')[0]?.replaceAll(/['"]/g, '').trim();

    if (sn && !deviceSNs.includes(sn)) {
      deviceSNs.push(sn);
    }
  });

  return deviceSNs;
}

// 根据SN列表获取设备信息（复用现有逻辑）
async function fetchDevicesBySNs(sns: string) {
  if (!sns.trim()) {
    message.error('设备SN不能为空');
    return;
  }

  try {
    fetchingDevices.value = true;

    const devices = await getDevicesByMultiSNApi(sns);

    if (!devices || devices.length === 0) {
      message.warning('未找到任何设备');
      return;
    }

    // 检查是否有重复SN
    const existingSNs = new Set(formState.items.map((item) => item.device_sn));
    let duplicateFound = false;
    let addedCount = 0;

    // 将设备添加到列表
    devices.forEach((device) => {
      // 检查设备对象是否有必需的字段
      if (!device.id || !device.sn) {
        console.warn('设备数据不完整:', device);
        message.warning(`设备数据不完整，跳过设备: ${device.sn || '未知SN'}`);
        return;
      }

      if (existingSNs.has(device.sn)) {
        message.warning(`设备SN "${device.sn}" 已存在`);
        duplicateFound = true;
        return;
      }

      // 获取模板名称，添加更安全的访问
      let templateName = '';
      if (device.template && device.template.templateName) {
        templateName = device.template.templateName;
      }

      const newItem = {
        device_id: device.id,
        device_sn: device.sn,
        vendor: device.brand || '',
        model: device.model || '',
        template_name: templateName,
      };

      formState.items.push(newItem);
      existingSNs.add(device.sn);
      addedCount++;
    });

    // 刷新表格显示最新数据
    await refreshGrid();

    if (addedCount > 0) {
      message.success(`成功添加 ${addedCount} 个设备`);
      modalVisible.value = false;
    } else if (!duplicateFound) {
      message.warning('未能添加任何设备，请检查SN是否正确');
    }
  } catch (error) {
    console.error('获取设备信息失败:', error);

    // 提供更详细的错误信息
    let errorMessage = '获取设备信息失败';
    if (error instanceof Error) {
      errorMessage = error.message;
    }

    message.error(`获取设备信息失败: ${errorMessage}`);
  } finally {
    fetchingDevices.value = false;
  }
}

// 从后端获取设备信息并添加
async function fetchAndAddDevices() {
  const sns = deviceSNInput.value.trim();
  if (!sns) {
    message.error('请输入设备SN');
    return;
  }

  await fetchDevicesBySNs(sns);

  // 如果成功添加设备，关闭模态框并清空输入
  if (formState.items.length > 0) {
    modalVisible.value = false;
    deviceSNInput.value = '';
  }
}

// 根据设备SN移除设备
function removeDeviceBySN(deviceSN: string) {
  const index = formState.items.findIndex(
    (item) => item.device_sn === deviceSN,
  );
  if (index !== -1) {
    formState.items.splice(index, 1);
    // 刷新表格显示
    refreshGrid();
    message.success('设备已移除');
  }
}

// 提交表单
async function handleSubmit() {
  try {
    const values = await formApi.getValues();
    formState.title = values.title;
    formState.description = values.description || '';

    // 验证设备列表不能为空
    if (formState.items.length === 0) {
      message.error('请至少添加一个设备');
      return;
    }

    loading.value = true;

    // 提交创建请求
    await createAcceptanceOrderApi(formState);
    message.success('创建验收工单成功');

    // 关闭抽屉并触发成功事件
    drawerApi.close();
    emit('success');
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    loading.value = false;
  }
}

// 向父组件暴露方法
defineExpose({
  open,
});
</script>

<template>
  <Drawer title="创建验收工单" :loading="loading" class="w-1/2">
    <template #default>
      <Form />

      <h3 class="mb-2 font-medium">设备列表</h3>

      <div class="mb-4 flex gap-2">
        <Button type="primary" @click="openAddDeviceModal">添加设备</Button>
        <Button
          type="primary"
          :loading="importLoading"
          @click="triggerFileSelect"
        >
          导入CSV
        </Button>
        <Button @click="handleDownloadTemplate"> 下载模板 </Button>
        <input
          ref="csvFileInputRef"
          type="file"
          accept=".csv"
          style="display: none"
          @change="handleCSVImport"
        />
      </div>

      <Grid>
        <template #operate="{ row }">
          <Button
            type="link"
            danger
            @click="() => removeDeviceBySN(row.device_sn)"
          >
            删除
          </Button>
        </template>
        <template #empty>
          <div class="flex items-center justify-center p-4 text-gray-500">
            暂无数据
          </div>
        </template>
      </Grid>

      <!-- 添加设备的Modal -->
      <Modal
        title="添加设备"
        v-model:visible="modalVisible"
        :confirm-loading="fetchingDevices"
        @ok="fetchAndAddDevices"
        @cancel="modalVisible = false"
      >
        <div class="mb-4">
          <div class="mb-3">
            <div class="mb-1">设备SN</div>
            <Input.TextArea
              v-model:value="deviceSNInput"
              placeholder="请输入设备SN，多个SN请用逗号分隔"
              :rows="4"
            />
            <div class="mt-2 text-xs text-gray-500">
              多个SN请用逗号分隔，系统将自动获取设备信息
            </div>
          </div>
        </div>
      </Modal>
    </template>

    <template #footer>
      <div class="flex justify-end">
        <Button class="mr-2" @click="drawerApi.close">取消</Button>
        <Button :loading="loading" type="primary" @click="handleSubmit">
          提交
        </Button>
      </div>
    </template>
  </Drawer>
</template>
