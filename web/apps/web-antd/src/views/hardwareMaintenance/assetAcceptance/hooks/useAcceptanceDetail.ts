import type { VxeGridProps } from '#/adapter/vxe-table';
import type {
  AcceptanceItem,
  AcceptanceOrderDetail,
  NoExistingPNInfo,
} from '#/api/core/hardWareMaint/acceptance';

import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

import { PASS_OPTIONS } from '../constants';
import {
  auditOrder,
  cancelOrder,
  completeOrder,
  fillComponentPN,
  loadAcceptanceItemComponents,
  loadAcceptanceItems,
  loadAcceptanceOrderHistory,
  loadOrderDetail,
  uploadFiles,
} from '../services';
import {
  getCurrentStep,
  isResultEditable as getIsResultEditable,
  validateAcceptanceResults,
} from '../utils';

export default function useAcceptanceDetail(id?: number | string) {
  const router = useRouter();
  const loading = ref(false);
  const itemsLoading = ref(false);
  const actionLoading = ref(false);
  const uploadLoading = ref(false);
  const fileInputRef = ref<HTMLInputElement | null>(null);
  const orderDetail = ref<AcceptanceOrderDetail | null>(null);

  // 操作历史相关状态
  const historyList = ref<any[]>([]);
  const historyLoading = ref(false);

  // 验收item的组件列表
  const componentList = ref<any[]>([]);
  const componentLoading = ref(false);

  // 本地数据缓存
  const localItemsData = ref<AcceptanceItem[]>([]);

  // 设备SN到itemId的映射
  const deviceSNToItemIdMap = ref<Map<string, number>>(new Map());

  // 组件SN到设备SN的映射
  const componentSNToDeviceSNMap = ref<Map<string, string>>(new Map());

  // 当前点击的设备
  const currentDevice = ref<any>(null);
  const deviceDetailVisible = ref(false);

  // 文件上传相关状态
  const selectedFiles = ref<File[]>([]);
  const fileUploadVisible = ref(false);

  // 筛选状态
  const showFailedOnly = ref(false);

  // 导出不通过设备SN相关状态
  const exportFailedSNVisible = ref(false);
  const failedDeviceSNs = ref('');

  // 完结工单错误信息弹窗状态
  const completeErrorVisible = ref(false);
  const completeErrorData = ref<{
    dbExistingComponentSNs: string[];
    invalidDeviceSNs: string[];
    missingPNModels: string[];
    needCMDBPNs: string[];
  }>({
    invalidDeviceSNs: [],
    missingPNModels: [],
    needCMDBPNs: [],
    dbExistingComponentSNs: [],
  });

  const batchFailModalVisible = ref(false);
  const batchFailReason = ref('');

  // 计算属性
  const currentStep = computed(() => {
    if (!orderDetail.value) return 0;
    return getCurrentStep(orderDetail.value.Status);
  });

  const isResultEditable = computed(() => {
    return orderDetail.value
      ? getIsResultEditable(orderDetail.value.Status)
      : false;
  });

  // 判断是否为已完成的工单
  const isOrderCompleted = computed(() => {
    return orderDetail.value?.Status === 'COMPLETED';
  });

  // 设备列表的Grid配置
  const gridOptions: VxeGridProps<AcceptanceItem> = {
    columns: [
      { field: 'device_sn', title: '设备SN', width: 200 },
      { field: 'vendor', title: '厂商' },
      { field: 'model', title: '型号' },
      { field: 'template_name', title: '模板名称' },
      {
        field: 'is_pass',
        title: '验收结果',
        width: 120,
        slots: { default: 'is_pass', edit: 'is_pass_edit' },
        editRender: {
          name: 'select',
          options: PASS_OPTIONS,
        },
      },
      {
        field: 'reason',
        title: '原因',
        editRender: {
          name: 'input',
          attrs: { placeholder: '请输入不通过原因' },
        },
      },
      {
        title: '检测组件结果',
        slots: { default: 'device_detail' },
      },
    ],
    data: [],
    height: 600,
    border: true,
    stripe: true,
    showOverflow: true,
    keepSource: true,
    rowConfig: { isHover: true },
    pagerConfig: {
      enabled: true,
      pageSize: 10,
      pageSizes: [5, 10, 20, 50],
      background: true,
      perfect: false,
    },
    editConfig: {
      trigger: 'click',
      mode: 'cell',
      showStatus: true,
    },
    proxyConfig: {
      enabled: true,
      autoLoad: false,
      ajax: {
        query: async ({ page }) => {
          if (!orderDetail.value) {
            return { items: [], total: 0 };
          }

          itemsLoading.value = true;
          try {
            // 如果本地缓存为空，从服务器加载数据
            if (localItemsData.value.length === 0) {
              const fullData = await loadItemsData(orderDetail.value.ID);
              localItemsData.value = fullData.items || [];
              // 更新设备SN到itemId的映射
              updateDeviceSNToItemIdMap(localItemsData.value);
            }

            // 根据筛选状态过滤数据
            let filteredItems = localItemsData.value;
            if (showFailedOnly.value) {
              filteredItems = localItemsData.value.filter(
                (item) => item.is_pass === false,
              );
            }

            // 客户端分页
            const startIndex = (page.currentPage - 1) * page.pageSize;
            const endIndex = startIndex + page.pageSize;
            const pagedItems = filteredItems.slice(startIndex, endIndex);

            return {
              items: pagedItems,
              total: filteredItems.length,
            };
          } finally {
            itemsLoading.value = false;
          }
        },
      },
      response: {
        result: 'items',
        total: 'total',
      },
    },
  };

  const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

  // 加载数据
  async function init(orderId?: number) {
    const idToUse = orderId ?? id;
    if (!idToUse) return;

    // 清空本地缓存
    localItemsData.value = [];
    deviceSNToItemIdMap.value.clear();

    // 加载详情数据
    await loadData(Number(idToUse));
    // 确保表格数据正确加载
    if (gridApi && gridApi.grid) {
      await gridApi.grid.commitProxy('query');
    }
  }

  // 加载详情数据
  async function loadData(orderId: number) {
    loading.value = true;
    try {
      // 加载工单详情数据
      const detailData = await loadOrderDetail(orderId);
      orderDetail.value = detailData;
    } finally {
      loading.value = false;
    }
  }

  // 加载验收项列表
  async function loadItemsData(orderId: number) {
    try {
      const res = await loadAcceptanceItems(orderId);
      return res;
    } catch (error) {
      console.error('加载验收项失败:', error);
      return { items: [], total: 0 };
    }
  }

  // 返回列表
  function goBack() {
    router.push('/hardware-maintenance/asset-acceptance');
  }

  // 审核工单
  async function handleAuditOrder(isApproved: boolean) {
    if (!orderDetail.value) return;

    actionLoading.value = true;
    try {
      const success = await auditOrder(orderDetail.value.ID, isApproved);
      if (success) {
        refreshDetail();
      }
    } finally {
      actionLoading.value = false;
    }
  }

  // 取消工单
  async function handleCancelOrder() {
    if (!orderDetail.value) return;

    actionLoading.value = true;
    try {
      const success = await cancelOrder(orderDetail.value.ID);
      if (success) {
        refreshDetail();
      }
    } finally {
      actionLoading.value = false;
    }
  }

  // 上传检测结果
  function handleUploadClick() {
    if (!orderDetail.value) return;

    // 直接触发文件选择
    if (fileInputRef.value) {
      fileInputRef.value.click();
    }
  }

  // 处理文件上传
  async function handleFileChange(e: Event) {
    const target = e.target as HTMLInputElement;
    if (!target.files || target.files.length === 0 || !orderDetail.value)
      return;

    const files = [...target.files];

    // 验证文件类型，只接受JSON文件
    const nonJsonFiles = files.filter(
      (file) =>
        !(
          file.type === 'application/json' ||
          file.name.toLowerCase().endsWith('.json')
        ),
    );

    if (nonJsonFiles.length > 0) {
      message.error('只能上传JSON格式文件，请重新选择');
      // 清空文件选择
      if (fileInputRef.value) {
        fileInputRef.value.value = '';
      }
      return;
    }

    // 检查文件大小
    const oversizedFiles = files.filter(
      (file) => file.size / 1024 / 1024 > 100,
    );
    if (oversizedFiles.length > 0) {
      message.error('文件大小不能超过100MB!');
      return;
    }

    // 存储选中的文件并显示确认弹窗
    selectedFiles.value = files;
    fileUploadVisible.value = true;
  }

  // 确认上传文件
  async function confirmUploadFiles() {
    if (selectedFiles.value.length === 0 || !orderDetail.value) {
      message.error('没有选择文件');
      return;
    }

    uploadLoading.value = true;
    try {
      const success = await uploadFiles(
        orderDetail.value.ID,
        selectedFiles.value,
      );
      if (success) {
        // 清空文件选择
        selectedFiles.value = [];
        fileUploadVisible.value = false;
        if (fileInputRef.value) {
          fileInputRef.value.value = '';
        }
        refreshDetail();
      }
    } finally {
      uploadLoading.value = false;
    }
  }

  // 取消上传
  function cancelUpload() {
    selectedFiles.value = [];
    fileUploadVisible.value = false;
    if (fileInputRef.value) {
      fileInputRef.value.value = '';
    }
  }

  // 移除选中的文件
  function removeSelectedFile(index: number) {
    selectedFiles.value.splice(index, 1);
    if (selectedFiles.value.length === 0) {
      fileUploadVisible.value = false;
    }
  }

  // 格式化文件大小
  function formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
  }

  // 添加更多文件
  function addMoreFiles() {
    if (fileInputRef.value) {
      fileInputRef.value.click();
    }
  }

  // 处理添加更多文件
  function handleAddMoreFiles(e: Event) {
    const target = e.target as HTMLInputElement;
    if (!target.files || target.files.length === 0) return;

    const newFiles = [...target.files];

    // 验证文件类型，只接受JSON文件
    const nonJsonFiles = newFiles.filter(
      (file) =>
        !(
          file.type === 'application/json' ||
          file.name.toLowerCase().endsWith('.json')
        ),
    );

    if (nonJsonFiles.length > 0) {
      message.error('只能上传JSON格式文件，请重新选择');
      // 清空文件选择
      if (fileInputRef.value) {
        fileInputRef.value.value = '';
      }
      return;
    }

    // 检查文件大小
    const oversizedFiles = newFiles.filter(
      (file) => file.size / 1024 / 1024 > 100,
    );
    if (oversizedFiles.length > 0) {
      message.error('文件大小不能超过100MB!');
      return;
    }

    // 检查是否有重复文件
    const existingFileNames = new Set(selectedFiles.value.map((f) => f.name));
    const duplicateFiles = newFiles.filter((file) =>
      existingFileNames.has(file.name),
    );

    if (duplicateFiles.length > 0) {
      message.warning(`已忽略 ${duplicateFiles.length} 个重复文件`);
    }

    // 过滤掉重复文件，只添加新文件
    const uniqueNewFiles = newFiles.filter(
      (file) => !existingFileNames.has(file.name),
    );

    if (uniqueNewFiles.length > 0) {
      selectedFiles.value.push(...uniqueNewFiles);
      message.success(`成功添加 ${uniqueNewFiles.length} 个文件`);
    }

    // 清空文件输入
    if (fileInputRef.value) {
      fileInputRef.value.value = '';
    }
  }

  // 刷新本地数据缓存
  async function refreshItemsCache() {
    if (!orderDetail.value) return;

    try {
      const fullData = await loadItemsData(orderDetail.value.ID);
      localItemsData.value = fullData.items || [];

      // 更新设备SN到itemId的映射
      updateDeviceSNToItemIdMap(localItemsData.value);
    } catch (error) {
      console.error('刷新缓存失败:', error);
      localItemsData.value = [];
      deviceSNToItemIdMap.value.clear();
    }
  }

  // 更新设备SN到itemId的映射
  function updateDeviceSNToItemIdMap(items: AcceptanceItem[]) {
    deviceSNToItemIdMap.value.clear();
    items.forEach((item) => {
      if (item.device_sn && item.id) {
        deviceSNToItemIdMap.value.set(item.device_sn, item.id);
      }
    });
  }

  // 构建组件SN到设备SN的映射关系
  async function buildComponentSNToDeviceSNMap() {
    if (!orderDetail.value || localItemsData.value.length === 0) {
      return;
    }

    componentSNToDeviceSNMap.value.clear();

    try {
      // 遍历所有设备，获取其组件信息
      for (const item of localItemsData.value) {
        if (item.id) {
          try {
            const components = await loadAcceptanceItemComponents(item.id);
            if (Array.isArray(components)) {
              // 为每个组件建立SN到设备SN的映射
              components.forEach((component: any) => {
                if (component.sn && item.device_sn) {
                  componentSNToDeviceSNMap.value.set(
                    component.sn,
                    item.device_sn,
                  );
                }
              });
            }
          } catch (error) {
            console.warn(`获取设备 ${item.device_sn} 的组件信息失败:`, error);
          }
        }
      }
      // 组件SN到设备SN映射已构建
    } catch (error) {
      console.error('构建组件SN映射失败:', error);
    }
  }

  // 根据组件SN查找对应的设备SN
  function findDeviceSNsByComponentSNs(componentSNs: string[]): string[] {
    const deviceSNs: string[] = [];
    const notFoundComponentSNs: string[] = [];

    componentSNs.forEach((componentSN) => {
      const deviceSN = componentSNToDeviceSNMap.value.get(componentSN);
      if (deviceSN && !deviceSNs.includes(deviceSN)) {
        deviceSNs.push(deviceSN);
      } else if (!deviceSN) {
        notFoundComponentSNs.push(componentSN);
      }
    });

    if (notFoundComponentSNs.length > 0) {
      console.warn('以下组件SN未找到对应的设备:', notFoundComponentSNs);
    }

    return deviceSNs;
  }

  // 设置所有设备为通过
  async function setAllPass() {
    if (!orderDetail.value || !isResultEditable.value) return;

    try {
      // 确保有数据
      if (localItemsData.value.length === 0) {
        await refreshItemsCache();
      }

      if (localItemsData.value.length > 0) {
        // 修改所有项目为通过
        localItemsData.value.forEach((item: AcceptanceItem) => {
          item.is_pass = true;
        });

        // 刷新表格显示
        await gridApi.grid.commitProxy('query');
        message.success('已设置所有设备为通过');
      } else {
        message.warning('当前没有设备数据');
      }
    } catch (error) {
      console.error('设置全部通过失败:', error);
      message.error('操作失败');
    }
  }

  // 设置所有设备为不通过
  function openBatchFailModal() {
    batchFailReason.value = '';
    batchFailModalVisible.value = true;
  }

  async function confirmBatchFail(reason: string) {
    if (!reason || !reason.trim()) {
      message.error('请填写不通过原因');
      return;
    }
    if (localItemsData.value.length > 0) {
      localItemsData.value.forEach((item: AcceptanceItem) => {
        item.is_pass = false;
        item.reason = reason;
      });
      await gridApi.grid.commitProxy('query');
      message.success('已设置所有设备为不通过');
    }
    batchFailModalVisible.value = false;
  }

  // 自动填入CPU和GPU的PN
  async function handleFillComponentPN() {
    if (!orderDetail.value || !isResultEditable.value) return;

    actionLoading.value = true;
    try {
      // 确保有数据
      if (localItemsData.value.length === 0) {
        await refreshItemsCache();
      }

      if (localItemsData.value.length > 0) {
        // 获取所有item的id
        const itemIds = localItemsData.value
          .map((item: AcceptanceItem) => item.id)
          .filter((id): id is number => id !== undefined);

        if (itemIds.length === 0) {
          message.warning('没有找到有效的验收项ID');
          return;
        }

        // 调用自动填入PN的服务
        await fillComponentPN(itemIds, true);
      } else {
        message.warning('当前没有设备数据');
      }
    } catch (error) {
      console.error('自动填入PN失败:', error);
      message.error('自动填入PN失败');
    } finally {
      actionLoading.value = false;
    }
  }

  // 设置设备验收结果为不通过
  function setDeviceFailByDeviceSN(
    deviceSNs: string[],
    reason: string = '未检测到该设备组件信息',
  ) {
    if (!gridApi?.grid) {
      console.error('gridApi.grid 不存在');
      return;
    }

    try {
      // 首先尝试从表格获取数据
      let tableData = gridApi.grid.getData() || [];

      // 如果表格数据为空，使用本地缓存数据
      if (tableData.length === 0 && localItemsData.value.length > 0) {
        tableData = [...localItemsData.value];
      }

      let updatedCount = 0;
      const updatedItemIds: number[] = [];
      const updatedDevices: string[] = [];

      // 同时更新表格数据和本地缓存
      tableData.forEach((device: any) => {
        if (deviceSNs.includes(device.device_sn)) {
          device.is_pass = false;
          device.reason = reason;
          updatedCount++;
          updatedDevices.push(device.device_sn);

          // 记录更新的itemId
          const itemId = deviceSNToItemIdMap.value.get(device.device_sn);
          if (itemId) {
            updatedItemIds.push(itemId);
          }
        }
      });

      // 同时更新本地缓存中的对应设备
      localItemsData.value.forEach((item: AcceptanceItem) => {
        if (deviceSNs.includes(item.device_sn)) {
          item.is_pass = false;
          item.reason = reason;
        }
      });

      if (updatedCount > 0) {
        // 强制重新加载表格数据
        gridApi.grid.loadData(tableData);

        // 手动触发表格刷新
        setTimeout(async () => {
          try {
            await gridApi.grid.commitProxy('query');

            // 验证更新是否生效
            setTimeout(() => {
              const currentData = gridApi.grid.getData() || [];
              const stillNotUpdated = deviceSNs.filter((sn) => {
                const device = currentData.find((d) => d.device_sn === sn);
                return device && device.is_pass !== false;
              });

              if (stillNotUpdated.length > 0) {
                console.warn('以下设备状态更新可能未生效:', stillNotUpdated);
                message.warning(
                  `请手动检查以下设备的验收状态：${stillNotUpdated.join(', ')}`,
                );
              }
            }, 500);
          } catch (error) {
            console.error('表格刷新失败:', error);
          }
        }, 100);

        message.warning(
          `已自动将 ${updatedCount} 个设备的验收结果设置为不通过（原因：${reason}）`,
        );
      } else {
        console.warn('没有找到需要更新的设备');
        console.warn('目标设备SNs:', deviceSNs);
        console.warn(
          '表格中的设备SNs:',
          tableData.map((d) => d.device_sn),
        );
      }
    } catch (error) {
      console.error('设置设备验收结果失败:', error);
      message.error('设置设备验收结果失败');
    }
  }

  // 完成工单
  async function handleCompleteOrder() {
    if (!orderDetail.value) return;

    actionLoading.value = true;
    try {
      // 获取当前缓存数据
      const items = localItemsData.value.length > 0 ? localItemsData.value : [];

      // 验证所有设备都已设置验收结果
      const validation = validateAcceptanceResults(items);
      if (!validation.valid) {
        message.error(validation.message);
        return;
      }

      // 提交验收结果
      const result = await completeOrder(orderDetail.value.ID, items);
      if (result.success) {
        const responseData = result.data;

        // 处理错误信息
        const invalidDeviceSNs = responseData?.invalid_device_sns || [];
        const noExistingPNInfos = responseData?.no_existing_pn_info || [];
        const dbExistingComponentSNs =
          responseData?.db_existing_component_sn || [];

        // 如果有错误信息，说明完结失败
        if (
          invalidDeviceSNs.length > 0 ||
          noExistingPNInfos.length > 0 ||
          dbExistingComponentSNs.length > 0
        ) {
          // 确保设备映射和数据已经准备好
          if (localItemsData.value.length === 0) {
            await refreshItemsCache();
          }

          // 处理无效设备SN
          if (invalidDeviceSNs.length > 0) {
            // 自动设置这些设备为验收不通过
            setDeviceFailByDeviceSN(invalidDeviceSNs, '未检测到该设备组件信息');
          }

          // 处理CMDB中已存在的组件SN
          if (dbExistingComponentSNs.length > 0) {
            // 构建组件SN到设备SN的映射
            await buildComponentSNToDeviceSNMap();

            // 找到这些组件SN对应的设备SN
            const affectedDeviceSNs = findDeviceSNsByComponentSNs(
              dbExistingComponentSNs,
            );

            if (affectedDeviceSNs.length > 0) {
              // 生成包含具体组件SN信息的原因
              const componentSNList = dbExistingComponentSNs.join(', ');
              const reason = `组件SN ${componentSNList} 已存在CMDB中，需要联系管理员解决`;

              // 自动设置这些设备为验收不通过
              setDeviceFailByDeviceSN(affectedDeviceSNs, reason);
            }
          }

          // 处理缺失PN信息
          const missingPNModels: string[] = [];
          const needCMDBPNs: string[] = [];

          noExistingPNInfos.forEach((info: NoExistingPNInfo) => {
            if (!info.pn || info.pn.trim() === '') {
              missingPNModels.push(`${info.model}(${info.component_type})`);
            } else {
              needCMDBPNs.push(
                `${info.pn}(${info.component_type}-${info.model})`,
              );
            }
          });

          // 设置错误信息并显示弹窗
          completeErrorData.value = {
            invalidDeviceSNs,
            missingPNModels,
            needCMDBPNs,
            dbExistingComponentSNs,
          };
          completeErrorVisible.value = true;

          message.error('完结工单失败，请查看详细错误信息');
        } else {
          // 没有问题，工单完成成功
          message.success('完成工单成功');
          refreshDetail();
        }
      }
    } finally {
      actionLoading.value = false;
    }
  }

  // 获取操作历史
  async function fetchHistory(orderId?: number) {
    const idToUse = orderId ?? orderDetail.value?.ID;
    if (!idToUse) {
      message.error('无效的工单ID');
      historyList.value = [];
      return;
    }

    historyLoading.value = true;
    try {
      const res = await loadAcceptanceOrderHistory(idToUse);
      // 兼容返回为对象或数组
      if (Array.isArray(res)) {
        historyList.value = res;
      } else if (res && res.code === 200) {
        historyList.value = res.data || [];
      } else {
        message.error(res?.message || '获取操作历史失败');
        historyList.value = [];
      }
    } catch (error: any) {
      message.error(`获取操作历史失败: ${error.message || '未知错误'}`);
      historyList.value = [];
    } finally {
      historyLoading.value = false;
    }
  }

  async function fetchItemComponents(itemId: number) {
    componentLoading.value = true;
    try {
      const res = await loadAcceptanceItemComponents(itemId);
      componentList.value = Array.isArray(res) ? res : [];
    } catch {
      componentList.value = [];
      message.error('获取组件信息失败');
    } finally {
      componentLoading.value = false;
    }
  }

  // 刷新详情
  async function refreshDetail() {
    if (!orderDetail.value) return;

    // 清空本地缓存，强制重新加载
    localItemsData.value = [];
    deviceSNToItemIdMap.value.clear();

    await loadData(orderDetail.value.ID);
    // 使用代理模式刷新表格数据
    await gridApi.grid.commitProxy('query');
    // 同时刷新历史数据
    await fetchHistory(orderDetail.value.ID);
  }

  const showDeviceDetail = (device: any) => {
    currentDevice.value = device;
    deviceDetailVisible.value = true;
    // 不在这里调用API，让Modal组件自己处理数据加载
    componentList.value = [];
  };

  // 切换筛选状态
  const toggleFailedFilter = async () => {
    showFailedOnly.value = !showFailedOnly.value;
    // 刷新表格数据以应用筛选
    if (gridApi && gridApi.grid) {
      await gridApi.grid.commitProxy('query');
    }

    if (showFailedOnly.value) {
      message.info('已筛选显示验收不通过的设备');
    } else {
      message.info('已显示所有设备');
    }
  };

  // 导出不通过设备SN
  const exportFailedDeviceSNs = () => {
    // 获取所有验收不通过的设备
    const failedDevices = localItemsData.value.filter(
      (item) => item.is_pass === false,
    );

    if (failedDevices.length === 0) {
      message.info('当前没有验收不通过的设备');
      return;
    }

    // 提取SN并用逗号拼接
    const sns = failedDevices.map((device) => device.device_sn).join(',');
    failedDeviceSNs.value = sns;
    exportFailedSNVisible.value = true;
  };

  // 复制SN到剪贴板
  const copyFailedSNsToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(failedDeviceSNs.value);
      message.success('SN列表已复制到剪贴板');
    } catch (error) {
      console.error('复制失败:', error);
      // 降级处理：创建一个临时的textarea元素
      const textArea = document.createElement('textarea');
      textArea.value = failedDeviceSNs.value;
      document.body.append(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        message.success('SN列表已复制到剪贴板');
      } catch {
        message.error('复制失败，请手动复制');
      }
      textArea.remove();
    }
  };

  return {
    loading,
    actionLoading,
    uploadLoading,
    fileInputRef,
    orderDetail,
    currentStep,
    isResultEditable,
    Grid,
    gridApi,
    historyList,
    historyLoading,
    init,
    goBack,
    handleAuditOrder,
    handleCancelOrder,
    handleUploadClick,
    handleFileChange,
    confirmUploadFiles,
    cancelUpload,
    removeSelectedFile,
    formatFileSize,
    setAllPass,
    openBatchFailModal,
    handleFillComponentPN,
    handleCompleteOrder,
    refreshDetail,
    fetchHistory,
    fetchItemComponents,
    deviceDetailVisible,
    showDeviceDetail,
    currentDevice,
    componentList,
    componentLoading,
    selectedFiles,
    fileUploadVisible,
    addMoreFiles,
    handleAddMoreFiles,
    showFailedOnly,
    isOrderCompleted,
    toggleFailedFilter,
    exportFailedSNVisible,
    failedDeviceSNs,
    exportFailedDeviceSNs,
    copyFailedSNsToClipboard,
    completeErrorVisible,
    completeErrorData,
    batchFailModalVisible,
    batchFailReason,
    confirmBatchFail,
  };
}
