import type {
  AcceptanceItem,
  AcceptanceOrderDetail,
} from '#/api/core/hardWareMaint/acceptance';

import { message } from 'ant-design-vue';

import {
  auditAcceptanceOrderApi,
  batchUpdateComponentPNApi,
  cancelAcceptanceOrderApi,
  completeAcceptanceOrderApi,
  fillComponentPNApi,
  getAcceptanceDetailApi,
  getAcceptanceItemComponentsApi,
  getAcceptanceItemsApi,
  getAcceptanceOrderHistoryApi,
  updateComponentPNApi,
  updateInspectingComponentInfoApi,
  uploadCheckResultApi,
} from '#/api/core/hardWareMaint/acceptance';

/**
 * 加载工单详情
 * @param id 工单ID
 * @returns 工单详情数据
 */
export async function loadOrderDetail(
  id: number,
): Promise<AcceptanceOrderDetail | null> {
  try {
    return await getAcceptanceDetailApi(id);
  } catch (error) {
    console.error('加载详情失败:', error);
    message.error('加载详情失败');
    return null;
  }
}

/**
 * 加载验收项列表
 * @param orderId 工单ID
 * @returns 验收项列表数据
 */
export async function loadAcceptanceItems(orderId: number) {
  try {
    const res = await getAcceptanceItemsApi(orderId);
    return {
      items: res.list || [],
      total: res.total || 0,
    };
  } catch (error) {
    console.error('加载验收项失败:', error);
    return { items: [], total: 0 };
  }
}

/**
 * 审核工单
 * @param orderId 工单ID
 * @param isApproved 是否批准
 * @returns 审核结果
 */
export async function auditOrder(
  orderId: number,
  isApproved: boolean,
): Promise<boolean> {
  try {
    await auditAcceptanceOrderApi(orderId, {
      is_approved: isApproved,
      remark: isApproved ? '审核通过' : '审核拒绝',
    });
    message.success(isApproved ? '审核通过成功' : '审核拒绝成功');
    return true;
  } catch (error) {
    console.error('审核操作失败:', error);
    message.error('操作失败');
    return false;
  }
}

/**
 * 取消工单
 * @param orderId 工单ID
 * @returns 取消结果
 */
export async function cancelOrder(orderId: number): Promise<boolean> {
  try {
    await cancelAcceptanceOrderApi(orderId, {
      remark: '用户取消',
    });
    message.success('取消工单成功');
    return true;
  } catch (error) {
    console.error('取消操作失败:', error);
    message.error('操作失败');
    return false;
  }
}

/**
 * 上传检测结果
 * @param orderId 工单ID
 * @param files 文件列表
 * @returns 上传结果
 */
export async function uploadFiles(
  orderId: number,
  files: File[],
): Promise<boolean> {
  try {
    await uploadCheckResultApi(orderId, files);
    message.success('上传检测结果成功');
    return true;
  } catch (error) {
    console.error('上传检测结果失败:', error);
    message.error('上传检测结果失败');
    return false;
  }
}

/**
 * 完成工单
 * @param orderId 工单ID
 * @param items 验收项列表
 * @returns 完成结果，包含响应数据
 */
export async function completeOrder(
  orderId: number,
  items: AcceptanceItem[],
): Promise<{ data?: any; success: boolean }> {
  try {
    const response = await completeAcceptanceOrderApi(orderId, {
      remark: '验收完成',
      items: items.map((item) => ({
        device_sn: item.device_sn,
        vendor: item.vendor,
        model: item.model,
        template_name: item.template_name,
        is_pass: !!item.is_pass,
        reason: item.reason || '',
      })),
    });

    // 检查响应数据是否包含额外信息
    const hasInvalidDevices = response?.invalid_device_sns?.length > 0;
    const hasMissingPNInfo = response?.no_existing_pn_info?.length > 0;

    if (!hasInvalidDevices && !hasMissingPNInfo) {
      message.success('完成工单成功');
    }

    return { success: true, data: response };
  } catch (error) {
    console.error('完成操作失败:', error);
    message.error('操作失败');
    return { success: false };
  }
}

/**
 * 获取工单操作历史
 * @param orderId 工单ID
 * @returns 操作历史数组
 */
export async function loadAcceptanceOrderHistory(orderId: number) {
  try {
    return await getAcceptanceOrderHistoryApi(orderId);
  } catch (error) {
    console.error('获取操作历史失败:', error);
    message.error('获取操作历史失败');
    return [];
  }
}

export async function loadAcceptanceItemComponents(itemId: number) {
  try {
    const result = await getAcceptanceItemComponentsApi(itemId);

    if (result && typeof result === 'object') {
      console.error('响应对象的键:', Object.keys(result));
    }

    return result;
  } catch (error: any) {
    console.error('API 调用异常:', error);
    console.error('错误详情:', {
      message: error?.message,
      status: error?.status,
      response: error?.response,
    });
    message.error('加载验收设备组件失败');
    return [];
  }
}

/**
 * 更新单个组件的PN
 * @param componentId 组件ID
 * @param pn PN值
 * @returns 更新结果
 */
export async function updateComponentPN(
  componentId: number,
  pn: string,
): Promise<boolean> {
  try {
    await updateComponentPNApi({ component_id: componentId, pn });
    message.success('PN更新成功');
    return true;
  } catch (error) {
    console.error('更新组件PN失败:', error);
    message.error('更新PN失败');
    return false;
  }
}

/**
 * 批量更新组件PN
 * @param updates 更新数据数组
 * @returns 更新结果
 */
export async function batchUpdateComponentPN(
  updates: { component_id: number; pn: string }[],
): Promise<boolean> {
  try {
    await batchUpdateComponentPNApi({ updates });
    message.success(`成功更新${updates.length}个组件的PN`);
    return true;
  } catch (error) {
    console.error('批量更新组件PN失败:', error);
    message.error('批量更新PN失败');
    return false;
  }
}

/**
 * 更新检测组件信息
 * @param itemId 验收项ID
 * @param orderId 工单ID
 * @param componentInfos 组件信息数组
 * @returns 更新结果
 */
export async function updateInspectingComponentInfo(
  itemId: number,
  orderId: number,
  componentInfos: Array<{
    component_id: number;
    description: string;
    item_id: number;
    model: string;
    pn: string;
    status: string;
  }>,
): Promise<boolean> {
  try {
    await updateInspectingComponentInfoApi(itemId, {
      order_id: orderId,
      item_id: itemId,
      component_infos: componentInfos,
    });
    message.success(`成功更新${componentInfos.length}个组件信息`);
    return true;
  } catch (error) {
    console.error('更新检测组件信息失败:', error);
    message.error('更新组件信息失败');
    return false;
  }
}

/**
 * 自动填入组件的PN
 * @param itemIds 验收项ID数组
 * @param overWriteExisting 是否覆盖已有的PN
 * @returns 填入结果
 */
export async function fillComponentPN(
  itemIds: number[],
  overWriteExisting: boolean = true,
): Promise<boolean> {
  try {
    await fillComponentPNApi({
      item_ids: itemIds,
      over_write_existing: overWriteExisting,
    });
    message.success(`成功为${itemIds.length}个验收项自动填入组件的PN`);
    return true;
  } catch (error) {
    console.error('自动填入PN失败:', error);
    message.error('自动填入PN失败');
    return false;
  }
}
