<script lang="ts" setup>
// 产品管理页面
</script>

<template>
  <div class="p-6">
    <div class="mb-4">
      <h1 class="text-2xl font-bold">产品管理</h1>
      <div class="text-gray-500">管理财务相关产品</div>
    </div>

    <div class="rounded bg-white p-4 shadow">
      <div class="mb-4 flex items-center justify-between">
        <div class="text-lg font-medium">产品列表</div>
        <button class="rounded bg-blue-500 px-4 py-2 text-white">
          添加产品
        </button>
      </div>

      <table class="w-full">
        <thead>
          <tr class="border-b">
            <th class="py-2 text-left">产品名称</th>
            <th class="py-2 text-left">产品类型</th>
            <th class="py-2 text-left">价格</th>
            <th class="py-2 text-left">状态</th>
            <th class="py-2 text-left">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr class="border-b">
            <td class="py-2">基础云服务</td>
            <td class="py-2">云计算</td>
            <td class="py-2">¥99/月</td>
            <td class="py-2"><span class="text-green-500">已上线</span></td>
            <td class="py-2">
              <button class="mr-2 text-blue-500">编辑</button>
              <button class="text-red-500">下线</button>
            </td>
          </tr>
          <tr class="border-b">
            <td class="py-2">高级存储方案</td>
            <td class="py-2">存储服务</td>
            <td class="py-2">¥199/月</td>
            <td class="py-2"><span class="text-green-500">已上线</span></td>
            <td class="py-2">
              <button class="mr-2 text-blue-500">编辑</button>
              <button class="text-red-500">下线</button>
            </td>
          </tr>
          <tr class="border-b">
            <td class="py-2">数据分析套件</td>
            <td class="py-2">数据服务</td>
            <td class="py-2">¥299/月</td>
            <td class="py-2"><span class="text-yellow-500">测试中</span></td>
            <td class="py-2">
              <button class="mr-2 text-blue-500">编辑</button>
              <button class="text-green-500">上线</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
