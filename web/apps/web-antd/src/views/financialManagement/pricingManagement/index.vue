<script lang="ts" setup>
// 定价管理页面
</script>

<template>
  <div class="p-6">
    <div class="mb-4">
      <h1 class="text-2xl font-bold">定价管理</h1>
      <div class="text-gray-500">管理产品价格和定价策略</div>
    </div>

    <div class="rounded bg-white p-4 shadow">
      <div class="mb-4 flex items-center justify-between">
        <div class="text-lg font-medium">价格方案</div>
        <button class="rounded bg-blue-500 px-4 py-2 text-white">
          新增方案
        </button>
      </div>

      <table class="w-full">
        <thead>
          <tr class="border-b">
            <th class="py-2 text-left">方案名称</th>
            <th class="py-2 text-left">适用产品</th>
            <th class="py-2 text-left">基础价格</th>
            <th class="py-2 text-left">折扣</th>
            <th class="py-2 text-left">生效时间</th>
            <th class="py-2 text-left">状态</th>
            <th class="py-2 text-left">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr class="border-b">
            <td class="py-2">标准方案</td>
            <td class="py-2">基础云服务</td>
            <td class="py-2">¥99/月</td>
            <td class="py-2">无</td>
            <td class="py-2">2023-01-01</td>
            <td class="py-2"><span class="text-green-500">生效中</span></td>
            <td class="py-2">
              <button class="mr-2 text-blue-500">编辑</button>
              <button class="text-red-500">停用</button>
            </td>
          </tr>
          <tr class="border-b">
            <td class="py-2">年付优惠</td>
            <td class="py-2">基础云服务</td>
            <td class="py-2">¥999/年</td>
            <td class="py-2">15%</td>
            <td class="py-2">2023-03-15</td>
            <td class="py-2"><span class="text-green-500">生效中</span></td>
            <td class="py-2">
              <button class="mr-2 text-blue-500">编辑</button>
              <button class="text-red-500">停用</button>
            </td>
          </tr>
          <tr class="border-b">
            <td class="py-2">企业套餐</td>
            <td class="py-2">高级存储方案</td>
            <td class="py-2">¥599/月</td>
            <td class="py-2">20%</td>
            <td class="py-2">2023-06-01</td>
            <td class="py-2"><span class="text-yellow-500">待生效</span></td>
            <td class="py-2">
              <button class="mr-2 text-blue-500">编辑</button>
              <button class="text-green-500">立即生效</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
