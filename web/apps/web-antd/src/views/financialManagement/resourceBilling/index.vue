<script lang="ts" setup>
// 资源出账页面
</script>

<template>
  <div class="p-6">
    <div class="mb-4">
      <h1 class="text-2xl font-bold">资源出账</h1>
      <div class="text-gray-500">管理资源使用计费和出账</div>
    </div>

    <div class="mb-4 rounded bg-white p-4 shadow">
      <div class="mb-4 text-lg font-medium">出账周期</div>
      <div class="flex space-x-4">
        <div class="flex flex-1 items-center rounded border p-3">
          <div class="mr-2">当前周期：</div>
          <div class="font-medium">2023年6月</div>
        </div>
        <div class="flex flex-1 items-center rounded border p-3">
          <div class="mr-2">出账日期：</div>
          <div class="font-medium">2023-07-01</div>
        </div>
        <div class="flex flex-1 items-center rounded border p-3">
          <div class="mr-2">出账状态：</div>
          <div class="font-medium text-yellow-500">待出账</div>
        </div>
      </div>
    </div>

    <div class="rounded bg-white p-4 shadow">
      <div class="mb-4 flex items-center justify-between">
        <div class="text-lg font-medium">资源使用明细</div>
        <div>
          <button class="mr-2 rounded bg-green-500 px-4 py-2 text-white">
            生成账单
          </button>
          <button class="rounded bg-blue-500 px-4 py-2 text-white">
            导出明细
          </button>
        </div>
      </div>

      <table class="w-full">
        <thead>
          <tr class="border-b">
            <th class="py-2 text-left">客户名称</th>
            <th class="py-2 text-left">资源类型</th>
            <th class="py-2 text-left">使用量</th>
            <th class="py-2 text-left">单价</th>
            <th class="py-2 text-left">金额</th>
            <th class="py-2 text-left">计费周期</th>
            <th class="py-2 text-left">状态</th>
          </tr>
        </thead>
        <tbody>
          <tr class="border-b">
            <td class="py-2">科技有限公司</td>
            <td class="py-2">基础云服务</td>
            <td class="py-2">30天</td>
            <td class="py-2">¥99/月</td>
            <td class="py-2">¥99.00</td>
            <td class="py-2">2023-06-01 至 2023-06-30</td>
            <td class="py-2"><span class="text-yellow-500">待出账</span></td>
          </tr>
          <tr class="border-b">
            <td class="py-2">数据科技公司</td>
            <td class="py-2">高级存储方案</td>
            <td class="py-2">500GB</td>
            <td class="py-2">¥0.5/GB</td>
            <td class="py-2">¥250.00</td>
            <td class="py-2">2023-06-01 至 2023-06-30</td>
            <td class="py-2"><span class="text-yellow-500">待出账</span></td>
          </tr>
          <tr class="border-b">
            <td class="py-2">互联网科技</td>
            <td class="py-2">数据分析套件</td>
            <td class="py-2">20天</td>
            <td class="py-2">¥299/月</td>
            <td class="py-2">¥199.33</td>
            <td class="py-2">2023-06-10 至 2023-06-30</td>
            <td class="py-2"><span class="text-yellow-500">待出账</span></td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
