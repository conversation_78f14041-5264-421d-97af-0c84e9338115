<script lang="ts" setup>
// 资源开通页面
</script>

<template>
  <div class="p-6">
    <div class="mb-4">
      <h1 class="text-2xl font-bold">资源开通</h1>
      <div class="text-gray-500">管理资源开通和授权</div>
    </div>

    <div class="rounded bg-white p-4 shadow">
      <div class="mb-4 flex items-center justify-between">
        <div class="text-lg font-medium">资源开通记录</div>
        <button class="rounded bg-blue-500 px-4 py-2 text-white">
          新增开通
        </button>
      </div>

      <table class="w-full">
        <thead>
          <tr class="border-b">
            <th class="py-2 text-left">订单号</th>
            <th class="py-2 text-left">客户名称</th>
            <th class="py-2 text-left">资源类型</th>
            <th class="py-2 text-left">开通时间</th>
            <th class="py-2 text-left">到期时间</th>
            <th class="py-2 text-left">状态</th>
            <th class="py-2 text-left">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr class="border-b">
            <td class="py-2">ORD20230001</td>
            <td class="py-2">科技有限公司</td>
            <td class="py-2">基础云服务</td>
            <td class="py-2">2023-01-15</td>
            <td class="py-2">2024-01-14</td>
            <td class="py-2"><span class="text-green-500">已开通</span></td>
            <td class="py-2">
              <button class="mr-2 text-blue-500">详情</button>
              <button class="text-red-500">停用</button>
            </td>
          </tr>
          <tr class="border-b">
            <td class="py-2">ORD20230042</td>
            <td class="py-2">数据科技公司</td>
            <td class="py-2">高级存储方案</td>
            <td class="py-2">2023-03-22</td>
            <td class="py-2">2023-09-21</td>
            <td class="py-2"><span class="text-green-500">已开通</span></td>
            <td class="py-2">
              <button class="mr-2 text-blue-500">详情</button>
              <button class="text-red-500">停用</button>
            </td>
          </tr>
          <tr class="border-b">
            <td class="py-2">ORD20230078</td>
            <td class="py-2">互联网科技</td>
            <td class="py-2">数据分析套件</td>
            <td class="py-2">2023-05-10</td>
            <td class="py-2">-</td>
            <td class="py-2"><span class="text-yellow-500">待开通</span></td>
            <td class="py-2">
              <button class="mr-2 text-blue-500">详情</button>
              <button class="text-green-500">立即开通</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
