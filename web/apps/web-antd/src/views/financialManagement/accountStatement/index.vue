<script lang="ts" setup>
// 账单明细页面
</script>

<template>
  <div class="p-6">
    <div class="mb-4">
      <h1 class="text-2xl font-bold">账单明细</h1>
      <div class="text-gray-500">查看客户账单明细</div>
    </div>

    <div class="mb-4 rounded bg-white p-4 shadow">
      <div class="flex flex-wrap gap-4">
        <div class="min-w-[200px] flex-1">
          <div class="mb-1 text-gray-500">客户筛选</div>
          <select class="w-full rounded border p-2">
            <option>全部客户</option>
            <option>科技有限公司</option>
            <option>数据科技公司</option>
            <option>互联网科技</option>
          </select>
        </div>
        <div class="min-w-[200px] flex-1">
          <div class="mb-1 text-gray-500">账单周期</div>
          <select class="w-full rounded border p-2">
            <option>2023年6月</option>
            <option>2023年5月</option>
            <option>2023年4月</option>
          </select>
        </div>
        <div class="min-w-[200px] flex-1">
          <div class="mb-1 text-gray-500">账单状态</div>
          <select class="w-full rounded border p-2">
            <option>全部状态</option>
            <option>已支付</option>
            <option>未支付</option>
            <option>已逾期</option>
          </select>
        </div>
        <div class="flex items-end">
          <button class="rounded bg-blue-500 px-4 py-2 text-white">查询</button>
        </div>
      </div>
    </div>

    <div class="rounded bg-white p-4 shadow">
      <div class="mb-4 flex items-center justify-between">
        <div class="text-lg font-medium">账单列表</div>
        <button class="rounded bg-blue-500 px-4 py-2 text-white">
          导出账单
        </button>
      </div>

      <table class="w-full">
        <thead>
          <tr class="border-b">
            <th class="py-2 text-left">账单编号</th>
            <th class="py-2 text-left">客户名称</th>
            <th class="py-2 text-left">账单周期</th>
            <th class="py-2 text-left">账单金额</th>
            <th class="py-2 text-left">生成时间</th>
            <th class="py-2 text-left">支付状态</th>
            <th class="py-2 text-left">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr class="border-b">
            <td class="py-2">BILL20230601</td>
            <td class="py-2">科技有限公司</td>
            <td class="py-2">2023年5月</td>
            <td class="py-2">¥99.00</td>
            <td class="py-2">2023-06-01</td>
            <td class="py-2"><span class="text-green-500">已支付</span></td>
            <td class="py-2">
              <button class="mr-2 text-blue-500">查看</button>
              <button class="text-blue-500">下载</button>
            </td>
          </tr>
          <tr class="border-b">
            <td class="py-2">BILL20230602</td>
            <td class="py-2">数据科技公司</td>
            <td class="py-2">2023年5月</td>
            <td class="py-2">¥250.00</td>
            <td class="py-2">2023-06-01</td>
            <td class="py-2"><span class="text-red-500">未支付</span></td>
            <td class="py-2">
              <button class="mr-2 text-blue-500">查看</button>
              <button class="text-blue-500">下载</button>
            </td>
          </tr>
          <tr class="border-b">
            <td class="py-2">BILL20230603</td>
            <td class="py-2">互联网科技</td>
            <td class="py-2">2023年5月</td>
            <td class="py-2">¥199.33</td>
            <td class="py-2">2023-06-01</td>
            <td class="py-2"><span class="text-yellow-500">已逾期</span></td>
            <td class="py-2">
              <button class="mr-2 text-blue-500">查看</button>
              <button class="text-blue-500">下载</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
