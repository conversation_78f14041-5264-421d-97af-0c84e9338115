<script lang="ts" setup>
// 计费明细页面
</script>

<template>
  <div class="p-6">
    <div class="mb-4">
      <h1 class="text-2xl font-bold">计费明细</h1>
      <div class="text-gray-500">查看资源使用计费明细</div>
    </div>

    <div class="mb-4 rounded bg-white p-4 shadow">
      <div class="flex flex-wrap gap-4">
        <div class="min-w-[200px] flex-1">
          <div class="mb-1 text-gray-500">客户筛选</div>
          <select class="w-full rounded border p-2">
            <option>全部客户</option>
            <option>科技有限公司</option>
            <option>数据科技公司</option>
            <option>互联网科技</option>
          </select>
        </div>
        <div class="min-w-[200px] flex-1">
          <div class="mb-1 text-gray-500">资源类型</div>
          <select class="w-full rounded border p-2">
            <option>全部类型</option>
            <option>基础云服务</option>
            <option>高级存储方案</option>
            <option>数据分析套件</option>
          </select>
        </div>
        <div class="min-w-[200px] flex-1">
          <div class="mb-1 text-gray-500">计费周期</div>
          <div class="flex space-x-2">
            <input type="date" class="flex-1 rounded border p-2" />
            <span class="flex items-center">至</span>
            <input type="date" class="flex-1 rounded border p-2" />
          </div>
        </div>
        <div class="flex items-end">
          <button class="rounded bg-blue-500 px-4 py-2 text-white">查询</button>
        </div>
      </div>
    </div>

    <div class="rounded bg-white p-4 shadow">
      <div class="mb-4 flex items-center justify-between">
        <div class="text-lg font-medium">计费明细列表</div>
        <div>
          <button class="mr-2 rounded bg-green-500 px-4 py-2 text-white">
            统计分析
          </button>
          <button class="rounded bg-blue-500 px-4 py-2 text-white">
            导出明细
          </button>
        </div>
      </div>

      <table class="w-full">
        <thead>
          <tr class="border-b">
            <th class="py-2 text-left">记录ID</th>
            <th class="py-2 text-left">客户名称</th>
            <th class="py-2 text-left">资源类型</th>
            <th class="py-2 text-left">计费项目</th>
            <th class="py-2 text-left">使用量</th>
            <th class="py-2 text-left">单价</th>
            <th class="py-2 text-left">金额</th>
            <th class="py-2 text-left">计费时间</th>
          </tr>
        </thead>
        <tbody>
          <tr class="border-b">
            <td class="py-2">REC20230601001</td>
            <td class="py-2">科技有限公司</td>
            <td class="py-2">基础云服务</td>
            <td class="py-2">CPU使用</td>
            <td class="py-2">720小时</td>
            <td class="py-2">¥0.05/小时</td>
            <td class="py-2">¥36.00</td>
            <td class="py-2">2023-06-01</td>
          </tr>
          <tr class="border-b">
            <td class="py-2">REC20230601002</td>
            <td class="py-2">科技有限公司</td>
            <td class="py-2">基础云服务</td>
            <td class="py-2">内存使用</td>
            <td class="py-2">4GB×720小时</td>
            <td class="py-2">¥0.02/GB·小时</td>
            <td class="py-2">¥57.60</td>
            <td class="py-2">2023-06-01</td>
          </tr>
          <tr class="border-b">
            <td class="py-2">REC20230601003</td>
            <td class="py-2">数据科技公司</td>
            <td class="py-2">高级存储方案</td>
            <td class="py-2">存储空间</td>
            <td class="py-2">500GB</td>
            <td class="py-2">¥0.5/GB</td>
            <td class="py-2">¥250.00</td>
            <td class="py-2">2023-06-01</td>
          </tr>
          <tr class="border-b">
            <td class="py-2">REC20230610001</td>
            <td class="py-2">互联网科技</td>
            <td class="py-2">数据分析套件</td>
            <td class="py-2">API调用</td>
            <td class="py-2">10,000次</td>
            <td class="py-2">¥0.01/次</td>
            <td class="py-2">¥100.00</td>
            <td class="py-2">2023-06-10</td>
          </tr>
        </tbody>
      </table>

      <div class="mt-4 flex items-center justify-between">
        <div>共 <span class="font-medium">24</span> 条记录</div>
        <div class="flex space-x-2">
          <button class="rounded border px-3 py-1">上一页</button>
          <button class="rounded bg-blue-500 px-3 py-1 text-white">1</button>
          <button class="rounded border px-3 py-1">2</button>
          <button class="rounded border px-3 py-1">3</button>
          <button class="rounded border px-3 py-1">下一页</button>
        </div>
      </div>
    </div>
  </div>
</template>
