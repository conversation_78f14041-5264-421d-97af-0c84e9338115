<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { Product } from '#/api/core/cmdb/product/product';

import { onMounted, ref } from 'vue';

import { message, Tag } from 'ant-design-vue';

import {
  createProductApi,
  deleteProductApi,
  getBrandsApi,
  getMaterialTypesApi,
  getProductCategoriesApi,
  getProductListApi,
  getSpecsByMaterialTypeApi,
  updateProductApi as originalUpdateProductApi,
} from '#/api/core/cmdb/product/product';
import CommonDetail from '#/components/CommonDetail/index.vue';
import CommonList from '#/components/CommonList/index.vue';

// 为了匹配CommonList组件的API格式，封装更新API
const wrappedUpdateProductApi = (data: any) => {
  return originalUpdateProductApi(data.id, data);
};

const detailRef = ref();
const detailData = ref<null | Product>(null);
const commonListRef = ref();

// 选项列表
const materialTypeOptions = ref<{ label: string; value: string }[]>([]);
const productCategoryOptions = ref<{ label: string; value: string }[]>([]);
const brandOptions = ref<{ label: string; value: string }[]>([]);
// 规格选项
const specOptions = ref<{ label: string; value: string }[]>([]);
const specOptionsLoading = ref(false);

// 添加物料类型到产品类别的映射关系
const materialTypeToCategory: { [key: string]: string } = {
  CPU: '配件',
  内存: '配件',
  硬盘: '配件',
  SSD: '配件',
  显卡: '配件',
  主板: '配件',
  机箱: '配件',
  电源模块: '配件',
  风扇: '配件',
  散热器: '配件',
  跳线: '配件',
  BMC板: '配件',
  网卡: '网络设备配件',
  线缆: '线缆',
  光模块: '网络设备配件',
  raid卡: '配件',
  GPU底板: '配件',
  Switch板: '网络设备配件',
  背板: '配件',
  服务器: '服务器',
  交换机: '网络设备',
  路由器: '网络设备',
  维保: '维保',
  AOC: 'AOC',
  // 其他物料类型默认归为"其他"
};

// 获取选项数据
async function fetchOptions() {
  try {
    // 获取物料类型
    const materialTypes = await getMaterialTypesApi();
    materialTypeOptions.value = materialTypes.map((type) => ({
      label: type,
      value: type,
    }));

    // 获取产品类别
    const categories = await getProductCategoriesApi();
    productCategoryOptions.value = categories.map((category) => ({
      label: category,
      value: category,
    }));

    // 获取品牌
    const brands = await getBrandsApi();
    brandOptions.value = brands.map((brand) => ({
      label: brand,
      value: brand,
    }));
  } catch (error) {
    console.error('获取选项数据失败:', error);

    // 设置一些默认值以防API失败
    if (materialTypeOptions.value.length === 0) {
      materialTypeOptions.value = [
        { label: 'CPU', value: 'CPU' },
        { label: '内存', value: '内存' },
        { label: '硬盘', value: '硬盘' },
        { label: '显卡', value: '显卡' },
      ];
    }

    if (productCategoryOptions.value.length === 0) {
      productCategoryOptions.value = [
        { label: '服务器', value: '服务器' },
        { label: '存储设备', value: '存储设备' },
        { label: '网络设备', value: '网络设备' },
        { label: '网络设备配件', value: '网络设备配件' },
        { label: '配件', value: '配件' },
        { label: '线缆', value: '线缆' },
        { label: '维保', value: '维保' },
        { label: '其他', value: '其他' },
      ];
    }

    if (brandOptions.value.length === 0) {
      brandOptions.value = [
        { label: 'Intel', value: 'Intel' },
        { label: 'AMD', value: 'AMD' },
        { label: 'Samsung', value: 'Samsung' },
      ];
    }
  }
}

// 获取特定物料类型的规格选项
async function fetchSpecOptions(materialType: string) {
  if (!materialType) {
    specOptions.value = [];
    return;
  }

  try {
    const specs = await getSpecsByMaterialTypeApi({
      material_type: materialType,
    });
    specOptions.value = specs.map((spec) => ({
      label: spec,
      value: spec,
    }));
  } catch {
    message.error('获取规格选项失败');
    specOptions.value = [];
  }
}

// 加载规格选项 - 从产品列表获取所有规格
async function loadSpecOptions() {
  try {
    specOptionsLoading.value = true;

    // 调用API获取产品列表
    const res = await getProductListApi({
      page: 1,
      pageSize: 1000,
    });

    // 处理响应数据
    if (res && Array.isArray(res.list)) {
      // 收集所有不重复的规格
      const uniqueSpecs = new Set<string>();

      res.list.forEach((item: Product) => {
        if (
          item.spec &&
          typeof item.spec === 'string' &&
          item.spec.trim() !== ''
        ) {
          uniqueSpecs.add(item.spec.trim());
        }
      });

      // 转换为选项格式
      specOptions.value = [...uniqueSpecs].map((spec: string) => ({
        label: spec,
        value: spec,
      }));

      // 按照规格名称排序
      specOptions.value.sort((a, b) => a.label.localeCompare(b.label));
    } else {
      console.error('加载规格选项失败，响应数据格式不正确:', res);
      message.error('加载规格选项失败，请刷新页面重试');
      specOptions.value = [];
    }
  } catch (error) {
    console.error('加载规格选项异常:', error);
    message.error('加载规格选项出错，请刷新页面重试');
    specOptions.value = [];
  } finally {
    specOptionsLoading.value = false;
  }
}

// 初始化时获取选项数据
onMounted(() => {
  fetchOptions();
  loadSpecOptions(); // 加载所有规格选项
});

// 默认数据
const defaultData = {
  material_type: '',
  brand: '',
  model: '',
  spec: '',
  pn: '',
  product_category: '',
  unit: '',
  reference_price: null,
  warranty_period: null,
  supplier_ids: [],
  status: 1,
  description: '',
};

// 表格搜索表单
const formOptions: VbenFormProps = {
  collapsed: true,
  collapsedRows: 2,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入PN号码' },
      fieldName: 'pn',
      label: 'PN号码',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择物料类型',
        options: materialTypeOptions,
        allowClear: true,
        showSearch: true,
        mode: 'combobox',
        filterOption: (
          input: string,
          option: { label: string; value: string },
        ) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
      },
      fieldName: 'material_type',
      label: '物料类型',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择产品类别',
        options: productCategoryOptions,
        allowClear: true,
        showSearch: true,
        mode: 'combobox',
        filterOption: (
          input: string,
          option: { label: string; value: string },
        ) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
      },
      fieldName: 'product_category',
      label: '产品类别',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择品牌',
        options: brandOptions,
        allowClear: true,
        showSearch: true,
        mode: 'combobox',
        filterOption: (
          input: string,
          option: { label: string; value: string },
        ) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
      },
      fieldName: 'brand',
      label: '品牌',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择或输入规格',
        options: specOptions,
        style: { width: '280px' },
        showSearch: true,
        mode: 'combobox',
        allowClear: true,
        autoClearSearchValue: false,
        loading: specOptionsLoading,
        filterOption: (
          input: string,
          option: { label: string; value: string },
        ) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
      },
      fieldName: 'spec',
      label: '规格',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入型号' },
      fieldName: 'model',
      label: '型号',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
        allowClear: true,
      },
      fieldName: 'status',
      label: '状态',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: true,
};

// 表格配置
const gridOptions: VxeGridProps<Product> = {
  checkboxConfig: {
    highlight: true,
  },
  exportConfig: {}, // 导出配置
  importConfig: {
    types: ['csv'],
    remote: true, // 使用服务端导入
  }, // 导入配置
  columns: [
    { align: 'center', type: 'checkbox', width: 40 },
    { field: 'id', title: 'ID', width: 60 },
    { field: 'pn', title: 'PN号码', width: 220, visible: true },
    { field: 'material_type', title: '物料类型', width: 120 },
    { field: 'product_category', title: '产品类别', width: 120 },
    { field: 'brand', title: '品牌', width: 120 },
    { field: 'spec', title: '规格', minWidth: 300 },
    { field: 'model', title: '型号', width: 300 },
    { field: 'unit', title: '单位', width: 80 },
    { field: 'reference_price', title: '参考单价(元)', width: 120 },
    { field: 'warranty_period', title: '质保期(月)', width: 100 },
    { field: 'status', title: '状态', width: 80, slots: { default: 'status' } },

    {
      title: '操作',
      slots: { default: 'operate' },
      width: 160,
      fixed: 'right',
    },
  ],
  keepSource: true,
  height: 'auto',
  rowConfig: { isHover: true },
  pagerConfig: { pageSize: 10, pageSizes: [10, 20, 50, 100] },
  toolbarConfig: {
    custom: true,
    refresh: true,
    export: true,
    zoom: true,
    import: true,
    slots: {
      buttons: 'toolbar-buttons',
    },
  },
};

// 新增/编辑表单配置
const editFormOptions: VbenFormProps = {
  showDefaultActions: false,
  schema: [
    {
      fieldName: 'material_type',
      label: '物料类型',
      component: 'Select',
      componentProps: {
        placeholder: '请选择或输入物料类型',
        options: materialTypeOptions,
        style: { width: '220px' },
        showSearch: true,
        mode: 'combobox',
        allowClear: true,
        autoClearSearchValue: false,
        filterOption: (
          input: string,
          option: { label: string; value: string },
        ) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
        onSearch: (value: string) => {
          // 如果当前没有匹配选项，添加输入值作为新选项
          if (value && !specOptions.value.some((opt) => opt.value === value)) {
            const existingOption = specOptions.value.find(
              (opt) => opt.value.toLowerCase() === value.toLowerCase(),
            );
            if (!existingOption) {
              specOptions.value = [
                ...specOptions.value,
                { label: value, value },
              ];
            }
          }
        },
        onChange: (value: string) => {
          // 当物料类型改变时，加载对应的规格选项
          fetchSpecOptions(value);

          // 自动选择对应的产品类别
          if (value) {
            // 查找物料类型对应的产品类别
            const category = materialTypeToCategory[value] || '其他';

            // 找到产品类别选择框并设置值
            if (commonListRef.value && commonListRef.value.formApi) {
              // 使用CommonList组件的表单API设置产品类别
              commonListRef.value.formApi.setFieldValue(
                'product_category',
                category,
              );
            }
          }
        },
      },
      rules: 'required',
    },
    {
      fieldName: 'product_category',
      label: '产品类别',
      component: 'Select',
      componentProps: {
        placeholder: '请选择或输入产品类别',
        options: productCategoryOptions,
        style: { width: '220px' },
        showSearch: true,
        mode: 'combobox',
        allowClear: true,
        autoClearSearchValue: false,
        filterOption: (
          input: string,
          option: { label: string; value: string },
        ) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
        onSearch: (value: string) => {
          // 如果当前没有匹配选项，添加输入值作为新选项
          if (
            value &&
            !productCategoryOptions.value.some((opt) => opt.value === value)
          ) {
            const existingOption = productCategoryOptions.value.find(
              (opt) => opt.value.toLowerCase() === value.toLowerCase(),
            );
            if (!existingOption) {
              productCategoryOptions.value = [
                ...productCategoryOptions.value,
                { label: value, value },
              ];
            }
          }
        },
      },
    },
    {
      fieldName: 'brand',
      label: '品牌',
      component: 'Select',
      componentProps: {
        placeholder: '请选择或输入品牌',
        options: brandOptions,
        style: { width: '220px' },
        showSearch: true,
        mode: 'combobox',
        allowClear: true,
        autoClearSearchValue: false,
        filterOption: (
          input: string,
          option: { label: string; value: string },
        ) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
        onSearch: (value: string) => {
          // 如果当前没有匹配选项，添加输入值作为新选项
          if (value && !brandOptions.value.some((opt) => opt.value === value)) {
            const existingOption = brandOptions.value.find(
              (opt) => opt.value.toLowerCase() === value.toLowerCase(),
            );
            if (!existingOption) {
              brandOptions.value = [
                ...brandOptions.value,
                { label: value, value },
              ];
            }
          }
        },
      },
    },
    {
      fieldName: 'model',
      label: '型号',
      component: 'Input',
      componentProps: {
        placeholder: '请输入型号',
      },
    },
    {
      fieldName: 'spec',
      label: '规格',
      component: 'Select',
      componentProps: {
        placeholder: '请选择或输入规格',
        options: specOptions,
        style: { width: '280px' },
        showSearch: true,
        mode: 'combobox',
        allowClear: true,
        autoClearSearchValue: false,
        loading: specOptionsLoading,
        filterOption: (
          input: string,
          option: { label: string; value: string },
        ) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
      },
      rules: 'required',
    },
    // PN字段在规格页面的编辑表单中也显示
    {
      fieldName: 'pn',
      label: 'PN号码',
      component: 'Input',
      componentProps: { placeholder: '请输入PN号码' },
      rules: 'required',
    },
    // 新增字段
    {
      fieldName: 'unit',
      label: '单位',
      component: 'Input',
      componentProps: {
        placeholder: '请输入单位',
        maxLength: 20,
      },
    },
    {
      fieldName: 'reference_price',
      label: '参考单价',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入参考单价',
        style: { width: '100%' },
        min: 0,
        precision: 2,
        step: 0.01,
      },
    },
    {
      fieldName: 'warranty_period',
      label: '质保期(月)',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入质保期',
        style: { width: '100%' },
        min: 0,
        precision: 0,
      },
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '已启用', value: 1 },
          { label: '已禁用', value: 0 },
        ],
        optionType: 'button',
      },
      defaultValue: 1,
      fieldName: 'status',
      label: '状态',
    },
    {
      fieldName: 'description',
      label: '产品描述',
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入产品描述',
        showCount: true,
        rows: 4,
      },
    },
  ],
};

// 详情字段配置
const detailFields = [
  { label: 'ID', field: 'id' },
  { label: '物料类型', field: 'material_type' },
  { label: '产品类别', field: 'product_category' },
  { label: '品牌', field: 'brand' },
  { label: '型号', field: 'model' },
  { label: '规格', field: 'spec' },
  { label: 'PN号码', field: 'pn' },
  { label: '单位', field: 'unit' },
  { label: '参考单价(元)', field: 'reference_price' },
  { label: '质保期(月)', field: 'warranty_period' },
  {
    label: '状态',
    field: 'status',
    type: 'status',
    statusMap: {
      1: { text: '启用', status: 'success' },
      0: { text: '禁用', status: 'error' },
    },
  },
  { label: '产品描述', field: 'description' },
  { label: '创建时间', field: 'created_at' },
  { label: '更新时间', field: 'updated_at' },
] as any;

// 处理详情事件
function handleDetail(row: Product) {
  detailData.value = row;
  detailRef.value?.drawerApi.open();
}

// 自定义权限配置
const permissions = {
  add: ['AssetMgt:SpecificationsInfo:Create'],
  edit: ['AssetMgt:SpecificationsInfo:Edit'],
  delete: ['AssetMgt:SpecificationsInfo:Delete'],
  import: ['AssetMgt:SpecificationsInfo:Import'],
};

// 添加处理编辑前的钩子函数
async function handleBeforeEdit(record: any) {
  // 直接返回记录，不再需要加载规格选项
  // 因为我们已经在组件初始化时通过loadSpecOptions加载了所有规格
  return record;
}
</script>

<template>
  <div>
    <!-- 使用通用列表组件 -->
    <CommonList
      ref="commonListRef"
      :form-options="formOptions"
      :grid-options="gridOptions"
      :edit-form-options="editFormOptions"
      :api="{
        getList: getProductListApi,
        add: createProductApi,
        edit: wrappedUpdateProductApi,
        delete: deleteProductApi,
      }"
      :default-data="defaultData"
      @detail="handleDetail"
      :permissions="permissions"
      :before-edit-hook="handleBeforeEdit"
      show-add-button
      show-edit-button
      show-delete-button
      show-detail-button
      :enable-confirm="true"
      import-model-type="product"
      show-import-button
      :show-template-button="true"
    >
      <!-- 自定义表格渲染 -->
      <template #status="{ row }">
        <Tag :color="row.status === 1 ? 'green' : 'red'">
          {{ row.status === 1 ? '启用' : '禁用' }}
        </Tag>
      </template>
    </CommonList>

    <!-- 使用通用详情组件 -->
    <CommonDetail
      ref="detailRef"
      :data="detailData"
      title="规格详情"
      :fields="detailFields"
      show-audit-log
      table-name="products"
    />
  </div>
</template>
