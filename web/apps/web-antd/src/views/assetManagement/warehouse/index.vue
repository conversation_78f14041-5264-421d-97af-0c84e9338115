<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type {
  Warehouse,
  WarehouseWithStats,
} from '#/api/core/cmdb/asset/warehouse';

import { h, onMounted, ref } from 'vue';

import { Badge, message, Tag } from 'ant-design-vue';

import {
  createWarehouseApi,
  deleteWarehouseApi,
  getWarehouseListApi,
  getWarehouseStatsApi,
  getWarehouseTypesApi,
  updateWarehouseApi,
} from '#/api/core/cmdb/asset/warehouse';
import { getRoomTableApi } from '#/api/core/cmdb/location/room';
import CommonDetail from '#/components/CommonDetail/index.vue';
import CommonList from '#/components/CommonList/index.vue';

const detailRef = ref();
const detailData = ref<null | WarehouseWithStats>(null);
const commonListRef = ref();

// 选项列表
const warehouseTypeOptions = ref<{ label: string; value: string }[]>([
  { label: '备件仓', value: '备件仓' },
  { label: '主机房仓', value: '主机房仓' },
  { label: '网络设备仓', value: '网络设备仓' },
  { label: '通用仓库', value: '通用仓库' },
]);

// 使用API动态获取房间选项
const roomOptions = ref<{ label: string; value: number }[]>([]);
const roomOptionsLoading = ref(false);

const statusOptions = ref<{ label: string; value: string }[]>([
  { label: '启用', value: 'active' },
  { label: '停用', value: 'inactive' },
]);

// 状态映射
const statusMap: {
  [key: string]: { color: string; text: string };
} = {
  active: { text: '启用', color: 'green' },
  inactive: { text: '停用', color: 'red' },
};

// 初始化选项数据
onMounted(async () => {
  try {
    // 获取仓库类型选项
    const typesRes = await getWarehouseTypesApi();
    if (typesRes && Array.isArray(typesRes)) {
      warehouseTypeOptions.value = typesRes.map((type: string) => ({
        label: type,
        value: type,
      }));
    }

    // 获取房间选项
    await fetchRoomOptions();
  } catch (error) {
    console.error('获取初始化选项失败:', error);
    message.error('获取初始化选项失败');
  }
});

// 获取房间选项的函数
async function fetchRoomOptions() {
  try {
    roomOptionsLoading.value = true;
    const res = await getRoomTableApi({ page: 1, pageSize: 100 });

    if (res && Array.isArray(res.list)) {
      roomOptions.value = res.list.map((room) => ({
        label: `${room.name}${room.dataCenter ? ` (${room.dataCenter.name})` : ''}`,
        value: room.id,
      }));
    } else {
      console.error('获取房间选项失败，响应格式不正确:', res);
      message.error('获取房间选项失败');
    }
  } catch (error) {
    console.error('获取房间选项异常:', error);
    message.error('获取房间选项失败');
  } finally {
    roomOptionsLoading.value = false;
  }
}

// 默认数据
const defaultData = {
  name: '',
  code: '',
  type: '备件仓',
  room_id: undefined,
  description: '',
  status: 'active',
};

// 表格搜索表单
const formOptions: VbenFormProps = {
  collapsed: true,
  collapsedRows: 2,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入仓库名称/编码' },
      fieldName: 'query',
      label: '关键词',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择仓库类型',
        options: warehouseTypeOptions,
        allowClear: true,
      },
      fieldName: 'type',
      label: '仓库类型',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择所在房间',
        options: roomOptions,
        allowClear: true,
        loading: roomOptionsLoading,
      },
      fieldName: 'room_id',
      label: '所在房间',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: true,
};

// 表格配置
const gridOptions: VxeGridProps<Warehouse> = {
  checkboxConfig: {
    highlight: true,
  },
  exportConfig: {}, // 导出配置
  columns: [
    { align: 'center', fixed: 'left', type: 'checkbox', width: 40 },
    { field: 'id', title: 'ID', width: 60, fixed: 'left' },
    { field: 'code', title: '仓库编码', fixed: 'left', width: 120 },
    { field: 'name', title: '仓库名称', width: 180 },
    { field: 'type', title: '仓库类型', width: 120 },
    {
      field: 'status',
      title: '状态',
      width: 80,
      slots: { default: 'status' },
    },
    {
      field: 'room.name',
      title: '所在房间',
      width: 120,
    },
    {
      field: 'room.dataCenter.name',
      title: '所属机房',
      width: 120,
    },
    { field: 'description', title: '描述', minWidth: 180 },
    { field: 'created_at', title: '创建时间', width: 140, visible: false },
    { field: 'updated_at', title: '更新时间', width: 140, visible: false },
    {
      title: '操作',
      slots: { default: 'operate' },
      width: 280,
      fixed: 'right',
    },
  ],
  keepSource: true,
  height: 'auto',
  rowConfig: { isHover: true },
  pagerConfig: { pageSize: 10, pageSizes: [10, 20, 50, 100] },
  toolbarConfig: {
    custom: true,
    refresh: true,
    export: true,
    zoom: true,
    slots: {
      buttons: 'toolbar-buttons',
    },
  },
};

// 处理详情事件
async function handleDetail(row: Warehouse) {
  try {
    // 同时获取详情和统计信息
    const statsRes = await getWarehouseStatsApi(row.id as number);
    detailData.value = statsRes;
    detailRef.value?.drawerApi.open();
  } catch (error) {
    console.error('获取仓库详情信息失败:', error);
    message.error('获取仓库详情信息失败');
  }
}

// 自定义权限配置
const permissions = {
  add: ['AssetMgt:Warehouse:Create'],
  edit: ['AssetMgt:Warehouse:Edit'],
  delete: ['AssetMgt:Warehouse:Delete'],
};

// 为了匹配CommonList组件的API格式，封装更新API
const wrappedUpdateWarehouseApi = (data: any) => {
  return updateWarehouseApi(data.id, data);
};

// 新增/编辑表单配置
const editFormOptions: VbenFormProps = {
  showDefaultActions: false,
  schema: [
    {
      fieldName: 'name',
      label: '仓库名称',
      component: 'Input',
      componentProps: {
        placeholder: '请输入仓库名称',
      },
      rules: 'required',
    },
    {
      fieldName: 'code',
      label: '仓库编码',
      component: 'Input',
      componentProps: {
        placeholder: '请输入仓库编码',
      },
      rules: 'required',
    },
    {
      fieldName: 'type',
      label: '仓库类型',
      component: 'Select',
      componentProps: {
        placeholder: '请选择仓库类型',
        options: warehouseTypeOptions,
        style: { width: '200px' },
      },
      rules: 'required',
    },
    {
      fieldName: 'room_id',
      label: '所在房间',
      component: 'Select',
      componentProps: {
        placeholder: '请选择所在房间',
        options: roomOptions,
        allowClear: true,
        loading: roomOptionsLoading,
        showSearch: true,
        filterOption: (input: string, option: any) =>
          option.label.toLowerCase().includes(input.toLowerCase()),
        style: { width: '100%' },
      },
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: statusOptions,
        optionLabelProp: 'label',
        optionRender: ({ option }: { option: any }) => {
          return h('div', {}, [
            h(
              Tag,
              {
                color: statusMap[option.value]?.color || 'default',
                style: { marginRight: '5px' },
              },
              () =>
                statusMap[option.value]?.text || option.label || option.value,
            ),
          ]);
        },
      },
      rules: 'required',
    },
    {
      fieldName: 'description',
      label: '描述',
      component: 'Input',
      componentProps: {
        placeholder: '请输入描述',
        rows: 4,
        type: 'textarea',
      },
    },
  ],
};

// 合并的详情字段配置（包含基本信息和统计信息）
const detailFields = [
  { label: 'ID', field: 'id' },
  { label: '仓库编码', field: 'code' },
  { label: '仓库名称', field: 'name' },
  { label: '仓库类型', field: 'type' },
  {
    label: '状态',
    field: 'status',
    type: 'status' as const,
    statusMap,
  },
  {
    label: '所在房间',
    field: 'room.name',
    format: (value: any) => value || '未指定',
  },
  {
    label: '所属机房',
    field: 'room.dataCenter.name',
    format: (value: any) => value || '未指定',
  },
  // 统计信息
  { label: '产品种类数', field: 'product_count' },
  { label: '物品总数', field: 'total_items' },
  { label: '可用物品数', field: 'available_items' },
  // 其他详情
  { label: '描述', field: 'description' },
  { label: '创建时间', field: 'created_at' },
  { label: '更新时间', field: 'updated_at' },
];
</script>

<template>
  <div>
    <!-- 使用通用列表组件 -->
    <CommonList
      ref="commonListRef"
      :form-options="formOptions"
      :grid-options="gridOptions"
      :edit-form-options="editFormOptions"
      :api="{
        getList: getWarehouseListApi,
        add: createWarehouseApi,
        edit: wrappedUpdateWarehouseApi,
        delete: deleteWarehouseApi,
      }"
      :default-data="defaultData"
      @detail="handleDetail"
      :permissions="permissions"
      show-add-button
      show-edit-button
      show-delete-button
      show-detail-button
      :enable-confirm="true"
      drawer-class="w-1/2"
    >
      <!-- 状态插槽 -->
      <template #status="{ row }">
        <Badge :status="row.status === 'active' ? 'success' : 'error'" />
        <span>{{ statusMap[row.status]?.text || row.status }}</span>
      </template>
    </CommonList>

    <!-- 使用通用详情组件 - 合并后的详情（包含统计信息） -->
    <CommonDetail
      ref="detailRef"
      :data="detailData"
      title="仓库详情"
      :fields="detailFields"
      show-audit-log
      table-name="warehouses"
      class="w-1/2"
    />
  </div>
</template>
