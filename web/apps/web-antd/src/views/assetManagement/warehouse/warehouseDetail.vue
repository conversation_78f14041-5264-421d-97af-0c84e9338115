<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { AssetSpare } from '#/api/core/cmdb/asset/model';
import type {
  Warehouse,
  WarehouseWithStats,
} from '#/api/core/cmdb/asset/warehouse';

import { computed, onMounted, reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { message, Tag } from 'ant-design-vue';

import {
  getWarehouseDetailApi,
  getWarehouseSparesApi,
  getWarehouseStatsApi,
} from '#/api/core/cmdb/asset/warehouse';

const route = useRoute();
const router = useRouter();
const warehouseId = computed(() => Number(route.params.id) || 0);
const warehouseData = ref<null | Warehouse>(null);
const statsData = ref<null | WarehouseWithStats>(null);
const loading = ref(false);
const sparesList = ref<AssetSpare[]>([]);
const sparesTotal = ref(0);
const sparesLoading = ref(false);
const activeTab = ref('spares');

// 加载仓库基本信息和统计数据
const loadWarehouseData = async () => {
  if (!warehouseId.value) {
    message.error('仓库ID无效');
    router.push('/warehouse');
    return;
  }

  loading.value = true;
  try {
    // 获取仓库详情
    const detail = await getWarehouseDetailApi(warehouseId.value);
    warehouseData.value = detail;

    // 获取统计信息
    const stats = await getWarehouseStatsApi(warehouseId.value);
    statsData.value = stats;
  } catch (error) {
    console.error('加载仓库数据失败:', error);
    message.error('加载仓库数据失败');
  } finally {
    loading.value = false;
  }
};

// 加载仓库内备件列表
const loadWarehouseSpares = async (page = 1, pageSize = 10) => {
  if (!warehouseId.value) return;

  sparesLoading.value = true;
  try {
    const res = await getWarehouseSparesApi({
      warehouseId: warehouseId.value,
      page,
      pageSize,
    });

    if (res && Array.isArray(res.list)) {
      sparesList.value = res.list;
      sparesTotal.value = res.total;
    }
  } catch (error) {
    console.error('加载仓库备件失败:', error);
    message.error('加载仓库备件失败');
  } finally {
    sparesLoading.value = false;
  }
};

// 页面初始化
onMounted(async () => {
  await loadWarehouseData();
  await loadWarehouseSpares();
});

// 备件表格配置
const sparesGridOptions = reactive<VxeGridProps<AssetSpare>>({
  checkboxConfig: {
    highlight: true,
  },
  columns: [
    { field: 'id', title: 'ID', width: 60 },
    { field: 'sn', title: 'SN', width: 140 },
    { field: 'type', title: '类型', width: 100 },
    { field: 'brand', title: '品牌', width: 100 },
    { field: 'model', title: '型号', width: 120 },
    { field: 'pn', title: 'PN号码', width: 120 },
    { field: 'asset_status', title: '资产状态', width: 100 },
    { field: 'hardware_status', title: '硬件状态', width: 100 },
    { field: 'location', title: '存放位置', width: 140 },
    { field: 'created_at', title: '创建时间', width: 140 },
  ],
  keepSource: true,
  height: 600,
  rowConfig: { isHover: true },
  pagerConfig: {
    pageSize: 10,
    pageSizes: [10, 20, 50, 100],
  },
});

// 处理表格页码变化
const handlePageChange = (params: any) => {
  loadWarehouseSpares(params.currentPage, params.pageSize);
};

// 获取状态标签颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'active': {
      return 'green';
    }
    case 'inactive': {
      return 'red';
    }
    default: {
      return 'default';
    }
  }
};

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'active': {
      return '活跃';
    }
    case 'inactive': {
      return '停用';
    }
    default: {
      return status;
    }
  }
};
</script>

<template>
  <div>
    <a-card
      :loading="loading"
      :title="`仓库详情: ${warehouseData?.name || ''}（${warehouseData?.code || ''}）`"
    >
      <!-- 仓库基本信息 -->
      <a-row :gutter="16" class="mb-4">
        <a-col :span="6">
          <a-statistic title="仓库类型" :value="warehouseData?.type || '-'" />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="所在房间"
            :value="warehouseData?.room?.name || '未指定'"
          />
        </a-col>
        <a-col :span="6">
          <div>
            <h4>状态</h4>
            <Tag :color="getStatusColor(warehouseData?.status || '')">
              {{ getStatusText(warehouseData?.status || '') }}
            </Tag>
          </div>
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="创建时间"
            :value="warehouseData?.created_at || '-'"
          />
        </a-col>
      </a-row>

      <!-- 仓库描述 -->
      <a-row class="mb-4">
        <a-col :span="24">
          <h4>描述</h4>
          <p>{{ warehouseData?.description || '无描述' }}</p>
        </a-col>
      </a-row>

      <!-- 统计信息卡片 -->
      <a-row :gutter="16" class="mb-4">
        <a-col :span="8">
          <a-card>
            <a-statistic
              title="产品种类数"
              :value="statsData?.product_count || 0"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card>
            <a-statistic
              title="物品总数"
              :value="statsData?.total_items || 0"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card>
            <a-statistic
              title="可用物品数"
              :value="statsData?.available_items || 0"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
      </a-row>

      <!-- 内容标签页 -->
      <a-tabs v-model:active-key="activeTab">
        <a-tab-pane key="spares" tab="仓库备件">
          <vxe-grid
            v-bind="sparesGridOptions"
            :data="sparesList"
            :loading="sparesLoading"
            :pager-config="{
              pageSize: 10,
              pageSizes: [10, 20, 50, 100],
              total: sparesTotal,
            }"
            @page-change="handlePageChange"
          />
        </a-tab-pane>
        <a-tab-pane key="inventory" tab="库存明细">
          <div class="p-10 text-center text-gray-500">
            <p>库存明细功能开发中...</p>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>
