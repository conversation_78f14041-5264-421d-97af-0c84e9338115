<script lang="ts" setup>
import type {
  OutboundTicketHistoryRes,
  OutboundTicketRes,
} from '#/api/core/ticket/types';

import { computed, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { getWarehouseListApi } from '#/api/core/cmdb/asset/warehouse';
import {
  getOutboundTicket,
  historyOutboundTicket,
} from '#/api/core/ticket/asset-ticket';

// 导入组件
import DetailHeader from './components/detail-header.vue';
import FileList from './components/fileList.vue';
import History from './components/history.vue';
import Process from './components/process.vue';

const ticket = ref<OutboundTicketRes>();
const historyData = ref<OutboundTicketHistoryRes[]>([]);
const warehouseMap = ref<Record<string, string>>({});
const router = useRouter();
const route = useRoute();
const ticketId = computed(() => route.params.id as string);

// 加载数据
const onLoad = async () => {
  const id = ticketId.value;
  if (!id) {
    console.error('未找到出库单ID');
    return;
  }

  // 获取出库单信息
  try {
    const res = await getOutboundTicket(id);
    ticket.value = res;
  } catch (error) {
    console.error('获取出库单信息失败:', error);
  }

  // 获取出库历史
  try {
    const res = await historyOutboundTicket(id);
    historyData.value = res;
  } catch (error) {
    console.error('获取出库历史失败:', error);
  }

  // 获取仓库信息
  try {
    const result = await getWarehouseListApi({
      page: 1,
      pageSize: 1000,
    });
    // 使用forEach替代reduce
    const map: Record<string, string> = {};
    result.list.forEach((item) => {
      map[item.id] = item.name;
    });
    warehouseMap.value = map;
  } catch (error) {
    console.error('获取仓库信息失败:', error);
  }
};

// 返回列表页
const goBack = () => {
  router.push('/asset-out-warehouse');
};

// 刷新组件
const handleRefresh = () => {
  onLoad();
};

onMounted(onLoad);
</script>

<template>
  <div class="min-h-screen bg-gray-50 pb-4 dark:bg-gray-900">
    <!-- 顶部标题和状态栏 -->
    <div
      class="mb-4 border-b border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800"
    >
      <div
        class="mx-auto flex max-w-full items-center justify-between px-4 py-3"
      >
        <div class="flex items-center">
          <a-button
            type="primary"
            @click="goBack"
            class="mr-4 flex items-center overflow-hidden whitespace-nowrap rounded-md border border-blue-600 border-transparent bg-blue-500 px-3 py-1 font-medium text-white shadow-sm transition-all duration-300 hover:bg-blue-600 hover:shadow-md"
          >
            <template #icon><span class="anticon">←</span></template>
            返回列表
          </a-button>
        </div>
      </div>
    </div>

    <div class="mx-auto max-w-full px-4">
      <!-- 出库申请信息 -->
      <DetailHeader
        v-if="ticket && ticket.id"
        :ticket="ticket"
        :warehouse-map="warehouseMap"
        :key="historyData.length"
      />

      <!-- 处理流程 -->
      <Process
        v-if="ticket && ticket.id"
        :id="ticketId"
        :ticket="ticket"
        @submit-success="handleRefresh"
        :key="historyData.length"
      />

      <!-- 出入库图片列表 -->
      <FileList
        v-if="
          ticket &&
          ticket.id &&
          ['allocate', 'rack', 'return_repair', 'sell'].includes(
            ticket.outbound_reason,
          )
        "
        :id="Number(ticketId)"
        module-type="outbound-ticket-photo"
        title="出入室图片"
        :key="historyData.length"
      />

      <!-- 验收单文件列表 -->
      <FileList
        v-if="ticket && ticket.id"
        :id="Number(ticketId)"
        module-type="outbound-ticket-verify"
        title="验收单列表"
        :key="historyData.length"
      />

      <!-- 处理历史 -->
      <History
        v-if="ticket && ticket.id"
        :history="historyData"
        :key="historyData.length"
      />
    </div>
  </div>
</template>
