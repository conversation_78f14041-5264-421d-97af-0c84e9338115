import type { VxeGridProps } from '#/adapter/vxe-table';
import type { RepairTicket } from '#/api/core/ticket/repair-ticket';
import type { OutboundTicketRes } from '#/api/core/ticket/types';

import { downloadExportFile } from '#/api/core/export';
import { getExportTemplateApi } from '#/api/core/purchase/purchase_old';
import { uploadFile, uploadFiles } from '#/api/core/upload';

export const outboundTypes: Record<string, string> = {
  part: '配件出库',
  device: '设备出库',
};

export const outboundTypeOptions = Object.entries(outboundTypes).map(
  ([key, value]) => ({
    value: key,
    label: value,
  }),
);

export const outboundReasons: Record<string, string> = {
  repair: '维修',
  replacement: '改配',
  sell: '售卖',
  allocate: '调拨',
  return_repair: '返修',
  rack: '上架',
};

export const outTypeOptions = Object.entries(outboundTypes).map(
  ([key, value]) => ({
    value: key,
    label: value,
  }),
);

export const outboundReason: Record<string, string> = {
  repair: '维修',
  replacement: '改配',
  sell: '售卖',
  allocate: '调拨',
  return_repair: '返修',
};

// 每个类型对应的出库类型
export const outboundReasonByType: Record<string, Record<string, string>> = {
  part: {
    repair: '维修',
    replacement: '改配',
    sell: '售卖',
    allocate: '调拨',
    return_repair: '返修',
  },
  device: {
    rack: '上架',
    // sell: '售卖',
    allocate: '调拨',
    // repair: '维修',
    // return_repair: '返修',
  },
  // server: {
  //   sell: '售卖',
  //   allocate: '调拨',
  //   repair: '维修',
  //   return_repair: '返修',
  // },
  // network: {
  //   sell: '售卖',
  //   allocate: '调拨',
  //   repair: '维修',
  //   return_repair: '返修',
  // },
};

export const outReasonOptions = Object.entries(outboundReason).map(
  ([key, value]) => ({
    value: key,
    label: value,
  }),
);

export const outboundStatusMap: Record<
  string,
  { color: string; text: string }
> = {
  waiting_approval: { color: 'processing', text: '待审批' },
  waiting_second_approval: { color: 'processing', text: '待确认' },
  outbounding: { color: 'processing', text: '正在出库' },
  completed: { color: 'success', text: '工单完成' },
  // complete_outbound: '出库完成',
  cancelled: { color: 'error', text: '取消出库' },
  rejected: { color: 'error', text: '拒绝出库' },

  // 新版本状态
  submiting_apply: { color: 'processing', text: '发起申请' },
  engineer_approval_pass: { color: 'success', text: '审批通过' },
  engineer_approval_fail: { color: 'error', text: '审批拒绝' },
  asset_approval_pass: { color: 'success', text: '审批通过' },
  asset_approval_fail: { color: 'danger', text: '审批拒绝' },
  replace_approval_pass: { color: 'success', text: '填写完成' },
  replace_approval_fail: { color: 'error', text: '拒绝' },
  waiting_engineer_approval: { color: 'processing', text: '工程师审批' },
  waiting_asset_approval: { color: 'processing', text: '资管审批' },
  waiting_replace_operate: { color: 'processing', text: '改配审批' },
  waiting_asset_dest_approval: { color: 'processing', text: '收货方审批' },
  asset_dest_approval_pass: { color: 'success', text: '审批通过' },
  asset_dest_approval_fail: { color: 'error', text: '审批拒绝' },
  waiting_buyer_approval: { color: 'processing', text: '买方审批' },
  buyer_approval_pass: { color: 'success', text: '审批通过' },
  buyer_approval_fail: { color: 'error', text: '审批拒绝' },
};

export const outboundFormMap: Record<string, string> = {
  rejected: '拒绝出库',
  complete_outbound: '工单完成',
  waiting_engineer_approval: '专业工程师审批',
  waiting_asset_approval: '资产管理员审批',
  waiting_replace_operate: '改配负责人审批',
  waiting_asset_dest_approval: '收货方资管审批',
};

export const outboundStageMap: Record<string, string> = {
  submit_apply: '发起申请',
  engineer_approval: '驻场运维负责人审批',
  asset_approval: '资产管理员审批',
  replace_approval: '改配负责人确认',
  asset_dest_approval: '收货方资管审批',
  rejected: '拒绝出库',
  completed: '完成工单',
};

const stepMap: Record<string, number> = {
  waiting_approval: 0,
  // waiting_second_approval: 1,
  outbounding: 1,
  completed: 2,
  rejected: 2,
  cancelled: -1,
};

// const stepMap2: Record<string, number> = {
//   waiting_approval: 0,
//   // outbounding: 1,
//   completed: 1,
//   rejected: 1,
//   cancelled: -1,
// };

const replacementStepMap: Record<string, number> = {
  waiting_engineer_approval: 1,
  waiting_asset_approval: 2,
  waiting_replace_operate: 3,
  completed: 4,
  rejected: 4,
};

const allocateStepMap: Record<string, number> = {
  waiting_asset_approval: 1,
  waiting_asset_dest_approval: 2,
  completed: 3,
  rejected: 3,
};

const returnRepairStepMap: Record<string, number> = {
  waiting_asset_approval: 1,
  completed: 2,
  rejected: 2,
};

const repairStepMap: Record<string, number> = {
  waiting_engineer_approval: 1,
  waiting_asset_approval: 2,
  completed: 3,
  rejected: 3,
};

const sellStepMap: Record<string, number> = {
  waiting_buyer_approval: 1,
  completed: 2,
  rejected: 2,
};

const rackStepMap: Record<string, number> = {
  waiting_asset_approval: 1,
  completed: 2,
  rejected: 2,
};

export function currentStep(status: string, outbound_reason: string): number {
  switch (outbound_reason) {
    case 'allocate': {
      return allocateStepMap[status] ?? +0;
    }
    case 'rack': {
      return rackStepMap[status] ?? +0;
    }
    case 'repair': {
      return repairStepMap[status] ?? -2;
    }
    case 'replacement': {
      return replacementStepMap[status] ?? +0;
    }
    case 'return_repair': {
      return returnRepairStepMap[status] ?? +0;
    }
    case 'sell': {
      return sellStepMap[status] ?? -2;
    }
    default: {
      return stepMap[status] ?? -2;
    }
  }
}

// 步骤
const steps = [
  {
    title: '开始',
  },
  {
    title: '驻场运维负责人审批',
  },
  {
    title: '资产管理员审批',
  },
  // {
  //   title: 'SN确认',
  // },
  {
    title: '完成',
  },
];
const steps2 = [
  {
    title: '开始',
  },
  {
    title: '驻场运维负责人审批',
  },
  {
    title: '资产管理员审批',
  },
  // {
  //   title: 'SN确认',
  // },
  {
    title: '拒绝出库',
  },
];

// const steps3 = [
//   {
//     title: '发起申请',
//   },
//   {
//     title: '资产管理员',
//   },
//   // {
//   //   title: 'SN确认',
//   // },
//   {
//     title: '完成',
//   },
// ];

// const steps4 = [
//   {
//     title: '发起申请',
//   },
//   {
//     title: '资产管理员',
//   },
//   // {
//   //   title: 'SN确认',
//   // },
//   {
//     title: '拒绝出库',
//   },
// ];

export function currentSteps(status: string, outbound_reason: string): any[] {
  switch (outbound_reason) {
    case 'allocate': {
      const steps: { status?: string; title: string }[] = [
        { title: '发起申请' },
        { title: '发货方资管审批' },
        { title: '收货方资管审批' },
      ];
      if (status === 'rejected') {
        steps.push({ title: '拒绝', status: 'error' });
      } else {
        steps.push({ title: '完成' });
      }
      return steps;
    }
    case 'rack': {
      const steps: { status?: string; title: string }[] = [
        { title: '发起申请' },
        { title: '资产管理员审批' },
      ];
      if (status === 'rejected') {
        steps.push({ title: '拒绝', status: 'error' });
      } else {
        steps.push({ title: '完成' });
      }
      return steps;
    }
    case 'repair': {
      const steps: { status?: string; title: string }[] = [
        { title: '发起申请' },
        { title: '专业工程师审批' },
        { title: '资产管理员审批' },
      ];
      if (status === 'rejected') {
        steps.push({ title: '拒绝', status: 'error' });
      } else {
        steps.push({ title: '完成' });
      }
      return steps;
    }
    case 'replacement': {
      const steps: { status?: string; title: string }[] = [
        { title: '发起申请' },
        { title: '专业工程师审批' },
        { title: '资产管理员审批' },
        { title: '改配负责人确认' },
      ];
      if (status === 'rejected') {
        steps.push({ title: '拒绝', status: 'error' });
      } else {
        steps.push({ title: '完成' });
      }
      return steps;
    }
    case 'return_repair': {
      const steps: { status?: string; title: string }[] = [
        { title: '发起申请' },
        { title: '资产管理员审批' },
      ];
      if (status === 'rejected') {
        steps.push({ title: '拒绝', status: 'error' });
      } else {
        steps.push({ title: '完成' });
      }
      return steps;
    }
    case 'sell': {
      const steps: { status?: string; title: string }[] = [
        { title: '发起申请' },
        { title: '买方审批' },
      ];
      if (status === 'rejected') {
        steps.push({ title: '拒绝', status: 'error' });
      } else {
        steps.push({ title: '完成' });
      }
      return steps;
    }
    default: {
      return status === 'rejected' ? steps2 : steps;
    }
  }
}

// export const buttonTextMap: Record<string, string> = {
//   waiting_approval: '通过',
//   waiting_second_approval: '通过',
//   outbounding: '出库',
// };

export const statusColors: Record<string, string> = {
  complete: 'green',
  cancelled: 'red',
  rejected: 'red',
};

export const selectedRepairOrderGrid: VxeGridProps<RepairTicket> = {
  minHeight: 40,
  columns: [
    { field: 'ticket_no', title: '维修单号' },
    { field: 'status', title: '状态', slots: { default: 'status' } },
    {
      field: 'repair_type',
      title: '维修类型',
      slots: { default: 'repair_type' },
    },
    { field: 'assigned_engineer_name', title: '接单工程师' },
    { field: 'repair_result', title: '维修结果' },
  ],
  data: [],
  pagerConfig: {
    enabled: false,
  },
};

export function isHuaiLai(project: string) {
  return (
    project?.toLowerCase() === 'cloud11' || project?.toLowerCase() === 'cloud23'
  );
}

// 资产状态映射
export const assetStatusMap: {
  [key: string]: { color: string; text: string };
} = {
  pending_in: { text: '待入库', color: 'purple' },
  in_stock: { text: '已入库', color: 'blue' },
  pending_out: { text: '待出库', color: 'orange' },
  out_stock: { text: '已出库', color: 'cyan' },
  idle: { text: '闲置中', color: 'lime' },
  in_use: { text: '使用中', color: 'green' },
  repairing: { text: '维修中', color: 'gold' },
  pending_scrap: { text: '待报废', color: 'volcano' },
  scrapped: { text: '已报废', color: 'red' },
};

// 硬件状态映射
export const hardwareStatusMap: {
  [key: string]: { color: string; text: string };
} = {
  normal: { text: '正常', color: 'green' },
  faulty: { text: '故障', color: 'red' },
  warning: { text: '警告', color: 'orange' },
};

// 来源类型映射
export const sourceTypeMap: {
  [key: string]: { color: string; text: string };
} = {
  新购: { text: '新购', color: 'blue' },
  拆机: { text: '拆机', color: 'orange' },
  维修: { text: '维修', color: 'purple' },
  退回: { text: '退回', color: 'gray' },
  其他: { text: '其他', color: 'gray' },
};

export const btnOutboundReasonTextMap: Record<string, string> = {
  repair: '选择维修工单',
  // replacement: '选择SN',
  repaired: '返修出库',
};

export function shouldInputSN(ticket: OutboundTicketRes): boolean {
  return (
    ticket.status === 'outbounding' ||
    (ticket.outbound_reason === 'sell' && ticket.status === 'waiting_approval')
  );
}

export function shouldInputSNV2(ticket: OutboundTicketRes): boolean {
  switch (ticket.outbound_reason) {
    case 'allocate':
    case 'repair':
    case 'replacement':
    case 'return_repair':
    case 'sell': {
      switch (ticket.status) {
        case 'waiting_asset_approval':
        case 'waiting_buyer_approval':
        case 'waiting_replace_operate': {
          return true;
        }
        default: {
          return false;
        }
      }
    }
    default: {
      return false;
    }
  }
}

export async function downloadSpareTemplate(fileName: string) {
  await downloadExportFile(`/import/template/outbound-spare`, fileName);
}

export async function downloadTemplate(fileName: string) {
  await downloadExportFile(`/download/${fileName}`);
}

export async function uploadVerifyFile(
  file: File,
  modelType: string,
  modelID: number,
  describe: string,
) {
  await uploadFile(file, modelType, modelID, describe);
}

export async function uploadVerifyFiles(
  files: File[],
  modelType: string,
  modelID: number,
  describe: string,
) {
  await uploadFiles(files, modelType, modelID, describe);
}

/**
 * 获取出库模板
 * @param type 类型
 * @param reason 原因
 * @param outbound_id 出库单ID
 * @returns {Promise<{templateType: string, fileName: string, filePath: string}>}
 */
export async function getTemplate(
  type: string,
  reason: string,
  outbound_id: string,
) {
  let templateType = '';
  let fileName = '';
  let filePath = '';

  // 使用switch-case结构确定模板类型
  switch (type) {
    case 'device': {
      switch (reason) {
        case 'allocate': {
          templateType = 'allocate_device_template';
          break;
        }
        case 'rack': {
          templateType = 'rack_device_template';
          break;
        }
      }
      break;
    }
    case 'part': {
      switch (reason) {
        case 'allocate': {
          templateType = 'allocate_part_template';
          break;
        }
        case 'repair': {
          templateType = 'repair_out_part_template';
          break;
        }
        case 'replacement': {
          templateType = 'replace_part_template';
          break;
        }
        case 'return_repair': {
          templateType = 'return_repair_out_part_template';
          break;
        }
        case 'sell': {
          templateType = 'sell_part_template';
          break;
        }
      }
      break;
    }
  }

  if (templateType) {
    // 从API获取文件信息
    try {
      const response = await getExportTemplateApi({
        template_type: templateType,
        outbound_id,
      });
      fileName = response.fileName;
      filePath = response.filePath;
    } catch (error) {
      console.error('获取模板失败:', error);
    }
  }

  return {
    templateType,
    fileName,
    filePath,
  };
}
