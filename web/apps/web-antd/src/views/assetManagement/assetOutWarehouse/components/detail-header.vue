<script setup lang="ts">
import type { OutboundTicketRes } from '#/api/core/ticket/types';

import { formatToDateTime } from '#/utils/common/time';

import { repairTypeMap } from '../../../hardwareMaintenance/hardwareOrder/type';
import {
  outboundReasons,
  outboundTypes,
  statusColors as statusColorMap,
  outboundStatusMap as statusTextMap,
} from '../config';
import PartInfo from './partInfo.vue';

const props = defineProps<{
  ticket: OutboundTicketRes;
  warehouseMap?: Record<string, string>;
}>();

// 从repairTypeMap提取颜色和文本
const repairColorMap: Record<string, string> = {};
const repairTextMap: Record<string, string> = {};

Object.entries(repairTypeMap).forEach(([key, value]) => {
  if (typeof value === 'object') {
    repairColorMap[key] = value.color || 'blue';
    repairTextMap[key] = value.text || key;
  }
});
</script>

<template>
  <div class="mb-4">
    <a-card
      class="h-full overflow-hidden rounded-lg border border-gray-200 transition-shadow duration-300 hover:shadow-md dark:border-gray-700"
    >
      <template #title>
        <div class="flex items-center font-medium">
          <a-badge status="processing" text="出库申请信息" />
        </div>
      </template>
      <div class="pt-1">
        <a-row :gutter="24" class="mb-4">
          <a-col :span="8">
            <div class="flex py-1">
              <a-typography-text type="secondary" class="w-[100px] font-medium">
                工单编号
              </a-typography-text>
              <a-typography-text class="text-gray-800 dark:text-gray-200">
                {{ props.ticket.ticketNo }}
              </a-typography-text>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="flex py-1">
              <a-typography-text type="secondary" class="w-[100px] font-medium">
                申请人
              </a-typography-text>
              <a-typography-text class="text-gray-800 dark:text-gray-200">
                {{ props.ticket.reporterName }}
              </a-typography-text>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="flex py-1">
              <a-typography-text type="secondary" class="w-[100px] font-medium">
                申请时间
              </a-typography-text>
              <a-typography-text class="text-gray-800 dark:text-gray-200">
                {{ formatToDateTime(props.ticket.created_at) }}
              </a-typography-text>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="flex py-1">
              <a-typography-text type="secondary" class="w-[100px] font-medium">
                项目
              </a-typography-text>
              <a-typography-text class="text-gray-800 dark:text-gray-200">
                {{ props.ticket.project }}
              </a-typography-text>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="flex py-1">
              <a-typography-text type="secondary" class="w-[100px] font-medium">
                出库类型
              </a-typography-text>
              <a-typography-text class="text-gray-800 dark:text-gray-200">
                {{ outboundTypes[props.ticket.outbound_type] }}
              </a-typography-text>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="flex py-1">
              <a-typography-text type="secondary" class="w-[100px] font-medium">
                出库原因
              </a-typography-text>
              <a-typography-text class="text-gray-800 dark:text-gray-200">
                {{ outboundReasons[props.ticket.outbound_reason] }}
              </a-typography-text>
            </div>
          </a-col>

          <a-col
            :span="8"
            v-if="
              [
                'allocate',
                'return_repair',
                'sell',
                'replacement',
                'rack',
              ].includes(props.ticket.outbound_reason)
            "
          >
            <div class="flex py-1">
              <a-typography-text type="secondary" class="w-[100px] font-medium">
                出库位置
              </a-typography-text>
              <a-typography-text class="text-gray-800 dark:text-gray-200">
                {{ props.warehouseMap?.[props.ticket.source_warehouse_id] }}
              </a-typography-text>
            </div>
          </a-col>
          <a-col
            :span="8"
            v-if="['allocate'].includes(props.ticket.outbound_reason)"
          >
            <div class="flex py-1">
              <a-typography-text type="secondary" class="w-[100px] font-medium">
                目标位置
              </a-typography-text>
              <a-typography-text class="text-gray-800 dark:text-gray-200">
                {{ props.warehouseMap?.[props.ticket.dest_warehouse_id] }}
              </a-typography-text>
            </div>
          </a-col>
          <template v-if="props.ticket.outbound_reason === 'sell'">
            <a-col :span="8">
              <div class="flex py-1">
                <a-typography-text
                  type="secondary"
                  class="w-[100px] font-medium"
                >
                  合同编号
                </a-typography-text>
                <a-typography-text class="text-gray-800 dark:text-gray-200">
                  {{ props.ticket.order_number }}
                </a-typography-text>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="flex py-1">
                <a-typography-text
                  type="secondary"
                  class="w-[100px] font-medium"
                >
                  买方信息
                </a-typography-text>
                <a-typography-text class="text-gray-800 dark:text-gray-200">
                  {{ props.ticket.buyer_info }}
                </a-typography-text>
              </div>
            </a-col>
          </template>
        </a-row>

        <!-- 关联维修工单 -->
        <template
          v-if="props.ticket.repair_ticket && props.ticket.repair_ticket[0]?.id"
        >
          <div class="mt-4">
            <a-typography-title :level="5" class="mb-2">
              关联维修工单
            </a-typography-title>
            <a-table
              :data-source="[props.ticket.repair_ticket[0]]"
              :pagination="false"
            >
              <a-table-column
                key="order_no"
                title="工单号"
                data-index="order_no"
              />
              <a-table-column key="title" title="标题" data-index="title" />
              <a-table-column key="status" title="状态">
                <template #default="{ record }">
                  <a-tag :color="statusColorMap[record.status]">
                    {{ statusTextMap[record.status] || record.status }}
                  </a-tag>
                </template>
              </a-table-column>
              <a-table-column key="repair_type" title="维修类型">
                <template #default="{ record }">
                  <a-tag :color="repairColorMap[record.repair_type]">
                    {{
                      repairTextMap[record.repair_type] || record.repair_type
                    }}
                  </a-tag>
                </template>
              </a-table-column>
            </a-table>
          </div>
        </template>

        <!-- 根据出库类型显示不同组件 -->
        <!-- <template v-if="props.ticket.outbound_type === 'device'">
          <DeviceInfo :id="props.ticket.id" class="mt-4" />
        </template> -->

        <PartInfo :id="props.ticket.id" class="mt-4" />
      </div>
    </a-card>
  </div>
</template>
