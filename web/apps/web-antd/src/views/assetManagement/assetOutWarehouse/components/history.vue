<script setup lang="ts">
import type { OutboundTicketHistoryRes } from '#/api/core/ticket/types';

import { formatToDateTime } from '#/utils/common/time';

import { outboundStageMap, outboundStatusMap, statusColors } from '../config';

const props = defineProps<{
  history: OutboundTicketHistoryRes[];
}>();

// 格式化备注，替换状态码为状态名称
const remarkFileter = (remark: string) => {
  Object.keys(outboundStatusMap).forEach((key: string) => {
    remark = remark.replace(key, outboundStatusMap[key] || key);
  });
  return remark;
};

// 根据状态获取历史记录的时间线项颜色
const historyTimelineItemColor = (status: string) => {
  return statusColors[status] || 'blue';
};
</script>

<template>
  <div
    class="mb-4 rounded-lg border border-gray-200 bg-white pl-4 pr-4 shadow-sm dark:border-gray-700 dark:bg-gray-800"
  >
    <a-tabs>
      <a-tab-pane key="history" tab="处理历史">
        <a-timeline v-if="props.history.length > 0" class="px-4">
          <a-timeline-item
            v-for="(item, index) in props.history"
            :key="index"
            :color="historyTimelineItemColor(item.new_status)"
          >
            <div
              class="mb-4 rounded-lg border border-gray-100 bg-white p-4 shadow-sm transition-shadow duration-300 hover:shadow-md dark:border-gray-700 dark:bg-gray-800"
            >
              <div
                class="mb-2 flex items-center justify-between border-b border-gray-100 pb-2 dark:border-gray-700"
              >
                <div>
                  <a-badge :color="historyTimelineItemColor(item.new_status)" />
                  <a-typography-text
                    strong
                    class="text-gray-800 dark:text-gray-200"
                  >
                    {{ outboundStageMap[item.stage] || item.stage }}
                  </a-typography-text>
                  <a-typography-text type="secondary" class="ml-2 text-sm">
                    {{ formatToDateTime(item.operation_time) }}
                  </a-typography-text>
                </div>
              </div>
              <div class="pl-2">
                <div class="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <a-typography-text type="secondary" class="font-medium">
                      操作人：
                    </a-typography-text>
                    <a-typography-text>
                      {{ item.operator_name || '-' }}
                    </a-typography-text>
                  </div>
                  <div>
                    <a-typography-text type="secondary" class="font-medium">
                      操作：
                    </a-typography-text>
                    <a-typography-text>
                      {{ outboundStatusMap[item.new_status] || '-' }}
                    </a-typography-text>
                  </div>
                  <div v-if="item.activity_category">
                    <a-typography-text type="secondary" class="font-medium">
                      活动类型：
                    </a-typography-text>
                    <a-typography-text>
                      {{ item.activity_category || '-' }}
                    </a-typography-text>
                  </div>
                  <div class="col-span-2">
                    <a-typography-text type="secondary" class="font-medium">
                      备注：
                    </a-typography-text>
                    <a-typography-text>
                      {{ remarkFileter(item.remarks) }}
                    </a-typography-text>
                  </div>
                </div>
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
        <a-empty v-else description="暂无状态历史" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
