<script setup lang="ts">
import type { VxeGridProps } from '#/adapter/vxe-table';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getOutboundTicketInfo } from '#/api/core/ticket/asset-ticket';

const props = defineProps<{
  id: string; // 出库单ID
}>();

const partInfoGrid: VxeGridProps = {
  maxHeight: 400,
  minHeight: 40,
  columns: [
    { title: '序号', type: 'seq', width: 50 },
    { field: 'product.material_type', title: '物料类型' },
    { field: 'product.brand', title: '品牌' },
    { field: 'product.model', title: '型号' },
    { field: 'product.spec', title: '规格' },
    { field: 'product.pn', title: '原厂PN' },
    {
      field: 'product.product_category',
      title: '产品类别',
    },
    { field: 'amount', title: '数量' },
  ],
  proxyConfig: {
    ajax: {
      query: async () => {
        const res = await getOutboundTicketInfo(props.id);
        // 过滤出配件类型数据
        const partItems = res;
        return {
          items: partItems,
          total: partItems.length,
        };
      },
    },
  },
  pagerConfig: {
    enabled: false,
  },
  scrollY: {
    enabled: true,
    gt: 0,
  },
  showOverflow: true,
};

const [PartInfoGrid] = useVbenVxeGrid({
  gridOptions: partInfoGrid,
});
</script>

<template>
  <PartInfoGrid />
</template>
