<script setup lang="ts">
import type { VxeGridProps } from '#/adapter/vxe-table';

import { ref, watch } from 'vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

interface DataItem {
  value: number | string;
  label: string;
  [key: string]: any;
}

interface Props {
  modelValue?: null | number | string;
  gridOptions: VxeGridProps;
  placeholder?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null,
  columns: () => [],
  placeholder: '请选择',
});

const emit = defineEmits<{
  (e: 'update:modelValue', value: null | number | string): void;
  (e: 'change', value: null | number | string): void;
}>();

const [Grid] = useVbenVxeGrid({});

const dropdownOpen = ref(false);
const searchText = ref('');
const selectedValue = ref<null | number | string>(props.modelValue);
const displayValue = ref('');

// 处理选择
// eslint-disable-next-line no-unused-vars
const _handleSelect = (record: DataItem) => {
  selectedValue.value = record.value;
  displayValue.value = record.label;
  emit('update:modelValue', record.value);
  emit('change', record.value);
  dropdownOpen.value = false;
};

// 处理搜索
const handleSearch = (e: Event) => {
  searchText.value = (e.target as HTMLInputElement).value;
};

// 点击输入框
const handleInputClick = () => {
  if (!dropdownOpen.value) {
    searchText.value = '';
    dropdownOpen.value = true;
  }
};

// 值变化监听
watch(
  () => props.modelValue,
  (newVal) => {
    const selected = props.gridOptions.data?.find(
      (item) => item.value === newVal,
    );
    selectedValue.value = newVal ?? null;
    displayValue.value = selected?.label || '';
  },
);

// 初始化显示值
const initDisplayValue = () => {
  const selected = props.gridOptions.data?.find(
    (item) => item.value === props.modelValue,
  );
  displayValue.value = selected?.label || '';
};
initDisplayValue();
</script>

<template>
  <a-dropdown
    v-model:open="dropdownOpen"
    :trigger="['click']"
    :get-popup-container="(trigger: HTMLElement) => trigger.parentElement"
  >
    <!-- 触发元素 -->
    <div class="trigger-wrapper">
      <a-input
        v-model:value="displayValue"
        readonly
        :placeholder="placeholder"
        @click="handleInputClick"
      />
    </div>

    <!-- 下拉内容 -->
    <template #overlay>
      <div class="table-dropdown-container">
        <div class="search-box">
          <a-input
            v-model:value="searchText"
            placeholder="输入搜索..."
            allow-clear
            @change="handleSearch"
          />
        </div>
        <Grid />
      </div>
    </template>
  </a-dropdown>
</template>

<style scoped>
.table-dropdown-container {
  width: 500px;
  background: #fff;
  border-radius: 4px;
  box-shadow:
    0 3px 6px -4px rgb(0 0 0 / 12%),
    0 6px 16px 0 rgb(0 0 0 / 8%),
    0 9px 28px 8px rgb(0 0 0 / 5%);
}

.search-box {
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.select-table {
  margin: 8px;
}

.table-cell {
  padding: 12px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.table-cell:hover {
  background: #e6f7ff;
}

.trigger-wrapper {
  cursor: pointer;
}

.arrow {
  transition: transform 0.3s;
}

.arrow-open {
  transform: rotate(180deg);
}

:deep(.ant-table-tbody > tr > td) {
  padding: 0 !important;
}
</style>
