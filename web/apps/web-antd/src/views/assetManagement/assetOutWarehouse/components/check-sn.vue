<script lang="ts" setup>
import { shallowReactive } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { getDeviceResourceTableApi } from '#/api/core/cmdb/asset/device';

const init = () => ({
  sn: '',
  loading: false,
  alertType: '',
  result: '',
});
const scope = shallowReactive(init());
const [Modal, modalApi] = useVbenModal();

modalApi.onConfirm = () => {
  modalApi.close();
};
modalApi.onClosed = () => {
  Object.assign(scope, init());
};

const check = async () => {
  scope.loading = true;
  let res;
  try {
    res = await getDeviceResourceTableApi({
      query: scope.sn,
      page: 1,
      pageSize: 10,
    });
  } finally {
    scope.loading = false;
  }
  if (res && res.total > 0) {
    scope.alertType = 'success';
    scope.result = 'SN存在于系统中.';
  } else {
    scope.alertType = 'error';
    scope.result = 'SN不在系统中.';
  }
};

defineExpose({
  api: modalApi,
});
</script>

<template>
  <Modal class="w-[360px]" title="校验SN">
    <a-input-group compact>
      <a-input v-model:value="scope.sn" style="width: calc(100% - 110px)" />
      <a-button type="primary" :loading="scope.loading" @click="check()">
        开始校验
      </a-button>
    </a-input-group>
    <a-alert
      v-if="!!scope.alertType"
      class="mt-4"
      :message="scope.alertType === 'success' ? '校验通过' : '校验失败'"
      :description="scope.result"
      :type="scope.alertType"
      show-icon
    />
  </Modal>
</template>
