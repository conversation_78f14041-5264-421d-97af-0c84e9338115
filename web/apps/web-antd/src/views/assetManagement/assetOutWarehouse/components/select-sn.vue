<script lang="ts" setup>
import type { VbenFormProps } from '@vben/common-ui';
import type { BasicOption } from '@vben/types';

import type { VxeGridProps } from '#/adapter/vxe-table';

import { shallowRef } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getDeviceResourceTableApi } from '#/api/core/cmdb/asset/device';

const snOptions = shallowRef<BasicOption[]>([]);

let timeout: any;
const fetchSN = async (sn: string) => {
  if (!sn) {
    return;
  }

  if (timeout) {
    clearTimeout(timeout);
    timeout = null;
  }

  timeout = setTimeout(async () => {
    try {
      const res = await getDeviceResourceTableApi({
        page: 1,
        pageSize: 100,
        query: sn,
      });
      snOptions.value = (res.list || []).map((item) => ({
        label: item.sn,
        value: item.sn,
      }));
    } catch (error) {
      console.error('获取SN失败', error);
      snOptions.value = [];
    }
  }, 300);
};

const selectSNForm: VbenFormProps = {
  collapsed: true,
  collapsedRows: 3,
  schema: [
    {
      component: 'AutoComplete',
      componentProps: {
        placeholder: '请选择或输入SN',
        options: snOptions,
        allowClear: true,
        notFoundContent: null,
        filterOption: false,
        onChange: fetchSN,
        onSearch: fetchSN,
      },
      fieldName: 'sn',
      label: 'SN',
    },
  ],
};

const selectSNGrid: VxeGridProps = {
  minHeight: 40,
  columns: [
    { type: 'checkbox', width: 60 },
    { field: 'sn', title: '资产SN' },
    { field: 'assetType', title: '类型' },
    {
      field: 'resource.hostname',
      title: '主机名',
    },
  ],
  data: [],
  proxyConfig: {
    response: {
      result: 'list',
    },
    ajax: {
      query: async ({ page }, form) => {
        try {
          const res = await getDeviceResourceTableApi({
            page: page.currentPage,
            pageSize: page.pageSize,
            query: form.sn,
          });
          return res;
        } catch (error) {
          console.error('获取SN失败', error);
          return { list: [], total: 0 };
        }
      },
    },
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10,
  },
};

const expectRef = shallowRef<{
  payload?: Record<string, any>;
  reject: () => void;
  resolve: (record: Record<string, any>[]) => void;
}>();
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: selectSNForm,
  gridOptions: selectSNGrid,
});
const [Modal, modalApi] = useVbenModal({
  onOpened() {
    gridApi.query();
  },
  onConfirm() {
    const results = gridApi.grid.getCheckboxRecords();
    if (!results) {
      message.warning('请选择SN');
      return;
    }
    expectRef.value!.payload = results;
    modalApi.close();
    expectRef.value!.resolve(results);
  },
});

defineExpose({
  open(): Promise<Record<string, any>[]> {
    modalApi.open();
    return new Promise((resolve, reject) => {
      expectRef.value = {
        payload: void 0,
        resolve,
        reject,
      };
    });
  },
});
</script>

<template>
  <Modal class="w-[800px]" title="选择SN">
    <Grid />
  </Modal>
</template>
