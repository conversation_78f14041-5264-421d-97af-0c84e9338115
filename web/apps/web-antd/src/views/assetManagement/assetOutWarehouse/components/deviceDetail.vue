<script setup lang="ts">
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { OutboundDetail } from '#/api/core/ticket/types';

import { ref, watch } from 'vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getOutboundTicketDetailList } from '#/api/core/ticket/asset-ticket';

const props = defineProps<{
  id: string; // 出库单ID
  outboundType: string; // 出库类型
  stage: string; // 阶段
}>();

const emit = defineEmits<{
  (e: 'data', data: OutboundDetail[]): void;
}>();

const assetTypeMap = {
  switch: '交换机',
  firewall: '防火墙',
  router: '路由器',
  loadBalancer: '负载均衡器',
  other: '其他',
  gpuServer: 'GPU服务器',
  server: '服务器',
  network: '网络设备',
  storage: '存储设备',
};

const templateCategoryMap = {
  standard: '标准',
  custom: '定制',
  high_performance: '高性能',
  network_device: '网络设备',
  gpu: 'GPU',
  Category1: 'Category1',
  other: '其他',
};

// 用于存储加载状态
const isLoaded = ref(false);
// 存储加载的数据
const loadedItems = ref<OutboundDetail[]>([]);

const deviceDetailGrid: VxeGridProps = {
  maxHeight: 400,
  minHeight: 40,
  columns: [
    { field: 'id', title: '编号', width: 50 },
    { field: 'template.templateName', title: '模板名称' },
    { field: 'template.cpuModel', title: 'CPU型号' },
    { field: 'template.memoryCapacity', title: '内存容量(GB)' },
    { field: 'template.gpuModel', title: 'GPU型号' },
    { field: 'template.diskType', title: '存储类型' },
    {
      field: 'template.templateCategory',
      title: '模板类别',
      width: 120,
      formatter: ({ cellValue }) =>
        templateCategoryMap[cellValue as keyof typeof templateCategoryMap] ||
        cellValue,
    },
    {
      field: 'asset_type',
      title: '资产类型',
      width: 140,
      formatter: ({ cellValue }) =>
        assetTypeMap[cellValue as keyof typeof assetTypeMap] || cellValue,
    },
    {
      field: 'device_sn',
      title: '设备SN',
      slots: { default: 'sn' },
    },
  ],
  proxyConfig: {
    response: {
      result: 'items',
      total: 'total',
    },
    ajax: {
      query: async ({ page }) => {
        // 获取并筛选设备类型的数据
        const res = await getOutboundTicketDetailList(
          props.id,
          page.currentPage,
          page.pageSize,
        );
        const deviceItems = res.list.filter(
          (item) => item.outbound_type === 'device',
        );

        // 保存数据并标记已加载
        loadedItems.value = deviceItems;
        isLoaded.value = true;

        return {
          items: deviceItems,
          total: res.total,
        };
      },
    },
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10,
    pageSizes: [10, 20, 50, 100],
  },
  scrollY: {
    enabled: true,
    gt: 0,
  },
  showOverflow: true,
};

const [DeviceDetailGrid, deviceDetailGridApi] = useVbenVxeGrid({
  gridOptions: deviceDetailGrid,
});

const handleData = () => {
  const data = deviceDetailGridApi.grid.getFullData();
  emit('data', data);
};

// 监听数据加载状态
watch(isLoaded, (newVal) => {
  if (newVal) {
    // 数据加载完成后，向父组件发送数据
    setTimeout(() => {
      handleData();
    }, 0);
  }
});
</script>

<template>
  <DeviceDetailGrid>
    <!-- 填写SN -->
    <template #sn="{ row }">
      <a-input
        v-model:value="row.device_sn"
        allow-clear
        @change="handleData"
        :status="row.device_sn ? '' : 'error'"
        v-if="stage === 'asset_approval'"
      />
      <span v-else>{{ row.device_sn }}</span>
    </template>
  </DeviceDetailGrid>
</template>
