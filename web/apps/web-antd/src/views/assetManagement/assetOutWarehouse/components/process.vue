<script setup lang="ts">
import type {
  UploadChangeParam,
  UploadFile,
  UploadProps,
} from 'ant-design-vue';

import type { VbenFormProps } from '#/adapter/form';
import type {
  OutboundDetail,
  OutboundTicketRes,
} from '#/api/core/ticket/types';

import { onMounted, ref } from 'vue';

import { AccessControl } from '@vben/access';
import { IconifyIcon } from '@vben/icons';

import { message, Upload } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  updateOutboundTicketApproval,
  updateOutboundTicketByFile,
  updateOutboundTicketDetails,
} from '#/api/core/ticket/asset-ticket';
import { sleep } from '#/utils/common/util';

import {
  currentStep,
  currentSteps,
  downloadTemplate,
  getTemplate,
  uploadVerifyFiles,
} from '../config';
import PartDetail from './partDetail.vue';

const props = defineProps<{
  id: string;
  onSuccess?: () => void;
  ticket: OutboundTicketRes;
}>();

const emit = defineEmits<{
  (e: 'submitSuccess'): void;
}>();

// 表单配置
const approveForm: VbenFormProps = {
  submitButtonOptions: {
    show: false,
  },
  resetButtonOptions: {
    show: false,
  },
  layout: 'horizontal',
  schema: [
    {
      component: 'Input',
      componentProps: {
        type: 'hidden',
      },
      fieldName: 'status',
      label: '状态',
      formItemClass: 'hidden',
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入备注',
        rows: 3,
      },
      fieldName: 'comments',
      label: '备注',
      rules: 'required',
      dependencies: {
        triggerFields: ['status'],
        show(values) {
          return values.status !== 'rejected' && values.status !== 'completed';
        },
      },
      labelWidth: 50,
    },
    {
      fieldName: 'asset_photo',
      component: 'Input',
      dependencies: {
        triggerFields: ['status'],
        show(values) {
          return (
            (values.status === 'waiting_asset_approval' ||
              values.status === 'waiting_buyer_approval') &&
            ['allocate', 'return_repair', 'sell'].includes(
              props.ticket.outbound_reason,
            )
          );
        },
      },
      labelWidth: 50,
    },
    {
      fieldName: 'verify_file',
      component: 'Input',
      componentProps: {
        type: 'hidden',
      },
      dependencies: {
        triggerFields: ['status'],
        show(values) {
          return values.status !== 'completed' && values.status !== 'rejected';
        },
      },
      labelWidth: 50,
    },
  ],
  wrapperClass: 'grid-cols-1',
};

// 审批处理表单
const [BaseForm, formApi] = useVbenForm(approveForm);

// 详情数据
const outboundDetail = ref<OutboundDetail[]>([]);
const detail = ref({
  outboundKey: 'input', // 表单模式：input-手动，import-导入
  templateFileName: '',
  templateFilePath: '',
  templateType: '',
  file: null as null | UploadFile,
  assetPhoto: [] as UploadFile[],
  verifyFile: [] as UploadFile[],
});
const fileList = ref<UploadProps['fileList']>([]);

// 处理设备详情数据变更
const handleDetailData = (data: OutboundDetail[]) => {
  outboundDetail.value = data;
};

/**
 * 下载模板
 */
const getOutboundTemplate = async () => {
  if (
    props.ticket.status === 'completed' ||
    props.ticket.status === 'rejected'
  ) {
    return;
  }

  try {
    // 使用封装后的函数获取模板
    const { templateType, fileName, filePath } = await getTemplate(
      props.ticket.outbound_type,
      props.ticket.outbound_reason,
      props.id,
    );

    // 更新详情数据
    detail.value.templateType = templateType;
    detail.value.templateFileName = fileName;
    detail.value.templateFilePath = filePath;
    // 下载模板
    downloadTemplate(detail.value.templateFileName);
  } catch (error) {
    console.error('获取模板失败:', error);
  }
};

// 设备图片处理
const onChoosePhoto = (info: UploadChangeParam, type: string) => {
  // 确保文件是图片类型
  if (info.file.type && !/^image\//.test(info.file.type)) {
    // console.warn('文件信息：', info.file);
    message.error('只能上传图片文件！');
    return Upload.LIST_IGNORE;
  }

  switch (type) {
    case 'asset': {
      detail.value.assetPhoto = [...detail.value.assetPhoto, info.file];
      // console.warn('图片信息：', detail.value.assetPhoto);
      formApi.setValues({
        asset_photo: detail.value.assetPhoto,
      });
      break;
    }
    case 'verify': {
      detail.value.verifyFile = [...detail.value.verifyFile, info.file];
      formApi.setValues({
        verify_file: detail.value.verifyFile,
      });
      break;
    }
  }
};

const onChooseFile = (info: UploadChangeParam) => {
  detail.value.file = info.file;
  return info.file;
};

const handleRemove = () => {
  // 清除已上传的文件
  detail.value.file = null;
};

const handleUploadDrop = (e: DragEvent) => {
  e.preventDefault();
  e.stopPropagation();
};

const handleRemovePhoto = (file: UploadFile, type: string) => {
  switch (type) {
    case 'asset': {
      detail.value.assetPhoto = detail.value.assetPhoto.filter(
        (item: UploadFile) => item.uid !== file.uid,
      );
      formApi.setValues({
        asset_photo: detail.value.assetPhoto,
      });
      break;
    }
    case 'verify': {
      detail.value.verifyFile = detail.value.verifyFile.filter(
        (item: UploadFile) => item.uid !== file.uid,
      );
      formApi.setValues({
        verify_file: detail.value.verifyFile,
      });
      break;
    }
  }
};

// 审批处理
const handleTicketApproval = async (val: string) => {
  const { valid } = await formApi.validate();
  if (!valid) {
    return;
  }

  const values = await formApi.getValues();
  const stage = props.ticket.stage;
  const data: Record<string, any> = {};
  let nextStatus = '';

  // 确定下一个状态
  switch (stage) {
    case 'asset_approval': {
      nextStatus =
        val === 'approved' ? 'asset_approval_pass' : 'asset_approval_fail';
      break;
    }
    case 'asset_dest_approval': {
      nextStatus =
        val === 'approved'
          ? 'asset_dest_approval_pass'
          : 'asset_dest_approval_fail';
      break;
    }
    case 'buyer_approval': {
      nextStatus =
        val === 'approved' ? 'buyer_approval_pass' : 'buyer_approval_fail';
      break;
    }
    case 'engineer_approval': {
      nextStatus =
        val === 'approved'
          ? 'engineer_approval_pass'
          : 'engineer_approval_fail';
      break;
    }
    case 'replace_approval': {
      nextStatus =
        val === 'approved' ? 'replace_approval_pass' : 'replace_approval_fail';
      break;
    }
  }

  try {
    // 根据表单模式处理
    if (props.ticket.stage === 'engineer_approval') {
      if (detail.value.verifyFile.length > 0) {
        await uploadVerifyFiles(
          detail.value.verifyFile as any,
          'outbound-ticket-verify',
          Number(props.id),
          '验收单',
        );
      }

      // 更新审批状态
      await updateOutboundTicketApproval(props.id, {
        status: nextStatus,
        comments: values.comments,
        data,
      });
      message.success('操作成功');
      emit('submitSuccess');
      return;
    }

    if (
      [
        'asset_approval_fail',
        'asset_dest_approval_fail',
        'buyer_approval_fail',
        'engineer_approval_fail',
        'replace_approval_fail',
      ].includes(nextStatus)
    ) {
      // 更新审批状态
      await updateOutboundTicketApproval(props.id, {
        status: nextStatus,
        comments: values.comments,
        data,
      });
      emit('submitSuccess');
      return;
    }

    // 其他阶段
    switch (detail.value.outboundKey) {
      case 'import': {
        const file = detail.value.file;
        if (!file) {
          message.warning('请上传文件');
          return;
        }
        const template = detail.value.templateType;
        if (!template) {
          message.warning('获取上传信息失败');
          return;
        }

        // 如果是批准，则先更新SN信息
        if (val === 'approved') {
          await updateOutboundTicketByFile(file as any, template);
        }

        // 上传验收图片
        if (
          (stage === 'asset_approval' || stage === 'buyer_approval') &&
          detail.value.assetPhoto.length > 0
        ) {
          await uploadVerifyFiles(
            detail.value.assetPhoto as any,
            'outbound-ticket-photo',
            Number(props.id),
            '验收设备图片',
          );
        }

        // 更新审批状态
        await updateOutboundTicketApproval(props.id, {
          status: nextStatus,
          comments: values.comments,
          data,
        });

        message.success('操作成功');
        emit('submitSuccess');
        break;
      }

      case 'input': {
        // 获取入库详情信息
        const details = outboundDetail.value.map((detail) => {
          switch (props.ticket.outbound_type) {
            case 'device': {
              // 检查必要字段
              if (val === 'approved') {
                if (stage === 'asset_approval' && !detail.device_sn) {
                  throw new Error('请填写设备SN');
                }
                if (stage === 'replace_approval' && !detail.device_sn) {
                  throw new Error('请填写设备SN');
                }
              }
              break;
            }
            case 'part': {
              // 检查必要字段
              if (val === 'approved') {
                if (stage === 'asset_approval' && !detail.component_sn) {
                  throw new Error('请填写配件SN');
                }
                if (stage === 'replace_approval' && !detail.device_sn) {
                  throw new Error('请填写设备SN');
                }
              }
              break;
            }
            default: {
              break;
            }
          }

          return {
            id: detail.id,
            product_id: detail.product_id,
            pn: detail.product?.pn,
            component_sn: detail.component_sn,
            device_sn: detail.device_sn,
          };
        });

        // 如果是批准，则先更新SN信息
        if (val === 'approved') {
          await updateOutboundTicketDetails(details);
        }

        // 上传验收图片
        if (detail.value.assetPhoto.length > 0 && val === 'approved') {
          await uploadVerifyFiles(
            detail.value.assetPhoto as any,
            'outbound-ticket-photo',
            Number(props.id),
            '验收设备图片',
          );
        }

        if (detail.value.verifyFile.length > 0 && val === 'approved') {
          await uploadVerifyFiles(
            detail.value.verifyFile as any,
            'outbound-ticket-verify',
            Number(props.id),
            '验收单',
          );
        }

        // 更新审批状态
        await updateOutboundTicketApproval(props.id, {
          status: nextStatus,
          comments: values.comments,
          data,
        });

        message.success('操作成功');
        await formApi.resetForm();
        await sleep(200);
        emit('submitSuccess');
        break;
      }
    }
  } catch (error) {
    message.error(`操作失败: ${(error as Error).message}`);
  }
};

// 判断是否需要输入SN
const shouldInputSN = (ticket: OutboundTicketRes) => {
  return ['asset_approval', 'buyer_approval', 'replace_approval'].includes(
    ticket.stage,
  );
};

onMounted(async () => {
  formApi.setFieldValue('status', props.ticket.status);
  // 使用封装后的函数获取模板
  const { templateType, fileName, filePath } = await getTemplate(
    props.ticket.outbound_type,
    props.ticket.outbound_reason,
    props.id,
  );

  // 更新详情数据
  detail.value.templateType = templateType;
  detail.value.templateFileName = fileName;
  detail.value.templateFilePath = filePath;
});
</script>

<template>
  <div
    class="mb-4 rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800"
  >
    <a-typography-title :level="5" class="m-0 text-gray-800 dark:text-gray-200">
      处理进度
    </a-typography-title>
    <a-steps
      class="p-1"
      :current="currentStep(props.ticket.status, props.ticket.outbound_reason)"
      :items="currentSteps(props.ticket.status, props.ticket.outbound_reason)"
    />

    <template v-if="props.ticket.status !== 'cancelled'">
      <!-- SN录入区域 -->
      <template v-if="shouldInputSN(props.ticket)">
        <a-tabs v-model:active-key="detail.outboundKey" class="my-3">
          <a-tab-pane key="input" tab="手动录入SN">
            <!-- 根据出库类型显示不同的详情组件 -->
            <!-- <template v-if="props.ticket.outbound_type === 'device'">
              <DeviceDetail
                :id="props.id"
                :outbound-type="props.ticket.outbound_type"
                :stage="props.ticket.stage"
                @data="handleDetailData"
              />
            </template> -->
            <PartDetail
              :id="props.id"
              :outbound-reason="props.ticket.outbound_reason"
              :outbound-type="props.ticket.outbound_type"
              :stage="props.ticket.stage"
              @data="handleDetailData"
            />
          </a-tab-pane>
          <a-tab-pane key="import" tab="批量录入SN">
            <a-button class="mr-4" type="primary" @click="getOutboundTemplate">
              <template #icon>
                <CloudDownload :size="18" />
              </template>
              下载配件出库模板
            </a-button>
            <a-upload-dragger
              name="file"
              :before-upload="() => false"
              @change="onChooseFile"
              @drop="handleUploadDrop"
              @remove="handleRemove"
              class="mt-2 flex h-40 flex-col items-center justify-center"
            >
              <div class="mb-2 flex items-center justify-center">
                <IconifyIcon
                  icon="file-icons:microsoft-excel"
                  width="40"
                  height="40"
                  style="color: #377818"
                />
              </div>
              <p class="ant-upload-text text-center">点击上传或拖拽文件到此</p>
              <p class="ant-upload-hint text-center">
                请您确认已填写文档，本功能支持.xlsx文件
              </p>
            </a-upload-dragger>
          </a-tab-pane>
        </a-tabs>

        <BaseForm class="mt-4">
          <template
            #asset_photo
            v-if="
              props.ticket.stage === 'asset_approval' ||
              props.ticket.stage === 'buyer_approval'
            "
          >
            <div>
              <a-upload
                :file-list="detail.assetPhoto"
                :before-upload="() => false"
                :disabled="detail.assetPhoto.length >= 3"
                @change="
                  (info: UploadChangeParam) => onChoosePhoto(info, 'asset')
                "
                @drop="handleUploadDrop"
                class="upload-list-inline"
                @remove="(file: UploadFile) => handleRemovePhoto(file, 'asset')"
              >
                <a-button v-if="fileList!.length === 0">
                  <CloudUpload />
                  请上传出入室图片
                </a-button>
              </a-upload>
            </div>
          </template>
          <template #verify_file>
            <div>
              <a-upload
                :file-list="detail.verifyFile"
                :before-upload="() => false"
                :disabled="detail.verifyFile.length >= 3"
                @change="
                  (info: UploadChangeParam) => onChoosePhoto(info, 'verify')
                "
                @drop="handleUploadDrop"
                class="upload-list-inline"
                @remove="
                  (file: UploadFile) => handleRemovePhoto(file, 'verify')
                "
              >
                <a-button v-if="fileList!.length === 0">
                  <CloudUpload />
                  请上传验收单
                </a-button>
              </a-upload>
            </div>
          </template>
        </BaseForm>
      </template>
      <template v-else>
        <!-- 根据出库类型显示不同的详情组件 -->
        <!-- <template v-if="props.ticket.outbound_type === 'device'">
          <DeviceDetail
            :id="props.id"
            :outbound-type="props.ticket.outbound_type"
            :stage="props.ticket.stage"
            @data="handleDetailData"
          />
        </template> -->
        <PartDetail
          :id="props.id"
          :outbound-reason="props.ticket.outbound_reason"
          :outbound-type="props.ticket.outbound_type"
          :stage="props.ticket.stage"
          @data="handleDetailData"
        />
        <BaseForm class="mt-4">
          <template #verify_file>
            <div>
              <a-upload
                :file-list="detail.verifyFile"
                :before-upload="() => false"
                :disabled="detail.verifyFile.length >= 3"
                @change="
                  (info: UploadChangeParam) => onChoosePhoto(info, 'verify')
                "
                @drop="handleUploadDrop"
                class="upload-list-inline"
                @remove="
                  (file: UploadFile) => handleRemovePhoto(file, 'verify')
                "
              >
                <a-button v-if="fileList!.length === 0">
                  <CloudUpload />
                  请上传验收单
                </a-button>
              </a-upload>
            </div>
          </template>
        </BaseForm>
      </template>

      <!-- 审批按钮 -->
      <div class="mt-4 text-center">
        <template v-if="props.ticket.stage === 'engineer_approval'">
          <AccessControl
            :codes="['AssetMgt:AssetOutbound:Engineer']"
            type="code"
          >
            <a-button
              type="primary"
              class="mr-4"
              @click="handleTicketApproval('approved')"
            >
              通过
            </a-button>
            <a-button
              type="primary"
              danger
              @click="handleTicketApproval('rejected')"
            >
              拒绝
            </a-button>
          </AccessControl>
        </template>

        <template
          v-if="
            ['asset_approval', 'buyer_approval'].includes(props.ticket.stage)
          "
        >
          <AccessControl :codes="['AssetMgt:AssetOutbound:Asset']" type="code">
            <a-button
              type="primary"
              class="mr-4"
              @click="handleTicketApproval('approved')"
            >
              通过
            </a-button>
            <a-button
              type="primary"
              danger
              @click="handleTicketApproval('rejected')"
            >
              拒绝
            </a-button>
          </AccessControl>
        </template>

        <template v-if="props.ticket.stage === 'replace_approval'">
          <AccessControl
            :codes="['AssetMgt:AssetOutbound:Replace']"
            type="code"
          >
            <a-button
              type="primary"
              class="mr-4"
              @click="handleTicketApproval('approved')"
            >
              通过
            </a-button>
            <a-button
              type="primary"
              danger
              @click="handleTicketApproval('rejected')"
            >
              拒绝
            </a-button>
          </AccessControl>
        </template>

        <template v-if="props.ticket.stage === 'asset_dest_approval'">
          <AccessControl
            :codes="['AssetMgt:AssetOutbound:DestAsset']"
            type="code"
          >
            <a-button
              type="primary"
              class="mr-4"
              @click="handleTicketApproval('approved')"
            >
              通过
            </a-button>
            <a-button
              type="primary"
              danger
              @click="handleTicketApproval('rejected')"
            >
              拒绝
            </a-button>
          </AccessControl>
        </template>
      </div>
    </template>
  </div>
</template>
