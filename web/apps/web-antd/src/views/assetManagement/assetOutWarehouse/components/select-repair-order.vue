<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';
import type {
  RepairTicket,
  RepairTicketStatus,
  RepairType,
} from '#/api/core/ticket/repair-ticket';

import { shallowRef } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
// import { getRepairTicketTableApi } from '#/api/core/cmdb/asset/inbound';
import { getRepairTicketTableApi } from '#/api/core/ticket/repair-ticket';

import {
  repairTypeMap,
  statusMap,
} from '../../../hardwareMaintenance/hardwareOrder/type';

const selectRepairOrderGrid: VxeGridProps<RepairTicket> = {
  minHeight: 40,
  columns: [
    { type: 'radio', width: 60 },
    { field: 'ticket_no', title: '维修单号' },
    { field: 'status', title: '状态', slots: { default: 'status' } },
    {
      field: 'repair_type',
      title: '维修类型',
      slots: { default: 'repair_type' },
    },
    { field: 'assigned_engineer_name', title: '接单工程师' },
    { field: 'repair_result', title: '维修结果' },
  ],
  data: [],
  proxyConfig: {
    response: {
      result: 'list',
    },
    ajax: {
      query: async ({ page }) => {
        try {
          const res = await getRepairTicketTableApi({
            page: page.currentPage,
            pageSize: page.pageSize,
          });
          return res;
        } catch (error) {
          console.error('获取维修单失败', error);
          return { list: [], total: 0 };
        }
      },
    },
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10,
  },
};

const expectRef = shallowRef<{
  payload?: Record<string, any>;
  reject: () => void;
  resolve: (record: Record<string, any>) => void;
}>();
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: selectRepairOrderGrid,
});
const [Modal, modalApi] = useVbenModal({
  onOpened() {
    gridApi.query();
  },
  onConfirm() {
    const result = gridApi.grid.getRadioRecord();
    if (!result) {
      message.warning('请选择工单');
      return;
    }
    expectRef.value!.payload = result;
    modalApi.close();
    expectRef.value!.resolve(result);
  },
  onClosed() {
    if (!expectRef.value!.payload) {
      expectRef.value!.reject();
    }
  },
});

defineExpose({
  open(): Promise<Record<string, any>> {
    modalApi.open();
    return new Promise((resolve, reject) => {
      expectRef.value = {
        payload: void 0,
        resolve,
        reject,
      };
    });
  },
});
</script>

<template>
  <Modal class="w-[800px]" title="选择硬件维修工单">
    <Grid>
      <!-- 状态插槽 -->
      <template #status="{ row }">
        <a-tag :color="statusMap[row.status as RepairTicketStatus]?.color">
          {{ statusMap[row.status as RepairTicketStatus]?.text || row.status }}
        </a-tag>
      </template>

      <!-- 维修类型插槽 -->
      <template #repair_type="{ row }">
        <a-tag :color="repairTypeMap[row.repair_type as RepairType]?.color">
          {{
            repairTypeMap[row.repair_type as RepairType]?.text ||
            row.repair_type
          }}
        </a-tag>
      </template>
    </Grid>
  </Modal>
</template>
