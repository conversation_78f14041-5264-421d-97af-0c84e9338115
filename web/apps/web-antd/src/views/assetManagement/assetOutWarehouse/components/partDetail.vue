<script setup lang="ts">
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { OutboundDetail } from '#/api/core/ticket/types';

import { nextTick, ref, watch } from 'vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getOutboundTicketDetailList } from '#/api/core/ticket/asset-ticket';

const props = defineProps<{
  id: string; // 出库单ID
  outboundReason: string; // 出库原因
  outboundType: string; // 出库类型
  stage: string; // 阶段
}>();

const emit = defineEmits<{
  (e: 'data', data: OutboundDetail[]): void;
}>();

const isLoaded = ref(false);
const loadedItems = ref<OutboundDetail[]>([]);

const partDetailGrid: VxeGridProps = {
  maxHeight: 400,
  minHeight: 40,
  columns: [
    { field: 'id', title: '编号', width: 50 },
    { field: 'product.material_type', title: '物料类型' },
    { field: 'product.brand', title: '品牌' },
    { field: 'product.model', title: '型号' },
    { field: 'product.spec', title: '规格' },
    { field: 'product.pn', title: '原厂PN' },
    { field: 'product.product_category', title: '产品类别' },
    {
      field: 'component_sn',
      title: '配件SN',
      slots: { default: 'component_sn' },
      visible: props.outboundType === 'part',
    },
    {
      field: 'device_sn',
      title: '设备SN',
      slots: { default: 'device_sn' },
      visible:
        props.outboundReason === 'replacement' ||
        props.outboundType === 'device',
    },
  ],
  proxyConfig: {
    response: {
      result: 'items',
      total: 'total',
    },
    ajax: {
      query: async ({ page }) => {
        const res = await getOutboundTicketDetailList(
          props.id,
          page.currentPage,
          page.pageSize,
        );
        loadedItems.value = res.list;
        isLoaded.value = true;
        return {
          items: res.list,
          total: res.total,
        };
      },
    },
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10,
    pageSizes: [10, 20, 50, 100, 500, 1000],
  },
  scrollY: {
    enabled: true,
    gt: 0,
  },
  showOverflow: true,
};

const [PartDetailGrid, partDetailGridApi] = useVbenVxeGrid({
  gridOptions: partDetailGrid,
});

const handleData = () => {
  const data = partDetailGridApi.grid.getFullData();
  emit('data', data);
};

watch(isLoaded, (newVal) => {
  if (newVal) {
    nextTick(() => {
      handleData();
    });
  }
});
</script>

<template>
  <PartDetailGrid>
    <!-- 配件SN编辑 -->
    <template #component_sn="{ row }">
      <a-input
        v-if="stage === 'asset_approval' || stage === 'buyer_approval'"
        v-model:value="row.component_sn"
        placeholder="请输入SN"
        @change="handleData"
        :status="row.component_sn ? '' : 'error'"
      />
      <span v-else>
        {{ row.component_sn }}
      </span>
    </template>

    <!-- 设备SN编辑（仅当出库原因为更换时显示） -->
    <template #device_sn="{ row }">
      <a-input
        v-if="['replace_approval', 'asset_approval'].includes(stage)"
        v-model:value="row.device_sn"
        placeholder="请输入SN"
        @change="handleData"
        :status="row.device_sn ? '' : 'error'"
      />
      <span v-else>
        {{ row.device_sn }}
      </span>
    </template>
  </PartDetailGrid>
</template>
