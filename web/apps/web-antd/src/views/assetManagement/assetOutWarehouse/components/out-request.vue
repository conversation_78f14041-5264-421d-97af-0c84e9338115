<script setup lang="ts">
import type { SelectOption } from '@vben/types';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type {
  RepairTicketStatus,
  RepairType,
} from '#/api/core/ticket/repair-ticket';

import { computed, shallowReactive, shallowRef, useTemplateRef } from 'vue';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm, z } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getDeviceResourceTableApi } from '#/api/core/cmdb/asset/device';
import { getProjectWarehouseApi } from '#/api/core/cmdb/asset/inbound';
import { getWarehouseListApi } from '#/api/core/cmdb/asset/warehouse';
import { getRegionTableApi } from '#/api/core/cmdb/location/region';
import {
  createOutboundTicketV2,
  startWorkflow,
} from '#/api/core/ticket/asset-ticket';

import {
  repairTypeMap,
  statusMap,
} from '../../../hardwareMaintenance/hardwareOrder/type';
import SelectComponentType from '../../assetStorage/components/select-component-type.vue';
import selectDeviceTypeModule from '../../assetStorage/components/select-device-type.vue';
import {
  btnOutboundReasonTextMap,
  isHuaiLai,
  outboundReasonByType,
  outTypeOptions,
  selectedRepairOrderGrid,
} from '../config';
// import CheckSN from './check-sn.vue';
import SelectRepairOrder from './select-repair-order.vue';
// import SelectSN from './select-sn.vue';

const expectRef = shallowRef<{
  // payload?: Record<string, any>;
  reject: () => void;
  resolve: () => void;
}>();

const initDetail = () => ({
  outboundType: 'part',
  outboundReason: '',
  project: '',
  componentType: '',
  warehouseName: '',
  warehouseId: 0,
});
const detail = shallowReactive(initDetail());
// const checkSN = useTemplateRef('checkSN');

/** 资源申请展示表格 */
const assetTypeGrid: VxeGridProps = {
  minHeight: 40,
  columns: [
    { field: 'product.id', title: 'ID', width: 50 },
    { field: 'product.material_type', title: '物料类型' },
    { field: 'product.brand', title: '品牌' },
    { field: 'product.model', title: '型号' },
    { field: 'product.spec', title: '规格' },
    { field: 'product.pn', title: '原厂PN' },
    { field: 'product.product_category', title: '产品类别' },
    { field: 'available_stock', title: '可用库存' },
    { field: 'amount', title: '数量', slots: { default: 'amount' } },
    { slots: { default: 'action' }, title: '操作' },
  ],
  data: [],
  pagerConfig: {
    enabled: false,
  },
  showOverflow: true,
};

/** 设备类型表格 */
const deviceTypeGrid: VxeGridProps = {
  minHeight: 40,
  columns: [
    { align: 'center', type: 'checkbox', width: 40 },
    { field: 'id', title: 'ID', width: 80 },
    { field: 'templateName', title: '模板名称' },
    { field: 'cpuModel', title: 'CPU型号' },
    { field: 'memoryCapacity', title: '内存容量(GB)' },
    { field: 'gpuModel', title: 'GPU型号' },
    { field: 'diskType', title: '存储类型' },
    {
      field: 'templateCategory',
      title: '模板类别',
      width: 120,
    },
    {
      field: 'amount',
      title: '数量',
      slots: { default: 'amount' },
    },
    { slots: { default: 'action' }, title: '操作' },
  ],
  data: [],
  pagerConfig: {
    enabled: false,
  },
  showOverflow: true,
};

const projectOptions = shallowRef<SelectOption[]>([]);
const sourceWarehouseOptions = shallowRef<{ label: string; value: number }[]>(
  [],
);
const warehouseOptions = shallowRef<{ label: string; value: number }[]>([]);

/** 资产出库申请表单 */
const [BaseForm, formApi] = useVbenForm({
  submitButtonOptions: {
    show: false,
  },
  resetButtonOptions: {
    show: false,
  },
  layout: 'horizontal',
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入出库标题',
        class: 'w-[400px]',
        allowClear: true,
      },
      fieldName: 'outbound_title',
      label: '出库标题',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择项目',
        options: projectOptions,
        onChange: (value: string) => {
          detail.project = value;
        },
        class: 'w-[400px]',
        allowClear: true,
      },
      fieldName: 'project',
      label: '项目',
      rules: 'selectRequired',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择出库类型',
        class: 'w-[400px]',
        options: outTypeOptions,
        onChange: (value: string) => {
          detail.outboundType = value;
          detail.outboundReason = '';
          formApi.setValues({
            outboundType: value,
            outboundReason: '',
          });
        },
        allowClear: true,
      },
      fieldName: 'outboundType',
      label: '出库类型',
      rules: 'selectRequired',
      // formItemClass: 'col-span-2',
      defaultValue: 'part',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择出库原因',
        class: 'w-[400px]',
        onChange: (value: string) => {
          detail.outboundReason = value;
        },
        allowClear: true,
      },
      fieldName: 'outboundReason',
      label: '出库原因',
      rules: 'selectRequired',
      // formItemClass: 'col-span-2',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入故障设备SN',
        class: 'w-[400px]',
        allowClear: true,
      },
      fieldName: 'sn',
      label: '故障设备SN',
      help: '需要维修的服务器SN',
      rules: 'required',
      dependencies: {
        triggerFields: ['project', 'outboundReason'],
        show(values) {
          return (
            values.outboundReason === 'repair' && isHuaiLai(values.project)
          );
        },
      },
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入合同编号',
        class: 'w-[400px]',
        allowClear: true,
      },
      fieldName: 'order_number',
      label: '合同编号',
      rules: 'required',
      dependencies: {
        triggerFields: ['outboundReason'],
        show(values) {
          return values.outboundReason === 'sell';
        },
      },
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入买方信息',
        allowClear: true,
        class: 'w-[400px]',
      },
      fieldName: 'buyer_info',
      label: '买方信息',
      rules: 'required',
      dependencies: {
        triggerFields: ['outboundReason'],
        show(values) {
          return values.outboundReason === 'sell';
        },
      },
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择出库仓库名称',
        options: sourceWarehouseOptions,
        class: 'w-[400px]',
        onChange: (value: string, option: SelectOption) => {
          // console.log(value, option);
          detail.warehouseName = option.label;
          detail.warehouseId = Number(value);
        },
      },
      fieldName: 'source_warehouse_id',
      label: '出库仓库名称',
      rules: 'selectRequired',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择目标仓库名称',
        options: warehouseOptions,
        class: 'w-[400px]',
      },
      fieldName: 'dest_warehouse_id',
      label: '目标仓库名称',
      rules: 'selectRequired',
      dependencies: {
        triggerFields: ['outboundReason'],
        show(values) {
          return values.outboundReason === 'allocate';
        },
      },
    },
    {
      component: 'Input',
      componentProps: {
        type: 'hidden',
      },
      fieldName: 'assets',
      label: '资源申请',
      rules: z.number().min(1, { message: '请添加出库设备' }),
      formItemClass: 'col-span-2',
    },
  ],
  wrapperClass: 'grid-cols-2',
  commonConfig: {
    labelWidth: 150,
  },
});

// const selectedSNGrid: VxeGridProps<RepairTicket> = {
//   minHeight: 40,
//   columns: [
//     { field: 'sn', title: '资产SN' },
//     { field: 'assetType', title: '类型' },
//     {
//       field: 'resource.hostname',
//       title: '主机名',
//     },
//     { slots: { default: 'action' }, title: '操作' },
//   ],
//   data: [],
//   pagerConfig: {
//     enabled: false,
//   },
// };

const selectRepairOrder = useTemplateRef('selectRepairOrder');
// const selectSN = useTemplateRef('selectSN');
const selectComponentType = useTemplateRef('selectComponentType');
// const [BaseForm, formApi] = useVbenForm(requestForm);
const currentOutReasonOptions = computed(() => {
  const type = formApi.form.values.outboundType;
  if (!type) return [];
  const reasons = outboundReasonByType[type] || {};
  return Object.entries(reasons).map(([key, value]) => ({
    value: key,
    label: value,
  }));
});
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: selectedRepairOrderGrid,
});
// const [Grid2, gridApi2] = useVbenVxeGrid({
//   gridOptions: selectedSNGrid,
// });
const [equipmentGrid, equipmentGridApi] = useVbenVxeGrid({
  gridOptions: assetTypeGrid,
});
const [DeviceTypeGrid, deviceTypeGridApi] = useVbenVxeGrid({
  gridOptions: deviceTypeGrid,
});
const [Drawer, drawerApi] = useVbenDrawer({
  confirmText: '提交',
  async onOpened() {
    getRegionTableApi({
      page: 1,
      pageSize: 1000,
    }).then((res) => {
      projectOptions.value = res.list.map((item) => ({
        label: item.name,
        value: item.name,
      }));
    });

    getWarehouseListApi({
      page: 1,
      pageSize: 1000,
    }).then((result) => {
      warehouseOptions.value = result.list.map((item) => ({
        label: item.name,
        value: item.id,
      })) as any;
    });
  },
  onClosed() {
    Object.assign(detail, initDetail());
  },

  // 提交
  async onConfirm() {
    drawerApi.setState({
      confirmLoading: true,
    });
    try {
      const { valid } = await formApi.validate();
      if (!valid) {
        return;
      }
      // 获取表单数据
      const values = await formApi.getValues();
      switch (values.outboundReason) {
        case 'repair': {
          // 如果是 cloud11/cloud23,就检查 SN
          if (isHuaiLai(values.project)) {
            const res = await getDeviceResourceTableApi({
              query: values.sn,
              page: 1,
              pageSize: 1,
            });
            if (!res || res.total === 0) {
              message.error('SN不存在系统中.');
              return;
            }
          }
          break;
        }
        // case 'replacement': {
        //   const result = values.sns.split(/[，,\s;|]+/g);
        //   if (!result || result.length === 0) {
        //     message.error('SN格式有误.');
        //     return;
        //   }
        //   await checkSnExistApi(result.join('\n'));
        //   break;
        // }
      }

      let assets;

      // 校验并获取表格数据
      try {
        assets = equipmentGridApi.grid.getFullData().map((item, i) => {
          const amount = Number.parseInt(item.amount);
          const product_id = item.product_id;
          if (!product_id) {
            const err = new TypeError('请输入规格');
            (err as any).lineno = i;
            throw err;
          }
          if (!amount) {
            const err = new TypeError('请输入正确的数量');
            (err as any).lineno = i;
            throw err;
          }
          return {
            product_id,
            amount,
          };
        });
      } catch (error: any) {
        equipmentGridApi.grid.scrollToRow(error.lineno || 0);
        formApi.form.setFieldError('assets', error.message);
        return;
      }

      // 创建出库单及启动工作流
      // 获取选中的仓库名称
      const DestWarehouse = warehouseOptions.value.find(
        (option) => option.value === values.dest_warehouse_id,
      );
      const dest_warehouse_name = DestWarehouse?.label || '';

      const SourceWarehouse = warehouseOptions.value.find(
        (option) => option.value === values.source_warehouse_id,
      );
      const source_warehouse_name = SourceWarehouse?.label || '';

      let repairTicketID = 0;
      if (values.outboundReason === 'repair' && !isHuaiLai(values.project)) {
        const list = gridApi.grid.getFullData();
        if (list.length > 0) {
          repairTicketID = list[0]?.id || 0;
        }
      }
      try {
        const res = await createOutboundTicketV2({
          project: values.project,
          outbound_title: values.outbound_title,
          outbound_type: values.outboundType,
          outbound_reason: values.outboundReason,
          info: assets as any,
          source_warehouse_id: values.source_warehouse_id,
          source_location: source_warehouse_name,
          dest_warehouse_id: values.dest_warehouse_id,
          order_number: values.order_number,
          buyer_info: values.buyer_info,
          dest_location: dest_warehouse_name,
          repair_ticket_id: repairTicketID,
          device_sn: values.sn,
        });
        await startWorkflow(res);
      } catch (error) {
        message.error(
          `操作失败: ${error instanceof Error ? error.message : String(error)}`,
        );
        throw error; // 允许上层处理错误
      }

      // switch (values.outboundType) {
      //   case 'device': {
      //     // 处理设备出库
      //     try {
      //       // 获取设备类型表格数据
      //       const deviceData = deviceTypeGridApi.grid.getFullData();
      //       if (deviceData.length === 0) {
      //         formApi.form.setFieldError('deviceType', '请添加出库设备');
      //         return;
      //       }

      //       // 校验每条记录的数量
      //       assets = deviceData.map((item, i) => {
      //         const amount = Number.parseInt(item.amount);
      //         if (!amount) {
      //           const err = new TypeError('请输入正确的数量');
      //           (err as any).lineno = i;
      //           throw err;
      //         }
      //         return {
      //           template_id: item.id,
      //           amount,
      //         };
      //       });

      //       // 根据出库原因准备请求参数
      //       const DestWarehouse = warehouseOptions.value.find(
      //         (option) => option.value === values.dest_warehouse_id,
      //       );
      //       const dest_warehouse_name = DestWarehouse?.label || '';

      //       const SourceWarehouse = warehouseOptions.value.find(
      //         (option) => option.value === values.source_warehouse_id,
      //       );
      //       const source_warehouse_name = SourceWarehouse?.label || '';

      //       // 调用创建出库单接口
      //       try {
      //         const res = await createOutboundTicketV2({
      //           project: values.project,
      //           outbound_title: values.outbound_title,
      //           outbound_type: values.outboundType,
      //           outbound_reason: values.outboundReason,
      //           info: assets as any,
      //           source_warehouse_id: values.source_warehouse_id,
      //           source_location: source_warehouse_name,
      //           dest_warehouse_id: values.dest_warehouse_id,
      //           order_number: values.order_number,
      //           buyer_info: values.buyer_info,
      //           dest_location: dest_warehouse_name,
      //           device_sn: values.sn,
      //         });
      //         await startWorkflow(res);
      //       } catch (error) {
      //         message.error(
      //           `操作失败: ${error instanceof Error ? error.message : String(error)}`,
      //         );
      //         throw error;
      //       }
      //     } catch (error: any) {
      //       if (error.lineno !== undefined) {
      //         deviceTypeGridApi.grid.scrollToRow(error.lineno);
      //         formApi.form.setFieldError('deviceType', error.message);
      //       }
      //       return;
      //     }
      //     break;
      //   }
      //   case 'part': {
      //     switch (values.outboundReason) {
      //       // 改配和调拨调用新接口
      //       case 'allocate':
      //       case 'repair':
      //       case 'replacement':
      //       case 'return_repair':
      //       case 'sell': {
      //         // 校验并获取表格数据
      //         try {
      //           assets = equipmentGridApi.grid.getFullData().map((item, i) => {
      //             const amount = Number.parseInt(item.amount);
      //             const product_id = item.id;
      //             if (!product_id) {
      //               const err = new TypeError('请输入规格');
      //               (err as any).lineno = i;
      //               throw err;
      //             }
      //             if (!amount) {
      //               const err = new TypeError('请输入正确的数量');
      //               (err as any).lineno = i;
      //               throw err;
      //             }
      //             return {
      //               product_id,
      //               amount,
      //             };
      //           });
      //         } catch (error: any) {
      //           equipmentGridApi.grid.scrollToRow(error.lineno || 0);
      //           formApi.form.setFieldError('assets', error.message);
      //           return;
      //         }

      //         // 创建出库单及启动工作流
      //         // 获取选中的仓库名称
      //         const DestWarehouse = warehouseOptions.value.find(
      //           (option) => option.value === values.dest_warehouse_id,
      //         );
      //         const dest_warehouse_name = DestWarehouse?.label || '';

      //         const SourceWarehouse = warehouseOptions.value.find(
      //           (option) => option.value === values.source_warehouse_id,
      //         );
      //         const source_warehouse_name = SourceWarehouse?.label || '';

      //         let repairTicketID = 0;
      //         if (
      //           values.outboundReason === 'repair' &&
      //           !isHuaiLai(values.project)
      //         ) {
      //           const list = gridApi.grid.getFullData();
      //           if (list.length > 0) {
      //             repairTicketID = list[0]?.id || 0;
      //           }
      //         }
      //         try {
      //           const res = await createOutboundTicketV2({
      //             project: values.project,
      //             outbound_title: values.outbound_title,
      //             outbound_type: values.outboundType,
      //             outbound_reason: values.outboundReason,
      //             info: assets as any,
      //             source_warehouse_id: values.source_warehouse_id,
      //             source_location: source_warehouse_name,
      //             dest_warehouse_id: values.dest_warehouse_id,
      //             order_number: values.order_number,
      //             buyer_info: values.buyer_info,
      //             dest_location: dest_warehouse_name,
      //             repair_ticket_id: repairTicketID,
      //             device_sn: values.sn,
      //           });
      //           await startWorkflow(res);
      //         } catch (error) {
      //           message.error(
      //             `操作失败: ${error instanceof Error ? error.message : String(error)}`,
      //           );
      //           throw error; // 允许上层处理错误
      //         }
      //         break;
      //       }
      //       default: {
      //         break;
      //       }
      //     }
      //     break;
      //   }
      //   default: {
      //     // 显示错误提示给用户
      //     message.error('不支持的出库类型，请联系系统管理员');
      //     // 抛出错误便于调试
      //     throw new Error(`Unsupported outbound type: ${values.outboundType}`);
      //   }
      // }

      message.success('提交成功');
      drawerApi.close();
    } finally {
      drawerApi.setState({
        confirmLoading: false,
      });
    }
    expectRef.value!.resolve();
  },
});

const changeOutReason = (value: string) => {
  formApi.setFieldValue('outboundReason', value);
  detail.outboundReason = value;
  detail.project = formApi.form.values.project;
  switch (value) {
    case 'repair': {
      if (!isHuaiLai(detail.project)) {
        selectRepairOrder.value!.open().then((res: Record<string, any>) => {
          gridApi.grid.reloadData([res]);
          formApi.form.setFieldError('outboundReason', '');
        });
      }
      break;
    }
    // case 'replacement': {
    //   selectSN.value!.open().then((res: Record<string, any>[]) => {
    //     gridApi2.grid.reloadData(res);
    //   });
    //   break;
    // }
  }
};

const addEquipment = async () => {
  if (!detail.warehouseId) {
    message.error('请选择项目-仓库');
    return;
  }
  if (detail.outboundType === '' || detail.outboundType === '请选择入库类型') {
    message.error('请选择出库类型');
    return;
  }
  const api = selectComponentType.value!;

  // 获取当前表格数据
  const currentData = equipmentGridApi.grid.getFullData();

  // 获取当前表格所有数据的ID，用于避免重复添加
  const existingIds = currentData.map((item) => item.id);

  // 创建一个映射存储现有数据的数量
  const existingDataMap = new Map();
  currentData.forEach((item) => {
    existingDataMap.set(item.id, {
      amount: item.amount,
    });
  });

  api
    .open({
      keys: existingIds,
    })
    .then((records: any[]) => {
      // 处理所有选择的记录
      const processedRecords = records.map((item) => {
        try {
          // 首先尝试使用structuredClone
          const newItem = structuredClone(item);

          // 对于新购入库，保留已填写的数量和单价
          if (existingDataMap.has(item.id)) {
            const existingData = existingDataMap.get(item.id);
            newItem.amount = existingData.amount;
          }

          return newItem;
        } catch {
          // 如果structuredClone失败，回退到手动创建对象
          const newItem = {
            ...item,
            product: item.product ? { ...item.product } : undefined,
          };

          // 对于新购入库，保留已填写的数量和单价
          if (existingDataMap.has(item.id)) {
            const existingData = existingDataMap.get(item.id);
            newItem.amount = existingData.amount;
          }

          return newItem;
        }
      });

      // 构建合并后的数据：保留已有数据，并添加新选择的数据
      const existingMap = new Map(currentData.map((item) => [item.id, item]));
      const mergedData = [...currentData];

      // 更新已存在的记录并添加新记录
      processedRecords.forEach((item) => {
        if (existingMap.has(item.id)) {
          // 更新已存在的记录
          const index = mergedData.findIndex((record) => record.id === item.id);
          if (index !== -1) {
            mergedData[index] = item;
          }
        } else {
          // 添加新记录
          mergedData.push(item);
        }
      });

      equipmentGridApi.grid.reloadData(mergedData);
      formApi.setFieldValue('assets', mergedData.length);
    });
};

/** 删除设备 */
const deleteEquipment = (row: any) => {
  equipmentGridApi.grid.remove(row);
  const len = equipmentGridApi.grid.getFullData().length;
  formApi.setFieldValue('assets', len);
};

// 删除SN
// const deleteSN = (row: any) => {
//   gridApi2.grid.remove(row);
// };

defineExpose({
  open(): Promise<void> {
    drawerApi.open();
    return new Promise((resolve, reject) => {
      expectRef.value = {
        // payload: void 0,
        resolve,
        reject,
      };
    });
  },
});

// 设备类型选择模态框
const [deviceTypeComp, deviceTypeApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: selectDeviceTypeModule,
  onOpenChange: (open) => {
    // 只有在模态框关闭时才处理数据
    if (!open) {
      const data = deviceTypeApi.getData();

      // 如果用户确认了选择
      if (data && data.confirmed) {
        const records = data.selectedRecords || [];

        // 获取当前表格数据
        const gridData = deviceTypeGridApi.grid.getFullData();
        const existingIds = new Set(gridData.map((item) => item.id));

        // 过滤出尚未在表格中的新记录
        const newRecords = records.filter(
          (item: { id: number | string }) => !existingIds.has(item.id),
        );

        // 为新记录添加默认的amount值
        const processedNewRecords = newRecords.map((item: any) => ({
          ...item,
          amount: 1, // 默认数量为1
        }));

        // 构建合并后的数据：保留已有数据，并添加新选择的数据
        const mergedData = [...gridData, ...processedNewRecords];

        // 更新表格数据
        deviceTypeGridApi.grid.reloadData(mergedData);

        // 确保表格更新后立即刷新视图
        setTimeout(() => {
          deviceTypeGridApi.grid.refreshScroll();
          deviceTypeGridApi.grid.recalculate();
        }, 10);

        // 更新表单字段值
        formApi.form.setFieldValue('deviceType', mergedData.length);
      }
    }
  },
});

// 打开设备类型选择模态框
function openDeviceModal() {
  // 获取当前表格所有数据的ID，用于避免重复添加
  const gridData = deviceTypeGridApi.grid.getFullData();
  const existingIds = gridData.map((item) => item.id);

  // 设置选中的键值
  deviceTypeApi.setData({
    selectedKeys: existingIds,
  });

  // 打开模态框
  deviceTypeApi.open();
}

// 删除设备类型
const deleteDeviceType = (row: any) => {
  deviceTypeGridApi.grid.remove(row);
  const len = deviceTypeGridApi.grid.getFullData().length;
  formApi.form.setFieldValue('deviceType', len || 0);

  // 刷新表格确保视图更新
  setTimeout(() => {
    deviceTypeGridApi.grid.refreshScroll();
    deviceTypeGridApi.grid.recalculate();
  }, 10);
};
</script>

<template>
  <div>
    <Drawer class="w-full" title="资产出库申请">
      <Page class="w-full">
        <BaseForm>
          <!-- 项目选择器 -->
          <template #project>
            <a-select
              placeholder="请选择项目"
              class="w-2/3"
              :options="projectOptions"
              @change="
                (value: string) => {
                  formApi.form.setFieldValue('project', value);
                  // 清空仓库选择
                  formApi.form.setFieldValue('source_warehouse_id', undefined);

                  // 获取项目关联的仓库
                  if (value) {
                    getProjectWarehouseApi(value).then((result) => {
                      if (result) {
                        // 将API返回的仓库数据转换为选项格式
                        console.warn('result', result);
                        const warehouseOptionsV2 = Object.entries(result).map(
                          ([name, id]) => ({
                            label: name,
                            value: id,
                          }),
                        );
                        sourceWarehouseOptions = warehouseOptionsV2;
                      } else {
                        sourceWarehouseOptions = [];
                      }
                    });
                  } else {
                    // 如果未选择项目，清空仓库选项
                    sourceWarehouseOptions = [];
                  }
                }
              "
            />
          </template>
          <template #outboundReason>
            <div class="flex w-full flex-col">
              <a-input-group compact>
                <a-select
                  :value="detail?.outboundReason"
                  placeholder="请选择出库原因"
                  class="w-[400px]"
                  @change="changeOutReason"
                  :options="currentOutReasonOptions"
                />
                <a-button
                  v-if="
                    !!btnOutboundReasonTextMap[detail.outboundReason] &&
                    !isHuaiLai(detail.project)
                  "
                  type="primary"
                  @click="changeOutReason(detail.outboundReason)"
                >
                  <!-- <template #icon>
                    <CirclePlus class="ml-1 size-3 cursor-pointer" />
                  </template> -->
                  {{ btnOutboundReasonTextMap[detail.outboundReason] }}
                </a-button>
              </a-input-group>
              <Grid
                class="grid"
                v-if="
                  detail.outboundReason === 'repair' &&
                  !isHuaiLai(detail.project)
                "
              >
                <!-- 状态插槽 -->
                <template #status="{ row }">
                  <a-tag
                    :color="statusMap[row.status as RepairTicketStatus]?.color"
                  >
                    {{
                      statusMap[row.status as RepairTicketStatus]?.text ||
                      row.status
                    }}
                  </a-tag>
                </template>

                <!-- 维修类型插槽 -->
                <template #repair_type="{ row }">
                  <a-tag
                    :color="repairTypeMap[row.repair_type as RepairType]?.color"
                  >
                    {{
                      repairTypeMap[row.repair_type as RepairType]?.text ||
                      row.repair_type
                    }}
                  </a-tag>
                </template>
              </Grid>
              <!-- <Grid2
                v-if="detail.outboundReason === 'replacement'"
                class="mt-1 grid"
              >
                <template #action="{ row }">
                  <a-button type="link" @click="deleteSN(row)"> 删除 </a-button>
                </template>
              </Grid2> -->
            </div>
          </template>
          <template #assets>
            <div class="flex w-full flex-col">
              <a-button
                text
                type="primary"
                class="w-fit"
                @click="addEquipment()"
              >
                添加出库设备
                <!-- <template #icon>
                  <CirclePlus class="ml-1 size-3 cursor-pointer" />
                </template> -->
              </a-button>
              <equipmentGrid class="grid">
                <template #amount="{ row }">
                  <a-input-number
                    v-model:value="row.amount"
                    placeholder="请输入数量"
                  />
                </template>
                <template #action="{ row }">
                  <a-button type="link" @click="deleteEquipment(row)">
                    删除
                  </a-button>
                </template>
              </equipmentGrid>
            </div>
          </template>
          <template #deviceType>
            <div class="flex w-full flex-col">
              <a-button
                text
                type="primary"
                class="w-fit"
                @click="openDeviceModal()"
              >
                添加出库设备
                <!-- <template #icon>
                  <CirclePlus class="ml-1 size-3 cursor-pointer" />
                </template> -->
              </a-button>
              <div class="asset-grid">
                <DeviceTypeGrid>
                  <template #amount="{ row }">
                    <a-input-number
                      v-model:value="row.amount"
                      placeholder="请输入数量"
                    />
                  </template>
                  <template #action="{ row }">
                    <a-button type="link" @click="deleteDeviceType(row)">
                      删除
                    </a-button>
                  </template>
                </DeviceTypeGrid>
              </div>
            </div>
          </template>
        </BaseForm>
      </Page>
    </Drawer>

    <SelectRepairOrder ref="selectRepairOrder" />
    <!-- <SelectSN ref="selectSN" /> -->
    <SelectComponentType
      ref="selectComponentType"
      title="选择要出库的设备"
      operation-type="outbound"
      :warehouse-id="detail.warehouseId"
      :view-mode="detail.outboundType === 'part' ? 'part' : 'device'"
    />
    <!-- <CheckSN ref="checkSN" /> -->
    <deviceTypeComp />
  </div>
</template>

<style>
.grid {
  margin: 0 -0.5rem -0.5rem;
}
</style>
