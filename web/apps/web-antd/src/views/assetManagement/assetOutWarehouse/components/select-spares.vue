<script lang="ts" setup>
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { Product } from '#/api/core/cmdb/product/product';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getSpareListApi } from '#/api/core/cmdb/asset/spare';
import { getWarehouseListApi } from '#/api/core/cmdb/asset/warehouse';
import {
  getBrandsApi,
  getMaterialTypesApi,
  getProductCategoriesApi,
  getProductListApi,
} from '#/api/core/cmdb/product/product';

import { assetStatusMap, hardwareStatusMap, sourceTypeMap } from '../config';

const emit = defineEmits(['selected']);

const [Modal, modalApi] = useVbenModal();

// 选项列表
const typeOptions = ref<{ label: string; value: string }[]>([]);
const assetStatusOptions = ref<{ label: string; value: string }[]>([]);
const hardwareStatusOptions = ref<{ label: string; value: string }[]>([]);
const sourceTypeOptions = ref<{ label: string; value: string }[]>([]);
const materialTypeOptions = ref<{ label: string; value: string }[]>([]);
const productCategoryOptions = ref<{ label: string; value: string }[]>([]);
const brandOptions = ref<{ label: string; value: string }[]>([]);
const specOptions = ref<{ label: string; value: string }[]>([]);
const specOptionsLoading = ref(false);
// 存放位置选项
const locationOptions = ref<{ label: string; value: string; warehouse: any }[]>(
  [],
);
const locationOptionsLoading = ref(false);

/** 选择SN查询条件 */
const selectSnForm: VbenFormProps = {
  collapsed: true,
  collapsedRows: 2,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入SN' },
      fieldName: 'sn',
      label: 'SN',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择或输入类型',
        options: typeOptions,
        allowClear: true,
        showSearch: true,
        mode: 'tags',
        filterOption: (input: string, option: any) =>
          option.label.toLowerCase().includes(input.toLowerCase()),
      },
      fieldName: 'type',
      label: '类型',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择来源类型',
        options: sourceTypeOptions,
        allowClear: true,
      },
      fieldName: 'source_type',
      label: '来源类型',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择资产状态',
        options: assetStatusOptions,
        allowClear: true,
      },
      fieldName: 'asset_status',
      label: '资产状态',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择硬件状态',
        options: hardwareStatusOptions,
        allowClear: true,
      },
      fieldName: 'hardware_status',
      label: '硬件状态',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择规格',
        options: specOptions,
        allowClear: true,
        showSearch: true,
        filterOption: (input: string, option: any) =>
          option.label.toLowerCase().includes(input.toLowerCase()),
        loading: specOptionsLoading,
      },
      fieldName: 'spec',
      label: '规格',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择仓库',
        options: locationOptions,
        allowClear: true,
        showSearch: true,
        filterOption: (input: string, option: any) =>
          option.label.toLowerCase().includes(input.toLowerCase()),
        // onChange: handleLocationChange,
      },
      fieldName: 'warehouse_id',
      label: '选择仓库',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: true,
  wrapperClass: 'grid-cols-2 grid',
};

const selectSNGrid: VxeGridProps = {
  minHeight: 40,
  columns: [
    { type: 'radio', width: 60 },
    { field: 'sn', title: 'SN' },
    { field: 'product.material_type', title: '类型' },
    { field: 'product.brand', title: '品牌', width: 100 },
    { field: 'product.spec', title: '规格', width: 260 },
    {
      field: 'warehouse.name',
      title: '仓库名称',
    },
  ],
  data: [],
  rowConfig: {
    keyField: 'id',
    isHover: true,
  },
  radioConfig: {
    highlight: true,
    reserve: true,
  },
  proxyConfig: {
    response: {
      result: 'list',
    },
    ajax: {
      query: async ({ page }, form) => {
        const data = modalApi.getData();
        try {
          const res = await getSpareListApi({
            page: page.currentPage,
            pageSize: page.pageSize,
            sn: form.sn,
            pn: data.pn as any,
            warehouse_id: form.warehouse_id,
            spec: form.spec,
            hardware_status: form.hardware_status,
            asset_status: form.asset_status,
            source_type: form.source_type,
            type: form.type,
          });
          return res;
        } catch (error) {
          console.error('获取SN失败', error);
          return { list: [], total: 0 };
        }
      },
    },
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: selectSnForm,
  gridOptions: selectSNGrid,
});

// 加载规格选项
async function loadSpecOptions() {
  try {
    specOptionsLoading.value = true;

    // 调用API获取产品列表
    const res = await getProductListApi({
      page: 1,
      pageSize: 1000,
    });

    // 处理响应数据
    if (res && Array.isArray(res.list)) {
      // 收集所有不重复的规格
      const uniqueSpecs = new Set<string>();

      res.list.forEach((item: Product) => {
        if (
          item.spec &&
          typeof item.spec === 'string' &&
          item.spec.trim() !== ''
        ) {
          uniqueSpecs.add(item.spec.trim());
        }
      });

      // 转换为选项格式
      specOptions.value = [...uniqueSpecs].map((spec: string) => ({
        label: spec,
        value: spec,
      }));

      // 按照规格名称排序
      specOptions.value.sort((a, b) => a.label.localeCompare(b.label));
    } else {
      console.error('加载规格选项失败，响应数据格式不正确:', res);
      message.error('加载规格选项失败，请刷新页面重试');
      specOptions.value = [];
    }
  } catch (error) {
    console.error('加载规格选项异常:', error);
    message.error('加载规格选项出错，请刷新页面重试');
    specOptions.value = [];
  } finally {
    specOptionsLoading.value = false;
  }
}

// 加载存放位置选项
async function loadLocationOptions() {
  try {
    locationOptionsLoading.value = true;

    // 调用API获取仓库列表
    const res = await getWarehouseListApi({
      page: 1,
      pageSize: 100,
    });

    if (res && Array.isArray(res.list)) {
      // 转换为选项格式 - 将仓库ID保存在value字段，同时保存warehouse信息用于后续获取
      locationOptions.value = res.list.map((item: any) => ({
        label: `${item.name} [${item.code}]${item.room ? ` - ${item.room.name}` : ''}`,
        value: String(item.ID || item.id), // 兼容大写ID和小写id
        warehouse: item, // 保存整个仓库对象以便后续使用
      }));
    } else {
      console.error('加载存放位置选项失败，响应数据格式不正确:', res);
      message.error('加载存放位置选项失败');
      locationOptions.value = [];
    }
  } catch (error) {
    console.error('加载存放位置选项异常:', error);
    message.error('加载存放位置选项出错');
    locationOptions.value = [];
  } finally {
    locationOptionsLoading.value = false;
  }
}

modalApi.onOpened = async () => {
  gridApi.query();

  // 设置备件类型选项
  typeOptions.value = [
    { label: 'CPU', value: 'CPU' },
    { label: '内存', value: '内存' },
    { label: '硬盘', value: '硬盘' },
    { label: 'SSD', value: 'SSD' },
    { label: '显卡', value: '显卡' },
    { label: '主板', value: '主板' },
    { label: '机箱', value: '机箱' },
    { label: '电源模块', value: '电源模块' },
    { label: '风扇', value: '风扇' },
    { label: '散热器', value: '散热器' },
    { label: '跳线', value: '跳线' },
    { label: 'BMC板', value: 'BMC板' },
    { label: '网卡', value: '网卡' },
    { label: '线缆', value: '线缆' },
    { label: '光模块', value: '光模块' },
    { label: 'raid卡', value: 'raid卡' },
    { label: 'GPU底板', value: 'GPU底板' },
    { label: 'Switch板', value: 'Switch板' },
    { label: '背板', value: '背板' },
  ];

  // 设置资产状态选项
  assetStatusOptions.value = Object.entries(assetStatusMap).map(
    ([value, item]) => ({
      label: item.text,
      value,
    }),
  );

  // 设置硬件状态选项
  hardwareStatusOptions.value = Object.entries(hardwareStatusMap).map(
    ([value, item]) => ({
      label: item.text,
      value,
    }),
  );

  // 设置来源类型选项
  sourceTypeOptions.value = Object.entries(sourceTypeMap).map(
    ([value, item]) => ({
      label: item.text,
      value,
    }),
  );

  try {
    // 获取产品物料类型选项
    const materialTypesRes = await getMaterialTypesApi();
    if (materialTypesRes && Array.isArray(materialTypesRes)) {
      materialTypeOptions.value = materialTypesRes.map((type: string) => ({
        label: type,
        value: type,
      }));
    }

    // 获取产品类别选项
    const productCategoriesRes = await getProductCategoriesApi();
    if (productCategoriesRes && Array.isArray(productCategoriesRes)) {
      productCategoryOptions.value = productCategoriesRes.map(
        (category: string) => ({
          label: category,
          value: category,
        }),
      );
    }

    // 获取品牌选项
    const brandsRes = await getBrandsApi();
    if (brandsRes && Array.isArray(brandsRes)) {
      brandOptions.value = brandsRes.map((brand: string) => ({
        label: brand,
        value: brand,
      }));
    }

    // 获取仓库列表作为存放位置选项
    await loadLocationOptions();

    // 获取规格选项列表
    await loadSpecOptions();
  } catch (error) {
    console.error('获取产品选项数据失败:', error);
    message.error('获取产品选项数据失败');
  }
};

modalApi.onConfirm = () => {
  const result = gridApi.grid.getRadioRecord();
  if (!result) {
    message.warning('请选择一条数据');
    return;
  }
  modalApi.close();
  emit('selected', result);
};

defineExpose({
  api: modalApi,
});
</script>

<template>
  <Modal class="w-[1000px]" title="选择SN">
    <Grid />
  </Modal>
</template>
