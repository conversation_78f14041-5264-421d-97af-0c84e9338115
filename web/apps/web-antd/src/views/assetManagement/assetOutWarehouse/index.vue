<script setup lang="ts">
import type { SelectOption } from '@vben/types';

import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { OutboundTicketRes } from '#/api/core/ticket/types';

import { onMounted, shallowRef, useTemplateRef } from 'vue';
import { useRouter } from 'vue-router';

import { AccessControl } from '@vben/access';
import { Page } from '@vben/common-ui';
import { formatDateTime } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getRegionTableApi } from '#/api/core/cmdb/location/region';
import { listOutboundTicket } from '#/api/core/ticket/asset-ticket';

import OutRequest from './components/out-request.vue';
import {
  outboundFormMap,
  outboundReasons,
  outboundStatusMap,
  outboundTypeOptions,
  outboundTypes,
  outReasonOptions,
} from './config';

const projectOptions = shallowRef<SelectOption[]>([]);

/** 资产出库表格表单配置 */
const assetOutWarehouseForm: VbenFormProps = {
  collapsed: true,
  collapsedRows: 3,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入工单号' },
      fieldName: 'ticketNo',
      label: '工单号',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择项目',
        options: projectOptions,
      },
      fieldName: 'project',
      label: '项目',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择环节',
        options: Object.entries(outboundFormMap).map(([key, value]) => ({
          value: key,
          label: value,
        })),
      },
      fieldName: 'status',
      label: '当前环节',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择出库类型',
        options: outboundTypeOptions,
      },
      fieldName: 'outboundType',
      label: '出库类型',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择出库原因',
        options: outReasonOptions,
      },
      fieldName: 'outboundReason',
      label: '出库原因',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: false,
  submitOnChange: false,
  submitOnEnter: true,
  wrapperClass: 'grid-cols-3 grid',
};

/** 资产出库表格列配置 */
const assetOutWarehouseGrid: VxeGridProps<OutboundTicketRes> = {
  columns: [
    {
      field: 'id',
      title: 'ID',
      width: 50,
    },
    { field: 'ticketNo', title: '工单号' },
    { field: 'outbound_title', title: '工单标题' },
    { field: 'project', title: '项目' },
    {
      field: 'outbound_type',
      title: '出库类型',
      formatter({ cellValue }) {
        return outboundTypes[cellValue] || cellValue;
      },
    },
    {
      field: 'outbound_reason',
      title: '出库原因',
      formatter({ cellValue }) {
        return outboundReasons[cellValue] || cellValue;
      },
    },
    { field: 'reporterName', title: '创建人' },
    { field: 'created_at', title: '创建时间', formatter: 'formatDateTime' },
    {
      field: 'completed_at',
      title: '完成时间',
      formatter({ row }) {
        return ['completed', 'rejected'].includes(row.status)
          ? formatDateTime(row.updated_at)
          : '';
      },
    },
    {
      field: 'status',
      title: '当前环节',
      slots: { default: 'status' },
    },
    {
      title: '操作',
      slots: { default: 'action' },
      width: 80,
    },
  ],
  data: [],
  proxyConfig: {
    response: {
      result: 'list',
    },
    ajax: {
      async query({ page }: any, form: any) {
        const result = await listOutboundTicket({
          page: page.currentPage,
          pageSize: page.pageSize,
          query: form.ticketNo,
          project: form.project,
          stage: form.status,
          outboundType: form.outboundType,
          outboundReason: form.outboundReason,
        });
        return result;
      },
    },
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10,
  },
  sortConfig: {
    multiple: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar-buttons',
    },
    custom: true,
    // export: true,
    // import: true,
    refresh: true,
    zoom: true,
  },
};

const outRequest = useTemplateRef('outRequest');
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: assetOutWarehouseForm,
  gridOptions: assetOutWarehouseGrid,
});
const router = useRouter();
const handleDetail = (row: any) => {
  const route = router.resolve({
    path: `/asset-out-warehouse-detail/${row.id}`,
  });
  window.open(route.href, '_blank');
};
const add = () => {
  outRequest.value!.open().then(() => {
    gridApi.query();
  });
};
const onLoad = async () => {
  getRegionTableApi({
    page: 1,
    pageSize: 1000,
  }).then((res) => {
    projectOptions.value = res.list.map((item) => ({
      label: item.name,
      value: item.name,
    }));
  });
};

onMounted(() => {
  onLoad();
});
</script>

<template>
  <div class="w-full">
    <Page>
      <Grid>
        <template #toolbar-buttons>
          <AccessControl :codes="['AssetMgt:AssetOutbound:Create']" type="code">
            <a-button class="mr-2" type="primary" @click="add()">
              新增
            </a-button>
          </AccessControl>
        </template>
        <template #completed_at="{ row }">
          {{ row.status === 'completed' ? row.updated_at : '-' }}
        </template>
        <template #action="{ row }">
          <a-button type="link" size="small" @click="handleDetail(row)">
            详情
          </a-button>
        </template>
        <template #status="{ row }">
          <a-tag :color="outboundStatusMap[row.status]?.color || 'processing'">
            {{ outboundStatusMap[row.status]?.text || '' }}
          </a-tag>
        </template>
      </Grid>
    </Page>

    <OutRequest ref="outRequest" />
  </div>
</template>
