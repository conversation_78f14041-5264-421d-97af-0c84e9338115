<script setup lang="ts">
import type { SelectOption } from '@vben/types';

import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { InboundReq } from '#/api/core/cmdb/asset/types';

import { computed, reactive, shallowRef, useTemplateRef } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getProjectWarehouseApi } from '#/api/core/cmdb/asset/inbound';
import { getRegionTableApi } from '#/api/core/cmdb/location/region';
import {
  createInboundTicketApiV2,
  startWorkflowV2,
} from '#/api/core/ticket/asset-ticket';

import { inboundReasonByTypes, inboundTypes } from '../config';
import SelectComponentType from './select-component-type.vue';
// 定义一个变量来指向SelectDeviceType组件

const projectOptions = shallowRef<SelectOption[]>([]);
const inboundLocationOptions = shallowRef<{ label: string; value: number }[]>(
  [],
);

// 入库类型选项
const inTypeOptions = computed(() => {
  return Object.entries(inboundTypes).map(([value, label]) => ({
    value,
    label,
  }));
});

const initDetail = () => ({
  newPurchaseKey: 'new',
  dismantledKey: 'new',
  repairedKey: 'new',
  purchaseTemplateFileName: '',
  purchaseTemplateFilePath: '',
  purchaseFile: null,

  dismantledTemplateFileName: '',
  dismantledTemplateFilePath: '',
  dismantledFile: null,

  repairedTemplateFileName: '',
  repairedTemplateFilePath: '',
  repairedFile: null,

  warehouseName: '',
  warehouseId: 0,
  inboundType: '请选择入库类型',
  inboundReason: '',
  operationType: 'inbound',
});

// 模板类别选项 - 根据实际数据调整
// const templateCategoryOptions = [
//   { label: '标准', value: 'standard' },
//   { label: '定制', value: 'custom' },
//   { label: '高性能', value: 'high_performance' },
//   { label: '网络设备', value: 'network_device' },
//   { label: 'GPU', value: 'gpu' },
//   { label: 'Category1', value: 'Category1' },
//   { label: '其他', value: 'other' },
// ];
// // 定义资产类型选项映射
// const assetTypeOptionsMap = reactive({
//   // 网络设备分类下的选项
//   network_device: [
//     { label: '交换机', value: 'switch' },
//     { label: '防火墙', value: 'firewall' },
//     { label: '路由器', value: 'router' },
//     { label: '负载均衡器', value: 'load_balancer' },
//     { label: '其他', value: 'other' },
//   ],
//   // GPU服务器分类下的选项
//   gpu: [{ label: 'GPU服务器', value: 'gpu_server' }],
//   standard: [{ label: '服务器', value: 'server' }],
//   // 默认选项
//   default: [
//     { label: '服务器', value: 'server' },
//     { label: '存储设备', value: 'storage' },
//   ],
// });

const detail = reactive(initDetail());

// 入库原因选项
const currentInReasonOptions = computed(() => {
  const type = detail.inboundType;
  if (!type) return [];
  const reasons = inboundReasonByTypes[type] || {};
  return Object.entries(reasons).map(([key, value]) => ({
    value: key,
    label: value,
  }));
});

/** 资产入库申请表单 */
const requestForm: VbenFormProps = {
  submitButtonOptions: {
    show: false,
  },
  resetButtonOptions: {
    show: false,
  },
  layout: 'horizontal',
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入工单标题',
        class: 'w-2/3',
      },
      fieldName: 'title',
      label: '工单标题',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择项目',
        options: projectOptions,
        class: 'w-2/3',
      },
      fieldName: 'project',
      label: '项目',
      rules: 'selectRequired',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择入库类型',
        options: Object.entries(inboundTypes).map(([value, label]) => ({
          value,
          label,
        })),
        class: 'w-2/3',
      },
      fieldName: 'inboundType',
      label: '入库类型',
      rules: 'selectRequired',
    },
    {
      component: 'Select',
      componentProps: {
        options: [], // 会在计算属性中动态更新
        class: 'w-2/3',
      },
      fieldName: 'inboundReason',
      label: '入库原因',
      rules: 'selectRequired',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择仓库名称',
        options: inboundLocationOptions,
        class: 'w-2/3',
        onChange: (value: string, option: SelectOption) => {
          // console.log(value, option);
          detail.warehouseName = option.label;
          detail.warehouseId = Number(value);
        },
      },
      fieldName: 'warehouse_id',
      label: '仓库名称',
      rules: 'selectRequired',
      // dependencies: {
      //   triggerFields: ['inboundReason'],
      //   rules(values) {
      //     return values.inboundReason === 'new_purchase'
      //       ? 'selectRequired'
      //       : null;
      //   },
      // },
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入采购订单编号',
        class: 'w-2/3',
      },
      fieldName: 'purchaseNo',
      label: '采购订单编号',
      rules: 'required',
      dependencies: {
        triggerFields: ['inboundReason'],
        show(values) {
          return values.inboundReason === 'new_purchase';
        },
      },
    },
    // {
    //   component: 'Input',
    //   fieldName: 'contractNo',
    //   label: '合同编号',
    //   rules: 'required',
    //   dependencies: {
    //     triggerFields: ['inboundReason'],
    //     show(values) {
    //       return values.inboundReason === 'new_purchase';
    //     },
    //   },
    // },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入供应商名称',
        class: 'w-2/3',
      },
      fieldName: 'supplier',
      label: '供应商',
      rules: 'required',
      dependencies: {
        triggerFields: ['inboundReason'],
        show(values) {
          return values.inboundReason === 'new_purchase';
        },
      },
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择预计到达时间',
        class: 'w-2/3',
      },
      fieldName: 'may_arrive_at',
      label: '预计到达时间',
      rules: 'required',
      defaultValue: null,
      dependencies: {
        triggerFields: ['inboundReason'],
        show(values) {
          return values.inboundReason === 'new_purchase';
        },
      },
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入物流信息',
        // class: 'w-2/3',
      },
      fieldName: 'tracking_info',
      label: '物流信息',
      rules: 'required',
      dependencies: {
        triggerFields: ['inboundReason'],
        show(values) {
          return values.inboundReason === 'new_purchase';
        },
      },
      formItemClass: 'col-span-2',
    },
    {
      component: 'Input',
      componentProps: {
        type: 'hidden',
      },
      fieldName: 'assetType',
      label: '关联资产类型',
      rules: 'required',
      // dependencies: {
      //   triggerFields: ['inboundType'],
      //   show(values) {
      //     return values.inboundType === 'part_inbound';
      //   },
      // },
      formItemClass: 'col-span-2',
    },
    // {
    //   component: 'Input',
    //   componentProps: {
    //     type: 'hidden',
    //   },
    //   fieldName: 'deviceType',
    //   label: '关联资产类型',
    //   rules: 'required',
    //   dependencies: {
    //     triggerFields: ['inboundType'],
    //     show(values) {
    //       return values.inboundType === 'device_inbound';
    //     },
    //   },
    //   formItemClass: 'col-span-2',
    // },
    {
      component: 'RadioGroup',
      componentProps: {
        options: [
          {
            label: '需要',
            value: true,
          },
          {
            label: '不需要',
            value: false,
          },
        ],
      },
      defaultValue: false,
      fieldName: 'need_return', // 返厂维修
      label: '返厂维修',
      rules: 'required',
      dependencies: {
        triggerFields: ['inboundReason'],
        show(values) {
          return values.inboundReason === 'dismantled';
        },
      },
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: [
          {
            label: '需要',
            value: true,
          },
          {
            label: '不需要',
            value: false,
          },
        ],
      },
      fieldName: 'validate',
      label: '是否需要工程师进行验证',
      defaultValue: false,
      dependencies: {
        triggerFields: ['inboundReason'],
        show(values) {
          return (
            values.inboundReason === 'new_purchase' ||
            values.inboundReason === 'return_repaired'
          );
        },
      },
    },
  ],
  wrapperClass: 'grid-cols-2',
};

/** 关联资产类型展示表格 */
const assetTypeGrid: VxeGridProps = {
  minHeight: 40,
  columns: [
    { field: 'product.id', title: 'ID', width: 80 },
    { field: 'product.material_type', title: '物料类型' },
    { field: 'product.brand', title: '品牌' },
    { field: 'product.model', title: '型号' },
    { field: 'product.spec', title: '规格' },
    { field: 'product.pn', title: '原厂PN' },
    { field: 'product.product_category', title: '产品类别' },
    {
      field: 'current_stock',
      title: '总库存',
      // titlePrefix: {
      //   content: '显示当前库存中存在的数量，不影响入库操作',
      //   message: '显示当前库存中存在的数量，不影响入库操作',
      // },
    },
    {
      field: 'allocated_stock',
      title: '已使用数量',
      // titlePrefix: {
      //   content: '显示已使用数量中存在的数量，不影响入库操作',
      //   message: '显示已使用数量中存在的数量，不影响入库操作',
      // },
    },
    {
      field: 'amount',
      title: '数量',
      slots: { default: 'amount' },
    },
    { slots: { default: 'action' }, title: '操作' },
  ],
  data: [],
  pagerConfig: {
    enabled: false,
  },
  showOverflow: true,
  rowConfig: { isHover: true },
  // editConfig: {
  //   trigger: 'click',
  //   mode: 'cell',
  // },
};

// const deviceTypeGrid: VxeGridProps = {
//   minHeight: 40,
//   columns: [
//     { field: 'id', title: 'ID', width: 80 },
//     { field: 'templateName', title: '模板名称' },
//     { field: 'cpuModel', title: 'CPU型号' },
//     { field: 'memoryCapacity', title: '内存容量(GB)' },
//     { field: 'gpuModel', title: 'GPU型号' },
//     { field: 'diskType', title: '存储类型' },
//     {
//       field: 'templateCategory',
//       title: '模板类别',
//       width: 120,
//       slots: { default: 'templateCategory' },
//     },
//     {
//       field: 'assetType',
//       title: '资产类型',
//       width: 140,
//       slots: { default: 'assetType' },
//     },
//     {
//       field: 'brand',
//       title: '品牌',
//       slots: { default: 'brand' },
//     },
//     {
//       field: 'model',
//       title: '型号',
//       slots: { default: 'model' },
//     },
//     {
//       field: 'amount',
//       title: '数量',
//       slots: { default: 'amount' },
//     },
//     { slots: { default: 'action' }, title: '操作' },
//   ],
//   data: [],
//   pagerConfig: {
//     enabled: false,
//   },
//   showOverflow: true,
// };

const expectRef = shallowRef<{
  // payload?: Record<string, any>;
  reject: () => void;
  resolve: () => void;
}>();

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: assetTypeGrid,
});
// const [DeviceTypeGrid, deviceTypeGridApi] = useVbenVxeGrid({
//   gridOptions: deviceTypeGrid,
// });
const [BaseForm, formApi] = useVbenForm(requestForm);

const selectComponentType = useTemplateRef('selectComponentType');
const [Drawer, drawerApi] = useVbenDrawer({
  confirmText: '提交',
  onClosed: () => {
    Object.assign(detail, initDetail());
  },
  onOpened: async () => {
    // getWarehouseListApi({
    //   page: 1,
    //   pageSize: 1000,
    // }).then((result) => {
    //   inboundLocationOptions.value = result!.list.map((item) => ({
    //     label: item.name,
    //     value: item.id,
    //   })) as any;
    // });

    getRegionTableApi({
      page: 1,
      pageSize: 1000,
    }).then((res) => {
      projectOptions.value = res.list.map((item) => ({
        label: item.name,
        value: item.name,
      }));
    });
  },
});

// 设备类型选择模态框
// const [deviceTypeComp, deviceTypeApi] = useVbenModal({
//   // 连接抽离的组件
//   connectedComponent: selectDeviceTypeModule,
//   onOpenChange: (open) => {
//     // 只有在模态框关闭时才处理数据
//     if (!open) {
//       const data = deviceTypeApi.getData();
//       console.warn(data);

//       // 如果用户确认了选择
//       if (data && data.confirmed) {
//         const records = data.selectedRecords || [];

//         // 获取当前表格数据
//         const gridData = deviceTypeGridApi.grid.getFullData();
//         const existingIds = new Set(gridData.map((item) => item.id));

//         // 过滤出尚未在表格中的新记录
//         const newRecords = records.filter(
//           (item: { id: number | string }) => !existingIds.has(item.id),
//         );

//         // 为新记录添加默认的amount值
//         const processedNewRecords = newRecords.map((item: any) => ({
//           ...item,
//           amount: 1, // 默认数量为1
//         }));

//         // 构建合并后的数据：保留已有数据，并添加新选择的数据
//         const mergedData = [...gridData, ...processedNewRecords];

//         // 更新表格数据
//         deviceTypeGridApi.grid.reloadData(mergedData);

//         // 确保表格更新后立即刷新视图
//         setTimeout(() => {
//           deviceTypeGridApi.grid.refreshScroll();
//           deviceTypeGridApi.grid.recalculate();
//         }, 10);

//         // 更新表单字段值
//         formApi.form.setFieldValue('deviceType', String(mergedData.length));
//       }
//     }
//   },
// });

// function openDeviceModal() {
//   // 获取当前表格所有数据的ID，用于避免重复添加
//   const gridData = deviceTypeGridApi.grid.getFullData();
//   const existingIds = gridData.map((item) => item.id);

//   // 设置选中的键值
//   deviceTypeApi.setData({
//     selectedKeys: existingIds,
//   });

//   // 打开模态框
//   deviceTypeApi.open();
// }

const addAssetType = () => {
  if (!detail.warehouseId) {
    message.error('请选择项目-仓库');
    return;
  }
  if (detail.inboundType === '' || detail.inboundType === '请选择入库类型') {
    message.error('请选择入库类型');
    return;
  }
  const { inboundReason } = formApi.form.values;
  detail.inboundReason = inboundReason;
  // let gridData;

  // // 如果指定了类型参数，使用该类型
  // const effectiveReason = type || inboundReason;

  // switch (effectiveReason) {
  //   case 'dismantled':
  //   case 'new_purchase': {
  //     gridData = gridApi.grid.getFullData();
  //     break;
  //   }
  //   default: {
  //     return;
  //   }
  // }
  const gridData = gridApi.grid.getFullData();
  const api = selectComponentType.value!;

  // 获取当前表格所有数据的ID，用于避免重复添加
  const existingIds = gridData.map((item) => item.id);
  const keys = existingIds;

  // 创建一个映射存储现有数据的amount和price
  const existingDataMap = new Map();
  if (inboundReason === 'new_purchase' || inboundReason === 'dismantled') {
    gridData.forEach((item) => {
      existingDataMap.set(item.id, {
        amount: item.amount,
        price: item.price,
      });
    });
  }
  api
    .open({
      keys,
    })
    .then((records: any[]) => {
      // 处理所有选择的记录
      const processedRecords = records.map((item) => {
        try {
          // 首先尝试使用structuredClone
          const newItem = structuredClone(item);

          // 对于新购入库，保留已填写的数量和单价
          if (existingDataMap.has(item.id)) {
            const existingData = existingDataMap.get(item.id);
            newItem.amount = existingData.amount;
          }

          return newItem;
        } catch {
          // 如果structuredClone失败，回退到手动创建对象
          const newItem = {
            ...item,
            product: item.product ? { ...item.product } : undefined,
          };

          // 对于新购入库，保留已填写的数量和单价
          if (existingDataMap.has(item.id)) {
            const existingData = existingDataMap.get(item.id);
            newItem.amount = existingData.amount;
          }

          return newItem;
        }
      });

      // 构建合并后的数据：保留已有数据，并添加新选择的数据
      const existingMap = new Map(gridData.map((item) => [item.id, item]));
      const mergedData = [...gridData];

      // 更新已存在的记录并添加新记录
      processedRecords.forEach((item) => {
        if (existingMap.has(item.id)) {
          // 更新已存在的记录
          const index = mergedData.findIndex((record) => record.id === item.id);
          if (index !== -1) {
            mergedData[index] = item;
          }
        } else {
          // 添加新记录
          mergedData.push(item);
        }
      });
      console.warn('mergedData', mergedData);
      gridApi.grid.reloadData(mergedData);
      formApi.form.setFieldValue('assetType', String(mergedData.length));
    });
};

const deleteAsset = (row: any) => {
  gridApi.grid.remove(row);
  const len = gridApi.grid.getFullData().length;
  formApi.form.setFieldValue('assetType', len ? String(len) : '');
};

// 添加设备类型删除函数
// const deleteDeviceType = (row: any) => {
//   deviceTypeGridApi.grid.remove(row);
//   const len = deviceTypeGridApi.grid.getFullData().length;
//   formApi.form.setFieldValue('deviceType', len ? String(len) : '');

//   // 刷新表格确保视图更新
//   setTimeout(() => {
//     deviceTypeGridApi.grid.refreshScroll();
//     deviceTypeGridApi.grid.recalculate();
//   }, 10);
// };

// 提交数据
drawerApi.onConfirm = async () => {
  drawerApi.setState({
    confirmLoading: true,
  });
  // 验证表单
  const valid = await formApi.validate();
  if (!valid.valid) {
    drawerApi.setState({
      confirmLoading: false,
    });
    return;
  }
  // 获取表单值
  const values = await formApi.getValues();
  try {
    const { inboundReason, inboundType } = values;
    const gridData = gridApi.grid.getFullData();
    // 添加数量校验
    gridData.forEach((item, i) => {
      const amount = Number(item.amount);
      if (!amount || amount <= 0) {
        const err = new TypeError('请输入有效的数量（大于0）');
        (err as any).lineno = i;
        throw err;
      }
    });
    const info = gridData.map((item) => ({
      product_id: item.product_id,
      amount: item.amount,
    }));

    const reqParams: InboundReq = {
      project: values.project,
      inbound_title: values.title,
      inbound_type: inboundType,
      inbound_reason: inboundReason,
      warehouse_id: values.warehouse_id,
      // 新购入库信息
      may_arrive_at: values.may_arrive_at?.toISOString() || null,
      tracking_info: values.tracking_info,
      purchase_no: values.purchaseNo,
      info,
      need_return: values.need_return,
      // 是否需要工程师验证
      valid: values.validate,
    };
    const { inbound_no } = await createInboundTicketApiV2(reqParams);
    await startWorkflowV2({
      inbound_no,
      inbound_type: values.inboundType,
      verify: values.validate,
    });

    // switch (inboundType) {
    //   // 新接口
    //   case 'device_inbound': {
    //     const gridData = deviceTypeGridApi.grid.getFullData();
    //     try {
    //       // 添加数量校验
    //       gridData.forEach((item, i) => {
    //         const amount = Number(item.amount);
    //         if (!amount || amount <= 0) {
    //           const err = new TypeError('请输入有效的数量（大于0）');
    //           (err as any).lineno = i;
    //           throw err;
    //         }
    //       });
    //       // 根据入库类型数据校验
    //       switch (inboundReason) {
    //         case 'new_purchase': {
    //           // 添加设备类型数据校验
    //           gridData.forEach((item, i) => {
    //             if (!item.brand || item.brand.trim() === '') {
    //               const err = new TypeError('请输入品牌');
    //               (err as any).lineno = i;
    //               throw err;
    //             }
    //             if (!item.model || item.model.trim() === '') {
    //               const err = new TypeError('请输入型号');
    //               (err as any).lineno = i;
    //               throw err;
    //             }
    //           });
    //           break;
    //         }
    //       }

    //       const info = gridData.map((item) => ({
    //         asset_type: item.assetType,
    //         template_id: item.id,
    //         amount: item.amount,
    //         brand: item.brand || '',
    //         model: item.model || '',
    //       }));

    //       const reqParams: InboundReq = {
    //         project: values.project,
    //         inbound_title: values.title,
    //         inbound_type: inboundType,
    //         inbound_reason: inboundReason,
    //         // 新购入库信息
    //         may_arrive_at: values.may_arrive_at?.toISOString() || null,
    //         tracking_info: values.tracking_info,
    //         purchase_no: values.purchaseNo,
    //         warehouse_id: values.warehouse_id,
    //         info,
    //         // 是否需要工程师验证
    //         valid: values.validate,
    //       };

    //       const { inbound_no } = await createInboundTicketApiV2(reqParams);
    //       await startWorkflowV2({
    //         inbound_no,
    //         inbound_type: values.inboundType,
    //         verify: values.validate,
    //       });
    //     } catch (error: any) {
    //       console.error(error);
    //       message.error('创建入库工单失败');
    //       if (error.lineno !== undefined) {
    //         // 如果是我们的校验错误，滚动到对应行并显示错误
    //         deviceTypeGridApi.grid.scrollToRow(error.lineno);
    //         formApi.form.setFieldError('deviceType', error.message);
    //         drawerApi.setState({
    //           confirmLoading: false,
    //         });
    //         return;
    //       }
    //       drawerApi.setState({
    //         confirmLoading: false,
    //       });
    //       return;
    //     }
    //     message.success('创建成功');
    //     break;
    //   }
    //   case 'part_inbound': {
    //     const gridData = gridApi.grid.getFullData();
    //     // 添加数量校验
    //     gridData.forEach((item, i) => {
    //       const amount = Number(item.amount);
    //       if (!amount || amount <= 0) {
    //         const err = new TypeError('请输入有效的数量（大于0）');
    //         (err as any).lineno = i;
    //         throw err;
    //       }
    //     });
    //     const info = gridData.map((item) => ({
    //       product_id: item.id,
    //       amount: item.amount,
    //     }));

    //     const reqParams: InboundReq = {
    //       project: values.project,
    //       inbound_title: values.title,
    //       inbound_type: inboundType,
    //       inbound_reason: inboundReason,
    //       warehouse_id: values.warehouse_id,
    //       // 新购入库信息
    //       may_arrive_at: values.may_arrive_at?.toISOString() || null,
    //       tracking_info: values.tracking_info,
    //       purchase_no: values.purchaseNo,
    //       info,
    //       // 是否需要工程师验证
    //       valid: values.validate,
    //     };
    //     const { inbound_no } = await createInboundTicketApiV2(reqParams);
    //     await startWorkflowV2({
    //       inbound_no,
    //       inbound_type: values.inboundType,
    //       verify: values.validate,
    //     });

    //     break;
    //   }
    // }

    message.success('提交成功');
    drawerApi.close();
  } finally {
    drawerApi.setState({
      confirmLoading: false,
    });
  }
  expectRef.value!.resolve();
};

// 根据templateCategory获取对应的assetType选项
// const getAssetTypeOptions = (templateCategory: string) => {
//   // 根据templateCategory查找对应的选项组
//   return (
//     assetTypeOptionsMap[templateCategory as keyof typeof assetTypeOptionsMap] ||
//     assetTypeOptionsMap.default
//   );
// };

defineExpose({
  open(): Promise<void> {
    drawerApi.open();
    return new Promise((resolve, reject) => {
      expectRef.value = {
        // payload: void 0,
        resolve,
        reject,
      };
    });
  },
});
</script>

<template>
  <div>
    <Drawer class="w-full" title="资产入库申请">
      <Page class="w-full">
        <BaseForm>
          <!-- 自定义入库类型和原因选择器 -->
          <!-- 项目选择器 -->
          <template #project>
            <a-select
              placeholder="请选择项目"
              class="w-2/3"
              :options="projectOptions"
              @change="
                (value: string) => {
                  formApi.form.setFieldValue('project', value);
                  // 清空仓库选择
                  formApi.form.setFieldValue('warehouse_id', undefined);
                  detail.warehouseName = '';
                  detail.warehouseId = 0;
                  // 获取项目关联的仓库
                  if (value) {
                    getProjectWarehouseApi(value).then((result) => {
                      if (result) {
                        // 将API返回的仓库数据转换为选项格式
                        console.warn('result', result);
                        const warehouseOptions = Object.entries(result).map(
                          ([name, id]) => ({
                            label: name,
                            value: id,
                          }),
                        );
                        console.warn(warehouseOptions);
                        inboundLocationOptions = warehouseOptions;
                      } else {
                        inboundLocationOptions = [];
                      }
                    });
                  } else {
                    // 如果未选择项目，清空仓库选项
                    inboundLocationOptions = [];
                  }
                }
              "
            />
          </template>
          <template #inboundType>
            <a-select
              v-model:value="detail.inboundType"
              placeholder="请选择入库类型"
              class="w-2/3"
              :options="inTypeOptions"
              @change="
                (value: string) => {
                  detail.inboundType = value;
                  detail.inboundReason = '';
                  formApi.setValues({
                    inboundType: value,
                    inboundReason: '',
                  });
                }
              "
              allow-clear
            />
          </template>
          <template #inboundReason>
            <a-select
              v-model:value="detail.inboundReason"
              placeholder="请选择入库原因"
              class="w-2/3"
              :options="currentInReasonOptions"
              @change="
                (value: string) => {
                  detail.inboundReason = value;
                  formApi.form.setFieldValue('inboundReason', value);
                }
              "
              allow-clear
            />
          </template>

          <!-- 配件入库 -->
          <template #assetType>
            <div class="flex w-full flex-col">
              <a-button
                text
                type="primary"
                class="mb-2 w-fit"
                @click="addAssetType()"
              >
                添加
                <!-- <template #icon>
                  <CirclePlus class="ml-1 size-3 cursor-pointer" />
                </template> -->
              </a-button>
              <div class="asset-grid">
                <Grid>
                  <template #amount="{ row }">
                    <a-input-number
                      v-model:value="row.amount"
                      placeholder="请输入数量"
                    />
                  </template>
                  <template #price="{ row }">
                    <a-input
                      v-model:value="row.price"
                      placeholder="请输入价格"
                    />
                  </template>
                  <template #action="{ row }">
                    <a-button type="link" @click="deleteAsset(row)">
                      删除
                    </a-button>
                  </template>
                </Grid>
              </div>
            </div>
          </template>
          <!-- 设备入库 -->
          <!-- <template #deviceType>
            <div class="flex w-full flex-col">
              <a-button
                text
                type="primary"
                class="mb-2 w-fit"
                @click="openDeviceModal()"
              >
                添加
                <template #icon>
                  <CirclePlus class="ml-1 size-3 cursor-pointer" />
                </template>
              </a-button>
              <div class="asset-grid">
                <DeviceTypeGrid>
                  <template #templateCategory="{ row }">
                    <span v-if="row && row.templateCategory">
                      {{
                        templateCategoryOptions.find(
                          (opt) => opt.value === row.templateCategory,
                        )?.label || row.templateCategory
                      }}
                    </span>
                    <span v-else>-</span>
                  </template>
                  <template #assetType="{ row }">
                    <a-select
                      v-model:value="row.assetType"
                      :options="getAssetTypeOptions(row.templateCategory)"
                      placeholder="请选择资产类型"
                    />
                  </template>
                  <template #brand="{ row }">
                    <a-input
                      v-model:value="row.brand"
                      placeholder="请输入品牌"
                      :status="!row.brand ? 'error' : ''"
                    />
                  </template>

                  <template #model="{ row }">
                    <a-input
                      v-model:value="row.model"
                      placeholder="请输入型号"
                      :status="!row.model ? 'error' : ''"
                    />
                  </template>
                  <template #amount="{ row }">
                    <a-input-number
                      v-model:value="row.amount"
                      placeholder="请输入数量"
                      :status="!row.amount || row.amount <= 0 ? 'error' : ''"
                    />
                  </template>
                  <template #action="{ row }">
                    <a-button type="link" @click="deleteDeviceType(row)">
                      删除
                    </a-button>
                  </template>
                </DeviceTypeGrid>
              </div>
            </div>
          </template> -->
        </BaseForm>
      </Page>
    </Drawer>

    <SelectComponentType
      ref="selectComponentType"
      title="选择要入库的资产"
      :key="detail.inboundReason"
      :warehouse-id="detail.warehouseId"
      :operation-type="detail.operationType"
      :operation-reason="detail.inboundReason"
      :view-mode="detail.inboundType === 'part_inbound' ? 'part' : 'device'"
    />
    <!-- <deviceTypeComp /> -->
  </div>
</template>

<style scoped>
.asset-grid {
  margin: -0.5rem;
}
</style>
