<script setup lang="ts">
import { IconUpload } from '@vben/icons';

import { message } from 'ant-design-vue';

defineEmits(['change']);

const customRequest = (options: any) => {
  const { file, onSuccess, onError, onProgress } = options;

  // 显示上传中状态
  if (onProgress) {
    onProgress({ percent: 0 });
  }

  // 使用FileReader创建本地预览
  const reader = new FileReader();
  reader.addEventListener('load', (e) => {
    const result = e.target?.result as string;

    // 创建上传文件对象，但不立即上传
    const uploadFile = {
      uid: file.uid,
      name: file.name,
      status: 'done',
      url: result,
      preview: result,
      originFileObj: file, // 保存原始文件对象，以便后续上传
      needUpload: true, // 标记为需要上传
    };

    // 模拟进度为100%
    if (onProgress) {
      onProgress({ percent: 100 });
    }

    onSuccess(uploadFile);
    message.success('文件已添加，将在提交时上传');
  });

  reader.addEventListener('error', (error) => {
    onError(error);
    message.error('加载文件失败');
  });

  reader.readAsDataURL(file);
};

const beforeUpload = (file: File) => {
  const fileSizeLimited = file.size / 1024 / 1024 < 10;

  if (!fileSizeLimited) {
    message.error('文件大小不能超过 10MB!');
    return false;
  }

  return true;
};
</script>
<template>
  <a-upload
    accept="image/*,.pdf,.doc,.docx,.xml,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    :custom-request="customRequest"
    :before-upload="beforeUpload"
    @change="$emit('change', $event)"
  >
    <a-button type="primary">
      <IconUpload :size="18" />
      上传验收单
    </a-button>
  </a-upload>
</template>
