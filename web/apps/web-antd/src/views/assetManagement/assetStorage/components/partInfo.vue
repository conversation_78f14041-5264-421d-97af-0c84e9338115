<script setup lang="ts">
import type { VxeGridProps } from '#/adapter/vxe-table';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getInboundInfoApi } from '#/api/core/ticket/asset-ticket';

const props = defineProps<{
  no: string;
}>();

/** 详情页入库信息 */
const partInfoGrid: VxeGridProps = {
  maxHeight: 400,
  minHeight: 40,
  columns: [
    { title: '序号', type: 'seq', width: 50 },
    { field: 'product.material_type', title: '物料类型' },
    { field: 'product.brand', title: '品牌' },
    { field: 'product.model', title: '型号' },
    { field: 'product.spec', title: '规格' },
    { field: 'product.pn', title: '原厂PN' },
    { field: 'product.product_category', title: '产品类别' },
    { field: 'amount', title: '数量' },
  ],
  data: [],
  proxyConfig: {
    // response: {
    //   result: 'list',
    // },
    ajax: {
      query: async () => {
        const res = await getInboundInfoApi(props.no);
        return {
          items: res.list,
          total: res.total,
        };
      },
    },
  },
  pagerConfig: {
    enabled: false,
  },
  scrollY: {
    enabled: true,
    gt: 0,
  },
  showOverflow: true,
};

const [PartInfoGrid] = useVbenVxeGrid({
  gridOptions: partInfoGrid,
});
</script>

<template>
  <PartInfoGrid v-if="props.no" />
</template>
