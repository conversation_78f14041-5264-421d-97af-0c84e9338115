<script setup lang="ts">
import type { VxeGridProps } from '#/adapter/vxe-table';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getInboundInfoApi } from '#/api/core/ticket/asset-ticket';

const props = defineProps<{
  no: string; // 入库单号
}>();

const assetTypeMap = {
  switch: '交换机',
  firewall: '防火墙',
  router: '路由器',
  loadBalancer: '负载均衡器',
  other: '其他',
  gpuServer: 'GPU服务器',
  server: '服务器',
  network: '网络设备',
  storage: '存储设备',
};

const templateCategoryMap = {
  standard: '标准',
  custom: '定制',
  high_performance: '高性能',
  network_device: '网络设备',
  gpu: 'GPU',
  Category1: 'Category1',
  other: '其他',
};

const deviceInfoGrid: VxeGridProps = {
  maxHeight: 400,
  minHeight: 40,
  columns: [
    { title: '序号', type: 'seq', width: 50 },
    { field: 'template.templateName', title: '模板名称' },
    { field: 'template.cpuModel', title: 'CPU型号' },
    { field: 'template.memoryCapacity', title: '内存容量(GB)' },
    { field: 'template.gpuModel', title: 'GPU型号' },
    { field: 'template.diskType', title: '存储类型' },
    {
      field: 'template.templateCategory',
      title: '模板类别',
      width: 120,
      formatter: ({ cellValue }) =>
        templateCategoryMap[cellValue as keyof typeof templateCategoryMap] ||
        cellValue,
    },
    {
      field: 'asset_type',
      title: '资产类型',
      width: 140,
      formatter: ({ cellValue }) =>
        assetTypeMap[cellValue as keyof typeof assetTypeMap] || cellValue,
    },
    {
      field: 'brand',
      title: '厂商',
    },
    {
      field: 'model',
      title: '型号',
    },
    {
      field: 'amount',
      title: '数量',
    },
  ],
  proxyConfig: {
    response: {
      result: 'list',
    },
    ajax: {
      query: async () => {
        const res = await getInboundInfoApi(props.no);
        return {
          items: res.list,
          total: res.total,
        };
      },
    },
  },
  pagerConfig: {
    enabled: false,
  },
  scrollY: {
    enabled: true,
    gt: 0,
  },
  showOverflow: true,
};

const [DeviceInfoGrid] = useVbenVxeGrid({
  gridOptions: deviceInfoGrid,
});
</script>

<template>
  <DeviceInfoGrid />
</template>
