<script setup lang="ts">
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { MachineTemplate } from '#/api/core/cmdb/template/template';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getTemplateListApi } from '#/api/core/cmdb/template/template';

// 模板类别选项 - 根据实际数据调整
const templateCategoryOptions = [
  { label: '标准', value: 'standard' },
  { label: '定制', value: 'custom' },
  { label: '高性能', value: 'high_performance' },
  { label: '网络设备', value: 'network_device' },
  { label: 'GPU', value: 'gpu' },
  { label: 'Category1', value: 'Category1' },
  { label: '其他', value: 'other' },
];
// 表格搜索表单
const formOptions: VbenFormProps = {
  collapsed: true,
  collapsedRows: 1,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入模板名称/CPU型号' },
      fieldName: 'query',
      label: '关键词',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择模板类别',
        options: templateCategoryOptions,
        allowClear: true,
      },
      fieldName: 'category',
      label: '模板类别',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: true,
};

// 表格配置
const gridOptions: VxeGridProps<MachineTemplate> = {
  rowConfig: {
    keyField: 'id',
    isHover: true,
  },
  checkboxConfig: {
    highlight: true,
    reserve: true,
  },
  columns: [
    { align: 'center', type: 'checkbox', width: 40 },
    { field: 'id', title: 'ID', width: 80 },
    { field: 'templateName', title: '模板名称' },
    { field: 'cpuModel', title: 'CPU型号' },
    { field: 'memoryCapacity', title: '内存容量(GB)' },
    { field: 'gpuModel', title: 'GPU型号' },
    { field: 'diskType', title: '存储类型' },
    {
      field: 'templateCategory',
      title: '模板类别',
      width: 120,
      slots: { default: 'templateCategory' },
    },
  ],
  keepSource: true,
  // height: '500px',
  pagerConfig: { pageSize: 10, pageSizes: [10, 20, 50, 100] },
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  proxyConfig: {
    response: {
      result: 'list',
    },
    ajax: {
      query: async ({ page }, form) => {
        try {
          const res = await getTemplateListApi({
            page: page.currentPage,
            pageSize: page.pageSize,
            query: form.query,
            category: form.category,
          });
          return res;
        } catch (error) {
          console.error('获取资产类型列表失败', error);
          return { list: [], total: 0 };
        }
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [Modal, modalApi] = useVbenModal({
  // 开启拖拽功能
  draggable: true,
  fullscreenButton: true,
});

modalApi.onOpened = async () => {
  // 可以在这里加载数据，或者设置已选中的行
  const { selectedKeys } = modalApi.getData() || {};
  if (selectedKeys && selectedKeys.length > 0) {
    gridApi.grid.setCheckboxRowKey(selectedKeys, true);
  }
};

modalApi.onConfirm = () => {
  gridApi.query();
  const result = [
    ...gridApi.grid.getCheckboxRecords(),
    ...gridApi.grid.getCheckboxReserveRecords(),
  ];

  if (result.length === 0) {
    message.warning('请选择设备类型');
    return;
  }

  // 设置选中的数据，供外部组件获取
  modalApi.setData({
    selectedRecords: result,
    confirmed: true,
  });
  modalApi.close();
};

modalApi.onClosed = () => {
  // 清空表格数据
  gridApi.grid.clearData();
};
</script>

<template>
  <Modal title="选择设备类型" class="w-[1000px]">
    <Grid>
      <template #templateCategory="{ row }">
        <span v-if="row && row.templateCategory">
          {{
            templateCategoryOptions.find(
              (opt) => opt.value === row.templateCategory,
            )?.label || row.templateCategory
          }}
        </span>
        <span v-else>-</span>
      </template>
    </Grid>
  </Modal>
</template>
