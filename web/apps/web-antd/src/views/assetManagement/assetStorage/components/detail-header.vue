<script setup lang="ts">
import type { InboundTicketRes } from '#/api/core/ticket/types';

import { formatToDateTime } from '#/utils/common/time';

import { inboundReasons, inboundTypes } from '../config';
import PartInfoGrid from './partInfo.vue';

const props = defineProps<{
  ticket: InboundTicketRes;
}>();
</script>

<template>
  <div class="mb-4">
    <a-card
      class="h-full overflow-hidden rounded-lg border border-gray-200 transition-shadow duration-300 hover:shadow-md dark:border-gray-700"
    >
      <template #title>
        <div class="flex items-center font-medium">
          <a-badge status="processing" text="入库申请信息" />
        </div>
      </template>
      <div class="pt-1">
        <a-row :gutter="24" class="mb-4">
          <a-col :span="8">
            <div
              class="flex border-b border-gray-100 py-1 dark:border-gray-700"
            >
              <a-typography-text type="secondary" class="w-[100px] font-medium">
                工单编号
              </a-typography-text>
              <a-typography-text class="text-gray-800 dark:text-gray-200">
                {{ props.ticket.inbound_no }}
              </a-typography-text>
            </div>
          </a-col>
          <a-col :span="8">
            <div
              class="flex border-b border-gray-100 py-1 dark:border-gray-700"
            >
              <a-typography-text type="secondary" class="w-[100px] font-medium">
                入库类型
              </a-typography-text>
              <a-typography-text class="text-gray-800 dark:text-gray-200">
                {{ inboundTypes[props.ticket.inbound_type] }}
              </a-typography-text>
            </div>
          </a-col>
          <a-col :span="8">
            <div
              class="flex border-b border-gray-100 py-1 dark:border-gray-700"
            >
              <a-typography-text type="secondary" class="w-[100px] font-medium">
                入库原因
              </a-typography-text>
              <a-typography-text class="text-gray-800 dark:text-gray-200">
                {{ inboundReasons[props.ticket.inbound_reason] }}
              </a-typography-text>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="flex py-1">
              <a-typography-text type="secondary" class="w-[100px] font-medium">
                创建人
              </a-typography-text>
              <a-typography-text class="text-gray-800 dark:text-gray-200">
                {{ props.ticket.create_by }}
              </a-typography-text>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="flex py-1">
              <a-typography-text type="secondary" class="w-[100px] font-medium">
                入库位置
              </a-typography-text>
              <a-typography-text class="text-gray-800 dark:text-gray-200">
                {{ props.ticket.warehouse.name }}
              </a-typography-text>
            </div>
          </a-col>
          <a-col :span="8" v-if="props.ticket.inbound_reason === 'dismantled'">
            <div class="flex py-1">
              <a-typography-text type="secondary" class="w-[100px] font-medium">
                需要返修
              </a-typography-text>
              <a-typography-text class="text-gray-800 dark:text-gray-200">
                {{ props.ticket.need_return ? '是' : '否' }}
              </a-typography-text>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="flex py-1">
              <a-typography-text type="secondary" class="w-[100px] font-medium">
                创建时间
              </a-typography-text>
              <a-typography-text class="text-gray-800 dark:text-gray-200">
                {{ formatToDateTime(props.ticket.created_at) }}
              </a-typography-text>
            </div>
          </a-col>
          <template v-if="ticket.inbound_reason === 'new_purchase'">
            <a-col :span="8">
              <div class="flex py-1">
                <a-typography-text
                  type="secondary"
                  class="w-[100px] font-medium"
                >
                  采购订单编号
                </a-typography-text>
                <a-typography-text class="text-gray-800 dark:text-gray-200">
                  {{ props.ticket.purchase_no }}
                </a-typography-text>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="flex py-1">
                <a-typography-text
                  type="secondary"
                  class="w-[100px] font-medium"
                >
                  预计到达时间
                </a-typography-text>
                <a-typography-text class="text-gray-800 dark:text-gray-200">
                  {{
                    props.ticket.may_arrive_at
                      ? formatToDateTime(props.ticket.may_arrive_at)
                      : '--'
                  }}
                </a-typography-text>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="flex py-1">
                <a-typography-text
                  type="secondary"
                  class="w-[100px] font-medium"
                >
                  物流信息
                </a-typography-text>
                <a-typography-text class="text-gray-800 dark:text-gray-200">
                  {{ props.ticket.tracking_info }}
                </a-typography-text>
              </div>
            </a-col>
          </template>
          <a-col
            :span="16"
            v-if="
              ['new_purchase', 'return_repaired'].includes(
                props.ticket.inbound_reason,
              )
            "
          >
            <div class="flex py-1">
              <a-typography-text type="secondary" class="w-[150px] font-medium">
                是否需要工程师验证
              </a-typography-text>
              <a-typography-text class="text-gray-800 dark:text-gray-200">
                {{ props.ticket.valid ? '是' : '否' }}
              </a-typography-text>
            </div>
          </a-col>
        </a-row>
        <!-- <DeviceInfoGrid
          v-if="props.ticket"
          :no="props.ticket.inbound_no"
          class="my-4"
        /> -->
        <PartInfoGrid v-if="props.ticket" :no="props.ticket.inbound_no" />
      </div>
    </a-card>
  </div>
</template>
