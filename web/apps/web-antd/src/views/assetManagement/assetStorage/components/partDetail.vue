<script setup lang="ts">
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { InboundDetail, InboundTicketRes } from '#/api/core/ticket/types';

import { nextTick, ref, watch } from 'vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getInboundDetailListApi } from '#/api/core/ticket/asset-ticket';

// 定义组件接收的属性
const props = defineProps<{
  no: string; // 入库工单编号
  ticket: InboundTicketRes; // 入库工单信息对象
}>();

// 定义组件触发的事件
const emits = defineEmits<{
  (e: 'detail', data: InboundDetail[]): void; // 配件明细数据变更时触发
}>();

// 控制数据是否已加载的状态标志
const isLoaded = ref(false);
// 存储加载的配件明细数据
const loadedItems = ref<InboundDetail[]>([]);

// 配件明细表格配置
const partDetailGrid: VxeGridProps = {
  maxHeight: 400,
  minHeight: 40,
  columns: [
    { field: 'id', title: '编号', width: 50 },
    { field: 'product.material_type', title: '物料类型' },
    { field: 'product.brand', title: '品牌' },
    { field: 'product.model', title: '型号' },
    { field: 'product.spec', title: '规格' },
    { field: 'product.pn', title: '原厂PN' },
    { field: 'product.product_category', title: '产品类别' },
    {
      field: 'component_sn',
      title: '配件SN',
      slots: { default: 'component_sn' }, // 使用自定义插槽渲染
      visible: props.ticket.inbound_type === 'part_inbound',
    },
    {
      field: 'device_sn',
      title: '设备SN',
      slots: { default: 'device_sn' }, // 使用自定义插槽渲染
      visible: props.ticket.inbound_type === 'device_inbound',
    },
    {
      field: 'return_repair_type',
      title: '返修类型',
      slots: { default: 'return_repair_type' }, // 使用自定义插槽渲染
      visible: props.ticket.inbound_reason === 'return_repaired', // 仅当为返修入库时显示
    },
    {
      field: 'new_component_sn',
      title: '新配件SN',
      slots: { default: 'new_component_sn' }, // 使用自定义插槽渲染
      visible: props.ticket.inbound_reason === 'return_repaired', // 仅当为返修入库时显示
    },
    {
      field: 'device_sn',
      title: '主设备SN',
      slots: { default: 'device_sn' }, // 使用自定义插槽渲染
      visible: props.ticket.inbound_reason === 'dismantled', // 仅当为拆机配件入库时显示
    },
    {
      field: 'component_state',
      title: '配件状态',
      slots: { default: 'component_state' }, // 使用自定义插槽渲染
      visible:
        props.ticket.inbound_reason === 'return_repaired' ||
        props.ticket.inbound_reason === 'dismantled', // 返修或拆机配件时显示
    },
  ],
  data: [],
  // 代理配置，用于远程数据加载
  proxyConfig: {
    response: {
      result: 'items',
      total: 'total',
    },
    ajax: {
      query: async ({ page }) => {
        // 获取入库单明细数据
        const res = await getInboundDetailListApi(
          props.no,
          page.currentPage,
          page.pageSize,
        );
        res.list.forEach((item) => {
          if (!item.component_state) {
            item.component_state = 'normal';
          }
        });
        loadedItems.value = res.list;
        isLoaded.value = true; // 标记数据已加载
        return {
          items: res.list,
          total: res.total,
        };
      },
    },
  },
  // 分页配置
  pagerConfig: {
    enabled: true,
    pageSize: 10,
    pageSizes: [10, 20, 50, 100, 500, 1000],
  },
  // 垂直滚动配置
  scrollY: {
    enabled: true,
    gt: 0,
  },
  showOverflow: true, // 显示溢出内容
};

const [PartDetailGrid, partDetailGridApi] = useVbenVxeGrid({
  gridOptions: partDetailGrid,
});

// 处理数据变更的函数，将更新后的数据通过事件发送给父组件
const handleData = () => {
  const data: InboundDetail[] = partDetailGridApi.grid.getFullData();
  emits('detail', data);
};

// 配件状态选项配置
const componentStateOptions = [
  { label: '正常', value: 'normal', color: 'green' },
  { label: '故障', value: 'faulty', color: 'red' },
];

// 返修类型选项配置
const returnRepairTypeOptions = [
  { label: '维修', value: 'repair', color: 'green' },
  { label: '换新', value: 'replace', color: 'blue' },
];

// 监听数据加载状态，加载完成后触发数据变更事件
watch(isLoaded, (newVal) => {
  if (newVal) {
    nextTick(() => {
      handleData();
    });
  }
});
</script>

<template>
  <PartDetailGrid>
    <!-- 配件SN列的自定义渲染 -->
    <template #component_sn="{ row }">
      <!-- 审批阶段显示输入框 -->
      <a-input
        v-if="props.ticket.stage === 'asset_approval'"
        v-model:value="row.component_sn"
        :status="row.component_sn ? '' : 'error'"
        @change="handleData"
      />
      <!-- 非审批阶段显示纯文本 -->
      <span v-else>{{ row.component_sn }}</span>
    </template>

    <!-- 主设备SN列的自定义渲染 -->
    <template #device_sn="{ row }">
      <!-- 审批阶段显示输入框 -->
      <a-input
        v-if="props.ticket.stage === 'asset_approval'"
        v-model:value="row.device_sn"
        :status="row.device_sn ? '' : 'error'"
        @change="handleData"
      />
      <!-- 非审批阶段显示纯文本 -->
      <span v-else>{{ row.device_sn }}</span>
    </template>

    <!-- 配件状态列的自定义渲染 -->
    <template #component_state="{ row }">
      <!-- 审批阶段显示下拉选择框 -->
      <a-select
        v-if="props.ticket.stage === 'asset_approval'"
        v-model:value="row.component_state"
        :options="componentStateOptions"
        :status="row.component_state ? '' : 'error'"
        @change="handleData"
      />
      <!-- 非审批阶段显示状态标签 -->
      <span v-else>
        <a-tag
          :color="
            componentStateOptions.find(
              (item) => item.value === row.component_state,
            )?.color
          "
          >{{
            componentStateOptions.find(
              (item) => item.value === row.component_state,
            )?.label
          }}</a-tag
        >
      </span>
    </template>

    <!-- 返修类型列的自定义渲染 -->
    <template #return_repair_type="{ row }">
      <!-- 审批阶段显示下拉选择框 -->
      <a-select
        v-if="props.ticket.stage === 'asset_approval'"
        v-model:value="row.return_repair_type"
        :options="returnRepairTypeOptions"
        :status="row.return_repair_type ? '' : 'error'"
        @change="handleData"
      />
      <!-- 非审批阶段显示状态标签 -->
      <span v-else>
        <a-tag
          :color="
            returnRepairTypeOptions.find(
              (item) => item.value === row.return_repair_type,
            )?.color
          "
          >{{
            returnRepairTypeOptions.find(
              (item) => item.value === row.return_repair_type,
            )?.label
          }}</a-tag
        ></span
      >
    </template>

    <!-- 新配件SN列的自定义渲染 -->
    <template #new_component_sn="{ row }">
      <!-- 条件1: 当处于审批阶段且返修类型为"换新"时，显示输入框 -->
      <a-input
        v-if="
          props.ticket.stage === 'asset_approval' &&
          row.return_repair_type === 'replace'
        "
        v-model:value="row.new_component_sn"
        :status="row.new_component_sn ? '' : 'error'"
        @change="handleData"
      />
      <!-- 条件2: 当返修类型为"换新"但不在审批阶段时，显示值 -->
      <span v-else-if="row.return_repair_type === 'replace'">{{
        row.new_component_sn
      }}</span>
      <!-- 条件3: 不需要换新的情况下，显示占位符 -->
      <span v-else>-</span>
    </template>
  </PartDetailGrid>
</template>
