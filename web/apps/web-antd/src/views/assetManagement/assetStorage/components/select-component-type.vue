<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { Product } from '#/api/core/cmdb/product/product';

import { shallowRef } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getInventoryListApi } from '#/api/core/cmdb/inventory/inventory';
import {
  getBrandsApi,
  getMaterialTypesApi,
  getProductCategoriesApi,
  getProductListApi,
} from '#/api/core/cmdb/product/product';

const props = defineProps<{
  operationReason: string;
  operationType: string;
  title: string;
  viewMode: string;
  warehouseId: number;
}>();

// onMounted(() => {
//   console.warn('mounted props:', props.operationReason);
// });

const expectRef = shallowRef<{
  payload?: Record<string, any>[];
  reject: () => void;
  resolve: (record: Record<string, any>[]) => void;
}>();

const materialTypeOptions = shallowRef<{ label: string; value: string }[]>([]);
const productCategoryOptions = shallowRef<{ label: string; value: string }[]>(
  [],
);
const brandOptions = shallowRef<{ label: string; value: string }[]>([]);
// 规格选项
const specOptions = shallowRef<{ label: string; value: string }[]>([]);
const specOptionsLoading = shallowRef(false);

/** 选择物料查询条件 */
const selectAssetTypeForm: VbenFormProps = {
  collapsed: true,
  collapsedRows: 3,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入PN号码' },
      fieldName: 'pn',
      label: 'PN号码',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择物料类型',
        options: materialTypeOptions,
        allowClear: true,
        showSearch: true,
        mode: 'combobox',
        filterOption: (
          input: string,
          option: { label: string; value: string },
        ) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
      },
      fieldName: 'material_type',
      label: '物料类型',
    },
    // {
    //   component: 'Select',
    //   componentProps: {
    //     placeholder: '请选择产品类别',
    //     options: productCategoryOptions,
    //     allowClear: true,
    //     showSearch: true,
    //     mode: 'combobox',
    //     filterOption: (
    //       input: string,
    //       option: { label: string; value: string },
    //     ) => {
    //       return option.label.toLowerCase().includes(input.toLowerCase());
    //     },
    //   },
    //   fieldName: 'product_category',
    //   label: '产品类别',
    // },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择品牌',
        options: brandOptions,
        allowClear: true,
        showSearch: true,
        mode: 'combobox',
        filterOption: (
          input: string,
          option: { label: string; value: string },
        ) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
      },
      fieldName: 'brand',
      label: '品牌',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择或输入规格',
        options: specOptions,
        showSearch: true,
        mode: 'combobox',
        allowClear: true,
        autoClearSearchValue: false,
        loading: specOptionsLoading,
        filterOption: (
          input: string,
          option: { label: string; value: string },
        ) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
      },
      fieldName: 'spec',
      label: '规格',
    },
    // {
    //   component: 'Input',
    //   componentProps: { placeholder: '请输入型号' },
    //   fieldName: 'model',
    //   label: '型号',
    // },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: false,
  submitOnChange: false,
  submitOnEnter: true,
  // wrapperClass: 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4 grid',
};

/** 选择资产类型表格 */
const selectAssetTypeGrid: VxeGridProps = {
  rowConfig: {
    keyField: 'id',
    isHover: true,
  },
  checkboxConfig: {
    highlight: true,
    reserve: true,
  },
  minHeight: 40,
  columns: [
    { type: 'checkbox', width: 60 },
    { field: 'product.material_type', title: '物料类型' },
    { field: 'product.brand', title: '品牌' },
    { field: 'product.model', title: '型号' },
    { field: 'product.spec', title: '规格' },
    { field: 'product.pn', title: '原厂PN' },
    { field: 'product.product_category', title: '产品类别' },
    { field: 'warehouse', title: '仓库' },
    {
      field: 'available_stock',
      title: '可用库存',
      visible: props.operationType === 'outbound',
    },
    {
      field: 'current_stock',
      title: '当前库存',
      visible:
        props.operationType === 'inbound' &&
        props.operationReason !== 'dismantled',
    },
    {
      field: 'allocated_stock',
      title: '已使用数量',
      visible:
        props.operationType === 'inbound' &&
        props.operationReason === 'dismantled',
    },
  ],
  data: [],
  keepSource: true,
  proxyConfig: {
    response: {
      result: 'list',
    },
    ajax: {
      query: async ({ page }, form) => {
        try {
          const res = await getInventoryListApi({
            page: page.currentPage,
            pageSize: page.pageSize,
            pn: form.pn,
            brand: form.brand,
            spec: form.spec,
            materialType: form.material_type,
            warehouseId: props.warehouseId,
            showAll: props.operationType === 'inbound',
            viewMode: props.viewMode,
          });
          return res;
        } catch (error) {
          console.error('获取资产类型列表失败', error);
        }

        // try {
        //   const res = await getProductListApi({
        //     page: page.currentPage,
        //     pageSize: page.pageSize,
        //     pn: form.pn,
        //     material_type: form.material_type,
        //     product_category: form.product_category,
        //     brand: form.brand,
        //     spec: form.spec,
        //     model: form.model,
        //   });
        //   return res;
        // } catch (error) {
        //   console.error('获取资产类型列表失败', error);
        //   return { list: [], total: 0 };
        // }
      },
    },
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10,
  },
  showOverflow: true,
};

const [Modal, modalApi] = useVbenModal();
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: selectAssetTypeForm,
  gridOptions: selectAssetTypeGrid,
});

// 获取选项数据
async function fetchOptions() {
  try {
    // 获取物料类型
    const materialTypes = await getMaterialTypesApi();
    materialTypeOptions.value = materialTypes.map((type) => ({
      label: type,
      value: type,
    }));

    // 获取产品类别
    const categories = await getProductCategoriesApi();
    productCategoryOptions.value = categories.map((category) => ({
      label: category,
      value: category,
    }));

    // 获取品牌
    const brands = await getBrandsApi();
    brandOptions.value = brands.map((brand) => ({
      label: brand,
      value: brand,
    }));
  } catch (error) {
    console.error('获取选项数据失败:', error);

    // 设置一些默认值以防API失败
    if (materialTypeOptions.value.length === 0) {
      materialTypeOptions.value = [
        { label: 'CPU', value: 'CPU' },
        { label: '内存', value: '内存' },
        { label: '硬盘', value: '硬盘' },
        { label: '显卡', value: '显卡' },
      ];
    }

    if (productCategoryOptions.value.length === 0) {
      productCategoryOptions.value = [
        { label: '服务器', value: '服务器' },
        { label: '存储设备', value: '存储设备' },
        { label: '网络设备', value: '网络设备' },
        { label: '网络设备配件', value: '网络设备配件' },
        { label: '配件', value: '配件' },
        { label: '线缆', value: '线缆' },
        { label: '维保', value: '维保' },
        { label: '其他', value: '其他' },
      ];
    }

    if (brandOptions.value.length === 0) {
      brandOptions.value = [
        { label: 'Intel', value: 'Intel' },
        { label: 'AMD', value: 'AMD' },
        { label: 'Samsung', value: 'Samsung' },
      ];
    }
  }
}

// 加载规格选项 - 从产品列表获取所有规格
async function loadSpecOptions() {
  try {
    specOptionsLoading.value = true;

    // 调用API获取产品列表
    const res = await getProductListApi({
      page: 1,
      pageSize: 1000,
    });

    // 处理响应数据
    if (res && Array.isArray(res.list)) {
      // 收集所有不重复的规格
      const uniqueSpecs = new Set<string>();

      res.list.forEach((item: Product) => {
        if (
          item.spec &&
          typeof item.spec === 'string' &&
          item.spec.trim() !== ''
        ) {
          uniqueSpecs.add(item.spec.trim());
        }
      });

      // 转换为选项格式
      specOptions.value = [...uniqueSpecs].map((spec: string) => ({
        label: spec,
        value: spec,
      }));

      // 按照规格名称排序
      specOptions.value.sort((a, b) => a.label.localeCompare(b.label));
    } else {
      console.error('加载规格选项失败，响应数据格式不正确:', res);
      message.error('加载规格选项失败，请刷新页面重试');
      specOptions.value = [];
    }
  } catch (error) {
    console.error('加载规格选项异常:', error);
    message.error('加载规格选项出错，请刷新页面重试');
    specOptions.value = [];
  } finally {
    specOptionsLoading.value = false;
  }
}

modalApi.onOpened = async () => {
  fetchOptions();
  loadSpecOptions();

  // gridApi.query();

  const { selected } = modalApi.getData() || {};
  if (selected && selected.length > 0) {
    gridApi.grid.setCheckboxRowKey(selected, true);
  }
};
modalApi.onConfirm = () => {
  const result = [
    ...gridApi.grid.getCheckboxRecords(),
    ...gridApi.grid.getCheckboxReserveRecords(),
  ];

  if (result.length === 0) {
    message.warning('请选择资产类型');
    return;
  }

  expectRef.value!.payload = result;
  modalApi.close();
  expectRef.value!.resolve(result);
};
modalApi.onClosed = () => {
  if (!expectRef.value!.payload) {
    expectRef.value!.reject();
  }

  gridApi.grid.clearData();
  materialTypeOptions.value = [];
  productCategoryOptions.value = [];
  brandOptions.value = [];
  // 规格选项
  specOptions.value = [];
  specOptionsLoading.value = false;
};

defineExpose({
  open(config?: any): Promise<Record<string, any>[]> {
    modalApi.setData({
      selected: config ? config.keys : [],
    });
    modalApi.open();
    return new Promise((resolve, reject) => {
      expectRef.value = {
        payload: void 0,
        resolve,
        reject,
      };
    });
  },
});
</script>

<template>
  <Modal class="w-[1000px]" :title="props.title">
    <Grid />
  </Modal>
</template>
