<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';

import { shallowReactive, shallowRef } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';

const snForm: VbenFormProps = {
  submitButtonOptions: {
    show: false,
  },
  resetButtonOptions: {
    show: false,
  },
  layout: 'horizontal',
  schema: [
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入SN，多个SN可以使用 "," ";" "|" 空格或者换行符分隔',
        rows: 6,
      },
      fieldName: 'sn',
      label: 'SN',
      rules: 'required',
    },
  ],
  wrapperClass: 'grid-cols-1',
};

const [BaseForm, formApi] = useVbenForm(snForm);
const init = () => ({
  alertType: '',
  result: '',
});
const scope = shallowReactive(init());
const expectRef = shallowRef<{
  payload?: Record<string, any>;
  reject: () => void;
  resolve: (record: Record<string, any>) => void;
}>();
const [Modal, modalApi] = useVbenModal({
  confirmText: '解析',
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const values = await formApi.getValues();
    const { sn } = values;
    const result = sn.split(/[，,\s;|]+/g);
    if (!result || result.length === 0) {
      scope.result = '解析失败';
      scope.alertType = 'error';
      return;
    }
    expectRef.value!.payload = result;
    modalApi.close();
    expectRef.value!.resolve(result);
  },
  onClosed() {
    if (!expectRef.value!.payload) {
      expectRef.value!.reject();
    }
    Object.assign(scope, init());
  },
});

defineExpose({
  open(): Promise<Record<string, any>> {
    modalApi.open();
    return new Promise((resolve, reject) => {
      expectRef.value = {
        payload: void 0,
        resolve,
        reject,
      };
    });
  },
});
</script>

<template>
  <Modal class="w-[480px]" title="批量添加SN">
    <BaseForm />
    <a-alert
      v-if="!!scope.alertType"
      class="mt-4"
      :message="scope.alertType === 'success' ? '解析成功' : '解析失败'"
      :description="scope.result"
      :type="scope.alertType"
      show-icon
    />
  </Modal>
</template>
