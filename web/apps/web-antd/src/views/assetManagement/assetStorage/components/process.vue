<script setup lang="ts">
import type {
  UploadChangeParam,
  UploadFile,
} from 'ant-design-vue/es/upload/interface';

import type { InboundDetail } from '#/api/core/cmdb/asset/types';
import type { InboundTicketRes } from '#/api/core/ticket/types';

import { onMounted, ref } from 'vue';

import { AccessControl } from '@vben/access';
import { IconifyIcon } from '@vben/icons';

import { message, Upload } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  TransitionInboundTicket,
  updateInboundDetailApi,
  updateInboundDetailImportApi,
} from '#/api/core/ticket/asset-ticket';

import { uploadVerifyFiles } from '../../assetOutWarehouse/config';
import {
  currentStepsV2,
  downloadTemplate,
  getTemplate,
  stepMapV2,
} from '../config';
import PartDetail from './partDetail.vue';

const props = defineProps<{
  no: string;
  ticket: InboundTicketRes;
}>();

const emits = defineEmits<{
  (e: 'submitSuccess'): void; // 提交成功事件
  (e: 'detail', data: InboundDetail[]): void; // 子组件数据
}>();

const detail = ref({
  tabKey: 'manual',
  file: null as null | UploadFile, // 批量导入文件，只允许上传一个
  assetPhotos: [] as UploadFile[], // 设备图片
  verifyFile: [] as UploadFile[], // 验收文件
  templateType: '', // 模板类型
  templateFileName: '', // 模板文件名
  templateFilePath: '', // 模板文件URL
});

const gridData = ref();

const [approveForm, approveFormApi] = useVbenForm({
  submitButtonOptions: {
    show: false,
  },
  resetButtonOptions: {
    show: false,
  },
  layout: 'horizontal',
  schema: [
    {
      component: 'Input',
      componentProps: {
        type: 'hidden',
      },
      fieldName: 'status',
      label: '状态',
      formItemClass: 'hidden',
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入备注',
        rows: 3,
      },
      fieldName: 'comments',
      label: '备注',
      dependencies: {
        triggerFields: ['status'],
        show() {
          return (
            props.ticket.status !== 'completed' &&
            props.ticket.status !== 'rejected'
          );
        },
      },
      labelWidth: 50,
      rules: 'required',
    },
    {
      fieldName: 'asset_photo',
      component: 'Input',
      componentProps: {
        type: 'hidden',
      },
      dependencies: {
        triggerFields: ['status'],
        show(values) {
          return (
            values.status === 'waiting_asset_approval' &&
            (props.ticket.inbound_reason === 'new_purchase' ||
              props.ticket.inbound_reason === 'return_repaired')
          );
        },
      },
      labelWidth: 50,
    },
    {
      fieldName: 'verify_file',
      component: 'Input',
      componentProps: {
        // type: 'hidden',
      },
      dependencies: {
        triggerFields: ['status'],
        show(values) {
          return values.status !== 'completed' && values.status !== 'rejected';
        },
      },
      hideLabel: false,
      labelWidth: 50,
    },
  ],
  wrapperClass: 'grid-cols-1',
  commonConfig: {
    labelWidth: 200,
  },
});

/**
 * 获取模板
 */
const getInboundTemplate = async () => {
  if (
    props.ticket.status === 'completed' ||
    props.ticket.status === 'rejected'
  ) {
    return;
  }

  try {
    // 使用封装后的函数获取模板
    const { templateType, fileName, filePath } = await getTemplate(
      props.ticket.inbound_type,
      props.ticket.inbound_reason,
      props.no,
    );

    // 更新详情数据
    detail.value.templateType = templateType;
    detail.value.templateFileName = fileName;
    detail.value.templateFilePath = filePath;
    // 下载模板
    downloadTemplate(detail.value.templateFileName);
  } catch (error) {
    console.error('获取模板失败:', error);
  }
  console.warn(`templateFileName:`, detail.value.templateFileName);
};

// 批量导入文件处理
const onChooseImportFile = (info: UploadChangeParam) => {
  detail.value.file = info.file;
  console.warn(`info:`, info);
};

const handleRemoveImportFile = () => {
  detail.value.file = null;
};

// 设备图片处理
const onChooseFile = (info: UploadChangeParam, type: string) => {
  // 确保文件是图片类型
  if (info.file.type && !/^image\//.test(info.file.type)) {
    console.warn('文件信息：', info.file);
    message.error('只能上传图片文件！');
    return Upload.LIST_IGNORE;
  }
  switch (type) {
    case 'asset': {
      if (detail.value.assetPhotos.length >= 3) {
        message.error('最多上传3张图片');
        return Upload.LIST_IGNORE;
      }
      detail.value.assetPhotos = [...detail.value.assetPhotos, info.file];
      approveFormApi.setValues({
        asset_photo: detail.value.assetPhotos,
      });
      break;
    }
    case 'verify': {
      if (detail.value.verifyFile.length >= 3) {
        message.error('最多上传3个文件');
        return Upload.LIST_IGNORE;
      }
      detail.value.verifyFile = [...detail.value.verifyFile, info.file];
      approveFormApi.setValues({
        verify_file: detail.value.verifyFile,
      });
      break;
    }
  }
};

const handleRemoveFile = (file: UploadFile, type: string) => {
  switch (type) {
    case 'asset': {
      detail.value.assetPhotos = detail.value.assetPhotos.filter(
        (item: UploadFile) => item.uid !== file.uid,
      );
      break;
    }
    case 'verify': {
      detail.value.verifyFile = detail.value.verifyFile.filter(
        (item: UploadFile) => item.uid !== file.uid,
      );
      break;
    }
  }
};

const handleUploadDrop = (e: DragEvent) => {
  // 可以在这里添加拖放事件的处理逻辑
  e.preventDefault();
  e.stopPropagation();
};

// 获取子组件数据
const handleData = (data: InboundDetail[]) => {
  gridData.value = data;
  console.warn(`gridData.value:`, gridData.value);
};

// 添加防抖标志
const submitting = ref(false);

const handleSubmit = async (pass: boolean) => {
  // 防抖处理：如果正在提交，直接返回
  if (submitting.value) {
    return;
  }
  // 根据阶段和是否通过，设置状态
  let status = '';
  switch (props.ticket.stage) {
    case 'asset_approval': {
      status = pass ? 'asset_approval_pass' : 'asset_approval_fail';
      break;
    }
    case 'verify': {
      status = pass ? 'verification_passed' : 'verification_failed';
      break;
    }
    default: {
      throw new Error('不支持的操作');
    }
  }

  try {
    submitting.value = true;

    // 验证表单
    const { valid } = await approveFormApi.validate();
    if (!valid) {
      return;
    }

    // 获取表单值
    const values = await approveFormApi.getValues();

    if (status === 'asset_approval_fail' || status === 'verification_failed') {
      await TransitionInboundTicket(props.no, {
        status,
        comments: values.comments,
        inbound_type: props.ticket.inbound_type,
      });
      emits('submitSuccess');
      return;
    }

    // 资产管理员需要更新SN数据
    if (props.ticket.stage === 'asset_approval') {
      let details: InboundDetail[] = [];
      if (detail.value.tabKey === 'import') {
        const { file } = detail.value;
        if (!file) {
          message.error('请上传文件');
          return;
        }
        try {
          await updateInboundDetailImportApi(props.no, file as any);
        } catch (error) {
          console.error(error);
          return;
        }
      } else {
        try {
          // 验证SN是否填写
          if (gridData.value) {
            details = gridData.value.map((n: InboundDetail, index: number) => {
              switch (props.ticket.inbound_type) {
                case 'device_inbound': {
                  if (!n.device_sn) {
                    throw new Error(`第 ${index + 1} 行请填写SN`);
                  }
                  break;
                }
                case 'part_inbound': {
                  switch (props.ticket.inbound_reason) {
                    case 'dismantled': {
                      if (!n.component_sn) {
                        throw new Error(`第 ${index + 1} 行请填写SN`);
                      }
                      if (!n.component_state) {
                        throw new Error(`第 ${index + 1} 行请填写状态`);
                      }
                      if (!n.device_sn) {
                        throw new Error(`第 ${index + 1} 行请填写设备SN`);
                      }
                      break;
                    }
                  }
                  break;
                }
              }
              return n;
            });
          }
          await updateInboundDetailApi(props.no, details);
        } catch (error) {
          message.error(`${(error as Error).message}`);
          console.error(error);
          return;
        }
      }
    }
    const stage = props.ticket.stage;
    const ticketID = props.ticket.id;

    try {
      // 上传设备图片
      if (
        stage === 'asset_approval' &&
        detail.value.assetPhotos &&
        detail.value.assetPhotos.length > 0
      ) {
        await uploadVerifyFiles(
          detail.value.assetPhotos as any,
          `inbound-ticket-photo`,
          Number(ticketID),
          '验收设备图片',
        );
      }
      // 上传验收文件
      if (detail.value.verifyFile && detail.value.verifyFile.length > 0) {
        await uploadVerifyFiles(
          detail.value.verifyFile as any,
          `inbound-ticket-verify`,
          Number(ticketID),
          '验收单',
        );
      }

      await TransitionInboundTicket(props.no, {
        status,
        comments: values.comments,
        inbound_type: props.ticket.inbound_type,
      });
    } catch (error) {
      message.error(`操作失败: ${(error as Error).message}`);
      console.error(error);
      throw error;
    }
    message.success('提交成功');
    // 触发提交成功事件，通知父组件刷新其他组件
    emits('submitSuccess');
  } finally {
    // 无论成功失败，都将submitting设为false
    submitting.value = false;
  }
};

onMounted(() => {
  approveFormApi.setValues({
    status: props.ticket.status,
  });
});
</script>

<template>
  <div
    class="mb-4 rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800"
  >
    <a-typography-title :level="5" class="m-0 text-gray-800 dark:text-gray-200">
      处理进度
    </a-typography-title>
    <a-steps
      class="p-1"
      :current="stepMapV2(props.ticket.inbound_reason, props.ticket.stage)"
      :items="currentStepsV2(props.ticket.inbound_reason, props.ticket.stage)"
    />
    <!-- 资产管理员审核 -->
    <div v-if="props.ticket.stage === 'asset_approval'">
      <a-tabs v-model:active-key="detail.tabKey">
        <a-tab-pane key="manual" tab="手动填写SN">
          <!-- <DeviceDetail
            :no="props.no"
            :stage="props.ticket.stage"
            :inbound-type="props.ticket.inbound_type"
            @data="handleData"
            v-if="props.ticket.inbound_type === 'device_inbound'"
          /> -->
          <PartDetail
            :no="props.no"
            :ticket="props.ticket"
            @detail="handleData"
          />
        </a-tab-pane>
        <a-tab-pane key="import" tab="批量录入SN">
          <a-button class="mr-4" type="primary" @click="getInboundTemplate">
            <template #icon>
              <CloudDownload :size="18" />
            </template>
            下载批量导入模板
          </a-button>
          <a-upload-dragger
            name="file"
            :before-upload="() => false"
            @change="onChooseImportFile"
            @drop="handleUploadDrop"
            @remove="handleRemoveImportFile"
            class="mt-2 flex h-40 flex-col items-center justify-center"
          >
            <div class="mb-2 flex items-center justify-center">
              <IconifyIcon
                icon="file-icons:microsoft-excel"
                width="40"
                height="40"
                style="color: #377818"
              />
            </div>
            <p class="ant-upload-text text-center">点击上传或拖拽文件到此</p>
            <p class="ant-upload-hint text-center">
              请您确认已填写文档，本功能支持.xlsx文件
            </p>
          </a-upload-dragger>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 专业工程师审核 -->
    <template v-else-if="props.ticket.stage === 'verify'">
      <!-- <DeviceDetail
        :no="props.no"
        :stage="props.ticket.stage"
        :inbound-type="props.ticket.inbound_type"
        @data="handleData"
        v-if="props.ticket.inbound_type === 'device_inbound'"
      /> -->
      <PartDetail :no="props.no" :ticket="props.ticket" @detail="handleData" />
    </template>

    <!-- 入库完成/拒绝 -->
    <template
      v-else-if="
        props.ticket.stage === 'complete_inbound' ||
        props.ticket.stage === 'rejected'
      "
    >
      <!-- <DeviceDetail
        :no="props.no"
        :stage="props.ticket.stage"
        :inbound-type="props.ticket.inbound_type"
        @data="handleData"
        v-if="props.ticket.inbound_type === 'device_inbound'"
      /> -->
      <PartDetail :no="props.no" :ticket="props.ticket" @detail="handleData" />
    </template>

    <!-- 部分需要填写的表单 -->
    <approveForm class="mt-4">
      <!-- 上传 -->
      <template #asset_photo>
        <a-upload
          :file-list="detail.assetPhotos"
          :before-upload="() => false"
          @change="(info: UploadChangeParam) => onChooseFile(info, 'asset')"
          @drop="handleUploadDrop"
          @remove="(file: UploadFile) => handleRemoveFile(file, 'asset')"
          :show-remove-icon="true"
        >
          <a-button> <CloudUpload /> 上传出入室图片 </a-button>
        </a-upload>
      </template>
      <template #verify_file>
        <a-upload
          :file-list="detail.verifyFile"
          :before-upload="() => false"
          :disabled="detail.verifyFile.length === 3"
          @change="(info: UploadChangeParam) => onChooseFile(info, 'verify')"
          @drop="handleUploadDrop"
          @remove="(file: UploadFile) => handleRemoveFile(file, 'verify')"
          :show-remove-icon="true"
        >
          <a-button> <CloudUpload /> 上传验收单</a-button>
        </a-upload>
      </template>

      <!-- 通过/拒绝 按钮 -->
      <div class="text-center">
        <!-- 资产管理员审核 -->
        <template v-if="props.ticket.stage === 'asset_approval'">
          <AccessControl :codes="['AssetMgt:AssetInbound:Asset']" type="code">
            <a-button
              type="primary"
              class="mr-4"
              @click="handleSubmit(true)"
              :loading="submitting"
            >
              通过
            </a-button>
            <a-button
              type="primary"
              danger
              @click="handleSubmit(false)"
              :loading="submitting"
            >
              拒绝
            </a-button>
          </AccessControl>
        </template>
        <!-- 专业工程师审核 -->
        <template v-else-if="props.ticket.stage === 'verify'">
          <AccessControl :codes="['AssetMgt:AssetInbound:Verify']" type="code">
            <a-button
              type="primary"
              class="mr-4"
              @click="handleSubmit(true)"
              :loading="submitting"
            >
              通过
            </a-button>

            <a-button
              type="primary"
              danger
              @click="handleSubmit(false)"
              :loading="submitting"
            >
              拒绝
            </a-button>
          </AccessControl>
        </template>
      </div>
    </approveForm>
  </div>
</template>
