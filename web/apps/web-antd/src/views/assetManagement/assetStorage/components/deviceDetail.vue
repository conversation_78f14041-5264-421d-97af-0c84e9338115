<script setup lang="ts">
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { InboundDetail } from '#/api/core/cmdb/asset/types';

import { ref, watch } from 'vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getInboundDetailListApi } from '#/api/core/ticket/asset-ticket';

const props = defineProps<{
  inboundType: string; // 入库类型
  no: string; // 入库单号
  stage: string; // 阶段
}>();

const emit = defineEmits<{
  (e: 'data', data: InboundDetail[]): void;
}>();

const isLoaded = ref(false);
const loadedItems = ref<InboundDetail[]>([]);

const assetTypeMap = {
  switch: '交换机',
  firewall: '防火墙',
  router: '路由器',
  loadbalancer: '负载均衡器',
  other: '其他',
  gpu_server: 'GPU服务器',
  server: '服务器',
  network: '网络设备',
  storage: '存储设备',
};

const templateCategoryMap = {
  standard: '标准',
  custom: '定制',
  high_performance: '高性能',
  network_device: '网络设备',
  gpu: 'GPU',
  Category1: 'Category1',
  other: '其他',
};

const deviceDetailGrid: VxeGridProps = {
  maxHeight: 400,
  minHeight: 40,
  columns: [
    { field: 'id', title: '编号', width: 50 },
    { field: 'template.templateName', title: '模板名称' },
    { field: 'template.cpuModel', title: 'CPU型号' },
    { field: 'template.memoryCapacity', title: '内存容量(GB)' },
    { field: 'template.gpuModel', title: 'GPU型号' },
    { field: 'template.diskType', title: '存储类型' },
    {
      field: 'template.templateCategory',
      title: '模板类别',
      width: 120,
      formatter: ({ cellValue }) =>
        templateCategoryMap[cellValue as keyof typeof templateCategoryMap] ||
        cellValue,
    },
    {
      field: 'asset_type',
      title: '资产类型',
      width: 140,
      formatter: ({ cellValue }) =>
        assetTypeMap[cellValue as keyof typeof assetTypeMap] || cellValue,
    },
    {
      field: 'sn',
      title: 'SN',
      slots: { default: 'sn' },
    },
  ],
  proxyConfig: {
    // response: {
    //   result: 'list',
    // },
    ajax: {
      query: async ({ page }) => {
        const res = await getInboundDetailListApi(
          props.no,
          page.currentPage,
          page.pageSize,
        );
        loadedItems.value = res.list;
        isLoaded.value = true;
        return {
          items: res.list,
          total: res.total,
        };
      },
    },
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10,
    pageSizes: [10, 20, 50, 100, 500, 1000],
  },
  scrollY: {
    enabled: true,
    gt: 0,
  },
  showOverflow: true,
};

const [DeviceDetailGrid, deviceDetailGridApi] = useVbenVxeGrid({
  gridOptions: deviceDetailGrid,
});

const handleData = () => {
  const data = deviceDetailGridApi.grid.getFullData();
  switch (props.inboundType) {
    case 'device_inbound': {
      data.forEach((item: any) => {
        item.device_sn = item.sn;
      });
      break;
    }
    case 'part_inbound': {
      data.forEach((item: any) => {
        item.component_sn = item.sn;
      });
      break;
    }
    default: {
      break;
    }
  }

  emit('data', data);
};

watch(isLoaded, (newVal) => {
  if (newVal) {
    handleData();
  }
});
</script>

<template>
  <DeviceDetailGrid>
    <!-- 填写SN -->
    <template #sn="{ row }">
      <a-input
        v-model:value="row.sn"
        allow-clear
        @change="handleData"
        :status="row.sn ? '' : 'error'"
        v-if="props.stage === 'asset_approval'"
      />
      <span v-else>{{ row.device_sn || row.component_sn }}</span>
    </template>
  </DeviceDetailGrid>
</template>
