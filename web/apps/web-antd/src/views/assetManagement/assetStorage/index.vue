<script setup lang="ts">
import type { SelectOption } from '@vben/types';

import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { onMounted, shallowRef, useTemplateRef } from 'vue';
import { useRouter } from 'vue-router';

import { AccessControl } from '@vben/access';
import { Page } from '@vben/common-ui';
import { formatDateTime } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { listInboundTicketApi } from '#/api/core/cmdb/asset/inbound';
import { getRegionTableApi } from '#/api/core/cmdb/location/region';

import InRequest from './components/in-request.vue';
import {
  inboundReasonOptions,
  inboundReasons,
  inboundTypes,
  stageMap,
  ticketStageMap,
} from './config';

const projectOptions = shallowRef<SelectOption[]>([]);
/** 资产入库查询条件 */
const inboundForm: VbenFormProps = {
  collapsed: true,
  collapsedRows: 3,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入工单编号' },
      fieldName: 'InboundNo',
      label: '工单编号',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择项目',
        options: projectOptions,
      },
      fieldName: 'project',
      label: '项目',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择当前环节',
        options: Object.entries(ticketStageMap).map(([key, value]) => {
          return {
            label: value,
            value: key,
          };
        }),
      },
      fieldName: 'stage',
      label: '当前环节',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择入库类型',
        options: [
          {
            label: '配件入库',
            value: 'part_inbound',
          },
        ],
      },
      fieldName: 'inboundType',
      label: '入库类型',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择入库原因',
        options: inboundReasonOptions,
      },
      fieldName: 'inboundReason',
      label: '入库原因',
    },

    // {
    //   component: 'Input',
    //   componentProps: { placeholder: '请输入工单标题' },
    //   fieldName: 'title',
    //   label: '工单标题',
    // },
    // {
    //   component: 'RangePicker',
    //   componentProps: { placeholder: ['开始时间', '结束时间'] },
    //   fieldName: 'title',
    //   label: '提单日期',
    // },
    // {
    //   component: 'Input',
    //   componentProps: { placeholder: '请输入合同编号' },
    //   fieldName: 'title',
    //   label: '合同编号',
    // },
    // {
    //   component: 'Input',
    //   componentProps: { placeholder: '请输入创建人' },
    //   fieldName: 'creatorName',
    //   label: '创建人',
    // },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: false,
  submitOnChange: false,
  submitOnEnter: true,
  wrapperClass: 'grid-cols-3 grid',
};

/** 资产入库表格配置 */
const inboundGrid: VxeGridProps = {
  columns: [
    { field: 'inbound_ticket_id', title: 'ID', width: 50 },
    { field: 'inbound_no', title: '工单编号' },
    { field: 'inbound_title', title: '工单标题' },
    { field: 'project', title: '项目' },
    {
      field: 'inbound_type',
      title: '入库类型',
      formatter({ cellValue }) {
        return inboundTypes[cellValue] || '';
      },
    },
    {
      field: 'inbound_reason',
      title: '入库原因',
      width: 80,
      formatter({ cellValue }) {
        return inboundReasons[cellValue] || '';
      },
    },
    { field: 'create_by', title: '创建人' },
    {
      field: 'created_at',
      title: '创建时间',
      width: 180,
      formatter: 'formatDateTime',
    },
    {
      field: 'completed_at',
      title: '完成时间',
      width: 180,
      formatter({ row }) {
        return ['complete_inbound', 'rejected'].includes(row.stage)
          ? formatDateTime(row.updated_at)
          : '';
      },
    },
    {
      field: 'stage',
      title: '当前环节',
      slots: { default: 'stage' },
    },
    {
      title: '操作',
      fixed: 'right',
      slots: { default: 'action' },
      width: 80,
    },
  ],
  proxyConfig: {
    response: {
      result: 'list',
    },
    ajax: {
      query: ({ page }, form) => {
        // console.log('query', page);
        return listInboundTicketApi({
          page: page.currentPage,
          page_size: page.pageSize,
          inbound_no: form.InboundNo,
          project: form.project,
          inbound_type: form.inboundType,
          inbound_reason: form.inboundReason,
          stage: form.stage,
        });
      },
    },
  },
  pagerConfig: {
    enabled: true,
    pageSize: 10,
  },
  sortConfig: {
    multiple: true,
  },
  virtualYConfig: {
    enabled: true,
    gt: 100,
  },
  virtualXConfig: {
    enabled: true,
    gt: 10,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar-buttons',
    },
    custom: true,
    // export: true,
    // import: true,
    refresh: true,
    zoom: true,
  },
};

const inRequest = useTemplateRef('inRequest');
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: inboundForm,
  gridOptions: inboundGrid,
});
const router = useRouter();
const handleDetail = (row: any) => {
  const route = router.resolve({
    path: `/asset-storage-detail/${row.inbound_no}`,
  });
  window.open(route.href, '_blank');
};

const add = () => {
  inRequest.value!.open().then(() => {
    gridApi.query();
  });
};
const onLoad = async () => {
  getRegionTableApi({
    page: 1,
    pageSize: 1000,
  }).then((res) => {
    projectOptions.value = res.list.map((item) => ({
      label: item.name,
      value: item.name,
    }));
  });
};

onMounted(() => {
  onLoad();
});
</script>

<template>
  <div class="w-full">
    <Page>
      <Grid>
        <template #toolbar-buttons>
          <AccessControl :codes="['AssetMgt:AssetInbound:Create']" type="code">
            <a-button class="mr-2" type="primary" @click="add()">
              新增
            </a-button>
          </AccessControl>
        </template>
        <template #action="{ row }">
          <a-button type="link" size="small" @click="handleDetail(row)">
            详情
          </a-button>
        </template>
        <template #stage="{ row }">
          <a-tag :color="stageMap[row.stage]?.color || 'default'">
            {{ stageMap[row.stage]?.text || row.stage }}
          </a-tag>
        </template>
      </Grid>
    </Page>

    <InRequest ref="inRequest" />
  </div>
</template>
