import { reactive } from 'vue';

import { downloadExportFile } from '#/api/core/export';
import { getExportTemplateApi } from '#/api/core/purchase/purchase_old';

export const inboundReasons: Record<string, string> = {
  new_purchase: '新购入库',
  dismantled: '拆机入库',
  return_repaired: '返修入库',
};

export const inboundTypes: Record<string, string> = {
  part_inbound: '配件入库',
  device_inbound: '设备入库',
};

export const inboundReasonByTypes: Record<string, Record<string, string>> = {
  part_inbound: {
    new_purchase: '新购入库',
    dismantled: '拆机入库',
    return_repaired: '返修入库',
  },
  device_inbound: {
    new_purchase: '新购入库',
  },
};

export const stageMap: Record<string, { color: string; text: string }> = {
  Process: { color: 'processing', text: '处理中' },
  Asset_approval: { color: 'processing', text: '资产审核中' },
  Verify: { color: 'processing', text: '待验证' },
  Complete: { color: 'green', text: '完成' },
  Fail: { color: 'red', text: '已拒绝' },
  process: { color: 'processing', text: '处理中' },
  asset_approval: { color: 'processing', text: '资管审核' },
  verify: { color: 'processing', text: '专业工程师验证' },
  complete: { color: 'success', text: '完成' },
  complete_inbound: { color: 'success', text: '完成入库' },
  fail: { color: 'error', text: '已拒绝' },
  Rejected: { color: 'error', text: '已拒绝' },
  rejected: { color: 'error', text: '已拒绝' },
};

export const historyStageMap: Record<string, string> = {
  submit_apply: '发起申请',
  asset_approval: '资产管理员审批',
  verify: '专业工程师审批',
  complete_inbound: '完成入库',
  rejected: '已拒绝',
};

export const statusMap: Record<string, string> = {
  submiting_apply: '发起申请',
  waiting_asset_approval: '等待资产管理员同意',
  asset_approval_pass: '资产管理员审核通过',
  asset_approval_fail: '资产管理员审核不通过',
  waiting_verify: '等待验证',
  verification_passed: '验证通过',
  verification_failed: '验证失败',
  completed: '已完成',
  rejected: '已拒绝',
};

export const inboundReasonOptions = Object.entries(inboundReasons).map(
  ([key, value]) => ({
    label: value,
    value: key,
  }),
);

export const ticketStageMap: Record<string, string> = {
  Fail: '处理失败',
  Process: '处理中',
  Complete: '处理完成',
  // 新状态
  complete_inbound: '完成入库',
  rejected: '拒绝出库',
  waiting_engineer_approval: '专业工程师审批',
  waiting_asset_approval: '资产管理员审批',
};

// 资产状态映射
export const assetStatusMap: {
  [key: string]: { color: string; text: string };
} = {
  pending_in: { text: '待入库', color: 'purple' },
  in_stock: { text: '已入库', color: 'blue' },
  pending_out: { text: '待出库', color: 'orange' },
  out_stock: { text: '已出库', color: 'cyan' },
  idle: { text: '闲置中', color: 'lime' },
  in_use: { text: '使用中', color: 'green' },
  repairing: { text: '维修中', color: 'gold' },
  pending_scrap: { text: '待报废', color: 'volcano' },
  scrapped: { text: '已报废', color: 'red' },
};

// 硬件状态映射
export const hardwareStatusMap: {
  [key: string]: { color: string; text: string };
} = {
  normal: { text: '正常', color: 'green' },
  faulty: { text: '故障', color: 'red' },
  warning: { text: '警告', color: 'orange' },
  repairing: { text: '维修中', color: 'orange' },
  retired: { text: '已报废', color: 'gray' },
};

// 来源类型映射
export const sourceTypeMap: {
  [key: string]: { color: string; text: string };
} = {
  新购: { text: '新购', color: 'blue' },
  拆机: { text: '拆机', color: 'orange' },
  维修: { text: '维修', color: 'purple' },
  退回: { text: '退回', color: 'gray' },
  其它: { text: '其它', color: 'gray' },
  // 新增映射
  new_purchase: { text: '新购', color: 'blue' },
  dismantled: { text: '拆机', color: 'orange' },
  repaired: { text: '维修', color: 'purple' },
  return_repaired: { text: '返厂维修', color: 'green' }, // 新增颜色配置
  returned: { text: '退回', color: 'gray' },
  other: { text: '其它', color: 'gray' }, // 已将"其他"改为"其它"
  renew: { text: '换新', color: 'teal' }, // 新增颜色配置
};

// 组件状态映射
export const componentStatusMap: {
  [key: string]: { color: string; text: string };
} = {
  normal: { text: '正常', color: 'green' },
  faulty: { text: '故障', color: 'red' },
  // repairing: { text: '维修中', color: 'orange' },
  // retired: { text: '已报废', color: 'gray' },
};

/** 拆机入库列表 */
export const inboundLocationEditRender = reactive({
  name: 'ASelect',
  props: {
    placeholder: '请选择入库位置',
  },
  options: [],
});

/** 详情页步骤 */
const stepItems = [
  {
    title: '开始',
  },
  {
    title: '资产管理员审批',
  },
  {
    title: '工程师验证',
  },
  {
    title: '完成',
  },
];

const stepItems2 = [
  {
    title: '开始',
  },
  {
    title: '资产管理员审批',
  },
  {
    title: '工程师验证',
  },
  {
    title: '已拒绝',
  },
];

export function currentSteps(stage: string) {
  return stage === 'rejected' ? stepItems2 : stepItems;
}

export function currentStepsV2(inboundReason: string, stage: string) {
  switch (inboundReason) {
    case 'dismantled': {
      const steps: { stage?: string; title: string }[] = [
        { title: '发起申请' },
        { title: '资产管理员审批' },
      ];
      if (stage === 'rejected') {
        steps.push({ title: '拒绝', stage: 'rejected' });
      } else {
        steps.push({ title: '完成' });
      }
      return steps;
    }
    case 'new_purchase':
    case 'return_repaired': {
      const steps: { stage?: string; title: string }[] = [
        { title: '发起申请' },
        { title: '资产管理员审批' },
        { title: '专业工程师审批' },
      ];
      if (stage === 'rejected') {
        steps.push({ title: '拒绝', stage: 'rejected' });
      } else {
        steps.push({ title: '完成' });
      }
      return steps;
    }
    default: {
      return stepItems;
    }
  }
}

const NewPurchaseSteps: Record<string, number> = {
  submit_apply: 0,
  asset_approval: 1,
  verify: 2,
  complete_inbound: 3,
  rejected: 3,
};

const DismantledSteps: Record<string, number> = {
  submit_apply: 0,
  asset_approval: 1,
  complete_inbound: 2,
  rejected: 2,
};

export function stepMapV2(inboundReason: string, stage: string) {
  switch (inboundReason) {
    case 'dismantled': {
      return DismantledSteps[stage];
    }
    case 'new_purchase':
    case 'return_repaired': {
      return NewPurchaseSteps[stage];
    }
  }
}

/** 步骤映射 */
export const stepMap: Record<string, number> = {
  process: 0,
  asset_approval: 1,
  verify: 2,
  complete_inbound: 3,
  Process: 0,
  Asset_approval: 1,
  Verify: 2,
  Complete_inbound: 3,
  Rejected: 3,
  rejected: 3,
};

export const statusColors: Record<string, string> = {
  asset_approval_pass: 'green',
  verification_passed: 'green',
  asset_approval_fail: 'red',
  verification_failed: 'red',
  rejected: 'red',
};

export const repairTypeMap: Record<string, string> = {
  repaired: '维修',
  renew: '换新',
};

export const repairTypeOptions = Object.entries(repairTypeMap).map(
  ([key, value]) => ({
    label: value,
    value: key,
  }),
);

export const remarkFileter = (remark: string) => {
  if (remark) {
    Object.keys(statusMap).forEach((key: string) => {
      remark = remark.replace(key, statusMap[key] || key);
    });
  }
  return remark;
};

export const historyTimelineItemColor = (status: string) => {
  return statusColors[status] || 'blue';
};

export async function downloadTemplate(fileName: string) {
  await downloadExportFile(`/download/${fileName}`);
}

/**
 * 获取出库模板
 * @param type 类型
 * @param reason 原因
 * @param outbound_id 出库单ID
 * @returns {Promise<{templateType: string, fileName: string, filePath: string}>}
 */
export async function getTemplate(
  type: string,
  reason: string,
  inbound_no: string,
) {
  let templateType = '';
  let fileName = '';
  let filePath = '';

  // 使用switch-case结构确定模板类型
  switch (type) {
    case 'device_inbound': {
      switch (reason) {
        case 'new_purchase': {
          templateType = 'new_device_template';
          break;
        }
        // 这里可以添加其他device的情况
      }
      break;
    }
    case 'part_inbound': {
      switch (reason) {
        case 'dismantled': {
          templateType = 'dismantled_part_template';
          break;
        }
        case 'new_purchase': {
          templateType = 'new_part_template';
          break;
        }
        case 'return_repaired': {
          templateType = 'return_repair_part_template';
          break;
        }
      }
      break;
    }
  }
  if (templateType) {
    // 从API获取文件信息
    try {
      const response = await getExportTemplateApi({
        template_type: templateType,
        inbound_no,
      });
      fileName = response.fileName;
      filePath = response.filePath;
    } catch (error) {
      console.error('获取模板失败:', error);
    }
  }

  return {
    templateType,
    fileName,
    filePath,
  };
}
