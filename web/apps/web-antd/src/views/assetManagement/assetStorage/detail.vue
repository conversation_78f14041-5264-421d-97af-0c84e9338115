<script lang="ts" setup>
import type {
  InboundTicketHistoryRes,
  InboundTicketRes,
} from '#/api/core/ticket/types';

import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { message } from 'ant-design-vue';

import {
  getInboundHistoryApi,
  getInboundTicketV2Api,
} from '#/api/core/ticket/asset-ticket';

import DetailHeader from './components/detail-header.vue';
import FileList from './components/fileList.vue';
import History from './components/history.vue';
import Process from './components/process.vue';

const router = useRouter();

const initPageData = () => {
  return {
    inboundTicket: {} as InboundTicketRes,
    inboundHistory: [] as InboundTicketHistoryRes[],
    assetPhotos: [], // 配件图片
    verifyPhotos: [], // 验收图片
    templateFileName: '',
    templateFilePath: '',
    templateType: '', // 模板类型
  };
};
const pageData = ref(initPageData());

const route = useRoute();

// 刷新组件;
const refreshComponents = async () => {
  const inbound_no = route.params.no as string;
  // 刷新详情和历史记录
  try {
    const ticket = await getInboundTicketV2Api(inbound_no);
    pageData.value.inboundTicket = ticket;
    const history = await getInboundHistoryApi(inbound_no);
    pageData.value.inboundHistory = history;
  } catch (error) {
    message.error('刷新数据失败');
    console.error(error);
  }
};

const onLoad = async () => {
  const inbound_no = route.params.no as string;
  // 统一获取所有信息，加载到pageData中
  try {
    const ticket = await getInboundTicketV2Api(inbound_no);
    pageData.value.inboundTicket = ticket;
    const history = await getInboundHistoryApi(inbound_no);
    pageData.value.inboundHistory = history;
  } catch (error) {
    message.error('获取入库信息失败');
    console.error(error);
  }
};

const goBack = () => {
  router.push('/asset-storage');
};

onMounted(onLoad);
</script>

<template>
  <div class="min-h-screen bg-gray-50 pb-4 dark:bg-gray-900">
    <!-- 顶部标题和状态栏 -->
    <div
      class="mb-4 border-b border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800"
    >
      <div
        class="mx-auto flex max-w-full items-center justify-between px-4 py-3"
      >
        <div class="flex items-center">
          <a-button
            type="primary"
            @click="goBack"
            class="mr-4 flex items-center overflow-hidden whitespace-nowrap rounded-md border border-blue-600 border-transparent bg-blue-500 px-3 py-1 font-medium text-white shadow-sm transition-all duration-300 hover:bg-blue-600 hover:shadow-md"
          >
            <template #icon><span class="anticon">←</span></template>
            返回列表
          </a-button>
        </div>
      </div>
    </div>

    <div class="mx-auto max-w-full px-4">
      <!-- 重构，抽离原有组件 -->
      <DetailHeader
        v-if="pageData.inboundTicket && pageData.inboundTicket.id"
        :ticket="pageData.inboundTicket"
        :key="pageData.inboundTicket.id"
      />

      <!-- 重构，抽离原有组件 -->
      <Process
        v-if="pageData.inboundTicket && pageData.inboundTicket.id"
        :ticket="pageData.inboundTicket"
        :no="pageData.inboundTicket.inbound_no"
        :key="pageData.inboundTicket.stage"
        @submit-success="refreshComponents"
      />

      <FileList
        v-if="pageData.inboundTicket && pageData.inboundTicket.id"
        :id="pageData.inboundTicket.id"
        module-type="inbound-ticket-photo"
        title="出入室图片列表"
        :key="`photo-${pageData.inboundTicket.id}-${pageData.inboundTicket.status}`"
      />
      <FileList
        v-if="pageData.inboundTicket && pageData.inboundTicket.id"
        :id="pageData.inboundTicket.id"
        module-type="inbound-ticket-verify"
        title="验收单列表"
        :key="`verify-${pageData.inboundTicket.id}-${pageData.inboundTicket.stage}`"
      />

      <!-- 处理历史 -->
      <History
        v-if="pageData.inboundHistory && pageData.inboundHistory.length > 0"
        :history="pageData.inboundHistory"
        :key="`history-${pageData.inboundHistory.length}`"
      />
    </div>
  </div>
</template>
