<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { AssetSpare } from '#/api/core/cmdb/asset/spare';
import type { Product } from '#/api/core/cmdb/product/product';

import { h, onMounted, ref } from 'vue';

import { AccessControl } from '@vben/access';

import { Button, message, Tag } from 'ant-design-vue';

import {
  createSpareApi,
  deleteSpareApi,
  getSpareListApi,
  updateSpareApi as originalUpdateSpareApi,
} from '#/api/core/cmdb/asset/spare';
import { getWarehouseListApi } from '#/api/core/cmdb/asset/warehouse';
import {
  getBrandsApi,
  getMaterialTypesApi,
  getProductCategoriesApi,
  getProductListApi,
} from '#/api/core/cmdb/product/product';
import CommonDetail from '#/components/CommonDetail/index.vue';
import CommonList from '#/components/CommonList/index.vue';

// 为了匹配CommonList组件的API格式，封装更新API
const wrappedUpdateSpareApi = (data: any) => {
  return originalUpdateSpareApi(data.id, data);
};

const detailRef = ref();
const detailData = ref<AssetSpare | null>(null);
const commonListRef = ref();

// 选项列表
const typeOptions = ref<{ label: string; value: string }[]>([]);
const assetStatusOptions = ref<{ label: string; value: string }[]>([]);
const hardwareStatusOptions = ref<{ label: string; value: string }[]>([]);
const sourceTypeOptions = ref<{ label: string; value: string }[]>([]);
const materialTypeOptions = ref<{ label: string; value: string }[]>([]);
const productCategoryOptions = ref<{ label: string; value: string }[]>([]);
const brandOptions = ref<{ label: string; value: string }[]>([]);
const pnOptions = ref<{ label: string; product: any; value: string }[]>([]);
const pnOptionsLoading = ref(false);
const specOptions = ref<{ label: string; value: string }[]>([]);
const specOptionsLoading = ref(false);

// 资产状态映射
const assetStatusMap: {
  [key: string]: { color: string; text: string };
} = {
  pending_in: { text: '待入库', color: 'purple' },
  in_stock: { text: '已入库', color: 'blue' },
  pending_out: { text: '待出库', color: 'orange' },
  out_stock: { text: '已出库', color: 'cyan' },
  idle: { text: '闲置中', color: 'lime' },
  in_use: { text: '使用中', color: 'green' },
  repairing: { text: '维修中', color: 'gold' },
  pending_scrap: { text: '待报废', color: 'volcano' },
  scrapped: { text: '已报废', color: 'red' },
  sold_out: { text: '已售卖', color: 'red' },
};

// 硬件状态映射
const hardwareStatusMap: {
  [key: string]: { color: string; text: string };
} = {
  normal: { text: '正常', color: 'green' },
  faulty: { text: '故障', color: 'red' },
  warning: { text: '警告', color: 'orange' },
};

// 来源类型映射
const sourceTypeMap: {
  [key: string]: { color: string; text: string };
} = {
  // 新购: { text: '新购', color: 'blue' },
  // 拆机: { text: '拆机', color: 'orange' },
  // 维修: { text: '维修', color: 'purple' },
  // 退回: { text: '退回', color: 'gray' },
  // 其他: { text: '其他', color: 'gray' },

  // 新版
  new_purchase: { text: '新购', color: 'blue' },
  dismantled: { text: '拆机', color: 'orange' },
  // repair: { text: '维修', color: 'purple' },
  return_repair: { text: '返修', color: 'pink' },
  return: { text: '退回', color: 'purple' },
  allocate: { text: '调拨', color: 'cyan' },
  other: { text: '其他', color: 'gray' },
};

// 初始化选项数据
onMounted(async () => {
  // 初始化一个空的备件类型选项数组，之后会通过API填充
  typeOptions.value = [];

  // 设置资产状态选项
  assetStatusOptions.value = Object.entries(assetStatusMap).map(
    ([value, item]) => ({
      label: item.text,
      value,
    }),
  );

  // 设置硬件状态选项
  hardwareStatusOptions.value = Object.entries(hardwareStatusMap).map(
    ([value, item]) => ({
      label: item.text,
      value,
    }),
  );

  // 设置来源类型选项
  sourceTypeOptions.value = Object.entries(sourceTypeMap).map(
    ([value, item]) => ({
      label: item.text,
      value,
    }),
  );

  try {
    // 获取产品物料类型选项用于类型选择框
    const materialTypesRes = await getMaterialTypesApi();
    if (materialTypesRes && Array.isArray(materialTypesRes)) {
      // 将返回的物料类型数据转换为下拉选项格式，同时用于两个不同的选项列表
      const typeOpts = materialTypesRes.map((type: string) => ({
        label: type,
        value: type,
      }));

      // 设置备件类型选项
      typeOptions.value = typeOpts;

      // 设置材料类型选项
      materialTypeOptions.value = typeOpts;
    }

    // 获取产品类别选项
    const productCategoriesRes = await getProductCategoriesApi();
    if (productCategoriesRes && Array.isArray(productCategoriesRes)) {
      productCategoryOptions.value = productCategoriesRes.map(
        (category: string) => ({
          label: category,
          value: category,
        }),
      );
    }

    // 获取品牌选项
    const brandsRes = await getBrandsApi();
    if (brandsRes && Array.isArray(brandsRes)) {
      brandOptions.value = brandsRes.map((brand: string) => ({
        label: brand,
        value: brand,
      }));
    }

    // 获取仓库列表作为存放位置选项
    await loadLocationOptions();

    // 获取PN号码列表
    await loadPnOptions();

    // 获取规格选项列表
    await loadSpecOptions();
  } catch (error) {
    console.error('获取产品选项数据失败:', error);
    message.error('获取产品选项数据失败');
  }
});

// 加载规格选项
async function loadSpecOptions() {
  try {
    specOptionsLoading.value = true;

    // 调用API获取产品列表
    const res = await getProductListApi({
      page: 1,
      pageSize: 1000,
    });

    // 处理响应数据
    if (res && Array.isArray(res.list)) {
      // 收集所有不重复的规格
      const uniqueSpecs = new Set<string>();

      res.list.forEach((item: Product) => {
        if (
          item.spec &&
          typeof item.spec === 'string' &&
          item.spec.trim() !== ''
        ) {
          uniqueSpecs.add(item.spec.trim());
        }
      });

      // 转换为选项格式
      specOptions.value = [...uniqueSpecs].map((spec: string) => ({
        label: spec,
        value: spec,
      }));

      // 按照规格名称排序
      specOptions.value.sort((a, b) => a.label.localeCompare(b.label));
    } else {
      console.error('加载规格选项失败，响应数据格式不正确:', res);
      message.error('加载规格选项失败，请刷新页面重试');
      specOptions.value = [];
    }
  } catch (error) {
    console.error('加载规格选项异常:', error);
    message.error('加载规格选项出错，请刷新页面重试');
    specOptions.value = [];
  } finally {
    specOptionsLoading.value = false;
  }
}

// 加载PN选项
async function loadPnOptions() {
  try {
    pnOptionsLoading.value = true;

    // 调用API获取产品列表 - 减小pageSize并添加排序参数
    const res = await getProductListApi({
      page: 1,
      pageSize: 1000, // 增加数量以获取更多产品
    });

    // 根据API响应结构调整处理逻辑
    if (res && Array.isArray(res.list)) {
      // 过滤有效的产品（必须具有ID和PN）
      const validProducts = res.list.filter((item: Product) => {
        if (!item.id || !item.pn) {
          console.warn('发现无效产品数据:', item);
          return false;
        }
        return true;
      });

      // 转换为选项格式 - 确保label和value格式正确
      pnOptions.value = validProducts.map((item: Product) => ({
        label: `${item.pn} [ID:${item.id}] - ${item.brand || ''} ${item.model || ''}`,
        value: item.pn, // 将值保持为PN，但在选择时使用ID建立关联
        product: item,
      }));
    } else {
      console.error('加载PN选项失败，响应数据格式不正确:', res);
      message.error('加载PN选项失败，请刷新页面重试');
      pnOptions.value = [];
    }
  } catch (error) {
    console.error('加载PN选项异常:', error);
    message.error('加载PN选项出错，请刷新页面重试');
    pnOptions.value = [];
  } finally {
    pnOptionsLoading.value = false;
  }
}

// 当PN号码变化时自动填充产品信息
function handlePnChange(value: string) {
  if (!value) {
    // 当清空PN时，也清空product_id
    const formApi = commonListRef.value?.formApi;
    if (formApi) {
      formApi.setFieldValue('product_id', undefined);
      formApi.setFieldValue('_product_info', '');
    }
    return;
  }

  // 获取表单实例 - 使用formApi而不是formRef
  const formApi = commonListRef.value?.formApi;
  if (!formApi) {
    console.error('无法获取表单实例');
    return;
  }

  // 查找对应的产品信息
  const selectedOption = pnOptions.value.find((item) => item.value === value);
  if (!selectedOption) {
    console.error('无法根据PN找到产品选项:', value);
    message.error('无法找到对应的产品信息，请重新选择PN号码');
    formApi.setFieldValue('product_id', undefined);
    formApi.setFieldValue('_product_info', '');
    return;
  }

  const selectedProduct = selectedOption.product;
  if (!selectedProduct || !selectedProduct.id) {
    console.error('产品数据不完整:', selectedProduct);
    message.error('产品数据不完整，请选择其他PN号码');
    formApi.setFieldValue('product_id', undefined);
    formApi.setFieldValue('_product_info', '');
    return;
  }

  // 设置产品ID - 这是最关键的字段，建立备件与产品的关联
  formApi.setFieldValue('product_id', Number(selectedProduct.id));

  // 填充表单显示字段（仅用于展示，不会保存到服务器）
  formApi.setFieldValue(
    '_product_info',
    `${selectedProduct.brand} ${selectedProduct.model} ${selectedProduct.spec || ''}`,
  );

  message.success(`已自动关联产品 [ID: ${selectedProduct.id}]`);
}

// 在表单提交前验证产品关联
function handleBeforeSubmit(formData: any) {
  // 检查product_id是否存在且有效
  if (!formData.product_id) {
    console.error('缺少product_id字段');
    message.error('必须选择有效的PN号码关联产品');
    return false;
  }

  // 确保product_id为数字类型
  formData.product_id = Number(formData.product_id);

  // 检查product_id是否为有效数字
  if (Number.isNaN(formData.product_id) || formData.product_id <= 0) {
    console.error('无效的product_id值:', formData.product_id);
    message.error('产品ID无效，请重新选择PN号码');
    return false;
  }

  return true;
}

// 重写备件创建API，确保正确传递产品ID和仓库ID
function createSpareWithProductId(data: any) {
  try {
    // 从选择的PN获取产品信息
    const pn = data.pn || '';
    const selectedProduct = pnOptions.value.find(
      (item) => item.value === pn,
    )?.product;

    if (!selectedProduct) {
      console.error('找不到关联的产品信息:', pn);
      message.error('找不到关联的产品信息，请重新选择PN');
      return Promise.reject(new Error('找不到产品信息'));
    }

    // 创建一个新对象，仅包含必要的字段
    const submitData: any = {
      sn: data.sn,
      product_id: Number(data.product_id),
      warehouse_id: Number(data.warehouse_id), // 添加仓库ID
      source_type: data.source_type,
      asset_status: data.asset_status,
      hardware_status: data.hardware_status,
      price: Number(data.price) || 0,
      location: data.location || '', // 仍然保留位置信息
      firmware_version: data.firmware_version || '',
      purchase_date: data.purchase_date || '',
      warranty_expire: data.warranty_expire || '',
      remark: data.remark || '',
      related_asset_sn: data.related_asset_sn || '',

      // 从选择的产品复制这些字段
      type: selectedProduct.material_type || '',
      model: selectedProduct.model || '',
      pn: selectedProduct.pn || '',
      brand: selectedProduct.brand || '',
    };

    // 验证关键字段
    if (!submitData.sn) {
      message.error('SN不能为空');
      return Promise.reject(new Error('SN不能为空'));
    }

    if (
      !submitData.product_id ||
      Number.isNaN(submitData.product_id) ||
      submitData.product_id <= 0
    ) {
      message.error('必须选择有效的PN号码关联产品');
      return Promise.reject(new Error('产品ID无效'));
    }

    if (
      !submitData.warehouse_id ||
      Number.isNaN(submitData.warehouse_id) ||
      submitData.warehouse_id <= 0
    ) {
      message.error('必须选择有效的仓库');
      return Promise.reject(new Error('仓库ID无效'));
    }

    // 调用原始API并添加错误处理
    return createSpareApi(submitData)
      .then((res) => {
        return res;
      })
      .catch((error) => {
        console.error('创建备件API错误:', error);
        // 提取错误信息
        const errorMsg =
          error?.response?.data?.message ||
          error?.message ||
          '创建备件失败，服务器内部错误';
        message.error(`创建备件失败: ${errorMsg}`);
        throw error;
      });
  } catch (error) {
    console.error('创建备件数据处理错误:', error);
    message.error('创建备件失败，请检查表单数据');
    return Promise.reject(error);
  }
}

// 存放位置选项
const locationOptions = ref<{ label: string; value: string; warehouse: any }[]>(
  [],
);
const locationOptionsLoading = ref(false);

// 加载存放位置选项
async function loadLocationOptions() {
  try {
    locationOptionsLoading.value = true;

    // 调用API获取仓库列表
    const res = await getWarehouseListApi({
      page: 1,
      pageSize: 100,
    });

    if (res && Array.isArray(res.list)) {
      // 转换为选项格式 - 将仓库ID保存在value字段，同时保存warehouse信息用于后续获取
      locationOptions.value = res.list.map((item: any) => ({
        label: `${item.name} [${item.code}]${item.room ? ` - ${item.room.name}` : ''}`,
        value: String(item.ID || item.id), // 兼容大写ID和小写id
        warehouse: item, // 保存整个仓库对象以便后续使用
      }));
    } else {
      console.error('加载存放位置选项失败，响应数据格式不正确:', res);
      message.error('加载存放位置选项失败');
      locationOptions.value = [];
    }
  } catch (error) {
    console.error('加载存放位置选项异常:', error);
    message.error('加载存放位置选项出错');
    locationOptions.value = [];
  } finally {
    locationOptionsLoading.value = false;
  }
}

// 当选择仓库时自动填充仓库ID
function handleLocationChange(value: string) {
  if (!value) {
    // 当清空仓库时，也清空warehouse_id
    const formApi = commonListRef.value?.formApi;
    if (formApi) {
      formApi.setFieldValue('warehouse_id', undefined);
      formApi.setFieldValue('_warehouse_info', '');
    }
    return;
  }

  // 获取表单实例
  const formApi = commonListRef.value?.formApi;
  if (!formApi) {
    console.error('无法获取表单实例');
    return;
  }

  // 查找对应的仓库信息
  const selectedOption = locationOptions.value.find(
    (item) => item.value === value,
  );
  if (!selectedOption) {
    console.error('无法根据ID找到仓库选项:', value);
    message.error('无法找到对应的仓库信息，请重新选择仓库');
    formApi.setFieldValue('warehouse_id', undefined);
    formApi.setFieldValue('_warehouse_info', '');
    return;
  }

  const selectedWarehouse = selectedOption.warehouse;

  // 检查仓库ID (兼容大写ID和小写id字段)
  const warehouseId = selectedWarehouse.ID || selectedWarehouse.id;

  if (!warehouseId) {
    console.error('仓库数据不完整:', selectedWarehouse);
    message.error('仓库数据不完整，请选择其他仓库');
    formApi.setFieldValue('warehouse_id', undefined);
    formApi.setFieldValue('_warehouse_info', '');
    return;
  }

  // 设置仓库ID - 建立备件与仓库的关联
  formApi.setFieldValue('warehouse_id', Number(warehouseId));

  // 填充表单显示字段（用于展示）
  formApi.setFieldValue(
    '_warehouse_info',
    `${selectedWarehouse.name} [${selectedWarehouse.code}]`,
  );

  message.success(`已自动关联仓库 [ID: ${warehouseId}]`);
}

// 默认数据
const defaultData = {
  sn: '',
  product_id: 0,
  _product_info: '', // 仅用于表单展示
  warehouse_id: 0, // 添加仓库ID字段
  _warehouse_info: '', // 仅用于表单展示
  source_type: '',
  asset_status: 'pending_in',
  hardware_status: 'normal',
  price: 0,
  location: '',
  firmware_version: '',
  purchase_date: '',
  warranty_expire: '',
  remark: '',
  related_asset_id: '',
  related_asset_sn: '',
};

// 表格搜索表单
const formOptions: VbenFormProps = {
  collapsed: true,
  collapsedRows: 3,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入SN' },
      fieldName: 'sn',
      label: 'SN',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择或输入类型',
        options: typeOptions,
        allowClear: true,
        showSearch: true,
        mode: 'tags',
        filterOption: (input: string, option: any) =>
          option.label.toLowerCase().includes(input.toLowerCase()),
      },
      fieldName: 'type',
      label: '类型',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择来源类型',
        options: sourceTypeOptions,
        allowClear: true,
      },
      fieldName: 'source_type',
      label: '来源类型',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择资产状态',
        options: assetStatusOptions,
        allowClear: true,
      },
      fieldName: 'asset_status',
      label: '资产状态',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择硬件状态',
        options: hardwareStatusOptions,
        allowClear: true,
      },
      fieldName: 'hardware_status',
      label: '硬件状态',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择PN号码',
        options: pnOptions,
        allowClear: true,
        showSearch: true,
        filterOption: (input: string, option: any) =>
          option.label.toLowerCase().includes(input.toLowerCase()),
        onChange: handlePnChange,
      },
      fieldName: 'pn',
      label: 'PN号码',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择规格',
        options: specOptions,
        allowClear: true,
        showSearch: true,
        filterOption: (input: string, option: any) =>
          option.label.toLowerCase().includes(input.toLowerCase()),
        loading: specOptionsLoading,
      },
      fieldName: 'spec',
      label: '规格',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择仓库',
        options: locationOptions,
        allowClear: true,
        showSearch: true,
        filterOption: (input: string, option: any) =>
          option.label.toLowerCase().includes(input.toLowerCase()),
        onChange: handleLocationChange,
      },
      fieldName: 'warehouse_id',
      label: '选择仓库',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入固件版本' },
      fieldName: 'firmware_version',
      label: '固件版本',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入批次号' },
      fieldName: 'batch_number',
      label: '批次号',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入关联资产SN' },
      fieldName: 'related_asset_sn',
      label: '关联资产SN',
    },
    {
      component: 'InputNumber',
      componentProps: {
        placeholder: '最低价格',
        min: 0,
        precision: 2,
        style: { width: '100%' },
      },
      fieldName: 'min_price',
      label: '最低价格',
    },
    {
      component: 'InputNumber',
      componentProps: {
        placeholder: '最高价格',
        min: 0,
        precision: 2,
        style: { width: '100%' },
      },
      fieldName: 'max_price',
      label: '最高价格',
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '购买日期开始',
        valueFormat: 'YYYY-MM-DD',
        style: { width: '100%' },
      },
      fieldName: 'purchase_date_start',
      label: '购买日期起',
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '购买日期结束',
        valueFormat: 'YYYY-MM-DD',
        style: { width: '100%' },
      },
      fieldName: 'purchase_date_end',
      label: '购买日期止',
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '保修期开始',
        valueFormat: 'YYYY-MM-DD',
        style: { width: '100%' },
      },
      fieldName: 'warranty_expire_start',
      label: '保修期起',
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '保修期结束',
        valueFormat: 'YYYY-MM-DD',
        style: { width: '100%' },
      },
      fieldName: 'warranty_expire_end',
      label: '保修期止',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: true,
};

// 表格配置
const gridOptions: VxeGridProps<AssetSpare> = {
  checkboxConfig: {
    highlight: true,
  },
  exportConfig: { modes: ['current', 'selected', 'all'], type: 'csv' }, // 导出配置
  importConfig: {
    types: ['csv'],
    remote: true, // 使用服务端导入
  }, // 导入配置
  columns: [
    { align: 'center', fixed: 'left', type: 'checkbox', width: 40 },
    { field: 'id', title: 'ID', width: 60, fixed: 'left' },
    { field: 'sn', title: 'SN', fixed: 'left', width: 140 },
    { field: 'product.material_type', title: '类型', width: 100 },
    { field: 'product.brand', title: '品牌', width: 100 },

    { field: 'product.pn', title: 'PN号码', width: 220 },
    { field: 'product.spec', title: '规格', width: 260 },
    {
      field: 'source_type',
      title: '来源类型',
      width: 100,
      slots: { default: 'source_type' },
    },
    {
      field: 'asset_status',
      title: '资产状态',
      width: 100,
      slots: { default: 'asset_status' },
    },
    {
      field: 'hardware_status',
      title: '硬件状态',
      width: 100,
      slots: { default: 'hardware_status' },
    },
    { field: 'product.model', title: '型号', width: 120 },

    { field: 'warehouse.name', title: '仓库名称', width: 220 },
    { field: 'location', title: '存放位置', width: 140 },

    { field: 'remark', title: '备注', minWidth: 180, visible: false },

    {
      field: 'firmware_version',
      title: '固件版本',
      width: 160,
      visible: false,
    },
    { field: 'price', title: '金额', width: 100 },
    { field: 'warranty_expire', title: '过保时间', width: 140, visible: false },
    { field: 'purchase_date', title: '购买时间', width: 140, visible: false },
    { field: 'created_at', title: '创建时间', width: 140, visible: false },
    { field: 'updated_at', title: '更新时间', width: 140, visible: false },
    {
      title: '操作',
      slots: { default: 'operate' },
      width: 200,
      fixed: 'right',
    },
  ],
  keepSource: true,
  height: 'auto',
  rowConfig: { isHover: true },
  pagerConfig: { pageSize: 10, pageSizes: [10, 20, 50, 100, 500, 1000] },
  toolbarConfig: {
    custom: true,
    refresh: true,
    export: true,
    zoom: true,
    import: true,
    slots: {
      buttons: 'toolbar-buttons',
    },
  },
};

// 处理详情事件
function handleDetail(row: AssetSpare) {
  detailData.value = row;
  detailRef.value?.drawerApi.open();
}

// 自定义权限配置
const permissions = {
  add: ['AssetMgt:Spares:Create'],
  edit: ['AssetMgt:Spares:Edit'],
  delete: ['AssetMgt:Spares:Delete'],
  import: ['AssetMgt:Spares:Import'],
};

// 新增/编辑表单配置
const editFormOptions: VbenFormProps = {
  showDefaultActions: false,
  wrapperClass: 'grid grid-cols-2 gap-4',
  schema: [
    {
      fieldName: 'sn',
      label: 'SN',
      component: 'Input',
      componentProps: {
        placeholder: '请输入SN',
      },
    },
    {
      fieldName: 'pn',
      label: 'PN号码',
      component: 'Select',
      componentProps: {
        placeholder: '请选择PN号码',
        options: pnOptions,
        allowClear: true,
        showSearch: true,
        filterOption: (input: string, option: any) =>
          option.label.toLowerCase().includes(input.toLowerCase()),
        onChange: handlePnChange,
        style: { width: '100%' },
      },
      rules: 'required',
    },
    {
      fieldName: '_product_info',
      label: '产品信息',
      component: 'Input',
      componentProps: {
        placeholder: '选择PN后自动填充',
        disabled: true,
      },
    },
    {
      fieldName: 'location',
      label: '存放位置',
      component: 'Select',
      componentProps: {
        placeholder: '请选择存放位置',
        options: locationOptions,
        allowClear: true,
        showSearch: true,
        filterOption: (input: string, option: any) =>
          option.label.toLowerCase().includes(input.toLowerCase()),
        onChange: handleLocationChange,
        style: { width: '100%' },
      },
      rules: 'required',
    },
    {
      fieldName: '_warehouse_info',
      label: '仓库信息',
      component: 'Input',
      componentProps: {
        placeholder: '选择存放位置后自动填充',
        disabled: true,
      },
    },
    {
      fieldName: 'firmware_version',
      label: '固件版本',
      component: 'Input',
      componentProps: {
        placeholder: '请输入固件版本',
      },
    },
    {
      fieldName: 'source_type',
      label: '来源类型',
      component: 'Select',
      componentProps: {
        placeholder: '请选择来源类型',
        options: sourceTypeOptions,
        optionLabelProp: 'label', // 修改为label
        // 自定义渲染选项，添加颜色标签
        optionRender: ({ option }: { option: any }) => {
          return h('div', {}, [
            h(
              Tag,
              {
                color: sourceTypeMap[option.value]?.color || 'default',
                style: { marginRight: '5px' },
              },
              () =>
                sourceTypeMap[option.value]?.text ||
                option.label ||
                option.value,
            ),
          ]);
        },
        style: { width: '50%' },
      },
      rules: 'required',
    },

    {
      fieldName: 'asset_status',
      label: '资产状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择资产状态',
        options: assetStatusOptions,
        optionLabelProp: 'label', // 修改为label
        // 自定义渲染选项，添加颜色标签
        optionRender: ({ option }: { option: any }) => {
          return h('div', {}, [
            h(
              Tag,
              {
                color: assetStatusMap[option.value]?.color || 'default',
                style: { marginRight: '5px' },
              },
              () =>
                assetStatusMap[option.value]?.text ||
                option.label ||
                option.value,
            ),
          ]);
        },
      },
      rules: 'required',
    },
    {
      fieldName: 'hardware_status',
      label: '硬件状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择硬件状态',
        options: hardwareStatusOptions,
        optionLabelProp: 'label', // 修改为label
        // 自定义渲染选项，添加颜色标签
        optionRender: ({ option }: { option: any }) => {
          return h('div', {}, [
            h(
              Tag,
              {
                color: hardwareStatusMap[option.value]?.color || 'default',
                style: { marginRight: '5px' },
              },
              () =>
                hardwareStatusMap[option.value]?.text ||
                option.label ||
                option.value,
            ),
          ]);
        },
      },
      rules: 'required',
    },
    {
      fieldName: 'purchase_date',
      label: '购买时间',
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择购买时间',
        valueFormat: 'YYYY-MM-DD',
        style: { width: '100%' },
      },
    },
    {
      fieldName: 'warranty_expire',
      label: '过保时间',
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择过保时间',
        valueFormat: 'YYYY-MM-DD',
        style: { width: '100%' },
      },
    },
    {
      fieldName: 'price',
      label: '金额',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入金额',
        style: { width: '100%' },
        min: 0,
        precision: 2,
      },
    },
    {
      fieldName: 'related_asset_sn',
      label: '关联资产SN',
      component: 'Input',
      componentProps: {
        placeholder: '请输入关联资产SN（可选）',
      },
    },
    // 备注
    {
      fieldName: 'remark',
      component: 'Input',
      label: '备注',
      componentProps: {
        placeholder: '请输入备注',
        style: { width: '100%' },
      },
    },

    // 隐藏字段
    {
      fieldName: 'product_id',
      label: '产品ID',
      component: 'InputNumber',
      componentProps: {
        style: { display: 'none' },
      },
      rules: 'required',
    },
    {
      fieldName: 'warehouse_id',
      label: '仓库ID',
      component: 'InputNumber',
      componentProps: {
        style: { display: 'none' },
      },
      rules: 'required',
    },
  ],
};

// 详情字段配置 - 按组显示全部信息
const detailFields = [
  // 基本信息组
  { label: 'ID', field: 'id', group: '基本信息' },
  { label: 'SN', field: 'sn', group: '基本信息' },
  { label: '类型', field: 'type', group: '基本信息' },
  { label: '品牌', field: 'brand', group: '基本信息' },
  { label: '型号', field: 'model', group: '基本信息' },
  {
    label: 'PN号码',
    field: 'product.pn',
    format: (value: any, row: any) => value || row.pn || '暂无',
    group: '基本信息',
  },
  { label: '固件版本', field: 'firmware_version', group: '基本信息' },

  // 状态信息组
  {
    label: '资产状态',
    field: 'asset_status',
    type: 'status' as const,
    statusMap: assetStatusMap,
    group: '状态信息',
  },
  {
    label: '硬件状态',
    field: 'hardware_status',
    type: 'status' as const,
    statusMap: hardwareStatusMap,
    group: '状态信息',
  },
  {
    label: '来源类型',
    field: 'source_type',
    type: 'status' as const,
    statusMap: sourceTypeMap,
    group: '状态信息',
  },

  // 财务信息组
  { label: '金额', field: 'price', group: '财务信息' },
  { label: '购买时间', field: 'purchase_date', group: '财务信息' },
  { label: '过保时间', field: 'warranty_expire', group: '财务信息' },

  // 位置信息组
  {
    label: '仓库名称',
    field: 'warehouse.name',
    format: (value: any) => value || '暂无',
    group: '位置信息',
  },
  {
    label: '仓库编码',
    field: 'warehouse.code',
    format: (value: any) => value || '暂无',
    group: '位置信息',
  },
  {
    label: '仓库类型',
    field: 'warehouse.type',
    format: (value: any) => value || '暂无',
    group: '位置信息',
  },
  { label: '存放位置', field: 'location', group: '位置信息' },

  // 产品信息组
  {
    label: '产品类型',
    field: 'product.material_type',
    format: (value: any) => value || '暂无',
    group: '产品信息',
  },
  {
    label: '产品品牌',
    field: 'product.brand',
    format: (value: any) => value || '暂无',
    group: '产品信息',
  },
  {
    label: '产品型号',
    field: 'product.model',
    format: (value: any) => value || '暂无',
    group: '产品信息',
  },
  {
    label: '产品规格',
    field: 'product.spec',
    format: (value: any) => value || '暂无',
    group: '产品信息',
  },
  {
    label: '产品类别',
    field: 'product.product_category',
    format: (value: any) => value || '暂无',
    group: '产品信息',
  },

  // 关联信息组
  {
    label: '关联资产ID',
    field: 'related_asset_id',
    format: (value: any) => (value > 0 ? value : '无关联资产'),
    group: '关联信息',
  },
  {
    label: '关联资产SN',
    field: 'related_asset_sn',
    format: (value: any) => value || '无关联资产',
    group: '关联信息',
  },

  // 其他信息组
  { label: '备注', field: 'remark', group: '其他信息' },
  { label: '创建时间', field: 'created_at', group: '其他信息' },
  { label: '更新时间', field: 'updated_at', group: '其他信息' },
];

// 编辑事件 - 在handlePnChange函数之后添加
function handleEditSpare(row: any) {
  // 将会被CommonList组件的editEvent调用，可以在这里进行特殊处理
  // 在这里预设表单数据以确保PN号码和仓库信息正确显示
  if (commonListRef.value) {
    // 获取当前产品PN和仓库对应的选项
    const productPn = row.product?.pn;
    const warehouseId = String(
      row.warehouse?.ID || row.warehouse?.id || row.warehouse_id,
    );

    // 编辑前先设置表单初始值
    const initialValues = {
      ...row,
      pn: productPn, // 设置PN号码
      location: warehouseId, // 设置仓库ID
      _product_info: row.product
        ? `${row.product.brand || ''} ${row.product.model || ''} ${row.product.spec || ''}`
        : '',
      _warehouse_info: row.warehouse
        ? `${row.warehouse.name || ''} [${row.warehouse.code || ''}]`
        : '',
    };

    // 直接调用CommonList的编辑方法
    commonListRef.value.editEvent(initialValues);
    return true; // 表示已处理
  }

  return false; // 表示未处理
}
</script>

<template>
  <div>
    <!-- 使用通用列表组件 -->
    <CommonList
      ref="commonListRef"
      :form-options="formOptions"
      :grid-options="gridOptions"
      :edit-form-options="editFormOptions"
      :api="{
        getList: getSpareListApi,
        add: createSpareWithProductId,
        edit: wrappedUpdateSpareApi,
        delete: deleteSpareApi,
      }"
      :default-data="defaultData"
      @detail="handleDetail"
      :permissions="permissions"
      :before-submit="handleBeforeSubmit"
      show-add-button
      show-delete-button
      show-detail-button
      :enable-confirm="true"
      import-model-type="spare"
      show-import-button
      show-template-button
      drawer-class="w-1/2"
    >
      <!-- 资产状态插槽 -->
      <template #asset_status="{ row }">
        <Tag :color="assetStatusMap[row.asset_status]?.color || 'default'">
          {{
            assetStatusMap[row.asset_status]?.text ||
            row.asset_status ||
            '未知状态'
          }}
        </Tag>
      </template>

      <!-- 硬件状态插槽 -->
      <template #hardware_status="{ row }">
        <Tag
          :color="hardwareStatusMap[row.hardware_status]?.color || 'default'"
        >
          {{
            hardwareStatusMap[row.hardware_status]?.text ||
            row.hardware_status ||
            '未知状态'
          }}
        </Tag>
      </template>

      <!-- 来源类型插槽 -->
      <template #source_type="{ row }">
        <Tag :color="sourceTypeMap[row.source_type]?.color || 'default'">
          {{
            sourceTypeMap[row.source_type]?.text ||
            row.source_type ||
            '未知来源'
          }}
        </Tag>
      </template>

      <!-- 自定义操作插槽 -->
      <template #operate="{ row }">
        <AccessControl
          :codes="[
            'super',
            'hardware_repair_manager',
            'admin',
            'asset_manager',
          ]"
        >
          <Button type="link" @click="handleEditSpare(row)">编辑</Button>
        </AccessControl>
        <Button type="link" @click="handleDetail(row)">详情</Button>
      </template>
    </CommonList>

    <!-- 使用通用详情组件 -->
    <CommonDetail
      ref="detailRef"
      :data="detailData"
      title="备件详情"
      :fields="detailFields"
      show-audit-log
      table-name="asset_spares"
      show-group
      class="w-1/2"
    />
  </div>
</template>
