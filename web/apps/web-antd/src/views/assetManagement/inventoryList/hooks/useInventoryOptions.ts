import { ref } from 'vue';

import { message } from 'ant-design-vue';

import { getWarehouseListApi } from '#/api/core/cmdb/asset/warehouse';
import {
  getBrandsApi,
  getMaterialTypesApi,
  getProductListApi,
} from '#/api/core/cmdb/product/product';

// 使用模块级别的响应式状态，确保只有一个实例
// 选项列表
const materialTypeOptions = ref<{ label: string; value: string }[]>([]);
const pnOptions = ref<{ label: string; product: any; value: string }[]>([]);
const pnOptionsLoading = ref(false);
const specOptions = ref<{ label: string; value: string }[]>([]);
const specOptionsLoading = ref(false);
const locationOptions = ref<{ label: string; value: string; warehouse: any }[]>(
  [],
);
const locationOptionsLoading = ref(false);
const brandOptions = ref<{ label: string; value: string }[]>([]);
const brandOptionsLoading = ref(false);

// 标记是否已初始化
let isInitialized = false;

export function useInventoryOptions() {
  // 加载规格选项
  async function loadSpecOptions() {
    try {
      specOptionsLoading.value = true;

      // 调用API获取产品列表
      const res = await getProductListApi({
        page: 1,
        pageSize: 1000,
      });

      // 处理响应数据
      if (res && Array.isArray(res.list)) {
        // 收集所有不重复的规格
        const uniqueSpecs = new Set<string>();

        res.list.forEach((item: any) => {
          if (
            item.spec &&
            typeof item.spec === 'string' &&
            item.spec.trim() !== ''
          ) {
            uniqueSpecs.add(item.spec.trim());
          }
        });

        // 转换为选项格式
        specOptions.value = [...uniqueSpecs].map((spec: string) => ({
          label: spec,
          value: spec,
        }));

        // 按照规格名称排序
        specOptions.value.sort((a, b) => a.label.localeCompare(b.label));
      } else {
        console.error('加载规格选项失败，响应数据格式不正确:', res);
        message.error('加载规格选项失败，请刷新页面重试');
        specOptions.value = [];
      }
    } catch (error) {
      console.error('加载规格选项异常:', error);
      message.error('加载规格选项出错，请刷新页面重试');
      specOptions.value = [];
    } finally {
      specOptionsLoading.value = false;
    }
  }

  // 加载PN选项
  async function loadPnOptions() {
    try {
      pnOptionsLoading.value = true;

      // 调用API获取产品列表
      const res = await getProductListApi({
        page: 1,
        pageSize: 1000,
      });

      // 处理响应数据
      if (res && Array.isArray(res.list)) {
        // 过滤有效的产品（必须具有ID和PN）
        const validProducts = res.list.filter((item: any) => {
          if (!item.id || !item.pn) {
            console.warn('发现无效产品数据:', item);
            return false;
          }
          return true;
        });

        // 转换为选项格式
        pnOptions.value = validProducts.map((item: any) => ({
          label: `${item.pn} [ID:${item.id}] - ${item.brand || ''} ${item.model || ''}`,
          value: item.pn,
          product: item,
        }));
      } else {
        console.error('加载PN选项失败，响应数据格式不正确:', res);
        message.error('加载PN选项失败，请刷新页面重试');
        pnOptions.value = [];
      }
    } catch (error) {
      console.error('加载PN选项异常:', error);
      message.error('加载PN选项出错，请刷新页面重试');
      pnOptions.value = [];
    } finally {
      pnOptionsLoading.value = false;
    }
  }

  // 加载存放位置选项
  async function loadLocationOptions() {
    try {
      locationOptionsLoading.value = true;

      // 调用API获取仓库列表
      const res = await getWarehouseListApi({
        page: 1,
        pageSize: 100,
      });

      if (res && Array.isArray(res.list)) {
        // 转换为选项格式
        locationOptions.value = res.list.map((item: any) => ({
          label: `${item.name} [${item.code}]${item.room ? ` - ${item.room.name}` : ''}`,
          value: String(item.ID || item.id),
          warehouse: item,
        }));
      } else {
        console.error('加载存放位置选项失败，响应数据格式不正确:', res);
        message.error('加载存放位置选项失败');
        locationOptions.value = [];
      }
    } catch (error) {
      console.error('加载存放位置选项异常:', error);
      message.error('加载存放位置选项出错');
      locationOptions.value = [];
    } finally {
      locationOptionsLoading.value = false;
    }
  }

  // 加载品牌选项
  async function loadBrandOptions() {
    try {
      brandOptionsLoading.value = true;

      // 调用API获取品牌列表
      const brandsRes = await getBrandsApi();

      if (brandsRes && Array.isArray(brandsRes)) {
        brandOptions.value = brandsRes.map((brand: string) => ({
          label: brand,
          value: brand,
        }));
      } else {
        console.error('加载品牌选项失败，响应数据格式不正确:', brandsRes);
        message.error('加载品牌选项失败，请刷新页面重试');
        brandOptions.value = [];
      }
    } catch (error) {
      console.error('加载品牌选项异常:', error);
      message.error('加载品牌选项出错，请刷新页面重试');
      brandOptions.value = [];
    } finally {
      brandOptionsLoading.value = false;
    }
  }

  // 加载物料类型
  async function loadMaterialTypeOptions() {
    try {
      // 获取产品物料类型选项
      const materialTypesRes = await getMaterialTypesApi();
      if (materialTypesRes && Array.isArray(materialTypesRes)) {
        materialTypeOptions.value = materialTypesRes.map((type: string) => ({
          label: type,
          value: type,
        }));
      }
    } catch (error) {
      console.error('获取物料类型选项失败:', error);
      message.error('获取物料类型选项失败');
      materialTypeOptions.value = [];
    }
  }

  // 初始化所有选项
  async function initAllOptions() {
    if (isInitialized) {
      return;
    }

    try {
      await Promise.all([
        loadMaterialTypeOptions(),
        loadLocationOptions(),
        loadPnOptions(),
        loadSpecOptions(),
        loadBrandOptions(),
      ]);
      isInitialized = true;
    } catch (error) {
      console.error('初始化选项数据失败:', error);
      message.error('初始化选项数据失败');
    }
  }

  return {
    materialTypeOptions,
    pnOptions,
    pnOptionsLoading,
    specOptions,
    specOptionsLoading,
    locationOptions,
    locationOptionsLoading,
    brandOptions,
    brandOptionsLoading,
    loadSpecOptions,
    loadPnOptions,
    loadLocationOptions,
    loadBrandOptions,
    loadMaterialTypeOptions,
    initAllOptions,
  };
}
