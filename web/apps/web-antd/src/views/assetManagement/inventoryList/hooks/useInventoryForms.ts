import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { InventoryDetail } from '#/api/core/cmdb/inventory/inventory';

import { h } from 'vue';

import XEUtils from 'xe-utils';

import { useInventoryOptions } from './useInventoryOptions';

// 美化库存数量显示的渲染函数
const renderStockNumber = (value: number, _row: any) => {
  return h(
    'span',
    {
      class: {
        'font-bold': true,
        'text-green-600': value > 0,
        'text-red-600': value === 0,
      },
      style: {
        fontSize: '14px',
      },
    },
    value.toString(),
  );
};

export function useInventoryForms() {
  const {
    materialTypeOptions,
    pnOptions,
    pnOptionsLoading,
    specOptions,
    specOptionsLoading,
    locationOptions,
    locationOptionsLoading,
    brandOptions,
    brandOptionsLoading,
  } = useInventoryOptions();

  // 表格搜索表单配置
  const formOptions: VbenFormProps = {
    collapsed: true,
    collapsedRows: 3,
    schema: [
      {
        component: 'Select',
        componentProps: {
          placeholder: '请选择物料类型',
          options: materialTypeOptions,
          allowClear: true,
          showSearch: true,
          filterOption: (input: string, option: any) =>
            option.label.toLowerCase().includes(input.toLowerCase()),
        },
        fieldName: 'materialType',
        label: '物料类型',
      },
      {
        component: 'Select',
        componentProps: {
          placeholder: '请选择PN号码',
          options: pnOptions,
          allowClear: true,
          showSearch: true,
          filterOption: (input: string, option: any) =>
            option.label.toLowerCase().includes(input.toLowerCase()),
          loading: pnOptionsLoading,
        },
        fieldName: 'pn',
        label: 'PN号码',
      },
      {
        component: 'Select',
        componentProps: {
          placeholder: '请选择品牌',
          options: brandOptions,
          allowClear: true,
          showSearch: true,
          filterOption: (input: string, option: any) =>
            option.label.toLowerCase().includes(input.toLowerCase()),
          loading: brandOptionsLoading,
        },
        fieldName: 'brand',
        label: '品牌',
      },
      {
        component: 'Select',
        componentProps: {
          placeholder: '请选择规格',
          options: specOptions,
          allowClear: true,
          showSearch: true,
          filterOption: (input: string, option: any) =>
            option.label.toLowerCase().includes(input.toLowerCase()),
          loading: specOptionsLoading,
        },
        fieldName: 'spec',
        label: '规格',
      },
      {
        component: 'Select',
        componentProps: {
          placeholder: '请选择仓库',
          options: locationOptions,
          allowClear: true,
          showSearch: true,
          filterOption: (input: string, option: any) =>
            option.label.toLowerCase().includes(input.toLowerCase()),
          loading: locationOptionsLoading,
        },
        fieldName: 'warehouseId',
        label: '仓库',
      },
      {
        component: 'Select',
        componentProps: {
          placeholder: '是否有可用库存',
          options: [
            { label: '有可用库存', value: 'true' },
            { label: '无可用库存', value: 'false' },
          ],
          allowClear: true,
        },
        fieldName: 'hasAvailable',
        label: '是否有可用库存',
      },
    ],
    submitButtonOptions: { content: '查询' },
    showCollapseButton: true,
    submitOnChange: false,
    submitOnEnter: true,
  };

  // 表格配置
  const gridOptions: VxeGridProps<InventoryDetail> = {
    checkboxConfig: {
      highlight: true,
    },
    exportConfig: {}, // 导出配置
    importConfig: {
      types: ['csv'],
      remote: true,
    }, // 导入配置
    columns: [
      { align: 'center', fixed: 'left', type: 'checkbox', width: 40 },
      { field: 'id', title: 'ID', width: 80, fixed: 'left' },
      { field: 'product.material_type', title: '物料类型', width: 100 },

      { field: 'product.pn', title: 'PN号', width: 120 },
      { field: 'product.spec', title: '规格', width: 150 },
      { field: 'product.brand', title: '品牌', width: 120 },
      {
        field: 'warehouse',
        title: '仓库',
        width: 120,
      },

      {
        field: 'current_stock',
        title: '当前库存',
        width: 100,
        titlePrefix: { message: '总库存: 不包括已报废和待报废、已售卖的资产' },
        slots: {
          default: ({ row }) => renderStockNumber(row.current_stock, row),
        },
      },
      {
        field: 'allocated_stock',
        title: '已分配',
        width: 100,
        titlePrefix: {
          message:
            '已分配库存: 包括"使用中"、"维修中"、"待出库"和"已出库"状态的资产',
        },
        slots: {
          default: ({ row }) => renderStockNumber(row.allocated_stock, row),
        },
      },
      {
        field: 'available_stock',
        title: '可用库存',
        width: 100,
        titlePrefix: { message: '可用库存: 包括"闲置中"和"已入库"状态的资产' },
        slots: {
          default: ({ row }) => renderStockNumber(row.available_stock, row),
        },
      },
      { field: 'product.product_category', title: '产品类别', width: 120 },

      { field: 'product.model', title: '产品型号', width: 150 },
      {
        field: 'warehouse_location',
        title: '库位',
        width: 120,
      },

      { field: 'created_at', title: '创建时间', width: 180, visible: false },
      { field: 'updated_at', title: '更新时间', width: 180, visible: false },
      {
        title: '操作',
        slots: { default: 'operate' },
        width: 120,
        fixed: 'right',
      },
    ],
    keepSource: true,
    height: 'auto',
    rowConfig: { isHover: true },
    pagerConfig: { pageSize: 10, pageSizes: [10, 20, 50, 100] },
    toolbarConfig: {
      custom: true,
      refresh: true,
      export: true,
      zoom: true,
      import: false,
      slots: {
        buttons: 'toolbar-buttons',
      },
    },
    showFooter: true,
    footerMethod({ columns, data }) {
      return [
        columns.map((column, columnIndex) => {
          if (columnIndex === 0) {
            return '总计';
          }

          if (column.field === 'current_stock') {
            const total = XEUtils.sum(data, 'current_stock') || 0;
            return total;
          }

          if (column.field === 'allocated_stock') {
            const total = XEUtils.sum(data, 'allocated_stock') || 0;
            return total;
          }

          if (column.field === 'available_stock') {
            const total = XEUtils.sum(data, 'available_stock') || 0;
            return total;
          }

          return '';
        }),
      ];
    },
    footerCellClassName({ column }) {
      if (
        column.field === 'current_stock' ||
        column.field === 'allocated_stock' ||
        column.field === 'available_stock'
      ) {
        return 'font-bold text-green-600';
      }
      if (column.type === 'checkbox') {
        return 'font-bold text-blue-600';
      }
      return '';
    },
  };

  // 编辑表单选项
  const editFormOptions: VbenFormProps = {
    showDefaultActions: false,
    schema: [
      // 编辑表单字段将在后续实现
    ],
  };

  return {
    formOptions,
    gridOptions,
    editFormOptions,
  };
}
