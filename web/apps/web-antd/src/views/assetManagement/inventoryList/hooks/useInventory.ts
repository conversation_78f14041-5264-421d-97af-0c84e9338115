import type { InventoryDetail } from '#/api/core/cmdb/inventory/inventory';

import { onMounted, ref } from 'vue';

import { message } from 'ant-design-vue';

import { useInventoryApi } from './useInventoryApi';
import { useInventoryOptions } from './useInventoryOptions';

export function useInventory() {
  // API相关
  const {
    stockHistoryList,
    stockHistoryLoading,
    fetchStockHistory,
    fetchStockHistoryByProductAndWarehouse,
    wrappedApi,
    getInventoryDetail,
    getInventoryByProductAndWarehouse,
  } = useInventoryApi();

  // 选项相关
  const { initAllOptions } = useInventoryOptions();

  // 页面状态
  const loading = ref<boolean>(false);
  const commonListRef = ref();
  const detailVisible = ref(false);
  const detailData = ref<InventoryDetail | null>(null);
  const detailLoading = ref<boolean>(false);
  const error = ref<null | string>(null);

  // 初始化数据
  onMounted(async () => {
    try {
      // 初始化所有选项数据
      await initAllOptions();
    } catch (error) {
      console.error('初始化数据失败:', error);
      message.error('初始化数据失败');
    }
  });

  // 查看详情
  async function handleDetail(row: InventoryDetail) {
    // 显示详情抽屉
    detailLoading.value = true;
    detailVisible.value = true;
    error.value = null;

    try {
      let detailsResponse;

      // 如果有产品ID和仓库ID，优先使用产品ID和仓库ID查询
      if (row.product_id && row.warehouse_id) {
        try {
          detailsResponse = await getInventoryByProductAndWarehouse(
            row.product_id,
            row.warehouse_id,
          );
        } catch {
          console.warn('通过产品ID和仓库ID查询库存失败，尝试使用ID查询');
        }
      }

      // 如果通过产品ID和仓库ID查询失败或没有这些字段，则使用ID查询
      if (!detailsResponse && row.id) {
        detailsResponse = await getInventoryDetail(row.id);
      } else if (!detailsResponse) {
        throw new Error('库存ID无效且产品ID或仓库ID缺失，无法查询详情');
      }

      if (detailsResponse) {
        detailData.value = detailsResponse;
        // 如果有产品ID和仓库ID，优先使用产品ID和仓库ID获取变更历史
        if (detailsResponse.product_id && detailsResponse.warehouse_id) {
          await fetchStockHistoryByProductAndWarehouse(
            detailsResponse.product_id,
            detailsResponse.warehouse_id,
          );
        } else if (detailsResponse.id) {
          // 否则使用库存ID获取变更历史
          await fetchStockHistory(detailsResponse.id);
        }
      } else {
        throw new Error('未找到库存数据');
      }
    } catch (error_: any) {
      const errorMsg = `获取库存详情失败: ${error_.message || '未知错误'}`;
      console.error(errorMsg, error_);
      message.error(errorMsg);
      error.value = errorMsg;

      // 清空数据
      detailData.value = null;
    } finally {
      // 确保无论如何都结束加载状态
      detailLoading.value = false;
    }
  }

  // 关闭详情抽屉
  const closeDetail = () => {
    detailVisible.value = false;
    detailData.value = null;
  };

  // 退回/重试
  const retry = () => {
    error.value = null;
    if (detailData.value?.id) {
      handleDetail(detailData.value as InventoryDetail);
    }
  };

  // 根据时间范围获取变更历史
  async function handleTimeRangeChange(startTime?: string, endTime?: string) {
    if (detailData.value?.product_id && detailData.value?.warehouse_id) {
      await fetchStockHistoryByProductAndWarehouse(
        detailData.value.product_id,
        detailData.value.warehouse_id,
        startTime,
        endTime,
      );
    } else if (detailData.value?.id) {
      await fetchStockHistory(detailData.value.id, startTime, endTime);
    } else {
      message.error('无法获取库存信息');
    }
  }

  return {
    loading,
    commonListRef,
    detailVisible,
    detailData,
    detailLoading,
    error,
    stockHistoryList,
    stockHistoryLoading,
    wrappedApi,
    handleDetail,
    closeDetail,
    retry,
    handleTimeRangeChange,
  };
}
