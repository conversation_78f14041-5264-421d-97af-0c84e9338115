import type { StockHistory } from '#/api/core/cmdb/inventory/inventory';

import { ref } from 'vue';

import { message } from 'ant-design-vue';

import {
  createInventoryApi,
  deleteInventoryApi,
  getInventoryByProductAndWarehouseApi,
  getInventoryDetailApi,
  getInventoryListApi,
  getStockHistoryApi,
  getStockHistoryByProductAndWarehouseApi,
  updateInventoryApi,
} from '#/api/core/cmdb/inventory/inventory';

export function useInventoryApi() {
  // 状态
  const stockHistoryList = ref<StockHistory[]>([]);
  const stockHistoryLoading = ref<boolean>(false);

  // 获取库存变更历史
  async function fetchStockHistory(
    detailId: number,
    startTime?: string,
    endTime?: string,
  ) {
    stockHistoryLoading.value = true;

    try {
      const response = await getStockHistoryApi(detailId, startTime, endTime);
      stockHistoryList.value = response || [];
    } catch (error_: any) {
      console.error('获取库存变更历史失败:', error_);
      message.error(`获取库存变更历史失败: ${error_.message || '未知错误'}`);
      stockHistoryList.value = [];
    } finally {
      stockHistoryLoading.value = false;
    }
  }

  // 根据产品ID和仓库ID获取库存变更历史
  async function fetchStockHistoryByProductAndWarehouse(
    productId: number,
    warehouseId: number,
    startTime?: string,
    endTime?: string,
  ) {
    stockHistoryLoading.value = true;

    try {
      const response = await getStockHistoryByProductAndWarehouseApi(
        productId,
        warehouseId,
        startTime,
        endTime,
      );

      stockHistoryList.value = response || [];
    } catch (error_: any) {
      console.error('获取库存变更历史失败:', error_);
      message.error(`获取库存变更历史失败: ${error_.message || '未知错误'}`);
      stockHistoryList.value = [];
    } finally {
      stockHistoryLoading.value = false;
    }
  }

  // 创建包装的API函数
  const wrappedApi = {
    getList: async (params: any) => {
      try {
        // 处理布尔值转换
        let hasAvailable;
        if (params.hasAvailable === 'true') {
          hasAvailable = true;
        } else if (params.hasAvailable === 'false') {
          hasAvailable = false;
        }

        const response = await getInventoryListApi({
          page: params.page,
          pageSize: params.pageSize,
          productID: params.productID,
          warehouse: params.warehouse,
          materialType: params.materialType,
          pn: params.pn,
          spec: params.spec,
          warehouseId: params.warehouseId,
          hasAvailable,
          brand: params.brand,
        });
        return response;
      } catch {
        return { list: [], total: 0 };
      }
    },
    add: createInventoryApi,
    edit: updateInventoryApi,
    delete: deleteInventoryApi,
  };

  // 获取库存详情
  async function getInventoryDetail(id: number) {
    try {
      return await getInventoryDetailApi(id);
    } catch (error: any) {
      console.error('获取库存详情失败:', error);
      message.error(`获取库存详情失败: ${error.message || '未知错误'}`);
      throw error;
    }
  }

  // 根据产品ID和仓库ID获取库存详情
  async function getInventoryByProductAndWarehouse(
    productId: number,
    warehouseId: number,
  ) {
    try {
      return await getInventoryByProductAndWarehouseApi(productId, warehouseId);
    } catch (error: any) {
      console.error('获取库存详情失败:', error);
      message.error(`获取库存详情失败: ${error.message || '未知错误'}`);
      throw error;
    }
  }

  return {
    stockHistoryList,
    stockHistoryLoading,
    fetchStockHistory,
    fetchStockHistoryByProductAndWarehouse,
    wrappedApi,
    getInventoryDetail,
    getInventoryByProductAndWarehouse,
  };
}
