<script lang="ts" setup>
import type {
  InventoryDetail,
  StockHistory,
} from '#/api/core/cmdb/inventory/inventory';

import {
  Button,
  Card,
  Descriptions,
  Drawer,
  Spin,
  Tabs,
  Tag,
} from 'ant-design-vue';

import StockHistoryList from '#/components/StockHistoryList/index.vue';

import { inventoryStatusMap } from '../constants';

defineProps<{
  data: InventoryDetail | null;
  error: null | string;
  loading: boolean;
  stockHistoryList: StockHistory[];
  stockHistoryLoading: boolean;
  visible: boolean;
}>();

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'retry'): void;
  (e: 'timeRangeChange', startTime?: string, endTime?: string): void;
}>();

// 关闭详情抽屉
const handleClose = () => {
  emit('close');
};

// 重试
const retry = () => {
  emit('retry');
};

// 时间范围变化
const handleTimeRangeChange = (startTime?: string, endTime?: string) => {
  emit('timeRangeChange', startTime, endTime);
};

// 格式化库存数量的函数
const formatStockNumber = (value: number) => {
  return {
    value,
    class: value > 0 ? 'stock-positive' : 'stock-zero',
  };
};
</script>

<template>
  <Drawer
    title="库存详情"
    :visible="visible"
    @close="handleClose"
    width="50%"
    :destroy-on-close="true"
  >
    <Spin :spinning="loading">
      <!-- 错误状态 -->
      <div v-if="error" class="flex flex-col items-center justify-center py-10">
        <div class="mb-4 text-red-500">{{ error }}</div>
        <Button type="primary" @click="retry">重试</Button>
      </div>

      <!-- 无数据状态 -->
      <template v-else-if="!data">
        <div class="flex h-64 items-center justify-center">
          <p>未找到库存信息</p>
        </div>
      </template>

      <!-- 有数据状态 -->
      <template v-else>
        <Card :bordered="false" class="mb-4">
          <template #extra>
            <Tag :color="inventoryStatusMap[data.status]?.color || 'default'">
              {{ inventoryStatusMap[data.status]?.text || data.status }}
            </Tag>
          </template>

          <Descriptions bordered size="middle" :column="3">
            <Descriptions.Item label="物料类型">
              {{ data.product?.material_type || '暂无' }}
            </Descriptions.Item>
            <Descriptions.Item label="品牌">
              {{ data.product?.brand || '暂无' }}
            </Descriptions.Item>
            <Descriptions.Item label="产品型号">
              {{ data.product?.model || '暂无' }}
            </Descriptions.Item>
            <Descriptions.Item label="规格">
              {{ data.product?.spec || '暂无' }}
            </Descriptions.Item>
            <Descriptions.Item label="PN号码">
              {{ data.product?.pn || '暂无' }}
            </Descriptions.Item>
            <Descriptions.Item label="产品类别">
              {{ data.product?.product_category || '暂无' }}
            </Descriptions.Item>
            <Descriptions.Item label="仓库">
              {{ data.warehouse }}
            </Descriptions.Item>
            <Descriptions.Item label="库位">
              {{ data.warehouse_location || '暂无' }}
            </Descriptions.Item>
            <Descriptions.Item label="批次号">
              {{ data.batch_number || '暂无' }}
            </Descriptions.Item>
            <Descriptions.Item label="当前库存">
              <span
                class="stock-number"
                :class="formatStockNumber(data.current_stock).class"
              >
                {{ formatStockNumber(data.current_stock).value }}
              </span>
            </Descriptions.Item>
            <Descriptions.Item label="已分配">
              <span
                class="stock-number"
                :class="formatStockNumber(data.allocated_stock).class"
              >
                {{ formatStockNumber(data.allocated_stock).value }}
              </span>
            </Descriptions.Item>
            <Descriptions.Item label="可用库存">
              <span
                class="stock-number"
                :class="formatStockNumber(data.available_stock).class"
              >
                {{ formatStockNumber(data.available_stock).value }}
              </span>
            </Descriptions.Item>
          </Descriptions>
        </Card>

        <Tabs default-active-key="history">
          <Tabs.TabPane key="history" tab="变更历史">
            <StockHistoryList
              :history-list="stockHistoryList"
              :loading="stockHistoryLoading"
              @time-range-change="handleTimeRangeChange"
            />
          </Tabs.TabPane>
        </Tabs>
      </template>
    </Spin>
  </Drawer>
</template>

<style scoped>
.ant-descriptions-item-label {
  width: 120px;
}

.ant-card {
  box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
}

.stock-number {
  font-size: 16px;
  font-weight: bold;
}

.stock-positive {
  color: #52c41a;
}

.stock-zero {
  color: #ff4d4f;
}
</style>
