<script lang="ts" setup>
import { Tag } from 'ant-design-vue';

import CommonList from '#/components/CommonList/index.vue';

import InventoryDetail from './components/InventoryDetail.vue';
import {
  defaultInventoryData,
  inventoryPermissions,
  inventoryStatusMap,
} from './constants';
import { useInventory } from './hooks/useInventory';
import { useInventoryForms } from './hooks/useInventoryForms';

// 使用抽离的组合式函数
const {
  commonListRef,
  detailVisible,
  detailData,
  detailLoading,
  error,
  stockHistoryList,
  stockHistoryLoading,
  wrappedApi,
  handleDetail,
  closeDetail,
  retry,
  handleTimeRangeChange,
} = useInventory();

// 使用表单配置
const { formOptions, gridOptions, editFormOptions } = useInventoryForms();
</script>

<template>
  <div>
    <!-- 使用通用列表组件 -->
    <CommonList
      ref="commonListRef"
      :form-options="formOptions"
      :grid-options="gridOptions"
      :edit-form-options="editFormOptions"
      :api="wrappedApi"
      :default-data="defaultInventoryData"
      @detail="handleDetail"
      :permissions="inventoryPermissions"
      show-detail-button
      drawer-class="w-1/2"
      import-model-type="inventory"
      :enable-confirm="true"
    >
      <!-- 状态插槽 -->
      <template #status="{ row }">
        <Tag :color="inventoryStatusMap[row.status]?.color || 'default'">
          {{ inventoryStatusMap[row.status]?.text || row.status || '未知状态' }}
        </Tag>
      </template>
    </CommonList>

    <!-- 库存详情组件 -->
    <InventoryDetail
      :visible="detailVisible"
      :loading="detailLoading"
      :data="detailData"
      :error="error"
      :stock-history-list="stockHistoryList"
      :stock-history-loading="stockHistoryLoading"
      @close="closeDetail"
      @retry="retry"
      @time-range-change="handleTimeRangeChange"
    />
  </div>
</template>

<style scoped>
.ant-card {
  box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
}
</style>
