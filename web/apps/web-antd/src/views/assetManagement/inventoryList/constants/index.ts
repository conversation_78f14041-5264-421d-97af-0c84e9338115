// 库存状态映射
export const inventoryStatusMap: {
  [key: string]: { color: string; text: string };
} = {
  active: { text: '正常', color: 'green' },
  pending: { text: '待处理', color: 'orange' },
  reserved: { text: '已预留', color: 'blue' },
  depleted: { text: '已耗尽', color: 'red' },
  expired: { text: '已过期', color: 'gray' },
};

// 默认库存数据
export const defaultInventoryData = {
  product_id: 0,
  warehouse: '',
  warehouse_location: '',
  current_stock: 0,
  allocated_stock: 0,
  available_stock: 0,
  status: 'active',
  warehouse_id: 0,
};

// 自定义权限配置
export const inventoryPermissions = {
  add: ['AssetMgt:Inventory:Create'],
  edit: ['AssetMgt:Inventory:Edit'],
  delete: ['AssetMgt:Inventory:Delete'],
};
