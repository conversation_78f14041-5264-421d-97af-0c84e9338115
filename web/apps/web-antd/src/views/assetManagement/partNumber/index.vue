<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { Product } from '#/api/core/cmdb/product/product';

import { onMounted, ref } from 'vue';

import {
  createProductApi,
  deleteProductApi,
  getBrandsApi,
  getMaterialTypesApi,
  getProductCategoriesApi,
  getProductListApi,
  updateProductApi as originalUpdateProductApi,
} from '#/api/core/cmdb/product/product';
import CommonDetail from '#/components/CommonDetail/index.vue';
import CommonList from '#/components/CommonList/index.vue';

// 为了匹配CommonList组件的API格式，封装更新API
const wrappedUpdateProductApi = (data: any) => {
  return originalUpdateProductApi(data.id, data);
};

const detailRef = ref();
const detailData = ref<null | Product>(null);
const commonListRef = ref();

// 选项列表
const materialTypeOptions = ref<{ label: string; value: string }[]>([]);
const productCategoryOptions = ref<{ label: string; value: string }[]>([]);
const brandOptions = ref<{ label: string; value: string }[]>([]);

// 获取选项数据
async function fetchOptions() {
  try {
    // 获取物料类型
    const materialTypes = await getMaterialTypesApi();
    materialTypeOptions.value = materialTypes.map((type) => ({
      label: type,
      value: type,
    }));

    // 获取产品类别
    const categories = await getProductCategoriesApi();
    productCategoryOptions.value = categories.map((category) => ({
      label: category,
      value: category,
    }));

    // 获取品牌
    const brands = await getBrandsApi();
    brandOptions.value = brands.map((brand) => ({
      label: brand,
      value: brand,
    }));
  } catch (error) {
    console.error('获取选项数据失败:', error);

    // 设置一些默认值以防API失败
    if (materialTypeOptions.value.length === 0) {
      materialTypeOptions.value = [
        { label: 'CPU', value: 'CPU' },
        { label: '内存', value: '内存' },
        { label: '硬盘', value: '硬盘' },
        { label: '显卡', value: '显卡' },
      ];
    }

    if (productCategoryOptions.value.length === 0) {
      productCategoryOptions.value = [
        { label: '服务器', value: '服务器' },
        { label: '网络设备', value: '网络设备' },
      ];
    }

    if (brandOptions.value.length === 0) {
      brandOptions.value = [
        { label: 'Intel', value: 'Intel' },
        { label: 'AMD', value: 'AMD' },
        { label: 'Samsung', value: 'Samsung' },
      ];
    }
  }
}

// 初始化时获取选项数据
onMounted(() => {
  fetchOptions();
});

// 默认数据
const defaultData = {
  material_type: '',
  brand: '',
  model: '',
  pn: '',
  product_category: '',
  spec: '',
};

// 表格搜索表单
const formOptions: VbenFormProps = {
  collapsed: true,
  collapsedRows: 1,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入PN号码' },
      fieldName: 'pn',
      label: 'PN号码',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择物料类型',
        options: materialTypeOptions,
        allowClear: true,
      },
      fieldName: 'material_type',
      label: '物料类型',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择产品类别',
        options: productCategoryOptions,
        allowClear: true,
      },
      fieldName: 'product_category',
      label: '产品类别',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择品牌',
        options: brandOptions,
        allowClear: true,
      },
      fieldName: 'brand',
      label: '品牌',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入规格' },
      fieldName: 'spec',
      label: '规格',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: true,
};

// 表格配置
const gridOptions: VxeGridProps<Product> = {
  checkboxConfig: {
    highlight: true,
    reserve: true,
  },
  exportConfig: {}, // 导出配置
  importConfig: {
    types: ['csv'],
  }, // 导入配置
  columns: [
    { align: 'center', type: 'checkbox', width: 40 },
    { field: 'id', title: 'ID', width: 60 },
    { field: 'pn', title: 'PN号码', width: 150 },
    { field: 'material_type', title: '物料类型', width: 120 },
    { field: 'product_category', title: '产品类别', width: 120 },
    { field: 'brand', title: '品牌', width: 120 },
    { field: 'model', title: '型号', width: 120 },
    { field: 'spec', title: '规格', minWidth: 180 },
    {
      title: '操作',
      slots: { default: 'operate' },
      width: 160,
      fixed: 'right',
    },
  ],
  keepSource: true,
  height: 'auto',
  rowConfig: { isHover: true },
  pagerConfig: { pageSize: 10, pageSizes: [10, 20, 50, 100] },
  toolbarConfig: {
    custom: true,
    refresh: true,
    export: true,
    zoom: true,
    import: true,
    slots: {
      buttons: 'toolbar-buttons',
    },
  },
};

// 新增/编辑表单配置
const editFormOptions: VbenFormProps = {
  showDefaultActions: false,
  schema: [
    {
      fieldName: 'pn',
      label: 'PN号码',
      component: 'Input',
      componentProps: { placeholder: '请输入PN号码' },
      rules: 'required',
    },
    {
      fieldName: 'material_type',
      label: '物料类型',
      component: 'Select',
      componentProps: {
        placeholder: '请选择物料类型',
        options: materialTypeOptions,
        style: {
          width: '50%',
        },
      },
      rules: 'required',
    },
    {
      fieldName: 'product_category',
      label: '产品类别',
      component: 'Select',
      componentProps: {
        placeholder: '请选择产品类别',
        options: productCategoryOptions,
        style: {
          width: '50%',
        },
      },
      rules: 'required',
    },
    {
      fieldName: 'brand',
      label: '品牌',
      component: 'Input',
      componentProps: {
        placeholder: '请输入品牌',
      },
      rules: 'required',
    },
    {
      fieldName: 'model',
      label: '型号',
      component: 'Input',
      componentProps: {
        placeholder: '请输入型号',
      },
    },
    {
      fieldName: 'spec',
      label: '规格',
      component: 'Input',
      componentProps: {
        placeholder: '请输入规格',
      },
      rules: 'required',
    },
  ],
};

// 详情字段配置
const detailFields = [
  { label: 'ID', field: 'id' },
  { label: 'PN号码', field: 'pn' },
  { label: '物料类型', field: 'material_type' },
  { label: '产品类别', field: 'product_category' },
  { label: '品牌', field: 'brand' },
  { label: '型号', field: 'model' },
  { label: '规格', field: 'spec' },
  { label: '创建时间', field: 'created_at' },
  { label: '更新时间', field: 'updated_at' },
];

// 处理详情事件
function handleDetail(row: Product) {
  detailData.value = row;
  detailRef.value?.drawerApi.open();
}

// 自定义权限配置
const permissions = {
  add: ['AssetMgt:SpecificationsInfo:Create'],
  edit: ['AssetMgt:SpecificationsInfo:Edit'],
  delete: ['AssetMgt:SpecificationsInfo:Delete'],
};
</script>

<template>
  <div class="w-full">
    <h1 class="mb-4 text-lg font-bold">PN信息管理</h1>

    <!-- 使用通用列表组件 -->
    <CommonList
      ref="commonListRef"
      :form-options="formOptions"
      :grid-options="gridOptions"
      :edit-form-options="editFormOptions"
      :api="{
        getList: getProductListApi,
        add: createProductApi,
        edit: wrappedUpdateProductApi,
        delete: deleteProductApi,
      }"
      :default-data="defaultData"
      @detail="handleDetail"
      :permissions="permissions"
      show-add-button
      show-edit-button
      show-delete-button
      show-detail-button
      :enable-confirm="true"
    />

    <!-- 使用通用详情组件 -->
    <CommonDetail
      ref="detailRef"
      :data="detailData"
      title="PN详情"
      :fields="detailFields"
      show-audit-log
      table-name="products"
    />
  </div>
</template>
