<script lang="ts" setup>
import type { Key } from 'ant-design-vue/es/vc-table/interface';

import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type {
  InventoryStatistics,
  ServerComponent,
  ServerComponentWithDetails,
  ServerInfo,
} from '#/api/core/cmdb/asset/serverComponent';

import { h, ref } from 'vue';

import {
  Button,
  Card,
  Descriptions,
  Drawer,
  Empty,
  message,
  Spin,
  Table,
  Tabs,
  Tag,
} from 'ant-design-vue';

import {
  addServerComponentApi,
  deleteServerComponentApi,
  getComponentSparesApi,
  getComponentWithDetailsApi,
  getInventoryStatisticsApi,
  getServerComponentByIdApi,
  getServerComponentsApi,
  updateServerComponentApi,
} from '#/api/core/cmdb/asset/serverComponent';
import CommonList from '#/components/CommonList/index.vue';

const detailVisible = ref(false);
const detailData = ref<null | ServerComponentWithDetails>(null);
const commonListRef = ref();
// 添加全局加载状态

const detailLoading = ref<boolean>(false);
const error = ref<null | string>(null);
const inventoryLoading = ref<boolean>(false);
const sparesLoading = ref<boolean>(false);
const activeTab = ref<string>('server');

// 服务器信息
const serverInfo = ref<null | ServerInfo>(null);
// 库存信息
const inventoryStats = ref<InventoryStatistics | null>(null);
// 相关备件
const spares = ref<any[]>([]);
const sparesTotal = ref<number>(0);
const sparesPagination = ref({
  current: 1,
  pageSize: 5,
});

// 组件类型映射
const componentTypeMap: {
  [key: string]: { color: string; text: string };
} = {
  CPU: { text: 'CPU', color: 'blue' },
  GPU: { text: 'GPU', color: 'purple' },
  Memory: { text: '内存', color: 'green' },
  Disk: { text: '磁盘', color: 'orange' },
  NetworkCard: { text: '网卡', color: 'cyan' },
  PowerSupply: { text: '电源', color: 'red' },
  Motherboard: { text: '主板', color: 'volcano' },
  Other: { text: '其他', color: 'default' },
};

// 组件状态映射
const componentStatusMap: {
  [key: string]: { color: string; text: string };
} = {
  normal: { text: '正常', color: 'green' },
  faulty: { text: '故障', color: 'red' },
  repairing: { text: '维修中', color: 'orange' },
  retired: { text: '已报废', color: 'gray' },
};

// 模拟变更历史
const changeHistory = ref<any[]>([
  {
    id: 1,
    date: '2023-10-15',
    type: '安装',
    serverSN: 'SVR001',
    operator: '张三',
    description: '初次安装到服务器',
  },
  {
    id: 2,
    date: '2023-11-20',
    type: '更换',
    serverSN: 'SVR002',
    operator: '李四',
    description: '从SVR001迁移到SVR002',
  },
  {
    id: 3,
    date: '2024-01-05',
    type: '维修',
    serverSN: 'SVR002',
    operator: '王五',
    description: '更换散热硅脂',
  },
]);

// 默认数据
const defaultData = {
  server_id: 0,
  product_id: 0,
  component_type: 'CPU',
  sn: '',
  model: '',
  pn: '',
  firmware_version: '',
  slot_position: '',
  install_date: '',
  status: 'normal',
  description: '',
  extra_info: {},
};

// 表格搜索表单
const formOptions: VbenFormProps = {
  collapsed: true,
  collapsedRows: 2,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入SN/型号/PN' },
      fieldName: 'query',
      label: '关键词',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择组件类型',
        options: Object.entries(componentTypeMap).map(([value, item]) => ({
          label: item.text,
          value,
        })),
        allowClear: true,
      },
      fieldName: 'componentType',
      label: '组件类型',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择组件状态',
        options: Object.entries(componentStatusMap).map(([value, item]) => ({
          label: item.text,
          value,
        })),
        allowClear: true,
      },
      fieldName: 'status',
      label: '组件状态',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入服务器SN' },
      fieldName: 'serverSN',
      label: '所属服务器',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: true,
};

// 表格配置
const gridOptions: VxeGridProps<ServerComponent> = {
  checkboxConfig: {
    highlight: true,
  },
  exportConfig: {}, // 导出配置
  importConfig: {
    types: ['csv'],
    remote: true,
  }, // 导入配置
  columns: [
    { align: 'center', fixed: 'left', type: 'checkbox', width: 40 },
    { field: 'id', title: 'ID', width: 80, fixed: 'left' },
    { field: 'sn', title: '组件SN', fixed: 'left', width: 140 },
    {
      field: 'component_type',
      title: '组件类型',
      width: 100,
      slots: { default: 'componentType' },
    },
    {
      field: 'model',
      title: '型号',
      width: 140,
    },
    {
      field: 'pn',
      title: 'PN号',
      width: 140,
    },
    {
      field: 'status',
      title: '状态',
      slots: { default: 'status' },
      width: 100,
    },
    {
      field: 'firmware_version',
      title: '固件版本',
      width: 120,
    },
    {
      field: 'server.sn',
      title: '服务器SN',
      slots: { default: 'serverSN' },
      width: 140,
    },
    {
      field: 'slot_position',
      title: '安装槽位',
      width: 100,
    },
    {
      field: 'install_date',
      title: '安装日期',
      width: 140,
    },
    { field: 'description', title: '描述', width: 200 },
    { field: 'created_at', title: '创建时间', width: 180, visible: false },
    { field: 'updated_at', title: '更新时间', width: 180, visible: false },
    {
      title: '操作',
      slots: { default: 'operate' },
      width: 200,
      fixed: 'right',
    },
  ],
  keepSource: true,
  height: 'auto',
  rowConfig: { isHover: true },
  pagerConfig: { pageSize: 10, pageSizes: [10, 20, 50, 100] },
  toolbarConfig: {
    custom: true,
    refresh: true,
    export: true,
    zoom: true,
    import: true,
    slots: {
      buttons: 'toolbar-buttons',
    },
  },
};

// 新增/编辑表单配置
const editFormOptions: VbenFormProps = {
  showDefaultActions: false,
  wrapperClass: 'grid grid-cols-2 gap-4',
  schema: [
    {
      fieldName: 'component_type',
      label: '组件类型',
      component: 'Select',
      componentProps: {
        placeholder: '请选择组件类型',
        options: Object.entries(componentTypeMap).map(([value, item]) => ({
          label: item.text,
          value,
        })),
      },
      rules: 'required',
    },
    {
      fieldName: 'sn',
      label: '组件SN',
      component: 'Input',
      componentProps: { placeholder: '请输入组件SN' },
    },
    {
      fieldName: 'model',
      label: '型号',
      component: 'Input',
      componentProps: { placeholder: '请输入型号' },
    },
    {
      fieldName: 'pn',
      label: 'PN号',
      component: 'Input',
      componentProps: { placeholder: '请输入PN号' },
    },
    {
      fieldName: 'firmware_version',
      label: '固件版本',
      component: 'Input',
      componentProps: { placeholder: '请输入固件版本' },
    },
    {
      fieldName: 'server_id',
      label: '所属服务器ID',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入服务器ID',
        min: 0,
      },
    },
    {
      fieldName: 'slot_position',
      label: '安装槽位',
      component: 'Input',
      componentProps: { placeholder: '请输入安装槽位，例如: CPU1' },
    },
    {
      fieldName: 'install_date',
      label: '安装日期',
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择安装日期',
        valueFormat: 'YYYY-MM-DDT00:00:00Z',
      },
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: Object.entries(componentStatusMap).map(([value, item]) => ({
          label: item.text,
          value,
        })),
      },
      rules: 'required',
    },
    {
      fieldName: 'product_id',
      label: '产品ID',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入产品ID',
        min: 0,
      },
    },
    {
      fieldName: 'description',
      label: '描述',
      component: 'Input',
      componentProps: {
        placeholder: '请输入描述',
        rows: 4,
        type: 'textarea',
      },
    },
  ],
};

// 格式化日期函数
const formatDate = (dateString?: string): string => {
  if (!dateString) return '';

  try {
    // 将RFC3339格式的日期转换为本地日期格式
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  } catch {
    return dateString; // 如果转换失败，返回原始字符串
  }
};

// 获取库存统计数据
async function fetchInventoryStats(productId: number) {
  if (!productId) {
    message.warning('该组件没有关联产品，无法获取库存统计');
    inventoryStats.value = null;
    return;
  }

  inventoryLoading.value = true;
  try {
    const response = await getInventoryStatisticsApi(productId);

    if (response) {
      inventoryStats.value = response;
      message.success('库存统计数据获取成功');
    } else {
      inventoryStats.value = null;
      message.info('没有找到相关库存统计数据');
    }
  } catch (error_: any) {
    message.error(`获取库存统计失败: ${error_.message || '未知错误'}`);
    inventoryStats.value = null;
  } finally {
    inventoryLoading.value = false;
  }
}

// 获取组件相关备件
async function fetchComponentSpares() {
  if (!detailData.value?.id) return;

  sparesLoading.value = true;
  try {
    const response = await getComponentSparesApi(
      detailData.value.id,
      sparesPagination.value.current,
      sparesPagination.value.pageSize,
    );

    if (response) {
      spares.value = response.list || [];
      sparesTotal.value = response.total || 0;
    } else {
      spares.value = [];
      sparesTotal.value = 0;
    }
  } catch {
    message.error('获取组件相关备件失败');
    spares.value = [];
    sparesTotal.value = 0;
  } finally {
    sparesLoading.value = false;
  }
}

// 处理备件表格分页变更
function handleSparesTableChange(pagination: any) {
  sparesPagination.value = {
    current: pagination.current,
    pageSize: pagination.pageSize,
  };
  fetchComponentSpares();
}

// 备件状态标签
function getSpareStatusTag(status: string) {
  const statusMap: Record<string, { color: string; text: string }> = {
    idle: { color: 'green', text: '闲置' },
    in_use: { color: 'blue', text: '使用中' },
    repairing: { color: 'orange', text: '维修中' },
    scrapped: { color: 'red', text: '已报废' },
  };

  const statusInfo = statusMap[status] || { color: 'default', text: status };
  return statusInfo;
}

// 硬件状态标签
function getHardwareStatusTag(status: string) {
  const statusMap: Record<string, { color: string; text: string }> = {
    normal: { color: 'green', text: '正常' },
    defect: { color: 'red', text: '故障' },
    warning: { color: 'orange', text: '警告' },
  };

  const statusInfo = statusMap[status] || { color: 'default', text: status };
  return statusInfo;
}

// 查看详情
async function handleDetail(row: ServerComponent) {
  // 检查是否有有效的ID
  if (!row.id) {
    message.error('组件ID无效，无法查看详情');
    return;
  }

  // 显示详情抽屉
  detailLoading.value = true;
  detailVisible.value = true;
  error.value = null;

  try {
    // 使用详情API获取数据
    const detailsResponse = await getComponentWithDetailsApi(row.id);

    if (detailsResponse) {
      detailData.value = detailsResponse;

      // 设置服务器信息
      if (detailsResponse.server_info) {
        serverInfo.value = detailsResponse.server_info;
      } else if (detailsResponse.server) {
        // 兼容旧数据结构
        serverInfo.value = {
          sn: detailsResponse.server?.sn,
          brand: detailsResponse.server?.brand,
          model: detailsResponse.server?.model,
        };
      } else {
        serverInfo.value = null;
      }

      // 设置库存信息
      inventoryStats.value = detailsResponse.inventory_detail
        ? {
            total_stock: detailsResponse.inventory_detail.total_stock,
            in_use_count: detailsResponse.inventory_detail.in_use_count,
            idle_count: detailsResponse.inventory_detail.idle_count,
            good_count: detailsResponse.inventory_detail.good_count,
            defect_count: detailsResponse.inventory_detail.defect_count,
          }
        : null;
    } else {
      // 如果详情API失败，尝试使用基本API
      const response = await getServerComponentByIdApi(row.id);
      if (response) {
        detailData.value = response as ServerComponentWithDetails;
        serverInfo.value = response.server
          ? {
              sn: response.server.sn,
              brand: response.server.brand,
              model: response.server.model,
            }
          : null;

        // 清空库存数据，等待标签页切换时加载
        inventoryStats.value = null;
      } else {
        throw new Error('未找到组件数据');
      }
    }
  } catch (error_: any) {
    const errorMsg = `获取组件详情失败: ${error_.message || '未知错误'}`;
    message.error(errorMsg);
    error.value = errorMsg;

    // 确保在出错时也设置值为null，避免显示旧数据
    detailData.value = null;
    serverInfo.value = null;
    inventoryStats.value = null;
  } finally {
    // 确保无论如何都结束加载状态
    detailLoading.value = false;
  }
}

// 标签页切换事件处理
const handleTabChange = (activeKey: Key) => {
  const tabKey = String(activeKey);
  activeTab.value = tabKey;

  if (
    tabKey === 'inventory' &&
    detailData.value?.product_id &&
    !inventoryStats.value
  ) {
    // 如果切换到库存标签页，且有产品ID但没有库存数据，则获取库存数据
    fetchInventoryStats(detailData.value.product_id);
  } else if (tabKey === 'spares' && detailData.value?.id) {
    // 如果切换到备件标签页，加载相关备件
    fetchComponentSpares();
  }
};

// 如果需要，提供一个重试函数
const retry = () => {
  error.value = null;
  if (detailData.value?.id) {
    handleDetail(detailData.value as ServerComponent);
  }
};

// 关闭详情抽屉
const closeDetail = () => {
  detailVisible.value = false;
  detailData.value = null;
  serverInfo.value = null;
  inventoryStats.value = null;
};

// 自定义权限配置
const permissions = {
  add: ['AssetMgt:AccessoryInfo:Create'],
  edit: [
    'AssetMgt:AccessoryInfo:Edit',
  ],
  delete: [
    'AssetMgt:AccessoryInfo:Delete',
  ],
 
};

// 创建包装的API函数
const wrappedApi = {
  getList: async (params: any) => {
    try {
      const response = await getServerComponentsApi({
        page: params.page,
        pageSize: params.pageSize,
        query: params.query,
        componentType: params.componentType,
      });
      return response;
    } catch {
      return { list: [], total: 0 };
    }
  },
  add: async (data: any) => {
    // 处理日期格式
    const formattedData = { ...data };
    if (
      formattedData.install_date &&
      typeof formattedData.install_date === 'string' &&
      !formattedData.install_date.includes('T')
    ) {
      // 将YYYY-MM-DD格式转换为RFC3339格式
      formattedData.install_date = `${formattedData.install_date}T00:00:00Z`;
    }
    return await addServerComponentApi(formattedData);
  },
  edit: async (data: any) => {
    // 处理日期格式
    const formattedData = { ...data };
    if (
      formattedData.install_date &&
      typeof formattedData.install_date === 'string' &&
      !formattedData.install_date.includes('T')
    ) {
      // 将YYYY-MM-DD格式转换为RFC3339格式
      formattedData.install_date = `${formattedData.install_date}T00:00:00Z`;
    }
    return await updateServerComponentApi(formattedData);
  },
  delete: deleteServerComponentApi,
};
</script>

<template>
  <div>
  
      <!-- 使用通用列表组件 -->
      <CommonList

        ref="commonListRef"
        :form-options="formOptions"
        :grid-options="gridOptions"
        :edit-form-options="editFormOptions"
        :api="wrappedApi"
        :default-data="defaultData"
        @detail="handleDetail"
        :permissions="permissions"
        show-add-button
        show-edit-button
        show-delete-button
        show-detail-button
        drawer-class="w-1/2"
        import-model-type="server-component"
        :enable-confirm="true"
      >
        <!-- 组件类型插槽 -->
        <template #componentType="{ row }">
          <Tag
            :color="componentTypeMap[row.component_type]?.color || 'default'"
          >
            {{
              componentTypeMap[row.component_type]?.text ||
              row.component_type ||
              '未知类型'
            }}
          </Tag>
        </template>

        <!-- 状态插槽 -->
        <template #status="{ row }">
          <Tag :color="componentStatusMap[row.status]?.color || 'default'">
            {{
              componentStatusMap[row.status]?.text || row.status || '未知状态'
            }}
          </Tag>
        </template>

        <!-- 服务器SN插槽 -->
        <template #serverSN="{ row }">
          <span v-if="row.server?.sn">{{ row.server.sn }}</span>
          <span v-else>未安装</span>
        </template>
      </CommonList>

      <!-- 自定义详情抽屉 -->
      <Drawer
        :title="`${detailData?.component_type ? componentTypeMap[detailData.component_type]?.text || detailData.component_type : '组件'} 详情`"
        :visible="detailVisible"
        @close="closeDetail"
        width="50%"
        :destroy-on-close="true"
      >
        <Spin :spinning="detailLoading">
          <!-- 错误状态 -->
          <div
            v-if="error"
            class="flex flex-col items-center justify-center py-10"
          >
            <div class="mb-4 text-red-500">{{ error }}</div>
            <Button type="primary" @click="retry">重试</Button>
          </div>

          <!-- 无数据状态 -->
          <Empty v-else-if="!detailData" description="未找到组件信息" />

          <!-- 有数据状态 -->
          <template v-else>
            <Card :bordered="false" class="mb-4">
              <template #extra>
                <Tag
                  v-if="detailData.status"
                  :color="
                    detailData.status
                      ? componentStatusMap[detailData.status]?.color ||
                        'default'
                      : 'default'
                  "
                >
                  {{
                    detailData.status
                      ? componentStatusMap[detailData.status]?.text ||
                        detailData.status
                      : '未知状态'
                  }}
                </Tag>
              </template>

              <Descriptions bordered size="middle" :column="3">
                <Descriptions.Item label="组件ID">
                  {{ detailData.id }}
                </Descriptions.Item>
                <Descriptions.Item label="组件SN">
                  {{ detailData.sn || '暂无' }}
                </Descriptions.Item>
                <Descriptions.Item label="组件类型">
                  <Tag
                    :color="
                      detailData.component_type
                        ? componentTypeMap[detailData.component_type]?.color ||
                          'default'
                        : 'default'
                    "
                  >
                    {{
                      detailData.component_type
                        ? componentTypeMap[detailData.component_type]?.text ||
                          detailData.component_type
                        : '未知类型'
                    }}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="型号">
                  {{ detailData.model || '暂无' }}
                </Descriptions.Item>
                <Descriptions.Item label="PN号">
                  {{ detailData.pn || '暂无' }}
                </Descriptions.Item>
                <Descriptions.Item label="固件版本">
                  {{ detailData.firmware_version || '暂无' }}
                </Descriptions.Item>
                <Descriptions.Item label="安装槽位">
                  {{ detailData.slot_position || '暂无' }}
                </Descriptions.Item>
                <Descriptions.Item label="安装日期">
                  {{ formatDate(detailData.install_date) || '暂无' }}
                </Descriptions.Item>
                <Descriptions.Item label="状态">
                  <Tag
                    v-if="detailData.status"
                    :color="
                      detailData.status
                        ? componentStatusMap[detailData.status]?.color ||
                          'default'
                        : 'default'
                    "
                  >
                    {{
                      detailData.status
                        ? componentStatusMap[detailData.status]?.text ||
                          detailData.status
                        : '未知状态'
                    }}
                  </Tag>
                  <span v-else>未知状态</span>
                </Descriptions.Item>
                <Descriptions.Item label="描述" :span="3">
                  {{ detailData.description || '暂无描述' }}
                </Descriptions.Item>
              </Descriptions>
            </Card>

            <Tabs @change="handleTabChange">
              <Tabs.TabPane key="server" tab="服务器信息">
                <Card :bordered="false">
                  <Empty
                    v-if="!serverInfo"
                    description="该组件未安装到服务器"
                  />
                  <Descriptions v-else bordered size="middle" :column="2">
                    <Descriptions.Item label="服务器ID">
                      {{ detailData.server?.id || '暂无' }}
                    </Descriptions.Item>
                    <Descriptions.Item label="服务器SN">
                      {{ serverInfo.sn || '暂无' }}
                    </Descriptions.Item>
                    <Descriptions.Item label="品牌">
                      {{ serverInfo.brand || '暂无' }}
                    </Descriptions.Item>
                    <Descriptions.Item label="型号">
                      {{ serverInfo.model || '暂无' }}
                    </Descriptions.Item>
                    <Descriptions.Item label="安装位置">
                      {{ detailData?.slot_position || '暂无' }}
                    </Descriptions.Item>
                    <Descriptions.Item label="安装时间">
                      {{ formatDate(detailData?.install_date) || '暂无' }}
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
              </Tabs.TabPane>

              <Tabs.TabPane key="inventory" tab="库存统计">
                <Spin :spinning="inventoryLoading">
                  <Card :bordered="false">
                    <div
                      v-if="
                        !inventoryStats && detailData && detailData.product_id
                      "
                      class="flex items-center justify-center p-4"
                    >
                      <Button
                        @click="
                          () => fetchInventoryStats(detailData?.product_id || 0)
                        "
                        type="primary"
                      >
                        加载库存数据
                      </Button>
                    </div>
                    <Empty
                      v-else-if="!inventoryStats"
                      description="暂无库存信息"
                    />
                    <Descriptions v-else bordered size="middle" :column="2">
                      <Descriptions.Item label="库存总量">
                        {{ inventoryStats.total_stock || 0 }}
                      </Descriptions.Item>
                      <Descriptions.Item label="在用数量">
                        {{ inventoryStats.in_use_count || 0 }}
                      </Descriptions.Item>
                      <Descriptions.Item label="闲置数量">
                        {{ inventoryStats.idle_count || 0 }}
                      </Descriptions.Item>
                      <Descriptions.Item label="好件数量">
                        {{ inventoryStats.good_count || 0 }}
                      </Descriptions.Item>
                      <Descriptions.Item label="坏件数量">
                        {{ inventoryStats.defect_count || 0 }}
                      </Descriptions.Item>
                    </Descriptions>
                  </Card>
                </Spin>
              </Tabs.TabPane>

              <Tabs.TabPane key="history" tab="变更历史">
                <Card :bordered="false">
                  <Empty
                    v-if="!changeHistory || changeHistory.length === 0"
                    description="暂无变更历史"
                  />
                  <div v-else class="p-2">
                    <div
                      v-for="item in changeHistory"
                      :key="item.id"
                      class="mb-4 rounded border border-gray-200 p-4"
                    >
                      <div class="flex items-center justify-between">
                        <div class="font-medium">{{ item.type }}</div>
                        <div class="text-gray-500">{{ item.date }}</div>
                      </div>
                      <div class="mt-2">
                        <div>
                          <span class="font-medium">服务器SN:</span>
                          {{ item.serverSN }}
                        </div>
                        <div>
                          <span class="font-medium">操作人:</span>
                          {{ item.operator }}
                        </div>
                        <div>
                          <span class="font-medium">描述:</span>
                          {{ item.description }}
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              </Tabs.TabPane>

              <Tabs.TabPane key="spares" tab="相关备件">
                <Spin :spinning="sparesLoading">
                  <Card :bordered="false">
                    <div
                      v-if="spares.length === 0 && detailData"
                      class="flex items-center justify-center p-4"
                    >
                      <Button @click="fetchComponentSpares" type="primary">
                        加载备件数据
                      </Button>
                    </div>
                    <Empty
                      v-else-if="spares.length === 0"
                      description="暂无相关备件"
                    />
                    <div v-else>
                      <Table
                        :data-source="spares"
                        :columns="[
                          {
                            title: '备件型号',
                            dataIndex: 'model',
                            customRender: ({ text, record }) => {
                              return h('div', [
                                h(
                                  'div',
                                  { class: 'font-medium' },
                                  text || '未知型号',
                                ),
                                record.product_info?.name
                                  ? h(
                                      'div',
                                      { class: 'text-gray-500 text-xs' },
                                      record.product_info.name,
                                    )
                                  : null,
                              ]);
                            },
                          },
                          {
                            title: '类型',
                            dataIndex: 'type',
                            customRender: ({ text }) => h(Tag, {}, () => text),
                          },
                          { title: 'SN', dataIndex: 'sn' },
                          {
                            title: '状态',
                            customRender: ({ record }) => {
                              return h('div', [
                                h(
                                  Tag,
                                  {
                                    color: getSpareStatusTag(
                                      record.asset_status,
                                    ).color,
                                  },
                                  () =>
                                    getSpareStatusTag(record.asset_status).text,
                                ),
                                h(
                                  Tag,
                                  {
                                    color: getHardwareStatusTag(
                                      record.hardware_status,
                                    ).color,
                                  },
                                  () =>
                                    getHardwareStatusTag(record.hardware_status)
                                      .text,
                                ),
                              ]);
                            },
                          },
                          {
                            title: '库存',
                            customRender: ({ record }) => {
                              if (!record.inventory_detail) return '-';
                              return h('div', [
                                h(
                                  'div',
                                  {},
                                  `总库存: ${record.inventory_detail.current_stock}`,
                                ),
                                h(
                                  'div',
                                  { class: 'text-gray-500 text-xs' },
                                  `已分配: ${record.inventory_detail.allocated_stock}`,
                                ),
                              ]);
                            },
                          },
                          {
                            title: '存放位置',
                            customRender: ({ record }) => {
                              if (!record.warehouse_info) return '-';
                              return h('div', [
                                h('div', {}, record.warehouse_info.name),
                                h(
                                  'div',
                                  { class: 'text-gray-500 text-xs' },
                                  record.location || '-',
                                ),
                              ]);
                            },
                          },
                        ]"
                        row-key="id"
                        :pagination="{
                          current: sparesPagination.current,
                          pageSize: sparesPagination.pageSize,
                          total: sparesTotal,
                          showSizeChanger: true,
                          showQuickJumper: true,
                          showTotal: (total) => `共 ${total} 条`,
                          onChange: handleSparesTableChange,
                          onShowSizeChange: handleSparesTableChange,
                        }"
                        size="small"
                      />
                    </div>
                  </Card>
                </Spin>
              </Tabs.TabPane>
            </Tabs>
          </template>
        </Spin>
      </Drawer>
  </div>
</template>

<style scoped>
.ant-descriptions-item-label {
  width: 120px;
}

.ant-card {
  box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
}
</style>
