<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { GetProductListParams } from '#/api/core/cmdb/product/product';
import type {
  MachineTemplate,
  TemplateComponent,
} from '#/api/core/cmdb/template/template';

import { h, ref } from 'vue';

import { AccessControl } from '@vben/access';

import {
  Button,
  Empty,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Select,
  Space,
  Spin,
  Table,
  Tabs,
} from 'ant-design-vue';

// 暂时使用模拟产品数据，后续替换为真实API
// import { getProductListApi } from '#/api/core/cmdb/product/product';
import { getProductListApi } from '#/api/core/cmdb/product/product';
import {
  createTemplateApi,
  deleteTemplateApi,
  deleteTemplateComponentApi,
  getTemplateComponentsApi,
  getTemplateDetailApi,
  getTemplateListApi,
  updateTemplateApi,
} from '#/api/core/cmdb/template/template';
import CommonDetail from '#/components/CommonDetail/index.vue';
import CommonList from '#/components/CommonList/index.vue';
import { useConfirmModal } from '#/hooks/useConfirmModal';

const detailRef = ref();
const detailData = ref<MachineTemplate | null>(null);
const commonListRef = ref();
const templateComponentsData = ref<TemplateComponent[]>([]);
const loadingComponents = ref(false);

// 产品选择相关状态
const productModalVisible = ref(false);
const productList = ref<any[]>([]);
const loadingProducts = ref(false);
const selectedProductId = ref<any>(null);
const productQuantity = ref(1);
const productSlot = ref('');
const editingTemplateId = ref<null | number>(null);

// 模板类别选项 - 根据实际数据调整
const templateCategoryOptions = [
  { label: '标准', value: 'standard' },
  { label: '定制', value: 'custom' },
  { label: '高性能', value: 'high_performance' },
  { label: '网络设备', value: 'network_device' },
  { label: 'GPU', value: 'gpu' },
  { label: 'Category1', value: 'Category1' },
  { label: '其他', value: 'other' },
];

// 默认数据
const defaultData = {
  templateName: '',
  cpuModel: '',
  memoryCapacity: 0,
  gpuModel: '',
  diskType: '',
  templateCategory: 'standard',
  status: 'active',
  componentList: '[]', // 添加默认空数组JSON字符串
};

// 表格搜索表单
const formOptions: VbenFormProps = {
  collapsed: true,
  collapsedRows: 1,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入模板名称/CPU型号' },
      fieldName: 'query',
      label: '关键词',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择模板类别',
        options: templateCategoryOptions,
        allowClear: true,
      },
      fieldName: 'category',
      label: '模板类别',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: true,
};

// 表格配置
const gridOptions: VxeGridProps<MachineTemplate> = {
  checkboxConfig: {
    highlight: true,
  },
  exportConfig: {}, // 导出配置
  importConfig: {
    types: ['csv'],
  }, // 导入配置
  columns: [
    { align: 'center', type: 'checkbox', width: 40 },
    { field: 'id', title: 'ID', width: 80 },
    { field: 'templateName', title: '模板名称' },
    { field: 'cpuModel', title: 'CPU型号' },
    { field: 'memoryCapacity', title: '内存容量(GB)' },
    { field: 'gpuModel', title: 'GPU型号' },
    { field: 'diskType', title: '存储类型' },
    {
      field: 'templateCategory',
      title: '模板类别',
      width: 120,
      slots: { default: 'templateCategory' },
    },
    { field: 'created_at', title: '创建时间', width: 180 },
    {
      title: '操作',
      slots: { default: 'operate' },
      width: 160,
      fixed: 'right',
    },
  ],
  keepSource: true,
  height: 'auto',
  rowConfig: { isHover: true },
  pagerConfig: { pageSize: 10, pageSizes: [10, 20, 50, 100] },
  toolbarConfig: {
    custom: true,
    refresh: true,
    export: true,
    zoom: true,
    import: true,
    slots: {
      buttons: 'toolbar-buttons',
    },
  },
};

// 新增/编辑表单配置
const editFormOptions: VbenFormProps = {
  showDefaultActions: false,
  schema: [
    {
      fieldName: 'templateName',
      label: '模板名称',
      component: 'Input',
      componentProps: { placeholder: '请输入模板名称' },
      rules: 'required',
    },
    {
      fieldName: 'templateCategory',
      label: '模板类别',
      component: 'Select',
      componentProps: {
        placeholder: '请选择模板类别',
        options: templateCategoryOptions,
        style: {
          width: '100%',
        },
      },
      rules: 'required',
    },
    {
      fieldName: 'cpuModel',
      label: 'CPU型号',
      component: 'Input',
      componentProps: { placeholder: '请输入CPU型号' },
    },
    {
      fieldName: 'memoryCapacity',
      label: '内存容量(GB)',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入内存容量',
        min: 0,
      },
    },
    {
      fieldName: 'gpuModel',
      label: 'GPU型号',
      component: 'Input',
      componentProps: { placeholder: '请输入GPU型号' },
    },
    {
      fieldName: 'diskType',
      label: '存储类型',
      component: 'Input',
      componentProps: { placeholder: '请输入存储类型' },
    },
  ],
};

// 详情字段配置
const detailFields = [
  { label: 'ID', field: 'id', group: '基本信息' },
  { label: '模板名称', field: 'templateName', group: '基本信息' },
  {
    label: '模板类别',
    field: 'templateCategory',
    format: (value: any) => {
      if (!value) return '暂无';
      const option = templateCategoryOptions.find((opt) => opt.value === value);
      return option ? option.label : value;
    },
    group: '基本信息',
  },
  { label: 'CPU型号', field: 'cpuModel', group: '配置信息' },
  { label: '内存容量(GB)', field: 'memoryCapacity', group: '配置信息' },
  { label: 'GPU型号', field: 'gpuModel', group: '配置信息' },
  { label: '存储类型', field: 'diskType', group: '配置信息' },
  {
    label: '组件列表',
    field: 'componentList',
    group: '配置信息',
    format: (value: any) => {
      if (!value) return '暂无';
      try {
        if (Array.isArray(value)) {
          return value.join(', ');
        } else if (typeof value === 'string') {
          return JSON.parse(value).join(', ');
        } else {
          return String(value);
        }
      } catch {
        return String(value);
      }
    },
  },
  { label: '创建时间', field: 'created_at', group: '其他信息' },
  { label: '更新时间', field: 'updated_at', group: '其他信息' },
];

// 格式化槽位信息，处理长文本
function formatSlot(slot: string) {
  if (!slot) return '暂无';

  // 如果槽位信息过长则截断显示
  if (slot.length > 30) {
    return `${slot.slice(0, 27)}...`;
  }
  return slot;
}

// 使用确认对话框hook
const { showConfirm } = useConfirmModal();

// 加载产品列表
async function loadProductList() {
  try {
    loadingProducts.value = true;
    const res = await getProductListApi({
      page: 1,
      pageSize: 100,
    } as GetProductListParams);
    productList.value = res.list || [];
  } catch {
    message.error('获取产品列表失败');
  } finally {
    loadingProducts.value = false;
  }
}

// 打开产品选择模态框
function openProductModal(templateId?: number) {
  editingTemplateId.value = templateId || null;
  loadProductList();
  selectedProductId.value = null;
  productQuantity.value = 1;
  productSlot.value = '';
  productModalVisible.value = true;
}

// 添加组件前确认
async function handleAddComponent(templateId?: number) {
  try {
    await showConfirm({
      title: '确认添加组件',
      content: '确定要为此套餐模板添加新组件吗？',
    });
    openProductModal(templateId);
  } catch {
    message.info('取消添加组件');
  }
}

// 确认添加产品组件
async function confirmAddProduct() {
  // 确保产品ID不为空
  if (!selectedProductId.value) {
    message.warning('请选择产品');
    return;
  }

  // 确保产品数量有效
  if (!productQuantity.value || productQuantity.value <= 0) {
    message.warning('请输入有效的产品数量');
    return;
  }

  try {
    await showConfirm({
      title: '确认添加组件',
      content: '确定要添加此组件到模板中吗？',
    });

    try {
      // 确保所有必要数据都存在
      if (!editingTemplateId.value) {
        message.error('未找到模板ID，无法添加组件');
        return;
      }

      message.success('组件添加成功');

      // 刷新组件列表
      if (detailData.value && detailData.value.id === editingTemplateId.value) {
        const data = await getTemplateComponentsApi(editingTemplateId.value);
        templateComponentsData.value = data || [];
      }

      productModalVisible.value = false;
    } catch (error) {
      console.error('添加组件失败:', error);
      message.error(
        `添加组件失败: ${error instanceof Error ? error.message : JSON.stringify(error)}`,
      );
    }
  } catch {
    message.info('取消添加组件');
  }
}

// 处理详情事件
function handleDetail(row: MachineTemplate) {
  // 获取模板详细信息
  getTemplateDetailApi(row.id as number)
    .then((response) => {
      detailData.value = response;
      detailRef.value?.drawerApi.open();

      // 如果响应中已包含组件信息，直接使用
      if (response.components && Array.isArray(response.components)) {
        templateComponentsData.value = response.components;
        loadingComponents.value = false;
      } else {
        // 否则获取模板组件信息
        loadingComponents.value = true;
        getTemplateComponentsApi(row.id as number)
          .then((data) => {
            templateComponentsData.value = data || [];
          })
          .catch((error) => {
            console.error('获取模板组件信息失败:', error);
            templateComponentsData.value = [];
          })
          .finally(() => {
            loadingComponents.value = false;
          });
      }
    })
    .catch((error) => {
      console.error('获取模板详情失败:', error);
    });
}

// 自定义权限配置
const permissions = {
  add: ['AssetMgt:ServerPackageInfo:Create'],
  edit: ['AssetMgt:ServerPackageInfo:Edit'],
  delete: ['AssetMgt:ServerPackageInfo:Delete'],
};

// 包装API
const wrappedApi = {
  getList: getTemplateListApi,
  add: (data: MachineTemplate) => {
    // 确保componentList字段是有效的JSON格式字符串
    if (!data.componentList) {
      data.componentList = '[]';
    } else if (Array.isArray(data.componentList)) {
      data.componentList = JSON.stringify(data.componentList);
    }
    return createTemplateApi(data);
  },
  edit: (data: MachineTemplate) => {
    // 确保componentList字段是有效的JSON格式字符串
    if (!data.componentList) {
      data.componentList = '[]';
    } else if (Array.isArray(data.componentList)) {
      data.componentList = JSON.stringify(data.componentList);
    }
    return updateTemplateApi(data.id as number, data);
  },
  delete: (id: number) => deleteTemplateApi(id),
};

// 添加一个处理模板创建成功的函数
async function handleTemplateCreated(_templateId: number) {
  message.success('模板创建成功，已保存基本信息');
  message.info('您可以通过编辑功能添加组件信息');

  // 可以在这里跳转到编辑组件页面
  // 或者提示用户如何添加组件
}

// 删除组件
async function handleDeleteComponent(componentId: number) {
  try {
    await showConfirm({
      title: '确认删除组件',
      content: '确定要删除此组件吗？此操作不可撤销。',
    });

    try {
      await deleteTemplateComponentApi(componentId);
      message.success('组件删除成功');

      // 刷新组件列表
      if (detailData.value && detailData.value.id) {
        const data = await getTemplateComponentsApi(detailData.value.id);
        templateComponentsData.value = data || [];
      }
    } catch (error) {
      console.error('删除组件失败:', error);
      message.error('删除组件失败');
    }
  } catch {
    message.info('取消删除组件');
  }
}
</script>

<template>
  <div>
    <!-- 使用通用列表组件 -->
    <CommonList
      ref="commonListRef"
      :form-options="formOptions"
      :grid-options="gridOptions"
      :edit-form-options="editFormOptions"
      :api="wrappedApi"
      :default-data="defaultData"
      @detail="handleDetail"
      @created="handleTemplateCreated"
      :permissions="permissions"
      title="服务器套餐模板管理"
      show-detail-button
      show-add-button
      show-delete-button
      show-edit-button
      :enable-confirm="true"
    >
      <!-- 模板类别插槽 -->
      <template #templateCategory="{ row }">
        <span v-if="row && row.templateCategory">
          {{
            templateCategoryOptions.find(
              (opt) => opt.value === row.templateCategory,
            )?.label || row.templateCategory
          }}
        </span>
        <span v-else>-</span>
      </template>

      <!-- 自定义编辑按钮 -->
      <template #edit="{ row }">
        <Space>
          <AccessControl :access="permissions.edit">
            <Button
              type="primary"
              size="small"
              @click="commonListRef.value?.editEvent(row)"
            >
              编辑信息
            </Button>
          </AccessControl>
          <AccessControl :access="permissions.add">
            <Button
              type="primary"
              size="small"
              @click="handleAddComponent(row.id)"
            >
              添加组件
            </Button>
          </AccessControl>
        </Space>
      </template>
    </CommonList>

    <!-- 使用通用详情组件 -->
    <CommonDetail
      ref="detailRef"
      :data="detailData"
      title="套餐模板详情"
      :fields="detailFields"
      show-audit-log
      table-name="machine_templates"
      show-group
      class="w-1/2"
    >
      <!-- 添加自定义标签页显示模板组件信息 -->
      <template #tabs>
        <Tabs.TabPane key="components" tab="模板组件信息">
          <div class="mb-4 flex justify-between">
            <span class="text-lg font-bold">组件列表</span>
            <Button
              type="primary"
              @click="handleAddComponent(detailData?.id)"
              v-if="detailData?.id"
            >
              添加组件
            </Button>
          </div>
          <Spin :spinning="loadingComponents">
            <Empty
              v-if="templateComponentsData.length === 0"
              description="暂无模板组件信息"
            />
            <Table
              v-else
              :data-source="templateComponentsData"
              :columns="[
                {
                  title: '组件名称',
                  dataIndex: ['product', 'model'],
                  key: 'productName',
                  width: 180,
                  ellipsis: true,
                },
                {
                  title: '产品类型',
                  dataIndex: ['product', 'product_category'],
                  key: 'productCategory',
                  width: 120,
                },
                {
                  title: '品牌',
                  dataIndex: ['product', 'brand'],
                  key: 'brand',
                  width: 120,
                },
                {
                  title: '规格',
                  dataIndex: ['product', 'spec'],
                  key: 'spec',
                  width: 150,
                  ellipsis: true,
                },
                {
                  title: '数量',
                  dataIndex: 'quantity',
                  key: 'quantity',
                  width: 80,
                },
                {
                  title: '槽位',
                  dataIndex: 'slot',
                  key: 'slot',
                  ellipsis: true,
                  customRender: ({ text }) => formatSlot(text),
                },
                {
                  title: '操作',
                  key: 'action',
                  width: 100,
                  fixed: 'right',
                  customRender: ({ record }) => {
                    return h(
                      Button,
                      {
                        type: 'link',
                        danger: true,
                        size: 'small',
                        onClick: () => handleDeleteComponent(record.id),
                      },
                      { default: () => '删除' },
                    );
                  },
                },
              ]"
              row-key="id"
              :pagination="{ pageSize: 10 }"
              :scroll="{ x: 900 }"
              bordered
            />
          </Spin>
        </Tabs.TabPane>
      </template>
    </CommonDetail>

    <!-- 产品选择模态框 -->
    <Modal
      v-model:open="productModalVisible"
      title="选择产品组件"
      @ok="confirmAddProduct"
      :confirm-loading="loadingProducts"
      width="600px"
    >
      <Spin :spinning="loadingProducts">
        <Form layout="vertical">
          <Form.Item
            label="产品"
            required
            :validate-status="selectedProductId ? 'success' : undefined"
            :help="!selectedProductId ? '请选择产品组件' : ''"
          >
            <Select
              v-model:value="selectedProductId"
              placeholder="请选择产品"
              style="width: 100%"
              :options="
                productList.map((item) => ({
                  label: `${item.model} ${item.spec || ''} (${item.brand} - ${item.product_category || item.material_type})`,
                  value: item.id,
                }))
              "
              :filter-option="
                (input, option) =>
                  (option?.label ?? '')
                    .toLowerCase()
                    .includes(input.toLowerCase())
              "
              show-search
            />
          </Form.Item>
          <Form.Item label="数量">
            <InputNumber
              v-model:value="productQuantity"
              :min="1"
              style="width: 100%"
            />
          </Form.Item>
          <Form.Item label="槽位">
            <Input
              v-model:value="productSlot"
              placeholder="请输入安装槽位信息，如CPU_Socket_1"
            />
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  </div>
</template>
