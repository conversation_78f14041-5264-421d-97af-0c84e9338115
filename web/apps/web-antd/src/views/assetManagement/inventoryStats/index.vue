<script lang="ts" setup>
import type { AnalysisOverviewItem } from '@vben/common-ui';
import type { EchartsUIType } from '@vben/plugins/echarts';

import type {
  ExpiringWarrantyItem,
  InventorySummary,
  LowStockProduct,
} from '#/api/core/cmdb/inventory/inventory';

import { h, onMounted, ref } from 'vue';

import { AnalysisChartCard, AnalysisChartsTabs } from '@vben/common-ui';
import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import {
  Alert,
  Button,
  Card,
  Empty,
  message,
  Progress,
  Spin,
  Statistic,
  Table,
  Tag,
} from 'ant-design-vue';

import {
  getExpiringWarrantyItemsApi,
  getLowStockProductsApi,
} from '#/api/core/cmdb/inventory/inventory';

const loading = ref<boolean>(false);
const lowStockLoading = ref<boolean>(false);
const expiringWarrantyLoading = ref<boolean>(false);
const inventorySummaryLoading = ref<boolean>(false);

const lowStockProducts = ref<LowStockProduct[]>([]);
const expiringWarrantyItems = ref<ExpiringWarrantyItem[]>([]);
const inventorySummaries = ref<InventorySummary[]>([]);

// 图表引用
const allocationChartRef = ref<EchartsUIType>();
const flowChartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(allocationChartRef);
const { renderEcharts: renderFlowEcharts } = useEcharts(flowChartRef);

// 库存状态颜色
const inventoryStatusColors = {
  normal: '#52c41a',
  warning: '#faad14',
  danger: '#f5222d',
};

// 低库存阈值
const lowStockThreshold = ref<number>(10);

// 过保提醒天数
const expiringWarrantyDays = ref<number>(30);

// 渲染库存图表
const renderInventoryCharts = () => {
  if (inventorySummaries.value.length === 0) return;

  // 渲染总体库存分布图表
  const totalAllocated = inventorySummaries.value.reduce(
    (sum, item) => sum + item.allocated_stock,
    0,
  );
  const totalAvailable = inventorySummaries.value.reduce(
    (sum, item) => sum + item.available_stock,
    0,
  );

  renderEcharts({
    title: {
      text: '库存分配情况',
      left: 'center',
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      left: 10,
      data: ['已分配', '可用库存'],
    },
    series: [
      {
        name: '库存分配',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: totalAllocated, name: '已分配' },
          { value: totalAvailable, name: '可用库存' },
        ],
      },
    ],
  });

  // 渲染库存流动图表
  const inboundData = inventorySummaries.value.map(
    (item) => item.inbound_items,
  );
  const outboundData = inventorySummaries.value.map(
    (item) => item.outbound_items,
  );
  const productModels = inventorySummaries.value.map(
    (item) => item.product_model,
  );

  renderFlowEcharts({
    title: {
      text: '库存流动情况',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      data: ['入库', '出库'],
      top: 'bottom',
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: productModels,
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '入库',
        type: 'bar',
        stack: 'total',
        label: {
          show: false,
        },
        emphasis: {
          focus: 'series',
        },
        data: inboundData,
        color: inventoryStatusColors.normal,
      },
      {
        name: '出库',
        type: 'bar',
        stack: 'total',
        label: {
          show: false,
        },
        emphasis: {
          focus: 'series',
        },
        data: outboundData,
        color: inventoryStatusColors.danger,
      },
    ],
  });
};

// 加载低库存产品
const fetchLowStockProducts = async () => {
  lowStockLoading.value = true;

  try {
    const response = await getLowStockProductsApi(lowStockThreshold.value);

    lowStockProducts.value = response || [];
  } catch (error: any) {
    console.error('获取低库存产品失败:', error);
    message.error(`获取低库存产品失败: ${error.message || '未知错误'}`);
    lowStockProducts.value = [];
  } finally {
    lowStockLoading.value = false;
  }
};

// 加载即将过保的库存项目
const fetchExpiringWarrantyItems = async () => {
  expiringWarrantyLoading.value = true;

  try {
    const response = await getExpiringWarrantyItemsApi(
      expiringWarrantyDays.value,
    );

    expiringWarrantyItems.value = response || [];
  } catch (error: any) {
    console.error('获取即将过保项目失败:', error);
    message.error(`获取即将过保项目失败: ${error.message || '未知错误'}`);
    expiringWarrantyItems.value = [];
  } finally {
    expiringWarrantyLoading.value = false;
  }
};

// 加载产品库存汇总（模拟数据，实际应该通过API获取）
const fetchInventorySummaries = async () => {
  inventorySummaryLoading.value = true;

  try {
    // 这里应该是循环调用API获取每个产品的库存汇总
    // 为了演示，使用模拟数据
    const mockData: InventorySummary[] = [
      {
        product_id: 1,
        product_model: 'Intel Xeon 8280',
        product_pn: 'XEON-8280',
        total_stock: 50,
        allocated_stock: 30,
        available_stock: 20,
        inbound_items: 60,
        outbound_items: 10,
      },
      {
        product_id: 2,
        product_model: 'NVIDIA A100',
        product_pn: 'NVDA-A100',
        total_stock: 20,
        allocated_stock: 15,
        available_stock: 5,
        inbound_items: 25,
        outbound_items: 5,
      },
      {
        product_id: 3,
        product_model: 'Samsung DDR4-3200',
        product_pn: 'SAM-DDR4-3200',
        total_stock: 100,
        allocated_stock: 40,
        available_stock: 60,
        inbound_items: 120,
        outbound_items: 20,
      },
    ];

    inventorySummaries.value = mockData;

    // 在数据加载完成后渲染图表
    setTimeout(() => {
      renderInventoryCharts();
    }, 300);
  } catch (error: any) {
    console.error('获取库存汇总失败:', error);
    message.error(`获取库存汇总失败: ${error.message || '未知错误'}`);
    inventorySummaries.value = [];
  } finally {
    inventorySummaryLoading.value = false;
  }
};

// 低库存产品表格列
const lowStockColumns = [
  {
    title: '产品ID',
    dataIndex: 'product_id',
    key: 'product_id',
  },
  {
    title: '产品型号',
    dataIndex: 'product_model',
    key: 'product_model',
  },
  {
    title: '分类',
    dataIndex: 'category',
    key: 'category',
  },
  {
    title: '当前库存',
    dataIndex: 'current_stock',
    key: 'current_stock',
  },
  {
    title: '阈值',
    dataIndex: 'threshold',
    key: 'threshold',
  },
  {
    title: '状态',
    key: 'status',
    customRender: ({ record }: { record: LowStockProduct }) => {
      // 计算状态和颜色
      const ratio = record.current_stock / record.threshold;
      let color = inventoryStatusColors.normal;
      let statusText = '正常';

      if (ratio <= 0.25) {
        color = inventoryStatusColors.danger;
        statusText = '严重不足';
      } else if (ratio <= 0.5) {
        color = inventoryStatusColors.warning;
        statusText = '库存不足';
      }

      return h(Tag, { color }, () => statusText);
    },
  },
  {
    title: '库存比例',
    key: 'ratio',
    customRender: ({ record }: { record: LowStockProduct }) => {
      // 计算比例和颜色
      const ratio = Math.min(
        (record.current_stock / record.threshold) * 100,
        100,
      );
      let color = inventoryStatusColors.normal;

      if (ratio <= 25) {
        color = inventoryStatusColors.danger;
      } else if (ratio <= 50) {
        color = inventoryStatusColors.warning;
      }

      return h(Progress, { percent: ratio, size: 'small', strokeColor: color });
    },
  },
];

// 即将过保项目列
const expiringWarrantyColumns = [
  {
    title: '产品ID',
    dataIndex: 'product_id',
    key: 'product_id',
  },
  {
    title: '产品型号',
    dataIndex: 'product_model',
    key: 'product_model',
  },
  {
    title: '批次号',
    dataIndex: 'batch_number',
    key: 'batch_number',
  },
  {
    title: '保修到期',
    dataIndex: 'warranty_end',
    key: 'warranty_end',
    customRender: ({ text }: { text: string }) => {
      return new Date(text).toLocaleDateString('zh-CN');
    },
  },
  {
    title: '剩余天数',
    dataIndex: 'remaining_days',
    key: 'remaining_days',
  },
  {
    title: '当前库存',
    dataIndex: 'current_stock',
    key: 'current_stock',
  },
  {
    title: '状态',
    key: 'status',
    customRender: ({ record }: { record: ExpiringWarrantyItem }) => {
      let color = inventoryStatusColors.normal;
      let statusText = '正常';

      if (record.remaining_days <= 7) {
        color = inventoryStatusColors.danger;
        statusText = '即将过保';
      } else if (record.remaining_days <= 14) {
        color = inventoryStatusColors.warning;
        statusText = '过保提醒';
      }

      return h(Tag, { color }, () => statusText);
    },
  },
];

// 页面加载时获取数据
onMounted(() => {
  loading.value = true;

  // 并行加载所有数据
  Promise.all([
    fetchLowStockProducts(),
    fetchExpiringWarrantyItems(),
    fetchInventorySummaries(),
  ]).finally(() => {
    loading.value = false;
  });
});

// 刷新数据
const refreshData = () => {
  loading.value = true;

  // 并行加载所有数据
  Promise.all([
    fetchLowStockProducts(),
    fetchExpiringWarrantyItems(),
    fetchInventorySummaries(),
  ]).finally(() => {
    loading.value = false;
  });
};

// 更新低库存阈值并刷新
const updateLowStockThreshold = () => {
  fetchLowStockProducts();
};

// 更新过保提醒天数并刷新
const updateExpiringWarrantyDays = () => {
  fetchExpiringWarrantyItems();
};

// 图表标签页配置
const chartTabs = [
  {
    label: '低库存预警',
    value: 'lowStock',
  },
  {
    label: '过保提醒',
    value: 'expiringWarranty',
  },
];

// 库存概览数据
const getOverviewData = (): AnalysisOverviewItem[] => {
  if (inventorySummaries.value.length === 0) return [];

  const totalStock = inventorySummaries.value.reduce(
    (sum, item) => sum + item.total_stock,
    0,
  );
  const allocatedStock = inventorySummaries.value.reduce(
    (sum, item) => sum + item.allocated_stock,
    0,
  );
  const availableStock = inventorySummaries.value.reduce(
    (sum, item) => sum + item.available_stock,
    0,
  );
  const lowStockCount = lowStockProducts.value.length;

  return [
    {
      title: '总库存',
      value: totalStock,
      icon: 'mdi:package-variant',
      totalTitle: '总库存累计',
      totalValue: totalStock,
    },
    {
      title: '已分配',
      value: allocatedStock,
      icon: 'mdi:package-variant-closed',
      totalTitle: '总分配累计',
      totalValue: allocatedStock,
    },
    {
      title: '可用库存',
      value: availableStock,
      icon: 'mdi:package-variant-plus',
      totalTitle: '可用库存累计',
      totalValue: availableStock,
    },
    {
      title: '低库存预警',
      value: lowStockCount,
      icon: 'mdi:alert-circle',
      totalTitle: '低库存产品数',
      totalValue: lowStockCount,
    },
  ];
};
</script>

<template>
  <div class="p-4">
    <Spin :spinning="loading">
      <div class="mb-4 flex items-center justify-between">
        <h1 class="text-xl font-bold">库存统计分析</h1>
        <Button type="primary" @click="refreshData">刷新数据</Button>
      </div>

      <!-- 库存概览 -->
      <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card v-for="(item, index) in getOverviewData()" :key="index">
          <Statistic :title="item.title" :value="item.value" />
        </Card>
      </div>

      <!-- 库存警告 -->
      <AnalysisChartsTabs :tabs="chartTabs" class="mb-4">
        <template #lowStock>
          <div class="p-4">
            <div class="mb-4 flex items-center">
              <span class="mr-2">阈值：</span>
              <input
                v-model.number="lowStockThreshold"
                type="number"
                class="w-20 rounded-md border border-gray-300 p-1 shadow-sm"
                min="1"
              />
              <Button
                type="primary"
                size="small"
                class="ml-2"
                @click="updateLowStockThreshold"
              >
                应用
              </Button>
            </div>

            <Alert
              v-if="lowStockProducts.length > 0"
              type="warning"
              :message="`发现 ${lowStockProducts.length} 个低库存产品，请及时补充库存`"
              show-icon
              class="mb-4"
            />

            <Spin :spinning="lowStockLoading">
              <Table
                v-if="lowStockProducts.length > 0"
                :columns="lowStockColumns"
                :data-source="lowStockProducts"
                :row-key="(record) => record.product_id"
                :pagination="{ pageSize: 5 }"
              />
              <Empty v-else description="没有低库存产品" />
            </Spin>
          </div>
        </template>

        <template #expiringWarranty>
          <div class="p-4">
            <div class="mb-4 flex items-center">
              <span class="mr-2">提前提醒天数：</span>
              <input
                v-model.number="expiringWarrantyDays"
                type="number"
                class="w-20 rounded-md border border-gray-300 p-1 shadow-sm"
                min="1"
              />
              <Button
                type="primary"
                size="small"
                class="ml-2"
                @click="updateExpiringWarrantyDays"
              >
                应用
              </Button>
            </div>

            <Alert
              v-if="expiringWarrantyItems.length > 0"
              type="warning"
              :message="`发现 ${expiringWarrantyItems.length} 个即将过保的库存项目，请注意处理`"
              show-icon
              class="mb-4"
            />

            <Spin :spinning="expiringWarrantyLoading">
              <Table
                v-if="expiringWarrantyItems.length > 0"
                :columns="expiringWarrantyColumns"
                :data-source="expiringWarrantyItems"
                :row-key="(record) => record.id"
                :pagination="{ pageSize: 5 }"
              />
              <Empty v-else description="没有即将过保的库存项目" />
            </Spin>
          </div>
        </template>
      </AnalysisChartsTabs>

      <!-- 库存图表 -->
      <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
        <AnalysisChartCard title="库存分配情况">
          <Spin :spinning="inventorySummaryLoading">
            <div
              v-if="inventorySummaries.length === 0"
              class="flex items-center justify-center py-10"
            >
              <Empty description="暂无库存汇总数据" />
            </div>
            <div v-else class="h-72">
              <EchartsUI ref="allocationChartRef" />
            </div>
          </Spin>
        </AnalysisChartCard>

        <AnalysisChartCard title="库存流动情况">
          <Spin :spinning="inventorySummaryLoading">
            <div
              v-if="inventorySummaries.length === 0"
              class="flex items-center justify-center py-10"
            >
              <Empty description="暂无库存汇总数据" />
            </div>
            <div v-else class="h-72">
              <EchartsUI ref="flowChartRef" />
            </div>
          </Spin>
        </AnalysisChartCard>
      </div>

      <!-- 产品库存明细 -->
      <Card title="产品库存明细" :bordered="false">
        <Spin :spinning="inventorySummaryLoading">
          <div
            v-if="inventorySummaries.length === 0"
            class="flex items-center justify-center py-10"
          >
            <Empty description="暂无库存明细数据" />
          </div>
          <div v-else>
            <div
              v-for="summary in inventorySummaries"
              :key="summary.product_id"
              class="mb-4"
            >
              <Card :title="summary.product_model" type="inner">
                <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <div>
                    <Statistic title="总库存" :value="summary.total_stock" />
                  </div>
                  <div>
                    <Statistic
                      title="已分配"
                      :value="summary.allocated_stock"
                    />
                  </div>
                  <div>
                    <Statistic
                      title="可用库存"
                      :value="summary.available_stock"
                    />
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </Spin>
      </Card>
    </Spin>
  </div>
</template>

<style scoped>
.ant-card {
  box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

.ant-card-head-title {
  font-weight: 600;
}
</style>
