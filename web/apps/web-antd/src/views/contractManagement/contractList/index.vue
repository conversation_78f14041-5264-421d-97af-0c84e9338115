<script lang="ts" setup>
// 合同列表页面
</script>

<template>
  <div class="p-6">
    <div class="mb-4">
      <h1 class="text-2xl font-bold">合同列表</h1>
      <div class="text-gray-500">管理所有合同信息</div>
    </div>

    <div class="mb-4 rounded bg-white p-4 shadow">
      <div class="flex flex-wrap gap-4">
        <div class="min-w-[200px] flex-1">
          <div class="mb-1 text-gray-500">合同编号</div>
          <input
            type="text"
            placeholder="请输入合同编号"
            class="w-full rounded border p-2"
          />
        </div>
        <div class="min-w-[200px] flex-1">
          <div class="mb-1 text-gray-500">合同类型</div>
          <select class="w-full rounded border p-2">
            <option>全部类型</option>
            <option>采购合同</option>
            <option>销售合同</option>
            <option>服务合同</option>
            <option>合作协议</option>
          </select>
        </div>
        <div class="min-w-[200px] flex-1">
          <div class="mb-1 text-gray-500">签订日期</div>
          <div class="flex space-x-2">
            <input type="date" class="flex-1 rounded border p-2" />
            <span class="flex items-center">至</span>
            <input type="date" class="flex-1 rounded border p-2" />
          </div>
        </div>
        <div class="flex items-end">
          <button class="rounded bg-blue-500 px-4 py-2 text-white">查询</button>
        </div>
      </div>
    </div>

    <div class="rounded bg-white p-4 shadow">
      <div class="mb-4 flex items-center justify-between">
        <div class="text-lg font-medium">合同列表</div>
        <div>
          <button class="mr-2 rounded bg-blue-500 px-4 py-2 text-white">
            新增合同
          </button>
          <button class="rounded bg-green-500 px-4 py-2 text-white">
            导出列表
          </button>
        </div>
      </div>

      <table class="w-full">
        <thead>
          <tr class="border-b">
            <th class="py-2 text-left">合同编号</th>
            <th class="py-2 text-left">合同名称</th>
            <th class="py-2 text-left">合同类型</th>
            <th class="py-2 text-left">对方单位</th>
            <th class="py-2 text-left">合同金额</th>
            <th class="py-2 text-left">签订日期</th>
            <th class="py-2 text-left">到期日期</th>
            <th class="py-2 text-left">状态</th>
            <th class="py-2 text-left">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr class="border-b">
            <td class="py-2">CT20230001</td>
            <td class="py-2">服务器采购合同</td>
            <td class="py-2">采购合同</td>
            <td class="py-2">科技设备有限公司</td>
            <td class="py-2">¥120,000.00</td>
            <td class="py-2">2023-01-15</td>
            <td class="py-2">2024-01-14</td>
            <td class="py-2"><span class="text-green-500">执行中</span></td>
            <td class="py-2">
              <button class="mr-2 text-blue-500">详情</button>
              <button class="text-blue-500">下载</button>
            </td>
          </tr>
          <tr class="border-b">
            <td class="py-2">CT20230042</td>
            <td class="py-2">软件许可协议</td>
            <td class="py-2">服务合同</td>
            <td class="py-2">软件科技公司</td>
            <td class="py-2">¥85,000.00</td>
            <td class="py-2">2023-03-22</td>
            <td class="py-2">2024-03-21</td>
            <td class="py-2"><span class="text-green-500">执行中</span></td>
            <td class="py-2">
              <button class="mr-2 text-blue-500">详情</button>
              <button class="text-blue-500">下载</button>
            </td>
          </tr>
          <tr class="border-b">
            <td class="py-2">CT20230078</td>
            <td class="py-2">办公用品采购框架协议</td>
            <td class="py-2">采购合同</td>
            <td class="py-2">办公用品批发商</td>
            <td class="py-2">¥50,000.00</td>
            <td class="py-2">2023-05-10</td>
            <td class="py-2">2024-05-09</td>
            <td class="py-2"><span class="text-green-500">执行中</span></td>
            <td class="py-2">
              <button class="mr-2 text-blue-500">详情</button>
              <button class="text-blue-500">下载</button>
            </td>
          </tr>
          <tr class="border-b">
            <td class="py-2">CT20230085</td>
            <td class="py-2">网络服务协议</td>
            <td class="py-2">服务合同</td>
            <td class="py-2">网络服务提供商</td>
            <td class="py-2">¥35,600.00</td>
            <td class="py-2">2023-05-18</td>
            <td class="py-2">2024-05-17</td>
            <td class="py-2"><span class="text-yellow-500">待生效</span></td>
            <td class="py-2">
              <button class="mr-2 text-blue-500">详情</button>
              <button class="text-blue-500">下载</button>
            </td>
          </tr>
        </tbody>
      </table>

      <div class="mt-4 flex items-center justify-between">
        <div>共 <span class="font-medium">28</span> 条记录</div>
        <div class="flex space-x-2">
          <button class="rounded border px-3 py-1">上一页</button>
          <button class="rounded bg-blue-500 px-3 py-1 text-white">1</button>
          <button class="rounded border px-3 py-1">2</button>
          <button class="rounded border px-3 py-1">3</button>
          <button class="rounded border px-3 py-1">下一页</button>
        </div>
      </div>
    </div>
  </div>
</template>
