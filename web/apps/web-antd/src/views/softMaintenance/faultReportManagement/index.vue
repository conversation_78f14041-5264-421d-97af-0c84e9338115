<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { ExportRequest } from '#/api/core/export';
import type {
  FaultTicket,
  FaultTicketListParams,
  FaultTicketStatus,
  Priority,
} from '#/api/core/ticket/fault-ticket';

import { ref, watch } from 'vue';

import { AccessControl } from '@vben/access';
import { Page, useVbenModal } from '@vben/common-ui';

import { Button, message, Modal, Space, Tag, Tooltip } from 'ant-design-vue';
import XEUtils from 'xe-utils';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { exportAndDownload } from '#/api/core/export';
import {
  addFaultTicketApi,
  getFaultTicketTableApi,
} from '#/api/core/ticket/fault-ticket';
import { getRepairTicketDetailApi } from '#/api/core/ticket/repair-ticket';
import { getUserListApi } from '#/api/core/user';
// import { assetTypeOptions } from '#/views/cmdb/serverInfo/server-config';

import ImageUploader from './components/ImageUploader.vue';
import {
  defaultFaultTicket,
  faultTypeMap,
  priorityMap,
  repairTypeOptions,
  statusMap,
} from './type';
import { getAvailableActions } from './utils/buttonActions';
import { formatFaultDetail } from './utils/formatterUtils';
import {
  approvalFormOptions,
  assignFormOptions,
  closeFormOptions,
  createApprovalModal,
  createAssignModal,
  createCloseModal,
  createRepairModal,
  createVerifyModal,
  getRepairFormOptions,
  verifyFormOptions,
} from './utils/formConfig';
import { uploadImagesToServer } from './utils/modalUtils';
import {
  debounce,
  getResourceBySN,
  showResourceMessage,
  showResourceNotFoundMessage,
  updateFormWithResource,
} from './utils/snUtils';
import {
  acceptTicket,
  cancelTicket,
  completeRepair,
  startRepair,
} from './utils/ticketWorkflow';

const selectRow = ref<FaultTicket | null>(null);
const currentDeviceSN = ref<string>('');

// 用户数据相关
const userListCache = ref<Array<{ label: string; value: string }>>([]);
const userListLoading = ref(false);

// 资产类型选项
const assetTypeOptions = [
  { label: '算力服务器', value: 'server' },
  { label: '计算服务器', value: 'gpu_server' },
  { label: '存储设备', value: 'storage' },
  { label: '其他', value: 'other' },
];

// 获取用户列表
async function fetchUserList(query = '') {
  if (userListLoading.value) return;

  userListLoading.value = true;
  try {
    const res = await getUserListApi({
      page: 1,
      pageSize: 100,
      query,
    });

    const options = res.list.map((user) => ({
      label: user.realName,
      value: user.realName,
    }));

    userListCache.value = options;
    return options;
  } catch (error) {
    console.error('获取用户列表失败', error);
    return [];
  } finally {
    userListLoading.value = false;
  }
}

// 表格搜索表单
const formOptions: VbenFormProps = {
  collapsed: true,
  collapsedRows: 3,
  schema: [
    {
      component: 'Input',
      componentProps: { placeholder: '请输入报障单号或设备SN' },
      fieldName: 'query',
      label: '关键词',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入故障标题' },
      fieldName: 'title',
      label: '故障标题',
    },
    {
      component: 'Input',
      componentProps: { placeholder: '请输入租户IP' },
      fieldName: 'resource_identifier',
      label: '租户IP',
    },
    {
      component: 'TreeSelect',
      componentProps: {
        placeholder: '请选择状态',
        treeData: [
          {
            title: '处理中',
            value: '__in_progress__',
            key: '__in_progress__',
            selectable: true,
            children: Object.entries(statusMap)
              .filter(([key]) => !['cancelled', 'completed'].includes(key))
              .map(([value, item]) => ({
                title: item.text,
                value,
                key: value,
              })),
          },
          {
            title: statusMap.completed.text,
            value: 'completed',
            key: 'completed',
          },
          {
            title: statusMap.cancelled.text,
            value: 'cancelled',
            key: 'cancelled',
          },
        ],
        showSearch: true,
        treeNodeFilterProp: 'title',
        allowClear: true,
        dropdownStyle: { maxHeight: '400px', overflow: 'auto' },
        treeDefaultExpandAll: true,
      },
      fieldName: 'status',
      label: '状态',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择故障类型',
        options: Object.entries(faultTypeMap).map(([value, item]) => ({
          label: item.text,
          value,
        })),
        allowClear: true,
      },
      fieldName: 'faultType',
      label: '故障类型',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择具体故障类型',
        options: [
          { label: 'GPU故障', value: 'GPU故障' },
          { label: '内存故障', value: '内存故障' },
          { label: 'CPU故障', value: 'CPU故障' },
          { label: '硬盘故障', value: '硬盘故障' },
          { label: '主板故障', value: '主板故障' },
          { label: '网卡故障', value: '网卡故障' },
          { label: '电源故障', value: '电源故障' },
          { label: '内部线缆故障', value: '内部线缆故障' },
          { label: '风扇故障', value: '风扇故障' },
          { label: 'baseboard故障', value: 'baseboard故障' },
          { label: '网线故障', value: '网线故障' },
          { label: '光模块故障', value: '光模块故障' },
          { label: '交换板故障', value: '交换板故障' },
          { label: '其他硬件故障', value: '其他硬件故障' },
          { label: '其他软件故障', value: '其他软件故障' },
        ],
        allowClear: true,
      },
      fieldName: 'fault_detail_type',
      label: '具体故障类型',
    },
    {
      component: 'RangePicker',
      componentProps: {
        placeholder: ['开始时间', '结束时间'],
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DDT00:00:00.000Z',
        style: { width: '100%' },
      },
      fieldName: 'creationTimeRange',
      label: '创建时间',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择或搜索报障人',
        showSearch: true,
        allowClear: true,
        filterOption: false,
        options: userListCache,
        onFocus: () => {
          if (userListCache.value.length === 0) {
            fetchUserList();
          }
        },
        onSearch: (value: string) => {
          if (value) {
            fetchUserList(value);
          }
        },
      },
      fieldName: 'reporterName',
      label: '报障人',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择或搜索接单人',
        showSearch: true,
        allowClear: true,
        filterOption: false,
        options: userListCache,
        onFocus: () => {
          if (userListCache.value.length === 0) {
            fetchUserList();
          }
        },
        onSearch: (value: string) => {
          if (value) {
            fetchUserList(value);
          }
        },
      },
      fieldName: 'assignedTo',
      label: '接单人',
    },

    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择优先级',
        options: Object.entries(priorityMap).map(([value, item]) => ({
          label: item.text,
          value,
        })),
        allowClear: true,
      },
      fieldName: 'priority',
      label: '优先级',
    },
  ],
  submitButtonOptions: { content: '查询' },
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: true,
};

// 使用ref存储需要查询的SN和表单API引用
const pendingSN = ref<string>('');
const formApiRef = ref<any>(null);

// 设备SN处理函数 - 不依赖createFormApi
async function processSN(sn: string) {
  if (!sn) return;

  const resource = await getResourceBySN(sn);
  if (resource && formApiRef.value) {
    updateFormWithResource(formApiRef.value, resource);
    showResourceMessage(resource);
  } else if (!resource) {
    showResourceNotFoundMessage(sn);
    // 只有当表单API可用时才清空租户IP
    if (formApiRef.value) {
      const formValues = formApiRef.value.getValues();
      formApiRef.value.setValues({
        ...formValues,
        resourceIdentifier: '',
      });
    }
  }
}

// 防抖版本的SN处理函数
const debouncedProcessSN = debounce(processSN, 500);

// 处理设备SN搜索按钮事件 - 不依赖createFormApi
async function handleDeviceSNSearch(value: string) {
  if (!value) return;

  await processSN(value);
}

// 处理验证SN按钮点击事件 - 不依赖createFormApi
async function handleVerifySN() {
  if (!formApiRef.value) return;

  try {
    const values = await formApiRef.value.getValues();
    if (values.deviceSN) {
      await handleDeviceSNSearch(values.deviceSN);
    } else {
      message.warning('请先输入设备SN');
    }
  } catch (error) {
    console.warn('获取表单值失败', error);
  }
}

// 表格配置
const gridOptions: VxeGridProps<FaultTicket> = {
  checkboxConfig: {
    highlight: true,
  },
  exportConfig: {
    remote: true,
    types: ['xlsx', 'csv'],
    modes: [
      { label: '导出全表数据', value: 'all' },
      { label: '导出选中的数据', value: 'selected' },
    ],
  },
  columns: [
    { align: 'center', type: 'checkbox', width: 40 },
    { field: 'id', title: 'ID', width: 80, visible: false },
    {
      field: 'title',
      title: '故障标题',
      minWidth: 240,
      slots: { default: 'title' },
      visible: false,
    },
    {
      field: 'ticketNo',
      title: '故障单号',
      minWidth: 220,
      slots: { default: 'ticketNo' },
    },
    {
      field: 'project',
      title: '故障项目',
      minWidth: 90,
      slots: { default: 'project' },
    },
    {
      field: 'assetType',
      title: '资产类型',
      minWidth: 90,
      slots: { default: 'assetType' },
    },
    {
      field: 'assertSN',
      title: '资产SN',
      minWidth: 200,
      align: 'center',
      slots: { default: 'deviceSN' },
    },
    {
      field: 'faultType',
      title: '故障类型',
      minWidth: 80,
      slots: { default: 'faultType' },
    },
    {
      field: 'source',
      title: '故障来源',
      minWidth: 80,
      formatter: ({ cellValue }) => {
        const sourceMap: Record<string, string> = {
          manual: '监控告警', // 手动创建
          customer: '业务报障', // 客户报修
          alarm: '业务巡检', // 告警触发
        };
        return sourceMap[cellValue as string] || cellValue;
      },
    },
    {
      field: 'resource_identifier',
      title: '租户IP',
      minWidth: 160,
      slots: { default: 'resource_identifier' },
      visible: false,
    },
    {
      field: 'assignedTo',
      title: '处理人',
      minWidth: 100,
      slots: { default: 'assignedTo' },
    },
    {
      field: 'status',
      title: '状态',
      minWidth: 110,
      slots: { default: 'status' },
    },
    {
      field: 'response_duration',
      title: '响应时长',
      minWidth: 120,
      slots: { default: 'response_duration' },
    },
    {
      field: 'faultSLADuration',
      title: '故障SLA时长',
      minWidth: 120,
      slots: { default: 'faultSLADuration' }, // 关闭时间减事件发生时间
    },

    // ******默认隐藏******
    {
      field: 'creationTime',
      title: '故障开始时间',
      width: 160,
      visible: false,
    }, // 事件发生时间
    // { field: 'closeTime', title: '故障结束时间', width: 160, visible: false }, // 关闭时间

    {
      field: 'count_in_sla',
      title: '计入SLA',
      width: 100,
      formatter: ({ cellValue }) => (cellValue ? '是' : '否'),
      visible: false,
    },

    {
      field: 'faultDescription',
      title: '故障描述',
      width: 200,
      slots: { default: 'faultDescription' },
      visible: false,
    },
    {
      field: 'priority',
      title: '优先级',
      width: 100,
      slots: { default: 'priority' },
      visible: false,
    },
    {
      field: 'fault_detail_type',
      title: '具体故障类型',
      width: 120,
      visible: false,
    },
    {
      field: 'repairMethod',
      title: '修复方法',
      width: 120,
      formatter: ({ cellValue }) => {
        if (!cellValue) return '-';
        const found = repairTypeOptions.find(
          (option) => option.value === cellValue,
        );
        return found ? found.label : cellValue;
      },
      visible: false,
    },

    { field: 'componentSN', title: '组件SN', width: 160, visible: false },

    { field: 'componentType', title: '组件类型', width: 120, visible: false },
    { field: 'reporterName', title: '报障人', width: 100, visible: false },
    // { field: 'assignedTo', title: '接单人', width: 100, visible: false },
    { field: 'repairTicketID', title: '维修单ID', width: 100, visible: false },
    {
      field: 'is_frequent_fault',
      title: '高频故障',
      width: 100,
      formatter: ({ cellValue }) => (cellValue ? '是' : '否'),
      visible: false,
    },
    { field: 'acknowledgeTime', title: '确认时间', width: 160, visible: false },
    { field: 'assignmentTime', title: '分配时间', width: 160, visible: false },
    {
      field: 'customerApprovalTime',
      title: '客户审批时间',
      width: 160,
      visible: false,
    },
    { field: 'actualFixTime', title: '修复时间', width: 160, visible: false },
    {
      field: 'verificationStartTime',
      title: '验证开始时间',
      width: 160,
      visible: false,
    },
    {
      field: 'verificationEndTime',
      title: '验证结束时间',
      width: 160,
      visible: false,
    },
    { field: 'closeTime', title: '关闭时间', width: 160, visible: false },
    // {
    //   title: '操作',
    //   fixed: 'right',
    //   slots: { default: 'operate' },
    //   width: 220,
    // },
  ],
  keepSource: true,
  height: 'auto',
  rowConfig: {
    isHover: true,
    height: 50,
  },
  scrollX: {
    enabled: true,
  },
  pagerConfig: { pageSize: 10, pageSizes: [10, 20, 50, 100] },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        try {
          // 基础参数
          const params = {
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          };

          // 处理日期范围
          if (
            params.creationTimeRange &&
            params.creationTimeRange.length === 2
          ) {
            try {
              // 如果是数组，直接构造开始和结束日期字符串

              if (Array.isArray(params.creationTimeRange)) {
                if (params.creationTimeRange[0]) {
                  // 确保日期格式正确，与后端期望的格式匹配
                  if (typeof params.creationTimeRange[0] === 'string') {
                    params.creationTimeStart = params.creationTimeRange[0];
                  } else if (params.creationTimeRange[0].format) {
                    params.creationTimeStart =
                      params.creationTimeRange[0].format('YYYY-MM-DDTHH:mm:ss');
                  } else {
                    params.creationTimeStart = params.creationTimeRange[0];
                  }
                }

                if (params.creationTimeRange[1]) {
                  if (typeof params.creationTimeRange[1] === 'string') {
                    params.creationTimeEnd = params.creationTimeRange[1];
                  } else if (params.creationTimeRange[1].format) {
                    params.creationTimeEnd = params.creationTimeRange[1].format(
                      'YYYY-MM-DDTHH:mm:ss',
                    );
                  } else {
                    params.creationTimeEnd = params.creationTimeRange[1];
                  }
                }
              }
            } catch (error) {
              console.error('日期格式转换错误:', error);
            }

            delete params.creationTimeRange;
          }

          // 处理特殊的"处理中"值
          if (params.status === '__in_progress__') {
            // 获取除了completed和cancelled之外的所有状态
            params.status = Object.keys(statusMap).filter(
              (key) => !['cancelled', 'completed'].includes(key),
            );
          }

          const res = await getFaultTicketTableApi(
            params as FaultTicketListParams,
          );
          return {
            items: res.list,
            total: res.total,
          };
        } catch (error) {
          console.error('获取报障单失败', error);
          return { items: [], total: 0 };
        }
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    export: true,
    zoom: true,
    slots: {
      buttons: 'toolbar-buttons',
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });

// 设置导出方法（确保在gridApi初始化后再设置）
if (gridApi && gridApi.grid && gridOptions.exportConfig) {
  gridOptions.exportConfig.exportMethod = async ({ options }) => {
    try {
      // 从表格API获取选中行数据
      const selectRecords = gridApi.grid.getCheckboxRecords();

      // 处理条件参数
      const request: ExportRequest = {
        tableName: 'fault_tickets',
        mode: options.mode === 'selected' ? 'selected' : 'all',
        isHeader: options.isHeader ?? true,
        fields: options.columns
          .filter((column: any) => column.field && column.title)
          .map((column: any) => ({
            field: column.field,
            title: column.title,
          })),
        filename:
          options.filename || `故障报障单_${new Date().toLocaleString()}.xlsx`,
        sheetName: options.sheetName || '故障报障单',
      };

      // 处理ids字段
      let ids;
      if (options.mode === 'selected') {
        ids =
          selectRecords.length > 0
            ? selectRecords.map((item: any) => item.id)
            : [];
      } else {
        ids = undefined;
      }
      request.ids = ids;

      // 如果是导出选中数据且没有选中任何行，提示用户
      if (
        options.mode === 'selected' &&
        (!selectRecords || selectRecords.length === 0)
      ) {
        message.warning('请先选择要导出的数据');
        return false;
      }

      // 确保文件名有正确的扩展名
      if (
        request.filename &&
        !request.filename.endsWith('.xlsx') &&
        !request.filename.endsWith('.csv')
      ) {
        request.filename += '.xlsx';
      }

      // 调用导出API
      await exportAndDownload(request);

      message.success('导出成功');
      return true;
    } catch (error) {
      console.error('导出失败', error);
      message.error(
        `导出失败：${error instanceof Error ? error.message : '未知错误'}`,
      );
      return false;
    }
  };
}

// 新增表单配置 - 不在内部直接使用createFormApi
const createFormOptions: VbenFormProps = {
  showDefaultActions: false,
  schema: [
    {
      fieldName: 'title',
      label: '故障标题',
      component: 'Input',
      componentProps: { placeholder: '请输入故障标题' },
      rules: 'required',
    },
    {
      fieldName: 'creationTime',
      label: '事件发生时间',
      component: 'DatePicker',
      componentProps: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        placeholder: '请选择告警事件发生时间',
        disabledDate: (current: any) => current && current > Date.now(),
      },
      rules: 'required',
    },
    {
      fieldName: 'deviceSN',
      label: '设备SN',
      component: 'Input',
      componentProps: {
        placeholder: '请输入设备SN',
        async onBlur(e: Event) {
          const sn = (e.target as HTMLInputElement).value;
          if (!sn) return;

          await processSN(sn);
        },
        allowClear: true,
        onChange: (e: Event) => {
          const target = e.target as HTMLInputElement;
          const sn = target.value;
          currentDeviceSN.value = sn;

          if (sn) {
            pendingSN.value = sn;
          }
        },
      },
      rules: 'required',
    },
    {
      fieldName: 'faultDescription',
      label: '故障描述',
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入故障描述',
        rows: 4,
      },
      rules: 'required',
    },
    {
      fieldName: 'images',
      label: '图片上传',
      component: 'Input',
      componentProps: {
        type: 'hidden',
      },
      defaultValue: '', // 设置默认值为空字符串
      hideLabel: true,
    },
  ],
};

// 在createFormApi定义之后设置formApiRef
const [CreateForm, createFormApi] = useVbenForm(createFormOptions);
// 添加维修单缓存
const repairTicketCache = ref<Map<number, any>>(new Map());
formApiRef.value = createFormApi;

// 监听pendingSN，当有值且formApiRef.value可用时处理
watch(
  pendingSN,
  (newSN) => {
    if (newSN && formApiRef.value) {
      debouncedProcessSN(newSN);
      pendingSN.value = '';
    }
  },
  { immediate: true },
);

// 分配工程师表单
const [AssignForm, assignFormApi] = useVbenForm(assignFormOptions);

// 维修选择表单
const [RepairForm, repairFormApi] = useVbenForm(
  getRepairFormOptions(repairTypeOptions),
);

// 客户审批表单
const [ApprovalForm, approvalFormApi] = useVbenForm(approvalFormOptions);

// 验证表单
const [VerifyForm, verifyFormApi] = useVbenForm(verifyFormOptions);

// 关闭工单表单
const [CloseForm, closeFormApi] = useVbenForm(closeFormOptions);

// 初始化图片列表
const uploadedImages = ref<any[]>([]);

// 创建报障单的图片列表
let createFormImages: any[] = [];

// 是否显示维修模态框
const repairModalVisible = ref(false);

// 是否显示创建报障单模态框
const createModalVisible = ref(false);

// 新增模态框
const [CreateModal, createModalApi] = useVbenModal({
  title: '新增报障单',
  confirmText: '确认',
  cancelText: '取消',
  class: 'create-modal w-1/2 h-full',
  onConfirm: async () => {
    try {
      const values = await createFormApi.getValues();

      // 检查必填字段是否有值
      if (!values.title) {
        message.warning('请输入故障标题');
        return;
      }

      if (!values.deviceSN) {
        message.warning('请输入设备SN');
        return;
      }

      if (!values.faultDescription) {
        message.warning('请输入故障描述');
        return;
      }

      if (!values.creationTime) {
        message.warning('请选择事件发生时间');
        return;
      }

      const valid = await createFormApi.validate();
      if (valid) {
        await handleAddFaultTicket();
      }
    } catch (error) {
      console.error('表单验证失败', error);
      message.error('表单验证失败');
    }
  },
});

// 分配模态框
const [AssignModal, assignModalApi] = createAssignModal(
  assignFormApi,
  selectRow,
  () => {
    gridApi.reload();
  },
);

// 维修选择模态框
const [RepairModal, repairModalApi] = createRepairModal(
  repairFormApi,
  selectRow,
  () => {
    // 成功提交后重置图片并重新加载列表
    uploadedImages.value = [];
    gridApi.reload();
  },
  uploadedImages, // 传递图片数组引用
);

// 客户审批模态框
const [ApprovalModal, approvalModalApi] = createApprovalModal(
  approvalFormApi,
  selectRow,
  () => {
    gridApi.reload();
  },
);

// 验证模态框
const [VerifyModal, verifyModalApi] = createVerifyModal(
  verifyFormApi,
  selectRow,
  () => {
    gridApi.reload();
  },
);

// 关闭工单模态框
const [CloseModal, closeModalApi] = createCloseModal(
  closeFormApi,
  selectRow,
  () => {
    gridApi.reload();
  },
);

// 监听模态框可见性变化
watch(
  () => repairModalApi,
  (modalApi) => {
    if (modalApi) {
      // 监听原始打开方法，添加自定义处理
      const originalOpen = modalApi.open;
      modalApi.open = () => {
        repairModalVisible.value = true;
        // 清空之前的图片列表
        uploadedImages.value = [];
        return originalOpen();
      };

      // 监听原始关闭方法
      const originalClose = modalApi.close;
      modalApi.close = () => {
        repairModalVisible.value = false;
        return originalClose();
      };
    }
  },
  { immediate: true, deep: true },
);

// 监听创建报障单模态框可见性变化
watch(
  () => createModalApi,
  (modalApi) => {
    if (modalApi) {
      // 监听原始打开方法，添加自定义处理
      const originalOpen = modalApi.open;
      modalApi.open = () => {
        createModalVisible.value = true;
        // 清空之前的图片列表
        createFormImages = [];
        return originalOpen();
      };

      // 监听原始关闭方法
      const originalClose = modalApi.close;
      modalApi.close = () => {
        createModalVisible.value = false;
        return originalClose();
      };
    }
  },
  { immediate: true, deep: true },
);

// 新增事件
function addEvent() {
  createFormApi.setValues(XEUtils.clone(defaultFaultTicket, true));
  createModalApi.open();
}

// 处理新增报障单
async function handleAddFaultTicket() {
  try {
    const values = await createFormApi.getValues();

    // 准备提交数据
    const submitData: any = {
      title: values.title,
      deviceSN: values.deviceSN,
      faultType: 'hardware', // 默认硬件故障
      faultDescription: values.faultDescription,
      priority: 'high', // 默认高优先级
      source: values.source || 'manual',
      creationTime: values.creationTime,
    };

    // 创建报障单，获取ID
    const response = await addFaultTicketApi(submitData);

    // 获取新创建的报障单ID
    const ticketId =
      response && typeof response === 'object'
        ? response.id || (response as any).data?.id
        : null;

    if (ticketId && createFormImages && createFormImages.length > 0) {
      try {
        message.loading({
          content: '正在上传图片...',
          key: 'uploading-images',
          duration: 0,
        });

        // 使用真实的报障单ID上传图片
        await uploadImagesToServer(createFormImages, ticketId);

        message.success({
          content: '图片上传完成',
          key: 'uploading-images',
          duration: 2,
        });
      } catch (error) {
        console.error('上传图片失败', error);
        message.error('图片上传失败，但报障单已创建');
      }
    }

    message.success('报障单创建成功');
    createModalApi.close();
    gridApi.reload();
    // 清空上传的图片
    createFormImages = [];
  } catch (error) {
    console.error('创建报障单失败', error);
    message.error('创建报障单失败');
  }
}

// 分配事件
function assignEvent(row: FaultTicket) {
  selectRow.value = row;
  assignFormApi.setValues({});
  assignModalApi.open();
}

// 接单事件
function acceptEvent(row: FaultTicket) {
  selectRow.value = row;
  acceptTicket(row, () => {
    gridApi.reload();
  });
}

// 提交维修方案事件
function repairSelectionEvent(row: FaultTicket) {
  selectRow.value = row;

  // 使用类型断言获取可能存在的is_frequent_fault属性
  const rowAny = row as any;

  // 检查可能的属性名 - TypeScript声明使用isFrequentFault，而后端返回可能是is_frequent_fault
  const isHighFrequencyFault =
    row.is_frequent_fault || rowAny.is_frequent_fault;

  // 如果是高频故障，显示二次确认提示框
  if (isHighFrequencyFault) {
    Modal.confirm({
      title: '高频故障提醒',
      content: '该设备为高频故障，请确认是否继续提交维修方案？',
      okText: '继续提交',
      cancelText: '取消',
      okType: 'danger',
      onOk: () => {
        // 确认后执行原有逻辑
        openRepairModal(row);
      },
    });
  } else {
    // 不是高频故障，直接执行
    openRepairModal(row);
  }
}

// 打开维修模态框的逻辑，从原函数中提取
function openRepairModal(row: FaultTicket) {
  // 如果存在租户IP，重新初始化表单
  if (row.resource_identifier) {
    // 使用新表单定义替换旧表单
    repairFormApi.updateSchema(
      getRepairFormOptions(
        repairTypeOptions,
        row.source === 'customer',
        false,
        row.resource_identifier,
      ).schema ?? [],
    );
  } else {
    // 无租户IP时使用默认表单
    repairFormApi.updateSchema(
      getRepairFormOptions(repairTypeOptions, row.source === 'customer', false)
        .schema ?? [],
    );
  }

  // 清空表单并设置初始值
  repairFormApi.setValues({
    // 如果来源是客户报障，设置默认不需要审批
    requireCustomerApproval: row.source !== 'customer',
    images: [], // 初始化images为空数组
    // 如果有租户IP，也设置到表单值中
    tenantIp: row.resource_identifier || '',
  });

  // 清空上传图片列表
  uploadedImages.value = [];

  repairModalVisible.value = true;
  repairModalApi.open();
}

// 客户审批事件
function customerApprovalEvent(row: FaultTicket) {
  selectRow.value = row;
  approvalFormApi.setValues({});
  approvalModalApi.open();
}

// 开始维修事件
function startRepairEvent(row: FaultTicket) {
  selectRow.value = row;
  startRepair(row, () => {
    gridApi.reload();
  });
}

// 完成维修事件
function completeRepairEvent(row: FaultTicket) {
  selectRow.value = row;
  completeRepair(row, () => {
    gridApi.reload();
  });
}

// 取消工单事件
function cancelTicketEvent(row: FaultTicket) {
  selectRow.value = row;
  cancelTicket(row, () => {
    gridApi.reload();
  });
}

// 验证事件
function verificationEvent(row: FaultTicket) {
  selectRow.value = row;
  verifyFormApi.setValues({});
  verifyModalApi.open();
}

// 关闭工单事件
function closeTicketEvent(row: FaultTicket) {
  selectRow.value = row;

  // 预设初始值
  const initialValues: Record<string, any> = {
    // 如果没有租户IP，默认不计入SLA
    count_in_sla: row.resource_identifier ? row.count_in_sla !== false : false,
  };

  // 预填入具体故障类型（如果存在）
  if (row.fault_detail_type) {
    initialValues.fault_detail_type = row.fault_detail_type;
  }

  // 预填入故障类型
  if (row.faultType) {
    initialValues.faultType = row.faultType;
  }

  // 预填入修复方法，从上一步维修完成记录中获取
  initialValues.repair_method = row.repairMethod || '';

  // 设置表单初始值
  closeFormApi.setValues(initialValues);
  closeModalApi.open();
}

// 处理跳转到详情页面
function navigateToDetailPage(row: FaultTicket) {
  // 使用路由跳转到详情页面
  window.open(`/fault-report-management/detail/${row.id}`, '_blank');
}

// 获取维修单详情
async function getRepairTicketDetails(repairTicketId: number) {
  // 如果缓存中已经有此维修单信息，直接返回
  if (repairTicketCache.value.has(repairTicketId)) {
    return repairTicketCache.value.get(repairTicketId);
  }

  try {
    const repairTicket = await getRepairTicketDetailApi(repairTicketId);
    // 缓存结果
    repairTicketCache.value.set(repairTicketId, repairTicket);
    return repairTicket;
  } catch (error) {
    console.error('获取维修单详情失败', error);
    return null;
  }
}

// 跳转到维修单详情页
function navigateToRepairDetail(repairTicketId: number) {
  if (!repairTicketId) return;
  window.open(`/hardware-order/${repairTicketId}`, '_blank');
}

// 故障详情模态框
const [FaultDetailModal, faultDetailModalApi] = useVbenModal({
  title: '故障详情',
  showConfirmButton: false,
  cancelText: '关闭',
});

const faultDetailContent = ref('');

// 展示故障详情模态框
function showFaultDetailModal(detailJson: any | string): void {
  try {
    faultDetailContent.value = formatFaultDetail(detailJson);
    faultDetailModalApi.open();
  } catch (error) {
    console.error('解析故障详情失败', error);
    message.error('解析故障详情失败');
  }
}

// 获取预览内容
function getPreviewContent(jsonStr: any | string): string {
  try {
    if (!jsonStr) return '';
    const data = typeof jsonStr === 'string' ? JSON.parse(jsonStr) : jsonStr;

    // 检查是否包含fault_detail字段
    if (data && typeof data === 'object' && data.fault_detail) {
      let content = '';

      try {
        const detailArray = JSON.parse(data.fault_detail);
        if (Array.isArray(detailArray) && detailArray.length > 0) {
          // 只展示第一条数据作为预览
          const item = detailArray[0];
          content = `
            <div style="max-width: 350px;">
              <div><strong>类型:</strong> ${item.type || '-'}</div>
              <div><strong>详情:</strong> ${(item.detail || '-').slice(0, 50)}${item.detail && item.detail.length > 50 ? '...' : ''}</div>
              ${item.location ? `<div><strong>位置:</strong> ${item.location}</div>` : ''}
              ${item.model ? `<div><strong>型号:</strong> ${item.model}</div>` : ''}
              ${detailArray.length > 1 ? `<div style="color: #faad14;">还有 ${detailArray.length - 1} 条记录...</div>` : ''}
            </div>
          `;
        }
        return content;
      } catch {
        return '<div>包含JSON格式的故障详情</div>';
      }
    }

    // 其他情况
    return '<div>包含JSON格式的故障详情</div>';
  } catch {
    return '<div>包含JSON格式的故障详情</div>';
  }
}

// 添加复制到剪贴板功能
function copyToClipboard(text: string) {
  // 创建临时输入元素
  const input = document.createElement('input');
  input.setAttribute('value', text);
  document.body.append(input);
  input.select();

  // 尝试执行复制命令
  let success = false;
  try {
    success = document.execCommand('copy');
  } catch (error) {
    console.error('复制失败', error);
  }

  // 移除临时元素
  input.remove();

  // 提示用户
  if (success) {
    message.success('已复制到剪贴板');
  } else {
    message.error('复制失败');
  }

  return success;
}

// 添加以下函数来处理维修单号的加载和显示
// 加载维修单详情
async function loadRepairTicketDetails(repairTicketId: number) {
  if (!repairTicketId) return;
  await getRepairTicketDetails(repairTicketId);
}

// 获取维修单号显示文本
function getRepairTicketNo(repairTicketId: number) {
  if (!repairTicketId) return '无维修单';

  // 从缓存中获取数据
  const repairTicket = repairTicketCache.value.get(repairTicketId);
  if (repairTicket && repairTicket.ticket_no) {
    return repairTicket.ticket_no;
  }

  // 如果没有缓存数据，显示加载中
  return '加载中...';
}

function formatDowntime(minutes: number): string {
  if (minutes === 0) return '0分钟';
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  if (hours === 0) return `${mins}分钟`;
  return `${hours}小时${mins}`;
}
</script>

<template>
  <Page auto-content-height>
    <!-- 表格区域 -->
    <Grid>
      <!-- 状态插槽 -->
      <template #status="{ row }">
        <Tag :color="statusMap[row.status as FaultTicketStatus]?.color">
          {{ statusMap[row.status as FaultTicketStatus]?.text || row.status }}
        </Tag>
      </template>

      <!-- 优先级插槽 -->
      <template #priority="{ row }">
        <Tag :color="priorityMap[row.priority as Priority]?.color">
          {{ priorityMap[row.priority as Priority]?.text || row.priority }}
        </Tag>
      </template>

      <!-- 故障类型插槽 -->
      <template #faultType="{ row }">
        <div>
          {{
            row.fault_detail_type
              ? row.fault_detail_type.split('-')[0].replace(/故障$/, '')
              : ''
          }}
        </div>
      </template>

      <!-- 故障标题插槽 -->
      <template #title="{ row }">
        <a
          class="cursor-pointer text-blue-500 hover:underline"
          @click="navigateToDetailPage(row)"
        >
          {{ row.title }}
        </a>
      </template>

      <!-- 资产类型插槽 -->
      <template #assetType="{ row }">
        {{
          assetTypeOptions.find(
            (item) => item.value === row.resource.device.assetType,
          )?.label || '-'
        }}
      </template>

      <!-- 处理人插槽 -->
      <template #assignedTo="{ row }">
        {{ row.assignedTo || '' }}
      </template>

      <!-- 报错项目插槽-->>
      <template #project="{ row }">
        {{ row.resource.project || '-' }}
      </template>

      <!-- 故障SLA插槽 -->
      <!-- 故障SLA插槽 -->
      <template #faultSLADuration="{ row }">
        {{ !row.count_in_sla ? '不记入' : formatDowntime(row.totalDowntime) }}
      </template>

      <!-- 响应时长插槽-->>
      <template #response_duration="{ row }">
        {{ formatDowntime(row.response_duration) }}
      </template>

      <!-- 报障单号插槽 -->
      <template #ticketNo="{ row }">
        <div>
          <a
            class="cursor-pointer text-blue-500 hover:underline"
            @click="navigateToDetailPage(row)"
          >
            <Tooltip
              v-if="row.repair_ticket_id || row.repairTicketID"
              placement="topLeft"
              :mouse-enter-delay="0.5"
              overlay-class-name="repair-ticket-tooltip"
              :overlay-style="{ maxWidth: '300px' }"
              @open-change="
                (visible) =>
                  visible &&
                  loadRepairTicketDetails(
                    row.repair_ticket_id || row.repairTicketID,
                  )
              "
            >
              <template #title>
                <div class="p-1">
                  <div class="mb-1 font-medium text-white">维修单信息</div>
                  <div class="flex items-center justify-between">
                    <span class="text-gray-300">维修单号：</span>
                    <span class="text-white">{{
                      getRepairTicketNo(
                        row.repair_ticket_id || row.repairTicketID,
                      )
                    }}</span>
                  </div>
                  <div class="mt-1 text-green-400">
                    <svg
                      viewBox="64 64 896 896"
                      class="mr-1 inline-block"
                      width="14"
                      height="14"
                      fill="currentColor"
                    >
                      <path
                        d="M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                      <path
                        d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
                      />
                    </svg>
                    点击查看维修单详情
                  </div>
                </div>
              </template>
              <div>
                <span>{{ row.ticketNo }}</span>

                <Tag
                  class="ml-2 cursor-pointer"
                  color="processing"
                  @click.stop="
                    navigateToRepairDetail(
                      row.repairTicketID || row.repair_ticket_id,
                    )
                  "
                >
                  维修单
                </Tag>
              </div>
            </Tooltip>

            <span v-else>
              {{ row.ticketNo }}
            </span>
          </a>
        </div>
      </template>

      <!-- 设备SN插槽 -->
      <template #deviceSN="{ row }">
        <div class="flex w-full items-center justify-center">
          <Tooltip
            v-if="row.resource"
            placement="topLeft"
            :mouse-enter-delay="0.3"
            overlay-class-name="resource-info-tooltip"
            :overlay-style="{ maxWidth: '320px' }"
          >
            <template #title>
              <div class="p-1">
                <div class="mb-2 text-base font-medium text-white">
                  设备信息
                </div>
                <div class="mb-1">
                  <div class="flex items-center justify-between">
                    <span class="font-medium text-gray-300">主机名：</span>
                    <div class="flex items-center">
                      <span class="mr-1 text-white">{{
                        row.resource.hostname || '-'
                      }}</span>
                      <Button
                        type="link"
                        size="small"
                        class="flex items-center px-1 py-0 text-white"
                        @click.stop="copyToClipboard(row.resource.hostname)"
                      >
                        <svg
                          viewBox="64 64 896 896"
                          focusable="false"
                          data-icon="copy"
                          width="1em"
                          height="1em"
                          fill="currentColor"
                          aria-hidden="true"
                        >
                          <path
                            d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"
                          />
                        </svg>
                      </Button>
                    </div>
                  </div>
                </div>
                <div class="mb-1">
                  <div class="flex items-center justify-between">
                    <span class="font-medium text-gray-300">集群：</span>
                    <div class="flex items-center">
                      <span class="mr-1 text-white">{{
                        row.resource.cluster || '-'
                      }}</span>
                      <Button
                        type="link"
                        size="small"
                        class="flex items-center px-1 py-0 text-white"
                        @click.stop="copyToClipboard(row.resource.cluster)"
                      >
                        <svg
                          viewBox="64 64 896 896"
                          focusable="false"
                          data-icon="copy"
                          width="1em"
                          height="1em"
                          fill="currentColor"
                          aria-hidden="true"
                        >
                          <path
                            d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"
                          />
                        </svg>
                      </Button>
                    </div>
                  </div>
                </div>
                <div class="mb-1">
                  <div class="flex items-center justify-between">
                    <span class="font-medium text-gray-300">VPC IP：</span>
                    <div class="flex items-center">
                      <span class="mr-1 text-white">{{
                        row.resource.vpcIP || '-'
                      }}</span>
                      <Button
                        type="link"
                        size="small"
                        class="flex items-center px-1 py-0 text-white"
                        @click.stop="copyToClipboard(row.resource.vpcIP)"
                      >
                        <svg
                          viewBox="64 64 896 896"
                          focusable="false"
                          data-icon="copy"
                          width="1em"
                          height="1em"
                          fill="currentColor"
                          aria-hidden="true"
                        >
                          <path
                            d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"
                          />
                        </svg>
                      </Button>
                    </div>
                  </div>
                </div>
                <div class="mb-1">
                  <div class="flex items-center justify-between">
                    <span class="font-medium text-gray-300">BMC IP：</span>
                    <div class="flex items-center">
                      <span class="mr-1 text-white">{{
                        row.resource.bmcIP || '-'
                      }}</span>
                      <Button
                        type="link"
                        size="small"
                        class="flex items-center px-1 py-0 text-white"
                        @click.stop="copyToClipboard(row.resource.bmcIP)"
                      >
                        <svg
                          viewBox="64 64 896 896"
                          focusable="false"
                          data-icon="copy"
                          width="1em"
                          height="1em"
                          fill="currentColor"
                          aria-hidden="true"
                        >
                          <path
                            d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"
                          />
                        </svg>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </template>

            <span class="text-gray-800">{{ row.deviceSN }}</span>
          </Tooltip>
          <span v-if="!row.resource" class="text-gray-800">{{
            row.deviceSN
          }}</span>
        </div>
      </template>

      <!-- 租户IP插槽 -->
      <template #resource_identifier="{ row }">
        <div
          v-if="
            row.resource_identifier && row.resource_identifier.includes('{')
          "
        >
          <Tooltip
            placement="topLeft"
            :mouse-enter-delay="0.5"
            overlay-class-name="json-preview-tooltip"
            :overlay-style="{ maxWidth: '400px' }"
          >
            <template #title>
              <div>
                <div v-if="row.instance_ipv4" style="margin-bottom: 5px">
                  <strong>IP: </strong>{{ row.instance_ipv4 }}
                </div>
                <div v-if="row.instance_id" style="margin-bottom: 5px">
                  <strong>实例ID: </strong>{{ row.instance_id }}
                </div>
                <div style="color: #1890ff">点击查看完整信息</div>
              </div>
            </template>
            <a
              class="cursor-pointer text-blue-500 hover:underline"
              @click="showFaultDetailModal(row.resource_identifier)"
            >
              {{ row.instance_ipv4 || '查看详情' }}
            </a>
          </Tooltip>
        </div>
        <div v-else>
          {{ row.resource_identifier }}
        </div>
      </template>

      <!-- 故障描述插槽 -->
      <template #faultDescription="{ row }">
        <div
          v-if="
            row.faultDescription &&
            (row.faultDescription.includes('{') ||
              row.faultDescription.includes('['))
          "
        >
          <Tooltip
            placement="topLeft"
            :mouse-enter-delay="0.5"
            overlay-class-name="json-preview-tooltip"
            :overlay-style="{ maxWidth: '400px' }"
          >
            <template #title>
              <div>
                <!-- eslint-disable vue/no-v-html -->
                <div
                  v-if="getPreviewContent(row.faultDescription)"
                  v-html="getPreviewContent(row.faultDescription)"
                ></div>
                <div style="color: #1890ff">点击查看完整信息</div>
              </div>
            </template>
            <a
              class="cursor-pointer text-blue-500 hover:underline"
              @click="showFaultDetailModal(row.faultDescription)"
            >
              {{
                row.faultDescription && row.faultDescription.length > 20
                  ? `${row.faultDescription.substring(0, 20)}...`
                  : row.faultDescription
              }}
            </a>
          </Tooltip>
        </div>
        <div v-else>
          {{ row.faultDescription }}
        </div>
      </template>

      <!-- 操作插槽 -->
      <template #operate="{ row }">
        <Space>
          <!-- 如果没有设备SN，显示"补充SN"按钮 -->
          <template v-if="!row.deviceSN">
            <Tooltip title="请前往详情页补充设备SN信息">
              <Button
                type="link"
                size="small"
                @click="navigateToDetailPage(row)"
                style="color: #faad14"
              >
                补充SN
              </Button>
            </Tooltip>
          </template>

          <!-- 显示当前状态文本，但已完成和已取消状态不显示 -->
          <template
            v-else-if="row.status !== 'completed' && row.status !== 'cancelled'"
          >
            <a
              class="cursor-pointer text-blue-500 hover:underline"
              @click="navigateToDetailPage(row)"
              style="color: #1890ff"
            >
              {{
                statusMap[row.status as FaultTicketStatus]?.text || row.status
              }}
            </a>
          </template>

          <!-- 常规操作按钮 (已隐藏但保留代码) -->
          <template v-if="false">
            <template
              v-for="action in getAvailableActions(row, {
                startRepairEvent,
                repairSelectionEvent,
                cancelTicketEvent,
                completeRepairEvent,
                closeTicketEvent,
                acceptEvent,
                assignEvent,
                customerApprovalEvent,
                verificationEvent,
              })"
              :key="action.key"
            >
              <!-- 使用自定义渲染 -->
              <div v-if="action.customRender">
                <!-- 使用字符串模板替代函数调用以避免类型错误 -->
                <Button type="link" size="small" @click="action.action">
                  {{ action.text || '操作' }}
                </Button>
              </div>

              <!-- 默认渲染 -->
              <Tooltip v-else :title="`执行${action.text}`">
                <Button
                  type="link"
                  size="small"
                  @click="action.action"
                  :disabled="action.disabled"
                >
                  {{ action.text }}
                </Button>
              </Tooltip>
            </template>
          </template>
        </Space>
      </template>

      <!-- 工具栏按钮 -->
      <template #toolbar-buttons>
        <AccessControl :codes="['super']">
          <Button class="mr-2" type="primary" @click="addEvent">
            创建报障单
          </Button>
        </AccessControl>
      </template>
    </Grid>

    <!-- 新增报障单模态框 -->
    <CreateModal>
      <template #default>
        <CreateForm />
        <div class="mt-2 flex justify-end">
          <Button type="primary" @click="handleVerifySN"> 验证设备SN </Button>
        </div>

        <!-- 添加图片上传组件 -->
        <div class="p-4">
          <ImageUploader
            :value="createFormImages"
            :modal-visible="createModalVisible"
            @update:value="
              (newImages) => {
                createFormImages = newImages;
                // 同步图片数据到表单
                if (createFormApi) {
                  const currentValues = createFormApi.getValues();
                  createFormApi.setValues({
                    ...currentValues,
                    images: newImages.length > 0 ? 'has_images' : '', // 只存储标记字符串
                  });
                }
              }
            "
          />
        </div>
      </template>
    </CreateModal>

    <!-- 分配工程师模态框 -->
    <AssignModal>
      <AssignForm />
    </AssignModal>

    <!-- 维修选择模态框 -->
    <RepairModal>
      <div class="p-4">
        <RepairForm />

        <!-- 使用新的ImageUploader组件 -->
        <ImageUploader
          v-model:value="uploadedImages"
          :modal-visible="repairModalVisible"
        />
      </div>
    </RepairModal>

    <!-- 客户审批模态框 -->
    <ApprovalModal>
      <ApprovalForm />
    </ApprovalModal>

    <!-- 验证结果模态框 -->
    <VerifyModal>
      <VerifyForm />
    </VerifyModal>

    <!-- 关闭工单模态框 -->
    <CloseModal>
      <CloseForm />
    </CloseModal>

    <!-- 故障详情模态框 -->
    <FaultDetailModal>
      <div v-html="faultDetailContent" class="fault-detail-container"></div>
    </FaultDetailModal>
  </Page>
</template>

<style lang="less" scoped>
/* 添加样式使故障详情模态框更宽并有滚动条 */
.fault-detail-container {
  max-height: 80vh;
  overflow: auto;
  padding: 16px;
}

/* 创建报障单模态框样式 */
:deep(.create-modal .ant-modal-content) {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

:deep(.create-modal .ant-form-item-label > label) {
  font-weight: 500;
  color: #333;
}

:deep(
  .create-modal .ant-form-item-label > label.ant-form-item-required::before
) {
  color: #f56c6c;
}

:deep(.create-modal .ant-textarea) {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  transition: all 0.3s;
}

:deep(.create-modal .ant-textarea:hover) {
  border-color: #1890ff;
}

:deep(.create-modal .ant-textarea:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  background-color: #fff;
}

.fault-detail-container {
  max-height: 80vh;
  overflow: auto;
  padding: 16px;
}

:deep(.ant-modal-content) {
  width: auto !important;
  min-width: 800px;
}

:deep(.ant-modal-body) {
  max-height: 80vh;
  overflow: auto;
}

/* 添加全局样式，使用深度选择器 */
:deep(.repair-modal .ant-modal-content) {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

:deep(.repair-modal .ant-form-item-label > label) {
  font-weight: 500;
  color: #333;
}

:deep(
  .repair-modal .ant-form-item-label > label.ant-form-item-required::before
) {
  color: #f56c6c;
}

:deep(.repair-modal .ant-select-selector) {
  border-radius: 0.375rem !important;
}

:deep(.repair-modal .ant-upload.ant-upload-select-picture-card) {
  border-radius: 0.375rem;
  border-color: #d9d9d9;
  transition: all 0.3s;
}

:deep(.repair-modal .ant-upload.ant-upload-select-picture-card:hover) {
  border-color: #1890ff;
}

:deep(.repair-modal .ant-textarea) {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  transition: all 0.3s;
}

:deep(.repair-modal .ant-textarea:hover) {
  border-color: #1890ff;
}

:deep(.repair-modal .ant-textarea:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  background-color: #fff;
}

/* 资源信息tooltip样式 */
:deep(.resource-info-tooltip) {
  max-width: 360px !important;
}

:deep(.resource-info-tooltip .ant-tooltip-inner) {
  padding: 12px 16px;
  background-color: rgba(0, 0, 0, 0.85);
  color: #fff;
  border-radius: 6px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

:deep(.resource-info-tooltip .ant-tooltip-arrow-content) {
  background-color: rgba(0, 0, 0, 0.85);
}

:deep(.resource-info-tooltip .ant-btn-link) {
  padding: 0 4px;
  height: auto;
  line-height: 1;
  color: #40a9ff;
}

:deep(.resource-info-tooltip .ant-btn-link:hover) {
  color: #69c0ff;
  background: rgba(24, 144, 255, 0.1);
  border-radius: 2px;
}

/* 维修单tooltip样式 */
:deep(.repair-ticket-tooltip) {
  max-width: 320px !important;
}

:deep(.repair-ticket-tooltip .ant-tooltip-inner) {
  padding: 12px 16px;
  background-color: rgba(0, 0, 0, 0.85);
  color: #fff;
  border-radius: 6px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

:deep(.repair-ticket-tooltip .ant-tooltip-arrow-content) {
  background-color: rgba(0, 0, 0, 0.85);
}

:deep(.repair-ticket-tooltip .ant-btn) {
  margin-top: 8px;
  margin-bottom: 4px;
  width: 100%;
  background-color: #1890ff;
  border-color: #1890ff;
  transition: all 0.3s;
}

:deep(.repair-ticket-tooltip .ant-btn:hover) {
  background-color: #40a9ff;
  border-color: #40a9ff;
}
</style>
