import type { UserInfo } from '@vben/types';

import { requestClient } from '#/api/request';

export namespace UserApi {
  /** 用户信息接口返回值 */
  export interface UserInfo {
    id: number;
    userName: string;
    realName: string;
    email: string;
    telephone: string;
    department: string;
    roleName: string;
    roles: string[];
    status: string;
    createdAt: string;
    updatedAt: string;
  }

  /** 用户列表查询参数 */
  export interface UserListParams {
    page: number;
    pageSize: number;
    query?: string;
    status?: string;
  }

  /** 用户列表返回值 */
  export interface UserListResult {
    total: number;
    list: UserInfo[];
  }

  /** 修改密码参数 */
  export interface ChangePasswordParams {
    oldPassword: string;
    newPassword: string;
  }

  /** 更新用户参数 */
  export interface UpdateUserParams {
    id: number;
    realName?: string;
    telephone?: string;
    email?: string;
    department?: string;
    roles?: string[];
    roleName?: string;
    status?: string;
  }

  /** 管理员创建用户参数 */
  export interface CreateUserParams {
    username: string;
    password: string;
    realName: string;
    email?: string;
    telephone?: string;
    department?: string;
    roleName: string;
    roles: string[];
    status: string;
  }

  /** 管理员重置密码参数 */
  export interface ResetPasswordParams {
    userId: number;
    newPassword: string;
  }
}

/**
 * 获取用户信息
 */
export async function getUserInfoApi() {
  return requestClient.get<UserInfo>('/auth/userInfo');
}

/**
 * 获取用户列表
 */
export async function getUserListApi(params: UserApi.UserListParams) {
  return requestClient.get<UserApi.UserListResult>('/auth/user-list', {
    params,
  });
}

/**
 * 修改密码
 */
export async function changePasswordApi(data: UserApi.ChangePasswordParams) {
  return requestClient.post('/auth/change-password', data);
}

/**
 * 更新用户信息
 */
export async function updateUserApi(data: UserApi.UpdateUserParams) {
  return requestClient.put('/auth/user-update', data);
}

/**
 * 删除用户
 */
export async function deleteUserApi(id: number) {
  return requestClient.delete(`/auth/user/${id}`);
}

/**
 * 管理员创建用户
 */
export async function createUserApi(data: UserApi.CreateUserParams) {
  return requestClient.post('/auth/admin/create-user', data);
}

/**
 * 管理员重置密码
 */
export async function resetPasswordApi(data: UserApi.ResetPasswordParams) {
  return requestClient.post('/auth/admin/reset-password', data);
}
