import { requestClient } from '#/api/request';

// 库存明细接口
export interface InventoryDetail {
  id?: number;
  created_at?: string;
  updated_at?: string;
  product_id: number;
  product?: any;
  warehouse: string;
  warehouse_location: string;
  current_stock: number;
  allocated_stock: number;
  available_stock: number;
  batch_number?: string;
  unit_price?: number;
  inbound_date?: string;
  warranty_start?: string;
  warranty_end?: string;
  inbound_id?: number;
  status: string;
  warehouse_id?: number;
}

// 库存汇总信息
export interface InventorySummary {
  product_id: number;
  product_model: string;
  product_pn: string;
  total_stock: number;
  allocated_stock: number;
  available_stock: number;
  inbound_items: number;
  outbound_items: number;
}

// 低库存产品
export interface LowStockProduct {
  product_id: number;
  product_model: string;
  current_stock: number;
  threshold: number;
  category: string;
}

// 即将过保项目
export interface ExpiringWarrantyItem {
  id: number;
  product_id: number;
  product_model: string;
  batch_number: string;
  warranty_end: string;
  remaining_days: number;
  current_stock: number;
}

// 库存变更历史
export interface StockHistory {
  id: number;
  created_at: string;
  updated_at: string;
  detail_id: number;
  product_id: number;
  old_quantity: number;
  new_quantity: number;
  change_amount: number;
  change_type: string;
  change_time: string;
  operator_id: number;
  operator: string;
  reason: string;
  batch_number: string;
  inbound_id: number;
  outbound_id: number;
  warehouse_id: number;
}

// 库存查询参数
export interface GetInventoryParams {
  page?: number;
  pageSize?: number;
  productID?: number;
  warehouse?: string;
  materialType?: string;
  pn?: string;
  spec?: string;
  warehouseId?: number;
  hasAvailable?: boolean;
  brand?: string;
  showAll?: boolean;
  viewMode?: string;
}

// 分页结果接口
export interface PageResult<T> {
  list: T[];
  total: number;
}

// 库存列表响应
export type GetInventoryResponse = PageResult<InventoryDetail>;

// 调整库存请求
export interface AdjustStockRequest {
  quantity: number;
  reason: string;
}

// 分配库存请求
export interface AllocateStockRequest {
  quantity: number;
  purpose: string;
}

// 释放库存请求
export interface ReleaseStockRequest {
  quantity: number;
}

/**
 * 获取库存明细列表
 * @param params 查询参数
 */
export async function getInventoryListApi(params: GetInventoryParams) {
  return requestClient.get<GetInventoryResponse>('/cmdb/inventory', {
    params,
  });
}

/**
 * 获取库存明细详情
 * @param id 库存明细ID
 */
export async function getInventoryDetailApi(id: number) {
  return requestClient.get<InventoryDetail>(`/cmdb/inventory/${id}`);
}

/**
 * 创建库存明细
 * @param data 库存明细数据
 */
export async function createInventoryApi(data: InventoryDetail) {
  return requestClient.post<InventoryDetail>('/cmdb/inventory', data);
}

/**
 * 更新库存明细
 * @param data 库存明细数据
 */
export async function updateInventoryApi(data: InventoryDetail) {
  return requestClient.put(`/cmdb/inventory/${data.id}`, data);
}

/**
 * 删除库存明细
 * @param id 库存明细ID
 */
export async function deleteInventoryApi(id: number) {
  return requestClient.delete(`/cmdb/inventory/${id}`);
}

/**
 * 调整库存数量
 * @param id 库存明细ID
 * @param data 调整信息
 */
export async function adjustStockApi(id: number, data: AdjustStockRequest) {
  return requestClient.post(`/cmdb/inventory/${id}/adjust`, data);
}

/**
 * 分配库存
 * @param id 库存明细ID
 * @param data 分配信息
 */
export async function allocateStockApi(id: number, data: AllocateStockRequest) {
  return requestClient.post(`/cmdb/inventory/${id}/allocate`, data);
}

/**
 * 释放已分配库存
 * @param id 库存明细ID
 * @param data 释放信息
 */
export async function releaseStockApi(id: number, data: ReleaseStockRequest) {
  return requestClient.post(`/cmdb/inventory/${id}/release`, data);
}

/**
 * 获取产品库存汇总
 * @param productId 产品ID
 */
export async function getInventorySummaryApi(productId: number) {
  return requestClient.get<InventorySummary>(
    `/cmdb/inventory/summary/${productId}`,
  );
}

/**
 * 获取低库存产品列表
 * @param threshold 库存阈值
 */
export async function getLowStockProductsApi(threshold?: number) {
  return requestClient.get<LowStockProduct[]>('/cmdb/inventory/low-stock', {
    params: { threshold },
  });
}

/**
 * 获取即将过保的库存项目
 * @param days 天数
 */
export async function getExpiringWarrantyItemsApi(days?: number) {
  return requestClient.get<ExpiringWarrantyItem[]>(
    '/cmdb/inventory/expiring-warranty',
    {
      params: { days },
    },
  );
}

/**
 * 根据仓库查询库存
 * @param warehouseId 仓库ID
 * @param page 页码
 * @param pageSize 每页数量
 */
export async function getInventoryByWarehouseApi(
  warehouseId: number,
  page: number = 1,
  pageSize: number = 10,
) {
  return requestClient.get<GetInventoryResponse>(
    `/cmdb/inventory/warehouse/${warehouseId}`,
    {
      params: { page, pageSize },
    },
  );
}

/**
 * 获取库存变更历史
 * @param detailId 库存明细ID
 * @param startTime 开始时间
 * @param endTime 结束时间
 */
export async function getStockHistoryApi(
  detailId: number,
  startTime?: string,
  endTime?: string,
) {
  return requestClient.get<StockHistory[]>(
    `/cmdb/inventory/history/${detailId}`,
    {
      params: { startTime, endTime },
    },
  );
}

/**
 * 根据产品ID和仓库ID查询库存
 * @param productId 产品ID
 * @param warehouseId 仓库ID
 */
export async function getInventoryByProductAndWarehouseApi(
  productId: number,
  warehouseId: number,
) {
  return requestClient.get<InventoryDetail>(
    `/cmdb/inventory/product/${productId}/warehouse/${warehouseId}`,
  );
}

/**
 * 根据产品ID和仓库ID获取库存变更历史
 * @param productId 产品ID
 * @param warehouseId 仓库ID
 * @param startTime 开始时间
 * @param endTime 结束时间
 */
export async function getStockHistoryByProductAndWarehouseApi(
  productId: number,
  warehouseId: number,
  startTime?: string,
  endTime?: string,
) {
  return requestClient.get<StockHistory[]>(
    `/cmdb/inventory/history/product/${productId}/warehouse/${warehouseId}`,
    {
      params: { startTime, endTime },
    },
  );
}
