import { requestClient } from '#/api/request';

/**
 * 产品接口
 */
export interface Product {
  id: number;
  created_at: string;
  updated_at: string;
  material_type: string; // 物料类型
  brand: string; // 品牌
  model: string; // 型号
  spec: string; // 规格
  unit: string; // 单位
  pn: string; // 原厂PN
  product_category: string; // 产品类别
  reference_price: number; // 参考单价
  warranty_period: number; // 质保期(月)
  supplier_ids: number[]; // 建议供应商ID列表
  status: number; // 状态(1:启用,0:禁用)
  description: string; // 产品描述
  created_by: number; // 创建人ID
  updated_by: number; // 更新人ID
}

/**
 * 查询产品列表参数
 */
export interface GetProductListParams {
  page: number;
  pageSize: number;
  pn?: string;
  material_type?: string;
  brand?: string;
  model?: string;
  spec?: string;
  product_category?: string;
  status?: number; // 状态(1:启用,0:禁用)
}

/**
 * 查询产品列表响应
 */
export interface GetProductListResponse {
  list: Product[];
  total: number;
}

/**
 * 获取产品列表
 * @param params 请求参数
 */
export function getProductListApi(params: Record<string, any>) {
  return requestClient.get('/cmdb/products', { params });
}

/**
 * 获取产品详情
 * @param id 产品ID
 */
export function getProductDetailApi(id: number) {
  return requestClient.get(`/cmdb/products/${id}`);
}

/**
 * 创建产品
 * @param data 产品数据
 */
export function createProductApi(data: Record<string, any>) {
  return requestClient.post('/cmdb/products', data);
}

/**
 * 更新产品
 * @param id 产品ID
 * @param data 产品数据
 */
export function updateProductApi(id: number, data: Record<string, any>) {
  return requestClient.put(`/cmdb/products/${id}`, data);
}

/**
 * 删除产品
 * @param id 产品ID
 */
export function deleteProductApi(id: number) {
  return requestClient.delete(`/cmdb/products/${id}`);
}

/**
 * 获取所有物料类型
 */
export function getMaterialTypesApi() {
  return requestClient.get<string[]>('/cmdb/dict/material-types');
}

/**
 * 获取所有产品类别
 */
export function getProductCategoriesApi() {
  return requestClient.get<string[]>('/cmdb/dict/product-categories');
}

/**
 * 获取所有品牌
 */
export function getBrandsApi() {
  return requestClient.get<string[]>('/cmdb/dict/brands');
}

/**
 * 获取所有规格
 */
export function getSpecsApi() {
  return requestClient.get<string[]>('/cmdb/dict/specs');
}

/**
 * 根据物料类型获取规格列表
 * @param params 请求参数
 */
export function getSpecsByMaterialTypeApi(params: { material_type: string }) {
  return requestClient.get<string[]>('/cmdb/dict/specs-by-material-type', { params });
}
