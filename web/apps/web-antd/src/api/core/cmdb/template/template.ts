// web/apps/web-antd/src/api/core/template.ts
import { requestClient } from '#/api/request';
/**
 * 模板组件接口
 */
export interface TemplateComponent {
  id?: number;
  created_at?: string;
  updated_at?: string;
  template_id?: number;
  product_id: number;
  product?: {
    brand: string; // 品牌
    created_at: string;
    id: number;
    material_type: string; // 物料类型
    model: string; // 型号
    pn: string; // 原厂PN
    product_category: string; // 产品类别
    spec: string; // 规格
    unit: string; // 单位
    updated_at: string;
  };
  quantity: number;
  slot?: string;
}
/**
 * 套餐模板接口
 */
export interface MachineTemplate {
  id?: number;
  created_at?: string;
  updated_at?: string;
  templateName: string;
  cpuModel?: string;
  memoryCapacity?: number;
  gpuModel?: string;
  diskType?: string;
  componentList?: string; // JSON字符串或数组
  templateCategory?: string;
  components?: TemplateComponent[];
}

/**
 * 查询套餐模板列表参数
 */
export interface GetTemplateListParams {
  page: number;
  pageSize: number;
  query?: string;
  category?: string;
}

/**
 * 查询套餐模板列表响应
 */
export interface GetTemplateListResponse {
  list: MachineTemplate[];
  total: number;
}

/**
 * 获取套餐模板列表
 */
export async function getTemplateListApi(params: GetTemplateListParams) {
  return requestClient.get<GetTemplateListResponse>('/cmdb/machine-templates', {
    params,
  });
}

/**
 * 获取套餐模板详情
 */
export async function getTemplateDetailApi(id: number) {
  return requestClient.get<MachineTemplate>(`/cmdb/machine-templates/${id}`);
}

/**
 * 创建套餐模板
 */
export async function createTemplateApi(data: MachineTemplate) {
  return requestClient.post('/cmdb/machine-templates', data);
}

/**
 * 更新套餐模板
 */
export async function updateTemplateApi(id: number, data: MachineTemplate) {
  return requestClient.put(`/cmdb/machine-templates/${id}`, data);
}

/**
 * 删除套餐模板
 */
export async function deleteTemplateApi(id: number) {
  return requestClient.delete(`/cmdb/machine-templates/${id}`);
}

/**
 * 获取模板组件列表
 */
export async function getTemplateComponentsApi(templateId: number) {
  return requestClient.get<TemplateComponent[]>(
    `/cmdb/machine-templates/${templateId}/components`,
  );
}

/**
 * 创建模板组件
 */
export async function createTemplateComponentApi(
  templateId: number,
  data: TemplateComponent,
) {
  return requestClient.post(
    `/cmdb/machine-templates/${templateId}/components`,
    data,
  );
}

/**
 * 更新模板组件
 */
export async function updateTemplateComponentApi(
  id: number,
  data: TemplateComponent,
) {
  return requestClient.put(`/cmdb/template-components/${id}`, data);
}

/**
 * 删除模板组件
 */
export async function deleteTemplateComponentApi(id: number) {
  return requestClient.delete(`/cmdb/template-components/${id}`);
}
