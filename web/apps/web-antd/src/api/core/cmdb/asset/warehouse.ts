import { requestClient } from '#/api/request';

// 仓库模型接口
export interface Warehouse {
  id: number;
  created_at?: string;
  updated_at?: string;
  name: string;
  code: string;
  type: string;
  room_id?: number;
  room?: {
    building?: string;
    code: string;
    dataCenter?: {
      id: number;
      name: string;
    };
    floor?: string;
    id: number;
    name: string;
  };
  description?: string;
  status: string;
}

// 带统计信息的仓库
export interface WarehouseWithStats extends Warehouse {
  product_count: number;
  total_items: number;
  available_items: number;
}

// 仓库列表查询参数
export interface WarehouseListParams {
  page?: number;
  pageSize?: number;
  query?: string;
  type?: string;
}

// 仓库列表响应
export interface WarehouseListResponse {
  list: Warehouse[];
  total: number;
}

// 仓库内备件查询参数
export interface WarehouseSpareParams {
  warehouseId: number;
  page?: number;
  pageSize?: number;
}

// 仓库内库存查询参数
export interface WarehouseInventoryParams {
  warehouseId: number;
  page?: number;
  pageSize?: number;
}

// 获取仓库列表
export function getWarehouseListApi(params: WarehouseListParams) {
  return requestClient.get<WarehouseListResponse>('/cmdb/warehouses', {
    params,
  });
}

// 创建仓库
export function createWarehouseApi(
  data: Omit<Warehouse, 'created_at' | 'id' | 'updated_at'>,
) {
  return requestClient.post<{ id: number }>('/cmdb/warehouses', data);
}

// 更新仓库
export function updateWarehouseApi(id: number, data: Partial<Warehouse>) {
  return requestClient.put(`/cmdb/warehouses/${id}`, data);
}

// 删除仓库
export function deleteWarehouseApi(id: number) {
  return requestClient.delete(`/cmdb/warehouses/${id}`);
}

// 获取仓库详情
export function getWarehouseDetailApi(id: number) {
  return requestClient.get<Warehouse>(`/cmdb/warehouses/${id}`);
}

// 根据编码获取仓库
export function getWarehouseByCodeApi(code: string) {
  return requestClient.get<Warehouse>(`/cmdb/warehouses/code/${code}`);
}

// 获取仓库及统计信息
export function getWarehouseStatsApi(id: number) {
  return requestClient.get<WarehouseWithStats>(`/cmdb/warehouses/${id}/stats`);
}

// 获取仓库内所有备件
export function getWarehouseSparesApi(params: WarehouseSpareParams) {
  const { warehouseId, ...restParams } = params;
  return requestClient.get<any>(`/cmdb/warehouses/${warehouseId}/spares`, {
    params: restParams,
  });
}

// 获取仓库内库存明细
export function getWarehouseInventoryApi(params: WarehouseInventoryParams) {
  const { warehouseId, ...restParams } = params;
  return requestClient.get<any>(`/cmdb/warehouses/${warehouseId}/inventory`, {
    params: restParams,
  });
}

// 获取所有仓库类型
export function getWarehouseTypesApi() {
  return requestClient.get<string[]>('/cmdb/warehouses/types');
}
