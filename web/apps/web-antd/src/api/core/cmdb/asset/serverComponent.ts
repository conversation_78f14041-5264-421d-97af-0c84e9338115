import { requestClient } from '#/api/request';

// 服务器组件类型定义
export interface ServerComponent {
  id?: number;
  created_at?: string;
  updated_at?: string;
  server_id: number;
  server?: any;
  product_id?: number;
  product?: any;
  component_type: string;
  sn?: string;
  model?: string;
  pn?: string;
  firmware_version?: string;
  slot_position?: string;
  install_date?: string;
  status?: string;
  description?: string;
  extra_info?: any;
}

// 服务器信息
export interface ServerInfo {
  sn?: string;
  brand?: string;
  model?: string;
}

// 库存详情
export interface InventoryDetail {
  total_stock: number;
  in_use_count: number;
  idle_count: number;
  good_count: number;
  defect_count: number;
}

// 服务器组件详情（带关联信息）
export interface ServerComponentWithDetails extends ServerComponent {
  server_info?: ServerInfo;
  inventory_detail?: InventoryDetail;
}
// 库存统计信息
export interface InventoryStatistics {
  total_stock: number;
  in_use_count: number;
  idle_count: number;
  good_count: number;
  defect_count: number;
}

// 组件统计信息
export interface ComponentStatistics {
  total_components: number;
  by_type: Record<string, number>;
  by_status: Record<string, number>;
  by_server: Record<string, number>;
  inventory?: InventoryStatistics;
}

// 组件变更信息
export interface ComponentChangeInfo {
  reason: string; // 变更原因
  operatorId?: number; // 操作人ID，可选，后端会从JWT获取
  remarks: string; // 备注
}

// 组件安装信息
export interface ComponentInstallInfo {
  slotPosition: string; // 安装位置
  componentType: string; // 组件类型
  reason: string; // 安装原因
  operatorId?: number; // 操作人ID，可选，后端会从JWT获取
  remarks: string; // 备注
}

// 组件移除信息
export interface ComponentRemoveInfo {
  reason: string; // 移除原因
  operatorId?: number; // 操作人ID，可选，后端会从JWT获取
  remarks: string; // 备注
}

// 组件变更日志
export interface ComponentChangeLog {
  id: number;
  created_at: string;
  updated_at: string;
  server_id: number;
  component_id: number;
  previous_component_id: number;
  spare_id: number;
  spare?: any;
  change_type: string;
  reason: string;
  operator_id: number;
  operator_name: string;
  change_time: string;
  details?: any;
  remark: string;
}

// 资产状态变更日志
export interface AssetStatusChangeLog {
  id: number;
  created_at: string;
  updated_at: string;
  asset_id: number;
  old_asset_status: string;
  new_asset_status: string;
  old_biz_status: string;
  new_biz_status: string;
  change_reason: string;
  operator_id: number;
  operator_name: string;
  ticket_no: string;
  workflow_id: string;
  old_hardware_status: string;
  new_hardware_status: string;
  source: string;

  // 附加数据
  total_down_time: string; // 总停机时间
}

/** 获取服务器组件列表接口参数 */
export interface GetServerComponentsParams {
  page?: number;
  pageSize?: number;
  query?: string;
  componentType?: string;
}

/** 获取服务器组件列表响应 */
export interface GetServerComponentsResponse {
  list: ServerComponent[];
  total: number;
}

/** 获取带详情的组件列表响应 */
export interface GetComponentsWithDetailsResponse {
  list: ServerComponentWithDetails[];
  total: number;
}

/**
 * 获取服务器组件列表
 * @param params 查询参数
 */
export async function getServerComponentsApi(
  params: GetServerComponentsParams,
) {
  return requestClient.get<GetServerComponentsResponse>(
    '/cmdb/server-components',
    {
      params,
    },
  );
}

/**
 * 获取服务器组件详情
 * @param id 组件ID
 */
export async function getServerComponentByIdApi(id: number) {
  return requestClient.get<ServerComponent>(`/cmdb/server-components/${id}`);
}

/**
 * 根据SN获取服务器组件
 * @param sn 组件序列号
 */
export async function getServerComponentBySnApi(sn: string) {
  return requestClient.get<ServerComponent>('/cmdb/server-components/by-sn', {
    params: { sn },
  });
}

/**
 * 根据服务器ID获取组件列表
 * @param serverId 服务器ID
 */
export async function getServerComponentsByServerIdApi(serverId: number) {
  return requestClient.get<ServerComponent[]>(
    '/cmdb/server-components/by-server',
    {
      params: { serverID: serverId },
    },
  );
}

/**
 * 创建服务器组件
 * @param data 组件数据
 */
export async function addServerComponentApi(
  data: Omit<ServerComponent, 'created_at' | 'id' | 'updated_at'>,
) {
  return requestClient.post<{ id: number }>('/cmdb/server-components', data);
}

/**
 * 更新服务器组件
 * @param data 组件数据
 */
export async function updateServerComponentApi(data: ServerComponent) {
  return requestClient.put(`/cmdb/server-components/${data.id}`, data);
}

/**
 * 删除服务器组件
 * @param id 组件ID
 */
export async function deleteServerComponentApi(id: number) {
  return requestClient.delete(`/cmdb/server-components/${id}`);
}

/**
 * 获取组件详情（包含关联信息）
 * @param id 组件ID
 */
export async function getComponentWithDetailsApi(id: number) {
  return requestClient.get<ServerComponentWithDetails>(
    `/cmdb/server-components/${id}/details`,
  );
}

/**
 * 获取组件列表（包含关联信息）
 * @param params 查询参数
 */
export async function getComponentsWithDetailsApi(
  params: GetServerComponentsParams,
) {
  return requestClient.get<GetComponentsWithDetailsResponse>(
    '/cmdb/server-components/details',
    {
      params,
    },
  );
}

/**
 * 获取组件统计信息
 * @param componentType 可选的组件类型过滤
 */
export async function getComponentStatisticsApi(componentType?: string) {
  return requestClient.get<ComponentStatistics>(
    '/cmdb/server-components/statistics',
    {
      params: { component_type: componentType },
    },
  );
}

/**
 * 获取指定产品的库存统计
 * @param productId 产品ID
 */
export async function getInventoryStatisticsApi(productId: number) {
  return requestClient.get<InventoryStatistics>(
    `/cmdb/server-components/inventory/${productId}`,
  );
}

/**
 * 获取组件相关的备件
 * @param componentId 组件ID
 * @param page 页码
 * @param pageSize 每页数量
 */
export async function getComponentSparesApi(
  componentId: number,
  page: number = 1,
  pageSize: number = 10,
) {
  return requestClient.get<{ list: any[]; total: number }>(
    `/cmdb/server-components/${componentId}/spares`,
    {
      params: { page, pageSize },
    },
  );
}

/**
 * 根据组件类型获取可用备件
 * @param componentType 组件类型
 * @param page 页码
 * @param pageSize 每页数量
 */
export async function getSparesByComponentTypeApi(
  componentType: string,
  page: number = 1,
  pageSize: number = 10,
) {
  return requestClient.get<{ list: any[]; total: number }>(
    '/cmdb/server-components/type-spares',
    {
      params: { component_type: componentType, page, pageSize },
    },
  );
}

// 查询组件变更历史
export async function getComponentChangeHistoryApi(componentId: number) {
  return requestClient.get<ComponentChangeLog[]>(
    `/cmdb/components/${componentId}/changes`,
  );
}

// 查询服务器组件变更历史
export async function getServerComponentChangeHistoryApi(
  serverId: number,
  page: number = 1,
  pageSize: number = 10,
) {
  return requestClient.get<{
    list: ComponentChangeLog[];
    page: number;
    pageSize: number;
    total: number;
    totalPage: number;
  }>(`/cmdb/components/server/${serverId}/changes`, {
    params: {
      page,
      pageSize,
    },
  });
}

export interface listLogParams {
  assetId: number;
  page: number;
  pageSize: number;
  startTime?: string;
  endTime?: string;
  operatorID?: number;
}
// 查询组件状态变更历史
export async function getAssetStatusChangeHistoryApi(params: listLogParams) {
  return requestClient.get<{
    list: AssetStatusChangeLog[];
    total: number;
  }>(`/cmdb/asset-status/${params.assetId}/history`, {
    params: {
      page: params.page,
      pageSize: params.pageSize,
      startTime: params.startTime,
      endTime: params.endTime,
      operatorID: params.operatorID,
    },
  });
}

// 替换组件
export async function replaceComponentApi(
  componentId: number,
  spareId: number,
  changeInfo: ComponentChangeInfo,
) {
  return requestClient.post(`/cmdb/components/${componentId}/replace`, {
    spareId,
    changeInfo,
  });
}

// 安装组件
export async function installComponentApi(
  serverId: number,
  productId: number,
  spareId: number,
  installInfo: ComponentInstallInfo,
) {
  return requestClient.post('/cmdb/components/install', {
    serverId,
    productId,
    spareId,
    installInfo,
  });
}

// 移除组件
export async function removeComponentApi(
  componentId: number,
  removeInfo: ComponentRemoveInfo,
) {
  return requestClient.post(`/cmdb/components/${componentId}/remove`, {
    removeInfo,
  });
}
