import { requestClient } from '#/api/request';

/** 获取网络设备列表参数 */
export interface GetNetworkDeviceListParams {
  page: number;
  pageSize: number;
  query?: string; // 搜索关键词
  role?: string; // 网络设备角色
}

/** 设备基本信息接口 */
export interface Device {
  id: number;
  sn: string;
  brand: string;
  model: string;
  assetType: string;
  assetStatus: string;
  hardwareStatus: string;
  templateID?: number; // 模板ID
  // 其他设备属性...
}

/** 网络设备基本信息接口 */
export interface NetworkDevice {
  id: number;
  deviceID: number;
  role: string; // 设备角色(S0/S1/GS0等)
  firmwareVersion: string; // 固件版本
  loopbackAddress: string; // Loopback地址
  managementAddress: string; // 管理地址
  ports?: number; // 端口数量
  portSpeed?: string; // 端口速率
  stackSupport?: boolean; // 是否支持堆叠
  stackID?: number; // 堆叠ID
  stackRole?: string; // 在堆叠中的角色
  layer?: number; // 网络层级(2/3)
  routingProtocols?: string; // 支持的路由协议
  device?: Device; // 关联的设备信息
  createdAt?: string;
  updatedAt?: string;
}

/** 带设备信息的网络设备详情 */
export interface NetworkDeviceWithDeviceInfo {
  id: number;
  role: string; // 设备角色
  firmwareVersion: string; // 固件版本
  loopbackAddress: string; // Loopback地址
  managementAddress: string; // 管理地址
  ports?: number; // 端口数量
  portSpeed?: string; // 端口速率
  stackSupport?: boolean; // 是否支持堆叠
  stackID?: number; // 堆叠ID
  stackRole?: string; // 在堆叠中的角色
  layer?: number; // 网络层级(2/3)
  routingProtocols?: string; // 支持的路由协议
  createdAt?: string; // 创建时间
  updatedAt?: string; // 更新时间

  // 基本设备信息
  deviceID: number;
  sn: string;
  brand: string;
  model: string;
  assetType: string;
  assetStatus: string;
  hardwareStatus: string;
  templateName: string; // 套餐模板名称
  remark?: string; // 备注信息

  // 资源信息
  resourceID?: number; // 资源ID
  project: string;
  cabinetID?: number; // 机柜ID
  cabinetName: string;
  rackPosition: number;
  unitHeight: number;
  bizStatus: string;
  resStatus?: string; // 资源状态
  rackingTime: string;
  deliveryTime: string;
  bmcIP?: string; // BMC IP地址
  vpcIP?: string; // VPC IP地址
  cluster?: string; // 集群
  isBackup?: boolean; // 是否备机
  resourceRemark?: string; // 资源备注

  // 位置信息
  roomID?: number; // 机房ID
  roomName?: string; // 机房名称
  dataCenterID?: number; // 数据中心ID
  dataCenterName?: string; // 数据中心名称
  dataCenterAddress?: string; // 数据中心地址
  azID?: number; // 可用区ID
  azName?: string; // 可用区名称
  regionID?: number; // 区域ID
  regionName?: string; // 区域名称

  // 机柜信息
  cabinetRow?: string; // 机柜行
  cabinetColumn?: string; // 机柜列
  cabinetType?: string; // 机柜类型
  networkEnvironment?: string; // 网络环境
  bondType?: string; // Bond类型
  totalPower?: number; // 总功率

  // 套餐模板信息
  templateID?: number; // 模板ID
  cpuModel?: string; // CPU型号
  memoryCapacity?: number; // 内存容量
  gpuModel?: string; // GPU型号
  diskType?: string; // 磁盘类型

  // 财务信息
  purchaseOrder: string;
  purchaseDate: string;
  warrantyExpire: string;
  price: number;
  residualValue: number;
}

// 一站式创建网络设备的请求接口
export interface CreateNetworkDeviceWithDeviceRequest {
  // 设备基本信息
  sn: string;
  brand?: string;
  model?: string;
  assetStatus: string;
  hardwareStatus?: string;
  purchaseOrder?: string;
  purchaseDate?: string;
  warrantyExpire?: string;
  price?: number;
  residualValue?: number;
  remark?: string;
  templateID?: number; // 套餐模板ID

  // 网络设备特有信息
  role: string;
  firmwareVersion?: string;
  loopbackAddress?: string;
  managementAddress?: string;
  ports?: number;
  portSpeed?: string;
  stackSupport?: boolean;
  stackID?: number;
  stackRole?: string;
  layer?: number;
  routingProtocols?: string;

  // 资源信息
  project?: string;
  cabinetName?: string;
  rackPosition?: number;
  unitHeight?: number;
  bizStatus?: string;
  rackingTime?: string;
  deliveryTime?: string;
}

/** 获取网络设备列表响应 */
export interface GetNetworkDeviceListResponse {
  list: NetworkDevice[];
  total: number;
}

/**
 * 获取网络设备列表
 * @param params 查询参数
 */
export async function getNetworkDeviceListApi(
  params: GetNetworkDeviceListParams,
) {
  return requestClient.get<GetNetworkDeviceListResponse>(
    '/cmdb/network-devices',
    {
      params,
    },
  );
}

/**
 * 获取网络设备详情
 * @param id 网络设备ID
 */
export async function getNetworkDeviceApi(id: number) {
  return requestClient.get<NetworkDevice>(`/cmdb/network-devices/${id}`);
}

/**
 * 获取网络设备详细信息(包含设备和资源信息)
 * @param id 网络设备ID
 */
export async function getNetworkDeviceDetailApi(id: number) {
  return requestClient.get<NetworkDeviceWithDeviceInfo>(
    `/cmdb/network-devices/detail/${id}`,
  );
}

/**
 * 根据设备ID获取网络设备
 * @param deviceId 设备ID
 */
export async function getNetworkDeviceByDeviceIdApi(deviceId: number) {
  return requestClient.get<NetworkDevice>(
    `/cmdb/network-devices/device/${deviceId}`,
  );
}

/**
 * 创建网络设备
 * @param data 网络设备数据
 */
export async function createNetworkDeviceApi(
  data: Omit<NetworkDevice, 'createdAt' | 'id' | 'updatedAt'>,
) {
  return requestClient.post<NetworkDevice>('/cmdb/network-devices', data);
}

/**
 * 更新网络设备
 * @param id 网络设备ID
 * @param data 网络设备数据
 */
export async function updateNetworkDeviceApi(
  id: number,
  data: Omit<NetworkDevice, 'createdAt' | 'updatedAt'>,
) {
  return requestClient.put<NetworkDevice>(`/cmdb/network-devices/${id}`, data);
}

/**
 * 删除网络设备
 * @param id 网络设备ID
 */
export async function deleteNetworkDeviceApi(id: number) {
  return requestClient.delete(`/cmdb/network-devices/${id}`);
}

// 一站式创建网络设备和基本设备
export async function createNetworkDeviceWithDeviceApi(
  data: CreateNetworkDeviceWithDeviceRequest,
) {
  return requestClient.post('/cmdb/network-devices/with-device', data);
}

/**
 * 一站式更新网络设备(包含设备基本信息)
 * @param id 网络设备ID
 * @param data 网络设备和设备数据
 */
export async function updateNetworkDeviceWithDeviceApi(id: number, data: any) {
  return requestClient.put(`/cmdb/network-devices/with-device/${id}`, data);
}
