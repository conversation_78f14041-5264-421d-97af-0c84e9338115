// 备件资产模型
export interface AssetSpare {
  id?: number;
  created_at?: string;
  updated_at?: string;
  sn?: string;
  type: string;
  model?: string;
  pn?: string;
  product_id?: number;
  product?: any;
  source_type?: string;
  related_asset_id?: number;
  related_asset_sn?: string;
  purchase_date?: string;
  warranty_expire?: string;
  asset_status: string;
  hardware_status: string;
  firmware_version?: string;
  price?: number;
  warehouse_id?: number;
  warehouse?: any;
  location?: string;
  remark?: string;
}

// 带详情的备件模型
export interface AssetSpareWithDetails extends AssetSpare {
  product_info?: {
    brand: string;
    model: string;
    name: string;
    product_category: string;
    spec: string;
  };
  warehouse_info?: {
    code: string;
    location: string;
    name: string;
    type: string;
  };
  inventory_detail?: {
    allocated_stock: number;
    current_stock: number;
    warehouse_id?: number;
  };
}

// 备件统计信息
export interface SpareStatistics {
  total_spares: number;
  by_type: Record<string, number>;
  by_status: Record<string, number>;
  by_warehouse: Record<string, number>;
}
