import { requestClient } from '#/api/request';

/** 获取设备表格数据接口参数 */
export interface GetDeviceTableApiParams {
  page: number;
  pageSize: number;
  query?: string; // 搜索关键词
  assetStatus?: string; // 资产状态
  assetType?: string; // 资产类型
  brand?: string; // 品牌
  [key: string]: any;
}

// 定义设备接口
export interface Device {
  id: number;
  purchaseOrder: string; // 采购合同
  sn: string; // 资产SN
  brand: string; // 厂商
  model: string; // 型号
  purchaseDate: string; // 购买时间
  warrantyExpire: string; // 过保时间
  assetStatus: string; // 资产状态
  hardwareStatus: string; // 硬件状态
  price: number; // 金额
  residualValue: number; // 残值
  assetType: string; // 资产类型
  remark?: string; // 备注
  lastStatusChange: string; // 最后状态变更时间
  created_at?: string;
  updated_at?: string;
  // 套餐模板相关字段
  templateID?: number; // 套餐模板ID
  template?: {
    [key: string]: any;
    id: number;
    templateName: string;
  };
  // 关联资源信息
  resource?: {
    bizStatus: string;
    bmcIP: string;
    cabinet?: {
      capacityUnits: number;
      id: number;
      name: string;
      room?: {
        dataCenter?: {
          id: number;
          name: string;
        };
        id: number;
        name: string;
      };
    };
    cabinetID?: number;
    cluster?: string; // 集群
    deliveryTime?: string; // 交付时间
    hostname?: string;
    id: number;
    isBackup?: boolean; // 是否备机
    project?: string;
    rackingTime?: string; // 上架时间
    rackPosition?: number;
    sn?: string; // 资源SN
    tenantIP?: string;
    vpcIP?: string;
  };
}

// 资源接口定义
export interface Resource {
  id: number;
  assetID?: number;
  sn?: string;
  hostname?: string;
  bmcIP?: string;
  vpcIP?: string;
  tenantIP?: string;
  rackPosition?: number;
  cabinetID?: number;
  bizStatus?: string;
  project?: string;
  cluster?: string;
  isBackup?: boolean;
  rackingTime?: string;
  deliveryTime?: string;
  created_at?: string;
  updated_at?: string;
  cabinet?: {
    capacityUnits: number;
    id: number;
    name: string;
    room?: {
      dataCenter?: {
        id: number;
        name: string;
      };
      id: number;
      name: string;
    };
  };
}

// 服务器组件类型定义
export interface ServerComponent {
  id?: number;
  created_at?: string;
  updated_at?: string;
  server_id: number;
  server?: any;
  product_id?: number;
  product?: any;
  component_type: string;
  sn?: string;
  model?: string;
  pn?: string;
  firmware_version?: string;
  slot_position?: string;
  install_date?: string;
  status?: string;
  description?: string;
  extra_info?: any;
}

/**
 * 获取设备资源列表
 * @param params 查询参数
 */
export interface GetDeviceResourceTableApiParams {
  page: number;
  pageSize: number;
  query?: string; // 搜索关键词
  assetStatus?: string; // 资产状态
  assetType?: string; // 资产类型
  bizStatus?: string; // 业务状态
  project?: string; // 项目
  ip?: string; // IP地址
  templateID?: number; // 套餐模板ID
}

export interface GetDeviceResourceTableApiResponse {
  list: Device[];
  total: number;
}

export async function getDeviceResourceTableApi(
  params: GetDeviceResourceTableApiParams,
) {
  return requestClient.get<GetDeviceResourceTableApiResponse>(
    '/cmdb/device-resources',
    {
      params,
    },
  );
}

/**
 * 获取设备资源详情
 * @param id 设备资源ID
 */
export async function getDeviceResourceDetailApi(id: number) {
  return requestClient.get<Device>(`/cmdb/device-resources/${id}`);
}

/**
 * 新增设备资源
 * @param data 设备资源数据
 */
export async function addDeviceResourceApi(
  data: Omit<Device, 'created_at' | 'id' | 'updated_at'>,
) {
  return requestClient.post<Device>('/cmdb/device-resources', data);
}

/**
 * 编辑设备资源
 * @param data 设备资源数据
 */
export async function editDeviceResourceApi(data: Device) {
  return requestClient.put<Device>(`/cmdb/device-resources/${data.id}`, data);
}

/**
 * 删除设备资源
 * @param id 设备资源ID
 */
export async function deleteDeviceResourceApi(id: number) {
  return requestClient.delete(`/cmdb/device-resources/${id}`);
}

/**
 * 获取设备的组件列表
 * @param deviceId 设备ID
 */
export async function getDevicePartsApi(deviceId: number) {
  return requestClient.get<ServerComponent[]>(
    `/cmdb/server-components/by-server`,
    {
      params: { serverID: deviceId },
    },
  );
}

/**
 * 获取设备的套餐模板信息
 * @param deviceId 设备ID
 */
export async function getDevicePlansApi(deviceId: number) {
  if (!deviceId) {
    console.warn('获取套餐模板时未提供有效设备ID');
    return [];
  }

  try {
    return await requestClient.get<any>(
      `/cmdb/devices/${deviceId}/machine-template`,
      {
        params: {
          with_components: true,
        },
      },
    );
  } catch (error) {
    // 静默处理错误
    console.warn('获取套餐模板信息失败，但不影响正常功能:', error);
    return [];
  }
}

/**
 * 根据SN获取设备信息
 * @param sn 设备序列号
 */
export async function getDeviceBySNApi(sn: string) {
  return requestClient.get<Resource>('/cmdb/resources/by-sn', {
    params: { sn },
  });
}

/**
 * 获取所有项目列表
 * @returns 项目名称列表
 */
export async function getProjectsApi() {
  return requestClient.get<string[]>('/cmdb/resources/projects');
}

/**
 * 获取所有集群列表
 * @returns 集群名称列表
 */
export async function getClustersApi() {
  return requestClient.get<string[]>('/cmdb/resources/clusters');
}

/**
 * 获取可用的备机列表
 * @param project 项目名称
 * @param cluster 集群名称
 * @returns 可用备机列表
 */
export async function getAvailableBackupsApi(
  project: string,
  cluster?: string,
) {
  return requestClient.get<Resource[]>('/cmdb/resources/available-backups', {
    params: {
      project,
      cluster,
      hardwareStatus: 'normal',
      bizStatus: 'maintaining',
    },
  });
}

/** 校验sn是否存在 */
export async function checkSnExistApi(sn: string) {
  return requestClient.get<boolean>('/cmdb/devices/isExist', {
    params: {
      sn,
    },
  });
}

/**
 * 根据多个SN获取设备信息
 * @param sn 多个设备序列号，用逗号分隔
 */
export async function getDevicesByMultiSNApi(sn: string) {
  return requestClient.get<Device[]>('/cmdb/devices/by-sn', {
    params: { sn },
  });
}

/**
 * 根据多个SN获取闲置中的设备信息
 * @param sn 多个设备序列号，用逗号分隔
 */
export async function getIdleDevicesByMultiSNApi(sn: string) {
  return requestClient.get<Device[]>('/cmdb/devices/idle/by-sn', {
    params: { sn },
  });
}

/**
 * 获取使用中状态的设备
 * @param params 查询参数
 */
export interface GetDevicesInUseApiParams {
  page: number;
  pageSize: number;
  query?: string; // 搜索关键词
  sn?: string; // 设备SN
  hostname?: string; // 主机名
  brand?: string; // 厂商
  model?: string; // 型号
  vpcIP?: string; // VPC IP
  bmcIP?: string; // BMC IP
}

export async function getDevicesInUseApi(params: GetDevicesInUseApiParams) {
  return requestClient.get<{ list: Device[]; total: number }>(
    '/cmdb/device-resources',
    {
      params: {
        ...params,
        // 固定为使用中状态的设备
        assetStatus: 'in_use',
      },
    },
  );
}

/**
 * 获取闲置状态的设备列表
 */
export async function getDevicesIdleApi(params: {
  brand?: string;
  model?: string;
  page: number;
  pageSize: number;
  query?: string;
}) {
  return requestClient.get<{
    list: Device[];
    total: number;
  }>('/cmdb/device-resources', {
    params: {
      ...params,
      // 固定为使用中状态的设备
      // assetStatus: 'idle',
      assetStatus: 'on_rack',
    },
  });
}

// }>('/cmdb/asset/devices/idle', {
//   params,
// });
