interface Product {
  product_id: number;
  amount: number;
  price: number;
}

export interface InboundOrderReq {
  project: string;
  // inbound_type: string;
  warehouse_id: string;
  warehouse_name: string;
  inbound_title: string;
  purchase_order_no: string;
  supplier_name: string;
  may_arrive_at: string;
  tracking_info: string;
  comment: string;
  new_info: Partial<Product>[];
}

interface InboundOrder {
  inbound_no: string;
  inbound_type: string;
  inbound_title: string;
  create_by: string;
  creater_id: number;
  created_at: string;
  updated_at: string;
  stage: number;
}

export interface InboundOrdersRes {
  total: number;
  list: InboundOrder[];
}

export interface InboundOrderRes {
  InboundType: string;
  inbound_info: {
    create_by: string;
    created_at: string;
    CreatedAt: string;
    details: any;
    id: number;
    inbound_no: string;
    may_arrive_at: string;
    new_details: Array<{
      Product: {
        brand: string;
        model: string;
        pn: string;
        product_category: string;
        spec: string;
      };
    }>;
    new_info: Array<{
      Product: {
        brand: string;
        model: string;
        pn: string;
        product_category: string;
        spec: string;
      };
    }>;
    purchase_order_no: string;
    repair_details: Array<{
      repair_type: string;
      replace_sn?: string;
      sn: string;
      warehouse_name: string;
    }>;
    supplier_name: string;
    tracking_info: string;
    UpdatedAt: string;
  };
  ticket_info: {
    History: Array<{
      comment: string;
      new_status: string;
      operation_time: string;
      operator_name: string;
      previous_status: string;
      stage: string;
    }>;
    ID: number;
    stage: string;
    status: string;
  };
}

/** 入库单详情请求参数 */
export interface InboundOrderDetailsReq {
  id: number;
  sn: string;
  warehouse_id: number;
  warehouse_name: string;
}

interface DismantledInbound {
  product_id: string;
  main_device_sn: string;
  component_sn: string;
  component_state: string;
  warehouse_id: number;
  warehouse_name: string;
  need_return: boolean;
}
/** 拆机入库请求参数 */
export interface DismantledInboundReq {
  project: string;
  inbound_title: string;
  dismantled_inbounds: {
    details: DismantledInbound[];
  };
}

export interface UploadDismantledInboundReq {
  project: string;
  inbound_title: string;
  file: File;
  need_return: boolean;
}

interface RepairedPart {
  sn: string;
  replace_sn: string;
  repair_type: string;
  warehouse_id: number;
  warehouse_name: string;
}
export interface RepairedPartReq {
  project: string;
  inbound_title: string;
  repair_inbound: {
    repair_details: RepairedPart[];
  };
}

export interface InboundInfo {
  id?: number;
  inbound_ticket_id?: number;
  inbound_no?: string;
  asset_type?: string;
  product_id?: number;
  template_id?: number;
  amount: number;
}

export interface InboundDetail {
  id: number;
  inbound_ticket_id?: number;
  inbound_no: string;
  asset_type?: string;
  product_id?: number;
  template_id?: number;
  asset_type?: string;
  device_sn?: string;
  component_sn?: string;
  component_state?: string;
  need_return?: boolean;
  return_repair_type?: string;
  pre_component_sn?: string;
}

export interface InboundReq {
  project: string;
  inbound_title: string;
  inbound_type: string;
  inbound_reason: string;
  info: InboundInfo[];
  warehouse_id: number;
  tracking_info?: string;
  may_arrive_at?: string;
  purchase_no?: string;
  need_return?: boolean;
  valid?: boolean;
}

export interface StartWorkflowReq {
  inbound_no: string;
  inbound_type: string;
  verify: boolean;
}

export interface UploadInboundTicketReq {
  file: File;
  project: string;
  inbound_title: string;
  purchase_order_no: string;
  supplier_name: string;
  may_arrive_at: string;
  tracking_info: string;
  // order_date: string;
  warehouse_name: string;
  warehouse_id: string;
}

export interface UploadRepairedPartReq {
  project: string;
  inbound_title: string;
  file: File;
}

export interface UploadOutboundTicketReq {
  model: string;
  file: File;
}
