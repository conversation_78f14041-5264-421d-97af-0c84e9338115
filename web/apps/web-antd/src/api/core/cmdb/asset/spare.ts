import { requestClient } from '#/api/request';

export interface AssetSpare {
  id: number;
  created_at: string;
  updated_at: string;
  sn: string;
  type: string;
  model: string;
  pn: string;
  brand: string;
  source_type: string;
  asset_status: string;
  hardware_status: string;
  price: number;
  location: string;
  remark: string;
}

export interface SpareListParams {
  page?: number;
  pageSize?: number;
  query?: string;
  type?: string;
  status?: string;
  sn?: string;
  asset_status?: string;
  hardware_status?: string;
  location?: string;
  pn?: string;
  warehouse_id?: string;
  spec?: string;
  source_type?: string;
}

export interface SpareListResponse {
  list: AssetSpare[];
  total: number;
}

// 获取备件列表
export function getSpareListApi(params: SpareListParams) {
  return requestClient.get<SpareListResponse>('/cmdb/spares', { params });
}

// 创建备件
export function createSpareApi(
  data: Omit<AssetSpare, 'created_at' | 'id' | 'updated_at'>,
) {
  return requestClient.post<{ id: number }>('/cmdb/spares', data);
}

// 更新备件
export function updateSpareApi(id: number, data: Partial<AssetSpare>) {
  return requestClient.put(`/cmdb/spares/${id}`, data);
}

// 删除备件
export function deleteSpareApi(id: number) {
  return requestClient.delete(`/cmdb/spares/${id}`);
}

// 获取备件详情
export function getSpareDetailApi(id: number) {
  return requestClient.get<AssetSpare>(`/cmdb/spares/${id}`);
}

// 更改备件状态
export function changeSpareStatusApi(
  id: number,
  status: string,
  reason: string,
) {
  return requestClient.post(`/cmdb/spares/${id}/status`, { status, reason });
}

// 分配备件到设备
export function assignSpareToDeviceApi(
  id: number,
  deviceId: number,
  position: string,
) {
  return requestClient.post(`/cmdb/spares/${id}/assign`, {
    device_id: deviceId,
    position,
  });
}

// 从设备移除备件
export function removeSpareFromDeviceApi(id: number) {
  return requestClient.post(`/cmdb/spares/${id}/remove`);
}

// 转移备件到其他仓库
export function transferSpareWarehouseApi(
  id: number,
  warehouseId: number,
  location: string,
) {
  return requestClient.post(`/cmdb/spares/${id}/transfer`, {
    warehouse_id: warehouseId,
    location,
  });
}

// 获取备件统计信息
export function getSpareStatisticsApi(type?: string) {
  return requestClient.get('/cmdb/spares/statistics', { params: { type } });
}

// 根据组件类型获取可用备件列表
export function getSparesByComponentTypeApi(
  componentType: string,
  page?: number,
  pageSize?: number,
) {
  return requestClient.get<SpareListResponse>(
    '/cmdb/spares/by-component-type',
    {
      params: { component_type: componentType, page, pageSize },
    },
  );
}

// 根据产品ID获取备件列表
export function getSparesByProductApi(
  productId: number,
  page?: number,
  pageSize?: number,
) {
  return requestClient.get<SpareListResponse>(
    `/cmdb/spares/product/${productId}/details`,
    {
      params: { page, pageSize },
    },
  );
}

/** 获取备件信息详情 */
export function getSpareDetailsApi(params: any) {
  return requestClient.post(`/cmdb/spares/sn`, params);
}
