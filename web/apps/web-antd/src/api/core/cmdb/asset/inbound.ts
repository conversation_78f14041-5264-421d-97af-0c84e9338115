import type {
  DismantledInboundReq,
  InboundOrderDetailsReq,
  InboundOrderReq,
  InboundOrderRes,
  InboundOrdersRes,
  RepairedPartReq,
  UploadDismantledInboundReq,
  UploadInboundTicketReq,
  UploadRepairedPartReq,
} from './types';

import { requestClient } from '#/api/request';

import { commonUploadFile } from '../../upload';

/** 创建新购入库单 */
export function createInboundOrderApi(data: InboundOrderReq) {
  return requestClient.post('/cmdb/inbound/new-input', data);
}

/** 入库单列表 */
export function listInboundTicketApi(params: any) {
  return requestClient.get<InboundOrdersRes>('/cmdb/inbound', { params });
}

/**  获取入库单详情 */
export function getInboundTicketApi(id: string) {
  return requestClient.get<InboundOrderRes>(`/cmdb/inbound/${id}`);
}

/** 更新入库详情 */
export function updateInboundTicketApi(data: InboundOrderDetailsReq[]) {
  return requestClient.put(`/cmdb/inbound/new/update-details-input`, data);
}

/** 创建拆机配件入库单 */
export function createDismantledApi(data: DismantledInboundReq) {
  // return requestClient.post(`/cmdb/inbound/dismantled-input`, data);
  return requestClient.post(`/cmdb/inbound/trans/dismantled-input`, data);
}

/** 创建返修入库 */
export function createRepairApi(data: RepairedPartReq) {
  return requestClient.post(`/cmdb/inbound/repair-input`, data);
}

/** 上传入库单 */
export function uploadInboundTicketApi(data: UploadInboundTicketReq) {
  const formData = new FormData();
  Object.entries(data).forEach(([key, value]) => {
    formData.append(key, value);
  });

  return commonUploadFile('/cmdb/inbound/new-import', formData);
}

/** 上传拆机 */
export function uploadDismantledInboundApi(data: UploadDismantledInboundReq) {
  const formData = new FormData();
  Object.entries(data).forEach(([key, value]) => {
    formData.append(key, value);
  });

  return commonUploadFile('/cmdb/inbound/trans/dismantled-import', formData);
}

/** 上传维修sn */
export function uploadRepairedPartApi(data: UploadRepairedPartReq) {
  const formData = new FormData();
  Object.entries(data).forEach(([key, value]) => {
    formData.append(key, value);
  });

  return commonUploadFile('/cmdb/inbound/repair-import', formData);
}

/** 上传审核的SN */
export function uploadApprovalInboundApi(file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return commonUploadFile('/cmdb/inbound/new/update-details-import', formData);
}

export const templateMap = {
  '1': '整机',
  '2': '配件',
};

type ProjectWarehouseRes = Record<string, number>;

/** 获取项目对应仓库 */
export function getProjectWarehouseApi(project: string) {
  return requestClient.get<ProjectWarehouseRes>(
    `/cmdb/regions/warehouse/${project}`,
  );
}
