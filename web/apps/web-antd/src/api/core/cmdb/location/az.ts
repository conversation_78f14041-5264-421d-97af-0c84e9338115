import { requestClient } from '#/api/request';

/** 获取可用区表格数据接口参数 */
export interface GetAZTableApiParams {
  page: number;
  pageSize: number;
  query?: string;
  regionId?: number;
  [key: string]: any;
}

// 定义可用区记录接口
export interface AZ {
  id: number;
  name: string;
  regionId: number;
  region?: {
    id: number;
    name: string;
    status: string;
  };
  description: string;
  status: string;
  created_at?: string;
  updated_at?: string;
}

// 定义 API 返回的整体数据格式
export interface GetAZTableApiResponse {
  list: AZ[];
  total: number;
}

/**
 * 获取可用区表格数据
 */
export async function getAZTableApi(params: GetAZTableApiParams) {
  return requestClient.get<GetAZTableApiResponse>('/cmdb/azs', {
    params,
  });
}

/**
 * 获取可用区详情
 */
export async function getAZDetailApi(id: number) {
  return requestClient.get<{ data: AZ }>(`/cmdb/azs/${id}`);
}

/**
 * 新增可用区
 */
export async function addAZApi(
  data: Omit<AZ, 'created_at' | 'id' | 'updated_at'>,
) {
  return requestClient.post('/cmdb/azs', data);
}

/**
 * 编辑可用区
 */
export async function editAZApi(data: AZ) {
  return requestClient.put(`/cmdb/azs/${data.id}`, data);
}

/**
 * 删除可用区
 */
export async function deleteAZApi(id: number) {
  return requestClient.delete(`/cmdb/azs/${id}`);
}

/**
 * 根据区域ID获取可用区列表
 */
export async function getAZsByRegionIdApi(regionId: number) {
  return requestClient.get<AZ[]>(`/cmdb/regions/${regionId}/azs`);
}
