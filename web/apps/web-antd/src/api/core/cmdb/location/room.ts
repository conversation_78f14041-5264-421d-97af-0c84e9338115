import { requestClient } from '#/api/request';

/** 获取房间表格数据接口参数 */
export interface GetRoomTableApiParams {
  page: number;
  pageSize: number;
  query?: string;
  dataCenterID?: number;
  [key: string]: any;
}

// 定义房间记录接口
export interface Room {
  id: number;
  name: string;
  dataCenterID: number;
  dataCenter?: {
    az?: {
      id: number;
      name: string;
      region?: {
        id: number;
        name: string;
      };
      regionId?: number;
    };
    azId?: number;
    id: number;
    name: string;
    status: string;
  };
  status: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
}

// 定义 API 返回的整体数据格式
export interface GetRoomTableApiResponse {
  list: Room[];
  total: number;
}

/**
 * 获取房间表格数据
 */
export async function getRoomTableApi(params: GetRoomTableApiParams) {
  return requestClient.get<GetRoomTableApiResponse>('/cmdb/rooms', {
    params,
  });
}

/**
 * 获取房间详情
 */
export async function getRoomDetailApi(id: number) {
  return requestClient.get<{ data: Room }>(`/cmdb/rooms/${id}`);
}

/**
 * 新增房间
 */
export async function addRoomApi(
  data: Omit<Room, 'created_at' | 'id' | 'updated_at'>,
) {
  return requestClient.post('/cmdb/rooms', data);
}

/**
 * 编辑房间
 */
export async function editRoomApi(data: Room) {
  return requestClient.put(`/cmdb/rooms/${data.id}`, data);
}

/**
 * 删除房间
 */
export async function deleteRoomApi(id: number) {
  return requestClient.delete(`/cmdb/rooms/${id}`);
}

/**
 * 根据机房ID获取房间列表
 */
export async function getRoomsByDataCenterIdApi(dataCenterId: number) {
  return requestClient.get<Room[]>(`/cmdb/datacenters/${dataCenterId}/rooms`);
}
