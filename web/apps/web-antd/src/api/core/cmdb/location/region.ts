import { requestClient } from '#/api/request';

/** 获取区域信息表格数据接口参数 */
export interface GetRegionTableApiParams {
  page: number;
  pageSize: number;
  name?: string;
  [key: string]: any;
}

// 定义区域记录接口
export interface Region {
  id: number;
  name: string;
  status: string;
  description: string;
}

// 定义 API 返回的整体数据格式
export interface GetRegionTableApiResponse {
  list: Region[];
  total: number;
}

/**
 * 获取区域信息表格数据
 */
export async function getRegionTableApi(params: GetRegionTableApiParams) {
  return requestClient.get<GetRegionTableApiResponse>('/cmdb/regions', {
    params,
  });
}

/**
 * 获取所有区域信息数据（用于导出全部）
 */
export async function getAllRegionApi() {
  return requestClient.get<GetRegionTableApiResponse>('/cmdb/regions', {
    params: {
      page: 1,
      pageSize: 10_000, // 设置一个足够大的数字以获取所有数据
    },
  });
}

/**
 * 获取区域详情
 */
export async function getRegionDetailApi(id: number) {
  return requestClient.get<{ data: Region }>(`/cmdb/regions/${id}`);
}

/**
 * 新增区域
 */
export async function addRegionApi(
  data: Omit<Region, 'created_at' | 'id' | 'updated_at'>,
) {
  return requestClient.post('/cmdb/regions', data);
}

/**
 * 编辑区域
 */
export async function editRegionApi(data: Region) {
  return requestClient.put(`/cmdb/regions/${data.id}`, data);
}

/**
 * 删除区域
 */
export async function deleteRegionApi(id: number) {
  return requestClient.delete(`/cmdb/regions/${id}`);
}
