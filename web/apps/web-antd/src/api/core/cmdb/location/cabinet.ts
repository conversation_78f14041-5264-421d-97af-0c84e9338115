import { requestClient } from '#/api/request';

/** 获取机柜表格数据接口参数 */
export interface GetCabinetTableApiParams {
  page: number;
  pageSize: number;
  query?: string;
  roomID?: number;
  [key: string]: any;
}

// 定义机柜记录接口
export interface Cabinet {
  id: number;
  name: string;
  roomID: number;
  room?: {
    dataCenter?: {
      az?: {
        id: number;
        name: string;
        region?: {
          id: number;
          name: string;
        };
        regionId?: number;
      };
      azId?: number;
      id: number;
      name: string;
    };
    dataCenterID?: number;
    id: number;
    name: string;
    status: string;
  };
  capacityUnits: number;
  row?: string;
  column?: string;
  cabinetType?: string;
  networkEnvironment?: string;
  bondType?: string;
  totalPower?: number;
  status: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
}

// 定义 API 返回的整体数据格式
export interface GetCabinetTableApiResponse {
  list: Cabinet[];
  total: number;
}

/**
 * 获取机柜表格数据
 */
export async function getCabinetTableApi(params: GetCabinetTableApiParams) {
  return requestClient.get<GetCabinetTableApiResponse>('/cmdb/cabinets', {
    params,
  });
}

/**
 * 获取机柜详情
 */
export async function getCabinetDetailApi(id: number) {
  return requestClient.get<{ data: Cabinet }>(`/cmdb/cabinets/${id}`);
}

/**
 * 新增机柜
 */
export async function addCabinetApi(
  data: Omit<Cabinet, 'created_at' | 'id' | 'updated_at'>,
) {
  return requestClient.post('/cmdb/cabinets', data);
}

/**
 * 编辑机柜
 */
export async function editCabinetApi(data: Cabinet) {
  return requestClient.put(`/cmdb/cabinets/${data.id}`, data);
}

/**
 * 删除机柜
 */
export async function deleteCabinetApi(id: number) {
  return requestClient.delete(`/cmdb/cabinets/${id}`);
}

/**
 * 根据房间ID获取机柜列表
 */
export async function getCabinetsByRoomIdApi(roomId: number) {
  return requestClient.get<Cabinet[]>(`/cmdb/rooms/${roomId}/cabinets`);
}
