import { requestClient } from '#/api/request';

/** 获取机房表格数据接口参数 */
export interface GetDataCenterTableApiParams {
  page: number;
  pageSize: number;
  query?: string;
  azId?: number;
  [key: string]: any;
}

// 定义机房记录接口
export interface DataCenter {
  id: number;
  name: string;
  azId: number;
  az?: {
    id: number;
    name: string;
    region?: {
      id: number;
      name: string;
    };
    status: string;
  };
  address: string;
  description: string;
  status: string;
  created_at?: string;
  updated_at?: string;
}

// 定义 API 返回的整体数据格式
export interface GetDataCenterTableApiResponse {
  list: DataCenter[];
  total: number;
}

/**
 * 获取机房表格数据
 */
export async function getDataCenterTableApi(
  params: GetDataCenterTableApiParams,
) {
  return requestClient.get<GetDataCenterTableApiResponse>('/cmdb/datacenters', {
    params,
  });
}

/**
 * 获取机房详情
 */
export async function getDataCenterDetailApi(id: number) {
  return requestClient.get<{ data: DataCenter }>(`/cmdb/datacenters/${id}`);
}

/**
 * 新增机房
 */
export async function addDataCenterApi(
  data: Omit<DataCenter, 'created_at' | 'id' | 'updated_at'>,
) {
  return requestClient.post('/cmdb/datacenters', data);
}

/**
 * 编辑机房
 */
export async function editDataCenterApi(data: DataCenter) {
  return requestClient.put(`/cmdb/datacenters/${data.id}`, data);
}

/**
 * 删除机房
 */
export async function deleteDataCenterApi(id: number) {
  return requestClient.delete(`/cmdb/datacenters/${id}`);
}

/**
 * 根据可用区ID获取机房列表
 */
export async function getDataCentersByAZIdApi(azId: number) {
  return requestClient.get<DataCenter[]>(`/cmdb/azs/${azId}/datacenters`);
}
