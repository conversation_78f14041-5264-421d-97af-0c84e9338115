import { requestClient } from '#/api/request';

// 操作日志类型定义
export interface OperationLog {
  id: number;
  created_at: string;

  // 操作模块信息
  module: string;
  operation: string;

  // 登录信息
  user_id: number;
  username: string;
  ip_address: string;
  user_agent: string;

  // 请求信息
  request_url: string;
  request_method: string;
  operation_func: string;
  request_body: string;
  response_body: string;

  // 结果信息
  status: string;
  execution_time: number;
  operation_time: string;
}

// 获取操作日志列表
export async function getOperationLogsApi(params: {
  end_time?: string;
  module?: string;
  operation?: string;
  page?: number;
  page_size?: number;
  start_time?: string;
  username?: string;
}) {
  return requestClient.get<{
    list: OperationLog[];
    total: number;
  }>('/audit/operation-logs', { params });
}
