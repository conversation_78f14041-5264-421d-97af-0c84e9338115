import { requestClient } from '#/api/request';

// 变更记录类型定义
export interface ChangeLog {
  id: number;
  created_at: string;
  table_name: string;
  entity_id: number;
  operation_type: string;
  user_id: number;
  username: string;
  change_data: string;
  ip_address: string;
  user_agent: string;
}

// 获取变更记录列表
export async function getChangeLogsApi(params: {
  entity_id?: number;
  page?: number;
  page_size?: number;
  table_name?: string;
}) {
  return requestClient.get<{
    list: ChangeLog[];
    total: number;
  }>('/audit/change-logs', { params });
}
