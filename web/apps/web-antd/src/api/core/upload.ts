import { useAccessStore } from '@vben/stores';

import { requestClient } from '#/api/request';

/**
 * 文件上传结果接口
 */
export interface UploadFileResult {
  url: string;
  path?: string;
  name?: string;
  size?: number;
  type?: string;
  data?: {
    [key: string]: any;
    url: string;
  };
  [key: string]: any;
}

/**
 * 文件上传参数
 */
export interface UploadFileParams {
  file: File;
  module_type?: string;
  module_id?: number;
  description?: string;
  path?: string;
}

/**
 * 上传单个文件
 * @param file 文件对象或上传参数对象
 * @param moduleType 关联模块类型(可选)
 * @param moduleId 关联模块ID(可选)
 * @param description 文件描述(可选)
 * @returns 返回上传结果
 */
export const uploadFile = async (
  file: File | UploadFileParams,
  moduleType?: string,
  moduleId?: number,
  description?: string,
): Promise<UploadFileResult> => {
  try {
    // 手动创建FormData
    const formData = new FormData();

    // 判断传入的是File对象还是参数对象
    if (file instanceof File) {
      formData.append('file', file);
      // 添加可选参数
      if (moduleType) formData.append('module_type', moduleType);
      if (moduleId) formData.append('module_id', moduleId.toString());
      if (description) formData.append('description', description);
    } else {
      // 参数对象
      formData.append('file', file.file);
      if (file.module_type) formData.append('module_type', file.module_type);
      if (file.module_id)
        formData.append('module_id', file.module_id.toString());
      if (file.description) formData.append('description', file.description);
      if (file.path) formData.append('path', file.path);
    }

    // 获取访问令牌
    const accessStore = useAccessStore();
    const token = accessStore.accessToken;

    // 如果没有令牌，抛出错误
    if (!token) {
      throw new Error('未授权：无访问令牌，请先登录');
    }

    // 使用requestClient直接上传
    return requestClient.post<UploadFileResult>('/file/upload', formData, {
      headers: {
        // 不手动设置Content-Type, 浏览器会自动设置为multipart/form-data并添加boundary
        'Content-Type': undefined,
      },
      timeout: 60 * 1000, // 上传可能需要较长时间
    });
  } catch (error) {
    console.error('上传文件错误:', error);
    throw error;
  }
};

/**
 * 批量上传文件
 * @param files 文件数组
 * @param moduleType 关联模块类型(可选)
 * @param moduleId 关联模块ID(可选)
 * @param description 文件描述(可选)
 * @returns 返回上传结果数组
 */
export const uploadFiles = async (
  files: File[],
  moduleType?: string,
  moduleId?: number,
  description?: string,
): Promise<UploadFileResult[]> => {
  const uploadPromises = files.map((file) =>
    uploadFile(file, moduleType, moduleId, description),
  );
  return Promise.all(uploadPromises);
};

/**
 * 上传Base64格式的图片
 * @param base64 Base64字符串
 * @param filename 文件名
 * @param moduleType 关联模块类型(可选)
 * @param moduleId 关联模块ID(可选)
 * @param description 文件描述(可选)
 * @returns 返回上传结果
 */
export const uploadBase64 = async (
  base64: string,
  filename: string,
  moduleType?: string,
  moduleId?: number,
  description?: string,
): Promise<UploadFileResult> => {
  // 将Base64转换为Blob
  const arr = base64.split(',');
  // 修复类型错误
  const mime = arr[0]?.match(/:(.*?);/)?.[1] || 'image/png';

  // 确保arr[1]存在
  if (!arr[1]) {
    throw new Error('无效的Base64格式');
  }

  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.codePointAt(n) || 0;
  }

  // 创建File对象
  const file = new File([u8arr], filename, { type: mime });

  return uploadFile(file, moduleType, moduleId, description);
};

/**
 * 获取模块关联的文件列表接口
 */
export interface ModuleFileListResult {
  id: number;
  file_name: string;
  storage_path: string;
  mime_type: string;
  file_type: string;
  file_size: number;
  url: string;
  thumbnail_url?: string;
  description?: string;
  module_type: string;
  module_id: number;
  created_at: string;
  uploaded_by: number;
  tags?: string;
  [key: string]: any;
}

/**
 * 获取模块关联的文件列表
 * @param moduleType 模块类型
 * @param moduleId 模块ID
 * @returns 文件列表
 */
export const getModuleFiles = async (
  moduleType: string,
  moduleId: number,
): Promise<ModuleFileListResult[]> => {
  try {
    // 使用与 user.ts 相同的请求方式
    const result = await requestClient.get<ModuleFileListResult[]>(
      '/file/list-by-module',
      {
        params: {
          module_type: moduleType,
          module_id: moduleId,
        },
      },
    );

    // 如果后端返回格式是 { data: [...] } 这种包装格式
    return result;
  } catch (error) {
    console.error('获取模块关联文件失败:', error);
    throw error;
  }
};

/**
 * 通用上传方法
 * @param url 上传接口
 * @param formData 表单数据
 * @returns 返回上传的响应结果
 */
export function commonUploadFile(url: string, formData: FormData) {
  try {
    // 手动创建FormData

    // 获取访问令牌
    const accessStore = useAccessStore();
    const token = accessStore.accessToken;

    // 如果没有令牌，抛出错误
    if (!token) {
      throw new Error('未授权：无访问令牌，请先登录');
    }

    // 使用requestClient直接上传
    return requestClient.post(url, formData, {
      headers: {
        // 不手动设置Content-Type, 浏览器会自动设置为multipart/form-data并添加boundary
        'Content-Type': undefined,
      },
      timeout: 60 * 1000, // 上传可能需要较长时间
    });
  } catch (error) {
    console.error('上传文件错误:', error);
    throw error;
  }
}

/**
 * 带进度回调的文件上传参数
 */
export interface UploadWithProgressParams {
  file: File;
  module_type?: string;
  module_id?: number;
  description?: string;
  path?: string;
  onError?: (error: Error) => void;
  onProgress?: (progress: { percent: number }) => void;
  onSuccess?: (data: UploadFileResult, file: File) => void;
}

/**
 * 上传进度事件类型
 */
export interface ProgressEvent {
  loaded: number;
  total?: number;
}

/**
 * 带进度回调的文件上传
 * @param params 上传参数对象
 * @param params.file 要上传的文件对象
 * @param params.module_type 关联模块类型(可选)
 * @param params.module_id 关联模块ID(可选)
 * @param params.description 文件描述(可选)
 * @param params.path 上传路径(可选)
 * @param params.onError 上传错误回调函数(可选)
 * @param params.onProgress 上传进度回调函数(可选)
 * @param params.onSuccess 上传成功回调函数(可选)
 * @returns 返回上传结果的Promise
 */
export async function uploadFileWithProgress({
  file,
  module_type,
  module_id,
  description,
  path,
  onError,
  onProgress,
  onSuccess,
}: UploadWithProgressParams) {
  try {
    // 通知开始上传
    onProgress?.({ percent: 0 });

    // 手动创建FormData
    const formData = new FormData();
    formData.append('file', file);

    // 添加可选参数
    if (module_type) formData.append('module_type', module_type);
    if (module_id) formData.append('module_id', module_id.toString());
    if (description) formData.append('description', description);
    if (path) formData.append('path', path);

    // 获取访问令牌
    const accessStore = useAccessStore();
    const token = accessStore.accessToken;

    // 如果没有令牌，抛出错误
    if (!token) {
      throw new Error('未授权：无访问令牌，请先登录');
    }

    // 使用post方法上传，并手动处理进度回调
    const data = await requestClient.post<UploadFileResult>(
      '/file/upload',
      formData,
      {
        headers: {
          'Content-Type': undefined, // 让浏览器自动设置Content-Type
        },
        timeout: 60 * 1000,
        onUploadProgress: (progressEvent: ProgressEvent) => {
          const percent = Math.round(
            (progressEvent.loaded * 100) / (progressEvent.total || 1),
          );
          onProgress?.({ percent });
        },
      },
    );

    // 上传完成
    onProgress?.({ percent: 100 });
    onSuccess?.(data, file);
    return data;
  } catch (error) {
    // 处理错误
    const err = error instanceof Error ? error : new Error(String(error));
    onError?.(err);
    throw err;
  }
}

/**
 * 批量上传带进度回调
 * @param files 文件数组
 * @param options 上传选项对象
 * @param options.description 文件描述(可选)
 * @param options.module_id 关联模块ID(可选)
 * @param options.module_type 关联模块类型(可选)
 * @param options.path 上传路径(可选)
 * @param callbacks 回调函数对象
 * @returns 返回上传结果数组的Promise
 */
export async function uploadFilesWithProgress(
  files: File[],
  options?: {
    description?: string;
    module_id?: number;
    module_type?: string;
    path?: string;
  },
  callbacks?: {
    onAllComplete?: (results: UploadFileResult[]) => void;
    onFileError?: (error: Error, file: File) => void;
    onFileProgress?: (file: File, progress: { percent: number }) => void;
    onFileSuccess?: (data: UploadFileResult, file: File) => void;
    onTotalProgress?: (progress: { percent: number }) => void;
  },
): Promise<UploadFileResult[]> {
  const {
    onTotalProgress,
    onFileProgress,
    onFileSuccess,
    onFileError,
    onAllComplete,
  } = callbacks || {};

  // 计算总进度
  let totalProgress = 0;
  const fileProgresses = new Map<File, number>();

  const updateTotalProgress = () => {
    if (!onTotalProgress) return;

    // 计算所有文件的平均进度
    let sum = 0;
    fileProgresses.forEach((progress) => {
      sum += progress;
    });

    totalProgress = Math.round(sum / (files.length || 1));
    onTotalProgress({ percent: totalProgress });
  };

  // 初始化每个文件的进度为0
  files.forEach((file) => {
    fileProgresses.set(file, 0);
  });

  // 初始通知总进度为0
  onTotalProgress?.({ percent: 0 });

  try {
    // 为每个文件创建上传任务
    const uploadTasks = files.map((file) =>
      uploadFileWithProgress({
        file,
        module_type: options?.module_type,
        module_id: options?.module_id,
        description: options?.description,
        path: options?.path,
        onProgress: (progress) => {
          // 更新单个文件进度
          fileProgresses.set(file, progress.percent);
          onFileProgress?.(file, progress);
          updateTotalProgress();
        },
        onSuccess: (data, file) => {
          onFileSuccess?.(data, file);
        },
        onError: (error) => {
          onFileError?.(error, file);
        },
      }),
    );

    // 等待所有上传任务完成
    const results = await Promise.all(uploadTasks);

    // 通知所有文件上传完成
    onTotalProgress?.({ percent: 100 });
    onAllComplete?.(results);

    return results;
  } catch {
    // 错误已经在单个文件上传中处理，这里不再抛出
    return [];
  }
}
