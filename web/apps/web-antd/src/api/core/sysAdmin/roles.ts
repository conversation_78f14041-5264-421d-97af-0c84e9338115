import { requestClient } from '#/api/request';

// 角色类型定义
export interface Role {
  id: string;
  name: string;
  status: number; // 0-禁用 1-启用
  createTime?: string;
  updateTime?: string;
  remark: string;
  permissions?: number[]; // 角色拥有的权限ID列表
}

// 创建角色请求参数
export interface RoleCreateParams {
  name: string;
  status: number;
  permissions?: number[];
  remark: string;
}

// 更新角色请求参数
export interface RoleUpdateParams extends RoleCreateParams {
  id: string;
}

// 角色列表请求参数
export interface RoleListParams {
  page?: number;
  pageSize?: number;
  name?: string;
}

// 角色列表响应
export interface RoleListResponse {
  items: Role[];
  total: number;
}

// 获取角色列表
export const getRoleTableApi = async (params: RoleListParams) => {
  return requestClient.get<RoleListResponse>('/system/role/list', {
    params,
  });
};

// 获取角色详情
export const getRoleByIdApi = async (id: string) => {
  return requestClient.get<Role>(`/system/role/${id}`);
};

// 创建角色
export const addRoleApi = async (params: RoleCreateParams) => {
  return requestClient.post('/system/role', params);
};

// 编辑角色
export const editRoleApi = async (params: RoleUpdateParams) => {
  return requestClient.put('/system/role', params);
};

// 删除角色
export const deleteRoleApi = async (id: string) => {
  return requestClient.delete(`/system/role/${id}`);
};

// 获取角色菜单
export const getRoleMenusApi = async (roleId: string) => {
  return requestClient.get<any[]>('/system/role/menus', {
    params: { roleId },
  });
};
