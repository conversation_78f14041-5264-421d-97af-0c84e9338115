import { requestClient } from '#/api/request';

/**
 * 导入结果接口
 */
export interface ImportResult {
  total: number;
  successCount: number;
  failCount: number;
  data: any[];
  failures: ImportError[];
}

/**
 * 导入错误详情
 */
export interface ImportError {
  row: number;
  message: string;
  data: Record<string, string>;
}

/**
 * 导入数据
 * @param file 文件对象
 * @param model 模型类型
 * @param importMode 导入模式: 'overwrite'=覆盖, 'append_bottom'=底部追加, 'append_top'=顶部追加
 * @param handleSoftDeleted 软删除记录处理方式: 'restore'=恢复, 'ignore'=忽略, 'error'=报错, 'delete'=物理删除
 */
export async function importDataApi(
  file: File,
  model: string,
  importMode: string = 'overwrite',
  handleSoftDeleted: string = 'error',
) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('model', model);
  formData.append('import_mode', importMode);
  formData.append('handle_soft_deleted', handleSoftDeleted);

  return requestClient.post<ImportResult>('/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 下载导入模板
 * @param model 模型类型
 * @returns Promise<void>
 */
export async function downloadImportTemplate(model: string): Promise<void> {
  try {
    const url = `/import/template/${model}`;

    // 使用requestClient.download方法获取Blob
    const blob = await requestClient.download(url);

    // 使用获取到的Blob创建下载链接
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `${model}_import_template.csv`;
    document.body.append(link);
    link.click();

    // 延迟清理，确保下载开始
    setTimeout(() => {
      link.remove();
      window.URL.revokeObjectURL(downloadUrl);
    }, 100);
  } catch (error: unknown) {
    throw new Error(
      `下载模板失败: ${error instanceof Error ? error.message : '未知错误'}`,
    );
  }
}
