import { requestClient } from '#/api/request';

enum Api {
  BaseUrl = '/hardware_maintenance/asset/racking',
}

// 获取上架工单列表
export function listRackingTicketsApi(params: {
  applicant?: string;
  endTime?: string;
  page?: number;
  pageSize?: number;
  project?: string;
  startTime?: string;
  status?: string;
  ticketNo?: string;
  title?: string;
}) {
  return requestClient.get<any>(`${Api.BaseUrl}`, {
    params,
  });
}

// 获取上架工单详情
export function getRackingTicketDetailApi(id: number) {
  return requestClient.get<any>(`${Api.BaseUrl}/${id}`);
}

// 创建上架工单
export function createRackingTicketApi(data: {
  PlannedRackTime?: Date | null | string;
  project: string;
  rackItems: Array<{
    assetID: number;
    cabinetID: number;
    rackPosition: number;
    roomID: number;
    status: string;
  }>;
  remark?: string;
  title: string;
}) {
  return requestClient.post(`${Api.BaseUrl}/create_ticket`, data);
}

// 变更工单状态
export function transitionRackingTicketApi(
  id: number,
  data: {
    comment?: string;
    rackItems?: Array<{
      assetID: number;
      bmcMAC?: string;
      cabinetID?: number;
      rackPosition?: number;
      roomID?: number;
      status: string;
      vpcMAC?: string;
    }>;
    status: string;
  },
) {
  return requestClient.put(`${Api.BaseUrl}/${id}/transition`, data);
}

// 完成工单
export function completeRackingTicketApi(id: number, data: any) {
  return requestClient.put(`${Api.BaseUrl}/${id}/complete`, data);
}

// 获取工单历史记录
export function getRackingTicketHistoryApi(id: number) {
  return requestClient.get<any>(`${Api.BaseUrl}/${id}/history`);
}
