import { requestClient } from '#/api/request';

enum Api {
  BaseUrl = '/hardware_maintenance/asset/acceptance',
}

// 工单状态类型
export type OrderStatus =
  | 'approved'
  | 'cancelled'
  | 'collected'
  | 'completed'
  | 'pending'
  | 'rejected';

// 验收项目类型
export interface AcceptanceItem {
  id?: number;
  order_id?: number;
  device_sn: string;
  vendor: string;
  model: string;
  template_name: string;
  is_pass?: boolean;
  reason?: string;
  created_at?: string;
  updated_at?: string;
  deleted_at?: null;
}

// 验收工单类型
export interface AcceptanceOrder {
  ID: number;
  OrderNo: string;
  Title: string;
  DeviceCount: number;
  Status: string;
  InitiatorID: number;
  InitiatorName: string;
  Description?: string;
  CreatedAt?: string;
  UpdatedAt?: string;
}

// 详情扩展接口，包含items
export interface AcceptanceOrderDetail extends AcceptanceOrder {
  Items?: AcceptanceItem[];
}

// 列表查询参数
export interface AcceptanceOrderParams {
  page?: number;
  pageSize?: number;
  title?: string;
  status?: string;
  orderNo?: string;
  initiatorName?: string;
  startDate?: string;
  endDate?: string;
  dateRange?: string[];
}

// 列表结果
export interface AcceptanceOrderResult {
  list: AcceptanceOrder[];
  total: number;
}

// 验收项列表结果
export interface AcceptanceItemResult {
  list: AcceptanceItem[];
  total: number;
}

// 创建工单请求
export interface CreateAcceptanceOrderRequest {
  title: string;
  description?: string;
  items: {
    device_id: number;
    device_sn: string;
    model: string;
    template_name: string;
    vendor: string;
  }[];
}

// 审核工单请求
export interface AuditAcceptanceOrderRequest {
  is_approved: boolean;
  remark: string;
}

// 取消工单请求
export interface OperateAcceptanceOrderRequest {
  remark: string;
}

// 分配工单请求
export interface AssignAcceptanceOrderRequest {
  assigneeID: number;
  assigneeName: string;
  remark: string;
}

// 完成工单请求
export interface CompleteAcceptanceOrderRequest {
  remark: string;
  items: {
    device_sn: string;
    is_pass: boolean;
    model: string;
    reason?: string;
    template_name: string;
    vendor: string;
  }[];
}

// 不存在PN信息结构
export interface NoExistingPNInfo {
  pn: string; // 为空，则表示这个组件的PN需要填入。不为空，则表示这个组件的PN的组件信息需要手动录入cmdb
  component_type: string;
  model: string;
  description: string;
}

// 完成验收工单响应结构
export interface CompleteAcceptanceOrderResponse {
  invalid_device_sns: string[]; // 检测出的设备结果SN不存在，但是通过的非法设备SNs
  no_existing_pn_info: NoExistingPNInfo[]; // 硬件检测结果中的不存在cmdb的PN
  db_existing_component_sn: string[]; // 通过的，但是设备SN已存在于cmdb中
}

// 更新组件PN请求
export interface UpdateComponentPNRequest {
  component_id: number;
  pn: string;
}

// 批量更新组件PN请求
export interface BatchUpdateComponentPNRequest {
  updates: UpdateComponentPNRequest[];
}

// 检测组件信息
export interface InspectingComponentInfo {
  item_id: number;
  pn: string;
  model: string;
  component_id: number;
  status: string;
  description: string;
}

// 更新检测组件信息请求
export interface UpdateInspectingComponentInfoReq {
  order_id: number;
  item_id: number;
  component_infos: InspectingComponentInfo[];
}

/**
 * 获取验收工单列表
 */
export function getAcceptanceListApi(params: AcceptanceOrderParams) {
  return requestClient.get<AcceptanceOrderResult>(Api.BaseUrl, {
    params,
  });
}

/**
 * 获取验收工单详情
 */
export function getAcceptanceDetailApi(id: number) {
  return requestClient.get<AcceptanceOrderDetail>(`${Api.BaseUrl}/${id}`);
}

/**
 * 获取验收工单的验收项列表
 */
export function getAcceptanceItemsApi(id: number) {
  return requestClient.get<AcceptanceItemResult>(`${Api.BaseUrl}/${id}/items`);
}

/**
 * 创建验收工单
 */
export function createAcceptanceOrderApi(data: CreateAcceptanceOrderRequest) {
  return requestClient.post(`${Api.BaseUrl}/create_order`, data);
}

/**
 * 审核验收工单
 */
export function auditAcceptanceOrderApi(
  id: number,
  data: AuditAcceptanceOrderRequest,
) {
  return requestClient.put(`${Api.BaseUrl}/${id}/audit`, data);
}

/**
 * 取消验收工单
 */
export function cancelAcceptanceOrderApi(
  id: number,
  data: OperateAcceptanceOrderRequest,
) {
  return requestClient.put(`${Api.BaseUrl}/${id}/cancel`, data);
}

/**
 * 分配验收工单
 */
export function assignAcceptanceOrderApi(
  id: number,
  data: AssignAcceptanceOrderRequest,
) {
  return requestClient.put(`${Api.BaseUrl}/${id}/assign`, data);
}

/**
 * 完成验收工单
 */
export function completeAcceptanceOrderApi(
  id: number,
  data: CompleteAcceptanceOrderRequest,
) {
  return requestClient.put<CompleteAcceptanceOrderResponse>(
    `${Api.BaseUrl}/${id}/complete`,
    data,
  );
}

/**
 * 上传验收检测结果
 */
export function uploadCheckResultApi(
  id: number,
  files: File[],
  description: string = '',
) {
  const formData = new FormData();

  // 添加多个文件
  files.forEach((file) => {
    formData.append('files', file);
  });

  // 添加描述信息
  if (description) {
    formData.append('description', description);
  }

  return requestClient.post(`${Api.BaseUrl}/${id}/upload_check`, formData, {
    headers: {
      // 不要手动设置Content-Type，让浏览器自动添加带boundary的multipart/form-data
      'Content-Type': undefined,
    },
  });
}

// 获取验收工单操作历史
export function getAcceptanceOrderHistoryApi(id: number) {
  return requestClient.get<any>(`${Api.BaseUrl}/${id}/history`);
}

/**
 * 获取验收项组件详情
 */
export function getAcceptanceItemComponentsApi(itemId: number) {
  return requestClient.get<any>(`${Api.BaseUrl}/item_components/${itemId}`);
}

/**
 * 更新组件PN
 */
export function updateComponentPNApi(data: UpdateComponentPNRequest) {
  return requestClient.put(`${Api.BaseUrl}/component/${data.component_id}/pn`, {
    pn: data.pn,
  });
}

/**
 * 批量更新组件PN
 */
export function batchUpdateComponentPNApi(data: BatchUpdateComponentPNRequest) {
  return requestClient.put(`${Api.BaseUrl}/components/batch_update_pn`, data);
}

/**
 * 更新检测组件信息
 */
export function updateInspectingComponentInfoApi(
  itemId: number,
  data: UpdateInspectingComponentInfoReq,
) {
  return requestClient.put(`${Api.BaseUrl}/update_inspecting_component_info`, {
    ...data,
    item_id: itemId,
  });
}

// 自动填入组件PN请求参数
export interface FillComponentPNRequest {
  item_ids: number[];
  over_write_existing?: boolean;
}

/**
 * 自动填入组件PN
 */
export function fillComponentPNApi(data: FillComponentPNRequest) {
  return requestClient.put(`${Api.BaseUrl}/fill_component_pn`, data);
}

// ==================== 型号PN映射相关接口 ====================

// 设备型号到PN映射数据结构
export interface ComponentModelPNMapping {
  id: number;
  component_type: string; // 支持所有组件类型
  model: string;
  pn: string;
  description?: string; // 添加描述字段
  status: 'ACTIVE' | 'INACTIVE'; // ACTIVE: 启用, INACTIVE: 禁用
  created_at: string;
  updated_at: string;
}

// 设备型号到PN映射查询过滤器
export interface ComponentModelPNMappingFilter {
  ids?: number[];
  model?: string;
  component_type?: string; // 支持所有组件类型
  pn?: string;
  status?: 'ACTIVE' | 'INACTIVE';
  page?: number;
  pageSize?: number;
}

// 设备型号到PN映射列表结果
export interface ComponentModelPNMappingResult {
  list: ComponentModelPNMapping[];
  total: number;
}

// 创建设备型号到PN映射请求
export interface CreateComponentModelPNMappingRequest {
  model: string;
  component_type: string; // 支持所有组件类型
  pn: string;
  description?: string; // 添加描述字段
  status?: 'ACTIVE' | 'INACTIVE'; // 默认为ACTIVE
}

// 更新设备型号到PN映射请求
export interface UpdateComponentModelPNMappingRequest {
  component_type?: string; // 支持所有组件类型
  model?: string;
  pn?: string;
  description?: string; // 添加描述字段
  status?: 'ACTIVE' | 'INACTIVE';
}

/**
 * 获取设备型号PN映射列表
 */
export function getComponentModelPNMappingListApi(
  params: ComponentModelPNMappingFilter,
) {
  return requestClient.get<ComponentModelPNMappingResult>(
    `${Api.BaseUrl}/device_model_pn_mapping`,
    { params },
  );
}

/**
 * 创建设备型号PN映射
 */
export function createComponentModelPNMappingApi(
  data: CreateComponentModelPNMappingRequest,
) {
  return requestClient.post(`${Api.BaseUrl}/device_model_pn_mapping`, data);
}

/**
 * 更新设备型号PN映射
 */
export function updateComponentModelPNMappingApi(
  id: number,
  data: UpdateComponentModelPNMappingRequest,
) {
  return requestClient.put(
    `${Api.BaseUrl}/device_model_pn_mapping/${id}`,
    data,
  );
}

/**
 * 删除设备型号PN映射
 */
export function deleteComponentModelPNMappingApi(id: number) {
  return requestClient.delete(`${Api.BaseUrl}/device_model_pn_mapping/${id}`);
}

/**
 * 根据型号、组件类型查询PN
 */
export function queryDeviceModelPNByModelComponentApi(params: {
  component_type: string;
  model: string;
}) {
  return requestClient.get<{ description?: string; pn: string }>(
    `${Api.BaseUrl}/device_model_pn_mapping/query`,
    { params },
  );
}
