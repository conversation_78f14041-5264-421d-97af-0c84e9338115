import { requestClient } from '#/api/request';

/** 获取软件下线工单列表接口参数 */
export interface ListOfflineTicketsParams {
  page: number;
  pageSize: number;
  ticket_no?: string;
  status?: string;
  title?: string;
}

/** 软件下线设备 */
export interface Device {
  id?: number;
  sn: string;
  vendor: string;
  model: string;
  hostname: string;
  vpc_ip: string;
  bmc_ip: string;
  notes?: string;
}

/** 软件下线工单 */
export interface OfflineTicket {
  id: number;
  offline_ticket_no: string;
  title: string;
  status: string;
  stage: string;
  offline_reason: string;
  applicant_id: number;
  applicant_name: string;
  handler_id?: number;
  handler_name: string;
  planned_completion_time: string;
  devices: Device[];
  devices_count: number;
  notes?: string;
  created_at: string;
}

/** 创建软件下线工单请求参数 */
export interface CreateOfflineTicketParams {
  title: string;
  planned_completion_time: string;
  offline_reason: string;
  devices: Device[];
  notes?: string;
}

/** 状态转换请求参数 */
export interface TransitionOfflineTicketParams {
  stage: string;
  status: string;
  comments?: string;
  data?: Record<string, any>;
}

/**
 * 获取软件下线工单列表
 */
export async function listOfflineTicketsApi(params: ListOfflineTicketsParams) {
  return requestClient.get<{
    list: OfflineTicket[];
    total: number;
  }>('/software_maintenance/offline', {
    params,
  });
}

/**
 * 创建软件下线工单
 */
export async function createOfflineTicketApi(data: CreateOfflineTicketParams) {
  return requestClient.post('/software_maintenance/offline', data);
}

/**
 * 获取软件下线工单详情
 */
export async function getOfflineTicketDetailApi(id: number) {
  return requestClient.get<OfflineTicket>(
    `/software_maintenance/offline/${id}`,
  );
}

/**
 * 获取软件下线工单状态历史
 */
export async function getOfflineTicketHistoryApi(id: number) {
  return requestClient.get(`/software_maintenance/offline/${id}/history`);
}

/**
 * 软件下线工单状态转换
 */
export async function transitionOfflineTicketApi(
  id: number,
  data: TransitionOfflineTicketParams,
) {
  return requestClient.put(
    `/software_maintenance/offline/part/${id}/transition`,
    data,
  );
}
