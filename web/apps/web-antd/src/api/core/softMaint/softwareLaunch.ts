import { requestClient } from '#/api/request';

/** 获取软件上线工单列表接口参数 */
export interface ListLaunchTicketsParams {
  page: number;
  pageSize: number;
  ticket_no?: string;
  status?: string;
  title?: string;
}

/** 软件上线设备 */
export interface Device {
  id?: number;
  sn: string;
  vendor: string;
  model: string;
  hostname: string;
  vpc_ip: string;
  bmc_ip: string;
  delivery_status?: string;
  notes?: string;
}

/** 软件上线工单 */
export interface LaunchTicket {
  id: number;
  launch_ticket_no: string;
  title: string;
  status: string;
  stage: string;
  applicant_id: number;
  applicant_name: string;
  handler_id?: number;
  handler: string;
  planned_completion_time: string;
  devices: Device[];
  devices_count: number;
  delivery_result?: string;
  notes?: string;
  created_at: string;
}

/** 创建软件上线工单请求参数 */
export interface CreateLaunchTicketParams {
  title: string;
  planned_completion_time: string;
  devices: Device[];
  notes?: string;
}

/** 状态转换请求参数 */
export interface TransitionLaunchTicketParams {
  stage: string;
  status: string;
  comments?: string;
  data?: Record<string, any>;
}

/**
 * 获取软件上线工单列表
 */
export async function listLaunchTicketsApi(params: ListLaunchTicketsParams) {
  return requestClient.get<{
    list: LaunchTicket[];
    total: number;
  }>('/software_maintenance/launch', {
    params,
  });
}

/**
 * 创建软件上线工单
 */
export async function createLaunchTicketApi(data: CreateLaunchTicketParams) {
  return requestClient.post('/software_maintenance/launch', data);
}

/**
 * 获取软件上线工单详情
 */
export async function getLaunchTicketDetailApi(id: number) {
  return requestClient.get<LaunchTicket>(`/software_maintenance/launch/${id}`);
}

/**
 * 获取软件上线工单状态历史
 */
export async function getLaunchTicketHistoryApi(id: number) {
  return requestClient.get(`/software_maintenance/launch/${id}/history`);
}

/**
 * 软件上线工单状态转换
 */
export async function transitionLaunchTicketApi(
  id: number,
  data: TransitionLaunchTicketParams,
) {
  return requestClient.put(
    `/software_maintenance/launch/part/${id}/transition`,
    data,
  );
}
