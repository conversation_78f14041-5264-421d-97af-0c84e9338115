import { requestClient } from '#/api/request';

/**
 * 导出字段定义
 */
export interface ExportField {
  field: string;
  title: string;
}

/**
 * 导出请求参数
 */
export interface ExportRequest {
  tableName: string; // 表名
  mode: 'all' | 'selected'; // 导出模式：all/selected
  ids?: number[]; // 选定的ID列表
  fields: ExportField[]; // 导出字段
  filename?: string; // 文件名
  sheetName?: string; // 工作表名称
  isHeader: boolean; // 是否包含表头
  condition?: string; // 额外的查询条件
}

/**
 * 导出响应结果
 */
export interface ExportResponse {
  fileUrl: string; // 文件下载URL
  expired: string; // 过期时间
}

/**
 * 导出数据
 * @param request 导出请求参数
 * @returns Promise<ExportResponse> 导出响应结果
 */
export async function exportDataApi(request: ExportRequest) {
  return requestClient.post<ExportResponse>('/export/export', request);
}

/**
 * 下载导出的文件
 * @param fileUrl 文件URL
 * @param filename 文件名
 */
export async function downloadExportFile(
  fileUrl: string,
  filename?: string,
): Promise<void> {
  try {
    // 使用requestClient.download方法获取Blob
    const blob = await requestClient.download(fileUrl);

    // 如果未提供文件名，从URL中提取
    if (!filename) {
      const urlParts = fileUrl.split('/');
      filename = urlParts[urlParts.length - 1];
    }

    // 使用获取到的Blob创建下载链接
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'export.xlsx';
    document.body.append(link);
    link.click();

    // 延迟清理，确保下载开始
    setTimeout(() => {
      link.remove();
      window.URL.revokeObjectURL(downloadUrl);
    }, 100);
  } catch (error: unknown) {
    throw new Error(
      `下载文件失败: ${error instanceof Error ? error.message : '未知错误'}`,
    );
  }
}

/**
 * 简化的导出函数，直接下载
 * @param request 导出请求参数
 */
export async function exportAndDownload(request: ExportRequest): Promise<void> {
  try {
    const response = await exportDataApi(request);
    await downloadExportFile(response.fileUrl, request.filename);
  } catch (error: unknown) {
    throw new Error(
      `导出失败: ${error instanceof Error ? error.message : '未知错误'}`,
    );
  }
}
