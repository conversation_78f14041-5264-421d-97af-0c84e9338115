import { requestClient } from '#/api/request';

export interface HardScheduleParams {
  date: string;
  username: string[]; // 现在主副值班人都放在这个数组里
  ID?: number;
}

export interface HardSchedule {
  ID: number;
  date: string;
  username: string[]; // 后端返回的也是数组
}

export interface GetHardScheduleResponse {
  list: HardSchedule[];
}

// 获取指定月份的排班数据
export async function getSchedulesByMonth(date: string) {
  return requestClient.get<GetHardScheduleResponse>(`/schedule/hard-sche/${date}`);
}

// 创建排班记录
export async function createSchedule(data: HardScheduleParams) {
  return requestClient.post('/schedule/hard-sche', data);
}

// 获取指定日期的排班数据
export async function getScheduleByDate(date: string) {
  return requestClient.get('/schedule/hard-sche', { 
    params: { date }
  });
}

// 更新排班记录
export async function updateSchedule(data: HardScheduleParams) {
  return requestClient.put('/schedule/hard-sche', data);
}
