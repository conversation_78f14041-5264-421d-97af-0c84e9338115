import { requestClient } from '#/api/request';

export interface GetScheduleApiParams {
  date: string;
  primary_username: string;
  second_username: string;
}

export interface Schedule {
  ID: number;
  date: string;
  primary_username: string;
  second_username: string;
}

export interface GetScheduleApiResponse {
  list: Schedule[];
}

export async function getSchedulesByMonth(date: string) {
  return requestClient.get<GetScheduleApiResponse>(`/schedule/soft-sche/${date}`);
}

export async function createSchedule(data: Schedule) {
  return requestClient.post('/schedule/soft-sche', data);
}

export async function getScheduleByDate(date: string) {
  return requestClient.get(`/schedule/soft-sche/date/${date}`);
}

export async function updateSchedule(data: Schedule) {
  return requestClient.put(`/schedule/soft-sche/${data.date}`, data);
}
