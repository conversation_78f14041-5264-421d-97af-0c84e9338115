import { baseRequestClient, requestClient } from '#/api/request';

export namespace AuthApi {
  /** 登录接口参数 */
  export interface LoginParams {
    password?: string;
    username?: string;
  }

  /** 登录接口返回值 */
  export interface LoginResult {
    accessToken: string;
  }

  export interface RefreshTokenResult {
    data: string;
    status: number;
  }

  export interface RegisterParams {
    password?: string;
    username?: string;
  }

  /** 权限更新检查返回结果 */
  export interface CheckPermissionUpdateResult {
    needRefresh: boolean;
  }

  /** 权限更新状态返回结果 */
  export interface CheckPermissionStatusResult {
    needRefresh: boolean;
    lastUpdateTime: number;
  }

  /** 飞书授权URL响应 */
  export interface FeishuAuthURLResponse {
    authUrl: string;
    state: string;
  }

  /** 飞书登录参数 */
  export interface FeishuLoginParams {
    code: string;
    state: string;
  }

  /** 飞书绑定账号参数 */
  export interface FeishuBindParams {
    username: string;
    password: string;
    code: string;
    state: string;
  }
}

/**
 * 登录
 */
export async function loginApi(data: AuthApi.LoginParams) {
  return requestClient.post<AuthApi.LoginResult>('/auth/login', data);
}

/**
 * 注册
 */
export async function registerApi(data: AuthApi.RegisterParams) {
  return requestClient.post('/auth/register', data);
}

/**
 * 刷新accessToken
 */
export async function refreshTokenApi() {
  return baseRequestClient.post<AuthApi.RefreshTokenResult>('/auth/refresh', {
    withCredentials: true,
  });
}

/**
 * 退出登录
 */
export async function logoutApi() {
  return baseRequestClient.post('/auth/logout', {
    withCredentials: true,
  });
}

/**
 * 获取用户权限码
 */
export async function getAccessCodesApi() {
  return requestClient.get<string[]>('/auth/codes');
}

/**
 * 检查权限更新状态（基于Redis标志位）
 * @returns 是否需要刷新权限及最后更新时间
 */
export async function checkPermissionStatusApi() {
  return requestClient.get<AuthApi.CheckPermissionStatusResult>(
    '/auth/check-permission-status',
  );
}

/**
 * 清除权限更新标志
 * 在获取最新权限后调用，通知后端已经完成权限更新
 */
export async function clearPermissionFlagApi() {
  return requestClient.post('/auth/clear-permission-flag');
}

/**
 * 获取飞书授权URL
 */
export async function getFeishuAuthUrlApi() {
  return requestClient.get<AuthApi.FeishuAuthURLResponse>('/auth/feishu/auth-url');
}

/**
 * 飞书登录
 */
export async function feishuLoginApi(data: AuthApi.FeishuLoginParams) {
  return requestClient.post<AuthApi.LoginResult>('/auth/feishu/login', data);
}

/**
 * 绑定飞书账号
 */
export async function bindFeishuAccountApi(data: AuthApi.FeishuBindParams) {
  return requestClient.post('/auth/feishu/bind', data);
}
