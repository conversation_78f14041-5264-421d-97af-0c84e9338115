import { requestClient } from '#/api/request';

// 时间范围参数
export interface TimeRangeParams {
  time_range?: string;
  start_date?: string;
  end_date?: string;
  group_by?: string;
  project?: string;
}

// 品牌分布数据类型
export interface BrandDistributionData {
  brand_counts: Array<{
    brand: string;
    count: number;
  }>;
  total_brands: number;
  chart_data: Array<{
    name: string;
    value: number;
  }>;
  project?: string;
  asset_type: string;
}

// 机房分布数据类型
export interface RoomDistributionData {
  room_counts: Array<{
    count: number;
    data_center_id: number;
    data_center_name: string;
    room_id: number;
    room_name: string;
  }>;
  chart_data: Array<{
    name: string;
    value: number;
  }>;
  data_center_stats: Array<{
    data_center_id: number;
    data_center_name: string;
    rooms: Array<{
      count: number;
      room_id: number;
      room_name: string;
    }>;
    total_count: number;
  }>;
  project?: string;
  asset_type: string;
}

// GPU卡型号分布数据类型
export interface GpuModelDistributionData {
  template_gpu_model_counts: Array<{
    count: number;
    gpu_model: string;
  }>;
  resource_gpu_model_counts: Array<{
    count: number;
    gpu_model: string;
  }>;
  chart_data: Array<{
    name: string;
    value: number;
  }>;
  project_gpu_model_stats: {
    [project: string]: Array<{
      count: number;
      gpu_model: string;
    }>;
  };
  project_gpu_server_counts: Array<{
    count: number;
    project: string;
  }>;
  project?: string;
  asset_type: string;
}

// 获取品牌分布数据
export function getBrandDistribution(params: TimeRangeParams = {}) {
  return requestClient.get<BrandDistributionData>(
    '/dashboard/resources/brand-distribution',
    { params },
  );
}

// 获取机房分布数据
export function getRoomDistribution(params: TimeRangeParams = {}) {
  return requestClient.get<RoomDistributionData>(
    '/dashboard/resources/room-distribution',
    { params },
  );
}

// 获取GPU卡型号分布数据
export function getGpuModelDistribution(params: TimeRangeParams = {}) {
  return requestClient.get<GpuModelDistributionData>(
    '/dashboard/resources/gpu-model-distribution',
    { params },
  );
}
