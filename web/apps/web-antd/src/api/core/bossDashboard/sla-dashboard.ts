import { requestClient } from '#/api/request';

// 标准响应类型
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

// 时间范围
export interface TimeRange {
  start: string;
  end: string;
}

// 服务器统计接口数据类型
export interface ServerStatsData {
  total_count: number;
  active_count: number;
  outage_count: number;
  allocated_count: number;
  unallocated_count: number;
  maintaining_count: number;
  backup_count: number;
  biz_status_stats: Array<{
    count: number;
    status: string;
  }>;
  res_status_stats: Array<{
    count: number;
    status: string;
  }>;
  // 资产类型统计数据
  asset_type_stats: Array<{
    asset_type: string;
    count: number;
  }>;
  asset_type_chart_data: Array<{
    name: string;
    value: number;
  }>;
  // 项目资产类型统计 - 支持两种格式
  project_asset_type_stats:
    | Array<{
        asset_type: string;
        count: number;
        project: string;
      }>
    | {
        [project: string]: {
          [assetType: string]: number;
        };
      };
  project_asset_type_chart_data: {
    [project: string]: Array<{
      name: string;
      value: number;
    }>;
  };
  raw_asset_type_data: Array<{
    asset_type: string;
    count: number;
    project: string;
  }>;
  // 项目及其计数
  project_counts: Array<{
    count: number;
    project: string;
  }>;
  project_status_stats: {
    [project: string]: {
      [status: string]: number;
    };
  };
  unique_project_count: number;
  // 服务器数量快捷方式
  server_count?: number;
  gpu_server_count?: number;
  network_count?: number;
  storage_count?: number;
}

// 集群统计接口数据类型
export interface ClusterStatsData {
  chart_data: Array<{
    name: string;
    value: number;
  }>;
  cluster_counts: Array<{
    cluster: string;
    count: number;
  }>;
  unique_cluster_count: number;
  // 项目集群数量统计
  project_cluster_stats: {
    [project: string]: Array<{
      cluster: string;
      count: number;
    }>;
  };
  project_cluster_counts: {
    [project: string]: number;
  };
  raw_data?: Array<{
    cluster: string;
    count: number;
    project: string;
  }>;
}

// SLA统计接口数据类型
export interface SLAStatsData {
  compliance: number;
  complianceRate: number;
  exempted: number;
  timeRange: TimeRange;
  total: number;
  violated: number;
}

// 工单时间分布接口数据类型
export interface TicketTimeStatsData {
  series: Array<{
    data: number[];
    name: string;
    type: string;
  }>;
  xAxis: string[];
  timeRange: TimeRange;
  total: number;
}

// 故障类型分布接口数据类型
export interface FaultTypeStatsData {
  legend: string[];
  series: Array<{
    data: Array<{
      name: string;
      value: number;
    }>;
    name: string;
    type: string;
  }>;
  timeRange: TimeRange;
  total: number;
}

// 故障来源分布接口数据类型
export interface SourceStatsData {
  legend: string[];
  series: Array<{
    data: Array<{
      name: string;
      value: number;
    }>;
    name: string;
    type: string;
  }>;
  timeRange: TimeRange;
  total: number;
}

// 具体故障类型分布接口数据类型
export interface FaultDetailTypeStatsData {
  legend: string[];
  series: Array<{
    data: Array<{
      name: string;
      value: number;
    }>;
    name: string;
    type: string;
  }>;
  timeRange: TimeRange;
  total: number;
}

// 工单状态分布接口数据类型
export interface StatusStatsData {
  legend: string[];
  series: Array<{
    data: Array<{
      name: string;
      value: number;
    }>;
    name: string;
    type: string;
  }>;
  timeRange: TimeRange;
  total: number;
}

// 处理时长统计接口数据类型
export interface DurationStatsData {
  phases: string[];
  avgTimes: number[];
  maxTimes: number[];
  minTimes: number[];
  timeRange: TimeRange;
}

// 业务影响总时长接口数据类型
export interface BusinessImpactData {
  avg_impact_time: number;
  impact_time_display: string;
  max_impact_time: number;
  min_impact_time: number;
  timeRange: TimeRange;
  total_impact_time: number;
  total_tickets: number;
  type_statistics: Array<{
    time: number;
    type: string;
  }>;
}

// GPU故障比接口数据类型
export interface GPUFaultRatioData {
  project: string;
  gpu_fault_count: number;
  gpu_server_count: number;
  total_gpu_cards: number;
  gpu_fault_ratio: number;
  fault_ratio_percent: number;
  time_range: TimeRange;
}

// 获取项目列表
export interface ProjectOption {
  label: string;
  value: string;
}

export function getProjectList() {
  return requestClient.get<string[]>('/cmdb/resources/projects');
}

// SLA相关接口
export interface SLACalculateData {
  year: number;
  month: number;
  algorithm: string;
  total_minutes: number;
  total_impact_minutes: number;
  excluded_minutes: number;
  excluded_count: number;
  sla_percentage: number;
  sla_tickets_count: number;
  period: {
    end: string;
    start: string;
  };
}

export interface CalculateSLAParams {
  year?: number;
  month?: number;
  algorithm?: string;
  project?: string;
}

export function calculateSLA(params: CalculateSLAParams = {}) {
  return requestClient.get<SLACalculateData>('/dashboard/sla/calculate', {
    params,
  });
}

// 工单统计相关接口
export interface TimeRangeParams {
  time_range?: string;
  start_date?: string;
  end_date?: string;
  group_by?: string;
  project?: string;
  page?: number; // 当前页码
  pageSize?: number; // 每页记录数
  count_in_sla?: boolean; // 是否只统计计入SLA的工单
}

export function getTicketTimeStats(params: TimeRangeParams = {}) {
  return requestClient.get<TicketTimeStatsData>(
    '/dashboard/ticket/time-stats',
    { params },
  );
}

export function getFaultTypeStats(params: TimeRangeParams = {}) {
  return requestClient.get<FaultTypeStatsData>(
    '/dashboard/ticket/fault-types',
    { params },
  );
}

export function getSourceStats(params: TimeRangeParams = {}) {
  return requestClient.get<SourceStatsData>('/dashboard/ticket/source-stats', {
    params,
  });
}

export function getFaultDetailTypeStats(params: TimeRangeParams = {}) {
  return requestClient.get<FaultDetailTypeStatsData>(
    '/dashboard/ticket/fault-detail-types',
    { params },
  );
}

export function getStatusStats(params: TimeRangeParams = {}) {
  return requestClient.get<StatusStatsData>('/dashboard/ticket/status-stats', {
    params,
  });
}

export function getDurationStats(params: TimeRangeParams = {}) {
  return requestClient.get<DurationStatsData>(
    '/dashboard/ticket/duration-stats',
    { params },
  );
}

export function getSLAStats(params: TimeRangeParams = {}) {
  return requestClient.get<SLAStatsData>('/dashboard/ticket/sla-stats', {
    params,
  });
}

export function getBusinessImpactTotalTime(params: TimeRangeParams = {}) {
  return requestClient.get<BusinessImpactData>(
    '/dashboard/ticket/business-impact',
    { params },
  );
}

// 获取GPU卡故障比
export function getGPUFaultRatio(params: TimeRangeParams = {}) {
  return requestClient.get<GPUFaultRatioData>(
    '/dashboard/ticket/gpu-fault-ratio',
    { params },
  );
}

// 故障单详情接口数据类型
export interface FaultTicketDetailData {
  details: Array<{
    business_impact_time: number;
    creation_time: string;
    fault_detail_type: string;
    is_cold_migration: boolean;
    repair_method: string;
    resource_identifier: string;
    ticket_no: string;
  }>;
  total: number;
  timeRange: TimeRange;
  project: string;
}

// 获取故障单详情列表
export function getFaultTicketDetails(params: TimeRangeParams = {}) {
  return requestClient.get<FaultTicketDetailData>(
    '/dashboard/ticket/fault-details',
    { params },
  );
}

// 获取故障总台数
export interface TotalFaultDevicesData {
  total_devices: number;
  total_tickets: number;
  type_stats: {
    data: Array<{ name: string; value: number }>;
    types: string[];
  };
  timeRange: TimeRange;
  project?: string;
}

// 获取故障总台数
export function getTotalFaultDevices(params: TimeRangeParams = {}) {
  return requestClient.get<TotalFaultDevicesData>(
    '/dashboard/ticket/total-devices',
    { params },
  );
}

// 资源统计相关接口
export function getServerStats(params?: TimeRangeParams) {
  return requestClient.get<ServerStatsData>(
    '/dashboard/resources/server-stats-by-project',
    { params },
  );
}

export function getClusterStats(params?: TimeRangeParams) {
  return requestClient.get<ClusterStatsData>(
    '/dashboard/resources/cluster-stats-by-project',
    { params },
  );
}

// 每日SLA数据接口类型
export interface DailySLAData {
  year: number;
  month: number;
  project: string;
  gpu_server_count: number;
  daily_data: {
    [date: string]: {
      [algorithm: string]: {
        ExcludedCount: number;
        ExcludedMinutes: number;
        SLAPercentage: number;
      };
    };
  };
  period: {
    end: string;
    start: string;
  };
  chart_data: {
    dates: string[];
    sla_90: number[];
    sla_95: number[];
    sla_100: number[];
  };
}

// 获取每日SLA数据
export function getDailySLA(params: CalculateSLAParams = {}) {
  return requestClient.get<DailySLAData>('/dashboard/sla/daily', {
    params,
  });
}

// 工程师响应和修复时长统计数据类型
export interface EngineerStatsData {
  engineers: string[];
  project: string;
  stats: Array<{
    avg_fix_time: number;
    avg_response_time: number;
    engineer_name: string;
    ticket_count: number;
    ticket_type: string;
  }>;
}

// 获取工程师响应和修复时长统计
export function getEngineerStats(params: TimeRangeParams = {}) {
  return requestClient.get<EngineerStatsData>(
    '/dashboard/ticket/engineer-stats',
    { params },
  );
}

// 故障设备厂商分布接口数据类型
export interface FaultDeviceBrandDistributionData {
  legend: string[];
  series: Array<{
    data: Array<{
      name: string;
      value: number;
    }>;
    name: string;
    type: string;
  }>;
  timeRange: TimeRange;
  total_devices: number;
}

// 获取故障设备厂商分布
export function getFaultDeviceBrandDistribution(params: TimeRangeParams = {}) {
  return requestClient.get<FaultDeviceBrandDistributionData>(
    '/dashboard/ticket/brand-distribution',
    { params },
  );
}

// 每天故障处理总时长趋势统计接口数据类型
export interface DailyImpactTrendData {
  impact_time_display: number;
  series: Array<{
    data: number[];
    name: string;
    type: string;
    yAxisIndex?: number;
  }>;
  timeRange: TimeRange;
  total_impact_time: number;
  total_tickets: number;
  xAxis: string[];
}

// 获取每天故障处理总时长趋势统计
export function getDailyImpactTrend(params: TimeRangeParams = {}) {
  return requestClient.get<DailyImpactTrendData>(
    '/dashboard/ticket/daily-impact-trend',
    { params },
  );
}

// 每天硬件故障次数趋势统计接口数据类型
export interface HardwareFaultTrendData {
  series: Array<{
    data: number[];
    name: string;
    type: string;
  }>;
  timeRange: TimeRange;
  total_tickets: number;
  xAxis: string[];
}

// 获取每天硬件故障次数趋势统计
export function getHardwareFaultTrend(params: TimeRangeParams = {}) {
  return requestClient.get<HardwareFaultTrendData>(
    '/dashboard/ticket/hardware-fault-trend',
    { params },
  );
}

// 资源类型统计
export interface ResourceType {
  name: string;
  count: number;
  percent: number;
}

// 资源变更统计数据
export interface ResourceChangesData {
  added: {
    total: number;
    trend: number;
    types: ResourceType[];
  };
  removed: {
    total: number;
    trend: number;
    types: ResourceType[];
  };
  timeRange?: {
    end: string;
    start: string;
  };
}

// 获取资源变更统计
export function getResourceChangesStats(params: TimeRangeParams = {}) {
  return requestClient.get<ResourceChangesData>(
    '/dashboard/ticket/resource-changes',
    { params },
  );
}
