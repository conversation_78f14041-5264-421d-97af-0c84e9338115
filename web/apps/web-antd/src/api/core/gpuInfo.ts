import { requestClient } from '#/api/request';

/** 获取 GPU 信息表格数据接口参数 */
export interface GetGpuInfoTableApiParams {
  page: number;
  pageSize: number;
  [key: string]: any;
}

// 定义 API 返回的单条 GPU 服务器记录（根据实际字段调整）
// 根据提供的返回数据更新 GPU 服务器记录接口字段
export interface GpuServer {
  ID: number;
  project: string;
  data_center: string;
  package: string;
  cabinet: string;
  hostname: string;
  sn: string;
  primary_owner: string;
  backup_owner: string;
  vpc_ip: string;
  vpc_mask: string;
  bmc_ip: string;
  bmc_mask: string;
  bmc_gateway: string;
  cpu_type: string;
  vpc_eth0_mac: string;
  bmc_mac: string;
  device_type: string;
  height: number;
  gpu_model: string;
  cluster: string;
  manufacturer: string;
  machine_model: string;
  business_status: string;
  asset_status: string;
  is_backup: boolean;
  status_change_record: string;
}

// 定义 API 返回的整体数据格式
export interface GetGpuInfoTableApiResponse {
  items: GpuServer[];
  total: number;
}

/**
 * 获取 GPU 信息表格数据
 */
export async function getGpuInfoTableApi(params: GetGpuInfoTableApiParams) {
  return requestClient.get<GetGpuInfoTableApiResponse>('/cmdb/gpuServer', {
    params,
  });
}

/**
 * 新增 GPU 服务器
 */
export async function addGpuServerApi(data: GpuServer) {
  return requestClient.post('/cmdb/gpuServer', data);
}

/**
 * 编辑 GPU 服务器
 */
export async function editGpuServerApi(data: GpuServer) {
  return requestClient.put(`/cmdb/gpuServer/${data.ID}`, data);
}
