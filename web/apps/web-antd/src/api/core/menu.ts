import type { RouteRecordStringComponent } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * 菜单类型
 */
export enum MenuType {
  BUTTON = 'button', // 按钮
  DIRECTORY = 'catalog', // 目录
  EXTERNAL = 'external', // 外链
  IFRAME = 'iframe', // 内嵌
  MENU = 'menu', // 菜单
}

/**
 * 菜单元数据接口，与后端MenuMeta结构体对应
 */
export interface MenuMeta {
  title: string; // 标题
  icon?: string; // 图标
  activeIcon?: string; // 激活状态图标
  activePath?: string; // 激活路径
  affixTab?: boolean; // 是否固定标签
  affixTabOrder?: number; // 固定标签顺序
  badge?: string; // 徽标内容
  badgeType?: string; // 徽标类型
  badgeVariants?: string; // 徽标颜色
  hideChildrenInMenu?: boolean; // 是否隐藏子菜单
  hideInBreadcrumb?: boolean; // 是否在面包屑中隐藏
  hideInMenu?: boolean; // 是否在菜单中隐藏
  hideInTab?: boolean; // 是否在标签页中隐藏
  iframeSrc?: string; // iframe源
  ignoreAccess?: boolean; // 是否忽略权限
  keepAlive?: boolean; // 是否保持组件状态
  link?: string; // 链接
  menuVisibleWithForbidden?: boolean; // 菜单在没有权限时是否可见
  noBasicLayout?: boolean; // 是否无基础布局
  openInNewWindow?: boolean; // 是否在新窗口打开
  order?: number; // 排序
  [key: string]: any; // 其他自定义属性
}

/**
 * 菜单项接口
 */
export interface MenuItem {
  id?: number; // 菜单ID
  name: string; // 菜单名称
  type: string; // 菜单类型
  path: string; // 路由地址
  component?: string; // 页面组件
  redirect?: string; // 重定向地址
  status: boolean; // 状态
  pid?: number; // 父级ID
  order?: number; // 排序
  authCode?: string; // 权限编码
  createTime?: string; // 创建时间
  updateTime?: string; // 更新时间
  children?: MenuItem[]; // 子菜单
  meta: MenuMeta; // 菜单元数据
}

/**
 * 创建菜单请求
 */
export interface MenuCreateRequest {
  name: string; // 菜单名称
  type: string; // 菜单类型（使用MenuType枚举）
  path: string; // 路由地址
  component?: string; // 页面组件路径
  redirect?: string; // 重定向地址
  pid?: number; // 父级菜单ID
  status: boolean; // 状态
  order?: number; // 排序
  authCode?: string; // 权限编码
  meta: MenuMeta; // 菜单元数据
}

/**
 * 更新菜单请求
 */
export interface MenuUpdateRequest extends MenuCreateRequest {
  id: number; // 菜单ID
}
/**
 * 获取用户所有菜单
 */
export async function getAllMenusApi() {
  return requestClient.get<RouteRecordStringComponent[]>('/system/menu/all');
}

/**
 * 获取菜单管理列表
 */
export async function getMenuListApi() {
  return requestClient.get<MenuItem[]>('/system/menu/list');
}

/**
 * 获取单个菜单详情
 */
export async function getMenuByIdApi(id: number) {
  return requestClient.get<MenuItem>(`/system/menu/${id}`);
}

/**
 * 创建菜单
 */
export async function createMenuApi(data: MenuCreateRequest) {
  return requestClient.post('/system/menu', data);
}

/**
 * 更新菜单
 */
export async function updateMenuApi(data: MenuUpdateRequest) {
  return requestClient.put('/system/menu', data);
}

/**
 * 删除菜单
 */
export async function deleteMenuApi(id: number) {
  return requestClient.delete(`/system/menu/${id}`);
}
