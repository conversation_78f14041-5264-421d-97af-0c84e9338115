import { requestClient } from '#/api/request';

// 付款申请明细接口
export interface PaymentRequestItem {
  id?: number;
  payment_request_id?: number;
  contract_item_id: number;
  contract_quantity: number;
  contract_amount: number;
  paid_quantity: number;
  paid_amount: number;
  unpaid_quantity: number;
  unpaid_amount: number;
  current_payment_quantity: number;
  current_payment_amount: number;
  remark?: string;
  created_at?: string;
  updated_at?: string;
  contract_item?: any; // 关联的合同明细
}

// 创建付款申请明细参数
export interface CreatePaymentRequestItemParams {
  contract_item_id: number;
  // paid_quantity?: number;
  // paid_amount?: number;
  // unpaid_quantity?: number;
  // unpaid_amount?: number;
  current_payment_quantity: number;
  current_payment_amount: number;
  remark?: string;
}

// 更新付款申请明细参数
export interface UpdatePaymentRequestItemParams {
  id?: number;
  contract_item_id: number;
  paid_quantity?: number;
  paid_amount?: number;
  unpaid_quantity?: number;
  unpaid_amount?: number;
  current_payment_quantity?: number;
  current_payment_amount?: number;
  remark?: string;
}

// 付款申请基础接口
export interface PaymentRequest {
  id: number;
  payment_no: string;
  contract_id: number;
  contract_no?: string;
  supplier_id: number;
  supplier_name?: string;
  our_company_name?: string;
  payment_type:
    | 'acceptance_delivery'
    | 'account_period_due'
    | 'advance'
    | 'after_acceptance'
    | 'after_delivery_acceptance'
    | 'cash_on_delivery';

  contract_total_amount: number;
  paid_amount: number;
  unpaid_amount: number;
  current_payment_amount: number;
  payment_reason: string;
  cumulative_paid_after_payment: number;
  remaining_unpaid_after_payment: number;
  bank_name: string;
  bank_account: string;
  remark?: string;
  status:
    | 'cancelled'
    | 'company_entity_judgment_1'
    | 'company_entity_judgment_2'
    | 'completed'
    | 'cyber_payment_handler'
    | 'cyber_payment_review'
    | 'cyber_review_2'
    | 'draft'
    | 'enterprise_manager_review'
    | 'final_review'
    | 'finance_manager_review'
    | 'fund_manager_review'
    | 'other_entity_payment_handler'
    | 'other_entity_payment_review'
    | 'other_entity_specialist_review'
    | 'purchase_review'
    | 'rejected';
  created_by?: number;
  creator_name?: string;
  created_at: string;
  updated_by?: number;
  updated_at?: string;
  items?: PaymentRequestItem[];

  // 关联对象
  contract?: {
    contract_no: string;
    contract_title?: string;
    contract_type?: string;
    id: number;
    supplier_name?: string;
    total_amount?: number;
  };
  supplier?: {
    address?: string;
    bank_account?: string;
    bank_name?: string;
    business_license?: string;
    contact_email?: string;
    contact_person?: string;
    contact_phone?: string;
    created_at?: string;
    credit_level?: number;
    id: number;
    remark?: string;
    short_name?: string;
    status?: number;
    supplier_code?: string;
    supplier_name: string;
    tax_number?: string;
  };
  company?: {
    company_name: string;
    contact_person?: string;
    contact_phone?: string;
    id: number;
    registered_address?: string;
  };
}

// 创建付款申请参数
export interface CreatePaymentRequestParams {
  contract_id: number;
  supplier_id: number;
  payment_type:
    | 'acceptance_delivery'
    | 'account_period_due'
    | 'advance'
    | 'after_acceptance'
    | 'after_delivery_acceptance'
    | 'cash_on_delivery';

  current_payment_amount: number;
  payment_reason: string;

  remark?: string;
  items: CreatePaymentRequestItemParams[];
}

// 更新付款申请参数
export interface UpdatePaymentRequestParams {
  id: number;
  payment_type?:
    | 'acceptance_delivery'
    | 'account_period_due'
    | 'advance'
    | 'after_acceptance'
    | 'after_delivery_acceptance'
    | 'cash_on_delivery';

  contract_total_amount?: number;
  paid_amount?: number;
  unpaid_amount?: number;
  current_payment_amount?: number;
  payment_reason?: string;
  cumulative_paid_after_payment?: number;
  remaining_unpaid_after_payment?: number;
  bank_name?: string;
  bank_account?: string;
  remark?: string;
  items?: UpdatePaymentRequestItemParams[];
}

// 付款申请查询参数
export interface PaymentRequestQuery {
  page: number;
  pageSize: number;
  payment_no?: string;
  contract_id?: number;
  supplier_id?: number;
  payment_type?: string;
  status?: string;
  created_by?: number;
  start_date?: string;
  end_date?: string;
  min_amount?: number;
  max_amount?: number;
}

// 付款申请列表响应
export interface PaymentRequestListResult {
  list: PaymentRequest[];
  total: number;
}

// 分页参数
export interface PageParams {
  currentPage: number;
  pageSize: number;
}

// 付款申请审批参数
export interface PaymentApprovalParams {
  payment_id: number;
  action: 'approve' | 'reject';
  comments: string;
  stage: string;
}

// 付款申请回退参数
export interface PaymentRequestRollbackParams {
  payment_id: number;
  rollback_to: string;
  comments: string;
}

/**
 * 创建付款申请
 */
export function createPaymentRequest(data: CreatePaymentRequestParams) {
  return requestClient.post<PaymentRequest>('/purchase/payment-requests', data);
}

/**
 * 获取付款申请列表
 */
export function getPaymentRequestList(params: PaymentRequestQuery) {
  return requestClient.get<PaymentRequestListResult>(
    '/purchase/payment-requests',
    {
      params,
    },
  );
}

/**
 * 获取付款申请详情
 */
export function getPaymentRequestById(id: number) {
  return requestClient.get<PaymentRequest>(`/purchase/payment-requests/${id}`);
}

/**
 * 更新付款申请
 */
export function updatePaymentRequest(
  id: number,
  data: UpdatePaymentRequestParams,
) {
  return requestClient.put<PaymentRequest>(
    `/purchase/payment-requests/${id}`,
    data,
  );
}

/**
 * 提交付款申请（草稿 -> 待审批）
 */
export function submitPaymentRequest(id: number, comments?: string) {
  return requestClient.post(`/purchase/payment-requests/${id}/submit`, {
    comments: comments || '提交付款申请',
  });
}

/**
 * 取消付款申请
 */
export function cancelPaymentRequest(id: number) {
  return requestClient.post(`/purchase/payment-requests/${id}/cancel`);
}

/**
 * 审批付款申请
 */
export function approvePaymentRequest(data: PaymentApprovalParams) {
  return requestClient.post('/purchase/payment-requests/approve', data);
}

/**
 * 回退付款申请
 */
export function rollbackPaymentRequest(data: PaymentRequestRollbackParams) {
  return requestClient.post('/purchase/payment-requests/rollback', data);
}

/**
 * 根据合同ID获取付款申请列表
 */
export function getPaymentRequestsByContract(contractId: number) {
  return requestClient.get<PaymentRequest[]>(
    `/purchase/payment-requests/by-contract/${contractId}`,
  );
}

/**
 * 根据付款申请单号获取付款申请
 */
export function getPaymentRequestByNo(paymentNo: string) {
  return requestClient.get<PaymentRequest>(
    `/purchase/payment-requests/by-no/${paymentNo}`,
  );
}

// 付款申请历史记录接口
export interface PaymentRequestHistory {
  id: number;
  business_type: string;
  business_id: number;
  previous_status: string;
  new_status: string;
  action: string;
  operator_id: number;
  operator_name: string;
  operation_time: string;
  comments: string;
  created_at: string;
  previous_status_display: string;
  new_status_display: string;
  action_display: string;
}

/**
 * 获取付款申请历史记录
 */
export function getPaymentRequestHistory(id: number) {
  return requestClient.get<PaymentRequestHistory[]>(
    `/purchase/payment-requests/${id}/history`,
  );
}
