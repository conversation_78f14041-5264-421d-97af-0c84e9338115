import type {
  PurchaseOrderReq,
  PurchaseTemplateReq,
  PurchaseTemplateRes,
  UploadPurchaseOrderReq,
} from './types';

import { requestClient } from '#/api/request';

import { commonUploadFile } from '../upload';

/** 创建采购单 */
export function createPurchaseOrderApi(data: PurchaseOrderReq) {
  return requestClient.post('/purchase_old/orders', data);
}

/** 获取模版 */
export function getExportTemplateApi(params: PurchaseTemplateReq) {
  return requestClient.get<PurchaseTemplateRes>('/export/template', {
    params,
  });
}

export function uploadPurchaseOrderApi(data: UploadPurchaseOrderReq) {
  const formData = new FormData();
  Object.entries(data).forEach(([key, value]) => {
    formData.append(key, value);
  });

  return commonUploadFile('/purchase_old/orders/import', formData);
}
