import { requestClient } from '#/api/request';

// 供应商信息接口定义
export interface Supplier {
  id: number;
  supplier_code: string;
  supplier_name: string;
  short_name?: string;
  contact_person?: string;
  contact_phone?: string;
  contact_email?: string;
  address?: string;
  tax_number?: string;
  bank_name?: string;
  bank_account?: string;
  business_license?: string;
  categories?: any;
  contract_start_date?: string;
  contract_end_date?: string;
  credit_level?: number;
  status: number;
  remark?: string;
  created_by?: number;
  created_at: string;
  updated_by?: number;
  updated_at: string;
  deleted_at?: null | string;
}

// 供应商查询参数接口
export interface SupplierQuery {
  page: number;
  pageSize: number;
  supplier_code?: string;
  supplier_name?: string;
  short_name?: string;
  contact_person?: string;
  contact_phone?: string;
  status?: number;
  contract_date_begin?: string;
  contract_date_end?: string;
}

// 供应商创建参数接口
export interface SupplierCreateParams {
  supplier_code?: string; // 可选，不提供则自动生成
  supplier_name: string;
  short_name?: string;
  contact_person?: string;
  contact_phone?: string;
  contact_email?: string;
  address?: string;
  tax_number?: string;
  bank_name?: string;
  bank_account?: string;
  business_license?: string;
  categories?: any;
  contract_start_date?: string;
  contract_end_date?: string;
  credit_level?: number;
  status?: number;
  remark?: string;
  created_by?: number;
}

// 供应商更新参数接口
export interface SupplierUpdateParams {
  supplier_name?: string;
  short_name?: string;
  contact_person?: string;
  contact_phone?: string;
  contact_email?: string;
  address?: string;
  tax_number?: string;
  bank_name?: string;
  bank_account?: string;
  business_license?: string;
  categories?: any;
  contract_start_date?: string;
  contract_end_date?: string;
  credit_level?: number;
  status?: number;
  remark?: string;
  updated_by?: number;
}

// 供应商列表查询响应
export interface SupplierListResponse {
  total: number;
  list: Supplier[];
}

/**
 * 获取供应商列表
 * @param params 查询参数
 */
export function getSupplierListApi(params: SupplierQuery) {
  return requestClient.get<SupplierListResponse>('/purchase/suppliers', {
    params,
  });
}

/**
 * 获取供应商详情
 * @param id 供应商ID
 */
export function getSupplierDetailApi(id: number) {
  return requestClient.get<Supplier>(`/purchase/suppliers/${id}`);
}

/**
 * 获取供应商详情(通过供应商编号)
 * @param code 供应商编号
 */
export function getSupplierByCodeApi(code: string) {
  return requestClient.get<Supplier>(`/purchase/suppliers/code/${code}`);
}

/**
 * 创建供应商
 * @param data 供应商数据
 */
export function createSupplierApi(data: SupplierCreateParams) {
  return requestClient.post('/purchase/suppliers', data);
}

/**
 * 更新供应商
 * @param id 供应商ID
 * @param data 供应商数据
 */
export function updateSupplierApi(id: number, data: SupplierUpdateParams) {
  return requestClient.put(`/purchase/suppliers/${id}`, { ...data, id });
}

/**
 * 删除供应商
 * @param id 供应商ID
 */
export function deleteSupplierApi(id: number) {
  return requestClient.delete(`/purchase/suppliers/${id}`);
}
