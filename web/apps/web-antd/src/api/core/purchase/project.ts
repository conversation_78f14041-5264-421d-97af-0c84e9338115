import { requestClient } from '#/api/request';

// 项目信息接口定义
export interface Project {
  id: number;
  project_code: string;
  project_name: string;
  customer_name: string;
  contract_info?: string;
  project_manager?: string;
  project_address?: string;
  contact_person?: string;
  contact_phone?: string;
  contact_email?: string;
  project_status: number;
  start_date?: string;
  end_date?: string;
  description?: string;
  created_by?: number;
  created_at: string;
  updated_by?: number;
  updated_at: string;
  deleted_at?: null | string;
}

// 项目查询参数接口
export interface ProjectQuery {
  page: number;
  pageSize: number;
  project_code?: string;
  project_name?: string;
  customer_name?: string;
  project_manager?: string;
  contact_person?: string;
  project_status?: number;
  start_date_begin?: string;
  start_date_end?: string;
}

// 项目创建参数接口
export interface ProjectCreateParams {
  project_code?: string; // 可选，不提供则自动生成
  project_name: string;
  customer_name: string;
  contract_info?: string;
  project_manager?: string;
  project_address?: string;
  contact_person?: string;
  contact_phone?: string;
  contact_email?: string;
  project_status?: number;
  start_date?: string;
  end_date?: string;
  description?: string;
  created_by?: number;
}

// 项目更新参数接口
export interface ProjectUpdateParams {
  project_name?: string;
  customer_name?: string;
  contract_info?: string;
  project_manager?: string;
  project_address?: string;
  contact_person?: string;
  contact_phone?: string;
  contact_email?: string;
  project_status?: number;
  start_date?: string;
  end_date?: string;
  description?: string;
  updated_by?: number;
}

// 项目列表查询响应
export interface ProjectListResponse {
  total: number;
  list: Project[];
}

/**
 * 获取项目列表
 * @param params 查询参数
 */
export function getProjectListApi(params: ProjectQuery) {
  return requestClient.get<ProjectListResponse>('/purchase/projects', {
    params,
  });
}

/**
 * 获取项目详情
 * @param id 项目ID
 */
export function getProjectDetailApi(id: number) {
  return requestClient.get<Project>(`/purchase/projects/${id}`);
}

/**
 * 获取项目详情(通过项目编号)
 * @param code 项目编号
 */
export function getProjectByCodeApi(code: string) {
  return requestClient.get<Project>(`/purchase/projects/code/${code}`);
}

/**
 * 创建项目
 * @param data 项目数据
 */
export function createProjectApi(data: ProjectCreateParams) {
  return requestClient.post('/purchase/projects', data);
}

/**
 * 更新项目
 * @param id 项目ID
 * @param data 项目数据
 */
export function updateProjectApi(id: number, data: ProjectUpdateParams) {
  return requestClient.put(`/purchase/projects/${id}`, { ...data, id });
}

/**
 * 删除项目
 * @param id 项目ID
 */
export function deleteProjectApi(id: number) {
  return requestClient.delete(`/purchase/projects/${id}`);
}
