import { requestClient } from '#/api/request';

// 采购询价明细（新的简化结构）
export interface PurchaseInquiryItem {
  id?: number;
  inquiry_id?: number;
  request_item_id: number;
  material_type?: string;
  model?: string;
  brand?: string;
  pn?: string;
  spec?: string;
  unit?: string;
  current_inquiry_quantity: number; // 本次询价数量
  inquired_quantity?: number; // 已询价数量
  budget_price?: number;
  budget_amount?: number;
  quoted_price?: number;
  quoted_amount?: number;
  is_quoted?: boolean;
  quoted_at?: string;
  remark?: string;
  created_at?: string;
  updated_at?: string;

  // 关联的申请明细信息
  request_item?: {
    brand?: string;
    id: number;
    material_type?: string;
    model?: string;
    pn?: string;
    quantity: number; // 需求总数量
    spec?: string;
    unit?: string;
  };
}

// 创建询价明细参数
export interface CreateInquiryItemParams {
  request_item_id: number;
  current_inquiry_quantity: number;
  pn?: string;
  budget_price?: number;
  remark?: string;
}

// 更新询价明细参数
export interface UpdateInquiryItemParams {
  id?: number;
  request_item_id: number;
  current_inquiry_quantity: number;
  pn?: string;
  budget_price?: number;
  budget_amount?: number;
  inquired_quantity?: number;
  remark?: string;
}

// 供应商报价明细参数
export interface SupplierQuoteItemParams {
  inquiry_item_id: number;
  pn?: string;
  quoted_price?: number;
  quoted_amount?: number;
  remark?: string;
}

// 询价物料统计
export interface InquiryItemStatistics {
  request_item_id: number;
  material_type: string;
  total_quantity: number;
  inquired_quantity: number;
  uninquired_quantity: number;
  inquiry_progress: number;
  inquiry_count: number;
}

// 询价供应商统计
export interface InquirySupplierStatistics {
  supplier_id: number;
  supplier_name: string;
  inquiry_count: number;
  quoted_count: number;
  pending_count: number;
  quote_rate: number;
}

// 定义采购询价的基础类型
export interface PurchaseInquiry {
  id?: number;
  inquiry_no?: string;
  request_id?: number;
  project_id?: number;
  supplier_id: number;
  total_amount: number;
  supplier_description?: string;
  status?: string;

  created_by?: number;
  created_at?: string;
  updated_by?: number;
  updated_at?: string;

  // 关联数据
  project_name?: string;
  supplier_name?: string;
  supplier_contact?: string;
  request_no?: string;
  creator_name?: string;
  items?: PurchaseInquiryItem[];
  purchase_request?: {
    project_name?: string;
    request_no?: string;
  };
  supplier?: {
    contact_person?: string;
    supplier_name?: string;
  };
}

// 查询参数接口
export interface PurchaseInquiryQuery {
  page: number;
  pageSize: number;
  inquiry_no?: string;
  request_id?: number;
  request_no?: string;
  project_id?: number;
  supplier_id?: number;
  status?: string;

  created_by?: number;
  start_date?: string;
  end_date?: string;
}

// 创建询价参数
export interface CreatePurchaseInquiryParams {
  request_id?: number;
  project_id?: number;
  supplier_id: number;
  supplier_description?: string;
  request_items: CreateInquiryItemParams[];
  created_by?: number;
}

// 更新询价参数
export interface UpdatePurchaseInquiryParams {
  id: number;
  request_id?: number;
  project_id?: number;
  supplier_id: number;
  supplier_description?: string;
  items?: UpdateInquiryItemParams[];
  updated_by?: number;
}

// 添加询价明细参数
export interface AddInquiryItemsParams {
  inquiry_id: number;
  items: CreateInquiryItemParams[];
  updated_by?: number;
}

// 供应商报价参数
export interface SupplierQuoteParams {
  inquiry_id: number;
  quote_items: SupplierQuoteItemParams[];
  updated_by?: number;
}

// 完成询价参数
export interface CompleteInquiryParams {
  inquiry_id: number;
  updated_by?: number;
}

// 审批参数
export interface InquiryApprovalParams {
  inquiry_id: number;
  action: 'approve' | 'reject';
  comments: string;
  current_stage: string; // 当前审批阶段
}

// 回退参数
export interface InquiryRollbackParams {
  inquiry_id: number;
  rollback_to: string;
  current_stage: string;
  comments: string;
}

// 审批历史记录
export interface InquiryApprovalHistory {
  id: number;
  business_type: string;
  business_id: number;
  previous_status: string;
  new_status: string;
  action: string;
  operator_id: number;
  operator_name: string;
  operation_time: string;
  comments: string;
  created_at: string;
}

// 询价统计信息
export interface InquiryStatistics {
  request_id: number;
  total_inquiries: number;
  completed_inquiries: number;
  pending_inquiries: number;
  item_statistics: InquiryItemStatistics[];
  supplier_statistics: InquirySupplierStatistics[];
}

// 询价数量验证参数
export interface InquiryQuantityValidationParams {
  request_item_id: number;
  inquiry_quantity: number;
}

// 请求明细询价状态
export interface RequestItemInquiryStatus {
  request_item_id: number;
  material_type: string;
  model: string;
  brand: string;
  pn: string;
  specifications: string;
  unit: string;
  total_quantity: number;
  inquired_quantity: number;
  uninquired_quantity: number;
  inquiry_progress: number;
}

// 请求明细询价状态结果
export interface RequestItemsInquiryStatusResult {
  request_id: number;
  request_no: string;
  items: RequestItemInquiryStatus[];
}

// 询价详情响应
export interface PurchaseInquiryDetailResponse {
  inquiry: PurchaseInquiry;
}

// 获取询价列表
export function getPurchaseInquiryList(params: PurchaseInquiryQuery) {
  return requestClient.get<{ list: PurchaseInquiry[]; total: number }>(
    '/purchase/inquiries',
    { params },
  );
}

// 获取询价详情
export function getPurchaseInquiryById(id: number) {
  return requestClient.get<PurchaseInquiry>(`/purchase/inquiries/${id}`);
}

// 创建询价
export function createPurchaseInquiry(data: CreatePurchaseInquiryParams) {
  return requestClient.post<PurchaseInquiry>('/purchase/inquiries', data);
}

// 更新询价
export function updatePurchaseInquiry(
  id: number,
  data: UpdatePurchaseInquiryParams,
) {
  return requestClient.put<PurchaseInquiry>(`/purchase/inquiries/${id}`, data);
}

// 添加询价明细
export function addInquiryItems(data: AddInquiryItemsParams) {
  return requestClient.post('/purchase/inquiries/items', data);
}

// 提交供应商报价
export function submitSupplierQuote(data: SupplierQuoteParams) {
  return requestClient.post('/purchase/inquiries/quote', data);
}

// 完成询价
export function completeInquiry(data: CompleteInquiryParams) {
  return requestClient.post('/purchase/inquiries/complete', data);
}

// 审批询价
export function approvePurchaseInquiry(data: InquiryApprovalParams) {
  return requestClient.post('/purchase/inquiries/approve', data);
}

// 回退询价
export function rollbackPurchaseInquiry(data: InquiryRollbackParams) {
  return requestClient.post('/purchase/inquiries/rollback', data);
}

// 获取询价历史记录
export function getPurchaseInquiryHistory(id: number) {
  return requestClient.get<InquiryApprovalHistory[]>(
    `/purchase/inquiries/${id}/history`,
  );
}

// 取消询价
export function cancelPurchaseInquiry(id: number) {
  return requestClient.post(`/purchase/inquiries/${id}/cancel`);
}

// 删除询价
export function deletePurchaseInquiry(id: number) {
  return requestClient.delete(`/purchase/inquiries/${id}`);
}

// 获取询价统计信息
export function getInquiryStatistics(requestId: number) {
  return requestClient.get<InquiryStatistics>(
    `/purchase/inquiries/statistics/${requestId}`,
  );
}

// 验证询价数量
export function validateInquiryQuantity(data: InquiryQuantityValidationParams) {
  return requestClient.post('/purchase/inquiries/validate-quantity', data);
}

// 获取采购申请的询价状态
export function getRequestInquiryStatus(requestId: number) {
  return requestClient.get<{
    completed_inquiries: number;
    items_summary: Array<{
      can_create_inquiry: boolean;
      completed_quantity: number;
      remaining_quantity: number;
      request_item_id: number;
      request_quantity: number;
    }>;
    pending_inquiries: number;
    request_id: number;
    total_inquiries: number;
  }>(`/purchase/inquiries/status/${requestId}`);
}

// 获取采购申请明细询价状态
export function getRequestItemsInquiryStatus(requestId: number) {
  return requestClient.get<RequestItemsInquiryStatusResult>(
    `/purchase/inquiries/request/${requestId}/items-status`,
  );
}

// 定义供表单使用的接口类型
export interface PurchaseInquiryFormData extends PurchaseInquiry {
  inquiry_type?: string;
  inquiry_reason?: string;
  expected_response_date?: string;
  suppliers?: Array<{
    contact_email?: string;
    contact_person?: string;
    contact_phone?: string;
    id?: number;
    inquiry_file?: string;
    response_status?: string;
    supplier_description?: string;
    supplier_id: number;
    supplier_name?: string;
  }>;
}

// 表单类型
export interface SupplierFormData {
  supplier_id: number;
  supplier_description?: string;
  inquiry_file?: string;
}

export interface InquiryItemFormData {
  current_inquiry_quantity: number;
  pn?: string;
  budget_price?: number;
  remark?: string;
}

// 扩展 CreatePurchaseInquiryParams 类型
export interface CreatePurchaseInquiryWithReason
  extends CreatePurchaseInquiryParams {
  expected_response_date?: string;
  inquiry_reason?: string;
}
