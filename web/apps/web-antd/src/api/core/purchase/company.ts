import { requestClient } from '#/api/request';

/**
 * Company 公司类型接口定义
 */
export interface Company {
  id: number;
  company_code: string;
  company_name: string;
  short_name?: string;
  legal_person?: string;
  unified_credit_code?: string;
  tax_number?: string;
  registered_address?: string;
  business_address?: string;
  bank_name?: string;
  bank_account?: string;
  contact_person?: string;
  contact_phone?: string;
  invoice_title?: string;
  invoice_address?: string;
  status: number;
  created_by?: number;
  created_at?: string;
  updated_by?: number;
  updated_at?: string;
  deleted_at?: null | string;
}

/**
 * CompanyQuery 公司查询参数接口
 */
export interface CompanyQuery {
  page: number;
  pageSize: number;
  company_code?: string;
  company_name?: string;
  short_name?: string;
  contact_person?: string;
  contact_phone?: string;
  status?: number;
}

/**
 * CompanyListResult 公司列表查询结果接口
 */
export interface CompanyListResult {
  total: number;
  list: Company[];
}

/**
 * CreateCompanyData 创建公司参数接口
 */
export interface CreateCompanyData {
  company_code?: string; // 可选，不提供则自动生成
  company_name: string;
  short_name?: string;
  legal_person?: string;
  unified_credit_code?: string;
  tax_number?: string;
  registered_address?: string;
  business_address?: string;
  bank_name?: string;
  bank_account?: string;
  contact_person?: string;
  contact_phone?: string;
  invoice_title?: string;
  invoice_address?: string;
  status?: number;
  created_by?: number;
}

/**
 * UpdateCompanyData 更新公司参数接口
 */
export interface UpdateCompanyData {
  company_name?: string;
  short_name?: string;
  legal_person?: string;
  unified_credit_code?: string;
  tax_number?: string;
  registered_address?: string;
  business_address?: string;
  bank_name?: string;
  bank_account?: string;
  contact_person?: string;
  contact_phone?: string;
  invoice_title?: string;
  invoice_address?: string;
  status?: number;
  updated_by?: number;
}

const BASE_URL = '/purchase/companies';

/**
 * 获取公司列表
 * @param params 查询参数
 * @returns 返回公司列表和总数
 */
export function getCompanyListApi(params: CompanyQuery) {
  return requestClient.get<CompanyListResult>(BASE_URL, { params });
}

/**
 * 获取公司详情
 * @param id 公司ID
 * @returns 返回公司详细信息
 */
export function getCompanyDetailApi(id: number) {
  return requestClient.get<Company>(`${BASE_URL}/${id}`);
}

/**
 * 创建公司
 * @param data 公司数据
 * @returns 返回创建的公司信息
 */
export function createCompanyApi(data: CreateCompanyData) {
  return requestClient.post<Company>(BASE_URL, data);
}

/**
 * 更新公司
 * @param id 公司ID
 * @param data 公司数据
 * @returns 返回更新后的公司信息
 */
export function updateCompanyApi(id: number, data: UpdateCompanyData) {
  return requestClient.put<Company>(`${BASE_URL}/${id}`, { ...data, id });
}

/**
 * 删除公司
 * @param id 公司ID
 * @returns 无返回值
 */
export function deleteCompanyApi(id: number) {
  return requestClient.delete(`${BASE_URL}/${id}`);
}

/**
 * 根据编码获取公司
 * @param code 公司编码
 * @returns 返回公司详细信息
 */
export function getCompanyByCodeApi(code: string) {
  return requestClient.get<Company>(`${BASE_URL}/code/${code}`);
}
