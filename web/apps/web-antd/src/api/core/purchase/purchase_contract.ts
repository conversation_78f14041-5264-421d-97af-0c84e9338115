import { requestClient } from '#/api/request';

// 采购合同明细项
export interface PurchaseContractItem {
  id?: number;
  contract_id?: number;
  inquiry_item_id?: number;
  product_id?: number;
  material_type: string;
  model?: string;
  brand?: string;
  pn?: string;
  spec?: string;
  unit?: string;
  contract_quantity: number;
  contract_price: number;
  contract_amount: number;
  remark?: string;
  created_at?: string;
  updated_at?: string;
}

// 创建合同明细参数
export interface CreateContractItemParams {
  inquiry_item_id?: number;
  product_id?: number;
  material_type: string;
  model?: string;
  brand?: string;
  pn?: string;
  spec?: string;
  unit?: string;
  contract_quantity: number;
  contract_price: number;
  contract_amount: number;

  remark?: string;
}

// 更新合同明细参数
export interface UpdateContractItemParams {
  id?: number;
  inquiry_item_id?: number;
  product_id?: number;
  material_type: string;
  model?: string;
  brand?: string;
  pn?: string;
  spec?: string;
  unit?: string;
  contract_quantity: number;
  contract_price: number;
  contract_amount: number;

  remark?: string;
}

// 采购合同主表
export interface PurchaseContract {
  id?: number;
  contract_no?: string;
  inquiry_id?: number;
  project_id?: number;
  our_company_id: number;
  supplier_id: number;
  contract_title?: string;
  contract_type?: string;
  signing_date?: string;
  delivery_address?: string;
  payment_terms?: string;
  warranty_period?: string;
  total_amount: number;
  status?: string;
  items?: PurchaseContractItem[];
  created_by?: number;
  created_at?: string;
  updated_by?: number;
  updated_at?: string;

  // 关联数据
  supplier_name?: string;
  project_name?: string;
  our_company_name?: string;
  creator_name?: string;
  updater_name?: string;
  inquiry_no?: string;
}

// 查询参数接口
export interface PurchaseContractQuery {
  page: number;
  pageSize: number;
  contract_no?: string;
  inquiry_id?: number;
  inquiry_no?: string;
  project_id?: number;
  supplier_id?: number;
  status?: string;
  created_by?: number;
  start_date?: string;
  end_date?: string;
  contract_type?: string;
}

// 创建合同参数
export interface CreatePurchaseContractParams {
  contract_no: string;
  inquiry_id?: number;
  project_id?: number;
  our_company_id: number;
  supplier_id: number;
  contract_title?: string;
  contract_type?: string;
  signing_date?: string;
  delivery_address?: string;
  payment_terms?: string;
  warranty_period?: string;
  total_amount: number;
  items: CreateContractItemParams[];
}

// 更新合同参数
export interface UpdatePurchaseContractParams {
  id: number;
  inquiry_id?: number;
  project_id?: number;
  our_company_id: number;
  supplier_id: number;
  contract_title?: string;
  contract_type?: string;
  signing_date?: string;
  delivery_address?: string;
  payment_terms?: string;
  warranty_period?: string;
  total_amount: number;
  items?: UpdateContractItemParams[];
}

// 审批参数
export interface ContractApprovalParams {
  contract_id: number;
  action: 'approve' | 'reject';
  comments: string;
  current_stage: string;
}

// 回退参数
export interface ContractRollbackParams {
  contract_id: number;
  rollback_to: string;
  current_stage: string;
  comments: string;
}

// 审批历史记录
export interface ContractApprovalHistory {
  id: number;
  business_type: string;
  business_id: number;
  previous_status: string;
  new_status: string;
  action: string;
  operator_id: number;
  operator_name: string;
  operation_time: string;
  comments: string;
  created_at: string;
}

// 合同工作流状态查询
export interface ContractWorkflowStatusQuery {
  contract_id: number;
}

// 合同回退历史记录
export interface ContractRollbackHistory {
  approver_id: number;
  approver_name: string;
  rollback_to: string;
  current_stage: string;
  comments: string;
  timestamp: string;
}

// 合同工作流状态响应
export interface ContractWorkflowStatusResponse {
  contract_id: number;
  current_stage: string;
  can_rollback: boolean;
  stage_history: string[];
  approval_history: ContractApprovalHistory[];
  rollback_history: ContractRollbackHistory[];
  result: string;
  started_at: string;
  completed_at?: string;
}

// 合同统计信息
export interface ContractStatistics {
  supplier_id: number;
  supplier_name: string;
  contract_count: number;
  total_amount: number;
  execution_rate: number;
  avg_amount: number;
}

// 合同详情响应
export interface PurchaseContractDetailResponse {
  contract: PurchaseContract;
  workflow_status: ContractWorkflowStatusResponse;
}

// 获取合同列表
export function getPurchaseContractList(params: PurchaseContractQuery) {
  return requestClient.get<{ list: PurchaseContract[]; total: number }>(
    '/purchase/contracts',
    { params },
  );
}

// 获取合同详情
export function getPurchaseContractById(id: number) {
  return requestClient.get<PurchaseContract>(`/purchase/contracts/${id}`);
}

// 根据合同编号获取合同
export function getPurchaseContractByNo(contractNo: string) {
  return requestClient.get<PurchaseContract>(
    `/purchase/contracts/no/${contractNo}`,
  );
}

// 创建合同
export function createPurchaseContract(data: CreatePurchaseContractParams) {
  return requestClient.post<PurchaseContract>('/purchase/contracts', data);
}

// 更新合同
export function updatePurchaseContract(
  id: number,
  data: UpdatePurchaseContractParams,
) {
  return requestClient.put<PurchaseContract>(`/purchase/contracts/${id}`, data);
}

// 提交合同
export function submitPurchaseContract(id: number) {
  return requestClient.post(`/purchase/contracts/${id}/submit`);
}

// 审批合同
export function approvePurchaseContract(data: ContractApprovalParams) {
  return requestClient.post('/purchase/contracts/approve', data);
}

// 回退合同
export function rollbackPurchaseContract(data: ContractRollbackParams) {
  return requestClient.post('/purchase/contracts/rollback', data);
}

// 取消合同
export function cancelPurchaseContract(id: number) {
  return requestClient.post(`/purchase/contracts/${id}/cancel`);
}

// 删除合同
export function deletePurchaseContract(id: number) {
  return requestClient.delete(`/purchase/contracts/${id}`);
}

// 获取合同审批历史
export function getPurchaseContractHistory(id: number) {
  return requestClient.get<ContractApprovalHistory[]>(
    `/purchase/contracts/${id}/history`,
  );
}

// 获取合同统计信息
export function getContractStatistics(params?: {
  end_date?: string;
  start_date?: string;
  supplier_id?: number;
}) {
  return requestClient.get<ContractStatistics[]>(
    '/purchase/contracts/statistics',
    { params },
  );
}

// 合同表单数据类型
export type PurchaseContractFormData = PurchaseContract;

// 询价明细采购统计信息
export interface InquiryItemPurchaseStats {
  inquiry_item_id: number; // 询价明细ID
  material_type: string; // 物料类型
  model: string; // 型号
  brand: string; // 品牌
  pn: string; // PN号
  spec: string; // 规格
  unit: string; // 单位
  inquiry_quantity: number; // 询价数量
  inquiry_budget: number; // 询价预算
  purchased_quantity: number; // 已采购数量
  purchased_amount: number; // 已采购金额
  unpurchased_quantity: number; // 未采购数量
  unpurchased_budget: number; // 未采购预算
}

/**
 * 获取询价明细采购统计信息
 * @param inquiryId 询价单ID
 * @returns 询价明细采购统计信息列表
 */
export function getInquiryItemsPurchaseStats(inquiryId: number) {
  return requestClient.get<InquiryItemPurchaseStats[]>(
    `/purchase/contracts/inquiry/${inquiryId}/stats`,
  );
}
