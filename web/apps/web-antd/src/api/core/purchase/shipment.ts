import { requestClient } from '#/api/request';

// 发货管理相关类型定义
export interface ShipmentItem {
  // 基本信息
  id?: number;
  shipment_id?: number;
  contract_item_id: number;
  contract_quantity: number;
  contract_amount: number;

  // 发货统计信息
  shipped_quantity: number;
  shipped_amount: number;
  unshipped_quantity: number;
  unshipped_amount: number;
  current_shipment_quantity: number;
  current_shipment_amount: number;
  expected_arrival_date?: string;

  // 付款统计信息
  paid_quantity?: number;
  paid_amount?: number;
  unpaid_quantity?: number;
  unpaid_amount?: number;

  // 序列号和备注
  serial_numbers?: string;
  remark?: string;

  // 时间戳
  created_at?: string;
  updated_at?: string;

  // 关联的合同明细信息
  contract_item?: {
    brand?: string;
    contract_price: number;
    contract_quantity: number;
    id: number;
    material_type?: string;
    model?: string;
    pn?: string;
    spec?: string;
    unit?: string;
  };

  // 序列号列表
  serial_number_list?: string[];
}

export interface Shipment {
  // 基本信息
  id: number;
  shipment_no: string;
  contract_id: number;
  supplier_id: number;
  shipment_notice?: string;
  tracking_info?: string;
  delivery_address?: string;
  expected_arrival_date?: string;
  total_quantity: number;
  total_amount: number;
  is_complete: boolean;
  remark?: string;

  // 状态信息
  status: string;
  status_display_name?: string;

  // 时间戳
  created_at: string;
  updated_at?: string;

  // 创建人和更新人信息
  created_by?: number;
  updated_by?: number;
  creator?: {
    id: number;
    name: string;
  };
  updater?: {
    id: number;
    name: string;
  };
  items?: ShipmentItem[];

  // 扩展信息
  contract_no?: string;
  supplier_name?: string;
  our_company_name?: string;
  creator_name?: string;
  updater_name?: string;
}

export interface CreateShipmentDTO {
  // 基本信息
  contract_id: number;
  shipment_notice?: string;
  tracking_info?: string;
  delivery_address?: string;
  expected_arrival_date?: string;
  remark?: string;
  items: {
    contract_item_id: number;
    current_shipment_amount: number;
    current_shipment_quantity: number;
    expected_arrival_date?: string;
    remark?: string;
    serial_numbers?: string[];
  }[];
}

export interface UpdateShipmentDTO {
  // 基本信息
  shipment_notice?: string;
  tracking_info?: string;
  delivery_address?: string;
  expected_arrival_date?: string;
  remark?: string;
  items?: {
    contract_item_id: number;
    current_shipment_amount: number;
    current_shipment_quantity: number;
    expected_arrival_date?: string;
    id?: number;
    remark?: string;
    serial_numbers?: string[];
  }[];
}

export interface ShipmentQuery {
  // 分页参数
  page?: number;
  pageSize?: number;
  shipment_no?: string;
  contract_no?: string;
  contract_id?: number;
  supplier_id?: number;
  status?: string;
  is_complete?: boolean;
  created_by?: number;

  // 时间范围
  start_date?: string;
  end_date?: string;

  // 排序
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

// 分页参数类型
export interface PageParams {
  currentPage: number;
  pageSize: number;
}

export interface ShipmentListResponse {
  list: Shipment[];
  page: number;
  total: number;
}

export interface ShipmentApprovalDTO {
  // 审批信息
  shipment_id: number;
  current_stage: string;
  action: 'approve' | 'reject';
  comments?: string;
}

export interface ShipmentRollbackParams {
  current_stage: string;
  rollback_to?: string;
  comments: string;
}

export interface ShipmentHistory {
  // 历史记录
  id: number;
  shipment_id: number;
  action: string;
  from_status: string;
  to_status: string;
  comments?: string;
  approver_id?: number;
  approver_name?: string;
  created_at: string;
}

export interface ShipmentStatistics {
  // 统计信息
  total_count: number;
  draft_count: number;
  pending_count: number;
  completed_count: number;
  cancelled_count: number;
  total_amount: number;
  completed_amount: number;
}

// API 接口函数
export async function getShipmentList(
  params: ShipmentQuery,
): Promise<ShipmentListResponse> {
  return requestClient.get('/purchase/shipments', { params });
}

export async function getShipmentById(id: number): Promise<Shipment> {
  return requestClient.get(`/purchase/shipments/${id}`);
}

export async function getShipmentByNo(shipmentNo: string): Promise<Shipment> {
  return requestClient.get(`/purchase/shipments/by-no/${shipmentNo}`);
}

export async function createShipment(
  data: CreateShipmentDTO,
): Promise<Shipment> {
  return requestClient.post('/purchase/shipments', data);
}

export async function updateShipment(
  id: number,
  data: UpdateShipmentDTO,
): Promise<Shipment> {
  return requestClient.put(`/purchase/shipments/${id}`, data);
}

export async function deleteShipment(id: number): Promise<void> {
  return requestClient.delete(`/purchase/shipments/${id}`);
}

export async function approveShipment(
  data: ShipmentApprovalDTO,
): Promise<Shipment> {
  return requestClient.post('/purchase/shipments/approve', data);
}

export async function rollbackShipment(
  id: number,
  data: ShipmentRollbackParams,
): Promise<Shipment> {
  return requestClient.post(`/purchase/shipments/${id}/rollback`, data);
}

export async function getShipmentHistory(
  id: number,
): Promise<ShipmentHistory[]> {
  return requestClient.get(`/purchase/shipments/${id}/history`);
}

export async function getShipmentStatistics(): Promise<ShipmentStatistics> {
  return requestClient.get('/purchase/shipments/statistics');
}

export async function getShipmentsByContract(
  contractId: number,
): Promise<Shipment[]> {
  return requestClient.get(`/purchase/shipments/by-contract/${contractId}`);
}
