import { requestClient } from '#/api/request';

export interface PurchaseRequestItem {
  id?: number;
  request_id?: number;
  product_id?: number;
  material_type: string;
  model: string;
  brand: string;
  pn: string;
  spec: string;
  unit: string;
  quantity: number;
  remark: string;
  created_at?: string;
  updated_at?: string;
}

export interface PurchaseRequest {
  id?: number;
  request_no?: string;
  project_id?: number;
  project_name?: string;
  request_type: string;
  urgency_level: string;
  expected_delivery_date?: string;
  reason: string;
  receive_address?: string;
  receiver?: string;
  receiver_phone?: string;
  status?: string; // 'draft' | 'project_manager_review' | 'purchase_manager_review' | 'approved' | 'rejected' | 'cancelled'
  workflow_id?: string;
  items: PurchaseRequestItem[];
  created_by?: number;
  created_at?: string;
  updated_at?: string;
  updated_by?: number;
  deleted_at?: null | string;
  creator_name?: string;
}

export interface PurchaseRequestQuery {
  request_no?: string;
  project_id?: number;
  request_type?: string;
  urgency_level?: string;
  status?: string;
  created_by?: number;
  start_date?: string;
  end_date?: string;
  page: number;
  pageSize: number;
}

export interface PurchaseRequestListResult {
  total: number;
  list: PurchaseRequest[];
}

export interface WorkflowApproval {
  request_id: number;
  action: 'approve' | 'reject';
  comments: string;
  current_stage: string;
}

export interface WorkflowRollback {
  request_id: number;
  approver_id: number;
  rollback_to: string;
  current_stage: string;
  comments: string;
}

export interface PurchaseApprovalHistory {
  id: number;
  business_type: string;
  business_id: number;
  previous_status: string;
  new_status: string;
  action: string;
  operator_id: number;
  operator_name: string;
  operation_time: string;
  comments: string;
  created_at: string;
}

// 更新为与实际返回数据匹配的接口定义
export type WorkflowStatus = PurchaseApprovalHistory[];

// 创建采购申请
export function createPurchaseRequest(data: PurchaseRequest) {
  return requestClient.post<PurchaseRequest>('/purchase/requests', data);
}

// 获取采购申请列表
export function getPurchaseRequestList(params: PurchaseRequestQuery) {
  return requestClient.get<PurchaseRequestListResult>('/purchase/requests', {
    params,
  });
}

// 根据ID获取采购申请详情
export function getPurchaseRequestById(id: number) {
  return requestClient.get<PurchaseRequest>(`/purchase/requests/${id}`);
}

// 根据ID获取采购申请详情（别名，为了向后兼容）
export function getPurchaseRequestDetail(id: number) {
  return getPurchaseRequestById(id);
}

// 根据申请单号获取采购申请
export function getPurchaseRequestByRequestNo(requestNo: string) {
  return requestClient.get<PurchaseRequest>(
    `/purchase/requests/no/${requestNo}`,
  );
}

// 更新采购申请
export function updatePurchaseRequest(id: number, data: PurchaseRequest) {
  return requestClient.put<PurchaseRequest>(`/purchase/requests/${id}`, data);
}

// 提交采购申请进入审批流程
export function submitPurchaseRequest(id: number) {
  return requestClient.post(`/purchase/requests/${id}/submit`);
}

// 审批采购申请
export function approvePurchaseRequest(data: WorkflowApproval) {
  return requestClient.post('/purchase/requests/approve', data);
}

// 回退采购申请
export function rollbackPurchaseRequest(data: WorkflowRollback) {
  return requestClient.post('/purchase/requests/rollback', data);
}

// 获取工作流状态
export function getWorkflowStatus(id: number) {
  return requestClient.get<WorkflowStatus>(
    `/purchase/requests/${id}/workflow-status`,
  );
}

// 获取采购申请历史记录
export function getPurchaseRequestHistory(id: number) {
  return requestClient.get<PurchaseApprovalHistory[]>(
    `/purchase/requests/${id}/history`,
  );
}

// 取消采购申请
export function cancelPurchaseRequest(id: number) {
  return requestClient.post(`/purchase/requests/${id}/cancel`);
}

// 删除采购申请
export function deletePurchaseRequest(id: number) {
  return requestClient.delete(`/purchase/requests/${id}`);
}
