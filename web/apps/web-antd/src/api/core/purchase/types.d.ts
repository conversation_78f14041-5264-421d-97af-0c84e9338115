interface Product {
  product_id: number;
  amount: number;
  price: number;
}

export interface PurchaseOrderReq {
  purchase_title: string;
  purchase_order_no: string;
  supplier_name: string;
  order_date: string;
  details: Partial<Product>[];
}

export interface PurchaseTemplateRes {
  fileName: string;
  filePath: string;
}

export interface PurchaseTemplateReq {
  template_type: string;
  inbound_no?: string;
  outbound_id?: string;
}

export interface UploadPurchaseOrderReq {
  purchase_title: string;
  purchase_order_no: string;
  supplier_name: string;
  may_arrive_at: string;
  tracking_info: string;
  order_date: string;
  file: File;
}
