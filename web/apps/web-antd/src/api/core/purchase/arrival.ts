import { requestClient } from '#/api/request';

// 到货管理相关类型定义
export interface ArrivalItem {
  id?: number;
  arrival_id?: number;
  contract_item_id: number;
  contract_quantity: number;
  contract_amount: number;
  received_quantity: number;
  received_amount: number;
  unreceived_quantity: number;
  unreceived_amount: number;
  paid_quantity: number;
  paid_amount: number;
  unpaid_quantity: number;
  unpaid_amount: number;
  current_arrival_quantity: number;
  current_arrival_amount: number;
  expected_arrival_date?: string;
  serial_numbers?: string;
  remark?: string;
  created_at?: string;
  updated_at?: string;
  // 关联信息
  contract_item?: {
    brand: string;
    contract_price: number;
    contract_quantity: number;
    id: number;
    material_type: string;
    model: string;
    pn: string;
    spec: string;
    unit: string;
  };
  serial_number_list?: string[];
}

export interface Arrival {
  id: number;
  arrival_no: string;
  contract_id: number;
  supplier_id: number;
  may_arrive_at?: string;
  arrival_notice?: string;
  tracking_info?: string;
  arrival_address?: string;
  total_quantity: number;
  total_amount: number;
  is_complete: boolean;
  remark?: string;
  status: string;
  created_by?: number;
  created_at: string;
  updated_by?: number;
  updated_at?: string;
  // 关联信息
  contract?: {
    contract_name: string;
    contract_no: string;
    id: number;
  };
  supplier?: {
    id: number;
    name: string;
  };
  items?: ArrivalItem[];
  // 扩展信息
  contract_no?: string;
  supplier_name?: string;
  our_company_name?: string;
  creator_name?: string;
  updater_name?: string;
}

export interface CreateArrivalDTO {
  contract_id: number;
  may_arrive_at?: string;
  arrival_notice?: string;
  tracking_info?: string;
  arrival_address?: string;
  remark?: string;
  items: {
    contract_item_id: number;
    current_arrival_amount: number;
    current_arrival_quantity: number;
    expected_arrival_date?: string;
    remark?: string;
    serial_numbers?: string[];
  }[];
}

export interface UpdateArrivalDTO {
  may_arrive_at?: string;
  arrival_notice?: string;
  tracking_info?: string;
  arrival_address?: string;
  remark?: string;
  items?: {
    contract_item_id: number;
    current_arrival_amount: number;
    current_arrival_quantity: number;
    expected_arrival_date?: string;
    id?: number;
    remark?: string;
    serial_numbers?: string[];
  }[];
}

export interface ArrivalQuery {
  page?: number;
  pageSize?: number;
  arrival_no?: string;
  contract_id?: number;
  supplier_id?: number;
  status?: string;
  is_complete?: boolean;
  created_by?: number;
  start_date?: string;
  end_date?: string;
  min_amount?: number;
  max_amount?: number;
}

export interface PageParams {
  currentPage: number;
  pageSize: number;
}

export interface ArrivalListResponse {
  list: Arrival[];
  total: number;
}

export interface ArrivalApprovalDTO {
  id: number;
  current_stage: string;
  action: 'approve' | 'reject';
  comments?: string;
}

export interface ArrivalRollbackParams {
  id: number;
  current_stage: string;
  rollback_to?: string;
  comments: string;
}

export interface ArrivalHistory {
  id: number;
  arrival_id: number;
  action: string;
  from_status: string;
  to_status: string;
  comments?: string;
  approver_id: number;
  approver_name: string;
  created_at: string;
}

export interface ArrivalStatistics {
  total_count: number;
  draft_count: number;
  pending_count: number;
  completed_count: number;
  cancelled_count: number;
  total_amount: number;
  completed_amount: number;
}

// API 接口函数
export async function getArrivalList(
  params: ArrivalQuery,
): Promise<ArrivalListResponse> {
  return requestClient.get('/purchase/arrivals', { params });
}

export async function getArrivalById(id: number): Promise<Arrival> {
  return requestClient.get(`/purchase/arrivals/${id}`);
}

export async function getArrivalByNo(arrivalNo: string): Promise<Arrival> {
  return requestClient.get(`/purchase/arrivals/by-no/${arrivalNo}`);
}

export async function createArrival(data: CreateArrivalDTO): Promise<Arrival> {
  return requestClient.post('/purchase/arrivals', data);
}

export async function updateArrival(
  id: number,
  data: UpdateArrivalDTO,
): Promise<Arrival> {
  return requestClient.put(`/purchase/arrivals/${id}`, data);
}

export async function deleteArrival(id: number): Promise<void> {
  return requestClient.delete(`/purchase/arrivals/${id}`);
}

export async function submitArrival(id: number): Promise<void> {
  return requestClient.post(`/purchase/arrivals/${id}/submit`);
}

export async function cancelArrival(id: number): Promise<void> {
  return requestClient.post(`/purchase/arrivals/${id}/cancel`);
}

export async function approveArrival(data: ArrivalApprovalDTO): Promise<void> {
  return requestClient.post('/purchase/arrivals/approve', data);
}

export async function rollbackArrival(
  data: ArrivalRollbackParams,
): Promise<void> {
  return requestClient.post('/purchase/arrivals/rollback', data);
}

export async function getArrivalsByContractId(
  contractId: number,
): Promise<Arrival[]> {
  return requestClient.get(`/purchase/arrivals/by-contract/${contractId}`);
}

export async function getArrivalHistory(id: number): Promise<ArrivalHistory[]> {
  return requestClient.get(`/purchase/arrivals/${id}/history`);
}

export async function getArrivalStatistics(): Promise<ArrivalStatistics> {
  return requestClient.get('/purchase/arrivals/statistics');
}
