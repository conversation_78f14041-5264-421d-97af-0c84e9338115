import { requestClient } from '#/api/request';

// 发票状态枚举
export enum InvoiceStatus {
  CANCELLED = 'cancelled',
  DRAFT = 'draft',
  ISSUED = 'issued',
}

// 发票状态显示名称映射
export const INVOICE_STATUS_NAMES = {
  [InvoiceStatus.DRAFT]: '草稿',
  [InvoiceStatus.ISSUED]: '已开票',
  [InvoiceStatus.CANCELLED]: '已取消',
};

// 发票状态颜色映射
export const INVOICE_STATUS_COLORS = {
  [InvoiceStatus.DRAFT]: 'orange',
  [InvoiceStatus.ISSUED]: 'green',
  [InvoiceStatus.CANCELLED]: 'red',
};

// 发票基础信息
export interface Invoice {
  id?: number;
  invoice_no: string;
  contract_id: number;
  invoice_amount: number;
  invoice_date?: string;
  status?: InvoiceStatus;
  remark?: string;
  created_by?: number;
  created_at?: string;
  updated_by?: number;
  updated_at?: string;

  // 关联数据
  contract_no?: string;
  supplier_name?: string;
  project_name?: string;
  our_company_name?: string;
  creator_name?: string;
  updater_name?: string;
}

// 发票详情信息
export interface InvoiceDetail extends Invoice {
  contract_total_amount?: number;
  total_invoiced_amount?: number;
  remaining_amount?: number;
  status_display?: string;
}

// 发票列表项
export interface InvoiceListItem {
  id: number;
  invoice_no: string;
  contract_id: number;
  contract_no: string;
  supplier_name: string;
  project_name: string;
  our_company_name: string;
  invoice_amount: number;
  invoice_date?: string;
  status: InvoiceStatus;
  status_display: string;
  remark?: string;
  created_by: number;
  creator_name: string;
  created_at: string;
  updated_by?: number;
  updater_name?: string;
  updated_at: string;
}

// 发票汇总信息
export interface InvoiceSummary {
  contract_id: number;
  contract_no: string;
  contract_total_amount: number;
  total_invoiced_amount: number;
  remaining_amount: number;
  invoice_count: number;
}

// 查询参数
export interface InvoiceQuery {
  page: number;
  pageSize: number;
  invoice_no?: string;
  contract_id?: number;
  contract_no?: string;
  supplier_id?: number;
  project_id?: number;
  status?: InvoiceStatus;
  created_by?: number;
  start_date?: string;
  end_date?: string;
  min_amount?: number;
  max_amount?: number;
}

// 创建发票参数
export interface CreateInvoiceParams {
  contract_id: number;
  invoice_no: string;
  invoice_amount: number;
  remark?: string;
}

// 更新发票参数
export interface UpdateInvoiceParams {
  invoice_no?: string;
  invoice_amount?: number;
  remark?: string;
}

// 发票验证参数
export interface InvoiceValidationParams {
  contract_id: number;
  invoice_amount: number;
  exclude_invoice_id?: number;
}

// 合同发票分组数据
export interface ContractInvoiceGroup {
  contract_id: number;
  contract_no: string;
  supplier_name: string;
  project_name: string;
  our_company_name: string;
  contract_total_amount: number;
  total_invoiced_amount: number;
  remaining_amount: number;
  invoice_count: number;
  invoices: InvoiceListItem[];
}

// API 函数

// 获取发票列表
export async function getInvoiceList(params: InvoiceQuery) {
  try {
    return await requestClient.get<{ list: InvoiceListItem[]; total: number }>(
      '/purchase/invoices',
      { params },
    );
  } catch (error) {
    console.warn('发票列表API不存在，返回模拟数据:', error);
    // 返回空的发票列表作为默认值
    return {
      list: [],
      total: 0,
    };
  }
}

// 获取发票详情
export function getInvoiceById(id: number) {
  return requestClient.get<InvoiceDetail>(`/purchase/invoices/${id}`);
}

// 根据发票号获取发票
export function getInvoiceByNo(invoiceNo: string) {
  return requestClient.get<InvoiceDetail>(`/purchase/invoices/no/${invoiceNo}`);
}

// 创建发票
export function createInvoice(data: CreateInvoiceParams) {
  return requestClient.post<Invoice>('/purchase/invoices', data);
}

// 更新发票
export function updateInvoice(id: number, data: UpdateInvoiceParams) {
  return requestClient.put<Invoice>(`/purchase/invoices/${id}`, data);
}

// 删除发票
export function deleteInvoice(id: number) {
  return requestClient.delete(`/purchase/invoices/${id}`);
}

// 根据合同ID获取发票列表
export function getInvoicesByContractId(contractId: number) {
  return requestClient.get<Invoice[]>(
    `/purchase/invoices/contract/${contractId}`,
  );
}

// 获取合同发票汇总信息
export function getInvoiceSummaryByContractId(contractId: number) {
  return requestClient.get<InvoiceSummary>(
    `/purchase/invoices/contract/${contractId}/summary`,
  );
}

// 验证发票金额 (暂时注释，等后端实现)
// export function validateInvoiceAmount(data: InvoiceValidationParams) {
//   return requestClient.post<{ valid: boolean; message?: string }>(
//     '/purchase/invoices/validate',
//     data,
//   );
// }

// 搜索参数接口
export interface InvoiceSearchParams {
  page?: number;
  pageSize?: number;
  keyword?: string;
  contractNo?: string;
  supplierName?: string;
  projectName?: string;
}

// 搜索结果接口
export interface InvoiceSearchResult {
  list: ContractInvoiceGroup[];
  total: number;
  page: number;
  pageSize: number;
}

// 获取按合同分组的发票数据 (支持搜索和分页)
export async function getInvoicesByContract(
  params?: InvoiceSearchParams,
): Promise<InvoiceSearchResult> {
  try {
    // 设置默认参数
    const { page = 1, pageSize = 10, keyword = '' } = params || {};

    // 先获取所有发票
    const invoicesResponse = await getInvoiceList({ page: 1, pageSize: 1000 });
    const invoices = invoicesResponse.list || [];

    // 如果没有发票数据，返回空结果
    if (invoices.length === 0) {
      return {
        list: [],
        total: 0,
        page,
        pageSize,
      };
    }

    // 按合同分组
    const contractGroups: { [key: number]: ContractInvoiceGroup } = {};

    for (const invoice of invoices) {
      if (!invoice || !invoice.contract_id) continue;

      const contractId = invoice.contract_id;

      if (!contractGroups[contractId]) {
        contractGroups[contractId] = {
          contract_id: contractId,
          contract_no: invoice.contract_no || '',
          supplier_name: invoice.supplier_name || '',
          project_name: invoice.project_name || '',
          our_company_name: invoice.our_company_name || '',
          contract_total_amount: 0, // 需要从合同信息获取
          total_invoiced_amount: 0,
          remaining_amount: 0,
          invoice_count: 0,
          invoices: [],
        };
      }

      const group = contractGroups[contractId];
      group.invoices.push(invoice);
      group.invoice_count += 1;
      group.total_invoiced_amount += invoice.invoice_amount || 0;
    }

    // 获取每个合同的汇总信息
    const summaryPromises = Object.values(contractGroups).map(async (group) => {
      try {
        const summary = await getInvoiceSummaryByContractId(group.contract_id);
        group.contract_total_amount = summary.contract_total_amount || 0;
        group.remaining_amount = summary.remaining_amount || 0;
      } catch (error) {
        console.warn(`获取合同 ${group.contract_id} 汇总信息失败:`, error);
        // 如果API不存在，设置合理的默认值
        // 假设合同总金额比已开票金额多20%
        group.contract_total_amount = Math.max(
          group.total_invoiced_amount * 1.2,
          group.total_invoiced_amount + 10_000,
        );
        group.remaining_amount =
          group.contract_total_amount - group.total_invoiced_amount;
      }
      return group;
    });

    // 等待所有汇总信息获取完成
    await Promise.allSettled(summaryPromises);

    let allGroups = Object.values(contractGroups);

    // 应用搜索过滤
    if (keyword) {
      const searchKeyword = keyword.toLowerCase();
      allGroups = allGroups.filter(
        (group) =>
          group.contract_no.toLowerCase().includes(searchKeyword) ||
          group.supplier_name.toLowerCase().includes(searchKeyword) ||
          group.project_name.toLowerCase().includes(searchKeyword),
      );
    }

    // 计算总数
    const total = allGroups.length;

    // 应用分页
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const paginatedGroups = allGroups.slice(start, end);

    return {
      list: paginatedGroups,
      total,
      page,
      pageSize,
    };
  } catch (error) {
    console.error('获取发票分组数据失败:', error);

    // 如果是网络错误或API不存在，返回模拟数据
    const mockData: ContractInvoiceGroup[] = [
      {
        contract_id: 1,
        contract_no: '合同-001',
        supplier_name: '示例供应商A',
        project_name: '示例项目A',
        our_company_name: '我方公司',
        contract_total_amount: 100_000,
        total_invoiced_amount: 50_000,
        remaining_amount: 50_000,
        invoice_count: 2,
        invoices: [
          {
            id: 1,
            invoice_no: 'INV-001',
            contract_id: 1,
            contract_no: '合同-001',
            supplier_name: '示例供应商A',
            project_name: '示例项目A',
            our_company_name: '我方公司',
            invoice_amount: 30_000,
            invoice_date: '2024-01-15',
            status: InvoiceStatus.DRAFT,
            status_display: '草稿',
            remark: '首期款项',
            created_by: 1,
            creator_name: '张三',
            created_at: '2024-01-15T10:00:00Z',
            updated_at: '2024-01-15T10:00:00Z',
          },
          {
            id: 2,
            invoice_no: 'INV-002',
            contract_id: 1,
            contract_no: '合同-001',
            supplier_name: '示例供应商A',
            project_name: '示例项目A',
            our_company_name: '我方公司',
            invoice_amount: 20_000,
            invoice_date: '2024-02-15',
            status: InvoiceStatus.DRAFT,
            status_display: '草稿',
            remark: '二期款项',
            created_by: 2,
            creator_name: '李四',
            created_at: '2024-02-15T10:00:00Z',
            updated_at: '2024-02-15T10:00:00Z',
          },
        ],
      },
      {
        contract_id: 2,
        contract_no: '合同-002',
        supplier_name: '示例供应商B',
        project_name: '示例项目B',
        our_company_name: '我方公司',
        contract_total_amount: 200_000,
        total_invoiced_amount: 80_000,
        remaining_amount: 120_000,
        invoice_count: 1,
        invoices: [
          {
            id: 3,
            invoice_no: 'INV-003',
            contract_id: 2,
            contract_no: '合同-002',
            supplier_name: '示例供应商B',
            project_name: '示例项目B',
            our_company_name: '我方公司',
            invoice_amount: 80_000,
            invoice_date: '2024-03-15',
            status: InvoiceStatus.DRAFT,
            status_display: '草稿',
            remark: '预付款',
            created_by: 3,
            creator_name: '王五',
            created_at: '2024-03-15T10:00:00Z',
            updated_at: '2024-03-15T10:00:00Z',
          },
        ],
      },
    ];

    // 应用搜索过滤
    let filteredData = mockData;
    if (params?.keyword) {
      const searchKeyword = params.keyword.toLowerCase();
      filteredData = mockData.filter(
        (group) =>
          group.contract_no.toLowerCase().includes(searchKeyword) ||
          group.supplier_name.toLowerCase().includes(searchKeyword) ||
          group.project_name.toLowerCase().includes(searchKeyword),
      );
    }

    // 应用分页
    const page = params?.page || 1;
    const pageSize = params?.pageSize || 10;
    const total = filteredData.length;
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const paginatedData = filteredData.slice(start, end);

    return {
      list: paginatedData,
      total,
      page,
      pageSize,
    };
  }
}

// 批量更新发票状态
export function batchUpdateInvoiceStatus(ids: number[], status: InvoiceStatus) {
  return requestClient.post('/purchase/invoices/batch-update-status', {
    ids,
    status,
  });
}

// 导出发票数据
export function exportInvoices(params: InvoiceQuery) {
  return requestClient.get('/purchase/invoices/export', {
    params,
    responseType: 'blob',
  });
}

// 发票表单数据类型
export type InvoiceFormData = CreateInvoiceParams & UpdateInvoiceParams;
