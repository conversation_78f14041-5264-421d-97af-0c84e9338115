import type { InboundReq, StartWorkflowReq } from '../cmdb/asset/types';
import type {
  ApproveInboundTicketReq,
  CreateInboundRepairTicketReq,
  CreateInboundTicketReq,
  CreateOutboundTicketReq,
  CreateOutboundTicketReqV2,
  InboundDetail,
  InboundDismantledTicketReq,
  InboundInfo,
  InboundTicketHistoryRes,
  InboundTicketRes,
  ListOutboundTicketReq,
  ListOutboundTicketRes,
  OutboundDetail,
  OutboundInfo,
  OutboundTicketHistoryRes,
  OutboundTicketRes,
  TransitionInboundTicketReq,
} from './types';

import { requestClient } from '#/api/request';

/** 资产出库管理 */
export function listOutboundTicket(params: ListOutboundTicketReq) {
  return requestClient.get<ListOutboundTicketRes>('/ticket/outbound-tickets', {
    params,
  });
}

/** 创建出库单 */
export function createOutboundTicket(data: CreateOutboundTicketReq) {
  return requestClient.post('/ticket/outbound-tickets', data);
}

/**  创建出库单 V2 */
export function createOutboundTicketV2(data: CreateOutboundTicketReqV2) {
  return requestClient.post('/ticket/outbound-tickets/v2', data);
}

/** 启动工作流 */
export function startWorkflow(ticket_id: number) {
  return requestClient.post(
    `/ticket/outbound-tickets/${ticket_id}/start-workflow`,
  );
}
/** 出库单详情 */
export function getOutboundTicket(id: string) {
  return requestClient.get<OutboundTicketRes>(`/ticket/outbound-tickets/${id}`);
}

export function getOutboundTicketInfo(id: string) {
  return requestClient.get<OutboundInfo[]>(
    `/ticket/outbound-tickets/${id}/info`,
  );
}

export function getOutboundTicketDetail(id: string) {
  return requestClient.get<{
    list: OutboundDetail[];
    total: number;
  }>(`/ticket/outbound-tickets/${id}/details`);
}

export function getOutboundTicketDetailList(
  id: string,
  page: number,
  page_size: number,
) {
  return requestClient.get<{
    list: OutboundDetail[];
    total: number;
  }>(`/ticket/outbound-tickets/${id}/details-list`, {
    params: {
      page,
      page_size,
    },
  });
}

/** 更新出库单详情 */
export function updateOutboundTicketDetails(details: OutboundDetail[]) {
  return requestClient.put(`/ticket/outbound-tickets/details`, details);
}

/** 通过上传文件的形式更新出库单详情 */
export function updateOutboundTicketByFile(file: any, template: string) {
  const formData = new FormData();
  formData.append('template_name', template);
  formData.append('file', file);
  return requestClient.put(
    `/ticket/outbound-tickets/details/import`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
}

/** 更新出库单审批 */
export function updateOutboundTicketApproval(id: string, data: any) {
  return requestClient.put(
    `/ticket/outbound-tickets/${id}/transition/v2`,
    data,
  );
}

/** 出库单历史 */
export function historyOutboundTicket(id: string) {
  return requestClient.get<OutboundTicketHistoryRes[]>(
    `/ticket/outbound-tickets/${id}/history`,
  );
}

/** 审批出库单 */
export function approveOutboundTicket(id: string, data: any) {
  return requestClient.put(`/ticket/outbound-tickets/${id}/transition`, data);
}

/** 创建入库单 */
export function createInboundTicketApi(data: CreateInboundTicketReq) {
  return requestClient.post('/ticket/inbound-tickets/new-part', data);
}
/** 创建入库单统一接口 */
export function createInboundTicketApiV2(data: InboundReq) {
  return requestClient.post(`/ticket/inbound-tickets`, data);
}

export function startWorkflowV2(data: StartWorkflowReq) {
  return requestClient.post(`/ticket/inbound-tickets/start-workflow`, data);
}

/** 获取入库单V2 */
export function getInboundTicketV2Api(inbound_no: string) {
  return requestClient.get<InboundTicketRes>(
    `/ticket/inbound-tickets/${inbound_no}`,
  );
}

export function getInboundDetailApi(inbound_no: string) {
  return requestClient.get<{
    list: InboundDetail[];
    total: number;
  }>(`/ticket/inbound-tickets/${inbound_no}/detail`);
}

export function getInboundDetailListApi(
  inbound_no: string,
  page: number,
  page_size: number,
) {
  return requestClient.get<{
    list: InboundDetail[];
    total: number;
  }>(`/ticket/inbound-tickets/${inbound_no}/detail-list`, {
    params: {
      page,
      page_size,
    },
  });
}

export function getInboundInfoApi(inbound_no: string) {
  return requestClient.get<{
    list: InboundInfo[];
    total: number;
  }>(`/ticket/inbound-tickets/${inbound_no}/info`);
}

export function getInboundHistoryApi(inbound_no: string) {
  return requestClient.get<InboundTicketHistoryRes[]>(
    `/ticket/inbound-tickets/${inbound_no}/history`,
  );
}

/** 更新入库单详情 */
export function updateInboundDetailApi(
  inbound_no: string,
  details: InboundDetail[],
) {
  return requestClient.put(
    `/ticket/inbound-tickets/${inbound_no}/update-detail`,
    details,
  );
}

/** 通过上传文件的形式更新入库单详情 */
export function updateInboundDetailImportApi(inbound_no: string, file: any) {
  const formData = new FormData();

  // 检查文件对象类型并正确处理
  if (file instanceof File) {
    formData.append('file', file);
  } else if (file && file.originFileObj) {
    // 处理Ant Design上传组件返回的文件对象结构
    formData.append('file', file.originFileObj);
  } else {
    console.error('无效的文件对象:', file);
    throw new Error('无效的文件对象');
  }

  return requestClient.put(
    `/ticket/inbound-tickets/${inbound_no}/update-detail-import`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
}

export function TransitionInboundTicket(
  inbound_no: string,
  data: TransitionInboundTicketReq,
) {
  return requestClient.put(
    `/ticket/inbound-tickets/${inbound_no}/transitionV2`,
    data,
  );
}

/** 入库审批 */
export function approveInboundTicket(
  id: string,
  data: ApproveInboundTicketReq,
) {
  return requestClient.put(`/ticket/inbound-tickets/${id}/transition`, data);
}

/** 创建返修入库单 */
export function createInboundRepairTicketApi(
  data: CreateInboundRepairTicketReq,
) {
  return requestClient.post('/ticket/inbound-tickets/repair', data);
}

/** 创建拆机入库工单 */
export function createInboundDismantledTicketApi(
  data: InboundDismantledTicketReq,
) {
  return requestClient.post('/ticket/inbound-tickets/dismantled', data);
}
