import type { Resource } from '../cmdb/asset/device';
import type {
  ApprovalEntryTicketReq,
  EntryPersonReq,
  EntryPersonsRes,
  EntryTicketListRes,
  EntryTicketRes,
  HistoryEntryTicketsRes,
  ListEntryPersonsReq,
  ListEntryTicketReq,
  PostEntryTicketReq,
  UpdateEntryPersonReq,
} from './types';

import { requestClient } from '#/api/request';

// 修改基础路径，避免路径重复的问题
const baseUrl = '/ticket/fault-tickets';

// 故障工单状态类型
export type FaultTicketStatus =
  | 'approved_waiting_action'
  | 'cancelled'
  | 'completed'
  | 'investigating'
  | 'migrating'
  | 'repairing'
  | 'restarting'
  | 'software_fixing'
  | 'summarizing'
  | 'waiting_accept'
  | 'waiting_approval'
  | 'waiting_verification';

// 故障类型
export type FaultType =
  | 'cloud_platform_fault'
  | 'datacenter_fault'
  | 'hardware'
  | 'network'
  | 'network_fault'
  | 'operation'
  | 'ops_platform_fault'
  | 'other_fault'
  | 'server_fault'
  | 'software'
  | 'system';

// 优先级
export type Priority = 'critical' | 'high' | 'low' | 'medium';

// 维修类型
export type RepairType =
  | 'cold_migration'
  | 'config_change'
  | 'firmware_update'
  | 'hardware_fix'
  | 'restart'
  | 'software_fix';

// 工作流阶段类型
export type WorkflowStage =
  | 'accept_ticket'
  | 'cancel'
  | 'complete_repair'
  | 'complete_verification'
  | 'customer_approval'
  | 'repair_selection'
  | 'start_repair'
  | 'summary'
  | 'verification';

// 故障工单基本信息
export interface FaultTicket {
  id: number;
  ticketNo: string;
  title?: string;
  deviceSN: string;
  deviceID?: number;
  faultType: FaultType;
  faultDescription: string;
  symptom: string;
  slotPosition?: string;
  priority: Priority;
  source: string;
  status: FaultTicketStatus;
  repairTicketID?: number;
  assignedTo?: string;
  assignedToName?: string;
  reporterID?: number;
  reporterName?: string;
  acknowledgeTime?: string;
  assignmentTime?: string;
  customerApprovalTime?: string;
  expectedFixTime?: string;
  actualFixTime?: string;
  verificationStartTime?: string;
  verificationEndTime?: string;
  closeTime?: string;
  creationTime: string;
  updateTime?: string;
  businessImpact?: string;
  faultSummary?: string;
  repairMethod?: string;
  preventionMeasures?: string;
  remarks?: string;
  componentSN?: string;
  componentType?: string;
  require_approval?: boolean;
  count_in_sla: boolean;
  is_frequent_fault?: boolean;
  resource_identifier: string;
  fault_detail_type?: string;
  response_duration?: number;
  is_duplicate_fault?: boolean;
  is_false_alarm?: boolean;
  related_ticket_id?: number;

  // 使用导入的Resource接口
  resource?: Resource;
}

// 列表请求参数
export interface FaultTicketListParams {
  pageSize: number;
  page: number;
  query?: string;
  title?: string;
  status?: FaultTicketStatus;
  priority?: Priority;
  ticketNo?: string;
  deviceSN?: string;
  faultType?: FaultType;
  fault_detail_type?: string;
  resource_identifier?: string;
  reporterName?: string;
  assignedTo?: string;
  creationTimeStart?: string;
  creationTimeEnd?: string;
}

// 列表响应
export interface FaultTicketListResponse {
  list: FaultTicket[];
  total: number;
}

// 工单历史记录
export interface FaultTicketHistory {
  id: number;
  fault_ticket_id: number;
  previous_status: FaultTicketStatus;
  new_status: FaultTicketStatus;
  operation_time: string;
  operator_id: number;
  operator_name: string;
  activity_category?: string;
  duration?: number;
  is_sla_pause?: boolean;
  pause_reason?: string;
  remarks?: string;
}

// 状态转换请求参数
export interface TransitionRequest {
  stage: WorkflowStage;
  status?: FaultTicketStatus;
  comments?: string;
  data?: Record<string, any>;
}

// 维修选择
export interface RepairSelection {
  id: number;
  ticket_id: number;
  repair_type: RepairType;
  fault_detail_type: string;
  slot_position: string;
  diagnosis: string;
  comments?: string;
  operator_id: number;
  operator_name: string;
  created_at: string;
  updated_at: string;
}

// 客户审批
export interface CustomerApproval {
  id: number;
  ticket_id: number;
  status: 'approved' | 'rejected';
  comments?: string;
  customer_id: number;
  customer_name: string;
  response_time: string;
  created_at: string;
  updated_at: string;
}

// 验证记录
export interface Verification {
  id: number;
  ticket_id: number;
  success: boolean;
  comments: string;
  verification_time: string;
  operator_id: number;
  operator_name: string;
  created_at: string;
  updated_at: string;
}

// 创建故障工单请求
export interface CreateFaultTicketRequest {
  deviceSN: string;
  title?: string;
  faultType: FaultType;
  priority: Priority;
  source?: string;
  resource_identifier?: string;
  faultDescription?: string;
  creationTime?: string;
  remarks?: string;
}

// 分配工程师请求
export interface AssignFaultTicketRequest {
  engineerID: number;
  expectedFixTime?: string;
}

// 关闭工单请求
export interface CloseFaultTicketRequest {
  summary: string;
  repair_method?: string;
  prevention_measures?: string;
  fault_detail_type?: string;
  count_in_sla?: boolean;
  creation_time?: string;
}

// 获取故障工单列表
export function getFaultTicketTableApi(params: FaultTicketListParams) {
  return requestClient.get<FaultTicketListResponse>(baseUrl, {
    params,
  });
}

// 获取故障工单详情
export function getFaultTicketDetailApi(id: number) {
  return requestClient.get<FaultTicket>(`${baseUrl}/${id}`);
}

// 创建故障工单
export function addFaultTicketApi(data: CreateFaultTicketRequest) {
  return requestClient.post<{ id: number; ticket_no: string }>(baseUrl, data);
}

// 更新故障工单
export function editFaultTicketApi(id: number, data: Partial<FaultTicket>) {
  return requestClient.put(`${baseUrl}/${id}`, data);
}

// 更新故障工单部分字段 (安全版本)
export function updateFaultTicketFieldsApi(
  id: number,
  data: Record<string, any>,
) {
  return requestClient.put(`${baseUrl}/${id}/fields`, data);
}

// 分配工程师
export function assignFaultTicketApi(
  id: number,
  data: AssignFaultTicketRequest,
) {
  return requestClient.put(`${baseUrl}/${id}/assign`, data);
}

// 状态转换与工作流阶段触发
export function transitionFaultTicketApi(id: number, data: TransitionRequest) {
  return requestClient.put(`${baseUrl}/${id}/transition`, data);
}

// 获取状态历史
export function getFaultTicketHistoryApi(id: number) {
  return requestClient.get<FaultTicketHistory[]>(`${baseUrl}/${id}/history`);
}

// 获取维修选择
export function getRepairSelectionApi(id: number) {
  return requestClient.get<RepairSelection>(
    `/ticket/workflow/tickets/${id}/repair-selection`,
  );
}

// 获取客户审批
export function getCustomerApprovalApi(id: number) {
  return requestClient.get<CustomerApproval>(
    `/ticket/workflow/tickets/${id}/customer-approval`,
  );
}

// 获取验证记录
export function getVerificationApi(id: number) {
  return requestClient.get<Verification>(
    `/ticket/workflow/tickets/${id}/verification`,
  );
}

// 关闭工单
export function closeFaultTicketApi(id: number, data: CloseFaultTicketRequest) {
  return requestClient.put(`${baseUrl}/${id}/close`, data);
}

// 获取等待手动触发的阶段
export function getWaitingStagesApi(id: number) {
  return requestClient.get<WorkflowStage[]>(
    `/ticket/workflow/waiting-stages/${id}`,
  );
}

// 手动触发工作流操作
export function triggerManualActionApi(id: number, data: TransitionRequest) {
  return requestClient.post(`/ticket/workflow/trigger/${id}`, data);
}

// 获取设备本月故障次数
export interface DeviceFaultCountResponse {
  device_sn: string;
  month: string;
  month_count: number;
  week: string;
  week_count: number;
}

export function getDeviceMonthlyFaultCountApi(sn: string) {
  return requestClient.get<DeviceFaultCountResponse>(
    `${baseUrl}/device/${sn}/fault-count`,
  );
}

// 冷迁移记录
export interface ColdMigration {
  id: number;
  ticket_id: number;
  fault_device_sn: string;
  backup_device_sn: string;
  tenant_ip: string;
  status: string;
  execution_time: string;
  duration: number;
  operator_id: number;
  operator_name: string;
  comments: string;
  created_at: string;
  updated_at: string;
}

// 获取冷迁移记录
export function getColdMigrationApi(id: number) {
  return requestClient.get<ColdMigration>(
    `/ticket/workflow/tickets/${id}/cold-migration`,
  );
}

// 获取冷迁移列表
export interface ColdMigrationListParams {
  page: number;
  page_size: number;
  fault_device_sn?: string;
  backup_device_sn?: string;
  status?: string;
}

export interface ColdMigrationListResponse {
  list: ColdMigration[];
  total: number;
  page: number;
  page_size: number;
}

export function listColdMigrationsApi(params: ColdMigrationListParams) {
  return requestClient.get<ColdMigrationListResponse>(
    `/ticket/workflow/cold-migrations`,
    { params },
  );
}

/** 入室申请单列表 */
export function listEntryTicketApi(params: ListEntryTicketReq) {
  return requestClient.get<EntryTicketListRes>(`/ticket/entry-tickets`, {
    params,
  });
}

/** 入室申请 */
export function addEntryTicketApi(params: PostEntryTicketReq) {
  return requestClient.post<{ id: number }>(`/ticket/entry-tickets`, params);
}

/** 入室申请详情 */
export function getEntryTicketApi(id: string) {
  return requestClient.get<EntryTicketRes>(`/ticket/entry-tickets/${id}`);
}

/** 入室申请审批 */
export function approveEntryTicketApi(
  id: string,
  params: ApprovalEntryTicketReq,
) {
  return requestClient.put(`/ticket/entry-tickets/${id}/transition`, params);
}

/** 当日入室人员 */
export function listEntryTicketsPersonsApi(params: ListEntryPersonsReq) {
  return requestClient.get<EntryPersonsRes[]>('/ticket/entry-tickets/person', {
    params,
  });
}

/** 入室申请历史 */
export function historyEntryTicketsApi(id: string) {
  return requestClient.get<HistoryEntryTicketsRes[]>(
    `/ticket/entry-tickets/${id}/history`,
  );
}

/** 更新入室人员信息 */
export function updateEntryTicketPersonsApi(
  id: string,
  data: UpdateEntryPersonReq,
) {
  return requestClient.put(`/ticket/entry-tickets/person/${id}/fields`, data);
}

/** 创建入室人员 */
export function createEntryTicketPersonsApi(data: EntryPersonReq[]) {
  return requestClient.post(`/ticket/entry-tickets/person/create`, data);
}
