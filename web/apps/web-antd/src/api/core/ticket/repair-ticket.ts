import type { Resource } from '../cmdb/asset/device';

import type { FaultTicket } from '#/api/core/ticket/fault-ticket';

import { requestClient } from '#/api/request';

// 基础路径
const baseUrl = '/ticket/repair-tickets';

// 维修单状态类型
export type RepairTicketStatus =
  | 'assigned'
  | 'cancelled'
  | 'completed'
  | 'failed'
  | 'hardware_replace_completed'
  | 'hardware_replace_failed'
  | 'in_progress'
  | 'replacing_hardware'
  | 'restart_completed'
  | 'restart_failed'
  | 'restarting'
  | 'waiting_accept'
  | 'waiting_authorization'
  | 'waiting_verification';

// 维修类型
export type RepairType = 'hardware_fix' | 'restart';

// 维修单基本信息
export interface RepairTicket {
  id: number;
  ticket_no: string;
  fault_ticket_id: number;
  status: RepairTicketStatus;
  repair_type: RepairType;
  fault_ticket: FaultTicket;
  assigned_engineer_id: number;
  assigned_engineer_name: string;
  created_time: string;
  assigned_time?: string;
  start_time?: string;
  arrive_time?: string;
  hardware_replace_start?: string;
  hardware_replace_end?: string;
  software_config_start?: string;
  software_config_end?: string;
  testing_start?: string;
  testing_end?: string;
  complete_time?: string;
  authorization_time?: string;
  verification_start_time?: string;
  hardware_operation_duration?: number;
  software_operation_duration?: number;
  total_repair_duration?: number;
  waiting_duration?: number;
  spare_id?: number;
  spare_sn?: string;
  spare_type?: string;
  spare_model?: string;
  spare_machine_id?: number;
  spare_machine_sn?: string;
  spare_machine_model?: string;
  repair_steps?: string;
  solution?: string;
  repair_result?: string;
  verification_result?: 'failed' | 'passed' | 'pending';
  verification_description?: string;
  is_first_attempt: boolean;
  attempt_number: number;

  // 添加机柜相关信息
  cabinet_id?: number;
  cabinet_name?: string;
  cabinet_location?: string;
  rack_position?: string;

  // 添加资源相关信息
  resource?: Resource;
}

// 列表请求参数
export interface RepairTicketListParams {
  pageSize: number;
  page: number;
  query?: string;
  status?: RepairTicketStatus;
  repair_type?: RepairType;
  ticket_no?: string;
  fault_ticket_id?: number;
  assigned_engineer_id?: number;
}

// 列表响应
export interface RepairTicketListResponse {
  list: RepairTicket[];
  total: number;
}

// 维修单历史记录
export interface RepairTicketHistory {
  id: number;
  repair_ticket_id: number;
  previous_status: RepairTicketStatus;
  new_status: RepairTicketStatus;
  operation_time: string;
  operator_id: number;
  operator_name: string;
  activity_category?: string;
  duration?: number;
  remarks?: string;
}

// 验证记录
export interface RepairVerification {
  id: number;
  repair_ticket_id: number;
  success: boolean;
  comments: string;
  verification_time: string;
  operator_id: number;
  operator_name: string;
  created_at: string;
  updated_at: string;
}

// 创建维修单请求
export interface CreateRepairTicketRequest {
  fault_ticket_id: number;
  repair_type: RepairType;
  spare_id?: number;
  comments?: string;
}

// 接单请求
export interface TakeRepairTicketRequest {
  engineer_id: number;
  comments?: string;
}

// 状态转换请求参数
export interface TransitionRequest {
  stage: string;
  status: string;
  comments?: string;
  data?: Record<string, any>;
}

// 完成维修请求
export interface CompleteRepairRequest {
  repair_result: string;
  solution: string;
  repair_steps: string;
}

// 验证维修请求
export interface VerifyRepairRequest {
  success: boolean;
  comments: string;
}

// 获取维修单列表
export function getRepairTicketTableApi(params: RepairTicketListParams) {
  return requestClient.get<RepairTicketListResponse>(baseUrl, {
    params,
  });
}

// 获取维修单详情
export function getRepairTicketDetailApi(id: number) {
  return requestClient.get<RepairTicket>(`${baseUrl}/${id}`);
}

// 创建维修单
export function addRepairTicketApi(data: CreateRepairTicketRequest) {
  return requestClient.post<{ id: number; ticket_no: string }>(baseUrl, data);
}

// 更新维修单
export function editRepairTicketApi(id: number, data: Partial<RepairTicket>) {
  return requestClient.put(`${baseUrl}/${id}`, data);
}

// 工程师接单
export function engineerTakeRepairTicketApi(
  id: number,
  comments: string = '工程师接单处理',
) {
  return transitionRepairTicketApi(id, {
    stage: 'engineer_take',
    status: 'assigned',
    comments,
    data: {},
  });
}

// 开始维修
export function startRepairApi(id: number, plannedActions: string = '') {
  return transitionRepairTicketApi(id, {
    stage: 'start_repair',
    status: 'in_progress',
    comments: '开始执行维修工作',
    data: {
      planned_actions: plannedActions,
    },
  });
}

// 到达现场
export function arriveOnSiteApi(id: number, location: string = '') {
  return transitionRepairTicketApi(id, {
    stage: 'arrive_on_site',
    status: 'in_progress',
    comments: '工程师已到达现场',
    data: {
      location,
    },
  });
}

// 开始硬件更换
export function startHardwareReplaceApi(
  id: number,
  componentType: string = '',
  componentModel: string = '',
) {
  return transitionRepairTicketApi(id, {
    stage: 'start_hardware_replace',
    status: 'replacing_hardware',
    comments: `开始更换${componentType || '硬件'}`,
    data: {
      component_type: componentType,
      component_model: componentModel,
    },
  });
}

// 完成硬件更换
export function completeHardwareReplaceApi(
  id: number,
  spareId: number,
  newPartSN: string = '',
  oldPartSN: string = '',
) {
  return transitionRepairTicketApi(id, {
    stage: 'complete_hardware_replace',
    status: 'hardware_replace_completed',
    comments: '硬件更换完成',
    data: {
      spare_id: spareId,
      new_part_sn: newPartSN,
      old_part_sn: oldPartSN,
      success: true,
    },
  });
}

// 硬件更换失败
export function failHardwareReplaceApi(id: number, reason: string) {
  return transitionRepairTicketApi(id, {
    stage: 'complete_hardware_replace',
    status: 'hardware_replace_failed',
    comments: `硬件更换失败: ${reason}`,
    data: {
      success: false,
      details: reason,
    },
  });
}

// 完成维修
export function completeRepairApi(id: number, data: CompleteRepairRequest) {
  return transitionRepairTicketApi(id, {
    stage: 'complete_repair',
    status: 'waiting_verification',
    comments: '维修任务已完成',
    data: {
      repair_result: data.repair_result,
      solution: data.solution,
      repair_steps: data.repair_steps,
      success: true,
    },
  });
}

// 维修验证
export function verifyRepairApi(id: number, data: VerifyRepairRequest) {
  return requestClient.put(`${baseUrl}/${id}/verify`, {
    success: data.success,
    comments: data.comments,
  });
}

// 获取状态历史
export function getRepairTicketHistoryApi(id: number) {
  return requestClient.get<RepairTicketHistory[]>(`${baseUrl}/${id}/history`);
}

// 获取验证记录
export function getRepairVerificationApi(id: number) {
  return requestClient.get<RepairVerification>(`${baseUrl}/${id}/verification`);
}

// 授权维修单
export function authorizeRepairTicketApi(id: number, comments?: string) {
  return requestClient.put(`${baseUrl}/${id}/authorize`, { comments });
}

// 工作流触发
export function triggerRepairWorkflowApi(id: number, data: TransitionRequest) {
  return requestClient.put(`${baseUrl}/${id}/trigger`, data);
}

// 获取可接单的维修单列表
export function getAvailableRepairTicketsApi(page: number, pageSize: number) {
  return requestClient.get<RepairTicketListResponse>(`${baseUrl}/available`, {
    params: { page, pageSize },
  });
}

// 统一的状态转换接口 - 可以用于所有状态转换
export function transitionRepairTicketApi(id: number, data: TransitionRequest) {
  return requestClient.put(`${baseUrl}/${id}/transition`, data);
}
