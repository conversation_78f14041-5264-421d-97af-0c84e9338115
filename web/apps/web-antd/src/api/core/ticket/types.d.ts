import type { Warehouse } from '#/api/core/cmdb/asset/warehouse';

/** 入室人员信息 */
export interface EntryPersonRes {
  /** 姓名 */
  name: string;
  /** 身份证 */
  identification_number: string;
  /** 手机号 */
  telephone: string;
  /** 人员类型 */
  person_type: string;
  /** 公司 */
  company: string;
  /** 是否禁用 */
  status: 'disabled' | 'enabled';
}
interface product {
  id: number;
  brand: string;
  model: string;
  pn: string;
  product_category: string;
  spec: string;
}

interface template {
  id: number;
  template_name: string;
  cpu_model: string;
  memory_capacity: number;
  gpu_model: string;
  disk_type: string;
  template_category: string;
}

export interface OutboundInfo {
  // 基础信息
  outbound_type?: string; // 资产类型，例如:“配件”丨"整机"
  device_sn?: string; // 整机设备SN
  component_sn?: string; // 配件SN
  amount: number; // 数量
  // 备件信息
  product_id?: number; // 规格ID
  product?: product;
  // 整机设备信息
  template_id?: number; // 模板ID
  template?: template;
}

export interface OutboundDetail {
  id: number;
  // 基础信息
  outbound_type?: string; // 资产类型，例如:“配件"丨"整机"
  device_sn?: string; // 整机设备SN
  component_sn?: string; // 配件SN
  // 备件信息
  product_id?: number; // 规格ID
  product?: product;
  // 整机设备信息
  template_id?: number; // 模板ID
  template?: template;
}

export interface EntryPersonsRes {
  list: EntryPersonRes[];
  total: number;
}

/** 入室人员列表请求 */
export interface ListEntryPersonsReq {
  page: number;
  pageSize: number;
}
/** 入室申请列表单相应条数据 */
export interface EntryTicketRes {
  /** 工单编号 */
  ticketNo: string;
  /** 用于查询详情 */
  id: number;
  /** 入室人员列表 */
  entryPersons: string;
  /** 审批状态 */
  status: string;
  /** 入室开始时间 */
  entryStartTime: string;
  /** 入室结束时间 */
  entryEndTime: string;
  /** 申请人 */
  applicantName: string;
  /** 申请日期 */
  creationTime: string;
  /** 申请原因 */
  reason: string;
  dataCenterName: string;
  roomName: string;
  isCarryEquipment: boolean;
  isOutSource: boolean;
}
/** 入室申请列表相应 */
export interface EntryTicketListRes {
  list: EntryTicketRes[];
  total: number;
}

/** 入室申请请求 */
export interface PostEntryTicketReq {
  /** 入室人员列表 */
  persons: EntryPersonRes[];
  /** 入室开始时间 */
  entry_start_time: number;
  /** 入室结束时间 */
  entry_end_time: number;
  /** 原因 */
  reason: string;
  /** 是否携带设备 */
  carryEquipment: string;
  /** 是否包含外派员工 */
  isOutSource: boolean;
  /** 数据中心 */
  dataCenterName: string;
  /** 目标机房 */
  roomName: string;
}

export type APPROVE_STATUS =
  | 'approved'
  | 'rejected'
  | 'waiting_approval'
  | 'waiting_second_approval';

/** 入室申请审批请求 */
export interface ApprovalEntryTicketReq {
  stage: string;
  /** 审批状态 */
  status: APPROVE_STATUS;
  /** 审批意见 */
  comments: string;
  /** 相关数据 */
  data: {
    status: APPROVE_STATUS;
  };
}

/** 入室申请列表请求 */
export interface ListEntryTicketReq {
  page: number;
  pageSize: number;
  /** 工单编号 */
  query?: string;
  /** 审批状态 */
  status?: string;
  /** 入室开始时间 */
  creationTimeStart?: string;
  /** 入室结束时间 */
  creationTimeEnd?: string;
}

export interface HistoryEntryTicketsRes {
  /** 入室单ID */
  entry_ticket_id: string;
  /** 先前状态 */
  previous_status: string;
  /** 新状态 */
  new_status: string;
  /** 操作人ID */
  operator_id: string;
  /** 操作人姓名 */
  operator_name: string;
  /** 操作时间 */
  operation_time: string;
  /** 状态持续时间 */
  duration: string;
  /** 活动类别 */
  activity_category: string;
  /** 备注 */
  remarks: string;
}

export interface EntryPersonReq {
  name: string;
  identification_number: string;
  telephone: string;
  person_type: string;
  company: string;
  status: string;
  entry_start_time: string;
  entry_end_time: string;
  dataCenterName: string;
  roomName: string;
}

export interface UpdateEntryPersonReq {
  status: string;
}

export interface ListOutboundTicketReq {
  page?: number;
  pageSize?: number;
  query?: string;
  stage?: string;
  project?: string;
  outboundType?: string;
  outboundReason?: string;
}

export interface OutboundTicketRes {
  id: string;
  /** 工单编号 */
  ticketNo: string;
  status: string;
  stage: string;
  reporterName: string;
  order_number: string;
  source_warehouse_id: string;
  source_location: string;
  dest_warehouse_id: string;
  dest_location: string;
  buyer_info: string;
  shipmentTime: string;
  spares: string;
  project: string;
  repair_ticket: Array<{
    assigned_engineer_name: string;
    id: string;
    repair_result: string;
    repair_type: string;
    status: string;
    ticket_no: string;
  }>;

  /** 创建时间 */
  created_at: string;
  /** 更新时间 */
  updated_at: string;
  /** 创建人 */
  /** 资产类型 */
  outbound_type: string;
  /** 出库原因 */
  outbound_reason: string;
  /** 出库位置 */

  /** 当前环节 */
  currentWaitingStage: string;
}

export interface OutboundTicketResV2 {
  id: string;
  /** 工单编号 */
  ticketNo: string;
  status: string;
  reporterName: string;
  order_number: string;
  source_warehouse_id: string;
  source_location: string;
  dest_warehouse_id: string;
  dest_location: string;
  buyer_info: string;
  shipmentTime: string;
  spares: string;
  info: OutboundInfo[];
  details: OutboundDetail[];
  repair_ticket: Array<{
    assigned_engineer_name: string;
    id: string;
    repair_result: string;
    repair_type: string;
    status: string;
    ticket_no: string;
  }>;

  /** 创建时间 */
  created_at: string;
  /** 更新时间 */
  /** 创建人 */
  /** 资产类型 */
  outbound_type: string;
  /** 出库原因 */
  outbound_reason: string;
  /** 出库位置 */

  /** 当前环节 */
  currentWaitingStage: string;
}

/** 出库单历史记录 */
export interface OutboundTicketHistoryRes {
  /** 入室单ID */
  id: string;
  /** 阶段 */
  stage: string;
  /** 先前状态 */
  previous_status: string;
  /** 新状态 */
  new_status: string;
  /** 操作人ID */
  operator_id: string;
  /** 操作人姓名 */
  operator_name: string;
  /** 操作时间 */
  operation_time: string;
  /** 状态持续时间 */
  duration: string;
  /** 活动类别 */
  activity_category: string;
  /** 备注 */
  remarks: string;
}

export interface ListOutboundTicketRes {
  list: OutboundTicketRes[];
  total: number;
}

interface Spares {
  pn: string;
  number: number;
}

export interface CreateOutboundTicketReq {
  project: string;
  outbound_title: string;
  outbound_type: string;
  outbound_reason: string;
  spares: Spares;
  repair_ticket_id?: string;
  order_number?: string;
  buyer_info?: string;
  shipmentTime?: string;
  dest_warehouse_id?: string;
  dest_location?: string;
}

export interface CreateOutboundTicketReqV2 {
  project: string;
  outbound_title: string;
  outbound_type: string;
  outbound_reason: string;
  info: OutboundInfo;
  repair_ticket_id?: number;
  device_sn: string;
  order_number?: string;
  buyer_info?: string;
  shipmentTime?: string;
  source_warehouse_id?: string;
  source_location?: string;
  dest_warehouse_id?: string;
  dest_location?: string;
}

export interface ApproveOutboundTicketReq {
  stage: string;
  status: string;
  comments?: string;
  data?: Record<string, any>;
}

export interface CreateInboundTicketReq {
  new_inbound_id: number;
  new_inbound_no: string;
  submitter_time: string;
}

export interface CreateInboundRepairTicketReq {
  inbound_no: string;
}

export interface InboundDismantledTicketReq {
  inbound_no: string;
}

/** 入库审批请求参数 */
export interface ApproveInboundTicketReq {
  status: string;
  required_approval: boolean;
  required_verified: boolean;
  comments?: string;
  data?: Record<string, any>;
}

export interface InboundInfo {
  id?: number;
  inbound_ticket_id?: number;
  inbound_no?: string;
  asset_type?: string;
  product_id?: number;
  template_id?: number;
  template?: template;
  amount: number;
}

export interface InboundDetail {
  id: number;
  inbound_ticket_id?: number;
  inbound_no: string;
  asset_type?: string;
  product_id?: number;
  template_id?: number;
  asset_type?: string;
  device_sn?: string;
  component_sn?: string;
  component_state?: string;
  need_return?: boolean;
  return_repair_type?: string;
  pre_component_sn?: string;
}

export interface InboundTicketRes {
  id: number;
  inbound_no: string;
  inbound_type: string;
  inbound_reason: string;
  inbound_title: string;
  create_by: string;
  create_id: number;
  stage: string;
  status: string;
  tracking_info?: string;
  may_arrive_at?: string;
  warehouse_id: number;
  warehouse: Warehouse;
  info?: InboundInfo[];
  details?: InboundDetail[];
  created_at: string;
  purchase_no?: string;
  supplier_name?: string;
  may_arrive_at?: string;
  need_return?: boolean;
  valid?: boolean;
}

export interface InboundTicketHistoryRes {
  /** ID */
  id: number;
  /** 创建时间 */
  created_at: string;
  /** 更新时间 */
  updated_at: string;
  /** 工单ID */
  inbound_ticket_id: number;
  /** 订单号 */
  inbound_no: string;
  /** 工单阶段 */
  stage: string;
  /** 状态 */
  status: string;
  /** 操作人ID */
  operator_id: number;
  /** 操作人姓名 */
  operator_name: string;
  /** 操作备注 */
  comment: string;
}

export interface TransitionInboundTicketReq {
  inbound_type: string;
  status: string;
  comments: string;
  data?: Record<string, any>;
}
