// import type { RouteRecordRaw } from 'vue-router';

// import { $t } from '#/locales';

// const routes: RouteRecordRaw[] = [
//   {
//     meta: {
//       icon: 'mdi:finance',
//       order: 6,
//       title: $t('page.financialManagement.title'),
//       authority: ['super', 'admin'],
//     },
//     name: 'FinancialManagement',
//     path: '/financial-management',
//     children: [
//       {
//         name: 'FinancialDashboard',
//         path: '/financial-dashboard',
//         component: () =>
//           import('#/views/financialManagement/financialDashboard/index.vue'),
//         meta: {
//           icon: 'mdi:view-dashboard',
//           title: $t('page.financialManagement.financialDashboard'),
//           authority: ['super', 'admin'],
//         },
//       },
//       {
//         name: 'ProductManagement',
//         path: '/product-management',
//         component: () =>
//           import('#/views/financialManagement/productManagement/index.vue'),
//         meta: {
//           icon: 'mdi:package-variant',
//           title: $t('page.financialManagement.productManagement'),
//           authority: ['super', 'admin'],
//         },
//       },
//       {
//         name: 'PricingManagement',
//         path: '/pricing-management',
//         component: () =>
//           import('#/views/financialManagement/pricingManagement/index.vue'),
//         meta: {
//           icon: 'mdi:currency-usd',
//           title: $t('page.financialManagement.pricingManagement'),
//           authority: ['super', 'admin'],
//         },
//       },
//       {
//         name: 'ResourceActivation',
//         path: '/resource-activation',
//         component: () =>
//           import('#/views/financialManagement/resourceActivation/index.vue'),
//         meta: {
//           icon: 'mdi:power-plug',
//           title: $t('page.financialManagement.resourceActivation'),
//           authority: ['super', 'admin'],
//         },
//       },
//       {
//         name: 'ResourceBilling',
//         path: '/resource-billing',
//         component: () =>
//           import('#/views/financialManagement/resourceBilling/index.vue'),
//         meta: {
//           icon: 'mdi:file-document-outline',
//           title: $t('page.financialManagement.resourceBilling'),
//           authority: ['super', 'admin'],
//         },
//       },
//       {
//         name: 'AccountStatement',
//         path: '/account-statement',
//         component: () =>
//           import('#/views/financialManagement/accountStatement/index.vue'),
//         meta: {
//           icon: 'mdi:file-document-multiple',
//           title: $t('page.financialManagement.accountStatement'),
//           authority: ['super', 'admin'],
//         },
//       },
//       {
//         name: 'BillingDetails',
//         path: '/billing-details',
//         component: () =>
//           import('#/views/financialManagement/billingDetails/index.vue'),
//         meta: {
//           icon: 'mdi:receipt-text',
//           title: $t('page.financialManagement.billingDetails'),
//           authority: ['super', 'admin'],
//         },
//       },
//     ],
//   },
// ];

// export default routes;
