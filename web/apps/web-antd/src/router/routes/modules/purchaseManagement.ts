// import type { RouteRecordRaw } from 'vue-router';

// import { $t } from '#/locales';

// const routes: RouteRecordRaw[] = [
//   {
//     meta: {
//       icon: 'mdi:cart',
//       order: 7,
//       title: $t('page.purchaseManagement.title'),
//       authority: ['super', 'admin'],
//     },
//     name: 'PurchaseManagement',
//     path: '/purchase-management',
//     children: [
//       {
//         name: 'PaymentRequest',
//         path: '/payment-request',
//         component: () =>
//           import('#/views/purchaseManagement/payment_request/index.vue'),
//         meta: {
//           icon: 'mdi:cash-multiple',
//           title: '付款申请',
//           authority: ['super', 'admin'],
//         },
//       },
//       {
//         name: 'PaymentRequestDetail',
//         path: '/payment-request/:id',
//         component: () =>
//           import('#/views/purchaseManagement/payment_request/detail.vue'),
//         meta: {
//           hideInMenu: true,
//           title: '付款申请详情',
//           authority: ['super', 'admin'],
//         },
//       },
//       {
//         name: 'WorkflowDemo',
//         path: '/workflow-demo',
//         component: () =>
//           import(
//             '#/views/purchaseManagement/payment_request/workflow-demo.vue'
//           ),
//         meta: {
//           icon: 'mdi:workflow',
//           title: '流程图演示',
//           authority: ['super', 'admin'],
//         },
//       },
//       {
//         name: 'ContractManagement',
//         path: '/contract-management',
//         component: () =>
//           import('#/views/purchaseManagement/contractManagement/index.vue'),
//         meta: {
//           icon: 'mdi:file-document',
//           title: $t('page.purchaseManagement.contractManagement'),
//           authority: ['super', 'admin'],
//         },
//       },
//       {
//         name: 'ResourceResale',
//         path: '/resource-resale',
//         component: () =>
//           import('#/views/purchaseManagement/resourceResale/index.vue'),
//         meta: {
//           icon: 'mdi:transfer',
//           title: $t('page.purchaseManagement.resourceResale'),
//           authority: ['super', 'admin'],
//         },
//       },
//       {
//         name: 'ArrivalManagement',
//         path: '/arrival-management',
//         component: () =>
//           import('#/views/purchaseManagement/arrival_management/index.vue'),
//         meta: {
//           icon: 'mdi:truck-delivery',
//           title: $t('page.purchaseManagement.arrivalManagement'),
//           authority: ['super', 'admin'],
//         },
//       },
//       {
//         name: 'ArrivalManagementDetail',
//         path: '/arrival-management/detail/:id',
//         component: () =>
//           import('#/views/purchaseManagement/arrival_management/detail.vue'),
//         meta: {
//           hideInMenu: true,
//           title: $t('page.purchaseManagement.arrivalManagementDetail'),
//           authority: ['super', 'admin'],
//         },
//       },
//     ],
//   },
// ];

// export default routes;
