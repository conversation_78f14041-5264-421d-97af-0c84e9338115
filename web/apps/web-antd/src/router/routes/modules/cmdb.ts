// import type { RouteRecordRaw } from 'vue-router';

// import { $t } from '#/locales';

// const routes: RouteRecordRaw[] = [
//   {
//     meta: {
//       icon: 'material-symbols:database-search-outline-rounded',
//       order: 3,
//       title: $t('page.cmdb.title'),
//       authority: ['super', 'admin', 'hardware_repair_manager', 'asset_manager'],
//     },
//     name: 'Cmdb',
//     path: '/cmdb',
//     children: [
//       {
//         name: 'ServerInfo',
//         path: '/server-info',
//         component: () => import('#/views/cmdb/serverInfo/index.vue'),
//         meta: {
//           icon: 'material-symbols:desktop-cloud-stack-outline',
//           title: $t('page.cmdb.serverInfo'),
//           authority: [
//             'super',
//             'admin',
//             'hardware_repair_manager',
//             'asset_manager',
//           ],
//         },
//       },
//       {
//         name: 'NetDeviceInfo',
//         path: '/net-device-info',
//         component: () => import('#/views/cmdb/netDeviceInfo/index.vue'),
//         meta: {
//           icon: 'material-symbols:devices-outline',
//           title: $t('page.cmdb.netDeviceInfo'),
//           authority: [
//             'super',
//             'admin',
//             'hardware_repair_manager',
//             'asset_manager',
//           ],
//         },
//       },
//       {
//         name: 'RegionInfo',
//         path: '/region-info',
//         component: () => import('#/views/cmdb/regionInfo/index.vue'),
//         meta: {
//           affixTab: true,
//           icon: 'mdi:map-marker-radius',
//           title: $t('page.cmdb.regionInfo'),
//           authority: [
//             'super',
//             'admin',
//             'hardware_repair_manager',
//             'asset_manager',
//           ],
//         },
//       },
//       {
//         name: 'AzInfo',
//         path: '/az-info',
//         component: () => import('#/views/cmdb/azInfo/index.vue'),
//         meta: {
//           icon: 'mdi:server-network',
//           title: $t('page.cmdb.azInfo'),
//           authority: [
//             'super',
//             'admin',
//             'hardware_repair_manager',
//             'asset_manager',
//           ],
//         },
//       },
//       {
//         name: 'IdcInfo',
//         path: '/idc-info',
//         component: () => import('#/views/cmdb/idcInfo/index.vue'),
//         meta: {
//           icon: 'mdi:office-building',
//           title: $t('page.cmdb.idcInfo'),
//           authority: [
//             'super',
//             'admin',
//             'hardware_repair_manager',
//             'asset_manager',
//           ],
//         },
//       },
//       {
//         name: 'IdcRoomInfo',
//         path: '/idc-room-info',
//         component: () => import('#/views/cmdb/idcRoomInfo/index.vue'),
//         meta: {
//           icon: 'mdi:door',
//           title: $t('page.cmdb.idcRoomInfo'),
//           authority: [
//             'super',
//             'admin',
//             'hardware_repair_manager',
//             'asset_manager',
//           ],
//         },
//       },
//       {
//         name: 'CabinetInfo',
//         path: '/cabinet-info',
//         component: () => import('#/views/cmdb/cabinetInfo/index.vue'),
//         meta: {
//           icon: 'mdi:server-network',
//           title: $t('page.cmdb.cabinetInfo'),
//         },
//       },
//     ],
//   },
// ];

// export default routes;
