// import type { RouteRecordRaw } from 'vue-router';

// import { $t } from '#/locales';

// const routes: RouteRecordRaw[] = [
//   {
//     meta: {
//       icon: 'mdi:server-plus',
//       title: $t('page.softMaintenance.title'),
//       order: 5,
//       authority: ['super', 'admin'],
//     },
//     name: 'SoftMaintenance',
//     path: '/soft-maintenance',
//     children: [
//       {
//         name: 'FaultReportManagement',
//         path: '/fault-report-management',
//         component: () =>
//           import('#/views/softMaintenance/faultReportManagement/index.vue'),
//         meta: {
//           icon: 'ic:baseline-event-note',
//           title: $t('page.softMaintenance.faultReportManagement'),
//           authority: ['super', 'admin'],
//         },
//       },
//       {
//         name: 'SoftSchedule',
//         path: '/soft-schedule',
//         component: () => import('#/views/softMaintenance/softSchedule/index.vue'),
//         meta: {
//           icon: 'ic:baseline-schedule',
//           title: $t('page.softMaintenance.schedule'),
//           authority: ['super', 'admin'],
//         },
//       },
//     ],
//   },
//   {
//     name: 'FaultReportDetail',
//     path: '/fault-report-management/detail/:id',
//     component: () =>
//       import('#/views/softMaintenance/faultReportManagement/detailPage.vue'),
//     meta: {
//       title: $t('page.softMaintenance.faultReportDetail'),
//       hideInMenu: true,
//       hideBreadcrumb: true,
//       hideChildrenInMenu: true,
//       authority: ['super', 'admin'],
//       currentActiveMenu: '/fault-report-management',
//     },
//   },
// ];

// export default routes;
