// import type { RouteRecordRaw } from 'vue-router';

// import { $t } from '#/locales';

// const routes: RouteRecordRaw[] = [
//   {
//     meta: {
//       icon: 'mdi:chart-bar',
//       order: 8,
//       title: $t('page.bossDashboard.title'),
//       authority: ['super', 'admin'],
//     },
//     name: 'BossDashboard',
//     path: '/boss-dashboard',
//     children: [
//       {
//         name: 'FinancialDashboard',
//         path: '/financial-dashboard',
//         component: () =>
//           import('#/views/bossDashboard/financialDashboard/index.vue'),
//         meta: {
//           icon: 'mdi:cash-multiple',
//           title: $t('page.bossDashboard.financialDashboard'),
//           authority: ['super', 'admin'],
//         },
//       },
//       {
//         name: 'AssetDashboard',
//         path: '/asset-dashboard',
//         component: () =>
//           import('#/views/bossDashboard/assetDashboard/index.vue'),
//         meta: {
//           icon: 'mdi:server',
//           title: $t('page.bossDashboard.assetDashboard'),
//           authority: ['super', 'admin'],
//         },
//       },
//       {
//         name: 'FaultDashboard',
//         path: '/fault-dashboard',
//         component: () =>
//           import('#/views/bossDashboard/faultDashboard/index.vue'),
//         meta: {
//           icon: 'mdi:alert-circle-outline',
//           title: $t('page.bossDashboard.faultDashboard'),
//           authority: ['super', 'admin'],
//         },
//       },
//       {
//         name: 'SlaDashboard',
//         path: '/sla-dashboard',
//         component: () => import('#/views/bossDashboard/slaDashboard/index.vue'),
//         meta: {
//           icon: 'mdi:clock-outline',
//           title: $t('page.bossDashboard.slaDashboard'),
//           authority: ['super', 'admin'],
//         },
//       },
//     ],
//   },
// ];

// export default routes;
