// import type { RouteRecordRaw } from 'vue-router';

// import { $t } from '#/locales';

// const routes: RouteRecordRaw[] = [
//   {
//     meta: {
//       icon: 'ic:baseline-build',
//       title: $t('page.hardwareMaintenance.title'),
//       order: 4,
//       authority: [
//         'super',
//         'admin',
//         'hardware_repair_manager',
//         'hardware_repair_engineer',
//       ],
//     },
//     name: 'HardwareMaintenance',
//     path: '/hardware-maintenance',

//     children: [
//       {
//         name: 'AssetRacking',
//         path: '/asset-racking',
//         component: () =>
//           import('#/views/hardwareMaintenance/assetRacking/index.vue'),
//         meta: {
//           icon: 'ic:sharp-storage',
//           title: $t('page.hardwareMaintenance.assetRacking'),
//           authority: ['super', 'admin', 'hardware_repair_manager'],
//         },
//       },
//       {
//         name: 'HardwareOrder',
//         path: '/hardware-order',
//         component: () =>
//           import('#/views/hardwareMaintenance/hardwareOrder/index.vue'),
//         meta: {
//           icon: 'ic:round-hardware',
//           title: $t('page.hardwareMaintenance.hardwareOrder'),
//           authority: [
//             'super',
//             'admin',
//             'hardware_repair_manager',
//             'hardware_repair_engineer',
//           ],
//         },
//       },
//       {
//         name: 'HardwareOrderCreate',
//         path: '/hardware-order/create',
//         component: () =>
//           import('#/views/hardwareMaintenance/hardwareOrder/createPage.vue'),
//         meta: {
//           title: $t('page.hardwareMaintenance.createHardwareOrder'),
//           hideInMenu: true,
//           authority: [
//             'super',
//             'admin',
//             'hardware_repair_manager',
//             'hardware_repair_engineer',
//           ],
//         },
//       },
//       {
//         name: 'HardwareOrderDetail',
//         path: '/hardware-order/:id',
//         component: () =>
//           import('#/views/hardwareMaintenance/hardwareOrder/detailPage.vue'),
//         meta: {
//           title: $t('page.hardwareMaintenance.hardwareOrderDetail'),
//           hideInMenu: true,
//           authority: [
//             'super',
//             'admin',
//             'hardware_repair_manager',
//             'hardware_repair_engineer',
//           ],
//         },
//       },
//       {
//         name: 'AccessApplication',
//         path: '/access-application',
//         component: () =>
//           import('#/views/hardwareMaintenance/accessApplication/index.vue'),
//         meta: {
//           icon: 'ic:outline-accessibility-new',
//           title: $t('page.hardwareMaintenance.accessApplication'),
//           authority: [
//             'super',
//             'admin',
//             'hardware_repair_manager',
//             'hardware_repair_engineer',
//           ],
//           // hideInMenu: true,
//         },
//       },
//       {
//         name: 'AccessApplicationDetail',
//         path: '/access-application/:id',
//         component: () =>
//           import('#/views/hardwareMaintenance/accessApplication/detail.vue'),
//         meta: {
//           icon: 'ic:round-person',
//           title: $t('page.hardwareMaintenance.accessManagementDetail'),
//           authority: [
//             'super',
//             'admin',
//             'hardware_repair_manager',
//             'hardware_repair_engineer',
//           ],
//           hideInMenu: true,
//         },
//       },
//       {
//         name: 'AccessManagement',
//         path: '/access-management',
//         component: () =>
//           import('#/views/hardwareMaintenance/accessManagement/index.vue'),
//         meta: {
//           icon: 'ic:round-person',
//           title: $t('page.hardwareMaintenance.accessManagement'),
//           authority: [
//             'super',
//             'admin',
//             'hardware_repair_manager',
//             'hardware_repair_engineer',
//           ],
//           // hideInMenu: true,
//         },
//       },
//       {
//         name: 'HardSchedule',
//         path: '/hard-schedule',
//         component: () =>
//            import('#/views/hardwareMaintenance/hardSchedule/index.vue'),
//         meta: {
//           icon: 'ic:baseline-schedule',
//           title: $t('page.hardwareMaintenance.schedule'),
//           authority: ['super', 'admin'],
//         },
//       },

//     ],
//   },
// ];

// export default routes;
