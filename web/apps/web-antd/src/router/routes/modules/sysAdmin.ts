// import type { RouteRecordRaw } from 'vue-router';

// import { $t } from '#/locales';

// const routes: RouteRecordRaw[] = [
//   {
//     meta: {
//       icon: 'material-symbols:settings-outline',
//       keepAlive: true,
//       order: 9999,

//       title: $t('page.systemAdmin.title'),
//     },
//     name: 'SysAdmin',
//     path: '/sysAdmin',
//     children: [
//       {
//         meta: {
//           icon: 'material-symbols:account-box',
//           title: $t('page.systemAdmin.userManagement'),
//           authority: ['super'],
//         },
//         name: 'UserManagement',
//         path: '/user-manage',
//         component: () => import('#/views/sysAdmin/userManage/index.vue'),
//       },
//       {
//         meta: {
//           icon: 'carbon:menu',
//           title: $t('page.systemAdmin.menusManagement'),
//           authority: ['super'],
//         },
//         name: 'MenusManagement',
//         path: '/menus-manage',
//         component: () => import('#/views/sysAdmin/menusManage/index.vue'),
//       },
//       {
//         meta: {
//           icon: 'material-symbols:group',
//           title: $t('page.systemAdmin.deptManagement'),
//           authority: ['super'],
//         },
//         name: 'DeptManagement',
//         path: '/dept-manage',
//         component: () => import('#/views/sysAdmin/deptManage/index.vue'),
//       },
//       {
//         path: 'audit-log',
//         name: 'SystemAuditLog',
//         component: () => import('#/views/system/auditLog/index.vue'),
//         meta: {
//           title: $t('page.systemAdmin.auditLog'),
//           authority: ['super'],
//           icon: 'ant-design:history-outlined',
//         },
//       },
//       {
//         path: 'user-set',
//         name: 'UserSet',
//         component: () => import('#/views/sysAdmin/userSet/index.vue'),
//         meta: {
//           title: $t('page.systemAdmin.userSet'),
//           icon: 'ant-design:setting-outlined',
//         },
//       },
//     ],
//   },
// ];

// export default routes;
