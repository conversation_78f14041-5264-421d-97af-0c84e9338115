// import type { RouteRecordRaw } from 'vue-router';

// import { $t } from '#/locales';

// const routes: RouteRecordRaw[] = [
//   {
//     meta: {
//       icon: 'material-symbols:document-search-outline',
//       title: $t('page.nodeSearch.title'),
//       order: 9999,
//       authority: ['super', 'admin'],
//     },
//     name: 'NodeSearch',
//     path: '/nodeSearch',
//     component: () => import('#/views/nodesearch/index.vue'),
//     children: [],
//   },
// ];

// export default routes;
