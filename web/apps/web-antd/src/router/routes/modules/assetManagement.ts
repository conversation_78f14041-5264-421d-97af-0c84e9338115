// import type { RouteRecordRaw } from 'vue-router';

// import { $t } from '#/locales';

// const routes: RouteRecordRaw[] = [
//   {
//     meta: {
//       icon: 'ic:baseline-assignment',
//       order: 4,
//       title: $t('page.assetManagement.title'),
//       authority: ['super', 'admin', 'asset_manager', 'hardware_repair_manager'],
//     },
//     name: 'AssetManagement',
//     path: '/asset-management',
//     children: [
//       {
//         name: 'AssetStorage',
//         path: '/asset-storage',
//         component: () =>
//           import('#/views/assetManagement/assetStorage/index.vue'),
//         meta: {
//           icon: 'mdi:package-variant-plus',
//           title: $t('page.assetManagement.assetStorage'),
//           authority: [
//             'super',
//             'admin',
//             'asset_manager',
//             'hardware_repair_manager',
//           ],
//           // hideInMenu: true,
//         },
//       },
//       {
//         name: 'AssetStorageDetail',
//         path: '/asset-storage-detail/:id',
//         component: () =>
//           import('#/views/assetManagement/assetStorage/detail.vue'),
//         meta: {
//           icon: 'mdi:package-variant-plus',
//           title: $t('page.assetManagement.assetStorageDetail'),
//           authority: [
//             'super',
//             'admin',
//             'asset_manager',
//             'hardware_repair_manager',
//           ],
//           hideInMenu: true,
//         },
//       },
//       {
//         name: 'AssetOutWarehouse',
//         path: '/asset-out-warehouse',
//         component: () =>
//           import('#/views/assetManagement/assetOutWarehouse/index.vue'),
//         meta: {
//           icon: 'mdi:package-variant-minus',
//           title: $t('page.assetManagement.assetOutWarehouse'),
//           authority: [
//             'super',
//             'admin',
//             'asset_manager',
//             'hardware_repair_manager',
//           ],
//           // hideInMenu: true,
//         },
//       },
//       {
//         name: 'assetOutWarehouseDetail',
//         path: '/asset-out-warehouse-detail/:id',
//         component: () =>
//           import('#/views/assetManagement/assetOutWarehouse/detail.vue'),
//         meta: {
//           icon: 'mdi:package-variant-minus',
//           title: $t('page.assetManagement.assetOutWarehouseDetail'),
//           authority: [
//             'super',
//             'admin',
//             'asset_manager',
//             'hardware_repair_manager',
//           ],
//           hideInMenu: true,
//         },
//       },
//       {
//         name: 'AssetRetirement',
//         path: '/asset-retirement',
//         component: () =>
//           import('#/views/assetManagement/assetRetirement/index.vue'),
//         meta: {
//           icon: 'mdi:delete-variant',
//           title: $t('page.assetManagement.assetRetirement'),
//           authority: [
//             'super',
//             'admin',
//             'asset_manager',
//             'hardware_repair_manager',
//           ],
//           hideInMenu: true,
//         },
//       },
//       {
//         name: 'AssetReallocation',
//         path: '/asset-reallocation',
//         component: () =>
//           import('#/views/assetManagement/assetReallocation/index.vue'),
//         meta: {
//           icon: 'mdi:swap-horizontal-variant',
//           title: $t('page.assetManagement.assetReallocation'),
//           authority: [
//             'super',
//             'admin',
//             'asset_manager',
//             'hardware_repair_manager',
//           ],
//           hideInMenu: true,
//         },
//       },
//       {
//         name: 'ServerPackageInfo',
//         path: '/server-package-info',
//         component: () =>
//           import('#/views/assetManagement/serverPackageInfo/index.vue'),
//         meta: {
//           icon: 'mdi:server-plus',
//           title: $t('page.assetManagement.serverPackageInfo'),
//           authority: [
//             'super',
//             'admin',
//             'asset_manager',
//             'hardware_repair_manager',
//           ],
//         },
//       },
//       {
//         name: 'Specifications',
//         path: '/specifications',
//         component: () =>
//           import('#/views/assetManagement/specifications/index.vue'),
//         meta: {
//           icon: 'mdi:clipboard-list-outline',
//           title: $t('page.assetManagement.specifications'),
//           authority: [
//             'super',
//             'admin',
//             'asset_manager',
//             'hardware_repair_manager',
//           ],
//         },
//       },
//       {
//         name: 'PartNumber',
//         path: '/part-number',
//         component: () => import('#/views/assetManagement/partNumber/index.vue'),
//         meta: {
//           icon: 'mdi:barcode',
//           title: $t('page.assetManagement.partNumber'),
//           authority: [
//             'super',
//             'admin',
//             'asset_manager',
//             'hardware_repair_manager',
//           ],
//           hideInMenu: true,
//         },
//       },
//       {
//         name: 'AccessoryInfo',
//         path: '/accessory-info',
//         component: () =>
//           import('#/views/assetManagement/accessoryInfo/index.vue'),
//         meta: {
//           icon: 'mdi:devices',
//           title: $t('page.assetManagement.accessoryInfo'),
//           authority: [
//             'super',
//             'admin',
//             'asset_manager',
//             'hardware_repair_manager',
//           ],
//         },
//       },
//       {
//         name: 'Warehouse',
//         path: '/warehouse',
//         component: () => import('#/views/assetManagement/warehouse/index.vue'),
//         meta: {
//           icon: 'mdi:warehouse',
//           title: $t('page.assetManagement.warehouse'),
//           authority: [
//             'super',
//             'admin',
//             'asset_manager',
//             'hardware_repair_manager',
//           ],
//         },
//       },
//       {
//         name: 'WarehouseDetail',
//         path: '/warehouse-detail/:id',
//         component: () =>
//           import('#/views/assetManagement/warehouse/warehouseDetail.vue'),
//         meta: {
//           icon: 'mdi:warehouse',
//           title: $t('page.assetManagement.warehouseDetail'),
//           authority: [
//             'super',
//             'admin',
//             'asset_manager',
//             'hardware_repair_manager',
//           ],
//           hideInMenu: true,
//         },
//       },
//       {
//         name: 'Spares',
//         path: '/spares',
//         component: () => import('#/views/assetManagement/spares/index.vue'),
//         meta: {
//           icon: 'mdi:chip',
//           title: $t('page.assetManagement.spares'),
//           authority: [
//             'super',
//             'admin',
//             'asset_manager',
//             'hardware_repair_manager',
//           ],
//         },
//       },
//       {
//         name: 'InventoryList',
//         path: '/inventory-list',
//         component: () =>
//           import('#/views/assetManagement/inventoryList/index.vue'),
//         meta: {
//           icon: 'mdi:clipboard-list',
//           title: $t('page.assetManagement.inventoryList'),
//           authority: [
//             'super',
//             'admin',
//             'asset_manager',
//             'hardware_repair_manager',
//           ],
//         },
//       },
//       {
//         name: 'InventoryStats',
//         path: '/inventory-stats',
//         component: () =>
//           import('#/views/assetManagement/inventoryStats/index.vue'),
//         meta: {
//           icon: 'mdi:chart-box',
//           title: $t('page.assetManagement.inventoryStats'),
//           authority: [
//             'super',
//             'admin',
//             'asset_manager',
//             'hardware_repair_manager',
//           ],
//         },
//       },
//     ],
//   },
// ];

// export default routes;
