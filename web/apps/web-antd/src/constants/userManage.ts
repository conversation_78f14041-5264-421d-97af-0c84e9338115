/**
 * 用户管理模块相关常量
 */
import { ref } from 'vue';

// 角色选项
export const roleOptions = ref([
  { label: '超级管理员', value: 'super' },
  { label: '管理员', value: 'admin' },
  { label: '普通用户', value: 'user' },
  { label: '硬件维修工程师', value: 'hardware_repair_engineer' },
  { label: '硬件维修主管', value: 'hardware_repair_manager' },
  { label: '资产管理员', value: 'asset_manager' },
  { label: '驻场运维负责人', value: 'site_manager' },
  { label: '仓库管理员', value: 'warehouse_manager' },
]);

// 部门选项
export const departmentOptions = ref([
  { label: '技术部', value: '技术部' },
  { label: '产品部', value: '产品部' },
  { label: '运营部', value: '运营部' },
  { label: '市场部', value: '市场部' },
  { label: '人力资源部', value: '人力资源部' },
  { label: '系统管理部', value: '系统管理部' },
]);

// 状态映射
export const statusMap: {
  [key: string]: { color: string; text: string };
} = {
  active: { text: '启用', color: 'success' },
  disabled: { text: '禁用', color: 'error' },
};

// 状态选项
export const statusOptions = ref([
  { label: '启用', value: 'active' },
  { label: '禁用', value: 'disabled' },
]);
