name: 'Setup Node'

description: 'Setup node and pnpm'

runs:
  using: 'composite'
  steps:
    - name: Install pnpm
      uses: pnpm/action-setup@v4

    - name: Install Node.js
      uses: actions/setup-node@v4
      with:
        node-version-file: .node-version
        cache: 'pnpm'

    - name: Get pnpm store directory
      shell: bash
      run: |
        echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

    - uses: actions/cache@v4
      name: Setup pnpm cache
      if: ${{ github.ref_name == 'main' }}
      with:
        path: ${{ env.STORE_PATH }}
        key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-store-

    - uses: actions/cache/restore@v4
      if: ${{ github.ref_name != 'main' }}
      with:
        path: ${{ env.STORE_PATH }}
        key: |
          ${{ runner.os }}-pnpm-store-

    - name: Install dependencies
      shell: bash
      run: pnpm install --frozen-lockfile
