name: Label Based Actions

on:
  issues:
    types: [labeled]
  # pull_request:
  #   types: [labeled]

permissions:
  issues: write
  pull-requests: write
  contents: write

jobs:
  reply-labeled:
    if: github.repository == 'vbenjs/vue-vben-admin'
    runs-on: ubuntu-latest
    steps:
      - name: remove enhancement pending
        if: github.event.label.name == 'enhancement'
        uses: actions-cool/issues-helper@v3
        with:
          actions: 'remove-labels'
          token: ${{ secrets.GITHUB_TOKEN }}
          issue-number: ${{ github.event.issue.number }}
          labels: 'enhancement: pending triage'

      - name: remove bug pending
        if: github.event.label.name == 'bug'
        uses: actions-cool/issues-helper@v3
        with:
          actions: 'remove-labels'
          token: ${{ secrets.GITHUB_TOKEN }}
          issue-number: ${{ github.event.issue.number }}
          labels: 'bug: pending triage'

      - name: needs reproduction
        if: github.event.label.name == 'needs reproduction'
        uses: actions-cool/issues-helper@v3
        with:
          actions: 'create-comment, remove-labels'
          token: ${{ secrets.GITHUB_TOKEN }}
          issue-number: ${{ github.event.issue.number }}
          body: |
            Hello @${{ github.event.issue.user.login }}. Please provide the complete reproduction steps and code. Issues labeled by `needs reproduction` will be closed if no activities in 3 days.
          labels: 'bug: pending triage'
