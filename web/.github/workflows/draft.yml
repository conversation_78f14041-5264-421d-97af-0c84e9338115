name: Release Drafter

on:
  push:
    branches:
      - main

permissions:
  contents: read
  pull-requests: write

jobs:
  update_release_draft:
    permissions:
      # write permission is required to create a github release
      contents: write
      # write permission is required for autolabeler
      # otherwise, read permission is required at least
      pull-requests: write
    if: github.repository == 'vbenjs/vue-vben-admin'
    runs-on: ubuntu-latest
    steps:
      - uses: release-drafter/release-drafter@v6
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
