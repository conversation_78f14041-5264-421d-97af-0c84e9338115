name: Lock Threads

on:
  schedule:
    - cron: '0 0 * * *'
  workflow_dispatch:

permissions:
  issues: write
  pull-requests: write

jobs:
  action:
    if: github.repository == 'vbenjs/vue-vben-admin'
    runs-on: ubuntu-latest
    steps:
      - uses: dessant/lock-threads@v5
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          issue-inactive-days: '14'
          issue-lock-reason: ''
          pr-inactive-days: '30'
          pr-lock-reason: ''
          process-only: 'issues, prs'
