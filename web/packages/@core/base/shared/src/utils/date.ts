import dayjs from 'dayjs';

export function formatDate(
  time: Date | dayjs.Dayjs | number | string | undefined,
  format = 'YYYY-MM-DD',
) {
  try {
    // 处理空值或无效值
    if (time === null || time === undefined || time === '') {
      return '';
    }

    const date = dayjs(time);
    if (!date.isValid()) {
      return '';
    }
    return date.format(format);
  } catch (error) {
    console.error(`Error formatting date: ${error}`);
    return '';
  }
}

export function formatDateTime(
  time: Date | dayjs.Dayjs | number | string | undefined,
) {
  return formatDate(time, 'YYYY-MM-DD HH:mm:ss');
}

export function isDate(value: any): value is Date {
  return value instanceof Date;
}

export function isDayjsObject(value: any): value is dayjs.Dayjs {
  return dayjs.isDayjs(value);
}
