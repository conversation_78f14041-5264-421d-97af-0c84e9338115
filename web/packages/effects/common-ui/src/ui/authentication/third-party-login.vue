<script setup lang="ts">
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Qq<PERSON><PERSON>, MdiWechat } from '@vben/icons';
import { $t } from '@vben/locales';

import { VbenIconButton } from '@vben-core/shadcn-ui';

defineOptions({
  name: 'ThirdPartyLogin',
});

interface Props {
  onFeishuLogin?: () => void;
}

const props = withDefaults(defineProps<Props>(), {
  onFeishuLogin: () => {},
});

// 处理飞书登录
function handleFeishuLogin() {
  props.onFeishuLogin?.();
}
</script>

<template>
  <div class="w-full sm:mx-auto md:max-w-md">
    <div class="mt-4 flex items-center justify-between">
      <span class="border-input w-[35%] border-b dark:border-gray-600"></span>
      <span class="text-muted-foreground text-center text-xs uppercase">
        {{ $t('authentication.thirdP<PERSON>yL<PERSON><PERSON>') }}
      </span>
      <span class="border-input w-[35%] border-b dark:border-gray-600"></span>
    </div>

    <div class="mt-4 flex flex-wrap justify-center">
      <!-- 飞书登录按钮 -->
      <VbenIconButton
        class="mb-3 mr-2"
        title="飞书登录"
        @click="handleFeishuLogin"
      >
        <MdiFeishu />
      </VbenIconButton>
      <VbenIconButton class="mb-3 mr-2">
        <MdiWechat />
      </VbenIconButton>
      <VbenIconButton class="mb-3 mr-2">
        <MdiQqchat />
      </VbenIconButton>
      <VbenIconButton class="mb-3 mr-2">
        <MdiGithub />
      </VbenIconButton>
      <VbenIconButton class="mb-3">
        <MdiGoogle />
      </VbenIconButton>
    </div>
  </div>
</template>
